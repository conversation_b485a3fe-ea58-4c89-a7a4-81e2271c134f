// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lyproto.quota.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_lyproto_2equota_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_lyproto_2equota_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021012 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_lyproto_2equota_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_lyproto_2equota_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_lyproto_2equota_2eproto;
namespace LYPROTO {
namespace QUOTA {
class FutureMarketData;
struct FutureMarketDataDefaultTypeInternal;
extern FutureMarketDataDefaultTypeInternal _FutureMarketData_default_instance_;
class MarketData;
struct MarketDataDefaultTypeInternal;
extern MarketDataDefaultTypeInternal _MarketData_default_instance_;
class Transaction;
struct TransactionDefaultTypeInternal;
extern TransactionDefaultTypeInternal _Transaction_default_instance_;
}  // namespace QUOTA
}  // namespace LYPROTO
PROTOBUF_NAMESPACE_OPEN
template<> ::LYPROTO::QUOTA::FutureMarketData* Arena::CreateMaybeMessage<::LYPROTO::QUOTA::FutureMarketData>(Arena*);
template<> ::LYPROTO::QUOTA::MarketData* Arena::CreateMaybeMessage<::LYPROTO::QUOTA::MarketData>(Arena*);
template<> ::LYPROTO::QUOTA::Transaction* Arena::CreateMaybeMessage<::LYPROTO::QUOTA::Transaction>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace LYPROTO {
namespace QUOTA {

// ===================================================================

class MarketData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.MarketData) */ {
 public:
  inline MarketData() : MarketData(nullptr) {}
  ~MarketData() override;
  explicit PROTOBUF_CONSTEXPR MarketData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarketData(const MarketData& from);
  MarketData(MarketData&& from) noexcept
    : MarketData() {
    *this = ::std::move(from);
  }

  inline MarketData& operator=(const MarketData& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarketData& operator=(MarketData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarketData& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarketData* internal_default_instance() {
    return reinterpret_cast<const MarketData*>(
               &_MarketData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MarketData& a, MarketData& b) {
    a.Swap(&b);
  }
  inline void Swap(MarketData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarketData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarketData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarketData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MarketData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MarketData& from) {
    MarketData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarketData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "LYPROTO.QUOTA.MarketData";
  }
  protected:
  explicit MarketData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBAFieldNumber = 18,
    kBPFieldNumber = 19,
    kSAFieldNumber = 20,
    kSPFieldNumber = 21,
    kExchIdFieldNumber = 1,
    kCategoryFieldNumber = 2,
    kStkIdFieldNumber = 3,
    kRcvSvrTimeFieldNumber = 4,
    kPubSvrTimeFieldNumber = 5,
    kStatusFieldNumber = 6,
    kExchTimeFieldNumber = 7,
    kMdSourceFieldNumber = 31,
    kPreCloseFieldNumber = 8,
    kHighLimitFieldNumber = 9,
    kLowLimitFieldNumber = 10,
    kOpenFieldNumber = 11,
    kHighFieldNumber = 12,
    kLowFieldNumber = 13,
    kLatestFieldNumber = 14,
    kVolumeFieldNumber = 16,
    kValueFieldNumber = 17,
    kKnockFieldNumber = 15,
    kIOPVFieldNumber = 26,
    kTotalBAFieldNumber = 22,
    kWeightedAvgBidPxFieldNumber = 23,
    kTotalSAFieldNumber = 24,
    kWeightedAvgOfferPxFieldNumber = 25,
    kTotalWarrantExecQtyFieldNumber = 28,
    kWarLowerPxFieldNumber = 29,
    kYieldToMaturityFieldNumber = 27,
    kWiDBuyNumFieldNumber = 32,
    kWarUpperPxFieldNumber = 30,
    kWiDSellNumFieldNumber = 33,
    kWiDNumFieldNumber = 34,
  };
  // repeated int64 BA = 18 [packed = true];
  int ba_size() const;
  private:
  int _internal_ba_size() const;
  public:
  void clear_ba();
  private:
  int64_t _internal_ba(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_ba() const;
  void _internal_add_ba(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_ba();
  public:
  int64_t ba(int index) const;
  void set_ba(int index, int64_t value);
  void add_ba(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      ba() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_ba();

  // repeated int64 BP = 19 [packed = true];
  int bp_size() const;
  private:
  int _internal_bp_size() const;
  public:
  void clear_bp();
  private:
  int64_t _internal_bp(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_bp() const;
  void _internal_add_bp(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_bp();
  public:
  int64_t bp(int index) const;
  void set_bp(int index, int64_t value);
  void add_bp(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      bp() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_bp();

  // repeated int64 SA = 20 [packed = true];
  int sa_size() const;
  private:
  int _internal_sa_size() const;
  public:
  void clear_sa();
  private:
  int64_t _internal_sa(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_sa() const;
  void _internal_add_sa(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_sa();
  public:
  int64_t sa(int index) const;
  void set_sa(int index, int64_t value);
  void add_sa(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      sa() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_sa();

  // repeated int64 SP = 21 [packed = true];
  int sp_size() const;
  private:
  int _internal_sp_size() const;
  public:
  void clear_sp();
  private:
  int64_t _internal_sp(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_sp() const;
  void _internal_add_sp(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_sp();
  public:
  int64_t sp(int index) const;
  void set_sp(int index, int64_t value);
  void add_sp(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      sp() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_sp();

  // required string ExchId = 1;
  bool has_exchid() const;
  private:
  bool _internal_has_exchid() const;
  public:
  void clear_exchid();
  const std::string& exchid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exchid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exchid();
  PROTOBUF_NODISCARD std::string* release_exchid();
  void set_allocated_exchid(std::string* exchid);
  private:
  const std::string& _internal_exchid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchid(const std::string& value);
  std::string* _internal_mutable_exchid();
  public:

  // required string Category = 2;
  bool has_category() const;
  private:
  bool _internal_has_category() const;
  public:
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // required string stkId = 3;
  bool has_stkid() const;
  private:
  bool _internal_has_stkid() const;
  public:
  void clear_stkid();
  const std::string& stkid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stkid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stkid();
  PROTOBUF_NODISCARD std::string* release_stkid();
  void set_allocated_stkid(std::string* stkid);
  private:
  const std::string& _internal_stkid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stkid(const std::string& value);
  std::string* _internal_mutable_stkid();
  public:

  // optional string RcvSvrTime = 4;
  bool has_rcvsvrtime() const;
  private:
  bool _internal_has_rcvsvrtime() const;
  public:
  void clear_rcvsvrtime();
  const std::string& rcvsvrtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rcvsvrtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rcvsvrtime();
  PROTOBUF_NODISCARD std::string* release_rcvsvrtime();
  void set_allocated_rcvsvrtime(std::string* rcvsvrtime);
  private:
  const std::string& _internal_rcvsvrtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rcvsvrtime(const std::string& value);
  std::string* _internal_mutable_rcvsvrtime();
  public:

  // optional string PubSvrTime = 5;
  bool has_pubsvrtime() const;
  private:
  bool _internal_has_pubsvrtime() const;
  public:
  void clear_pubsvrtime();
  const std::string& pubsvrtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pubsvrtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pubsvrtime();
  PROTOBUF_NODISCARD std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(std::string* pubsvrtime);
  private:
  const std::string& _internal_pubsvrtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pubsvrtime(const std::string& value);
  std::string* _internal_mutable_pubsvrtime();
  public:

  // optional string Status = 6;
  bool has_status() const;
  private:
  bool _internal_has_status() const;
  public:
  void clear_status();
  const std::string& status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status();
  PROTOBUF_NODISCARD std::string* release_status();
  void set_allocated_status(std::string* status);
  private:
  const std::string& _internal_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status(const std::string& value);
  std::string* _internal_mutable_status();
  public:

  // optional string ExchTime = 7;
  bool has_exchtime() const;
  private:
  bool _internal_has_exchtime() const;
  public:
  void clear_exchtime();
  const std::string& exchtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exchtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exchtime();
  PROTOBUF_NODISCARD std::string* release_exchtime();
  void set_allocated_exchtime(std::string* exchtime);
  private:
  const std::string& _internal_exchtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchtime(const std::string& value);
  std::string* _internal_mutable_exchtime();
  public:

  // optional string MdSource = 31;
  bool has_mdsource() const;
  private:
  bool _internal_has_mdsource() const;
  public:
  void clear_mdsource();
  const std::string& mdsource() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mdsource(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mdsource();
  PROTOBUF_NODISCARD std::string* release_mdsource();
  void set_allocated_mdsource(std::string* mdsource);
  private:
  const std::string& _internal_mdsource() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mdsource(const std::string& value);
  std::string* _internal_mutable_mdsource();
  public:

  // optional int64 PreClose = 8;
  bool has_preclose() const;
  private:
  bool _internal_has_preclose() const;
  public:
  void clear_preclose();
  int64_t preclose() const;
  void set_preclose(int64_t value);
  private:
  int64_t _internal_preclose() const;
  void _internal_set_preclose(int64_t value);
  public:

  // optional int64 HighLimit = 9;
  bool has_highlimit() const;
  private:
  bool _internal_has_highlimit() const;
  public:
  void clear_highlimit();
  int64_t highlimit() const;
  void set_highlimit(int64_t value);
  private:
  int64_t _internal_highlimit() const;
  void _internal_set_highlimit(int64_t value);
  public:

  // optional int64 LowLimit = 10;
  bool has_lowlimit() const;
  private:
  bool _internal_has_lowlimit() const;
  public:
  void clear_lowlimit();
  int64_t lowlimit() const;
  void set_lowlimit(int64_t value);
  private:
  int64_t _internal_lowlimit() const;
  void _internal_set_lowlimit(int64_t value);
  public:

  // optional int64 Open = 11;
  bool has_open() const;
  private:
  bool _internal_has_open() const;
  public:
  void clear_open();
  int64_t open() const;
  void set_open(int64_t value);
  private:
  int64_t _internal_open() const;
  void _internal_set_open(int64_t value);
  public:

  // optional int64 High = 12;
  bool has_high() const;
  private:
  bool _internal_has_high() const;
  public:
  void clear_high();
  int64_t high() const;
  void set_high(int64_t value);
  private:
  int64_t _internal_high() const;
  void _internal_set_high(int64_t value);
  public:

  // optional int64 Low = 13;
  bool has_low() const;
  private:
  bool _internal_has_low() const;
  public:
  void clear_low();
  int64_t low() const;
  void set_low(int64_t value);
  private:
  int64_t _internal_low() const;
  void _internal_set_low(int64_t value);
  public:

  // optional int64 Latest = 14;
  bool has_latest() const;
  private:
  bool _internal_has_latest() const;
  public:
  void clear_latest();
  int64_t latest() const;
  void set_latest(int64_t value);
  private:
  int64_t _internal_latest() const;
  void _internal_set_latest(int64_t value);
  public:

  // optional int64 Volume = 16;
  bool has_volume() const;
  private:
  bool _internal_has_volume() const;
  public:
  void clear_volume();
  int64_t volume() const;
  void set_volume(int64_t value);
  private:
  int64_t _internal_volume() const;
  void _internal_set_volume(int64_t value);
  public:

  // optional int64 Value = 17;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  int64_t value() const;
  void set_value(int64_t value);
  private:
  int64_t _internal_value() const;
  void _internal_set_value(int64_t value);
  public:

  // optional int32 Knock = 15;
  bool has_knock() const;
  private:
  bool _internal_has_knock() const;
  public:
  void clear_knock();
  int32_t knock() const;
  void set_knock(int32_t value);
  private:
  int32_t _internal_knock() const;
  void _internal_set_knock(int32_t value);
  public:

  // optional int32 IOPV = 26;
  bool has_iopv() const;
  private:
  bool _internal_has_iopv() const;
  public:
  void clear_iopv();
  int32_t iopv() const;
  void set_iopv(int32_t value);
  private:
  int32_t _internal_iopv() const;
  void _internal_set_iopv(int32_t value);
  public:

  // optional int64 TotalBA = 22;
  bool has_totalba() const;
  private:
  bool _internal_has_totalba() const;
  public:
  void clear_totalba();
  int64_t totalba() const;
  void set_totalba(int64_t value);
  private:
  int64_t _internal_totalba() const;
  void _internal_set_totalba(int64_t value);
  public:

  // optional int64 WeightedAvgBidPx = 23;
  bool has_weightedavgbidpx() const;
  private:
  bool _internal_has_weightedavgbidpx() const;
  public:
  void clear_weightedavgbidpx();
  int64_t weightedavgbidpx() const;
  void set_weightedavgbidpx(int64_t value);
  private:
  int64_t _internal_weightedavgbidpx() const;
  void _internal_set_weightedavgbidpx(int64_t value);
  public:

  // optional int64 TotalSA = 24;
  bool has_totalsa() const;
  private:
  bool _internal_has_totalsa() const;
  public:
  void clear_totalsa();
  int64_t totalsa() const;
  void set_totalsa(int64_t value);
  private:
  int64_t _internal_totalsa() const;
  void _internal_set_totalsa(int64_t value);
  public:

  // optional int64 WeightedAvgOfferPx = 25;
  bool has_weightedavgofferpx() const;
  private:
  bool _internal_has_weightedavgofferpx() const;
  public:
  void clear_weightedavgofferpx();
  int64_t weightedavgofferpx() const;
  void set_weightedavgofferpx(int64_t value);
  private:
  int64_t _internal_weightedavgofferpx() const;
  void _internal_set_weightedavgofferpx(int64_t value);
  public:

  // optional int64 TotalWarrantExecQty = 28;
  bool has_totalwarrantexecqty() const;
  private:
  bool _internal_has_totalwarrantexecqty() const;
  public:
  void clear_totalwarrantexecqty();
  int64_t totalwarrantexecqty() const;
  void set_totalwarrantexecqty(int64_t value);
  private:
  int64_t _internal_totalwarrantexecqty() const;
  void _internal_set_totalwarrantexecqty(int64_t value);
  public:

  // optional int64 WarLowerPx = 29;
  bool has_warlowerpx() const;
  private:
  bool _internal_has_warlowerpx() const;
  public:
  void clear_warlowerpx();
  int64_t warlowerpx() const;
  void set_warlowerpx(int64_t value);
  private:
  int64_t _internal_warlowerpx() const;
  void _internal_set_warlowerpx(int64_t value);
  public:

  // optional int32 YieldToMaturity = 27;
  bool has_yieldtomaturity() const;
  private:
  bool _internal_has_yieldtomaturity() const;
  public:
  void clear_yieldtomaturity();
  int32_t yieldtomaturity() const;
  void set_yieldtomaturity(int32_t value);
  private:
  int32_t _internal_yieldtomaturity() const;
  void _internal_set_yieldtomaturity(int32_t value);
  public:

  // optional int32 WiDBuyNum = 32;
  bool has_widbuynum() const;
  private:
  bool _internal_has_widbuynum() const;
  public:
  void clear_widbuynum();
  int32_t widbuynum() const;
  void set_widbuynum(int32_t value);
  private:
  int32_t _internal_widbuynum() const;
  void _internal_set_widbuynum(int32_t value);
  public:

  // optional int64 WarUpperPx = 30;
  bool has_warupperpx() const;
  private:
  bool _internal_has_warupperpx() const;
  public:
  void clear_warupperpx();
  int64_t warupperpx() const;
  void set_warupperpx(int64_t value);
  private:
  int64_t _internal_warupperpx() const;
  void _internal_set_warupperpx(int64_t value);
  public:

  // optional int32 WiDSellNum = 33;
  bool has_widsellnum() const;
  private:
  bool _internal_has_widsellnum() const;
  public:
  void clear_widsellnum();
  int32_t widsellnum() const;
  void set_widsellnum(int32_t value);
  private:
  int32_t _internal_widsellnum() const;
  void _internal_set_widsellnum(int32_t value);
  public:

  // optional int32 WiDNum = 34;
  bool has_widnum() const;
  private:
  bool _internal_has_widnum() const;
  public:
  void clear_widnum();
  int32_t widnum() const;
  void set_widnum(int32_t value);
  private:
  int32_t _internal_widnum() const;
  void _internal_set_widnum(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.MarketData)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > ba_;
    mutable std::atomic<int> _ba_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > bp_;
    mutable std::atomic<int> _bp_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > sa_;
    mutable std::atomic<int> _sa_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > sp_;
    mutable std::atomic<int> _sp_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exchid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stkid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rcvsvrtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pubsvrtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exchtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mdsource_;
    int64_t preclose_;
    int64_t highlimit_;
    int64_t lowlimit_;
    int64_t open_;
    int64_t high_;
    int64_t low_;
    int64_t latest_;
    int64_t volume_;
    int64_t value_;
    int32_t knock_;
    int32_t iopv_;
    int64_t totalba_;
    int64_t weightedavgbidpx_;
    int64_t totalsa_;
    int64_t weightedavgofferpx_;
    int64_t totalwarrantexecqty_;
    int64_t warlowerpx_;
    int32_t yieldtomaturity_;
    int32_t widbuynum_;
    int64_t warupperpx_;
    int32_t widsellnum_;
    int32_t widnum_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_lyproto_2equota_2eproto;
};
// -------------------------------------------------------------------

class Transaction final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.Transaction) */ {
 public:
  inline Transaction() : Transaction(nullptr) {}
  ~Transaction() override;
  explicit PROTOBUF_CONSTEXPR Transaction(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Transaction(const Transaction& from);
  Transaction(Transaction&& from) noexcept
    : Transaction() {
    *this = ::std::move(from);
  }

  inline Transaction& operator=(const Transaction& from) {
    CopyFrom(from);
    return *this;
  }
  inline Transaction& operator=(Transaction&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Transaction& default_instance() {
    return *internal_default_instance();
  }
  static inline const Transaction* internal_default_instance() {
    return reinterpret_cast<const Transaction*>(
               &_Transaction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Transaction& a, Transaction& b) {
    a.Swap(&b);
  }
  inline void Swap(Transaction* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Transaction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Transaction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Transaction>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Transaction& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Transaction& from) {
    Transaction::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transaction* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "LYPROTO.QUOTA.Transaction";
  }
  protected:
  explicit Transaction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExchangeIDFieldNumber = 1,
    kCategoryFieldNumber = 2,
    kSecurityIDFieldNumber = 3,
    kPubSvrTimeFieldNumber = 4,
    kTradeIndexFieldNumber = 5,
    kTradeTimeFieldNumber = 6,
    kSideFieldNumber = 10,
    kTradeTypeFieldNumber = 11,
    kTradeCodeFieldNumber = 12,
    kOfferIndexFieldNumber = 13,
    kBidIndexFieldNumber = 14,
    kReserveFieldNumber = 15,
    kTradePriceFieldNumber = 7,
    kTradeMoneyFieldNumber = 9,
    kTradeQtyFieldNumber = 8,
  };
  // required string ExchangeID = 1;
  bool has_exchangeid() const;
  private:
  bool _internal_has_exchangeid() const;
  public:
  void clear_exchangeid();
  const std::string& exchangeid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exchangeid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exchangeid();
  PROTOBUF_NODISCARD std::string* release_exchangeid();
  void set_allocated_exchangeid(std::string* exchangeid);
  private:
  const std::string& _internal_exchangeid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchangeid(const std::string& value);
  std::string* _internal_mutable_exchangeid();
  public:

  // required string Category = 2;
  bool has_category() const;
  private:
  bool _internal_has_category() const;
  public:
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // required string SecurityID = 3;
  bool has_securityid() const;
  private:
  bool _internal_has_securityid() const;
  public:
  void clear_securityid();
  const std::string& securityid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_securityid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_securityid();
  PROTOBUF_NODISCARD std::string* release_securityid();
  void set_allocated_securityid(std::string* securityid);
  private:
  const std::string& _internal_securityid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_securityid(const std::string& value);
  std::string* _internal_mutable_securityid();
  public:

  // optional string PubSvrTime = 4;
  bool has_pubsvrtime() const;
  private:
  bool _internal_has_pubsvrtime() const;
  public:
  void clear_pubsvrtime();
  const std::string& pubsvrtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pubsvrtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pubsvrtime();
  PROTOBUF_NODISCARD std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(std::string* pubsvrtime);
  private:
  const std::string& _internal_pubsvrtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pubsvrtime(const std::string& value);
  std::string* _internal_mutable_pubsvrtime();
  public:

  // optional string TradeIndex = 5;
  bool has_tradeindex() const;
  private:
  bool _internal_has_tradeindex() const;
  public:
  void clear_tradeindex();
  const std::string& tradeindex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tradeindex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tradeindex();
  PROTOBUF_NODISCARD std::string* release_tradeindex();
  void set_allocated_tradeindex(std::string* tradeindex);
  private:
  const std::string& _internal_tradeindex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tradeindex(const std::string& value);
  std::string* _internal_mutable_tradeindex();
  public:

  // optional string TradeTime = 6;
  bool has_tradetime() const;
  private:
  bool _internal_has_tradetime() const;
  public:
  void clear_tradetime();
  const std::string& tradetime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tradetime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tradetime();
  PROTOBUF_NODISCARD std::string* release_tradetime();
  void set_allocated_tradetime(std::string* tradetime);
  private:
  const std::string& _internal_tradetime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tradetime(const std::string& value);
  std::string* _internal_mutable_tradetime();
  public:

  // optional string Side = 10;
  bool has_side() const;
  private:
  bool _internal_has_side() const;
  public:
  void clear_side();
  const std::string& side() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_side(ArgT0&& arg0, ArgT... args);
  std::string* mutable_side();
  PROTOBUF_NODISCARD std::string* release_side();
  void set_allocated_side(std::string* side);
  private:
  const std::string& _internal_side() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_side(const std::string& value);
  std::string* _internal_mutable_side();
  public:

  // optional string TradeType = 11;
  bool has_tradetype() const;
  private:
  bool _internal_has_tradetype() const;
  public:
  void clear_tradetype();
  const std::string& tradetype() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tradetype(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tradetype();
  PROTOBUF_NODISCARD std::string* release_tradetype();
  void set_allocated_tradetype(std::string* tradetype);
  private:
  const std::string& _internal_tradetype() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tradetype(const std::string& value);
  std::string* _internal_mutable_tradetype();
  public:

  // optional string TradeCode = 12;
  bool has_tradecode() const;
  private:
  bool _internal_has_tradecode() const;
  public:
  void clear_tradecode();
  const std::string& tradecode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tradecode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tradecode();
  PROTOBUF_NODISCARD std::string* release_tradecode();
  void set_allocated_tradecode(std::string* tradecode);
  private:
  const std::string& _internal_tradecode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tradecode(const std::string& value);
  std::string* _internal_mutable_tradecode();
  public:

  // optional string OfferIndex = 13;
  bool has_offerindex() const;
  private:
  bool _internal_has_offerindex() const;
  public:
  void clear_offerindex();
  const std::string& offerindex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_offerindex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_offerindex();
  PROTOBUF_NODISCARD std::string* release_offerindex();
  void set_allocated_offerindex(std::string* offerindex);
  private:
  const std::string& _internal_offerindex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_offerindex(const std::string& value);
  std::string* _internal_mutable_offerindex();
  public:

  // optional string BidIndex = 14;
  bool has_bidindex() const;
  private:
  bool _internal_has_bidindex() const;
  public:
  void clear_bidindex();
  const std::string& bidindex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bidindex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bidindex();
  PROTOBUF_NODISCARD std::string* release_bidindex();
  void set_allocated_bidindex(std::string* bidindex);
  private:
  const std::string& _internal_bidindex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bidindex(const std::string& value);
  std::string* _internal_mutable_bidindex();
  public:

  // optional string reserve = 15;
  bool has_reserve() const;
  private:
  bool _internal_has_reserve() const;
  public:
  void clear_reserve();
  const std::string& reserve() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_reserve(ArgT0&& arg0, ArgT... args);
  std::string* mutable_reserve();
  PROTOBUF_NODISCARD std::string* release_reserve();
  void set_allocated_reserve(std::string* reserve);
  private:
  const std::string& _internal_reserve() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_reserve(const std::string& value);
  std::string* _internal_mutable_reserve();
  public:

  // optional int64 TradePrice = 7;
  bool has_tradeprice() const;
  private:
  bool _internal_has_tradeprice() const;
  public:
  void clear_tradeprice();
  int64_t tradeprice() const;
  void set_tradeprice(int64_t value);
  private:
  int64_t _internal_tradeprice() const;
  void _internal_set_tradeprice(int64_t value);
  public:

  // optional int64 TradeMoney = 9;
  bool has_trademoney() const;
  private:
  bool _internal_has_trademoney() const;
  public:
  void clear_trademoney();
  int64_t trademoney() const;
  void set_trademoney(int64_t value);
  private:
  int64_t _internal_trademoney() const;
  void _internal_set_trademoney(int64_t value);
  public:

  // optional int32 TradeQty = 8;
  bool has_tradeqty() const;
  private:
  bool _internal_has_tradeqty() const;
  public:
  void clear_tradeqty();
  int32_t tradeqty() const;
  void set_tradeqty(int32_t value);
  private:
  int32_t _internal_tradeqty() const;
  void _internal_set_tradeqty(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.Transaction)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exchangeid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr securityid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pubsvrtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tradeindex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tradetime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr side_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tradetype_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tradecode_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr offerindex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bidindex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr reserve_;
    int64_t tradeprice_;
    int64_t trademoney_;
    int32_t tradeqty_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_lyproto_2equota_2eproto;
};
// -------------------------------------------------------------------

class FutureMarketData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:LYPROTO.QUOTA.FutureMarketData) */ {
 public:
  inline FutureMarketData() : FutureMarketData(nullptr) {}
  ~FutureMarketData() override;
  explicit PROTOBUF_CONSTEXPR FutureMarketData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FutureMarketData(const FutureMarketData& from);
  FutureMarketData(FutureMarketData&& from) noexcept
    : FutureMarketData() {
    *this = ::std::move(from);
  }

  inline FutureMarketData& operator=(const FutureMarketData& from) {
    CopyFrom(from);
    return *this;
  }
  inline FutureMarketData& operator=(FutureMarketData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FutureMarketData& default_instance() {
    return *internal_default_instance();
  }
  static inline const FutureMarketData* internal_default_instance() {
    return reinterpret_cast<const FutureMarketData*>(
               &_FutureMarketData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FutureMarketData& a, FutureMarketData& b) {
    a.Swap(&b);
  }
  inline void Swap(FutureMarketData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FutureMarketData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FutureMarketData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FutureMarketData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FutureMarketData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FutureMarketData& from) {
    FutureMarketData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FutureMarketData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "LYPROTO.QUOTA.FutureMarketData";
  }
  protected:
  explicit FutureMarketData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBAFieldNumber = 23,
    kBPFieldNumber = 24,
    kSAFieldNumber = 25,
    kSPFieldNumber = 26,
    kExchIdFieldNumber = 1,
    kCategoryFieldNumber = 2,
    kStkIdFieldNumber = 3,
    kRcvSvrTimeFieldNumber = 4,
    kPubSvrTimeFieldNumber = 5,
    kStatusFieldNumber = 6,
    kExchTimeFieldNumber = 7,
    kTradeDateFieldNumber = 8,
    kPreCloseFieldNumber = 9,
    kPreSettleFieldNumber = 10,
    kHighLimitFieldNumber = 12,
    kLowLimitFieldNumber = 13,
    kOpenFieldNumber = 14,
    kLatestFieldNumber = 15,
    kPreOpenPosFieldNumber = 11,
    kLatestVolumeFieldNumber = 19,
    kHighFieldNumber = 16,
    kLowFieldNumber = 17,
    kSettleFieldNumber = 18,
    kVolumeFieldNumber = 20,
    kOpenPosFieldNumber = 21,
    kValueFieldNumber = 22,
    kPreDeltaFieldNumber = 27,
    kCurDeltaFieldNumber = 28,
  };
  // repeated int32 BA = 23 [packed = true];
  int ba_size() const;
  private:
  int _internal_ba_size() const;
  public:
  void clear_ba();
  private:
  int32_t _internal_ba(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_ba() const;
  void _internal_add_ba(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_ba();
  public:
  int32_t ba(int index) const;
  void set_ba(int index, int32_t value);
  void add_ba(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      ba() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_ba();

  // repeated int64 BP = 24 [packed = true];
  int bp_size() const;
  private:
  int _internal_bp_size() const;
  public:
  void clear_bp();
  private:
  int64_t _internal_bp(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_bp() const;
  void _internal_add_bp(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_bp();
  public:
  int64_t bp(int index) const;
  void set_bp(int index, int64_t value);
  void add_bp(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      bp() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_bp();

  // repeated int32 SA = 25 [packed = true];
  int sa_size() const;
  private:
  int _internal_sa_size() const;
  public:
  void clear_sa();
  private:
  int32_t _internal_sa(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_sa() const;
  void _internal_add_sa(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_sa();
  public:
  int32_t sa(int index) const;
  void set_sa(int index, int32_t value);
  void add_sa(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      sa() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_sa();

  // repeated int64 SP = 26 [packed = true];
  int sp_size() const;
  private:
  int _internal_sp_size() const;
  public:
  void clear_sp();
  private:
  int64_t _internal_sp(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_sp() const;
  void _internal_add_sp(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_sp();
  public:
  int64_t sp(int index) const;
  void set_sp(int index, int64_t value);
  void add_sp(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      sp() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_sp();

  // required string ExchId = 1;
  bool has_exchid() const;
  private:
  bool _internal_has_exchid() const;
  public:
  void clear_exchid();
  const std::string& exchid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exchid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exchid();
  PROTOBUF_NODISCARD std::string* release_exchid();
  void set_allocated_exchid(std::string* exchid);
  private:
  const std::string& _internal_exchid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchid(const std::string& value);
  std::string* _internal_mutable_exchid();
  public:

  // required string Category = 2;
  bool has_category() const;
  private:
  bool _internal_has_category() const;
  public:
  void clear_category();
  const std::string& category() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_category(ArgT0&& arg0, ArgT... args);
  std::string* mutable_category();
  PROTOBUF_NODISCARD std::string* release_category();
  void set_allocated_category(std::string* category);
  private:
  const std::string& _internal_category() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_category(const std::string& value);
  std::string* _internal_mutable_category();
  public:

  // required string stkId = 3;
  bool has_stkid() const;
  private:
  bool _internal_has_stkid() const;
  public:
  void clear_stkid();
  const std::string& stkid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stkid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stkid();
  PROTOBUF_NODISCARD std::string* release_stkid();
  void set_allocated_stkid(std::string* stkid);
  private:
  const std::string& _internal_stkid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stkid(const std::string& value);
  std::string* _internal_mutable_stkid();
  public:

  // optional string RcvSvrTime = 4;
  bool has_rcvsvrtime() const;
  private:
  bool _internal_has_rcvsvrtime() const;
  public:
  void clear_rcvsvrtime();
  const std::string& rcvsvrtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rcvsvrtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rcvsvrtime();
  PROTOBUF_NODISCARD std::string* release_rcvsvrtime();
  void set_allocated_rcvsvrtime(std::string* rcvsvrtime);
  private:
  const std::string& _internal_rcvsvrtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rcvsvrtime(const std::string& value);
  std::string* _internal_mutable_rcvsvrtime();
  public:

  // optional string PubSvrTime = 5;
  bool has_pubsvrtime() const;
  private:
  bool _internal_has_pubsvrtime() const;
  public:
  void clear_pubsvrtime();
  const std::string& pubsvrtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pubsvrtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pubsvrtime();
  PROTOBUF_NODISCARD std::string* release_pubsvrtime();
  void set_allocated_pubsvrtime(std::string* pubsvrtime);
  private:
  const std::string& _internal_pubsvrtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pubsvrtime(const std::string& value);
  std::string* _internal_mutable_pubsvrtime();
  public:

  // optional string Status = 6;
  bool has_status() const;
  private:
  bool _internal_has_status() const;
  public:
  void clear_status();
  const std::string& status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status();
  PROTOBUF_NODISCARD std::string* release_status();
  void set_allocated_status(std::string* status);
  private:
  const std::string& _internal_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status(const std::string& value);
  std::string* _internal_mutable_status();
  public:

  // optional string ExchTime = 7;
  bool has_exchtime() const;
  private:
  bool _internal_has_exchtime() const;
  public:
  void clear_exchtime();
  const std::string& exchtime() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exchtime(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exchtime();
  PROTOBUF_NODISCARD std::string* release_exchtime();
  void set_allocated_exchtime(std::string* exchtime);
  private:
  const std::string& _internal_exchtime() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exchtime(const std::string& value);
  std::string* _internal_mutable_exchtime();
  public:

  // optional string TradeDate = 8;
  bool has_tradedate() const;
  private:
  bool _internal_has_tradedate() const;
  public:
  void clear_tradedate();
  const std::string& tradedate() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tradedate(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tradedate();
  PROTOBUF_NODISCARD std::string* release_tradedate();
  void set_allocated_tradedate(std::string* tradedate);
  private:
  const std::string& _internal_tradedate() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tradedate(const std::string& value);
  std::string* _internal_mutable_tradedate();
  public:

  // optional int64 PreClose = 9;
  bool has_preclose() const;
  private:
  bool _internal_has_preclose() const;
  public:
  void clear_preclose();
  int64_t preclose() const;
  void set_preclose(int64_t value);
  private:
  int64_t _internal_preclose() const;
  void _internal_set_preclose(int64_t value);
  public:

  // optional int64 PreSettle = 10;
  bool has_presettle() const;
  private:
  bool _internal_has_presettle() const;
  public:
  void clear_presettle();
  int64_t presettle() const;
  void set_presettle(int64_t value);
  private:
  int64_t _internal_presettle() const;
  void _internal_set_presettle(int64_t value);
  public:

  // optional int64 HighLimit = 12;
  bool has_highlimit() const;
  private:
  bool _internal_has_highlimit() const;
  public:
  void clear_highlimit();
  int64_t highlimit() const;
  void set_highlimit(int64_t value);
  private:
  int64_t _internal_highlimit() const;
  void _internal_set_highlimit(int64_t value);
  public:

  // optional int64 LowLimit = 13;
  bool has_lowlimit() const;
  private:
  bool _internal_has_lowlimit() const;
  public:
  void clear_lowlimit();
  int64_t lowlimit() const;
  void set_lowlimit(int64_t value);
  private:
  int64_t _internal_lowlimit() const;
  void _internal_set_lowlimit(int64_t value);
  public:

  // optional int64 Open = 14;
  bool has_open() const;
  private:
  bool _internal_has_open() const;
  public:
  void clear_open();
  int64_t open() const;
  void set_open(int64_t value);
  private:
  int64_t _internal_open() const;
  void _internal_set_open(int64_t value);
  public:

  // optional int64 Latest = 15;
  bool has_latest() const;
  private:
  bool _internal_has_latest() const;
  public:
  void clear_latest();
  int64_t latest() const;
  void set_latest(int64_t value);
  private:
  int64_t _internal_latest() const;
  void _internal_set_latest(int64_t value);
  public:

  // optional int32 PreOpenPos = 11;
  bool has_preopenpos() const;
  private:
  bool _internal_has_preopenpos() const;
  public:
  void clear_preopenpos();
  int32_t preopenpos() const;
  void set_preopenpos(int32_t value);
  private:
  int32_t _internal_preopenpos() const;
  void _internal_set_preopenpos(int32_t value);
  public:

  // optional int32 LatestVolume = 19;
  bool has_latestvolume() const;
  private:
  bool _internal_has_latestvolume() const;
  public:
  void clear_latestvolume();
  int32_t latestvolume() const;
  void set_latestvolume(int32_t value);
  private:
  int32_t _internal_latestvolume() const;
  void _internal_set_latestvolume(int32_t value);
  public:

  // optional int64 High = 16;
  bool has_high() const;
  private:
  bool _internal_has_high() const;
  public:
  void clear_high();
  int64_t high() const;
  void set_high(int64_t value);
  private:
  int64_t _internal_high() const;
  void _internal_set_high(int64_t value);
  public:

  // optional int64 Low = 17;
  bool has_low() const;
  private:
  bool _internal_has_low() const;
  public:
  void clear_low();
  int64_t low() const;
  void set_low(int64_t value);
  private:
  int64_t _internal_low() const;
  void _internal_set_low(int64_t value);
  public:

  // optional int64 Settle = 18;
  bool has_settle() const;
  private:
  bool _internal_has_settle() const;
  public:
  void clear_settle();
  int64_t settle() const;
  void set_settle(int64_t value);
  private:
  int64_t _internal_settle() const;
  void _internal_set_settle(int64_t value);
  public:

  // optional int32 Volume = 20;
  bool has_volume() const;
  private:
  bool _internal_has_volume() const;
  public:
  void clear_volume();
  int32_t volume() const;
  void set_volume(int32_t value);
  private:
  int32_t _internal_volume() const;
  void _internal_set_volume(int32_t value);
  public:

  // optional int32 OpenPos = 21;
  bool has_openpos() const;
  private:
  bool _internal_has_openpos() const;
  public:
  void clear_openpos();
  int32_t openpos() const;
  void set_openpos(int32_t value);
  private:
  int32_t _internal_openpos() const;
  void _internal_set_openpos(int32_t value);
  public:

  // optional int64 Value = 22;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  int64_t value() const;
  void set_value(int64_t value);
  private:
  int64_t _internal_value() const;
  void _internal_set_value(int64_t value);
  public:

  // optional int32 PreDelta = 27;
  bool has_predelta() const;
  private:
  bool _internal_has_predelta() const;
  public:
  void clear_predelta();
  int32_t predelta() const;
  void set_predelta(int32_t value);
  private:
  int32_t _internal_predelta() const;
  void _internal_set_predelta(int32_t value);
  public:

  // optional int32 CurDelta = 28;
  bool has_curdelta() const;
  private:
  bool _internal_has_curdelta() const;
  public:
  void clear_curdelta();
  int32_t curdelta() const;
  void set_curdelta(int32_t value);
  private:
  int32_t _internal_curdelta() const;
  void _internal_set_curdelta(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:LYPROTO.QUOTA.FutureMarketData)
 private:
  class _Internal;

  // helper for ByteSizeLong()
  size_t RequiredFieldsByteSizeFallback() const;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > ba_;
    mutable std::atomic<int> _ba_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > bp_;
    mutable std::atomic<int> _bp_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > sa_;
    mutable std::atomic<int> _sa_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > sp_;
    mutable std::atomic<int> _sp_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exchid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr category_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stkid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rcvsvrtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pubsvrtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exchtime_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tradedate_;
    int64_t preclose_;
    int64_t presettle_;
    int64_t highlimit_;
    int64_t lowlimit_;
    int64_t open_;
    int64_t latest_;
    int32_t preopenpos_;
    int32_t latestvolume_;
    int64_t high_;
    int64_t low_;
    int64_t settle_;
    int32_t volume_;
    int32_t openpos_;
    int64_t value_;
    int32_t predelta_;
    int32_t curdelta_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_lyproto_2equota_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MarketData

// required string ExchId = 1;
inline bool MarketData::_internal_has_exchid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool MarketData::has_exchid() const {
  return _internal_has_exchid();
}
inline void MarketData::clear_exchid() {
  _impl_.exchid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& MarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchId)
  return _internal_exchid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_exchid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.exchid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchId)
}
inline std::string* MarketData::mutable_exchid() {
  std::string* _s = _internal_mutable_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchId)
  return _s;
}
inline const std::string& MarketData::_internal_exchid() const {
  return _impl_.exchid_.Get();
}
inline void MarketData::_internal_set_exchid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.exchid_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_exchid() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.exchid_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchId)
  if (!_internal_has_exchid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.exchid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchid_.IsDefault()) {
    _impl_.exchid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_exchid(std::string* exchid) {
  if (exchid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.exchid_.SetAllocated(exchid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchid_.IsDefault()) {
    _impl_.exchid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchId)
}

// required string Category = 2;
inline bool MarketData::_internal_has_category() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool MarketData::has_category() const {
  return _internal_has_category();
}
inline void MarketData::clear_category() {
  _impl_.category_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& MarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_category(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.category_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Category)
}
inline std::string* MarketData::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Category)
  return _s;
}
inline const std::string& MarketData::_internal_category() const {
  return _impl_.category_.Get();
}
inline void MarketData::_internal_set_category(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.category_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_category() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.category_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Category)
  if (!_internal_has_category()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.category_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.category_.SetAllocated(category, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Category)
}

// required string stkId = 3;
inline bool MarketData::_internal_has_stkid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool MarketData::has_stkid() const {
  return _internal_has_stkid();
}
inline void MarketData::clear_stkid() {
  _impl_.stkid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& MarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.stkId)
  return _internal_stkid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_stkid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.stkid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.stkId)
}
inline std::string* MarketData::mutable_stkid() {
  std::string* _s = _internal_mutable_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.stkId)
  return _s;
}
inline const std::string& MarketData::_internal_stkid() const {
  return _impl_.stkid_.Get();
}
inline void MarketData::_internal_set_stkid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.stkid_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_stkid() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.stkid_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.stkId)
  if (!_internal_has_stkid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.stkid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.stkid_.IsDefault()) {
    _impl_.stkid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_stkid(std::string* stkid) {
  if (stkid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.stkid_.SetAllocated(stkid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.stkid_.IsDefault()) {
    _impl_.stkid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.stkId)
}

// optional string RcvSvrTime = 4;
inline bool MarketData::_internal_has_rcvsvrtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool MarketData::has_rcvsvrtime() const {
  return _internal_has_rcvsvrtime();
}
inline void MarketData::clear_rcvsvrtime() {
  _impl_.rcvsvrtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const std::string& MarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return _internal_rcvsvrtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_rcvsvrtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000008u;
 _impl_.rcvsvrtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}
inline std::string* MarketData::mutable_rcvsvrtime() {
  std::string* _s = _internal_mutable_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  return _s;
}
inline const std::string& MarketData::_internal_rcvsvrtime() const {
  return _impl_.rcvsvrtime_.Get();
}
inline void MarketData::_internal_set_rcvsvrtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.rcvsvrtime_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_rcvsvrtime() {
  _impl_._has_bits_[0] |= 0x00000008u;
  return _impl_.rcvsvrtime_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.RcvSvrTime)
  if (!_internal_has_rcvsvrtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000008u;
  auto* p = _impl_.rcvsvrtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rcvsvrtime_.IsDefault()) {
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_rcvsvrtime(std::string* rcvsvrtime) {
  if (rcvsvrtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.rcvsvrtime_.SetAllocated(rcvsvrtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rcvsvrtime_.IsDefault()) {
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
inline bool MarketData::_internal_has_pubsvrtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool MarketData::has_pubsvrtime() const {
  return _internal_has_pubsvrtime();
}
inline void MarketData::clear_pubsvrtime() {
  _impl_.pubsvrtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline const std::string& MarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return _internal_pubsvrtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_pubsvrtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000010u;
 _impl_.pubsvrtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PubSvrTime)
}
inline std::string* MarketData::mutable_pubsvrtime() {
  std::string* _s = _internal_mutable_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.PubSvrTime)
  return _s;
}
inline const std::string& MarketData::_internal_pubsvrtime() const {
  return _impl_.pubsvrtime_.Get();
}
inline void MarketData::_internal_set_pubsvrtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.pubsvrtime_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_pubsvrtime() {
  _impl_._has_bits_[0] |= 0x00000010u;
  return _impl_.pubsvrtime_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.PubSvrTime)
  if (!_internal_has_pubsvrtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000010u;
  auto* p = _impl_.pubsvrtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_pubsvrtime(std::string* pubsvrtime) {
  if (pubsvrtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  _impl_.pubsvrtime_.SetAllocated(pubsvrtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.PubSvrTime)
}

// optional string Status = 6;
inline bool MarketData::_internal_has_status() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool MarketData::has_status() const {
  return _internal_has_status();
}
inline void MarketData::clear_status() {
  _impl_.status_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline const std::string& MarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Status)
  return _internal_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_status(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000020u;
 _impl_.status_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Status)
}
inline std::string* MarketData::mutable_status() {
  std::string* _s = _internal_mutable_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.Status)
  return _s;
}
inline const std::string& MarketData::_internal_status() const {
  return _impl_.status_.Get();
}
inline void MarketData::_internal_set_status(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.status_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_status() {
  _impl_._has_bits_[0] |= 0x00000020u;
  return _impl_.status_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.Status)
  if (!_internal_has_status()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000020u;
  auto* p = _impl_.status_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_.IsDefault()) {
    _impl_.status_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_status(std::string* status) {
  if (status != nullptr) {
    _impl_._has_bits_[0] |= 0x00000020u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000020u;
  }
  _impl_.status_.SetAllocated(status, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_.IsDefault()) {
    _impl_.status_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.Status)
}

// optional string ExchTime = 7;
inline bool MarketData::_internal_has_exchtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool MarketData::has_exchtime() const {
  return _internal_has_exchtime();
}
inline void MarketData::clear_exchtime() {
  _impl_.exchtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline const std::string& MarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.ExchTime)
  return _internal_exchtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_exchtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000040u;
 _impl_.exchtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.ExchTime)
}
inline std::string* MarketData::mutable_exchtime() {
  std::string* _s = _internal_mutable_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.ExchTime)
  return _s;
}
inline const std::string& MarketData::_internal_exchtime() const {
  return _impl_.exchtime_.Get();
}
inline void MarketData::_internal_set_exchtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.exchtime_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_exchtime() {
  _impl_._has_bits_[0] |= 0x00000040u;
  return _impl_.exchtime_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.ExchTime)
  if (!_internal_has_exchtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000040u;
  auto* p = _impl_.exchtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchtime_.IsDefault()) {
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_exchtime(std::string* exchtime) {
  if (exchtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000040u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000040u;
  }
  _impl_.exchtime_.SetAllocated(exchtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchtime_.IsDefault()) {
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.ExchTime)
}

// optional int64 PreClose = 8;
inline bool MarketData::_internal_has_preclose() const {
  bool value = (_impl_._has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool MarketData::has_preclose() const {
  return _internal_has_preclose();
}
inline void MarketData::clear_preclose() {
  _impl_.preclose_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000100u;
}
inline int64_t MarketData::_internal_preclose() const {
  return _impl_.preclose_;
}
inline int64_t MarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.PreClose)
  return _internal_preclose();
}
inline void MarketData::_internal_set_preclose(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000100u;
  _impl_.preclose_ = value;
}
inline void MarketData::set_preclose(int64_t value) {
  _internal_set_preclose(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.PreClose)
}

// optional int64 HighLimit = 9;
inline bool MarketData::_internal_has_highlimit() const {
  bool value = (_impl_._has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool MarketData::has_highlimit() const {
  return _internal_has_highlimit();
}
inline void MarketData::clear_highlimit() {
  _impl_.highlimit_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000200u;
}
inline int64_t MarketData::_internal_highlimit() const {
  return _impl_.highlimit_;
}
inline int64_t MarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.HighLimit)
  return _internal_highlimit();
}
inline void MarketData::_internal_set_highlimit(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000200u;
  _impl_.highlimit_ = value;
}
inline void MarketData::set_highlimit(int64_t value) {
  _internal_set_highlimit(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.HighLimit)
}

// optional int64 LowLimit = 10;
inline bool MarketData::_internal_has_lowlimit() const {
  bool value = (_impl_._has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool MarketData::has_lowlimit() const {
  return _internal_has_lowlimit();
}
inline void MarketData::clear_lowlimit() {
  _impl_.lowlimit_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000400u;
}
inline int64_t MarketData::_internal_lowlimit() const {
  return _impl_.lowlimit_;
}
inline int64_t MarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.LowLimit)
  return _internal_lowlimit();
}
inline void MarketData::_internal_set_lowlimit(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000400u;
  _impl_.lowlimit_ = value;
}
inline void MarketData::set_lowlimit(int64_t value) {
  _internal_set_lowlimit(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.LowLimit)
}

// optional int64 Open = 11;
inline bool MarketData::_internal_has_open() const {
  bool value = (_impl_._has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool MarketData::has_open() const {
  return _internal_has_open();
}
inline void MarketData::clear_open() {
  _impl_.open_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000800u;
}
inline int64_t MarketData::_internal_open() const {
  return _impl_.open_;
}
inline int64_t MarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Open)
  return _internal_open();
}
inline void MarketData::_internal_set_open(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000800u;
  _impl_.open_ = value;
}
inline void MarketData::set_open(int64_t value) {
  _internal_set_open(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Open)
}

// optional int64 High = 12;
inline bool MarketData::_internal_has_high() const {
  bool value = (_impl_._has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool MarketData::has_high() const {
  return _internal_has_high();
}
inline void MarketData::clear_high() {
  _impl_.high_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00001000u;
}
inline int64_t MarketData::_internal_high() const {
  return _impl_.high_;
}
inline int64_t MarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.High)
  return _internal_high();
}
inline void MarketData::_internal_set_high(int64_t value) {
  _impl_._has_bits_[0] |= 0x00001000u;
  _impl_.high_ = value;
}
inline void MarketData::set_high(int64_t value) {
  _internal_set_high(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.High)
}

// optional int64 Low = 13;
inline bool MarketData::_internal_has_low() const {
  bool value = (_impl_._has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool MarketData::has_low() const {
  return _internal_has_low();
}
inline void MarketData::clear_low() {
  _impl_.low_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00002000u;
}
inline int64_t MarketData::_internal_low() const {
  return _impl_.low_;
}
inline int64_t MarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Low)
  return _internal_low();
}
inline void MarketData::_internal_set_low(int64_t value) {
  _impl_._has_bits_[0] |= 0x00002000u;
  _impl_.low_ = value;
}
inline void MarketData::set_low(int64_t value) {
  _internal_set_low(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Low)
}

// optional int64 Latest = 14;
inline bool MarketData::_internal_has_latest() const {
  bool value = (_impl_._has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool MarketData::has_latest() const {
  return _internal_has_latest();
}
inline void MarketData::clear_latest() {
  _impl_.latest_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00004000u;
}
inline int64_t MarketData::_internal_latest() const {
  return _impl_.latest_;
}
inline int64_t MarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Latest)
  return _internal_latest();
}
inline void MarketData::_internal_set_latest(int64_t value) {
  _impl_._has_bits_[0] |= 0x00004000u;
  _impl_.latest_ = value;
}
inline void MarketData::set_latest(int64_t value) {
  _internal_set_latest(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Latest)
}

// optional int32 Knock = 15;
inline bool MarketData::_internal_has_knock() const {
  bool value = (_impl_._has_bits_[0] & 0x00020000u) != 0;
  return value;
}
inline bool MarketData::has_knock() const {
  return _internal_has_knock();
}
inline void MarketData::clear_knock() {
  _impl_.knock_ = 0;
  _impl_._has_bits_[0] &= ~0x00020000u;
}
inline int32_t MarketData::_internal_knock() const {
  return _impl_.knock_;
}
inline int32_t MarketData::knock() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Knock)
  return _internal_knock();
}
inline void MarketData::_internal_set_knock(int32_t value) {
  _impl_._has_bits_[0] |= 0x00020000u;
  _impl_.knock_ = value;
}
inline void MarketData::set_knock(int32_t value) {
  _internal_set_knock(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Knock)
}

// optional int64 Volume = 16;
inline bool MarketData::_internal_has_volume() const {
  bool value = (_impl_._has_bits_[0] & 0x00008000u) != 0;
  return value;
}
inline bool MarketData::has_volume() const {
  return _internal_has_volume();
}
inline void MarketData::clear_volume() {
  _impl_.volume_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00008000u;
}
inline int64_t MarketData::_internal_volume() const {
  return _impl_.volume_;
}
inline int64_t MarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Volume)
  return _internal_volume();
}
inline void MarketData::_internal_set_volume(int64_t value) {
  _impl_._has_bits_[0] |= 0x00008000u;
  _impl_.volume_ = value;
}
inline void MarketData::set_volume(int64_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Volume)
}

// optional int64 Value = 17;
inline bool MarketData::_internal_has_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00010000u) != 0;
  return value;
}
inline bool MarketData::has_value() const {
  return _internal_has_value();
}
inline void MarketData::clear_value() {
  _impl_.value_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00010000u;
}
inline int64_t MarketData::_internal_value() const {
  return _impl_.value_;
}
inline int64_t MarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.Value)
  return _internal_value();
}
inline void MarketData::_internal_set_value(int64_t value) {
  _impl_._has_bits_[0] |= 0x00010000u;
  _impl_.value_ = value;
}
inline void MarketData::set_value(int64_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.Value)
}

// repeated int64 BA = 18 [packed = true];
inline int MarketData::_internal_ba_size() const {
  return _impl_.ba_.size();
}
inline int MarketData::ba_size() const {
  return _internal_ba_size();
}
inline void MarketData::clear_ba() {
  _impl_.ba_.Clear();
}
inline int64_t MarketData::_internal_ba(int index) const {
  return _impl_.ba_.Get(index);
}
inline int64_t MarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BA)
  return _internal_ba(index);
}
inline void MarketData::set_ba(int index, int64_t value) {
  _impl_.ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BA)
}
inline void MarketData::_internal_add_ba(int64_t value) {
  _impl_.ba_.Add(value);
}
inline void MarketData::add_ba(int64_t value) {
  _internal_add_ba(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BA)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::_internal_ba() const {
  return _impl_.ba_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BA)
  return _internal_ba();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::_internal_mutable_ba() {
  return &_impl_.ba_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BA)
  return _internal_mutable_ba();
}

// repeated int64 BP = 19 [packed = true];
inline int MarketData::_internal_bp_size() const {
  return _impl_.bp_.size();
}
inline int MarketData::bp_size() const {
  return _internal_bp_size();
}
inline void MarketData::clear_bp() {
  _impl_.bp_.Clear();
}
inline int64_t MarketData::_internal_bp(int index) const {
  return _impl_.bp_.Get(index);
}
inline int64_t MarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.BP)
  return _internal_bp(index);
}
inline void MarketData::set_bp(int index, int64_t value) {
  _impl_.bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.BP)
}
inline void MarketData::_internal_add_bp(int64_t value) {
  _impl_.bp_.Add(value);
}
inline void MarketData::add_bp(int64_t value) {
  _internal_add_bp(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.BP)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::_internal_bp() const {
  return _impl_.bp_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.BP)
  return _internal_bp();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::_internal_mutable_bp() {
  return &_impl_.bp_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.BP)
  return _internal_mutable_bp();
}

// repeated int64 SA = 20 [packed = true];
inline int MarketData::_internal_sa_size() const {
  return _impl_.sa_.size();
}
inline int MarketData::sa_size() const {
  return _internal_sa_size();
}
inline void MarketData::clear_sa() {
  _impl_.sa_.Clear();
}
inline int64_t MarketData::_internal_sa(int index) const {
  return _impl_.sa_.Get(index);
}
inline int64_t MarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SA)
  return _internal_sa(index);
}
inline void MarketData::set_sa(int index, int64_t value) {
  _impl_.sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SA)
}
inline void MarketData::_internal_add_sa(int64_t value) {
  _impl_.sa_.Add(value);
}
inline void MarketData::add_sa(int64_t value) {
  _internal_add_sa(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SA)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::_internal_sa() const {
  return _impl_.sa_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SA)
  return _internal_sa();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::_internal_mutable_sa() {
  return &_impl_.sa_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SA)
  return _internal_mutable_sa();
}

// repeated int64 SP = 21 [packed = true];
inline int MarketData::_internal_sp_size() const {
  return _impl_.sp_.size();
}
inline int MarketData::sp_size() const {
  return _internal_sp_size();
}
inline void MarketData::clear_sp() {
  _impl_.sp_.Clear();
}
inline int64_t MarketData::_internal_sp(int index) const {
  return _impl_.sp_.Get(index);
}
inline int64_t MarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.SP)
  return _internal_sp(index);
}
inline void MarketData::set_sp(int index, int64_t value) {
  _impl_.sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.SP)
}
inline void MarketData::_internal_add_sp(int64_t value) {
  _impl_.sp_.Add(value);
}
inline void MarketData::add_sp(int64_t value) {
  _internal_add_sp(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.MarketData.SP)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::_internal_sp() const {
  return _impl_.sp_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.MarketData.SP)
  return _internal_sp();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::_internal_mutable_sp() {
  return &_impl_.sp_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.MarketData.SP)
  return _internal_mutable_sp();
}

// optional int64 TotalBA = 22;
inline bool MarketData::_internal_has_totalba() const {
  bool value = (_impl_._has_bits_[0] & 0x00080000u) != 0;
  return value;
}
inline bool MarketData::has_totalba() const {
  return _internal_has_totalba();
}
inline void MarketData::clear_totalba() {
  _impl_.totalba_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00080000u;
}
inline int64_t MarketData::_internal_totalba() const {
  return _impl_.totalba_;
}
inline int64_t MarketData::totalba() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalBA)
  return _internal_totalba();
}
inline void MarketData::_internal_set_totalba(int64_t value) {
  _impl_._has_bits_[0] |= 0x00080000u;
  _impl_.totalba_ = value;
}
inline void MarketData::set_totalba(int64_t value) {
  _internal_set_totalba(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalBA)
}

// optional int64 WeightedAvgBidPx = 23;
inline bool MarketData::_internal_has_weightedavgbidpx() const {
  bool value = (_impl_._has_bits_[0] & 0x00100000u) != 0;
  return value;
}
inline bool MarketData::has_weightedavgbidpx() const {
  return _internal_has_weightedavgbidpx();
}
inline void MarketData::clear_weightedavgbidpx() {
  _impl_.weightedavgbidpx_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00100000u;
}
inline int64_t MarketData::_internal_weightedavgbidpx() const {
  return _impl_.weightedavgbidpx_;
}
inline int64_t MarketData::weightedavgbidpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
  return _internal_weightedavgbidpx();
}
inline void MarketData::_internal_set_weightedavgbidpx(int64_t value) {
  _impl_._has_bits_[0] |= 0x00100000u;
  _impl_.weightedavgbidpx_ = value;
}
inline void MarketData::set_weightedavgbidpx(int64_t value) {
  _internal_set_weightedavgbidpx(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgBidPx)
}

// optional int64 TotalSA = 24;
inline bool MarketData::_internal_has_totalsa() const {
  bool value = (_impl_._has_bits_[0] & 0x00200000u) != 0;
  return value;
}
inline bool MarketData::has_totalsa() const {
  return _internal_has_totalsa();
}
inline void MarketData::clear_totalsa() {
  _impl_.totalsa_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00200000u;
}
inline int64_t MarketData::_internal_totalsa() const {
  return _impl_.totalsa_;
}
inline int64_t MarketData::totalsa() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalSA)
  return _internal_totalsa();
}
inline void MarketData::_internal_set_totalsa(int64_t value) {
  _impl_._has_bits_[0] |= 0x00200000u;
  _impl_.totalsa_ = value;
}
inline void MarketData::set_totalsa(int64_t value) {
  _internal_set_totalsa(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalSA)
}

// optional int64 WeightedAvgOfferPx = 25;
inline bool MarketData::_internal_has_weightedavgofferpx() const {
  bool value = (_impl_._has_bits_[0] & 0x00400000u) != 0;
  return value;
}
inline bool MarketData::has_weightedavgofferpx() const {
  return _internal_has_weightedavgofferpx();
}
inline void MarketData::clear_weightedavgofferpx() {
  _impl_.weightedavgofferpx_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00400000u;
}
inline int64_t MarketData::_internal_weightedavgofferpx() const {
  return _impl_.weightedavgofferpx_;
}
inline int64_t MarketData::weightedavgofferpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
  return _internal_weightedavgofferpx();
}
inline void MarketData::_internal_set_weightedavgofferpx(int64_t value) {
  _impl_._has_bits_[0] |= 0x00400000u;
  _impl_.weightedavgofferpx_ = value;
}
inline void MarketData::set_weightedavgofferpx(int64_t value) {
  _internal_set_weightedavgofferpx(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WeightedAvgOfferPx)
}

// optional int32 IOPV = 26;
inline bool MarketData::_internal_has_iopv() const {
  bool value = (_impl_._has_bits_[0] & 0x00040000u) != 0;
  return value;
}
inline bool MarketData::has_iopv() const {
  return _internal_has_iopv();
}
inline void MarketData::clear_iopv() {
  _impl_.iopv_ = 0;
  _impl_._has_bits_[0] &= ~0x00040000u;
}
inline int32_t MarketData::_internal_iopv() const {
  return _impl_.iopv_;
}
inline int32_t MarketData::iopv() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.IOPV)
  return _internal_iopv();
}
inline void MarketData::_internal_set_iopv(int32_t value) {
  _impl_._has_bits_[0] |= 0x00040000u;
  _impl_.iopv_ = value;
}
inline void MarketData::set_iopv(int32_t value) {
  _internal_set_iopv(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.IOPV)
}

// optional int32 YieldToMaturity = 27;
inline bool MarketData::_internal_has_yieldtomaturity() const {
  bool value = (_impl_._has_bits_[0] & 0x02000000u) != 0;
  return value;
}
inline bool MarketData::has_yieldtomaturity() const {
  return _internal_has_yieldtomaturity();
}
inline void MarketData::clear_yieldtomaturity() {
  _impl_.yieldtomaturity_ = 0;
  _impl_._has_bits_[0] &= ~0x02000000u;
}
inline int32_t MarketData::_internal_yieldtomaturity() const {
  return _impl_.yieldtomaturity_;
}
inline int32_t MarketData::yieldtomaturity() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.YieldToMaturity)
  return _internal_yieldtomaturity();
}
inline void MarketData::_internal_set_yieldtomaturity(int32_t value) {
  _impl_._has_bits_[0] |= 0x02000000u;
  _impl_.yieldtomaturity_ = value;
}
inline void MarketData::set_yieldtomaturity(int32_t value) {
  _internal_set_yieldtomaturity(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.YieldToMaturity)
}

// optional int64 TotalWarrantExecQty = 28;
inline bool MarketData::_internal_has_totalwarrantexecqty() const {
  bool value = (_impl_._has_bits_[0] & 0x00800000u) != 0;
  return value;
}
inline bool MarketData::has_totalwarrantexecqty() const {
  return _internal_has_totalwarrantexecqty();
}
inline void MarketData::clear_totalwarrantexecqty() {
  _impl_.totalwarrantexecqty_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00800000u;
}
inline int64_t MarketData::_internal_totalwarrantexecqty() const {
  return _impl_.totalwarrantexecqty_;
}
inline int64_t MarketData::totalwarrantexecqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
  return _internal_totalwarrantexecqty();
}
inline void MarketData::_internal_set_totalwarrantexecqty(int64_t value) {
  _impl_._has_bits_[0] |= 0x00800000u;
  _impl_.totalwarrantexecqty_ = value;
}
inline void MarketData::set_totalwarrantexecqty(int64_t value) {
  _internal_set_totalwarrantexecqty(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.TotalWarrantExecQty)
}

// optional int64 WarLowerPx = 29;
inline bool MarketData::_internal_has_warlowerpx() const {
  bool value = (_impl_._has_bits_[0] & 0x01000000u) != 0;
  return value;
}
inline bool MarketData::has_warlowerpx() const {
  return _internal_has_warlowerpx();
}
inline void MarketData::clear_warlowerpx() {
  _impl_.warlowerpx_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x01000000u;
}
inline int64_t MarketData::_internal_warlowerpx() const {
  return _impl_.warlowerpx_;
}
inline int64_t MarketData::warlowerpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarLowerPx)
  return _internal_warlowerpx();
}
inline void MarketData::_internal_set_warlowerpx(int64_t value) {
  _impl_._has_bits_[0] |= 0x01000000u;
  _impl_.warlowerpx_ = value;
}
inline void MarketData::set_warlowerpx(int64_t value) {
  _internal_set_warlowerpx(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarLowerPx)
}

// optional int64 WarUpperPx = 30;
inline bool MarketData::_internal_has_warupperpx() const {
  bool value = (_impl_._has_bits_[0] & 0x08000000u) != 0;
  return value;
}
inline bool MarketData::has_warupperpx() const {
  return _internal_has_warupperpx();
}
inline void MarketData::clear_warupperpx() {
  _impl_.warupperpx_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x08000000u;
}
inline int64_t MarketData::_internal_warupperpx() const {
  return _impl_.warupperpx_;
}
inline int64_t MarketData::warupperpx() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WarUpperPx)
  return _internal_warupperpx();
}
inline void MarketData::_internal_set_warupperpx(int64_t value) {
  _impl_._has_bits_[0] |= 0x08000000u;
  _impl_.warupperpx_ = value;
}
inline void MarketData::set_warupperpx(int64_t value) {
  _internal_set_warupperpx(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WarUpperPx)
}

// optional string MdSource = 31;
inline bool MarketData::_internal_has_mdsource() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool MarketData::has_mdsource() const {
  return _internal_has_mdsource();
}
inline void MarketData::clear_mdsource() {
  _impl_.mdsource_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline const std::string& MarketData::mdsource() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.MdSource)
  return _internal_mdsource();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarketData::set_mdsource(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000080u;
 _impl_.mdsource_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.MdSource)
}
inline std::string* MarketData::mutable_mdsource() {
  std::string* _s = _internal_mutable_mdsource();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.MarketData.MdSource)
  return _s;
}
inline const std::string& MarketData::_internal_mdsource() const {
  return _impl_.mdsource_.Get();
}
inline void MarketData::_internal_set_mdsource(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.mdsource_.Set(value, GetArenaForAllocation());
}
inline std::string* MarketData::_internal_mutable_mdsource() {
  _impl_._has_bits_[0] |= 0x00000080u;
  return _impl_.mdsource_.Mutable(GetArenaForAllocation());
}
inline std::string* MarketData::release_mdsource() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.MarketData.MdSource)
  if (!_internal_has_mdsource()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000080u;
  auto* p = _impl_.mdsource_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.mdsource_.IsDefault()) {
    _impl_.mdsource_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void MarketData::set_allocated_mdsource(std::string* mdsource) {
  if (mdsource != nullptr) {
    _impl_._has_bits_[0] |= 0x00000080u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000080u;
  }
  _impl_.mdsource_.SetAllocated(mdsource, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.mdsource_.IsDefault()) {
    _impl_.mdsource_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.MarketData.MdSource)
}

// optional int32 WiDBuyNum = 32;
inline bool MarketData::_internal_has_widbuynum() const {
  bool value = (_impl_._has_bits_[0] & 0x04000000u) != 0;
  return value;
}
inline bool MarketData::has_widbuynum() const {
  return _internal_has_widbuynum();
}
inline void MarketData::clear_widbuynum() {
  _impl_.widbuynum_ = 0;
  _impl_._has_bits_[0] &= ~0x04000000u;
}
inline int32_t MarketData::_internal_widbuynum() const {
  return _impl_.widbuynum_;
}
inline int32_t MarketData::widbuynum() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WiDBuyNum)
  return _internal_widbuynum();
}
inline void MarketData::_internal_set_widbuynum(int32_t value) {
  _impl_._has_bits_[0] |= 0x04000000u;
  _impl_.widbuynum_ = value;
}
inline void MarketData::set_widbuynum(int32_t value) {
  _internal_set_widbuynum(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WiDBuyNum)
}

// optional int32 WiDSellNum = 33;
inline bool MarketData::_internal_has_widsellnum() const {
  bool value = (_impl_._has_bits_[0] & 0x10000000u) != 0;
  return value;
}
inline bool MarketData::has_widsellnum() const {
  return _internal_has_widsellnum();
}
inline void MarketData::clear_widsellnum() {
  _impl_.widsellnum_ = 0;
  _impl_._has_bits_[0] &= ~0x10000000u;
}
inline int32_t MarketData::_internal_widsellnum() const {
  return _impl_.widsellnum_;
}
inline int32_t MarketData::widsellnum() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WiDSellNum)
  return _internal_widsellnum();
}
inline void MarketData::_internal_set_widsellnum(int32_t value) {
  _impl_._has_bits_[0] |= 0x10000000u;
  _impl_.widsellnum_ = value;
}
inline void MarketData::set_widsellnum(int32_t value) {
  _internal_set_widsellnum(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WiDSellNum)
}

// optional int32 WiDNum = 34;
inline bool MarketData::_internal_has_widnum() const {
  bool value = (_impl_._has_bits_[0] & 0x20000000u) != 0;
  return value;
}
inline bool MarketData::has_widnum() const {
  return _internal_has_widnum();
}
inline void MarketData::clear_widnum() {
  _impl_.widnum_ = 0;
  _impl_._has_bits_[0] &= ~0x20000000u;
}
inline int32_t MarketData::_internal_widnum() const {
  return _impl_.widnum_;
}
inline int32_t MarketData::widnum() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.MarketData.WiDNum)
  return _internal_widnum();
}
inline void MarketData::_internal_set_widnum(int32_t value) {
  _impl_._has_bits_[0] |= 0x20000000u;
  _impl_.widnum_ = value;
}
inline void MarketData::set_widnum(int32_t value) {
  _internal_set_widnum(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.MarketData.WiDNum)
}

// -------------------------------------------------------------------

// Transaction

// required string ExchangeID = 1;
inline bool Transaction::_internal_has_exchangeid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Transaction::has_exchangeid() const {
  return _internal_has_exchangeid();
}
inline void Transaction::clear_exchangeid() {
  _impl_.exchangeid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& Transaction::exchangeid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.ExchangeID)
  return _internal_exchangeid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_exchangeid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.exchangeid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.ExchangeID)
}
inline std::string* Transaction::mutable_exchangeid() {
  std::string* _s = _internal_mutable_exchangeid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.ExchangeID)
  return _s;
}
inline const std::string& Transaction::_internal_exchangeid() const {
  return _impl_.exchangeid_.Get();
}
inline void Transaction::_internal_set_exchangeid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.exchangeid_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_exchangeid() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.exchangeid_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_exchangeid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.ExchangeID)
  if (!_internal_has_exchangeid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.exchangeid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchangeid_.IsDefault()) {
    _impl_.exchangeid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_exchangeid(std::string* exchangeid) {
  if (exchangeid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.exchangeid_.SetAllocated(exchangeid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchangeid_.IsDefault()) {
    _impl_.exchangeid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.ExchangeID)
}

// required string Category = 2;
inline bool Transaction::_internal_has_category() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Transaction::has_category() const {
  return _internal_has_category();
}
inline void Transaction::clear_category() {
  _impl_.category_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& Transaction::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_category(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.category_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Category)
}
inline std::string* Transaction::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Category)
  return _s;
}
inline const std::string& Transaction::_internal_category() const {
  return _impl_.category_.Get();
}
inline void Transaction::_internal_set_category(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.category_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_category() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.category_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Category)
  if (!_internal_has_category()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.category_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.category_.SetAllocated(category, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Category)
}

// required string SecurityID = 3;
inline bool Transaction::_internal_has_securityid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool Transaction::has_securityid() const {
  return _internal_has_securityid();
}
inline void Transaction::clear_securityid() {
  _impl_.securityid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& Transaction::securityid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.SecurityID)
  return _internal_securityid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_securityid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.securityid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.SecurityID)
}
inline std::string* Transaction::mutable_securityid() {
  std::string* _s = _internal_mutable_securityid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.SecurityID)
  return _s;
}
inline const std::string& Transaction::_internal_securityid() const {
  return _impl_.securityid_.Get();
}
inline void Transaction::_internal_set_securityid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.securityid_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_securityid() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.securityid_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_securityid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.SecurityID)
  if (!_internal_has_securityid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.securityid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.securityid_.IsDefault()) {
    _impl_.securityid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_securityid(std::string* securityid) {
  if (securityid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.securityid_.SetAllocated(securityid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.securityid_.IsDefault()) {
    _impl_.securityid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.SecurityID)
}

// optional string PubSvrTime = 4;
inline bool Transaction::_internal_has_pubsvrtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool Transaction::has_pubsvrtime() const {
  return _internal_has_pubsvrtime();
}
inline void Transaction::clear_pubsvrtime() {
  _impl_.pubsvrtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const std::string& Transaction::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return _internal_pubsvrtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_pubsvrtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000008u;
 _impl_.pubsvrtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.PubSvrTime)
}
inline std::string* Transaction::mutable_pubsvrtime() {
  std::string* _s = _internal_mutable_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.PubSvrTime)
  return _s;
}
inline const std::string& Transaction::_internal_pubsvrtime() const {
  return _impl_.pubsvrtime_.Get();
}
inline void Transaction::_internal_set_pubsvrtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.pubsvrtime_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_pubsvrtime() {
  _impl_._has_bits_[0] |= 0x00000008u;
  return _impl_.pubsvrtime_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.PubSvrTime)
  if (!_internal_has_pubsvrtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000008u;
  auto* p = _impl_.pubsvrtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_pubsvrtime(std::string* pubsvrtime) {
  if (pubsvrtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.pubsvrtime_.SetAllocated(pubsvrtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.PubSvrTime)
}

// optional string TradeIndex = 5;
inline bool Transaction::_internal_has_tradeindex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool Transaction::has_tradeindex() const {
  return _internal_has_tradeindex();
}
inline void Transaction::clear_tradeindex() {
  _impl_.tradeindex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline const std::string& Transaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeIndex)
  return _internal_tradeindex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_tradeindex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000010u;
 _impl_.tradeindex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeIndex)
}
inline std::string* Transaction::mutable_tradeindex() {
  std::string* _s = _internal_mutable_tradeindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeIndex)
  return _s;
}
inline const std::string& Transaction::_internal_tradeindex() const {
  return _impl_.tradeindex_.Get();
}
inline void Transaction::_internal_set_tradeindex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.tradeindex_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_tradeindex() {
  _impl_._has_bits_[0] |= 0x00000010u;
  return _impl_.tradeindex_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_tradeindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeIndex)
  if (!_internal_has_tradeindex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000010u;
  auto* p = _impl_.tradeindex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradeindex_.IsDefault()) {
    _impl_.tradeindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_tradeindex(std::string* tradeindex) {
  if (tradeindex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  _impl_.tradeindex_.SetAllocated(tradeindex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradeindex_.IsDefault()) {
    _impl_.tradeindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeIndex)
}

// optional string TradeTime = 6;
inline bool Transaction::_internal_has_tradetime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool Transaction::has_tradetime() const {
  return _internal_has_tradetime();
}
inline void Transaction::clear_tradetime() {
  _impl_.tradetime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline const std::string& Transaction::tradetime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeTime)
  return _internal_tradetime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_tradetime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000020u;
 _impl_.tradetime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeTime)
}
inline std::string* Transaction::mutable_tradetime() {
  std::string* _s = _internal_mutable_tradetime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeTime)
  return _s;
}
inline const std::string& Transaction::_internal_tradetime() const {
  return _impl_.tradetime_.Get();
}
inline void Transaction::_internal_set_tradetime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.tradetime_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_tradetime() {
  _impl_._has_bits_[0] |= 0x00000020u;
  return _impl_.tradetime_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_tradetime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeTime)
  if (!_internal_has_tradetime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000020u;
  auto* p = _impl_.tradetime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradetime_.IsDefault()) {
    _impl_.tradetime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_tradetime(std::string* tradetime) {
  if (tradetime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000020u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000020u;
  }
  _impl_.tradetime_.SetAllocated(tradetime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradetime_.IsDefault()) {
    _impl_.tradetime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeTime)
}

// optional int64 TradePrice = 7;
inline bool Transaction::_internal_has_tradeprice() const {
  bool value = (_impl_._has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool Transaction::has_tradeprice() const {
  return _internal_has_tradeprice();
}
inline void Transaction::clear_tradeprice() {
  _impl_.tradeprice_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00001000u;
}
inline int64_t Transaction::_internal_tradeprice() const {
  return _impl_.tradeprice_;
}
inline int64_t Transaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradePrice)
  return _internal_tradeprice();
}
inline void Transaction::_internal_set_tradeprice(int64_t value) {
  _impl_._has_bits_[0] |= 0x00001000u;
  _impl_.tradeprice_ = value;
}
inline void Transaction::set_tradeprice(int64_t value) {
  _internal_set_tradeprice(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradePrice)
}

// optional int32 TradeQty = 8;
inline bool Transaction::_internal_has_tradeqty() const {
  bool value = (_impl_._has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool Transaction::has_tradeqty() const {
  return _internal_has_tradeqty();
}
inline void Transaction::clear_tradeqty() {
  _impl_.tradeqty_ = 0;
  _impl_._has_bits_[0] &= ~0x00004000u;
}
inline int32_t Transaction::_internal_tradeqty() const {
  return _impl_.tradeqty_;
}
inline int32_t Transaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeQty)
  return _internal_tradeqty();
}
inline void Transaction::_internal_set_tradeqty(int32_t value) {
  _impl_._has_bits_[0] |= 0x00004000u;
  _impl_.tradeqty_ = value;
}
inline void Transaction::set_tradeqty(int32_t value) {
  _internal_set_tradeqty(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeQty)
}

// optional int64 TradeMoney = 9;
inline bool Transaction::_internal_has_trademoney() const {
  bool value = (_impl_._has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool Transaction::has_trademoney() const {
  return _internal_has_trademoney();
}
inline void Transaction::clear_trademoney() {
  _impl_.trademoney_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00002000u;
}
inline int64_t Transaction::_internal_trademoney() const {
  return _impl_.trademoney_;
}
inline int64_t Transaction::trademoney() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeMoney)
  return _internal_trademoney();
}
inline void Transaction::_internal_set_trademoney(int64_t value) {
  _impl_._has_bits_[0] |= 0x00002000u;
  _impl_.trademoney_ = value;
}
inline void Transaction::set_trademoney(int64_t value) {
  _internal_set_trademoney(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeMoney)
}

// optional string Side = 10;
inline bool Transaction::_internal_has_side() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool Transaction::has_side() const {
  return _internal_has_side();
}
inline void Transaction::clear_side() {
  _impl_.side_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline const std::string& Transaction::side() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.Side)
  return _internal_side();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_side(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000040u;
 _impl_.side_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.Side)
}
inline std::string* Transaction::mutable_side() {
  std::string* _s = _internal_mutable_side();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.Side)
  return _s;
}
inline const std::string& Transaction::_internal_side() const {
  return _impl_.side_.Get();
}
inline void Transaction::_internal_set_side(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.side_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_side() {
  _impl_._has_bits_[0] |= 0x00000040u;
  return _impl_.side_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_side() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.Side)
  if (!_internal_has_side()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000040u;
  auto* p = _impl_.side_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.side_.IsDefault()) {
    _impl_.side_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_side(std::string* side) {
  if (side != nullptr) {
    _impl_._has_bits_[0] |= 0x00000040u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000040u;
  }
  _impl_.side_.SetAllocated(side, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.side_.IsDefault()) {
    _impl_.side_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.Side)
}

// optional string TradeType = 11;
inline bool Transaction::_internal_has_tradetype() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool Transaction::has_tradetype() const {
  return _internal_has_tradetype();
}
inline void Transaction::clear_tradetype() {
  _impl_.tradetype_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline const std::string& Transaction::tradetype() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeType)
  return _internal_tradetype();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_tradetype(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000080u;
 _impl_.tradetype_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeType)
}
inline std::string* Transaction::mutable_tradetype() {
  std::string* _s = _internal_mutable_tradetype();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeType)
  return _s;
}
inline const std::string& Transaction::_internal_tradetype() const {
  return _impl_.tradetype_.Get();
}
inline void Transaction::_internal_set_tradetype(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.tradetype_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_tradetype() {
  _impl_._has_bits_[0] |= 0x00000080u;
  return _impl_.tradetype_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_tradetype() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeType)
  if (!_internal_has_tradetype()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000080u;
  auto* p = _impl_.tradetype_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradetype_.IsDefault()) {
    _impl_.tradetype_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_tradetype(std::string* tradetype) {
  if (tradetype != nullptr) {
    _impl_._has_bits_[0] |= 0x00000080u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000080u;
  }
  _impl_.tradetype_.SetAllocated(tradetype, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradetype_.IsDefault()) {
    _impl_.tradetype_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeType)
}

// optional string TradeCode = 12;
inline bool Transaction::_internal_has_tradecode() const {
  bool value = (_impl_._has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool Transaction::has_tradecode() const {
  return _internal_has_tradecode();
}
inline void Transaction::clear_tradecode() {
  _impl_.tradecode_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000100u;
}
inline const std::string& Transaction::tradecode() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.TradeCode)
  return _internal_tradecode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_tradecode(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000100u;
 _impl_.tradecode_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.TradeCode)
}
inline std::string* Transaction::mutable_tradecode() {
  std::string* _s = _internal_mutable_tradecode();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.TradeCode)
  return _s;
}
inline const std::string& Transaction::_internal_tradecode() const {
  return _impl_.tradecode_.Get();
}
inline void Transaction::_internal_set_tradecode(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000100u;
  _impl_.tradecode_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_tradecode() {
  _impl_._has_bits_[0] |= 0x00000100u;
  return _impl_.tradecode_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_tradecode() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.TradeCode)
  if (!_internal_has_tradecode()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000100u;
  auto* p = _impl_.tradecode_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradecode_.IsDefault()) {
    _impl_.tradecode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_tradecode(std::string* tradecode) {
  if (tradecode != nullptr) {
    _impl_._has_bits_[0] |= 0x00000100u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000100u;
  }
  _impl_.tradecode_.SetAllocated(tradecode, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradecode_.IsDefault()) {
    _impl_.tradecode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.TradeCode)
}

// optional string OfferIndex = 13;
inline bool Transaction::_internal_has_offerindex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool Transaction::has_offerindex() const {
  return _internal_has_offerindex();
}
inline void Transaction::clear_offerindex() {
  _impl_.offerindex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000200u;
}
inline const std::string& Transaction::offerindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.OfferIndex)
  return _internal_offerindex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_offerindex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000200u;
 _impl_.offerindex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.OfferIndex)
}
inline std::string* Transaction::mutable_offerindex() {
  std::string* _s = _internal_mutable_offerindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.OfferIndex)
  return _s;
}
inline const std::string& Transaction::_internal_offerindex() const {
  return _impl_.offerindex_.Get();
}
inline void Transaction::_internal_set_offerindex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000200u;
  _impl_.offerindex_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_offerindex() {
  _impl_._has_bits_[0] |= 0x00000200u;
  return _impl_.offerindex_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_offerindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.OfferIndex)
  if (!_internal_has_offerindex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000200u;
  auto* p = _impl_.offerindex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.offerindex_.IsDefault()) {
    _impl_.offerindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_offerindex(std::string* offerindex) {
  if (offerindex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000200u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000200u;
  }
  _impl_.offerindex_.SetAllocated(offerindex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.offerindex_.IsDefault()) {
    _impl_.offerindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.OfferIndex)
}

// optional string BidIndex = 14;
inline bool Transaction::_internal_has_bidindex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool Transaction::has_bidindex() const {
  return _internal_has_bidindex();
}
inline void Transaction::clear_bidindex() {
  _impl_.bidindex_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000400u;
}
inline const std::string& Transaction::bidindex() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.BidIndex)
  return _internal_bidindex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_bidindex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000400u;
 _impl_.bidindex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.BidIndex)
}
inline std::string* Transaction::mutable_bidindex() {
  std::string* _s = _internal_mutable_bidindex();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.BidIndex)
  return _s;
}
inline const std::string& Transaction::_internal_bidindex() const {
  return _impl_.bidindex_.Get();
}
inline void Transaction::_internal_set_bidindex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000400u;
  _impl_.bidindex_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_bidindex() {
  _impl_._has_bits_[0] |= 0x00000400u;
  return _impl_.bidindex_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_bidindex() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.BidIndex)
  if (!_internal_has_bidindex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000400u;
  auto* p = _impl_.bidindex_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.bidindex_.IsDefault()) {
    _impl_.bidindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_bidindex(std::string* bidindex) {
  if (bidindex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000400u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000400u;
  }
  _impl_.bidindex_.SetAllocated(bidindex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.bidindex_.IsDefault()) {
    _impl_.bidindex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.BidIndex)
}

// optional string reserve = 15;
inline bool Transaction::_internal_has_reserve() const {
  bool value = (_impl_._has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool Transaction::has_reserve() const {
  return _internal_has_reserve();
}
inline void Transaction::clear_reserve() {
  _impl_.reserve_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000800u;
}
inline const std::string& Transaction::reserve() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.Transaction.reserve)
  return _internal_reserve();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Transaction::set_reserve(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000800u;
 _impl_.reserve_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.Transaction.reserve)
}
inline std::string* Transaction::mutable_reserve() {
  std::string* _s = _internal_mutable_reserve();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.Transaction.reserve)
  return _s;
}
inline const std::string& Transaction::_internal_reserve() const {
  return _impl_.reserve_.Get();
}
inline void Transaction::_internal_set_reserve(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000800u;
  _impl_.reserve_.Set(value, GetArenaForAllocation());
}
inline std::string* Transaction::_internal_mutable_reserve() {
  _impl_._has_bits_[0] |= 0x00000800u;
  return _impl_.reserve_.Mutable(GetArenaForAllocation());
}
inline std::string* Transaction::release_reserve() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.Transaction.reserve)
  if (!_internal_has_reserve()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000800u;
  auto* p = _impl_.reserve_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.reserve_.IsDefault()) {
    _impl_.reserve_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Transaction::set_allocated_reserve(std::string* reserve) {
  if (reserve != nullptr) {
    _impl_._has_bits_[0] |= 0x00000800u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000800u;
  }
  _impl_.reserve_.SetAllocated(reserve, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.reserve_.IsDefault()) {
    _impl_.reserve_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.Transaction.reserve)
}

// -------------------------------------------------------------------

// FutureMarketData

// required string ExchId = 1;
inline bool FutureMarketData::_internal_has_exchid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool FutureMarketData::has_exchid() const {
  return _internal_has_exchid();
}
inline void FutureMarketData::clear_exchid() {
  _impl_.exchid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& FutureMarketData::exchid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return _internal_exchid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_exchid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.exchid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchId)
}
inline std::string* FutureMarketData::mutable_exchid() {
  std::string* _s = _internal_mutable_exchid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchId)
  return _s;
}
inline const std::string& FutureMarketData::_internal_exchid() const {
  return _impl_.exchid_.Get();
}
inline void FutureMarketData::_internal_set_exchid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.exchid_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_exchid() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.exchid_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_exchid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchId)
  if (!_internal_has_exchid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.exchid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchid_.IsDefault()) {
    _impl_.exchid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_exchid(std::string* exchid) {
  if (exchid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.exchid_.SetAllocated(exchid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchid_.IsDefault()) {
    _impl_.exchid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchId)
}

// required string Category = 2;
inline bool FutureMarketData::_internal_has_category() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool FutureMarketData::has_category() const {
  return _internal_has_category();
}
inline void FutureMarketData::clear_category() {
  _impl_.category_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& FutureMarketData::category() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Category)
  return _internal_category();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_category(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.category_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Category)
}
inline std::string* FutureMarketData::mutable_category() {
  std::string* _s = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Category)
  return _s;
}
inline const std::string& FutureMarketData::_internal_category() const {
  return _impl_.category_.Get();
}
inline void FutureMarketData::_internal_set_category(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.category_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_category() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.category_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_category() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Category)
  if (!_internal_has_category()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.category_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_category(std::string* category) {
  if (category != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.category_.SetAllocated(category, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.category_.IsDefault()) {
    _impl_.category_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Category)
}

// required string stkId = 3;
inline bool FutureMarketData::_internal_has_stkid() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool FutureMarketData::has_stkid() const {
  return _internal_has_stkid();
}
inline void FutureMarketData::clear_stkid() {
  _impl_.stkid_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& FutureMarketData::stkid() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.stkId)
  return _internal_stkid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_stkid(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.stkid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.stkId)
}
inline std::string* FutureMarketData::mutable_stkid() {
  std::string* _s = _internal_mutable_stkid();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.stkId)
  return _s;
}
inline const std::string& FutureMarketData::_internal_stkid() const {
  return _impl_.stkid_.Get();
}
inline void FutureMarketData::_internal_set_stkid(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.stkid_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_stkid() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.stkid_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_stkid() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.stkId)
  if (!_internal_has_stkid()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.stkid_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.stkid_.IsDefault()) {
    _impl_.stkid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_stkid(std::string* stkid) {
  if (stkid != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.stkid_.SetAllocated(stkid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.stkid_.IsDefault()) {
    _impl_.stkid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.stkId)
}

// optional string RcvSvrTime = 4;
inline bool FutureMarketData::_internal_has_rcvsvrtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool FutureMarketData::has_rcvsvrtime() const {
  return _internal_has_rcvsvrtime();
}
inline void FutureMarketData::clear_rcvsvrtime() {
  _impl_.rcvsvrtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const std::string& FutureMarketData::rcvsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return _internal_rcvsvrtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_rcvsvrtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000008u;
 _impl_.rcvsvrtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}
inline std::string* FutureMarketData::mutable_rcvsvrtime() {
  std::string* _s = _internal_mutable_rcvsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  return _s;
}
inline const std::string& FutureMarketData::_internal_rcvsvrtime() const {
  return _impl_.rcvsvrtime_.Get();
}
inline void FutureMarketData::_internal_set_rcvsvrtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.rcvsvrtime_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_rcvsvrtime() {
  _impl_._has_bits_[0] |= 0x00000008u;
  return _impl_.rcvsvrtime_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_rcvsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
  if (!_internal_has_rcvsvrtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000008u;
  auto* p = _impl_.rcvsvrtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rcvsvrtime_.IsDefault()) {
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_rcvsvrtime(std::string* rcvsvrtime) {
  if (rcvsvrtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.rcvsvrtime_.SetAllocated(rcvsvrtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rcvsvrtime_.IsDefault()) {
    _impl_.rcvsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.RcvSvrTime)
}

// optional string PubSvrTime = 5;
inline bool FutureMarketData::_internal_has_pubsvrtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool FutureMarketData::has_pubsvrtime() const {
  return _internal_has_pubsvrtime();
}
inline void FutureMarketData::clear_pubsvrtime() {
  _impl_.pubsvrtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline const std::string& FutureMarketData::pubsvrtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return _internal_pubsvrtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_pubsvrtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000010u;
 _impl_.pubsvrtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}
inline std::string* FutureMarketData::mutable_pubsvrtime() {
  std::string* _s = _internal_mutable_pubsvrtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  return _s;
}
inline const std::string& FutureMarketData::_internal_pubsvrtime() const {
  return _impl_.pubsvrtime_.Get();
}
inline void FutureMarketData::_internal_set_pubsvrtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.pubsvrtime_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_pubsvrtime() {
  _impl_._has_bits_[0] |= 0x00000010u;
  return _impl_.pubsvrtime_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_pubsvrtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
  if (!_internal_has_pubsvrtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000010u;
  auto* p = _impl_.pubsvrtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_pubsvrtime(std::string* pubsvrtime) {
  if (pubsvrtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  _impl_.pubsvrtime_.SetAllocated(pubsvrtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pubsvrtime_.IsDefault()) {
    _impl_.pubsvrtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.PubSvrTime)
}

// optional string Status = 6;
inline bool FutureMarketData::_internal_has_status() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool FutureMarketData::has_status() const {
  return _internal_has_status();
}
inline void FutureMarketData::clear_status() {
  _impl_.status_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline const std::string& FutureMarketData::status() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Status)
  return _internal_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_status(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000020u;
 _impl_.status_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Status)
}
inline std::string* FutureMarketData::mutable_status() {
  std::string* _s = _internal_mutable_status();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.Status)
  return _s;
}
inline const std::string& FutureMarketData::_internal_status() const {
  return _impl_.status_.Get();
}
inline void FutureMarketData::_internal_set_status(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.status_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_status() {
  _impl_._has_bits_[0] |= 0x00000020u;
  return _impl_.status_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_status() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.Status)
  if (!_internal_has_status()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000020u;
  auto* p = _impl_.status_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_.IsDefault()) {
    _impl_.status_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_status(std::string* status) {
  if (status != nullptr) {
    _impl_._has_bits_[0] |= 0x00000020u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000020u;
  }
  _impl_.status_.SetAllocated(status, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_.IsDefault()) {
    _impl_.status_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.Status)
}

// optional string ExchTime = 7;
inline bool FutureMarketData::_internal_has_exchtime() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool FutureMarketData::has_exchtime() const {
  return _internal_has_exchtime();
}
inline void FutureMarketData::clear_exchtime() {
  _impl_.exchtime_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline const std::string& FutureMarketData::exchtime() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return _internal_exchtime();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_exchtime(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000040u;
 _impl_.exchtime_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}
inline std::string* FutureMarketData::mutable_exchtime() {
  std::string* _s = _internal_mutable_exchtime();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  return _s;
}
inline const std::string& FutureMarketData::_internal_exchtime() const {
  return _impl_.exchtime_.Get();
}
inline void FutureMarketData::_internal_set_exchtime(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.exchtime_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_exchtime() {
  _impl_._has_bits_[0] |= 0x00000040u;
  return _impl_.exchtime_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_exchtime() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.ExchTime)
  if (!_internal_has_exchtime()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000040u;
  auto* p = _impl_.exchtime_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchtime_.IsDefault()) {
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_exchtime(std::string* exchtime) {
  if (exchtime != nullptr) {
    _impl_._has_bits_[0] |= 0x00000040u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000040u;
  }
  _impl_.exchtime_.SetAllocated(exchtime, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exchtime_.IsDefault()) {
    _impl_.exchtime_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.ExchTime)
}

// optional string TradeDate = 8;
inline bool FutureMarketData::_internal_has_tradedate() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool FutureMarketData::has_tradedate() const {
  return _internal_has_tradedate();
}
inline void FutureMarketData::clear_tradedate() {
  _impl_.tradedate_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline const std::string& FutureMarketData::tradedate() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return _internal_tradedate();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FutureMarketData::set_tradedate(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000080u;
 _impl_.tradedate_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}
inline std::string* FutureMarketData::mutable_tradedate() {
  std::string* _s = _internal_mutable_tradedate();
  // @@protoc_insertion_point(field_mutable:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  return _s;
}
inline const std::string& FutureMarketData::_internal_tradedate() const {
  return _impl_.tradedate_.Get();
}
inline void FutureMarketData::_internal_set_tradedate(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.tradedate_.Set(value, GetArenaForAllocation());
}
inline std::string* FutureMarketData::_internal_mutable_tradedate() {
  _impl_._has_bits_[0] |= 0x00000080u;
  return _impl_.tradedate_.Mutable(GetArenaForAllocation());
}
inline std::string* FutureMarketData::release_tradedate() {
  // @@protoc_insertion_point(field_release:LYPROTO.QUOTA.FutureMarketData.TradeDate)
  if (!_internal_has_tradedate()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000080u;
  auto* p = _impl_.tradedate_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradedate_.IsDefault()) {
    _impl_.tradedate_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void FutureMarketData::set_allocated_tradedate(std::string* tradedate) {
  if (tradedate != nullptr) {
    _impl_._has_bits_[0] |= 0x00000080u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000080u;
  }
  _impl_.tradedate_.SetAllocated(tradedate, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tradedate_.IsDefault()) {
    _impl_.tradedate_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:LYPROTO.QUOTA.FutureMarketData.TradeDate)
}

// optional int64 PreClose = 9;
inline bool FutureMarketData::_internal_has_preclose() const {
  bool value = (_impl_._has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool FutureMarketData::has_preclose() const {
  return _internal_has_preclose();
}
inline void FutureMarketData::clear_preclose() {
  _impl_.preclose_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000100u;
}
inline int64_t FutureMarketData::_internal_preclose() const {
  return _impl_.preclose_;
}
inline int64_t FutureMarketData::preclose() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreClose)
  return _internal_preclose();
}
inline void FutureMarketData::_internal_set_preclose(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000100u;
  _impl_.preclose_ = value;
}
inline void FutureMarketData::set_preclose(int64_t value) {
  _internal_set_preclose(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreClose)
}

// optional int64 PreSettle = 10;
inline bool FutureMarketData::_internal_has_presettle() const {
  bool value = (_impl_._has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool FutureMarketData::has_presettle() const {
  return _internal_has_presettle();
}
inline void FutureMarketData::clear_presettle() {
  _impl_.presettle_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000200u;
}
inline int64_t FutureMarketData::_internal_presettle() const {
  return _impl_.presettle_;
}
inline int64_t FutureMarketData::presettle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreSettle)
  return _internal_presettle();
}
inline void FutureMarketData::_internal_set_presettle(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000200u;
  _impl_.presettle_ = value;
}
inline void FutureMarketData::set_presettle(int64_t value) {
  _internal_set_presettle(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreSettle)
}

// optional int32 PreOpenPos = 11;
inline bool FutureMarketData::_internal_has_preopenpos() const {
  bool value = (_impl_._has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool FutureMarketData::has_preopenpos() const {
  return _internal_has_preopenpos();
}
inline void FutureMarketData::clear_preopenpos() {
  _impl_.preopenpos_ = 0;
  _impl_._has_bits_[0] &= ~0x00004000u;
}
inline int32_t FutureMarketData::_internal_preopenpos() const {
  return _impl_.preopenpos_;
}
inline int32_t FutureMarketData::preopenpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
  return _internal_preopenpos();
}
inline void FutureMarketData::_internal_set_preopenpos(int32_t value) {
  _impl_._has_bits_[0] |= 0x00004000u;
  _impl_.preopenpos_ = value;
}
inline void FutureMarketData::set_preopenpos(int32_t value) {
  _internal_set_preopenpos(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreOpenPos)
}

// optional int64 HighLimit = 12;
inline bool FutureMarketData::_internal_has_highlimit() const {
  bool value = (_impl_._has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool FutureMarketData::has_highlimit() const {
  return _internal_has_highlimit();
}
inline void FutureMarketData::clear_highlimit() {
  _impl_.highlimit_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000400u;
}
inline int64_t FutureMarketData::_internal_highlimit() const {
  return _impl_.highlimit_;
}
inline int64_t FutureMarketData::highlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.HighLimit)
  return _internal_highlimit();
}
inline void FutureMarketData::_internal_set_highlimit(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000400u;
  _impl_.highlimit_ = value;
}
inline void FutureMarketData::set_highlimit(int64_t value) {
  _internal_set_highlimit(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.HighLimit)
}

// optional int64 LowLimit = 13;
inline bool FutureMarketData::_internal_has_lowlimit() const {
  bool value = (_impl_._has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool FutureMarketData::has_lowlimit() const {
  return _internal_has_lowlimit();
}
inline void FutureMarketData::clear_lowlimit() {
  _impl_.lowlimit_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000800u;
}
inline int64_t FutureMarketData::_internal_lowlimit() const {
  return _impl_.lowlimit_;
}
inline int64_t FutureMarketData::lowlimit() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LowLimit)
  return _internal_lowlimit();
}
inline void FutureMarketData::_internal_set_lowlimit(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000800u;
  _impl_.lowlimit_ = value;
}
inline void FutureMarketData::set_lowlimit(int64_t value) {
  _internal_set_lowlimit(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LowLimit)
}

// optional int64 Open = 14;
inline bool FutureMarketData::_internal_has_open() const {
  bool value = (_impl_._has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool FutureMarketData::has_open() const {
  return _internal_has_open();
}
inline void FutureMarketData::clear_open() {
  _impl_.open_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00001000u;
}
inline int64_t FutureMarketData::_internal_open() const {
  return _impl_.open_;
}
inline int64_t FutureMarketData::open() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Open)
  return _internal_open();
}
inline void FutureMarketData::_internal_set_open(int64_t value) {
  _impl_._has_bits_[0] |= 0x00001000u;
  _impl_.open_ = value;
}
inline void FutureMarketData::set_open(int64_t value) {
  _internal_set_open(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Open)
}

// optional int64 Latest = 15;
inline bool FutureMarketData::_internal_has_latest() const {
  bool value = (_impl_._has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool FutureMarketData::has_latest() const {
  return _internal_has_latest();
}
inline void FutureMarketData::clear_latest() {
  _impl_.latest_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00002000u;
}
inline int64_t FutureMarketData::_internal_latest() const {
  return _impl_.latest_;
}
inline int64_t FutureMarketData::latest() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Latest)
  return _internal_latest();
}
inline void FutureMarketData::_internal_set_latest(int64_t value) {
  _impl_._has_bits_[0] |= 0x00002000u;
  _impl_.latest_ = value;
}
inline void FutureMarketData::set_latest(int64_t value) {
  _internal_set_latest(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Latest)
}

// optional int64 High = 16;
inline bool FutureMarketData::_internal_has_high() const {
  bool value = (_impl_._has_bits_[0] & 0x00010000u) != 0;
  return value;
}
inline bool FutureMarketData::has_high() const {
  return _internal_has_high();
}
inline void FutureMarketData::clear_high() {
  _impl_.high_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00010000u;
}
inline int64_t FutureMarketData::_internal_high() const {
  return _impl_.high_;
}
inline int64_t FutureMarketData::high() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.High)
  return _internal_high();
}
inline void FutureMarketData::_internal_set_high(int64_t value) {
  _impl_._has_bits_[0] |= 0x00010000u;
  _impl_.high_ = value;
}
inline void FutureMarketData::set_high(int64_t value) {
  _internal_set_high(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.High)
}

// optional int64 Low = 17;
inline bool FutureMarketData::_internal_has_low() const {
  bool value = (_impl_._has_bits_[0] & 0x00020000u) != 0;
  return value;
}
inline bool FutureMarketData::has_low() const {
  return _internal_has_low();
}
inline void FutureMarketData::clear_low() {
  _impl_.low_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00020000u;
}
inline int64_t FutureMarketData::_internal_low() const {
  return _impl_.low_;
}
inline int64_t FutureMarketData::low() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Low)
  return _internal_low();
}
inline void FutureMarketData::_internal_set_low(int64_t value) {
  _impl_._has_bits_[0] |= 0x00020000u;
  _impl_.low_ = value;
}
inline void FutureMarketData::set_low(int64_t value) {
  _internal_set_low(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Low)
}

// optional int64 Settle = 18;
inline bool FutureMarketData::_internal_has_settle() const {
  bool value = (_impl_._has_bits_[0] & 0x00040000u) != 0;
  return value;
}
inline bool FutureMarketData::has_settle() const {
  return _internal_has_settle();
}
inline void FutureMarketData::clear_settle() {
  _impl_.settle_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00040000u;
}
inline int64_t FutureMarketData::_internal_settle() const {
  return _impl_.settle_;
}
inline int64_t FutureMarketData::settle() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Settle)
  return _internal_settle();
}
inline void FutureMarketData::_internal_set_settle(int64_t value) {
  _impl_._has_bits_[0] |= 0x00040000u;
  _impl_.settle_ = value;
}
inline void FutureMarketData::set_settle(int64_t value) {
  _internal_set_settle(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Settle)
}

// optional int32 LatestVolume = 19;
inline bool FutureMarketData::_internal_has_latestvolume() const {
  bool value = (_impl_._has_bits_[0] & 0x00008000u) != 0;
  return value;
}
inline bool FutureMarketData::has_latestvolume() const {
  return _internal_has_latestvolume();
}
inline void FutureMarketData::clear_latestvolume() {
  _impl_.latestvolume_ = 0;
  _impl_._has_bits_[0] &= ~0x00008000u;
}
inline int32_t FutureMarketData::_internal_latestvolume() const {
  return _impl_.latestvolume_;
}
inline int32_t FutureMarketData::latestvolume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
  return _internal_latestvolume();
}
inline void FutureMarketData::_internal_set_latestvolume(int32_t value) {
  _impl_._has_bits_[0] |= 0x00008000u;
  _impl_.latestvolume_ = value;
}
inline void FutureMarketData::set_latestvolume(int32_t value) {
  _internal_set_latestvolume(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.LatestVolume)
}

// optional int32 Volume = 20;
inline bool FutureMarketData::_internal_has_volume() const {
  bool value = (_impl_._has_bits_[0] & 0x00080000u) != 0;
  return value;
}
inline bool FutureMarketData::has_volume() const {
  return _internal_has_volume();
}
inline void FutureMarketData::clear_volume() {
  _impl_.volume_ = 0;
  _impl_._has_bits_[0] &= ~0x00080000u;
}
inline int32_t FutureMarketData::_internal_volume() const {
  return _impl_.volume_;
}
inline int32_t FutureMarketData::volume() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Volume)
  return _internal_volume();
}
inline void FutureMarketData::_internal_set_volume(int32_t value) {
  _impl_._has_bits_[0] |= 0x00080000u;
  _impl_.volume_ = value;
}
inline void FutureMarketData::set_volume(int32_t value) {
  _internal_set_volume(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Volume)
}

// optional int32 OpenPos = 21;
inline bool FutureMarketData::_internal_has_openpos() const {
  bool value = (_impl_._has_bits_[0] & 0x00100000u) != 0;
  return value;
}
inline bool FutureMarketData::has_openpos() const {
  return _internal_has_openpos();
}
inline void FutureMarketData::clear_openpos() {
  _impl_.openpos_ = 0;
  _impl_._has_bits_[0] &= ~0x00100000u;
}
inline int32_t FutureMarketData::_internal_openpos() const {
  return _impl_.openpos_;
}
inline int32_t FutureMarketData::openpos() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.OpenPos)
  return _internal_openpos();
}
inline void FutureMarketData::_internal_set_openpos(int32_t value) {
  _impl_._has_bits_[0] |= 0x00100000u;
  _impl_.openpos_ = value;
}
inline void FutureMarketData::set_openpos(int32_t value) {
  _internal_set_openpos(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.OpenPos)
}

// optional int64 Value = 22;
inline bool FutureMarketData::_internal_has_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00200000u) != 0;
  return value;
}
inline bool FutureMarketData::has_value() const {
  return _internal_has_value();
}
inline void FutureMarketData::clear_value() {
  _impl_.value_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00200000u;
}
inline int64_t FutureMarketData::_internal_value() const {
  return _impl_.value_;
}
inline int64_t FutureMarketData::value() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.Value)
  return _internal_value();
}
inline void FutureMarketData::_internal_set_value(int64_t value) {
  _impl_._has_bits_[0] |= 0x00200000u;
  _impl_.value_ = value;
}
inline void FutureMarketData::set_value(int64_t value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.Value)
}

// repeated int32 BA = 23 [packed = true];
inline int FutureMarketData::_internal_ba_size() const {
  return _impl_.ba_.size();
}
inline int FutureMarketData::ba_size() const {
  return _internal_ba_size();
}
inline void FutureMarketData::clear_ba() {
  _impl_.ba_.Clear();
}
inline int32_t FutureMarketData::_internal_ba(int index) const {
  return _impl_.ba_.Get(index);
}
inline int32_t FutureMarketData::ba(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BA)
  return _internal_ba(index);
}
inline void FutureMarketData::set_ba(int index, int32_t value) {
  _impl_.ba_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BA)
}
inline void FutureMarketData::_internal_add_ba(int32_t value) {
  _impl_.ba_.Add(value);
}
inline void FutureMarketData::add_ba(int32_t value) {
  _internal_add_ba(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BA)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
FutureMarketData::_internal_ba() const {
  return _impl_.ba_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
FutureMarketData::ba() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return _internal_ba();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
FutureMarketData::_internal_mutable_ba() {
  return &_impl_.ba_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
FutureMarketData::mutable_ba() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BA)
  return _internal_mutable_ba();
}

// repeated int64 BP = 24 [packed = true];
inline int FutureMarketData::_internal_bp_size() const {
  return _impl_.bp_.size();
}
inline int FutureMarketData::bp_size() const {
  return _internal_bp_size();
}
inline void FutureMarketData::clear_bp() {
  _impl_.bp_.Clear();
}
inline int64_t FutureMarketData::_internal_bp(int index) const {
  return _impl_.bp_.Get(index);
}
inline int64_t FutureMarketData::bp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.BP)
  return _internal_bp(index);
}
inline void FutureMarketData::set_bp(int index, int64_t value) {
  _impl_.bp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.BP)
}
inline void FutureMarketData::_internal_add_bp(int64_t value) {
  _impl_.bp_.Add(value);
}
inline void FutureMarketData::add_bp(int64_t value) {
  _internal_add_bp(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.BP)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
FutureMarketData::_internal_bp() const {
  return _impl_.bp_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
FutureMarketData::bp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return _internal_bp();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
FutureMarketData::_internal_mutable_bp() {
  return &_impl_.bp_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
FutureMarketData::mutable_bp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.BP)
  return _internal_mutable_bp();
}

// repeated int32 SA = 25 [packed = true];
inline int FutureMarketData::_internal_sa_size() const {
  return _impl_.sa_.size();
}
inline int FutureMarketData::sa_size() const {
  return _internal_sa_size();
}
inline void FutureMarketData::clear_sa() {
  _impl_.sa_.Clear();
}
inline int32_t FutureMarketData::_internal_sa(int index) const {
  return _impl_.sa_.Get(index);
}
inline int32_t FutureMarketData::sa(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SA)
  return _internal_sa(index);
}
inline void FutureMarketData::set_sa(int index, int32_t value) {
  _impl_.sa_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SA)
}
inline void FutureMarketData::_internal_add_sa(int32_t value) {
  _impl_.sa_.Add(value);
}
inline void FutureMarketData::add_sa(int32_t value) {
  _internal_add_sa(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SA)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
FutureMarketData::_internal_sa() const {
  return _impl_.sa_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
FutureMarketData::sa() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return _internal_sa();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
FutureMarketData::_internal_mutable_sa() {
  return &_impl_.sa_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
FutureMarketData::mutable_sa() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SA)
  return _internal_mutable_sa();
}

// repeated int64 SP = 26 [packed = true];
inline int FutureMarketData::_internal_sp_size() const {
  return _impl_.sp_.size();
}
inline int FutureMarketData::sp_size() const {
  return _internal_sp_size();
}
inline void FutureMarketData::clear_sp() {
  _impl_.sp_.Clear();
}
inline int64_t FutureMarketData::_internal_sp(int index) const {
  return _impl_.sp_.Get(index);
}
inline int64_t FutureMarketData::sp(int index) const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.SP)
  return _internal_sp(index);
}
inline void FutureMarketData::set_sp(int index, int64_t value) {
  _impl_.sp_.Set(index, value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.SP)
}
inline void FutureMarketData::_internal_add_sp(int64_t value) {
  _impl_.sp_.Add(value);
}
inline void FutureMarketData::add_sp(int64_t value) {
  _internal_add_sp(value);
  // @@protoc_insertion_point(field_add:LYPROTO.QUOTA.FutureMarketData.SP)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
FutureMarketData::_internal_sp() const {
  return _impl_.sp_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
FutureMarketData::sp() const {
  // @@protoc_insertion_point(field_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return _internal_sp();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
FutureMarketData::_internal_mutable_sp() {
  return &_impl_.sp_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
FutureMarketData::mutable_sp() {
  // @@protoc_insertion_point(field_mutable_list:LYPROTO.QUOTA.FutureMarketData.SP)
  return _internal_mutable_sp();
}

// optional int32 PreDelta = 27;
inline bool FutureMarketData::_internal_has_predelta() const {
  bool value = (_impl_._has_bits_[0] & 0x00400000u) != 0;
  return value;
}
inline bool FutureMarketData::has_predelta() const {
  return _internal_has_predelta();
}
inline void FutureMarketData::clear_predelta() {
  _impl_.predelta_ = 0;
  _impl_._has_bits_[0] &= ~0x00400000u;
}
inline int32_t FutureMarketData::_internal_predelta() const {
  return _impl_.predelta_;
}
inline int32_t FutureMarketData::predelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.PreDelta)
  return _internal_predelta();
}
inline void FutureMarketData::_internal_set_predelta(int32_t value) {
  _impl_._has_bits_[0] |= 0x00400000u;
  _impl_.predelta_ = value;
}
inline void FutureMarketData::set_predelta(int32_t value) {
  _internal_set_predelta(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.PreDelta)
}

// optional int32 CurDelta = 28;
inline bool FutureMarketData::_internal_has_curdelta() const {
  bool value = (_impl_._has_bits_[0] & 0x00800000u) != 0;
  return value;
}
inline bool FutureMarketData::has_curdelta() const {
  return _internal_has_curdelta();
}
inline void FutureMarketData::clear_curdelta() {
  _impl_.curdelta_ = 0;
  _impl_._has_bits_[0] &= ~0x00800000u;
}
inline int32_t FutureMarketData::_internal_curdelta() const {
  return _impl_.curdelta_;
}
inline int32_t FutureMarketData::curdelta() const {
  // @@protoc_insertion_point(field_get:LYPROTO.QUOTA.FutureMarketData.CurDelta)
  return _internal_curdelta();
}
inline void FutureMarketData::_internal_set_curdelta(int32_t value) {
  _impl_._has_bits_[0] |= 0x00800000u;
  _impl_.curdelta_ = value;
}
inline void FutureMarketData::set_curdelta(int32_t value) {
  _internal_set_curdelta(value);
  // @@protoc_insertion_point(field_set:LYPROTO.QUOTA.FutureMarketData.CurDelta)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace QUOTA
}  // namespace LYPROTO

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_lyproto_2equota_2eproto
