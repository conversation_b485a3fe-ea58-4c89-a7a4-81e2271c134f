// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSAOrder.proto

#ifndef PROTOBUF_MDUSAOrder_2eproto__INCLUDED
#define PROTOBUF_MDUSAOrder_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDUSAOrder_2eproto();
void protobuf_InitDefaults_MDUSAOrder_2eproto();
void protobuf_AssignDesc_MDUSAOrder_2eproto();
void protobuf_ShutdownFile_MDUSAOrder_2eproto();

class MDUSAOrder;

// ===================================================================

class MDUSAOrder : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDUSAOrder) */ {
 public:
  MDUSAOrder();
  virtual ~MDUSAOrder();

  MDUSAOrder(const MDUSAOrder& from);

  inline MDUSAOrder& operator=(const MDUSAOrder& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDUSAOrder& default_instance();

  static const MDUSAOrder* internal_default_instance();

  void Swap(MDUSAOrder* other);

  // implements Message ----------------------------------------------

  inline MDUSAOrder* New() const { return New(NULL); }

  MDUSAOrder* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDUSAOrder& from);
  void MergeFrom(const MDUSAOrder& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDUSAOrder* other);
  void UnsafeMergeFrom(const MDUSAOrder& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 7;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 7;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 8;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 8;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 Nanosecond = 9;
  void clear_nanosecond();
  static const int kNanosecondFieldNumber = 9;
  ::google::protobuf::int32 nanosecond() const;
  void set_nanosecond(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 10;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 10;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int64 OrderIndex = 11;
  void clear_orderindex();
  static const int kOrderIndexFieldNumber = 11;
  ::google::protobuf::int64 orderindex() const;
  void set_orderindex(::google::protobuf::int64 value);

  // optional int64 OriginalOrderIndex = 12;
  void clear_originalorderindex();
  static const int kOriginalOrderIndexFieldNumber = 12;
  ::google::protobuf::int64 originalorderindex() const;
  void set_originalorderindex(::google::protobuf::int64 value);

  // optional int32 OrderType = 13;
  void clear_ordertype();
  static const int kOrderTypeFieldNumber = 13;
  ::google::protobuf::int32 ordertype() const;
  void set_ordertype(::google::protobuf::int32 value);

  // optional int64 OrderPrice = 14;
  void clear_orderprice();
  static const int kOrderPriceFieldNumber = 14;
  ::google::protobuf::int64 orderprice() const;
  void set_orderprice(::google::protobuf::int64 value);

  // optional double OrderQty = 15;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 15;
  double orderqty() const;
  void set_orderqty(double value);

  // optional int32 OrderBSFlag = 16;
  void clear_orderbsflag();
  static const int kOrderBSFlagFieldNumber = 16;
  ::google::protobuf::int32 orderbsflag() const;
  void set_orderbsflag(::google::protobuf::int32 value);

  // optional string Attribution = 17;
  void clear_attribution();
  static const int kAttributionFieldNumber = 17;
  const ::std::string& attribution() const;
  void set_attribution(const ::std::string& value);
  void set_attribution(const char* value);
  void set_attribution(const char* value, size_t size);
  ::std::string* mutable_attribution();
  ::std::string* release_attribution();
  void set_allocated_attribution(::std::string* attribution);

  // optional int32 TrackingNum = 18;
  void clear_trackingnum();
  static const int kTrackingNumFieldNumber = 18;
  ::google::protobuf::int32 trackingnum() const;
  void set_trackingnum(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 19;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 19;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 TimeIndex = 20;
  void clear_timeindex();
  static const int kTimeIndexFieldNumber = 20;
  ::google::protobuf::int32 timeindex() const;
  void set_timeindex(::google::protobuf::int32 value);

  // optional int64 DataIndex = 21;
  void clear_dataindex();
  static const int kDataIndexFieldNumber = 21;
  ::google::protobuf::int64 dataindex() const;
  void set_dataindex(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDUSAOrder)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr attribution_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 nanosecond_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int64 orderindex_;
  ::google::protobuf::int64 originalorderindex_;
  ::google::protobuf::int64 orderprice_;
  ::google::protobuf::int32 ordertype_;
  ::google::protobuf::int32 orderbsflag_;
  double orderqty_;
  ::google::protobuf::int32 trackingnum_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int64 dataindex_;
  ::google::protobuf::int32 timeindex_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDUSAOrder_2eproto_impl();
  friend void  protobuf_AddDesc_MDUSAOrder_2eproto_impl();
  friend void protobuf_AssignDesc_MDUSAOrder_2eproto();
  friend void protobuf_ShutdownFile_MDUSAOrder_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDUSAOrder> MDUSAOrder_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSAOrder

// optional string HTSCSecurityID = 1;
inline void MDUSAOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSAOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
inline void MDUSAOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
inline void MDUSAOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
inline ::std::string* MDUSAOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSAOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDUSAOrder::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.MDDate)
  return mddate_;
}
inline void MDUSAOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.MDDate)
}

// optional int32 MDTime = 3;
inline void MDUSAOrder::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.MDTime)
  return mdtime_;
}
inline void MDUSAOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDUSAOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataTimestamp)
  return datatimestamp_;
}
inline void MDUSAOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDUSAOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDUSAOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDUSAOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDUSAOrder::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDUSAOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDUSAOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.securityType)
}

// optional int32 ExchangeDate = 7;
inline void MDUSAOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeDate)
  return exchangedate_;
}
inline void MDUSAOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
inline void MDUSAOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeTime)
  return exchangetime_;
}
inline void MDUSAOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeTime)
}

// optional int32 Nanosecond = 9;
inline void MDUSAOrder::clear_nanosecond() {
  nanosecond_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.Nanosecond)
  return nanosecond_;
}
inline void MDUSAOrder::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.Nanosecond)
}

// optional int32 ChannelNo = 10;
inline void MDUSAOrder::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ChannelNo)
  return channelno_;
}
inline void MDUSAOrder::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ChannelNo)
}

// optional int64 OrderIndex = 11;
inline void MDUSAOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderIndex)
  return orderindex_;
}
inline void MDUSAOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderIndex)
}

// optional int64 OriginalOrderIndex = 12;
inline void MDUSAOrder::clear_originalorderindex() {
  originalorderindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAOrder::originalorderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OriginalOrderIndex)
  return originalorderindex_;
}
inline void MDUSAOrder::set_originalorderindex(::google::protobuf::int64 value) {
  
  originalorderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OriginalOrderIndex)
}

// optional int32 OrderType = 13;
inline void MDUSAOrder::clear_ordertype() {
  ordertype_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderType)
  return ordertype_;
}
inline void MDUSAOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderType)
}

// optional int64 OrderPrice = 14;
inline void MDUSAOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderPrice)
  return orderprice_;
}
inline void MDUSAOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderPrice)
}

// optional double OrderQty = 15;
inline void MDUSAOrder::clear_orderqty() {
  orderqty_ = 0;
}
inline double MDUSAOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderQty)
  return orderqty_;
}
inline void MDUSAOrder::set_orderqty(double value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderQty)
}

// optional int32 OrderBSFlag = 16;
inline void MDUSAOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderBSFlag)
  return orderbsflag_;
}
inline void MDUSAOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderBSFlag)
}

// optional string Attribution = 17;
inline void MDUSAOrder::clear_attribution() {
  attribution_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSAOrder::attribution() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  return attribution_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAOrder::set_attribution(const ::std::string& value) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
inline void MDUSAOrder::set_attribution(const char* value) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
inline void MDUSAOrder::set_attribution(const char* value, size_t size) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
inline ::std::string* MDUSAOrder::mutable_attribution() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  return attribution_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSAOrder::release_attribution() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  
  return attribution_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAOrder::set_allocated_attribution(::std::string* attribution) {
  if (attribution != NULL) {
    
  } else {
    
  }
  attribution_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), attribution);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}

// optional int32 TrackingNum = 18;
inline void MDUSAOrder::clear_trackingnum() {
  trackingnum_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::trackingnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.TrackingNum)
  return trackingnum_;
}
inline void MDUSAOrder::set_trackingnum(::google::protobuf::int32 value) {
  
  trackingnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.TrackingNum)
}

// optional int32 DataMultiplePowerOf10 = 19;
inline void MDUSAOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDUSAOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 20;
inline void MDUSAOrder::clear_timeindex() {
  timeindex_ = 0;
}
inline ::google::protobuf::int32 MDUSAOrder::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.TimeIndex)
  return timeindex_;
}
inline void MDUSAOrder::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.TimeIndex)
}

// optional int64 DataIndex = 21;
inline void MDUSAOrder::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAOrder::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataIndex)
  return dataindex_;
}
inline void MDUSAOrder::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataIndex)
}

inline const MDUSAOrder* MDUSAOrder::internal_default_instance() {
  return &MDUSAOrder_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDUSAOrder_2eproto__INCLUDED
