// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBenchmark.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsBenchmark.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsBenchmark_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsBenchmark_reflection_ = NULL;
const ::google::protobuf::Descriptor* YieldCurve_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  YieldCurve_reflection_ = NULL;
const ::google::protobuf::Descriptor* YieldCurve_Curve_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  YieldCurve_Curve_reflection_ = NULL;
const ::google::protobuf::Descriptor* ShiborData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ShiborData_reflection_ = NULL;
const ::google::protobuf::Descriptor* LoanPrimeRate_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  LoanPrimeRate_reflection_ = NULL;
const ::google::protobuf::Descriptor* FixRepoRate_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  FixRepoRate_reflection_ = NULL;
const ::google::protobuf::Descriptor* RateSwapCurve_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RateSwapCurve_reflection_ = NULL;
const ::google::protobuf::Descriptor* RateSwapCurve_Curve_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RateSwapCurve_Curve_reflection_ = NULL;
const ::google::protobuf::Descriptor* BondValuation_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BondValuation_reflection_ = NULL;
const ::google::protobuf::Descriptor* BondIndex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BondIndex_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsBenchmark_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsBenchmark_2eproto() {
  protobuf_AddDesc_MDCfetsBenchmark_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsBenchmark.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsBenchmark_descriptor_ = file->message_type(0);
  static const int MDCfetsBenchmark_offsets_[26] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, messagenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, referencesymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, volatilitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, calculatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, cfetssecuritytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, cfetssecuritysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, cfetssecuritysubtypecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, rateswapcurveinsertmethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, contingencyindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, benchmarktype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, yieldcurves_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, shibordata_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, loanprimerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, fixreporate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, rateswapcurves_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, bondvaluation_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, bondindex_),
  };
  MDCfetsBenchmark_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsBenchmark_descriptor_,
      MDCfetsBenchmark::internal_default_instance(),
      MDCfetsBenchmark_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsBenchmark),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBenchmark, _internal_metadata_));
  YieldCurve_descriptor_ = file->message_type(1);
  static const int YieldCurve_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, yieldterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, yieldtermtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, settldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, sectermyearly_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, curves_),
  };
  YieldCurve_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      YieldCurve_descriptor_,
      YieldCurve::internal_default_instance(),
      YieldCurve_offsets_,
      -1,
      -1,
      -1,
      sizeof(YieldCurve),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve, _internal_metadata_));
  YieldCurve_Curve_descriptor_ = YieldCurve_descriptor_->nested_type(0);
  static const int YieldCurve_Curve_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve_Curve, curvetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve_Curve, datasource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve_Curve, yieldpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve_Curve, spread_),
  };
  YieldCurve_Curve_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      YieldCurve_Curve_descriptor_,
      YieldCurve_Curve::internal_default_instance(),
      YieldCurve_Curve_offsets_,
      -1,
      -1,
      -1,
      sizeof(YieldCurve_Curve),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(YieldCurve_Curve, _internal_metadata_));
  ShiborData_descriptor_ = file->message_type(2);
  static const int ShiborData_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, effectivedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, shibor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, shiborchange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, shiborquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, shiborquotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, quotepartyid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, quotepartyshortname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, movingaverage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, movingaveragetype_),
  };
  ShiborData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ShiborData_descriptor_,
      ShiborData::internal_default_instance(),
      ShiborData_offsets_,
      -1,
      -1,
      -1,
      sizeof(ShiborData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ShiborData, _internal_metadata_));
  LoanPrimeRate_descriptor_ = file->message_type(3);
  static const int LoanPrimeRate_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoanPrimeRate, effectivedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoanPrimeRate, lpr_),
  };
  LoanPrimeRate_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      LoanPrimeRate_descriptor_,
      LoanPrimeRate::internal_default_instance(),
      LoanPrimeRate_offsets_,
      -1,
      -1,
      -1,
      sizeof(LoanPrimeRate),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(LoanPrimeRate, _internal_metadata_));
  FixRepoRate_descriptor_ = file->message_type(4);
  static const int FixRepoRate_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FixRepoRate, effectivedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FixRepoRate, rate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FixRepoRate, termupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FixRepoRate, termfloorlimit_),
  };
  FixRepoRate_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      FixRepoRate_descriptor_,
      FixRepoRate::internal_default_instance(),
      FixRepoRate_offsets_,
      -1,
      -1,
      -1,
      sizeof(FixRepoRate),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FixRepoRate, _internal_metadata_));
  RateSwapCurve_descriptor_ = file->message_type(5);
  static const int RateSwapCurve_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve, yieldterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve, yieldtermtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve, calculatedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve, curves_),
  };
  RateSwapCurve_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RateSwapCurve_descriptor_,
      RateSwapCurve::internal_default_instance(),
      RateSwapCurve_offsets_,
      -1,
      -1,
      -1,
      sizeof(RateSwapCurve),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve, _internal_metadata_));
  RateSwapCurve_Curve_descriptor_ = RateSwapCurve_descriptor_->nested_type(0);
  static const int RateSwapCurve_Curve_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve_Curve, curvetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve_Curve, yieldpx_),
  };
  RateSwapCurve_Curve_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RateSwapCurve_Curve_descriptor_,
      RateSwapCurve_Curve::internal_default_instance(),
      RateSwapCurve_Curve_offsets_,
      -1,
      -1,
      -1,
      sizeof(RateSwapCurve_Curve),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapCurve_Curve, _internal_metadata_));
  BondValuation_descriptor_ = file->message_type(6);
  static const int BondValuation_offsets_[15] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, transactdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, valuationdatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, currency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, couponratetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, valuationamt_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, dirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, spreadcnydirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, spreadcnyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, valuationyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, spreadduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, spreadconvexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, modifyduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, convexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, spreadpx_),
  };
  BondValuation_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BondValuation_descriptor_,
      BondValuation::internal_default_instance(),
      BondValuation_offsets_,
      -1,
      -1,
      -1,
      sizeof(BondValuation),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondValuation, _internal_metadata_));
  BondIndex_descriptor_ = file->message_type(7);
  static const int BondIndex_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, kcurvetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, totalvolumetrademillion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, totalvaluetrademillion_),
  };
  BondIndex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BondIndex_descriptor_,
      BondIndex::internal_default_instance(),
      BondIndex_offsets_,
      -1,
      -1,
      -1,
      sizeof(BondIndex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondIndex, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsBenchmark_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsBenchmark_descriptor_, MDCfetsBenchmark::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      YieldCurve_descriptor_, YieldCurve::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      YieldCurve_Curve_descriptor_, YieldCurve_Curve::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ShiborData_descriptor_, ShiborData::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      LoanPrimeRate_descriptor_, LoanPrimeRate::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      FixRepoRate_descriptor_, FixRepoRate::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RateSwapCurve_descriptor_, RateSwapCurve::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RateSwapCurve_Curve_descriptor_, RateSwapCurve_Curve::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BondValuation_descriptor_, BondValuation::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BondIndex_descriptor_, BondIndex::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto() {
  MDCfetsBenchmark_default_instance_.Shutdown();
  delete MDCfetsBenchmark_reflection_;
  YieldCurve_default_instance_.Shutdown();
  delete YieldCurve_reflection_;
  YieldCurve_Curve_default_instance_.Shutdown();
  delete YieldCurve_Curve_reflection_;
  ShiborData_default_instance_.Shutdown();
  delete ShiborData_reflection_;
  LoanPrimeRate_default_instance_.Shutdown();
  delete LoanPrimeRate_reflection_;
  FixRepoRate_default_instance_.Shutdown();
  delete FixRepoRate_reflection_;
  RateSwapCurve_default_instance_.Shutdown();
  delete RateSwapCurve_reflection_;
  RateSwapCurve_Curve_default_instance_.Shutdown();
  delete RateSwapCurve_Curve_reflection_;
  BondValuation_default_instance_.Shutdown();
  delete BondValuation_reflection_;
  BondIndex_default_instance_.Shutdown();
  delete BondIndex_reflection_;
}

void protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsBenchmark_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  YieldCurve_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  YieldCurve_Curve_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ShiborData_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  LoanPrimeRate_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  FixRepoRate_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  RateSwapCurve_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  RateSwapCurve_Curve_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BondValuation_default_instance_.DefaultConstruct();
  BondIndex_default_instance_.DefaultConstruct();
  MDCfetsBenchmark_default_instance_.get_mutable()->InitAsDefaultInstance();
  YieldCurve_default_instance_.get_mutable()->InitAsDefaultInstance();
  YieldCurve_Curve_default_instance_.get_mutable()->InitAsDefaultInstance();
  ShiborData_default_instance_.get_mutable()->InitAsDefaultInstance();
  LoanPrimeRate_default_instance_.get_mutable()->InitAsDefaultInstance();
  FixRepoRate_default_instance_.get_mutable()->InitAsDefaultInstance();
  RateSwapCurve_default_instance_.get_mutable()->InitAsDefaultInstance();
  RateSwapCurve_Curve_default_instance_.get_mutable()->InitAsDefaultInstance();
  BondValuation_default_instance_.get_mutable()->InitAsDefaultInstance();
  BondIndex_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsBenchmark_2eproto_once_);
void protobuf_InitDefaults_MDCfetsBenchmark_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsBenchmark_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026MDCfetsBenchmark.proto\022\032com.htsc.mdc.i"
    "nsight.model\032\027ESecurityIDSource.proto\032\023E"
    "SecurityType.proto\"\363\007\n\020MDCfetsBenchmark\022"
    "\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n\014SecurityType\030"
    "\002 \001(\0162!.com.htsc.mdc.model.ESecurityType"
    "\022\?\n\020SecurityIDSource\030\003 \001(\0162%.com.htsc.md"
    "c.model.ESecurityIDSource\022\016\n\006MDDate\030\004 \001("
    "\005\022\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp\030\006 \001(\003"
    "\022\024\n\014TransactTime\030\007 \001(\t\022\027\n\017MarketIndicato"
    "r\030\010 \001(\t\022\035\n\025DataMultiplePowerOf10\030\t \001(\005\022\025"
    "\n\rMessageNumber\030\024 \001(\003\022\027\n\017ReferenceSymbol"
    "\030\025 \001(\t\022\026\n\016VolatilityType\030\026 \001(\t\022\025\n\rCalcul"
    "ateTime\030\027 \001(\t\022\031\n\021CfetsSecurityType\030\030 \001(\t"
    "\022\034\n\024CfetsSecuritySubType\030\031 \001(\t\022 \n\030CfetsS"
    "ecuritySubTypeCode\030\032 \001(\t\022!\n\031RateSwapCurv"
    "eInsertMethod\030\033 \001(\005\022\034\n\024ContingencyIndica"
    "tor\030\034 \001(\010\022\025\n\rBenchmarkType\030c \001(\005\022;\n\013Yiel"
    "dCurves\030d \003(\0132&.com.htsc.mdc.insight.mod"
    "el.YieldCurve\022:\n\nShiborData\030e \001(\0132&.com."
    "htsc.mdc.insight.model.ShiborData\022@\n\rLoa"
    "nPrimeRate\030f \001(\0132).com.htsc.mdc.insight."
    "model.LoanPrimeRate\022<\n\013FixRepoRate\030g \001(\013"
    "2\'.com.htsc.mdc.insight.model.FixRepoRat"
    "e\022A\n\016RateSwapCurves\030h \003(\0132).com.htsc.mdc"
    ".insight.model.RateSwapCurve\022@\n\rBondValu"
    "ation\030i \001(\0132).com.htsc.mdc.insight.model"
    ".BondValuation\0228\n\tBondIndex\030j \001(\0132%.com."
    "htsc.mdc.insight.model.BondIndex\"\357\001\n\nYie"
    "ldCurve\022\021\n\tYieldTerm\030\001 \001(\t\022\025\n\rYieldTermT"
    "ype\030\002 \001(\t\022\021\n\tSettlDate\030\003 \001(\t\022\025\n\rSecTermY"
    "early\030\004 \001(\001\022<\n\006Curves\030d \003(\0132,.com.htsc.m"
    "dc.insight.model.YieldCurve.Curve\032O\n\005Cur"
    "ve\022\021\n\tCurveType\030\001 \001(\t\022\022\n\nDataSource\030\002 \001("
    "\t\022\017\n\007YieldPx\030\003 \001(\001\022\016\n\006Spread\030\004 \001(\001\"\334\001\n\nS"
    "hiborData\022\025\n\rEffectiveDate\030\001 \001(\t\022\016\n\006Shib"
    "or\030\002 \001(\001\022\024\n\014ShiborChange\030\003 \001(\001\022\023\n\013Shibor"
    "Quote\030\004 \001(\001\022\027\n\017ShiborQuoteTime\030\005 \001(\t\022\024\n\014"
    "QuotePartyID\030\006 \001(\t\022\033\n\023QuotePartyShortNam"
    "e\030\007 \001(\t\022\025\n\rMovingAverage\030\010 \001(\001\022\031\n\021Moving"
    "AverageType\030\t \001(\t\"3\n\rLoanPrimeRate\022\025\n\rEf"
    "fectiveDate\030\001 \001(\t\022\013\n\003Lpr\030\002 \001(\001\"b\n\013FixRep"
    "oRate\022\025\n\rEffectiveDate\030\001 \001(\t\022\014\n\004Rate\030\002 \001"
    "(\001\022\026\n\016TermUpperLimit\030\003 \001(\005\022\026\n\016TermFloorL"
    "imit\030\004 \001(\005\"\276\001\n\rRateSwapCurve\022\021\n\tYieldTer"
    "m\030\001 \001(\t\022\025\n\rYieldTermType\030\002 \001(\t\022\025\n\rCalcul"
    "ateDate\030\003 \001(\t\022\?\n\006Curves\030d \003(\0132/.com.htsc"
    ".mdc.insight.model.RateSwapCurve.Curve\032+"
    "\n\005Curve\022\021\n\tCurveType\030\001 \001(\t\022\017\n\007YieldPx\030\002 "
    "\001(\001\"\341\002\n\rBondValuation\022\024\n\014TransactDate\030\001 "
    "\001(\t\022\031\n\021ValuationDateTime\030\002 \001(\t\022\020\n\010Curren"
    "cy\030\003 \001(\t\022\026\n\016CouponRateType\030\004 \001(\t\022\024\n\014Valu"
    "ationAmt\030\005 \001(\001\022\022\n\nDirtyPrice\030\006 \001(\001\022\033\n\023Sp"
    "readCNYDirtyPrice\030\007 \001(\001\022\026\n\016SpreadCNYPric"
    "e\030\010 \001(\001\022\026\n\016ValuationYield\030\t \001(\001\022\026\n\016Sprea"
    "dDuration\030\n \001(\001\022\027\n\017SpreadConvexity\030\013 \001(\001"
    "\022\020\n\010Duration\030\014 \001(\001\022\026\n\016ModifyDuration\030\r \001"
    "(\001\022\021\n\tConvexity\030\016 \001(\001\022\020\n\010SpreadPx\030\017 \001(\001\""
    "\260\001\n\tBondIndex\022\022\n\nKCurveType\030\001 \001(\005\022\016\n\006Las"
    "tPx\030\002 \001(\001\022\016\n\006OpenPx\030\003 \001(\001\022\017\n\007ClosePx\030\004 \001"
    "(\001\022\016\n\006HighPx\030\005 \001(\001\022\r\n\005LowPx\030\006 \001(\001\022\037\n\027Tot"
    "alVolumeTradeMillion\030\007 \001(\001\022\036\n\026TotalValue"
    "TradeMillion\030\010 \001(\001B9\n\032com.htsc.mdc.insig"
    "ht.modelB\026MDCfetsBenchmarkProtosH\001\240\001\001b\006p"
    "roto3", 2525);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsBenchmark.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsBenchmark_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsBenchmark_2eproto_once_);
void protobuf_AddDesc_MDCfetsBenchmark_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsBenchmark_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsBenchmark_2eproto {
  StaticDescriptorInitializer_MDCfetsBenchmark_2eproto() {
    protobuf_AddDesc_MDCfetsBenchmark_2eproto();
  }
} static_descriptor_initializer_MDCfetsBenchmark_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsBenchmark::kHTSCSecurityIDFieldNumber;
const int MDCfetsBenchmark::kSecurityTypeFieldNumber;
const int MDCfetsBenchmark::kSecurityIDSourceFieldNumber;
const int MDCfetsBenchmark::kMDDateFieldNumber;
const int MDCfetsBenchmark::kMDTimeFieldNumber;
const int MDCfetsBenchmark::kDataTimestampFieldNumber;
const int MDCfetsBenchmark::kTransactTimeFieldNumber;
const int MDCfetsBenchmark::kMarketIndicatorFieldNumber;
const int MDCfetsBenchmark::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsBenchmark::kMessageNumberFieldNumber;
const int MDCfetsBenchmark::kReferenceSymbolFieldNumber;
const int MDCfetsBenchmark::kVolatilityTypeFieldNumber;
const int MDCfetsBenchmark::kCalculateTimeFieldNumber;
const int MDCfetsBenchmark::kCfetsSecurityTypeFieldNumber;
const int MDCfetsBenchmark::kCfetsSecuritySubTypeFieldNumber;
const int MDCfetsBenchmark::kCfetsSecuritySubTypeCodeFieldNumber;
const int MDCfetsBenchmark::kRateSwapCurveInsertMethodFieldNumber;
const int MDCfetsBenchmark::kContingencyIndicatorFieldNumber;
const int MDCfetsBenchmark::kBenchmarkTypeFieldNumber;
const int MDCfetsBenchmark::kYieldCurvesFieldNumber;
const int MDCfetsBenchmark::kShiborDataFieldNumber;
const int MDCfetsBenchmark::kLoanPrimeRateFieldNumber;
const int MDCfetsBenchmark::kFixRepoRateFieldNumber;
const int MDCfetsBenchmark::kRateSwapCurvesFieldNumber;
const int MDCfetsBenchmark::kBondValuationFieldNumber;
const int MDCfetsBenchmark::kBondIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsBenchmark::MDCfetsBenchmark()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsBenchmark)
}

void MDCfetsBenchmark::InitAsDefaultInstance() {
  shibordata_ = const_cast< ::com::htsc::mdc::insight::model::ShiborData*>(
      ::com::htsc::mdc::insight::model::ShiborData::internal_default_instance());
  loanprimerate_ = const_cast< ::com::htsc::mdc::insight::model::LoanPrimeRate*>(
      ::com::htsc::mdc::insight::model::LoanPrimeRate::internal_default_instance());
  fixreporate_ = const_cast< ::com::htsc::mdc::insight::model::FixRepoRate*>(
      ::com::htsc::mdc::insight::model::FixRepoRate::internal_default_instance());
  bondvaluation_ = const_cast< ::com::htsc::mdc::insight::model::BondValuation*>(
      ::com::htsc::mdc::insight::model::BondValuation::internal_default_instance());
  bondindex_ = const_cast< ::com::htsc::mdc::insight::model::BondIndex*>(
      ::com::htsc::mdc::insight::model::BondIndex::internal_default_instance());
}

MDCfetsBenchmark::MDCfetsBenchmark(const MDCfetsBenchmark& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsBenchmark)
}

void MDCfetsBenchmark::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  referencesymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitytype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritytype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtypecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shibordata_ = NULL;
  loanprimerate_ = NULL;
  fixreporate_ = NULL;
  bondvaluation_ = NULL;
  bondindex_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&benchmarktype_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(benchmarktype_));
  _cached_size_ = 0;
}

MDCfetsBenchmark::~MDCfetsBenchmark() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  SharedDtor();
}

void MDCfetsBenchmark::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  referencesymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitytype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritytype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtypecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsBenchmark_default_instance_.get()) {
    delete shibordata_;
    delete loanprimerate_;
    delete fixreporate_;
    delete bondvaluation_;
    delete bondindex_;
  }
}

void MDCfetsBenchmark::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsBenchmark::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsBenchmark_descriptor_;
}

const MDCfetsBenchmark& MDCfetsBenchmark::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBenchmark> MDCfetsBenchmark_default_instance_;

MDCfetsBenchmark* MDCfetsBenchmark::New(::google::protobuf::Arena* arena) const {
  MDCfetsBenchmark* n = new MDCfetsBenchmark;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsBenchmark::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsBenchmark, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsBenchmark*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(messagenumber_, datamultiplepowerof10_);
  referencesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cfetssecuritysubtypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(rateswapcurveinsertmethod_, benchmarktype_);
  if (GetArenaNoVirtual() == NULL && shibordata_ != NULL) delete shibordata_;
  shibordata_ = NULL;
  if (GetArenaNoVirtual() == NULL && loanprimerate_ != NULL) delete loanprimerate_;
  loanprimerate_ = NULL;
  if (GetArenaNoVirtual() == NULL && fixreporate_ != NULL) delete fixreporate_;
  fixreporate_ = NULL;
  if (GetArenaNoVirtual() == NULL && bondvaluation_ != NULL) delete bondvaluation_;
  bondvaluation_ = NULL;
  if (GetArenaNoVirtual() == NULL && bondindex_ != NULL) delete bondindex_;
  bondindex_ = NULL;

#undef ZR_HELPER_
#undef ZR_

  yieldcurves_.Clear();
  rateswapcurves_.Clear();
}

bool MDCfetsBenchmark::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 20;
      case 20: {
        if (tag == 160) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_ReferenceSymbol;
        break;
      }

      // optional string ReferenceSymbol = 21;
      case 21: {
        if (tag == 170) {
         parse_ReferenceSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_referencesymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->referencesymbol().data(), this->referencesymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_VolatilityType;
        break;
      }

      // optional string VolatilityType = 22;
      case 22: {
        if (tag == 178) {
         parse_VolatilityType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_volatilitytype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->volatilitytype().data(), this->volatilitytype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_CalculateTime;
        break;
      }

      // optional string CalculateTime = 23;
      case 23: {
        if (tag == 186) {
         parse_CalculateTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_calculatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->calculatetime().data(), this->calculatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_CfetsSecurityType;
        break;
      }

      // optional string CfetsSecurityType = 24;
      case 24: {
        if (tag == 194) {
         parse_CfetsSecurityType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cfetssecuritytype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cfetssecuritytype().data(), this->cfetssecuritytype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_CfetsSecuritySubType;
        break;
      }

      // optional string CfetsSecuritySubType = 25;
      case 25: {
        if (tag == 202) {
         parse_CfetsSecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cfetssecuritysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cfetssecuritysubtype().data(), this->cfetssecuritysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_CfetsSecuritySubTypeCode;
        break;
      }

      // optional string CfetsSecuritySubTypeCode = 26;
      case 26: {
        if (tag == 210) {
         parse_CfetsSecuritySubTypeCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cfetssecuritysubtypecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cfetssecuritysubtypecode().data(), this->cfetssecuritysubtypecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_RateSwapCurveInsertMethod;
        break;
      }

      // optional int32 RateSwapCurveInsertMethod = 27;
      case 27: {
        if (tag == 216) {
         parse_RateSwapCurveInsertMethod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateswapcurveinsertmethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_ContingencyIndicator;
        break;
      }

      // optional bool ContingencyIndicator = 28;
      case 28: {
        if (tag == 224) {
         parse_ContingencyIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &contingencyindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(792)) goto parse_BenchmarkType;
        break;
      }

      // optional int32 BenchmarkType = 99;
      case 99: {
        if (tag == 792) {
         parse_BenchmarkType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &benchmarktype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_YieldCurves;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
      case 100: {
        if (tag == 802) {
         parse_YieldCurves:
          DO_(input->IncrementRecursionDepth());
         parse_loop_YieldCurves:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_yieldcurves()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_loop_YieldCurves;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(810)) goto parse_ShiborData;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
      case 101: {
        if (tag == 810) {
         parse_ShiborData:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_shibordata()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(818)) goto parse_LoanPrimeRate;
        break;
      }

      // optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
      case 102: {
        if (tag == 818) {
         parse_LoanPrimeRate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_loanprimerate()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(826)) goto parse_FixRepoRate;
        break;
      }

      // optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
      case 103: {
        if (tag == 826) {
         parse_FixRepoRate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_fixreporate()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(834)) goto parse_RateSwapCurves;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
      case 104: {
        if (tag == 834) {
         parse_RateSwapCurves:
          DO_(input->IncrementRecursionDepth());
         parse_loop_RateSwapCurves:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_rateswapcurves()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(834)) goto parse_loop_RateSwapCurves;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(842)) goto parse_BondValuation;
        break;
      }

      // optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
      case 105: {
        if (tag == 842) {
         parse_BondValuation:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondvaluation()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(850)) goto parse_BondIndex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
      case 106: {
        if (tag == 850) {
         parse_BondIndex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondindex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  return false;
#undef DO_
}

void MDCfetsBenchmark::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional int64 MessageNumber = 20;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->messagenumber(), output);
  }

  // optional string ReferenceSymbol = 21;
  if (this->referencesymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->referencesymbol().data(), this->referencesymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->referencesymbol(), output);
  }

  // optional string VolatilityType = 22;
  if (this->volatilitytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitytype().data(), this->volatilitytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->volatilitytype(), output);
  }

  // optional string CalculateTime = 23;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->calculatetime(), output);
  }

  // optional string CfetsSecurityType = 24;
  if (this->cfetssecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritytype().data(), this->cfetssecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->cfetssecuritytype(), output);
  }

  // optional string CfetsSecuritySubType = 25;
  if (this->cfetssecuritysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritysubtype().data(), this->cfetssecuritysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->cfetssecuritysubtype(), output);
  }

  // optional string CfetsSecuritySubTypeCode = 26;
  if (this->cfetssecuritysubtypecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritysubtypecode().data(), this->cfetssecuritysubtypecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      26, this->cfetssecuritysubtypecode(), output);
  }

  // optional int32 RateSwapCurveInsertMethod = 27;
  if (this->rateswapcurveinsertmethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(27, this->rateswapcurveinsertmethod(), output);
  }

  // optional bool ContingencyIndicator = 28;
  if (this->contingencyindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(28, this->contingencyindicator(), output);
  }

  // optional int32 BenchmarkType = 99;
  if (this->benchmarktype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(99, this->benchmarktype(), output);
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
  for (unsigned int i = 0, n = this->yieldcurves_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      100, this->yieldcurves(i), output);
  }

  // optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
  if (this->has_shibordata()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      101, *this->shibordata_, output);
  }

  // optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
  if (this->has_loanprimerate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      102, *this->loanprimerate_, output);
  }

  // optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
  if (this->has_fixreporate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      103, *this->fixreporate_, output);
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
  for (unsigned int i = 0, n = this->rateswapcurves_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      104, this->rateswapcurves(i), output);
  }

  // optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
  if (this->has_bondvaluation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      105, *this->bondvaluation_, output);
  }

  // optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
  if (this->has_bondindex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      106, *this->bondindex_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsBenchmark)
}

::google::protobuf::uint8* MDCfetsBenchmark::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional int64 MessageNumber = 20;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->messagenumber(), target);
  }

  // optional string ReferenceSymbol = 21;
  if (this->referencesymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->referencesymbol().data(), this->referencesymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->referencesymbol(), target);
  }

  // optional string VolatilityType = 22;
  if (this->volatilitytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitytype().data(), this->volatilitytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->volatilitytype(), target);
  }

  // optional string CalculateTime = 23;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->calculatetime(), target);
  }

  // optional string CfetsSecurityType = 24;
  if (this->cfetssecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritytype().data(), this->cfetssecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->cfetssecuritytype(), target);
  }

  // optional string CfetsSecuritySubType = 25;
  if (this->cfetssecuritysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritysubtype().data(), this->cfetssecuritysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->cfetssecuritysubtype(), target);
  }

  // optional string CfetsSecuritySubTypeCode = 26;
  if (this->cfetssecuritysubtypecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cfetssecuritysubtypecode().data(), this->cfetssecuritysubtypecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        26, this->cfetssecuritysubtypecode(), target);
  }

  // optional int32 RateSwapCurveInsertMethod = 27;
  if (this->rateswapcurveinsertmethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(27, this->rateswapcurveinsertmethod(), target);
  }

  // optional bool ContingencyIndicator = 28;
  if (this->contingencyindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(28, this->contingencyindicator(), target);
  }

  // optional int32 BenchmarkType = 99;
  if (this->benchmarktype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(99, this->benchmarktype(), target);
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
  for (unsigned int i = 0, n = this->yieldcurves_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        100, this->yieldcurves(i), false, target);
  }

  // optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
  if (this->has_shibordata()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        101, *this->shibordata_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
  if (this->has_loanprimerate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        102, *this->loanprimerate_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
  if (this->has_fixreporate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        103, *this->fixreporate_, false, target);
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
  for (unsigned int i = 0, n = this->rateswapcurves_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        104, this->rateswapcurves(i), false, target);
  }

  // optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
  if (this->has_bondvaluation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        105, *this->bondvaluation_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
  if (this->has_bondindex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        106, *this->bondindex_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  return target;
}

size_t MDCfetsBenchmark::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 MessageNumber = 20;
  if (this->messagenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  // optional string ReferenceSymbol = 21;
  if (this->referencesymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->referencesymbol());
  }

  // optional string VolatilityType = 22;
  if (this->volatilitytype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->volatilitytype());
  }

  // optional string CalculateTime = 23;
  if (this->calculatetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->calculatetime());
  }

  // optional string CfetsSecurityType = 24;
  if (this->cfetssecuritytype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cfetssecuritytype());
  }

  // optional string CfetsSecuritySubType = 25;
  if (this->cfetssecuritysubtype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cfetssecuritysubtype());
  }

  // optional string CfetsSecuritySubTypeCode = 26;
  if (this->cfetssecuritysubtypecode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cfetssecuritysubtypecode());
  }

  // optional int32 RateSwapCurveInsertMethod = 27;
  if (this->rateswapcurveinsertmethod() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateswapcurveinsertmethod());
  }

  // optional bool ContingencyIndicator = 28;
  if (this->contingencyindicator() != 0) {
    total_size += 2 + 1;
  }

  // optional int32 BenchmarkType = 99;
  if (this->benchmarktype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->benchmarktype());
  }

  // optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
  if (this->has_shibordata()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->shibordata_);
  }

  // optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
  if (this->has_loanprimerate()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->loanprimerate_);
  }

  // optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
  if (this->has_fixreporate()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->fixreporate_);
  }

  // optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
  if (this->has_bondvaluation()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondvaluation_);
  }

  // optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
  if (this->has_bondindex()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondindex_);
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
  {
    unsigned int count = this->yieldcurves_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->yieldcurves(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
  {
    unsigned int count = this->rateswapcurves_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->rateswapcurves(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsBenchmark::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsBenchmark* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsBenchmark>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsBenchmark)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsBenchmark)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsBenchmark::MergeFrom(const MDCfetsBenchmark& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsBenchmark::UnsafeMergeFrom(const MDCfetsBenchmark& from) {
  GOOGLE_DCHECK(&from != this);
  yieldcurves_.MergeFrom(from.yieldcurves_);
  rateswapcurves_.MergeFrom(from.rateswapcurves_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
  if (from.referencesymbol().size() > 0) {

    referencesymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.referencesymbol_);
  }
  if (from.volatilitytype().size() > 0) {

    volatilitytype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.volatilitytype_);
  }
  if (from.calculatetime().size() > 0) {

    calculatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.calculatetime_);
  }
  if (from.cfetssecuritytype().size() > 0) {

    cfetssecuritytype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cfetssecuritytype_);
  }
  if (from.cfetssecuritysubtype().size() > 0) {

    cfetssecuritysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cfetssecuritysubtype_);
  }
  if (from.cfetssecuritysubtypecode().size() > 0) {

    cfetssecuritysubtypecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cfetssecuritysubtypecode_);
  }
  if (from.rateswapcurveinsertmethod() != 0) {
    set_rateswapcurveinsertmethod(from.rateswapcurveinsertmethod());
  }
  if (from.contingencyindicator() != 0) {
    set_contingencyindicator(from.contingencyindicator());
  }
  if (from.benchmarktype() != 0) {
    set_benchmarktype(from.benchmarktype());
  }
  if (from.has_shibordata()) {
    mutable_shibordata()->::com::htsc::mdc::insight::model::ShiborData::MergeFrom(from.shibordata());
  }
  if (from.has_loanprimerate()) {
    mutable_loanprimerate()->::com::htsc::mdc::insight::model::LoanPrimeRate::MergeFrom(from.loanprimerate());
  }
  if (from.has_fixreporate()) {
    mutable_fixreporate()->::com::htsc::mdc::insight::model::FixRepoRate::MergeFrom(from.fixreporate());
  }
  if (from.has_bondvaluation()) {
    mutable_bondvaluation()->::com::htsc::mdc::insight::model::BondValuation::MergeFrom(from.bondvaluation());
  }
  if (from.has_bondindex()) {
    mutable_bondindex()->::com::htsc::mdc::insight::model::BondIndex::MergeFrom(from.bondindex());
  }
}

void MDCfetsBenchmark::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsBenchmark::CopyFrom(const MDCfetsBenchmark& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBenchmark)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsBenchmark::IsInitialized() const {

  return true;
}

void MDCfetsBenchmark::Swap(MDCfetsBenchmark* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsBenchmark::InternalSwap(MDCfetsBenchmark* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(messagenumber_, other->messagenumber_);
  referencesymbol_.Swap(&other->referencesymbol_);
  volatilitytype_.Swap(&other->volatilitytype_);
  calculatetime_.Swap(&other->calculatetime_);
  cfetssecuritytype_.Swap(&other->cfetssecuritytype_);
  cfetssecuritysubtype_.Swap(&other->cfetssecuritysubtype_);
  cfetssecuritysubtypecode_.Swap(&other->cfetssecuritysubtypecode_);
  std::swap(rateswapcurveinsertmethod_, other->rateswapcurveinsertmethod_);
  std::swap(contingencyindicator_, other->contingencyindicator_);
  std::swap(benchmarktype_, other->benchmarktype_);
  yieldcurves_.UnsafeArenaSwap(&other->yieldcurves_);
  std::swap(shibordata_, other->shibordata_);
  std::swap(loanprimerate_, other->loanprimerate_);
  std::swap(fixreporate_, other->fixreporate_);
  rateswapcurves_.UnsafeArenaSwap(&other->rateswapcurves_);
  std::swap(bondvaluation_, other->bondvaluation_);
  std::swap(bondindex_, other->bondindex_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsBenchmark::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsBenchmark_descriptor_;
  metadata.reflection = MDCfetsBenchmark_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBenchmark

// optional string HTSCSecurityID = 1;
void MDCfetsBenchmark::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
void MDCfetsBenchmark::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
void MDCfetsBenchmark::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
::std::string* MDCfetsBenchmark::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsBenchmark::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsBenchmark::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsBenchmark::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsBenchmark::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsBenchmark::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsBenchmark::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsBenchmark::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsBenchmark::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDDate)
  return mddate_;
}
void MDCfetsBenchmark::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsBenchmark::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsBenchmark::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDTime)
  return mdtime_;
}
void MDCfetsBenchmark::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsBenchmark::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBenchmark::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsBenchmark::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsBenchmark::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
void MDCfetsBenchmark::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
void MDCfetsBenchmark::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
::std::string* MDCfetsBenchmark::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsBenchmark::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
void MDCfetsBenchmark::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
void MDCfetsBenchmark::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
::std::string* MDCfetsBenchmark::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDCfetsBenchmark::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsBenchmark::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsBenchmark::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 20;
void MDCfetsBenchmark::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBenchmark::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MessageNumber)
  return messagenumber_;
}
void MDCfetsBenchmark::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MessageNumber)
}

// optional string ReferenceSymbol = 21;
void MDCfetsBenchmark::clear_referencesymbol() {
  referencesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::referencesymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  return referencesymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_referencesymbol(const ::std::string& value) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
void MDCfetsBenchmark::set_referencesymbol(const char* value) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
void MDCfetsBenchmark::set_referencesymbol(const char* value, size_t size) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
::std::string* MDCfetsBenchmark::mutable_referencesymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  return referencesymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_referencesymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  
  return referencesymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_referencesymbol(::std::string* referencesymbol) {
  if (referencesymbol != NULL) {
    
  } else {
    
  }
  referencesymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), referencesymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}

// optional string VolatilityType = 22;
void MDCfetsBenchmark::clear_volatilitytype() {
  volatilitytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::volatilitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  return volatilitytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_volatilitytype(const ::std::string& value) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
void MDCfetsBenchmark::set_volatilitytype(const char* value) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
void MDCfetsBenchmark::set_volatilitytype(const char* value, size_t size) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
::std::string* MDCfetsBenchmark::mutable_volatilitytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  return volatilitytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_volatilitytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  
  return volatilitytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_volatilitytype(::std::string* volatilitytype) {
  if (volatilitytype != NULL) {
    
  } else {
    
  }
  volatilitytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}

// optional string CalculateTime = 23;
void MDCfetsBenchmark::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
void MDCfetsBenchmark::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
void MDCfetsBenchmark::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
::std::string* MDCfetsBenchmark::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}

// optional string CfetsSecurityType = 24;
void MDCfetsBenchmark::clear_cfetssecuritytype() {
  cfetssecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::cfetssecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  return cfetssecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_cfetssecuritytype(const ::std::string& value) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
void MDCfetsBenchmark::set_cfetssecuritytype(const char* value) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
void MDCfetsBenchmark::set_cfetssecuritytype(const char* value, size_t size) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
::std::string* MDCfetsBenchmark::mutable_cfetssecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  return cfetssecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_cfetssecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  
  return cfetssecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_cfetssecuritytype(::std::string* cfetssecuritytype) {
  if (cfetssecuritytype != NULL) {
    
  } else {
    
  }
  cfetssecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}

// optional string CfetsSecuritySubType = 25;
void MDCfetsBenchmark::clear_cfetssecuritysubtype() {
  cfetssecuritysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::cfetssecuritysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  return cfetssecuritysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_cfetssecuritysubtype(const ::std::string& value) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
void MDCfetsBenchmark::set_cfetssecuritysubtype(const char* value) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
void MDCfetsBenchmark::set_cfetssecuritysubtype(const char* value, size_t size) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
::std::string* MDCfetsBenchmark::mutable_cfetssecuritysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  return cfetssecuritysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_cfetssecuritysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  
  return cfetssecuritysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_cfetssecuritysubtype(::std::string* cfetssecuritysubtype) {
  if (cfetssecuritysubtype != NULL) {
    
  } else {
    
  }
  cfetssecuritysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}

// optional string CfetsSecuritySubTypeCode = 26;
void MDCfetsBenchmark::clear_cfetssecuritysubtypecode() {
  cfetssecuritysubtypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBenchmark::cfetssecuritysubtypecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  return cfetssecuritysubtypecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const ::std::string& value) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const char* value) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const char* value, size_t size) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
::std::string* MDCfetsBenchmark::mutable_cfetssecuritysubtypecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  return cfetssecuritysubtypecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBenchmark::release_cfetssecuritysubtypecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  
  return cfetssecuritysubtypecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBenchmark::set_allocated_cfetssecuritysubtypecode(::std::string* cfetssecuritysubtypecode) {
  if (cfetssecuritysubtypecode != NULL) {
    
  } else {
    
  }
  cfetssecuritysubtypecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritysubtypecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}

// optional int32 RateSwapCurveInsertMethod = 27;
void MDCfetsBenchmark::clear_rateswapcurveinsertmethod() {
  rateswapcurveinsertmethod_ = 0;
}
::google::protobuf::int32 MDCfetsBenchmark::rateswapcurveinsertmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurveInsertMethod)
  return rateswapcurveinsertmethod_;
}
void MDCfetsBenchmark::set_rateswapcurveinsertmethod(::google::protobuf::int32 value) {
  
  rateswapcurveinsertmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurveInsertMethod)
}

// optional bool ContingencyIndicator = 28;
void MDCfetsBenchmark::clear_contingencyindicator() {
  contingencyindicator_ = false;
}
bool MDCfetsBenchmark::contingencyindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ContingencyIndicator)
  return contingencyindicator_;
}
void MDCfetsBenchmark::set_contingencyindicator(bool value) {
  
  contingencyindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.ContingencyIndicator)
}

// optional int32 BenchmarkType = 99;
void MDCfetsBenchmark::clear_benchmarktype() {
  benchmarktype_ = 0;
}
::google::protobuf::int32 MDCfetsBenchmark::benchmarktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BenchmarkType)
  return benchmarktype_;
}
void MDCfetsBenchmark::set_benchmarktype(::google::protobuf::int32 value) {
  
  benchmarktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.BenchmarkType)
}

// repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
int MDCfetsBenchmark::yieldcurves_size() const {
  return yieldcurves_.size();
}
void MDCfetsBenchmark::clear_yieldcurves() {
  yieldcurves_.Clear();
}
const ::com::htsc::mdc::insight::model::YieldCurve& MDCfetsBenchmark::yieldcurves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Get(index);
}
::com::htsc::mdc::insight::model::YieldCurve* MDCfetsBenchmark::mutable_yieldcurves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Mutable(index);
}
::com::htsc::mdc::insight::model::YieldCurve* MDCfetsBenchmark::add_yieldcurves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >*
MDCfetsBenchmark::mutable_yieldcurves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return &yieldcurves_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >&
MDCfetsBenchmark::yieldcurves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_;
}

// optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
bool MDCfetsBenchmark::has_shibordata() const {
  return this != internal_default_instance() && shibordata_ != NULL;
}
void MDCfetsBenchmark::clear_shibordata() {
  if (GetArenaNoVirtual() == NULL && shibordata_ != NULL) delete shibordata_;
  shibordata_ = NULL;
}
const ::com::htsc::mdc::insight::model::ShiborData& MDCfetsBenchmark::shibordata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  return shibordata_ != NULL ? *shibordata_
                         : *::com::htsc::mdc::insight::model::ShiborData::internal_default_instance();
}
::com::htsc::mdc::insight::model::ShiborData* MDCfetsBenchmark::mutable_shibordata() {
  
  if (shibordata_ == NULL) {
    shibordata_ = new ::com::htsc::mdc::insight::model::ShiborData;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  return shibordata_;
}
::com::htsc::mdc::insight::model::ShiborData* MDCfetsBenchmark::release_shibordata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  
  ::com::htsc::mdc::insight::model::ShiborData* temp = shibordata_;
  shibordata_ = NULL;
  return temp;
}
void MDCfetsBenchmark::set_allocated_shibordata(::com::htsc::mdc::insight::model::ShiborData* shibordata) {
  delete shibordata_;
  shibordata_ = shibordata;
  if (shibordata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
}

// optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
bool MDCfetsBenchmark::has_loanprimerate() const {
  return this != internal_default_instance() && loanprimerate_ != NULL;
}
void MDCfetsBenchmark::clear_loanprimerate() {
  if (GetArenaNoVirtual() == NULL && loanprimerate_ != NULL) delete loanprimerate_;
  loanprimerate_ = NULL;
}
const ::com::htsc::mdc::insight::model::LoanPrimeRate& MDCfetsBenchmark::loanprimerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  return loanprimerate_ != NULL ? *loanprimerate_
                         : *::com::htsc::mdc::insight::model::LoanPrimeRate::internal_default_instance();
}
::com::htsc::mdc::insight::model::LoanPrimeRate* MDCfetsBenchmark::mutable_loanprimerate() {
  
  if (loanprimerate_ == NULL) {
    loanprimerate_ = new ::com::htsc::mdc::insight::model::LoanPrimeRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  return loanprimerate_;
}
::com::htsc::mdc::insight::model::LoanPrimeRate* MDCfetsBenchmark::release_loanprimerate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  
  ::com::htsc::mdc::insight::model::LoanPrimeRate* temp = loanprimerate_;
  loanprimerate_ = NULL;
  return temp;
}
void MDCfetsBenchmark::set_allocated_loanprimerate(::com::htsc::mdc::insight::model::LoanPrimeRate* loanprimerate) {
  delete loanprimerate_;
  loanprimerate_ = loanprimerate;
  if (loanprimerate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
}

// optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
bool MDCfetsBenchmark::has_fixreporate() const {
  return this != internal_default_instance() && fixreporate_ != NULL;
}
void MDCfetsBenchmark::clear_fixreporate() {
  if (GetArenaNoVirtual() == NULL && fixreporate_ != NULL) delete fixreporate_;
  fixreporate_ = NULL;
}
const ::com::htsc::mdc::insight::model::FixRepoRate& MDCfetsBenchmark::fixreporate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  return fixreporate_ != NULL ? *fixreporate_
                         : *::com::htsc::mdc::insight::model::FixRepoRate::internal_default_instance();
}
::com::htsc::mdc::insight::model::FixRepoRate* MDCfetsBenchmark::mutable_fixreporate() {
  
  if (fixreporate_ == NULL) {
    fixreporate_ = new ::com::htsc::mdc::insight::model::FixRepoRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  return fixreporate_;
}
::com::htsc::mdc::insight::model::FixRepoRate* MDCfetsBenchmark::release_fixreporate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  
  ::com::htsc::mdc::insight::model::FixRepoRate* temp = fixreporate_;
  fixreporate_ = NULL;
  return temp;
}
void MDCfetsBenchmark::set_allocated_fixreporate(::com::htsc::mdc::insight::model::FixRepoRate* fixreporate) {
  delete fixreporate_;
  fixreporate_ = fixreporate;
  if (fixreporate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
}

// repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
int MDCfetsBenchmark::rateswapcurves_size() const {
  return rateswapcurves_.size();
}
void MDCfetsBenchmark::clear_rateswapcurves() {
  rateswapcurves_.Clear();
}
const ::com::htsc::mdc::insight::model::RateSwapCurve& MDCfetsBenchmark::rateswapcurves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Get(index);
}
::com::htsc::mdc::insight::model::RateSwapCurve* MDCfetsBenchmark::mutable_rateswapcurves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Mutable(index);
}
::com::htsc::mdc::insight::model::RateSwapCurve* MDCfetsBenchmark::add_rateswapcurves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >*
MDCfetsBenchmark::mutable_rateswapcurves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return &rateswapcurves_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >&
MDCfetsBenchmark::rateswapcurves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_;
}

// optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
bool MDCfetsBenchmark::has_bondvaluation() const {
  return this != internal_default_instance() && bondvaluation_ != NULL;
}
void MDCfetsBenchmark::clear_bondvaluation() {
  if (GetArenaNoVirtual() == NULL && bondvaluation_ != NULL) delete bondvaluation_;
  bondvaluation_ = NULL;
}
const ::com::htsc::mdc::insight::model::BondValuation& MDCfetsBenchmark::bondvaluation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  return bondvaluation_ != NULL ? *bondvaluation_
                         : *::com::htsc::mdc::insight::model::BondValuation::internal_default_instance();
}
::com::htsc::mdc::insight::model::BondValuation* MDCfetsBenchmark::mutable_bondvaluation() {
  
  if (bondvaluation_ == NULL) {
    bondvaluation_ = new ::com::htsc::mdc::insight::model::BondValuation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  return bondvaluation_;
}
::com::htsc::mdc::insight::model::BondValuation* MDCfetsBenchmark::release_bondvaluation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  
  ::com::htsc::mdc::insight::model::BondValuation* temp = bondvaluation_;
  bondvaluation_ = NULL;
  return temp;
}
void MDCfetsBenchmark::set_allocated_bondvaluation(::com::htsc::mdc::insight::model::BondValuation* bondvaluation) {
  delete bondvaluation_;
  bondvaluation_ = bondvaluation;
  if (bondvaluation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
}

// optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
bool MDCfetsBenchmark::has_bondindex() const {
  return this != internal_default_instance() && bondindex_ != NULL;
}
void MDCfetsBenchmark::clear_bondindex() {
  if (GetArenaNoVirtual() == NULL && bondindex_ != NULL) delete bondindex_;
  bondindex_ = NULL;
}
const ::com::htsc::mdc::insight::model::BondIndex& MDCfetsBenchmark::bondindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  return bondindex_ != NULL ? *bondindex_
                         : *::com::htsc::mdc::insight::model::BondIndex::internal_default_instance();
}
::com::htsc::mdc::insight::model::BondIndex* MDCfetsBenchmark::mutable_bondindex() {
  
  if (bondindex_ == NULL) {
    bondindex_ = new ::com::htsc::mdc::insight::model::BondIndex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  return bondindex_;
}
::com::htsc::mdc::insight::model::BondIndex* MDCfetsBenchmark::release_bondindex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  
  ::com::htsc::mdc::insight::model::BondIndex* temp = bondindex_;
  bondindex_ = NULL;
  return temp;
}
void MDCfetsBenchmark::set_allocated_bondindex(::com::htsc::mdc::insight::model::BondIndex* bondindex) {
  delete bondindex_;
  bondindex_ = bondindex;
  if (bondindex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
}

inline const MDCfetsBenchmark* MDCfetsBenchmark::internal_default_instance() {
  return &MDCfetsBenchmark_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int YieldCurve_Curve::kCurveTypeFieldNumber;
const int YieldCurve_Curve::kDataSourceFieldNumber;
const int YieldCurve_Curve::kYieldPxFieldNumber;
const int YieldCurve_Curve::kSpreadFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

YieldCurve_Curve::YieldCurve_Curve()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.YieldCurve.Curve)
}

void YieldCurve_Curve::InitAsDefaultInstance() {
}

YieldCurve_Curve::YieldCurve_Curve(const YieldCurve_Curve& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.YieldCurve.Curve)
}

void YieldCurve_Curve::SharedCtor() {
  curvetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datasource_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&yieldpx_, 0, reinterpret_cast<char*>(&spread_) -
    reinterpret_cast<char*>(&yieldpx_) + sizeof(spread_));
  _cached_size_ = 0;
}

YieldCurve_Curve::~YieldCurve_Curve() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.YieldCurve.Curve)
  SharedDtor();
}

void YieldCurve_Curve::SharedDtor() {
  curvetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datasource_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void YieldCurve_Curve::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* YieldCurve_Curve::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return YieldCurve_Curve_descriptor_;
}

const YieldCurve_Curve& YieldCurve_Curve::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<YieldCurve_Curve> YieldCurve_Curve_default_instance_;

YieldCurve_Curve* YieldCurve_Curve::New(::google::protobuf::Arena* arena) const {
  YieldCurve_Curve* n = new YieldCurve_Curve;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void YieldCurve_Curve::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(YieldCurve_Curve, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<YieldCurve_Curve*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(yieldpx_, spread_);
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datasource_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool YieldCurve_Curve::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string CurveType = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_curvetype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->curvetype().data(), this->curvetype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_DataSource;
        break;
      }

      // optional string DataSource = 2;
      case 2: {
        if (tag == 18) {
         parse_DataSource:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_datasource()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->datasource().data(), this->datasource().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_YieldPx;
        break;
      }

      // optional double YieldPx = 3;
      case 3: {
        if (tag == 25) {
         parse_YieldPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_Spread;
        break;
      }

      // optional double Spread = 4;
      case 4: {
        if (tag == 33) {
         parse_Spread:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spread_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.YieldCurve.Curve)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.YieldCurve.Curve)
  return false;
#undef DO_
}

void YieldCurve_Curve::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvetype().data(), this->curvetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->curvetype(), output);
  }

  // optional string DataSource = 2;
  if (this->datasource().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datasource().data(), this->datasource().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->datasource(), output);
  }

  // optional double YieldPx = 3;
  if (this->yieldpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->yieldpx(), output);
  }

  // optional double Spread = 4;
  if (this->spread() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->spread(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.YieldCurve.Curve)
}

::google::protobuf::uint8* YieldCurve_Curve::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvetype().data(), this->curvetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->curvetype(), target);
  }

  // optional string DataSource = 2;
  if (this->datasource().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datasource().data(), this->datasource().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->datasource(), target);
  }

  // optional double YieldPx = 3;
  if (this->yieldpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->yieldpx(), target);
  }

  // optional double Spread = 4;
  if (this->spread() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->spread(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.YieldCurve.Curve)
  return target;
}

size_t YieldCurve_Curve::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  size_t total_size = 0;

  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->curvetype());
  }

  // optional string DataSource = 2;
  if (this->datasource().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->datasource());
  }

  // optional double YieldPx = 3;
  if (this->yieldpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double Spread = 4;
  if (this->spread() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void YieldCurve_Curve::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const YieldCurve_Curve* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const YieldCurve_Curve>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.YieldCurve.Curve)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.YieldCurve.Curve)
    UnsafeMergeFrom(*source);
  }
}

void YieldCurve_Curve::MergeFrom(const YieldCurve_Curve& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void YieldCurve_Curve::UnsafeMergeFrom(const YieldCurve_Curve& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.curvetype().size() > 0) {

    curvetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.curvetype_);
  }
  if (from.datasource().size() > 0) {

    datasource_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.datasource_);
  }
  if (from.yieldpx() != 0) {
    set_yieldpx(from.yieldpx());
  }
  if (from.spread() != 0) {
    set_spread(from.spread());
  }
}

void YieldCurve_Curve::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void YieldCurve_Curve::CopyFrom(const YieldCurve_Curve& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.YieldCurve.Curve)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool YieldCurve_Curve::IsInitialized() const {

  return true;
}

void YieldCurve_Curve::Swap(YieldCurve_Curve* other) {
  if (other == this) return;
  InternalSwap(other);
}
void YieldCurve_Curve::InternalSwap(YieldCurve_Curve* other) {
  curvetype_.Swap(&other->curvetype_);
  datasource_.Swap(&other->datasource_);
  std::swap(yieldpx_, other->yieldpx_);
  std::swap(spread_, other->spread_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata YieldCurve_Curve::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = YieldCurve_Curve_descriptor_;
  metadata.reflection = YieldCurve_Curve_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int YieldCurve::kYieldTermFieldNumber;
const int YieldCurve::kYieldTermTypeFieldNumber;
const int YieldCurve::kSettlDateFieldNumber;
const int YieldCurve::kSecTermYearlyFieldNumber;
const int YieldCurve::kCurvesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

YieldCurve::YieldCurve()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.YieldCurve)
}

void YieldCurve::InitAsDefaultInstance() {
}

YieldCurve::YieldCurve(const YieldCurve& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.YieldCurve)
}

void YieldCurve::SharedCtor() {
  yieldterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settldate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sectermyearly_ = 0;
  _cached_size_ = 0;
}

YieldCurve::~YieldCurve() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.YieldCurve)
  SharedDtor();
}

void YieldCurve::SharedDtor() {
  yieldterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settldate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void YieldCurve::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* YieldCurve::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return YieldCurve_descriptor_;
}

const YieldCurve& YieldCurve::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<YieldCurve> YieldCurve_default_instance_;

YieldCurve* YieldCurve::New(::google::protobuf::Arena* arena) const {
  YieldCurve* n = new YieldCurve;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void YieldCurve::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.YieldCurve)
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sectermyearly_ = 0;
  curves_.Clear();
}

bool YieldCurve::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.YieldCurve)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string YieldTerm = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_yieldterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->yieldterm().data(), this->yieldterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.YieldCurve.YieldTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_YieldTermType;
        break;
      }

      // optional string YieldTermType = 2;
      case 2: {
        if (tag == 18) {
         parse_YieldTermType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_yieldtermtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->yieldtermtype().data(), this->yieldtermtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.YieldCurve.YieldTermType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_SettlDate;
        break;
      }

      // optional string SettlDate = 3;
      case 3: {
        if (tag == 26) {
         parse_SettlDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settldate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settldate().data(), this->settldate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.YieldCurve.SettlDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_SecTermYearly;
        break;
      }

      // optional double SecTermYearly = 4;
      case 4: {
        if (tag == 33) {
         parse_SecTermYearly:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sectermyearly_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_Curves;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
      case 100: {
        if (tag == 802) {
         parse_Curves:
          DO_(input->IncrementRecursionDepth());
         parse_loop_Curves:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_curves()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_loop_Curves;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.YieldCurve)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.YieldCurve)
  return false;
#undef DO_
}

void YieldCurve::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.YieldCurve)
  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldterm().data(), this->yieldterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.YieldTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->yieldterm(), output);
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldtermtype().data(), this->yieldtermtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.YieldTermType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->yieldtermtype(), output);
  }

  // optional string SettlDate = 3;
  if (this->settldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settldate().data(), this->settldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.SettlDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->settldate(), output);
  }

  // optional double SecTermYearly = 4;
  if (this->sectermyearly() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->sectermyearly(), output);
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
  for (unsigned int i = 0, n = this->curves_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      100, this->curves(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.YieldCurve)
}

::google::protobuf::uint8* YieldCurve::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.YieldCurve)
  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldterm().data(), this->yieldterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.YieldTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->yieldterm(), target);
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldtermtype().data(), this->yieldtermtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.YieldTermType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->yieldtermtype(), target);
  }

  // optional string SettlDate = 3;
  if (this->settldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settldate().data(), this->settldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.YieldCurve.SettlDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->settldate(), target);
  }

  // optional double SecTermYearly = 4;
  if (this->sectermyearly() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->sectermyearly(), target);
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
  for (unsigned int i = 0, n = this->curves_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        100, this->curves(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.YieldCurve)
  return target;
}

size_t YieldCurve::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.YieldCurve)
  size_t total_size = 0;

  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->yieldterm());
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->yieldtermtype());
  }

  // optional string SettlDate = 3;
  if (this->settldate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settldate());
  }

  // optional double SecTermYearly = 4;
  if (this->sectermyearly() != 0) {
    total_size += 1 + 8;
  }

  // repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
  {
    unsigned int count = this->curves_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->curves(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void YieldCurve::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.YieldCurve)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const YieldCurve* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const YieldCurve>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.YieldCurve)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.YieldCurve)
    UnsafeMergeFrom(*source);
  }
}

void YieldCurve::MergeFrom(const YieldCurve& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.YieldCurve)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void YieldCurve::UnsafeMergeFrom(const YieldCurve& from) {
  GOOGLE_DCHECK(&from != this);
  curves_.MergeFrom(from.curves_);
  if (from.yieldterm().size() > 0) {

    yieldterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.yieldterm_);
  }
  if (from.yieldtermtype().size() > 0) {

    yieldtermtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.yieldtermtype_);
  }
  if (from.settldate().size() > 0) {

    settldate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settldate_);
  }
  if (from.sectermyearly() != 0) {
    set_sectermyearly(from.sectermyearly());
  }
}

void YieldCurve::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.YieldCurve)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void YieldCurve::CopyFrom(const YieldCurve& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.YieldCurve)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool YieldCurve::IsInitialized() const {

  return true;
}

void YieldCurve::Swap(YieldCurve* other) {
  if (other == this) return;
  InternalSwap(other);
}
void YieldCurve::InternalSwap(YieldCurve* other) {
  yieldterm_.Swap(&other->yieldterm_);
  yieldtermtype_.Swap(&other->yieldtermtype_);
  settldate_.Swap(&other->settldate_);
  std::swap(sectermyearly_, other->sectermyearly_);
  curves_.UnsafeArenaSwap(&other->curves_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata YieldCurve::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = YieldCurve_descriptor_;
  metadata.reflection = YieldCurve_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// YieldCurve_Curve

// optional string CurveType = 1;
void YieldCurve_Curve::clear_curvetype() {
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& YieldCurve_Curve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  return curvetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve_Curve::set_curvetype(const ::std::string& value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
void YieldCurve_Curve::set_curvetype(const char* value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
void YieldCurve_Curve::set_curvetype(const char* value, size_t size) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
::std::string* YieldCurve_Curve::mutable_curvetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  return curvetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* YieldCurve_Curve::release_curvetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  
  return curvetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve_Curve::set_allocated_curvetype(::std::string* curvetype) {
  if (curvetype != NULL) {
    
  } else {
    
  }
  curvetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}

// optional string DataSource = 2;
void YieldCurve_Curve::clear_datasource() {
  datasource_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& YieldCurve_Curve::datasource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  return datasource_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve_Curve::set_datasource(const ::std::string& value) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
void YieldCurve_Curve::set_datasource(const char* value) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
void YieldCurve_Curve::set_datasource(const char* value, size_t size) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
::std::string* YieldCurve_Curve::mutable_datasource() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  return datasource_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* YieldCurve_Curve::release_datasource() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  
  return datasource_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve_Curve::set_allocated_datasource(::std::string* datasource) {
  if (datasource != NULL) {
    
  } else {
    
  }
  datasource_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datasource);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}

// optional double YieldPx = 3;
void YieldCurve_Curve::clear_yieldpx() {
  yieldpx_ = 0;
}
double YieldCurve_Curve::yieldpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.YieldPx)
  return yieldpx_;
}
void YieldCurve_Curve::set_yieldpx(double value) {
  
  yieldpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.YieldPx)
}

// optional double Spread = 4;
void YieldCurve_Curve::clear_spread() {
  spread_ = 0;
}
double YieldCurve_Curve::spread() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.Spread)
  return spread_;
}
void YieldCurve_Curve::set_spread(double value) {
  
  spread_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.Spread)
}

inline const YieldCurve_Curve* YieldCurve_Curve::internal_default_instance() {
  return &YieldCurve_Curve_default_instance_.get();
}
// -------------------------------------------------------------------

// YieldCurve

// optional string YieldTerm = 1;
void YieldCurve::clear_yieldterm() {
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& YieldCurve::yieldterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  return yieldterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_yieldterm(const ::std::string& value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
void YieldCurve::set_yieldterm(const char* value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
void YieldCurve::set_yieldterm(const char* value, size_t size) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
::std::string* YieldCurve::mutable_yieldterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  return yieldterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* YieldCurve::release_yieldterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  
  return yieldterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_allocated_yieldterm(::std::string* yieldterm) {
  if (yieldterm != NULL) {
    
  } else {
    
  }
  yieldterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}

// optional string YieldTermType = 2;
void YieldCurve::clear_yieldtermtype() {
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& YieldCurve::yieldtermtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  return yieldtermtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_yieldtermtype(const ::std::string& value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
void YieldCurve::set_yieldtermtype(const char* value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
void YieldCurve::set_yieldtermtype(const char* value, size_t size) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
::std::string* YieldCurve::mutable_yieldtermtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  return yieldtermtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* YieldCurve::release_yieldtermtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  
  return yieldtermtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_allocated_yieldtermtype(::std::string* yieldtermtype) {
  if (yieldtermtype != NULL) {
    
  } else {
    
  }
  yieldtermtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldtermtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}

// optional string SettlDate = 3;
void YieldCurve::clear_settldate() {
  settldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& YieldCurve::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  return settldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_settldate(const ::std::string& value) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
void YieldCurve::set_settldate(const char* value) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
void YieldCurve::set_settldate(const char* value, size_t size) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
::std::string* YieldCurve::mutable_settldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  return settldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* YieldCurve::release_settldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  
  return settldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void YieldCurve::set_allocated_settldate(::std::string* settldate) {
  if (settldate != NULL) {
    
  } else {
    
  }
  settldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}

// optional double SecTermYearly = 4;
void YieldCurve::clear_sectermyearly() {
  sectermyearly_ = 0;
}
double YieldCurve::sectermyearly() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.SecTermYearly)
  return sectermyearly_;
}
void YieldCurve::set_sectermyearly(double value) {
  
  sectermyearly_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.SecTermYearly)
}

// repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
int YieldCurve::curves_size() const {
  return curves_.size();
}
void YieldCurve::clear_curves() {
  curves_.Clear();
}
const ::com::htsc::mdc::insight::model::YieldCurve_Curve& YieldCurve::curves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Get(index);
}
::com::htsc::mdc::insight::model::YieldCurve_Curve* YieldCurve::mutable_curves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Mutable(index);
}
::com::htsc::mdc::insight::model::YieldCurve_Curve* YieldCurve::add_curves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >*
YieldCurve::mutable_curves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return &curves_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >&
YieldCurve::curves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_;
}

inline const YieldCurve* YieldCurve::internal_default_instance() {
  return &YieldCurve_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ShiborData::kEffectiveDateFieldNumber;
const int ShiborData::kShiborFieldNumber;
const int ShiborData::kShiborChangeFieldNumber;
const int ShiborData::kShiborQuoteFieldNumber;
const int ShiborData::kShiborQuoteTimeFieldNumber;
const int ShiborData::kQuotePartyIDFieldNumber;
const int ShiborData::kQuotePartyShortNameFieldNumber;
const int ShiborData::kMovingAverageFieldNumber;
const int ShiborData::kMovingAverageTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ShiborData::ShiborData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ShiborData)
}

void ShiborData::InitAsDefaultInstance() {
}

ShiborData::ShiborData(const ShiborData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ShiborData)
}

void ShiborData::SharedCtor() {
  effectivedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shiborquotetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyshortname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  movingaveragetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&shibor_, 0, reinterpret_cast<char*>(&movingaverage_) -
    reinterpret_cast<char*>(&shibor_) + sizeof(movingaverage_));
  _cached_size_ = 0;
}

ShiborData::~ShiborData() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ShiborData)
  SharedDtor();
}

void ShiborData::SharedDtor() {
  effectivedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shiborquotetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyshortname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  movingaveragetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ShiborData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ShiborData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ShiborData_descriptor_;
}

const ShiborData& ShiborData::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ShiborData> ShiborData_default_instance_;

ShiborData* ShiborData::New(::google::protobuf::Arena* arena) const {
  ShiborData* n = new ShiborData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ShiborData::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ShiborData)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ShiborData, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ShiborData*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(shibor_, movingaverage_);
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  shiborquotetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quotepartyshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  movingaveragetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ShiborData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ShiborData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string EffectiveDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_effectivedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->effectivedate().data(), this->effectivedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ShiborData.EffectiveDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_Shibor;
        break;
      }

      // optional double Shibor = 2;
      case 2: {
        if (tag == 17) {
         parse_Shibor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &shibor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_ShiborChange;
        break;
      }

      // optional double ShiborChange = 3;
      case 3: {
        if (tag == 25) {
         parse_ShiborChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &shiborchange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_ShiborQuote;
        break;
      }

      // optional double ShiborQuote = 4;
      case 4: {
        if (tag == 33) {
         parse_ShiborQuote:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &shiborquote_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_ShiborQuoteTime;
        break;
      }

      // optional string ShiborQuoteTime = 5;
      case 5: {
        if (tag == 42) {
         parse_ShiborQuoteTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_shiborquotetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->shiborquotetime().data(), this->shiborquotetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_QuotePartyID;
        break;
      }

      // optional string QuotePartyID = 6;
      case 6: {
        if (tag == 50) {
         parse_QuotePartyID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quotepartyid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quotepartyid().data(), this->quotepartyid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ShiborData.QuotePartyID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_QuotePartyShortName;
        break;
      }

      // optional string QuotePartyShortName = 7;
      case 7: {
        if (tag == 58) {
         parse_QuotePartyShortName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quotepartyshortname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quotepartyshortname().data(), this->quotepartyshortname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_MovingAverage;
        break;
      }

      // optional double MovingAverage = 8;
      case 8: {
        if (tag == 65) {
         parse_MovingAverage:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &movingaverage_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_MovingAverageType;
        break;
      }

      // optional string MovingAverageType = 9;
      case 9: {
        if (tag == 74) {
         parse_MovingAverageType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_movingaveragetype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->movingaveragetype().data(), this->movingaveragetype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ShiborData.MovingAverageType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ShiborData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ShiborData)
  return false;
#undef DO_
}

void ShiborData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ShiborData)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.EffectiveDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->effectivedate(), output);
  }

  // optional double Shibor = 2;
  if (this->shibor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->shibor(), output);
  }

  // optional double ShiborChange = 3;
  if (this->shiborchange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->shiborchange(), output);
  }

  // optional double ShiborQuote = 4;
  if (this->shiborquote() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->shiborquote(), output);
  }

  // optional string ShiborQuoteTime = 5;
  if (this->shiborquotetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->shiborquotetime().data(), this->shiborquotetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->shiborquotetime(), output);
  }

  // optional string QuotePartyID = 6;
  if (this->quotepartyid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotepartyid().data(), this->quotepartyid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.QuotePartyID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->quotepartyid(), output);
  }

  // optional string QuotePartyShortName = 7;
  if (this->quotepartyshortname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotepartyshortname().data(), this->quotepartyshortname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->quotepartyshortname(), output);
  }

  // optional double MovingAverage = 8;
  if (this->movingaverage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->movingaverage(), output);
  }

  // optional string MovingAverageType = 9;
  if (this->movingaveragetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->movingaveragetype().data(), this->movingaveragetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.MovingAverageType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->movingaveragetype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ShiborData)
}

::google::protobuf::uint8* ShiborData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ShiborData)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.EffectiveDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->effectivedate(), target);
  }

  // optional double Shibor = 2;
  if (this->shibor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->shibor(), target);
  }

  // optional double ShiborChange = 3;
  if (this->shiborchange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->shiborchange(), target);
  }

  // optional double ShiborQuote = 4;
  if (this->shiborquote() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->shiborquote(), target);
  }

  // optional string ShiborQuoteTime = 5;
  if (this->shiborquotetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->shiborquotetime().data(), this->shiborquotetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->shiborquotetime(), target);
  }

  // optional string QuotePartyID = 6;
  if (this->quotepartyid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotepartyid().data(), this->quotepartyid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.QuotePartyID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->quotepartyid(), target);
  }

  // optional string QuotePartyShortName = 7;
  if (this->quotepartyshortname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotepartyshortname().data(), this->quotepartyshortname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->quotepartyshortname(), target);
  }

  // optional double MovingAverage = 8;
  if (this->movingaverage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->movingaverage(), target);
  }

  // optional string MovingAverageType = 9;
  if (this->movingaveragetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->movingaveragetype().data(), this->movingaveragetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ShiborData.MovingAverageType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->movingaveragetype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ShiborData)
  return target;
}

size_t ShiborData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ShiborData)
  size_t total_size = 0;

  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->effectivedate());
  }

  // optional double Shibor = 2;
  if (this->shibor() != 0) {
    total_size += 1 + 8;
  }

  // optional double ShiborChange = 3;
  if (this->shiborchange() != 0) {
    total_size += 1 + 8;
  }

  // optional double ShiborQuote = 4;
  if (this->shiborquote() != 0) {
    total_size += 1 + 8;
  }

  // optional string ShiborQuoteTime = 5;
  if (this->shiborquotetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->shiborquotetime());
  }

  // optional string QuotePartyID = 6;
  if (this->quotepartyid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quotepartyid());
  }

  // optional string QuotePartyShortName = 7;
  if (this->quotepartyshortname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quotepartyshortname());
  }

  // optional double MovingAverage = 8;
  if (this->movingaverage() != 0) {
    total_size += 1 + 8;
  }

  // optional string MovingAverageType = 9;
  if (this->movingaveragetype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->movingaveragetype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ShiborData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ShiborData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ShiborData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ShiborData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ShiborData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ShiborData)
    UnsafeMergeFrom(*source);
  }
}

void ShiborData::MergeFrom(const ShiborData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ShiborData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ShiborData::UnsafeMergeFrom(const ShiborData& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.effectivedate().size() > 0) {

    effectivedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.effectivedate_);
  }
  if (from.shibor() != 0) {
    set_shibor(from.shibor());
  }
  if (from.shiborchange() != 0) {
    set_shiborchange(from.shiborchange());
  }
  if (from.shiborquote() != 0) {
    set_shiborquote(from.shiborquote());
  }
  if (from.shiborquotetime().size() > 0) {

    shiborquotetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.shiborquotetime_);
  }
  if (from.quotepartyid().size() > 0) {

    quotepartyid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quotepartyid_);
  }
  if (from.quotepartyshortname().size() > 0) {

    quotepartyshortname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quotepartyshortname_);
  }
  if (from.movingaverage() != 0) {
    set_movingaverage(from.movingaverage());
  }
  if (from.movingaveragetype().size() > 0) {

    movingaveragetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.movingaveragetype_);
  }
}

void ShiborData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ShiborData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ShiborData::CopyFrom(const ShiborData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ShiborData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ShiborData::IsInitialized() const {

  return true;
}

void ShiborData::Swap(ShiborData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ShiborData::InternalSwap(ShiborData* other) {
  effectivedate_.Swap(&other->effectivedate_);
  std::swap(shibor_, other->shibor_);
  std::swap(shiborchange_, other->shiborchange_);
  std::swap(shiborquote_, other->shiborquote_);
  shiborquotetime_.Swap(&other->shiborquotetime_);
  quotepartyid_.Swap(&other->quotepartyid_);
  quotepartyshortname_.Swap(&other->quotepartyshortname_);
  std::swap(movingaverage_, other->movingaverage_);
  movingaveragetype_.Swap(&other->movingaveragetype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ShiborData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ShiborData_descriptor_;
  metadata.reflection = ShiborData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ShiborData

// optional string EffectiveDate = 1;
void ShiborData::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ShiborData::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
void ShiborData::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
void ShiborData::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
::std::string* ShiborData::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ShiborData::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}

// optional double Shibor = 2;
void ShiborData::clear_shibor() {
  shibor_ = 0;
}
double ShiborData::shibor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.Shibor)
  return shibor_;
}
void ShiborData::set_shibor(double value) {
  
  shibor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.Shibor)
}

// optional double ShiborChange = 3;
void ShiborData::clear_shiborchange() {
  shiborchange_ = 0;
}
double ShiborData::shiborchange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborChange)
  return shiborchange_;
}
void ShiborData::set_shiborchange(double value) {
  
  shiborchange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborChange)
}

// optional double ShiborQuote = 4;
void ShiborData::clear_shiborquote() {
  shiborquote_ = 0;
}
double ShiborData::shiborquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborQuote)
  return shiborquote_;
}
void ShiborData::set_shiborquote(double value) {
  
  shiborquote_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborQuote)
}

// optional string ShiborQuoteTime = 5;
void ShiborData::clear_shiborquotetime() {
  shiborquotetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ShiborData::shiborquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  return shiborquotetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_shiborquotetime(const ::std::string& value) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
void ShiborData::set_shiborquotetime(const char* value) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
void ShiborData::set_shiborquotetime(const char* value, size_t size) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
::std::string* ShiborData::mutable_shiborquotetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  return shiborquotetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ShiborData::release_shiborquotetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  
  return shiborquotetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_allocated_shiborquotetime(::std::string* shiborquotetime) {
  if (shiborquotetime != NULL) {
    
  } else {
    
  }
  shiborquotetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), shiborquotetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}

// optional string QuotePartyID = 6;
void ShiborData::clear_quotepartyid() {
  quotepartyid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ShiborData::quotepartyid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  return quotepartyid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_quotepartyid(const ::std::string& value) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
void ShiborData::set_quotepartyid(const char* value) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
void ShiborData::set_quotepartyid(const char* value, size_t size) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
::std::string* ShiborData::mutable_quotepartyid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  return quotepartyid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ShiborData::release_quotepartyid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  
  return quotepartyid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_allocated_quotepartyid(::std::string* quotepartyid) {
  if (quotepartyid != NULL) {
    
  } else {
    
  }
  quotepartyid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotepartyid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}

// optional string QuotePartyShortName = 7;
void ShiborData::clear_quotepartyshortname() {
  quotepartyshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ShiborData::quotepartyshortname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  return quotepartyshortname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_quotepartyshortname(const ::std::string& value) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
void ShiborData::set_quotepartyshortname(const char* value) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
void ShiborData::set_quotepartyshortname(const char* value, size_t size) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
::std::string* ShiborData::mutable_quotepartyshortname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  return quotepartyshortname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ShiborData::release_quotepartyshortname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  
  return quotepartyshortname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_allocated_quotepartyshortname(::std::string* quotepartyshortname) {
  if (quotepartyshortname != NULL) {
    
  } else {
    
  }
  quotepartyshortname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotepartyshortname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}

// optional double MovingAverage = 8;
void ShiborData::clear_movingaverage() {
  movingaverage_ = 0;
}
double ShiborData::movingaverage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.MovingAverage)
  return movingaverage_;
}
void ShiborData::set_movingaverage(double value) {
  
  movingaverage_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.MovingAverage)
}

// optional string MovingAverageType = 9;
void ShiborData::clear_movingaveragetype() {
  movingaveragetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ShiborData::movingaveragetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  return movingaveragetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_movingaveragetype(const ::std::string& value) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
void ShiborData::set_movingaveragetype(const char* value) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
void ShiborData::set_movingaveragetype(const char* value, size_t size) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
::std::string* ShiborData::mutable_movingaveragetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  return movingaveragetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ShiborData::release_movingaveragetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  
  return movingaveragetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ShiborData::set_allocated_movingaveragetype(::std::string* movingaveragetype) {
  if (movingaveragetype != NULL) {
    
  } else {
    
  }
  movingaveragetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), movingaveragetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}

inline const ShiborData* ShiborData::internal_default_instance() {
  return &ShiborData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoanPrimeRate::kEffectiveDateFieldNumber;
const int LoanPrimeRate::kLprFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoanPrimeRate::LoanPrimeRate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.LoanPrimeRate)
}

void LoanPrimeRate::InitAsDefaultInstance() {
}

LoanPrimeRate::LoanPrimeRate(const LoanPrimeRate& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.LoanPrimeRate)
}

void LoanPrimeRate::SharedCtor() {
  effectivedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  lpr_ = 0;
  _cached_size_ = 0;
}

LoanPrimeRate::~LoanPrimeRate() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.LoanPrimeRate)
  SharedDtor();
}

void LoanPrimeRate::SharedDtor() {
  effectivedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void LoanPrimeRate::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoanPrimeRate::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return LoanPrimeRate_descriptor_;
}

const LoanPrimeRate& LoanPrimeRate::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<LoanPrimeRate> LoanPrimeRate_default_instance_;

LoanPrimeRate* LoanPrimeRate::New(::google::protobuf::Arena* arena) const {
  LoanPrimeRate* n = new LoanPrimeRate;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoanPrimeRate::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  lpr_ = 0;
}

bool LoanPrimeRate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string EffectiveDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_effectivedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->effectivedate().data(), this->effectivedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_Lpr;
        break;
      }

      // optional double Lpr = 2;
      case 2: {
        if (tag == 17) {
         parse_Lpr:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lpr_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.LoanPrimeRate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.LoanPrimeRate)
  return false;
#undef DO_
}

void LoanPrimeRate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->effectivedate(), output);
  }

  // optional double Lpr = 2;
  if (this->lpr() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->lpr(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.LoanPrimeRate)
}

::google::protobuf::uint8* LoanPrimeRate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->effectivedate(), target);
  }

  // optional double Lpr = 2;
  if (this->lpr() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->lpr(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.LoanPrimeRate)
  return target;
}

size_t LoanPrimeRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  size_t total_size = 0;

  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->effectivedate());
  }

  // optional double Lpr = 2;
  if (this->lpr() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoanPrimeRate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const LoanPrimeRate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoanPrimeRate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.LoanPrimeRate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.LoanPrimeRate)
    UnsafeMergeFrom(*source);
  }
}

void LoanPrimeRate::MergeFrom(const LoanPrimeRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void LoanPrimeRate::UnsafeMergeFrom(const LoanPrimeRate& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.effectivedate().size() > 0) {

    effectivedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.effectivedate_);
  }
  if (from.lpr() != 0) {
    set_lpr(from.lpr());
  }
}

void LoanPrimeRate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoanPrimeRate::CopyFrom(const LoanPrimeRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.LoanPrimeRate)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool LoanPrimeRate::IsInitialized() const {

  return true;
}

void LoanPrimeRate::Swap(LoanPrimeRate* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoanPrimeRate::InternalSwap(LoanPrimeRate* other) {
  effectivedate_.Swap(&other->effectivedate_);
  std::swap(lpr_, other->lpr_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoanPrimeRate::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = LoanPrimeRate_descriptor_;
  metadata.reflection = LoanPrimeRate_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// LoanPrimeRate

// optional string EffectiveDate = 1;
void LoanPrimeRate::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& LoanPrimeRate::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void LoanPrimeRate::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
void LoanPrimeRate::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
void LoanPrimeRate::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
::std::string* LoanPrimeRate::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* LoanPrimeRate::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void LoanPrimeRate::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}

// optional double Lpr = 2;
void LoanPrimeRate::clear_lpr() {
  lpr_ = 0;
}
double LoanPrimeRate::lpr() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.LoanPrimeRate.Lpr)
  return lpr_;
}
void LoanPrimeRate::set_lpr(double value) {
  
  lpr_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.LoanPrimeRate.Lpr)
}

inline const LoanPrimeRate* LoanPrimeRate::internal_default_instance() {
  return &LoanPrimeRate_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FixRepoRate::kEffectiveDateFieldNumber;
const int FixRepoRate::kRateFieldNumber;
const int FixRepoRate::kTermUpperLimitFieldNumber;
const int FixRepoRate::kTermFloorLimitFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FixRepoRate::FixRepoRate()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.FixRepoRate)
}

void FixRepoRate::InitAsDefaultInstance() {
}

FixRepoRate::FixRepoRate(const FixRepoRate& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.FixRepoRate)
}

void FixRepoRate::SharedCtor() {
  effectivedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&rate_, 0, reinterpret_cast<char*>(&termfloorlimit_) -
    reinterpret_cast<char*>(&rate_) + sizeof(termfloorlimit_));
  _cached_size_ = 0;
}

FixRepoRate::~FixRepoRate() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.FixRepoRate)
  SharedDtor();
}

void FixRepoRate::SharedDtor() {
  effectivedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void FixRepoRate::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FixRepoRate::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return FixRepoRate_descriptor_;
}

const FixRepoRate& FixRepoRate::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<FixRepoRate> FixRepoRate_default_instance_;

FixRepoRate* FixRepoRate::New(::google::protobuf::Arena* arena) const {
  FixRepoRate* n = new FixRepoRate;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FixRepoRate::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.FixRepoRate)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(FixRepoRate, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<FixRepoRate*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(rate_, termfloorlimit_);
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool FixRepoRate::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.FixRepoRate)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string EffectiveDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_effectivedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->effectivedate().data(), this->effectivedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_Rate;
        break;
      }

      // optional double Rate = 2;
      case 2: {
        if (tag == 17) {
         parse_Rate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &rate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_TermUpperLimit;
        break;
      }

      // optional int32 TermUpperLimit = 3;
      case 3: {
        if (tag == 24) {
         parse_TermUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &termupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_TermFloorLimit;
        break;
      }

      // optional int32 TermFloorLimit = 4;
      case 4: {
        if (tag == 32) {
         parse_TermFloorLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &termfloorlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.FixRepoRate)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.FixRepoRate)
  return false;
#undef DO_
}

void FixRepoRate::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.FixRepoRate)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->effectivedate(), output);
  }

  // optional double Rate = 2;
  if (this->rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->rate(), output);
  }

  // optional int32 TermUpperLimit = 3;
  if (this->termupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->termupperlimit(), output);
  }

  // optional int32 TermFloorLimit = 4;
  if (this->termfloorlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->termfloorlimit(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.FixRepoRate)
}

::google::protobuf::uint8* FixRepoRate::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.FixRepoRate)
  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->effectivedate().data(), this->effectivedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->effectivedate(), target);
  }

  // optional double Rate = 2;
  if (this->rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->rate(), target);
  }

  // optional int32 TermUpperLimit = 3;
  if (this->termupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->termupperlimit(), target);
  }

  // optional int32 TermFloorLimit = 4;
  if (this->termfloorlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->termfloorlimit(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.FixRepoRate)
  return target;
}

size_t FixRepoRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.FixRepoRate)
  size_t total_size = 0;

  // optional string EffectiveDate = 1;
  if (this->effectivedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->effectivedate());
  }

  // optional double Rate = 2;
  if (this->rate() != 0) {
    total_size += 1 + 8;
  }

  // optional int32 TermUpperLimit = 3;
  if (this->termupperlimit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->termupperlimit());
  }

  // optional int32 TermFloorLimit = 4;
  if (this->termfloorlimit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->termfloorlimit());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FixRepoRate::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.FixRepoRate)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const FixRepoRate* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FixRepoRate>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.FixRepoRate)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.FixRepoRate)
    UnsafeMergeFrom(*source);
  }
}

void FixRepoRate::MergeFrom(const FixRepoRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.FixRepoRate)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void FixRepoRate::UnsafeMergeFrom(const FixRepoRate& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.effectivedate().size() > 0) {

    effectivedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.effectivedate_);
  }
  if (from.rate() != 0) {
    set_rate(from.rate());
  }
  if (from.termupperlimit() != 0) {
    set_termupperlimit(from.termupperlimit());
  }
  if (from.termfloorlimit() != 0) {
    set_termfloorlimit(from.termfloorlimit());
  }
}

void FixRepoRate::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.FixRepoRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FixRepoRate::CopyFrom(const FixRepoRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.FixRepoRate)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool FixRepoRate::IsInitialized() const {

  return true;
}

void FixRepoRate::Swap(FixRepoRate* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FixRepoRate::InternalSwap(FixRepoRate* other) {
  effectivedate_.Swap(&other->effectivedate_);
  std::swap(rate_, other->rate_);
  std::swap(termupperlimit_, other->termupperlimit_);
  std::swap(termfloorlimit_, other->termfloorlimit_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FixRepoRate::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = FixRepoRate_descriptor_;
  metadata.reflection = FixRepoRate_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FixRepoRate

// optional string EffectiveDate = 1;
void FixRepoRate::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& FixRepoRate::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FixRepoRate::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
void FixRepoRate::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
void FixRepoRate::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
::std::string* FixRepoRate::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* FixRepoRate::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void FixRepoRate::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}

// optional double Rate = 2;
void FixRepoRate::clear_rate() {
  rate_ = 0;
}
double FixRepoRate::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.Rate)
  return rate_;
}
void FixRepoRate::set_rate(double value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.Rate)
}

// optional int32 TermUpperLimit = 3;
void FixRepoRate::clear_termupperlimit() {
  termupperlimit_ = 0;
}
::google::protobuf::int32 FixRepoRate::termupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.TermUpperLimit)
  return termupperlimit_;
}
void FixRepoRate::set_termupperlimit(::google::protobuf::int32 value) {
  
  termupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.TermUpperLimit)
}

// optional int32 TermFloorLimit = 4;
void FixRepoRate::clear_termfloorlimit() {
  termfloorlimit_ = 0;
}
::google::protobuf::int32 FixRepoRate::termfloorlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.TermFloorLimit)
  return termfloorlimit_;
}
void FixRepoRate::set_termfloorlimit(::google::protobuf::int32 value) {
  
  termfloorlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.TermFloorLimit)
}

inline const FixRepoRate* FixRepoRate::internal_default_instance() {
  return &FixRepoRate_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RateSwapCurve_Curve::kCurveTypeFieldNumber;
const int RateSwapCurve_Curve::kYieldPxFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RateSwapCurve_Curve::RateSwapCurve_Curve()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
}

void RateSwapCurve_Curve::InitAsDefaultInstance() {
}

RateSwapCurve_Curve::RateSwapCurve_Curve(const RateSwapCurve_Curve& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
}

void RateSwapCurve_Curve::SharedCtor() {
  curvetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldpx_ = 0;
  _cached_size_ = 0;
}

RateSwapCurve_Curve::~RateSwapCurve_Curve() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  SharedDtor();
}

void RateSwapCurve_Curve::SharedDtor() {
  curvetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RateSwapCurve_Curve::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RateSwapCurve_Curve::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RateSwapCurve_Curve_descriptor_;
}

const RateSwapCurve_Curve& RateSwapCurve_Curve::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RateSwapCurve_Curve> RateSwapCurve_Curve_default_instance_;

RateSwapCurve_Curve* RateSwapCurve_Curve::New(::google::protobuf::Arena* arena) const {
  RateSwapCurve_Curve* n = new RateSwapCurve_Curve;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RateSwapCurve_Curve::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldpx_ = 0;
}

bool RateSwapCurve_Curve::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string CurveType = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_curvetype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->curvetype().data(), this->curvetype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_YieldPx;
        break;
      }

      // optional double YieldPx = 2;
      case 2: {
        if (tag == 17) {
         parse_YieldPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  return false;
#undef DO_
}

void RateSwapCurve_Curve::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvetype().data(), this->curvetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->curvetype(), output);
  }

  // optional double YieldPx = 2;
  if (this->yieldpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->yieldpx(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
}

::google::protobuf::uint8* RateSwapCurve_Curve::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvetype().data(), this->curvetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->curvetype(), target);
  }

  // optional double YieldPx = 2;
  if (this->yieldpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->yieldpx(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  return target;
}

size_t RateSwapCurve_Curve::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  size_t total_size = 0;

  // optional string CurveType = 1;
  if (this->curvetype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->curvetype());
  }

  // optional double YieldPx = 2;
  if (this->yieldpx() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RateSwapCurve_Curve::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RateSwapCurve_Curve* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RateSwapCurve_Curve>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
    UnsafeMergeFrom(*source);
  }
}

void RateSwapCurve_Curve::MergeFrom(const RateSwapCurve_Curve& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RateSwapCurve_Curve::UnsafeMergeFrom(const RateSwapCurve_Curve& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.curvetype().size() > 0) {

    curvetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.curvetype_);
  }
  if (from.yieldpx() != 0) {
    set_yieldpx(from.yieldpx());
  }
}

void RateSwapCurve_Curve::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RateSwapCurve_Curve::CopyFrom(const RateSwapCurve_Curve& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RateSwapCurve_Curve::IsInitialized() const {

  return true;
}

void RateSwapCurve_Curve::Swap(RateSwapCurve_Curve* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RateSwapCurve_Curve::InternalSwap(RateSwapCurve_Curve* other) {
  curvetype_.Swap(&other->curvetype_);
  std::swap(yieldpx_, other->yieldpx_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RateSwapCurve_Curve::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RateSwapCurve_Curve_descriptor_;
  metadata.reflection = RateSwapCurve_Curve_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RateSwapCurve::kYieldTermFieldNumber;
const int RateSwapCurve::kYieldTermTypeFieldNumber;
const int RateSwapCurve::kCalculateDateFieldNumber;
const int RateSwapCurve::kCurvesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RateSwapCurve::RateSwapCurve()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.RateSwapCurve)
}

void RateSwapCurve::InitAsDefaultInstance() {
}

RateSwapCurve::RateSwapCurve(const RateSwapCurve& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.RateSwapCurve)
}

void RateSwapCurve::SharedCtor() {
  yieldterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

RateSwapCurve::~RateSwapCurve() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.RateSwapCurve)
  SharedDtor();
}

void RateSwapCurve::SharedDtor() {
  yieldterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RateSwapCurve::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RateSwapCurve::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RateSwapCurve_descriptor_;
}

const RateSwapCurve& RateSwapCurve::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RateSwapCurve> RateSwapCurve_default_instance_;

RateSwapCurve* RateSwapCurve::New(::google::protobuf::Arena* arena) const {
  RateSwapCurve* n = new RateSwapCurve;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RateSwapCurve::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.RateSwapCurve)
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  calculatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curves_.Clear();
}

bool RateSwapCurve::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.RateSwapCurve)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string YieldTerm = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_yieldterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->yieldterm().data(), this->yieldterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_YieldTermType;
        break;
      }

      // optional string YieldTermType = 2;
      case 2: {
        if (tag == 18) {
         parse_YieldTermType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_yieldtermtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->yieldtermtype().data(), this->yieldtermtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_CalculateDate;
        break;
      }

      // optional string CalculateDate = 3;
      case 3: {
        if (tag == 26) {
         parse_CalculateDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_calculatedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->calculatedate().data(), this->calculatedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_Curves;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
      case 100: {
        if (tag == 802) {
         parse_Curves:
          DO_(input->IncrementRecursionDepth());
         parse_loop_Curves:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_curves()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_loop_Curves;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.RateSwapCurve)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.RateSwapCurve)
  return false;
#undef DO_
}

void RateSwapCurve::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.RateSwapCurve)
  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldterm().data(), this->yieldterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->yieldterm(), output);
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldtermtype().data(), this->yieldtermtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->yieldtermtype(), output);
  }

  // optional string CalculateDate = 3;
  if (this->calculatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatedate().data(), this->calculatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->calculatedate(), output);
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
  for (unsigned int i = 0, n = this->curves_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      100, this->curves(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.RateSwapCurve)
}

::google::protobuf::uint8* RateSwapCurve::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.RateSwapCurve)
  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldterm().data(), this->yieldterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->yieldterm(), target);
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->yieldtermtype().data(), this->yieldtermtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->yieldtermtype(), target);
  }

  // optional string CalculateDate = 3;
  if (this->calculatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatedate().data(), this->calculatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->calculatedate(), target);
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
  for (unsigned int i = 0, n = this->curves_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        100, this->curves(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.RateSwapCurve)
  return target;
}

size_t RateSwapCurve::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.RateSwapCurve)
  size_t total_size = 0;

  // optional string YieldTerm = 1;
  if (this->yieldterm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->yieldterm());
  }

  // optional string YieldTermType = 2;
  if (this->yieldtermtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->yieldtermtype());
  }

  // optional string CalculateDate = 3;
  if (this->calculatedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->calculatedate());
  }

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
  {
    unsigned int count = this->curves_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->curves(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RateSwapCurve::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.RateSwapCurve)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RateSwapCurve* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RateSwapCurve>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.RateSwapCurve)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.RateSwapCurve)
    UnsafeMergeFrom(*source);
  }
}

void RateSwapCurve::MergeFrom(const RateSwapCurve& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.RateSwapCurve)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RateSwapCurve::UnsafeMergeFrom(const RateSwapCurve& from) {
  GOOGLE_DCHECK(&from != this);
  curves_.MergeFrom(from.curves_);
  if (from.yieldterm().size() > 0) {

    yieldterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.yieldterm_);
  }
  if (from.yieldtermtype().size() > 0) {

    yieldtermtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.yieldtermtype_);
  }
  if (from.calculatedate().size() > 0) {

    calculatedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.calculatedate_);
  }
}

void RateSwapCurve::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.RateSwapCurve)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RateSwapCurve::CopyFrom(const RateSwapCurve& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.RateSwapCurve)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RateSwapCurve::IsInitialized() const {

  return true;
}

void RateSwapCurve::Swap(RateSwapCurve* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RateSwapCurve::InternalSwap(RateSwapCurve* other) {
  yieldterm_.Swap(&other->yieldterm_);
  yieldtermtype_.Swap(&other->yieldtermtype_);
  calculatedate_.Swap(&other->calculatedate_);
  curves_.UnsafeArenaSwap(&other->curves_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RateSwapCurve::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RateSwapCurve_descriptor_;
  metadata.reflection = RateSwapCurve_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RateSwapCurve_Curve

// optional string CurveType = 1;
void RateSwapCurve_Curve::clear_curvetype() {
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapCurve_Curve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  return curvetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve_Curve::set_curvetype(const ::std::string& value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
void RateSwapCurve_Curve::set_curvetype(const char* value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
void RateSwapCurve_Curve::set_curvetype(const char* value, size_t size) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
::std::string* RateSwapCurve_Curve::mutable_curvetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  return curvetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapCurve_Curve::release_curvetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  
  return curvetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve_Curve::set_allocated_curvetype(::std::string* curvetype) {
  if (curvetype != NULL) {
    
  } else {
    
  }
  curvetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}

// optional double YieldPx = 2;
void RateSwapCurve_Curve::clear_yieldpx() {
  yieldpx_ = 0;
}
double RateSwapCurve_Curve::yieldpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curve.YieldPx)
  return yieldpx_;
}
void RateSwapCurve_Curve::set_yieldpx(double value) {
  
  yieldpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.Curve.YieldPx)
}

inline const RateSwapCurve_Curve* RateSwapCurve_Curve::internal_default_instance() {
  return &RateSwapCurve_Curve_default_instance_.get();
}
// -------------------------------------------------------------------

// RateSwapCurve

// optional string YieldTerm = 1;
void RateSwapCurve::clear_yieldterm() {
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapCurve::yieldterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  return yieldterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_yieldterm(const ::std::string& value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
void RateSwapCurve::set_yieldterm(const char* value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
void RateSwapCurve::set_yieldterm(const char* value, size_t size) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
::std::string* RateSwapCurve::mutable_yieldterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  return yieldterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapCurve::release_yieldterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  
  return yieldterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_allocated_yieldterm(::std::string* yieldterm) {
  if (yieldterm != NULL) {
    
  } else {
    
  }
  yieldterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}

// optional string YieldTermType = 2;
void RateSwapCurve::clear_yieldtermtype() {
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapCurve::yieldtermtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  return yieldtermtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_yieldtermtype(const ::std::string& value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
void RateSwapCurve::set_yieldtermtype(const char* value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
void RateSwapCurve::set_yieldtermtype(const char* value, size_t size) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
::std::string* RateSwapCurve::mutable_yieldtermtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  return yieldtermtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapCurve::release_yieldtermtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  
  return yieldtermtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_allocated_yieldtermtype(::std::string* yieldtermtype) {
  if (yieldtermtype != NULL) {
    
  } else {
    
  }
  yieldtermtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldtermtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}

// optional string CalculateDate = 3;
void RateSwapCurve::clear_calculatedate() {
  calculatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapCurve::calculatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  return calculatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_calculatedate(const ::std::string& value) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
void RateSwapCurve::set_calculatedate(const char* value) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
void RateSwapCurve::set_calculatedate(const char* value, size_t size) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
::std::string* RateSwapCurve::mutable_calculatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  return calculatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapCurve::release_calculatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  
  return calculatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapCurve::set_allocated_calculatedate(::std::string* calculatedate) {
  if (calculatedate != NULL) {
    
  } else {
    
  }
  calculatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}

// repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
int RateSwapCurve::curves_size() const {
  return curves_.size();
}
void RateSwapCurve::clear_curves() {
  curves_.Clear();
}
const ::com::htsc::mdc::insight::model::RateSwapCurve_Curve& RateSwapCurve::curves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Get(index);
}
::com::htsc::mdc::insight::model::RateSwapCurve_Curve* RateSwapCurve::mutable_curves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Mutable(index);
}
::com::htsc::mdc::insight::model::RateSwapCurve_Curve* RateSwapCurve::add_curves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >*
RateSwapCurve::mutable_curves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return &curves_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >&
RateSwapCurve::curves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_;
}

inline const RateSwapCurve* RateSwapCurve::internal_default_instance() {
  return &RateSwapCurve_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BondValuation::kTransactDateFieldNumber;
const int BondValuation::kValuationDateTimeFieldNumber;
const int BondValuation::kCurrencyFieldNumber;
const int BondValuation::kCouponRateTypeFieldNumber;
const int BondValuation::kValuationAmtFieldNumber;
const int BondValuation::kDirtyPriceFieldNumber;
const int BondValuation::kSpreadCNYDirtyPriceFieldNumber;
const int BondValuation::kSpreadCNYPriceFieldNumber;
const int BondValuation::kValuationYieldFieldNumber;
const int BondValuation::kSpreadDurationFieldNumber;
const int BondValuation::kSpreadConvexityFieldNumber;
const int BondValuation::kDurationFieldNumber;
const int BondValuation::kModifyDurationFieldNumber;
const int BondValuation::kConvexityFieldNumber;
const int BondValuation::kSpreadPxFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BondValuation::BondValuation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BondValuation)
}

void BondValuation::InitAsDefaultInstance() {
}

BondValuation::BondValuation(const BondValuation& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BondValuation)
}

void BondValuation::SharedCtor() {
  transactdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  valuationdatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  couponratetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&valuationamt_, 0, reinterpret_cast<char*>(&spreadpx_) -
    reinterpret_cast<char*>(&valuationamt_) + sizeof(spreadpx_));
  _cached_size_ = 0;
}

BondValuation::~BondValuation() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BondValuation)
  SharedDtor();
}

void BondValuation::SharedDtor() {
  transactdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  valuationdatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  couponratetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BondValuation::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BondValuation::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BondValuation_descriptor_;
}

const BondValuation& BondValuation::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BondValuation> BondValuation_default_instance_;

BondValuation* BondValuation::New(::google::protobuf::Arena* arena) const {
  BondValuation* n = new BondValuation;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BondValuation::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BondValuation)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BondValuation, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BondValuation*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(valuationamt_, spreadcnyprice_);
  transactdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  valuationdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  couponratetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(valuationyield_, spreadpx_);

#undef ZR_HELPER_
#undef ZR_

}

bool BondValuation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BondValuation)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TransactDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transactdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transactdate().data(), this->transactdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondValuation.TransactDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_ValuationDateTime;
        break;
      }

      // optional string ValuationDateTime = 2;
      case 2: {
        if (tag == 18) {
         parse_ValuationDateTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuationdatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuationdatetime().data(), this->valuationdatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondValuation.ValuationDateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Currency;
        break;
      }

      // optional string Currency = 3;
      case 3: {
        if (tag == 26) {
         parse_Currency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_currency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->currency().data(), this->currency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondValuation.Currency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_CouponRateType;
        break;
      }

      // optional string CouponRateType = 4;
      case 4: {
        if (tag == 34) {
         parse_CouponRateType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_couponratetype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->couponratetype().data(), this->couponratetype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondValuation.CouponRateType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_ValuationAmt;
        break;
      }

      // optional double ValuationAmt = 5;
      case 5: {
        if (tag == 41) {
         parse_ValuationAmt:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationamt_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_DirtyPrice;
        break;
      }

      // optional double DirtyPrice = 6;
      case 6: {
        if (tag == 49) {
         parse_DirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &dirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_SpreadCNYDirtyPrice;
        break;
      }

      // optional double SpreadCNYDirtyPrice = 7;
      case 7: {
        if (tag == 57) {
         parse_SpreadCNYDirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadcnydirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_SpreadCNYPrice;
        break;
      }

      // optional double SpreadCNYPrice = 8;
      case 8: {
        if (tag == 65) {
         parse_SpreadCNYPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadcnyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(73)) goto parse_ValuationYield;
        break;
      }

      // optional double ValuationYield = 9;
      case 9: {
        if (tag == 73) {
         parse_ValuationYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_SpreadDuration;
        break;
      }

      // optional double SpreadDuration = 10;
      case 10: {
        if (tag == 81) {
         parse_SpreadDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_SpreadConvexity;
        break;
      }

      // optional double SpreadConvexity = 11;
      case 11: {
        if (tag == 89) {
         parse_SpreadConvexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadconvexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_Duration;
        break;
      }

      // optional double Duration = 12;
      case 12: {
        if (tag == 97) {
         parse_Duration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &duration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_ModifyDuration;
        break;
      }

      // optional double ModifyDuration = 13;
      case 13: {
        if (tag == 105) {
         parse_ModifyDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &modifyduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_Convexity;
        break;
      }

      // optional double Convexity = 14;
      case 14: {
        if (tag == 113) {
         parse_Convexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &convexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_SpreadPx;
        break;
      }

      // optional double SpreadPx = 15;
      case 15: {
        if (tag == 121) {
         parse_SpreadPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BondValuation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BondValuation)
  return false;
#undef DO_
}

void BondValuation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BondValuation)
  // optional string TransactDate = 1;
  if (this->transactdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactdate().data(), this->transactdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.TransactDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->transactdate(), output);
  }

  // optional string ValuationDateTime = 2;
  if (this->valuationdatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuationdatetime().data(), this->valuationdatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.ValuationDateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->valuationdatetime(), output);
  }

  // optional string Currency = 3;
  if (this->currency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currency().data(), this->currency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.Currency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->currency(), output);
  }

  // optional string CouponRateType = 4;
  if (this->couponratetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->couponratetype().data(), this->couponratetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.CouponRateType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->couponratetype(), output);
  }

  // optional double ValuationAmt = 5;
  if (this->valuationamt() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->valuationamt(), output);
  }

  // optional double DirtyPrice = 6;
  if (this->dirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->dirtyprice(), output);
  }

  // optional double SpreadCNYDirtyPrice = 7;
  if (this->spreadcnydirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->spreadcnydirtyprice(), output);
  }

  // optional double SpreadCNYPrice = 8;
  if (this->spreadcnyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->spreadcnyprice(), output);
  }

  // optional double ValuationYield = 9;
  if (this->valuationyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->valuationyield(), output);
  }

  // optional double SpreadDuration = 10;
  if (this->spreadduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(10, this->spreadduration(), output);
  }

  // optional double SpreadConvexity = 11;
  if (this->spreadconvexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->spreadconvexity(), output);
  }

  // optional double Duration = 12;
  if (this->duration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->duration(), output);
  }

  // optional double ModifyDuration = 13;
  if (this->modifyduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->modifyduration(), output);
  }

  // optional double Convexity = 14;
  if (this->convexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->convexity(), output);
  }

  // optional double SpreadPx = 15;
  if (this->spreadpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->spreadpx(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BondValuation)
}

::google::protobuf::uint8* BondValuation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BondValuation)
  // optional string TransactDate = 1;
  if (this->transactdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactdate().data(), this->transactdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.TransactDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->transactdate(), target);
  }

  // optional string ValuationDateTime = 2;
  if (this->valuationdatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuationdatetime().data(), this->valuationdatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.ValuationDateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->valuationdatetime(), target);
  }

  // optional string Currency = 3;
  if (this->currency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currency().data(), this->currency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.Currency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->currency(), target);
  }

  // optional string CouponRateType = 4;
  if (this->couponratetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->couponratetype().data(), this->couponratetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondValuation.CouponRateType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->couponratetype(), target);
  }

  // optional double ValuationAmt = 5;
  if (this->valuationamt() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->valuationamt(), target);
  }

  // optional double DirtyPrice = 6;
  if (this->dirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->dirtyprice(), target);
  }

  // optional double SpreadCNYDirtyPrice = 7;
  if (this->spreadcnydirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->spreadcnydirtyprice(), target);
  }

  // optional double SpreadCNYPrice = 8;
  if (this->spreadcnyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->spreadcnyprice(), target);
  }

  // optional double ValuationYield = 9;
  if (this->valuationyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->valuationyield(), target);
  }

  // optional double SpreadDuration = 10;
  if (this->spreadduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(10, this->spreadduration(), target);
  }

  // optional double SpreadConvexity = 11;
  if (this->spreadconvexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->spreadconvexity(), target);
  }

  // optional double Duration = 12;
  if (this->duration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->duration(), target);
  }

  // optional double ModifyDuration = 13;
  if (this->modifyduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->modifyduration(), target);
  }

  // optional double Convexity = 14;
  if (this->convexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->convexity(), target);
  }

  // optional double SpreadPx = 15;
  if (this->spreadpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->spreadpx(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BondValuation)
  return target;
}

size_t BondValuation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BondValuation)
  size_t total_size = 0;

  // optional string TransactDate = 1;
  if (this->transactdate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transactdate());
  }

  // optional string ValuationDateTime = 2;
  if (this->valuationdatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuationdatetime());
  }

  // optional string Currency = 3;
  if (this->currency().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->currency());
  }

  // optional string CouponRateType = 4;
  if (this->couponratetype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->couponratetype());
  }

  // optional double ValuationAmt = 5;
  if (this->valuationamt() != 0) {
    total_size += 1 + 8;
  }

  // optional double DirtyPrice = 6;
  if (this->dirtyprice() != 0) {
    total_size += 1 + 8;
  }

  // optional double SpreadCNYDirtyPrice = 7;
  if (this->spreadcnydirtyprice() != 0) {
    total_size += 1 + 8;
  }

  // optional double SpreadCNYPrice = 8;
  if (this->spreadcnyprice() != 0) {
    total_size += 1 + 8;
  }

  // optional double ValuationYield = 9;
  if (this->valuationyield() != 0) {
    total_size += 1 + 8;
  }

  // optional double SpreadDuration = 10;
  if (this->spreadduration() != 0) {
    total_size += 1 + 8;
  }

  // optional double SpreadConvexity = 11;
  if (this->spreadconvexity() != 0) {
    total_size += 1 + 8;
  }

  // optional double Duration = 12;
  if (this->duration() != 0) {
    total_size += 1 + 8;
  }

  // optional double ModifyDuration = 13;
  if (this->modifyduration() != 0) {
    total_size += 1 + 8;
  }

  // optional double Convexity = 14;
  if (this->convexity() != 0) {
    total_size += 1 + 8;
  }

  // optional double SpreadPx = 15;
  if (this->spreadpx() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BondValuation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BondValuation)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BondValuation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BondValuation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BondValuation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BondValuation)
    UnsafeMergeFrom(*source);
  }
}

void BondValuation::MergeFrom(const BondValuation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BondValuation)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BondValuation::UnsafeMergeFrom(const BondValuation& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.transactdate().size() > 0) {

    transactdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transactdate_);
  }
  if (from.valuationdatetime().size() > 0) {

    valuationdatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuationdatetime_);
  }
  if (from.currency().size() > 0) {

    currency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.currency_);
  }
  if (from.couponratetype().size() > 0) {

    couponratetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.couponratetype_);
  }
  if (from.valuationamt() != 0) {
    set_valuationamt(from.valuationamt());
  }
  if (from.dirtyprice() != 0) {
    set_dirtyprice(from.dirtyprice());
  }
  if (from.spreadcnydirtyprice() != 0) {
    set_spreadcnydirtyprice(from.spreadcnydirtyprice());
  }
  if (from.spreadcnyprice() != 0) {
    set_spreadcnyprice(from.spreadcnyprice());
  }
  if (from.valuationyield() != 0) {
    set_valuationyield(from.valuationyield());
  }
  if (from.spreadduration() != 0) {
    set_spreadduration(from.spreadduration());
  }
  if (from.spreadconvexity() != 0) {
    set_spreadconvexity(from.spreadconvexity());
  }
  if (from.duration() != 0) {
    set_duration(from.duration());
  }
  if (from.modifyduration() != 0) {
    set_modifyduration(from.modifyduration());
  }
  if (from.convexity() != 0) {
    set_convexity(from.convexity());
  }
  if (from.spreadpx() != 0) {
    set_spreadpx(from.spreadpx());
  }
}

void BondValuation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BondValuation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BondValuation::CopyFrom(const BondValuation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BondValuation)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BondValuation::IsInitialized() const {

  return true;
}

void BondValuation::Swap(BondValuation* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BondValuation::InternalSwap(BondValuation* other) {
  transactdate_.Swap(&other->transactdate_);
  valuationdatetime_.Swap(&other->valuationdatetime_);
  currency_.Swap(&other->currency_);
  couponratetype_.Swap(&other->couponratetype_);
  std::swap(valuationamt_, other->valuationamt_);
  std::swap(dirtyprice_, other->dirtyprice_);
  std::swap(spreadcnydirtyprice_, other->spreadcnydirtyprice_);
  std::swap(spreadcnyprice_, other->spreadcnyprice_);
  std::swap(valuationyield_, other->valuationyield_);
  std::swap(spreadduration_, other->spreadduration_);
  std::swap(spreadconvexity_, other->spreadconvexity_);
  std::swap(duration_, other->duration_);
  std::swap(modifyduration_, other->modifyduration_);
  std::swap(convexity_, other->convexity_);
  std::swap(spreadpx_, other->spreadpx_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BondValuation::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BondValuation_descriptor_;
  metadata.reflection = BondValuation_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BondValuation

// optional string TransactDate = 1;
void BondValuation::clear_transactdate() {
  transactdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondValuation::transactdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  return transactdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_transactdate(const ::std::string& value) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
void BondValuation::set_transactdate(const char* value) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
void BondValuation::set_transactdate(const char* value, size_t size) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
::std::string* BondValuation::mutable_transactdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  return transactdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondValuation::release_transactdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  
  return transactdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_allocated_transactdate(::std::string* transactdate) {
  if (transactdate != NULL) {
    
  } else {
    
  }
  transactdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}

// optional string ValuationDateTime = 2;
void BondValuation::clear_valuationdatetime() {
  valuationdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondValuation::valuationdatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  return valuationdatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_valuationdatetime(const ::std::string& value) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
void BondValuation::set_valuationdatetime(const char* value) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
void BondValuation::set_valuationdatetime(const char* value, size_t size) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
::std::string* BondValuation::mutable_valuationdatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  return valuationdatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondValuation::release_valuationdatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  
  return valuationdatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_allocated_valuationdatetime(::std::string* valuationdatetime) {
  if (valuationdatetime != NULL) {
    
  } else {
    
  }
  valuationdatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuationdatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}

// optional string Currency = 3;
void BondValuation::clear_currency() {
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondValuation::currency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Currency)
  return currency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_currency(const ::std::string& value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Currency)
}
void BondValuation::set_currency(const char* value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.Currency)
}
void BondValuation::set_currency(const char* value, size_t size) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.Currency)
}
::std::string* BondValuation::mutable_currency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.Currency)
  return currency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondValuation::release_currency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.Currency)
  
  return currency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_allocated_currency(::std::string* currency) {
  if (currency != NULL) {
    
  } else {
    
  }
  currency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.Currency)
}

// optional string CouponRateType = 4;
void BondValuation::clear_couponratetype() {
  couponratetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondValuation::couponratetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  return couponratetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_couponratetype(const ::std::string& value) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
void BondValuation::set_couponratetype(const char* value) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
void BondValuation::set_couponratetype(const char* value, size_t size) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
::std::string* BondValuation::mutable_couponratetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  return couponratetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondValuation::release_couponratetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  
  return couponratetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondValuation::set_allocated_couponratetype(::std::string* couponratetype) {
  if (couponratetype != NULL) {
    
  } else {
    
  }
  couponratetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), couponratetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}

// optional double ValuationAmt = 5;
void BondValuation::clear_valuationamt() {
  valuationamt_ = 0;
}
double BondValuation::valuationamt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationAmt)
  return valuationamt_;
}
void BondValuation::set_valuationamt(double value) {
  
  valuationamt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationAmt)
}

// optional double DirtyPrice = 6;
void BondValuation::clear_dirtyprice() {
  dirtyprice_ = 0;
}
double BondValuation::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.DirtyPrice)
  return dirtyprice_;
}
void BondValuation::set_dirtyprice(double value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.DirtyPrice)
}

// optional double SpreadCNYDirtyPrice = 7;
void BondValuation::clear_spreadcnydirtyprice() {
  spreadcnydirtyprice_ = 0;
}
double BondValuation::spreadcnydirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadCNYDirtyPrice)
  return spreadcnydirtyprice_;
}
void BondValuation::set_spreadcnydirtyprice(double value) {
  
  spreadcnydirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadCNYDirtyPrice)
}

// optional double SpreadCNYPrice = 8;
void BondValuation::clear_spreadcnyprice() {
  spreadcnyprice_ = 0;
}
double BondValuation::spreadcnyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadCNYPrice)
  return spreadcnyprice_;
}
void BondValuation::set_spreadcnyprice(double value) {
  
  spreadcnyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadCNYPrice)
}

// optional double ValuationYield = 9;
void BondValuation::clear_valuationyield() {
  valuationyield_ = 0;
}
double BondValuation::valuationyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationYield)
  return valuationyield_;
}
void BondValuation::set_valuationyield(double value) {
  
  valuationyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationYield)
}

// optional double SpreadDuration = 10;
void BondValuation::clear_spreadduration() {
  spreadduration_ = 0;
}
double BondValuation::spreadduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadDuration)
  return spreadduration_;
}
void BondValuation::set_spreadduration(double value) {
  
  spreadduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadDuration)
}

// optional double SpreadConvexity = 11;
void BondValuation::clear_spreadconvexity() {
  spreadconvexity_ = 0;
}
double BondValuation::spreadconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadConvexity)
  return spreadconvexity_;
}
void BondValuation::set_spreadconvexity(double value) {
  
  spreadconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadConvexity)
}

// optional double Duration = 12;
void BondValuation::clear_duration() {
  duration_ = 0;
}
double BondValuation::duration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Duration)
  return duration_;
}
void BondValuation::set_duration(double value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Duration)
}

// optional double ModifyDuration = 13;
void BondValuation::clear_modifyduration() {
  modifyduration_ = 0;
}
double BondValuation::modifyduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ModifyDuration)
  return modifyduration_;
}
void BondValuation::set_modifyduration(double value) {
  
  modifyduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ModifyDuration)
}

// optional double Convexity = 14;
void BondValuation::clear_convexity() {
  convexity_ = 0;
}
double BondValuation::convexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Convexity)
  return convexity_;
}
void BondValuation::set_convexity(double value) {
  
  convexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Convexity)
}

// optional double SpreadPx = 15;
void BondValuation::clear_spreadpx() {
  spreadpx_ = 0;
}
double BondValuation::spreadpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadPx)
  return spreadpx_;
}
void BondValuation::set_spreadpx(double value) {
  
  spreadpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadPx)
}

inline const BondValuation* BondValuation::internal_default_instance() {
  return &BondValuation_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BondIndex::kKCurveTypeFieldNumber;
const int BondIndex::kLastPxFieldNumber;
const int BondIndex::kOpenPxFieldNumber;
const int BondIndex::kClosePxFieldNumber;
const int BondIndex::kHighPxFieldNumber;
const int BondIndex::kLowPxFieldNumber;
const int BondIndex::kTotalVolumeTradeMillionFieldNumber;
const int BondIndex::kTotalValueTradeMillionFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BondIndex::BondIndex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BondIndex)
}

void BondIndex::InitAsDefaultInstance() {
}

BondIndex::BondIndex(const BondIndex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BondIndex)
}

void BondIndex::SharedCtor() {
  ::memset(&lastpx_, 0, reinterpret_cast<char*>(&kcurvetype_) -
    reinterpret_cast<char*>(&lastpx_) + sizeof(kcurvetype_));
  _cached_size_ = 0;
}

BondIndex::~BondIndex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BondIndex)
  SharedDtor();
}

void BondIndex::SharedDtor() {
}

void BondIndex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BondIndex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BondIndex_descriptor_;
}

const BondIndex& BondIndex::default_instance() {
  protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BondIndex> BondIndex_default_instance_;

BondIndex* BondIndex::New(::google::protobuf::Arena* arena) const {
  BondIndex* n = new BondIndex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BondIndex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BondIndex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BondIndex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BondIndex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(lastpx_, kcurvetype_);

#undef ZR_HELPER_
#undef ZR_

}

bool BondIndex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BondIndex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 KCurveType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &kcurvetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_LastPx;
        break;
      }

      // optional double LastPx = 2;
      case 2: {
        if (tag == 17) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_OpenPx;
        break;
      }

      // optional double OpenPx = 3;
      case 3: {
        if (tag == 25) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_ClosePx;
        break;
      }

      // optional double ClosePx = 4;
      case 4: {
        if (tag == 33) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_HighPx;
        break;
      }

      // optional double HighPx = 5;
      case 5: {
        if (tag == 41) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_LowPx;
        break;
      }

      // optional double LowPx = 6;
      case 6: {
        if (tag == 49) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_TotalVolumeTradeMillion;
        break;
      }

      // optional double TotalVolumeTradeMillion = 7;
      case 7: {
        if (tag == 57) {
         parse_TotalVolumeTradeMillion:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalvolumetrademillion_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_TotalValueTradeMillion;
        break;
      }

      // optional double TotalValueTradeMillion = 8;
      case 8: {
        if (tag == 65) {
         parse_TotalValueTradeMillion:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalvaluetrademillion_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BondIndex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BondIndex)
  return false;
#undef DO_
}

void BondIndex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BondIndex)
  // optional int32 KCurveType = 1;
  if (this->kcurvetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->kcurvetype(), output);
  }

  // optional double LastPx = 2;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->lastpx(), output);
  }

  // optional double OpenPx = 3;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->openpx(), output);
  }

  // optional double ClosePx = 4;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->closepx(), output);
  }

  // optional double HighPx = 5;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->highpx(), output);
  }

  // optional double LowPx = 6;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->lowpx(), output);
  }

  // optional double TotalVolumeTradeMillion = 7;
  if (this->totalvolumetrademillion() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->totalvolumetrademillion(), output);
  }

  // optional double TotalValueTradeMillion = 8;
  if (this->totalvaluetrademillion() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->totalvaluetrademillion(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BondIndex)
}

::google::protobuf::uint8* BondIndex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BondIndex)
  // optional int32 KCurveType = 1;
  if (this->kcurvetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->kcurvetype(), target);
  }

  // optional double LastPx = 2;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->lastpx(), target);
  }

  // optional double OpenPx = 3;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->openpx(), target);
  }

  // optional double ClosePx = 4;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->closepx(), target);
  }

  // optional double HighPx = 5;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->highpx(), target);
  }

  // optional double LowPx = 6;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->lowpx(), target);
  }

  // optional double TotalVolumeTradeMillion = 7;
  if (this->totalvolumetrademillion() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->totalvolumetrademillion(), target);
  }

  // optional double TotalValueTradeMillion = 8;
  if (this->totalvaluetrademillion() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->totalvaluetrademillion(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BondIndex)
  return target;
}

size_t BondIndex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BondIndex)
  size_t total_size = 0;

  // optional int32 KCurveType = 1;
  if (this->kcurvetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->kcurvetype());
  }

  // optional double LastPx = 2;
  if (this->lastpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double OpenPx = 3;
  if (this->openpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double ClosePx = 4;
  if (this->closepx() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighPx = 5;
  if (this->highpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double LowPx = 6;
  if (this->lowpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalVolumeTradeMillion = 7;
  if (this->totalvolumetrademillion() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalValueTradeMillion = 8;
  if (this->totalvaluetrademillion() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BondIndex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BondIndex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BondIndex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BondIndex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BondIndex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BondIndex)
    UnsafeMergeFrom(*source);
  }
}

void BondIndex::MergeFrom(const BondIndex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BondIndex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BondIndex::UnsafeMergeFrom(const BondIndex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.kcurvetype() != 0) {
    set_kcurvetype(from.kcurvetype());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.totalvolumetrademillion() != 0) {
    set_totalvolumetrademillion(from.totalvolumetrademillion());
  }
  if (from.totalvaluetrademillion() != 0) {
    set_totalvaluetrademillion(from.totalvaluetrademillion());
  }
}

void BondIndex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BondIndex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BondIndex::CopyFrom(const BondIndex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BondIndex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BondIndex::IsInitialized() const {

  return true;
}

void BondIndex::Swap(BondIndex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BondIndex::InternalSwap(BondIndex* other) {
  std::swap(kcurvetype_, other->kcurvetype_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(totalvolumetrademillion_, other->totalvolumetrademillion_);
  std::swap(totalvaluetrademillion_, other->totalvaluetrademillion_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BondIndex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BondIndex_descriptor_;
  metadata.reflection = BondIndex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BondIndex

// optional int32 KCurveType = 1;
void BondIndex::clear_kcurvetype() {
  kcurvetype_ = 0;
}
::google::protobuf::int32 BondIndex::kcurvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.KCurveType)
  return kcurvetype_;
}
void BondIndex::set_kcurvetype(::google::protobuf::int32 value) {
  
  kcurvetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.KCurveType)
}

// optional double LastPx = 2;
void BondIndex::clear_lastpx() {
  lastpx_ = 0;
}
double BondIndex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.LastPx)
  return lastpx_;
}
void BondIndex::set_lastpx(double value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.LastPx)
}

// optional double OpenPx = 3;
void BondIndex::clear_openpx() {
  openpx_ = 0;
}
double BondIndex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.OpenPx)
  return openpx_;
}
void BondIndex::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.OpenPx)
}

// optional double ClosePx = 4;
void BondIndex::clear_closepx() {
  closepx_ = 0;
}
double BondIndex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.ClosePx)
  return closepx_;
}
void BondIndex::set_closepx(double value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.ClosePx)
}

// optional double HighPx = 5;
void BondIndex::clear_highpx() {
  highpx_ = 0;
}
double BondIndex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.HighPx)
  return highpx_;
}
void BondIndex::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.HighPx)
}

// optional double LowPx = 6;
void BondIndex::clear_lowpx() {
  lowpx_ = 0;
}
double BondIndex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.LowPx)
  return lowpx_;
}
void BondIndex::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.LowPx)
}

// optional double TotalVolumeTradeMillion = 7;
void BondIndex::clear_totalvolumetrademillion() {
  totalvolumetrademillion_ = 0;
}
double BondIndex::totalvolumetrademillion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.TotalVolumeTradeMillion)
  return totalvolumetrademillion_;
}
void BondIndex::set_totalvolumetrademillion(double value) {
  
  totalvolumetrademillion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.TotalVolumeTradeMillion)
}

// optional double TotalValueTradeMillion = 8;
void BondIndex::clear_totalvaluetrademillion() {
  totalvaluetrademillion_ = 0;
}
double BondIndex::totalvaluetrademillion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.TotalValueTradeMillion)
  return totalvaluetrademillion_;
}
void BondIndex::set_totalvaluetrademillion(double value) {
  
  totalvaluetrademillion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.TotalValueTradeMillion)
}

inline const BondIndex* BondIndex::internal_default_instance() {
  return &BondIndex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
