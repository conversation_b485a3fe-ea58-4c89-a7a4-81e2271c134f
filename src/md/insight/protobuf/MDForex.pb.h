// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDForex.proto

#ifndef PROTOBUF_MDForex_2eproto__INCLUDED
#define PROTOBUF_MDForex_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDForex_2eproto();
void protobuf_InitDefaults_MDForex_2eproto();
void protobuf_AssignDesc_MDForex_2eproto();
void protobuf_ShutdownFile_MDForex_2eproto();

class ForexEntry;
class MDForex;

// ===================================================================

class MDForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDForex) */ {
 public:
  MDForex();
  virtual ~MDForex();

  MDForex(const MDForex& from);

  inline MDForex& operator=(const MDForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDForex& default_instance();

  static const MDForex* internal_default_instance();

  void Swap(MDForex* other);

  // implements Message ----------------------------------------------

  inline MDForex* New() const { return New(NULL); }

  MDForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDForex& from);
  void MergeFrom(const MDForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDForex* other);
  void UnsafeMergeFrom(const MDForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 11;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 11;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 12;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 13;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 13;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 14;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 14;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 15;
  void clear_closepx();
  static const int kClosePxFieldNumber = 15;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 16;
  void clear_highpx();
  static const int kHighPxFieldNumber = 16;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 17;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 17;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 18;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 18;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 19;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 19;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 59;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 59;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 MidPx = 60;
  void clear_midpx();
  static const int kMidPxFieldNumber = 60;
  ::google::protobuf::int64 midpx() const;
  void set_midpx(::google::protobuf::int64 value);

  // optional string BuyCurrency = 61;
  void clear_buycurrency();
  static const int kBuyCurrencyFieldNumber = 61;
  const ::std::string& buycurrency() const;
  void set_buycurrency(const ::std::string& value);
  void set_buycurrency(const char* value);
  void set_buycurrency(const char* value, size_t size);
  ::std::string* mutable_buycurrency();
  ::std::string* release_buycurrency();
  void set_allocated_buycurrency(::std::string* buycurrency);

  // optional string SellCurrency = 62;
  void clear_sellcurrency();
  static const int kSellCurrencyFieldNumber = 62;
  const ::std::string& sellcurrency() const;
  void set_sellcurrency(const ::std::string& value);
  void set_sellcurrency(const char* value);
  void set_sellcurrency(const char* value, size_t size);
  ::std::string* mutable_sellcurrency();
  ::std::string* release_sellcurrency();
  void set_allocated_sellcurrency(::std::string* sellcurrency);

  // optional int32 Tenor = 63;
  void clear_tenor();
  static const int kTenorFieldNumber = 63;
  ::google::protobuf::int32 tenor() const;
  void set_tenor(::google::protobuf::int32 value);

  // optional string SettleDate = 20;
  void clear_settledate();
  static const int kSettleDateFieldNumber = 20;
  const ::std::string& settledate() const;
  void set_settledate(const ::std::string& value);
  void set_settledate(const char* value);
  void set_settledate(const char* value, size_t size);
  ::std::string* mutable_settledate();
  ::std::string* release_settledate();
  void set_allocated_settledate(::std::string* settledate);

  // repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
  int forexentries_size() const;
  void clear_forexentries();
  static const int kForexEntriesFieldNumber = 21;
  const ::com::htsc::mdc::insight::model::ForexEntry& forexentries(int index) const;
  ::com::htsc::mdc::insight::model::ForexEntry* mutable_forexentries(int index);
  ::com::htsc::mdc::insight::model::ForexEntry* add_forexentries();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >*
      mutable_forexentries();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >&
      forexentries() const;

  // optional string MDReqId = 22;
  void clear_mdreqid();
  static const int kMDReqIdFieldNumber = 22;
  const ::std::string& mdreqid() const;
  void set_mdreqid(const ::std::string& value);
  void set_mdreqid(const char* value);
  void set_mdreqid(const char* value, size_t size);
  ::std::string* mutable_mdreqid();
  ::std::string* release_mdreqid();
  void set_allocated_mdreqid(::std::string* mdreqid);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry > forexentries_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr buycurrency_;
  ::google::protobuf::internal::ArenaStringPtr sellcurrency_;
  ::google::protobuf::internal::ArenaStringPtr settledate_;
  ::google::protobuf::internal::ArenaStringPtr mdreqid_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 midpx_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 tenor_;
  ::google::protobuf::int32 delaytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDForex_2eproto();
  friend void protobuf_ShutdownFile_MDForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDForex> MDForex_default_instance_;

// -------------------------------------------------------------------

class ForexEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ForexEntry) */ {
 public:
  ForexEntry();
  virtual ~ForexEntry();

  ForexEntry(const ForexEntry& from);

  inline ForexEntry& operator=(const ForexEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ForexEntry& default_instance();

  static const ForexEntry* internal_default_instance();

  void Swap(ForexEntry* other);

  // implements Message ----------------------------------------------

  inline ForexEntry* New() const { return New(NULL); }

  ForexEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForexEntry& from);
  void MergeFrom(const ForexEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForexEntry* other);
  void UnsafeMergeFrom(const ForexEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 MDEntryType = 1;
  void clear_mdentrytype();
  static const int kMDEntryTypeFieldNumber = 1;
  ::google::protobuf::int32 mdentrytype() const;
  void set_mdentrytype(::google::protobuf::int32 value);

  // optional double MDEntryPx = 2;
  void clear_mdentrypx();
  static const int kMDEntryPxFieldNumber = 2;
  double mdentrypx() const;
  void set_mdentrypx(double value);

  // optional double MDEntrySize = 3;
  void clear_mdentrysize();
  static const int kMDEntrySizeFieldNumber = 3;
  double mdentrysize() const;
  void set_mdentrysize(double value);

  // optional string MDEntryId = 4;
  void clear_mdentryid();
  static const int kMDEntryIdFieldNumber = 4;
  const ::std::string& mdentryid() const;
  void set_mdentryid(const ::std::string& value);
  void set_mdentryid(const char* value);
  void set_mdentryid(const char* value, size_t size);
  ::std::string* mutable_mdentryid();
  ::std::string* release_mdentryid();
  void set_allocated_mdentryid(::std::string* mdentryid);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ForexEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr mdentryid_;
  double mdentrypx_;
  double mdentrysize_;
  ::google::protobuf::int32 mdentrytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDForex_2eproto();
  friend void protobuf_ShutdownFile_MDForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForexEntry> ForexEntry_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDForex

// optional string HTSCSecurityID = 1;
inline void MDForex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
inline void MDForex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
inline void MDForex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
inline ::std::string* MDForex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDForex::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDForex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDDate)
  return mddate_;
}
inline void MDForex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDDate)
}

// optional int32 MDTime = 3;
inline void MDForex::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDForex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDTime)
  return mdtime_;
}
inline void MDForex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDForex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DataTimestamp)
  return datatimestamp_;
}
inline void MDForex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDForex::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
inline void MDForex::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
inline void MDForex::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
inline ::std::string* MDForex::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDForex::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDForex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDForex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDForex::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDForex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDForex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.securityType)
}

// optional int64 MaxPx = 8;
inline void MDForex::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MaxPx)
  return maxpx_;
}
inline void MDForex::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDForex::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MinPx)
  return minpx_;
}
inline void MDForex::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDForex::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.PreClosePx)
  return preclosepx_;
}
inline void MDForex::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.PreClosePx)
}

// optional int64 TotalVolumeTrade = 11;
inline void MDForex::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDForex::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 12;
inline void MDForex::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDForex::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TotalValueTrade)
}

// optional int64 LastPx = 13;
inline void MDForex::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.LastPx)
  return lastpx_;
}
inline void MDForex::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.LastPx)
}

// optional int64 OpenPx = 14;
inline void MDForex::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.OpenPx)
  return openpx_;
}
inline void MDForex::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.OpenPx)
}

// optional int64 ClosePx = 15;
inline void MDForex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ClosePx)
  return closepx_;
}
inline void MDForex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ClosePx)
}

// optional int64 HighPx = 16;
inline void MDForex::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.HighPx)
  return highpx_;
}
inline void MDForex::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.HighPx)
}

// optional int64 LowPx = 17;
inline void MDForex::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.LowPx)
  return lowpx_;
}
inline void MDForex::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.LowPx)
}

// optional int32 ExchangeDate = 18;
inline void MDForex::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDForex::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ExchangeDate)
  return exchangedate_;
}
inline void MDForex::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ExchangeDate)
}

// optional int32 ExchangeTime = 19;
inline void MDForex::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDForex::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ExchangeTime)
  return exchangetime_;
}
inline void MDForex::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ExchangeTime)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDForex::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDForex::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDForex::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDForex::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
}
inline void MDForex::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDForex::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDForex::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDForex::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
}
inline void MDForex::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDForex::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDForex::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDForex::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDForex::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
}
inline void MDForex::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDForex::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDForex::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDForex::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
}
inline void MDForex::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDForex::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDForex::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDForex::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
}
inline void MDForex::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDForex::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDForex::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDForex::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
}
inline void MDForex::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDForex::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDForex::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDForex::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
}
inline void MDForex::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDForex::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDForex::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDForex::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDForex::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
}
inline void MDForex::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
inline void MDForex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDForex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDForex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DataMultiplePowerOf10)
}

// optional int64 MidPx = 60;
inline void MDForex::clear_midpx() {
  midpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDForex::midpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MidPx)
  return midpx_;
}
inline void MDForex::set_midpx(::google::protobuf::int64 value) {
  
  midpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MidPx)
}

// optional string BuyCurrency = 61;
inline void MDForex::clear_buycurrency() {
  buycurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::buycurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  return buycurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_buycurrency(const ::std::string& value) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
inline void MDForex::set_buycurrency(const char* value) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
inline void MDForex::set_buycurrency(const char* value, size_t size) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
inline ::std::string* MDForex::mutable_buycurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  return buycurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_buycurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  
  return buycurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_buycurrency(::std::string* buycurrency) {
  if (buycurrency != NULL) {
    
  } else {
    
  }
  buycurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buycurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}

// optional string SellCurrency = 62;
inline void MDForex::clear_sellcurrency() {
  sellcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::sellcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  return sellcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_sellcurrency(const ::std::string& value) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
inline void MDForex::set_sellcurrency(const char* value) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
inline void MDForex::set_sellcurrency(const char* value, size_t size) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
inline ::std::string* MDForex::mutable_sellcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  return sellcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_sellcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  
  return sellcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_sellcurrency(::std::string* sellcurrency) {
  if (sellcurrency != NULL) {
    
  } else {
    
  }
  sellcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}

// optional int32 Tenor = 63;
inline void MDForex::clear_tenor() {
  tenor_ = 0;
}
inline ::google::protobuf::int32 MDForex::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.Tenor)
  return tenor_;
}
inline void MDForex::set_tenor(::google::protobuf::int32 value) {
  
  tenor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.Tenor)
}

// optional string SettleDate = 20;
inline void MDForex::clear_settledate() {
  settledate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::settledate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SettleDate)
  return settledate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_settledate(const ::std::string& value) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
inline void MDForex::set_settledate(const char* value) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
inline void MDForex::set_settledate(const char* value, size_t size) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
inline ::std::string* MDForex::mutable_settledate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.SettleDate)
  return settledate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_settledate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.SettleDate)
  
  return settledate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_settledate(::std::string* settledate) {
  if (settledate != NULL) {
    
  } else {
    
  }
  settledate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settledate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.SettleDate)
}

// repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
inline int MDForex::forexentries_size() const {
  return forexentries_.size();
}
inline void MDForex::clear_forexentries() {
  forexentries_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ForexEntry& MDForex::forexentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ForexEntry* MDForex::mutable_forexentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ForexEntry* MDForex::add_forexentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >*
MDForex::mutable_forexentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return &forexentries_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >&
MDForex::forexentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_;
}

// optional string MDReqId = 22;
inline void MDForex::clear_mdreqid() {
  mdreqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDForex::mdreqid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDReqId)
  return mdreqid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_mdreqid(const ::std::string& value) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
inline void MDForex::set_mdreqid(const char* value) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
inline void MDForex::set_mdreqid(const char* value, size_t size) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
inline ::std::string* MDForex::mutable_mdreqid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.MDReqId)
  return mdreqid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDForex::release_mdreqid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.MDReqId)
  
  return mdreqid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDForex::set_allocated_mdreqid(::std::string* mdreqid) {
  if (mdreqid != NULL) {
    
  } else {
    
  }
  mdreqid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdreqid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.MDReqId)
}

// optional int32 DelayType = 101;
inline void MDForex::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDForex::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DelayType)
  return delaytype_;
}
inline void MDForex::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DelayType)
}

inline const MDForex* MDForex::internal_default_instance() {
  return &MDForex_default_instance_.get();
}
// -------------------------------------------------------------------

// ForexEntry

// optional int32 MDEntryType = 1;
inline void ForexEntry::clear_mdentrytype() {
  mdentrytype_ = 0;
}
inline ::google::protobuf::int32 ForexEntry::mdentrytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryType)
  return mdentrytype_;
}
inline void ForexEntry::set_mdentrytype(::google::protobuf::int32 value) {
  
  mdentrytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryType)
}

// optional double MDEntryPx = 2;
inline void ForexEntry::clear_mdentrypx() {
  mdentrypx_ = 0;
}
inline double ForexEntry::mdentrypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryPx)
  return mdentrypx_;
}
inline void ForexEntry::set_mdentrypx(double value) {
  
  mdentrypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryPx)
}

// optional double MDEntrySize = 3;
inline void ForexEntry::clear_mdentrysize() {
  mdentrysize_ = 0;
}
inline double ForexEntry::mdentrysize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntrySize)
  return mdentrysize_;
}
inline void ForexEntry::set_mdentrysize(double value) {
  
  mdentrysize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntrySize)
}

// optional string MDEntryId = 4;
inline void ForexEntry::clear_mdentryid() {
  mdentryid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForexEntry::mdentryid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  return mdentryid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForexEntry::set_mdentryid(const ::std::string& value) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
inline void ForexEntry::set_mdentryid(const char* value) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
inline void ForexEntry::set_mdentryid(const char* value, size_t size) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
inline ::std::string* ForexEntry::mutable_mdentryid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  return mdentryid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForexEntry::release_mdentryid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  
  return mdentryid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForexEntry::set_allocated_mdentryid(::std::string* mdentryid) {
  if (mdentryid != NULL) {
    
  } else {
    
  }
  mdentryid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdentryid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}

inline const ForexEntry* ForexEntry::internal_default_instance() {
  return &ForexEntry_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDForex_2eproto__INCLUDED
