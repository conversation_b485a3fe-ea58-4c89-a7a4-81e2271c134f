syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDFIQuote {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  string QuoteID = 10;
  double BidYield = 11;
  double AskYield = 12;
  double BidPrice = 13;
  double AskPrice = 14;
  double BidSize = 15;
  double AskSize = 16;
  string QuoteTime = 17;
  string QuoteStatus = 18;
  int64 MessageNumber = 100;
}
