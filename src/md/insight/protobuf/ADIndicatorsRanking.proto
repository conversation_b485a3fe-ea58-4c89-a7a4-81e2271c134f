syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";
import "MDSimpleTick.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADIndicatorsRankingProtos";
option optimize_for = SPEED;

// ADIndicatorsRanking message represents indicators ranking data for securities
message ADIndicatorsRanking {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Type of ranking indicator
    int32 RankingType = 7;
    
    // Typical value for the ranking indicator
    MDSimpleTick TypicalValue = 8;
    
    // List of securities with their ranking values
    repeated MDSimpleTick RankingList = 9;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 10;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 11;
    
    // Data scaling factor (power of 10 multiplier for value fields)
    int32 DataMultiplePowerOf10 = 12;
}
