// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDBond.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDBond.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDBond_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDBond_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDBond_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDBond_2eproto() {
  protobuf_AddDesc_MDBond_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDBond.proto");
  GOOGLE_CHECK(file != NULL);
  MDBond_descriptor_ = file->message_type(0);
  static const int MDBond_offsets_[98] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, diffpx1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, diffpx2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalbuyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalsellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, weightedavgbuypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, weightedavgsellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawbuyamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawbuymoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawsellamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, withdrawsellmoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, totalsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buytrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, selltrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, numbuyorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, numsellorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, yieldtomaturity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, weightedavgpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, weightedavgpxbp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, precloseweightedavgpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, precloseyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, preweightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, openyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, highyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, lowyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, lastyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, weightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, norminalpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, shortsellsharestraded_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, shortsellturnover_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buysettltypequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellsettltypequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, buyyieldqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, sellyieldqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, premarketlastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, premarkettotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, premarkettotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, premarkethighpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, premarketlowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, afterhourslastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, afterhourstotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, afterhourstotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, afterhourshighpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, afterhourslowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, marketphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, subtradingphasecode1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, subtradingphasecode2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, subtradingphasecode3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, subtradingphasecode4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, subtradingphasecode5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, lastpxtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, auctionlastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, auctionvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, auctionvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, usconsolidatevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, uscompositeclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, tradinghaltreason_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, otctotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, otctotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, otcnumtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, referencepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, maxbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, minbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, maxsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, minsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, delaytype_),
  };
  MDBond_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDBond_descriptor_,
      MDBond::internal_default_instance(),
      MDBond_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDBond),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBond, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDBond_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDBond_descriptor_, MDBond::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDBond_2eproto() {
  MDBond_default_instance_.Shutdown();
  delete MDBond_reflection_;
}

void protobuf_InitDefaults_MDBond_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDBond_default_instance_.DefaultConstruct();
  MDBond_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDBond_2eproto_once_);
void protobuf_InitDefaults_MDBond_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDBond_2eproto_once_,
                 &protobuf_InitDefaults_MDBond_2eproto_impl);
}
void protobuf_AddDesc_MDBond_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDBond_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\014MDBond.proto\022\032com.htsc.mdc.insight.mod"
    "el\032\027ESecurityIDSource.proto\032\023ESecurityTy"
    "pe.proto\"\323\023\n\006MDBond\022\026\n\016HTSCSecurityID\030\001 "
    "\001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rD"
    "ataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode\030\005"
    " \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.hts"
    "c.mdc.model.ESecurityIDSource\0227\n\014securit"
    "yType\030\007 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022\n\n"
    "PreClosePx\030\n \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022\030\n\020T"
    "otalVolumeTrade\030\014 \001(\003\022\027\n\017TotalValueTrade"
    "\030\r \001(\003\022\016\n\006LastPx\030\016 \001(\003\022\016\n\006OpenPx\030\017 \001(\003\022\017"
    "\n\007ClosePx\030\020 \001(\003\022\016\n\006HighPx\030\021 \001(\003\022\r\n\005LowPx"
    "\030\022 \001(\003\022\017\n\007DiffPx1\030\023 \001(\003\022\017\n\007DiffPx2\030\024 \001(\003"
    "\022\023\n\013TotalBuyQty\030\025 \001(\003\022\024\n\014TotalSellQty\030\026 "
    "\001(\003\022\030\n\020WeightedAvgBuyPx\030\027 \001(\003\022\031\n\021Weighte"
    "dAvgSellPx\030\030 \001(\003\022\031\n\021WithdrawBuyNumber\030\031 "
    "\001(\003\022\031\n\021WithdrawBuyAmount\030\032 \001(\003\022\030\n\020Withdr"
    "awBuyMoney\030\033 \001(\003\022\032\n\022WithdrawSellNumber\030\034"
    " \001(\003\022\032\n\022WithdrawSellAmount\030\035 \001(\003\022\031\n\021With"
    "drawSellMoney\030\036 \001(\003\022\026\n\016TotalBuyNumber\030\037 "
    "\001(\003\022\027\n\017TotalSellNumber\030  \001(\003\022\033\n\023BuyTrade"
    "MaxDuration\030! \001(\003\022\034\n\024SellTradeMaxDuratio"
    "n\030\" \001(\003\022\024\n\014NumBuyOrders\030# \001(\005\022\025\n\rNumSell"
    "Orders\030$ \001(\005\022\027\n\017YieldToMaturity\030% \001(\003\022\025\n"
    "\rWeightedAvgPx\030& \001(\003\022\027\n\017WeightedAvgPxBP\030"
    "\' \001(\003\022\035\n\025PreCloseWeightedAvgPx\030( \001(\003\022\024\n\014"
    "ExchangeDate\030) \001(\005\022\024\n\014ExchangeTime\030* \001(\005"
    "\022\025\n\rPreCloseYield\030+ \001(\003\022\033\n\023PreWeightedAv"
    "gYield\030, \001(\003\022\021\n\tOpenYield\030- \001(\003\022\021\n\tHighY"
    "ield\030. \001(\003\022\020\n\010LowYield\030/ \001(\003\022\021\n\tLastYiel"
    "d\0300 \001(\003\022\030\n\020WeightedAvgYield\0301 \001(\003\022\021\n\tCha"
    "nnelNo\0302 \001(\005\022\031\n\rBuyPriceQueue\0303 \003(\003B\002\020\001\022"
    "\034\n\020BuyOrderQtyQueue\0304 \003(\003B\002\020\001\022\032\n\016SellPri"
    "ceQueue\0305 \003(\003B\002\020\001\022\035\n\021SellOrderQtyQueue\0306"
    " \003(\003B\002\020\001\022\031\n\rBuyOrderQueue\0307 \003(\003B\002\020\001\022\032\n\016S"
    "ellOrderQueue\0308 \003(\003B\002\020\001\022\035\n\021BuyNumOrdersQ"
    "ueue\0309 \003(\003B\002\020\001\022\036\n\022SellNumOrdersQueue\030: \003"
    "(\003B\002\020\001\022\022\n\nNorminalPx\030; \001(\003\022\035\n\025ShortSellS"
    "haresTraded\030< \001(\003\022\031\n\021ShortSellTurnover\030="
    " \001(\003\022\035\n\021BuySettlTypeQueue\030> \003(\005B\002\020\001\022\036\n\022S"
    "ellSettlTypeQueue\030\? \003(\005B\002\020\001\022\031\n\rBuyYieldQ"
    "ueue\030@ \003(\003B\002\020\001\022\032\n\016SellYieldQueue\030A \003(\003B\002"
    "\020\001\022\027\n\017PreMarketLastPx\030B \001(\003\022!\n\031PreMarket"
    "TotalVolumeTrade\030C \001(\003\022 \n\030PreMarketTotal"
    "ValueTrade\030D \001(\003\022\027\n\017PreMarketHighPx\030E \001("
    "\003\022\026\n\016PreMarketLowPx\030F \001(\003\022\030\n\020AfterHoursL"
    "astPx\030G \001(\003\022\"\n\032AfterHoursTotalVolumeTrad"
    "e\030H \001(\003\022!\n\031AfterHoursTotalValueTrade\030I \001"
    "(\003\022\030\n\020AfterHoursHighPx\030J \001(\003\022\027\n\017AfterHou"
    "rsLowPx\030K \001(\003\022\027\n\017MarketPhaseCode\030L \001(\t\022\034"
    "\n\024SubTradingPhaseCode1\030M \001(\t\022\034\n\024SubTradi"
    "ngPhaseCode2\030N \001(\t\022\034\n\024SubTradingPhaseCod"
    "e3\030O \001(\t\022\034\n\024SubTradingPhaseCode4\030P \001(\t\022\034"
    "\n\024SubTradingPhaseCode5\030Q \001(\t\022\022\n\nLastPxTy"
    "pe\030R \001(\005\022\025\n\rAuctionLastPx\030S \001(\003\022\032\n\022Aucti"
    "onVolumeTrade\030T \001(\003\022\031\n\021AuctionValueTrade"
    "\030U \001(\003\022\033\n\023USConsolidateVolume\030V \001(\003\022\032\n\022U"
    "SCompositeClosePx\030W \001(\003\022\031\n\021TradingHaltRe"
    "ason\030X \001(\t\022\033\n\023OtcTotalVolumeTrade\030Y \001(\003\022"
    "\032\n\022OtcTotalValueTrade\030Z \001(\003\022\024\n\014OtcNumTra"
    "des\030[ \001(\003\022\035\n\025DataMultiplePowerOf10\030\\ \001(\005"
    "\022\023\n\013ReferencePx\030] \001(\003\022\023\n\013MaxBuyPrice\030^ \001"
    "(\003\022\023\n\013MinBuyPrice\030_ \001(\003\022\024\n\014MaxSellPrice\030"
    "` \001(\003\022\024\n\014MinSellPrice\030a \001(\003\022\021\n\tDelayType"
    "\030e \001(\005B/\n\032com.htsc.mdc.insight.modelB\014MD"
    "BondProtosH\001\240\001\001b\006proto3", 2663);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDBond.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDBond_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDBond_2eproto_once_);
void protobuf_AddDesc_MDBond_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDBond_2eproto_once_,
                 &protobuf_AddDesc_MDBond_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDBond_2eproto {
  StaticDescriptorInitializer_MDBond_2eproto() {
    protobuf_AddDesc_MDBond_2eproto();
  }
} static_descriptor_initializer_MDBond_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDBond::kHTSCSecurityIDFieldNumber;
const int MDBond::kMDDateFieldNumber;
const int MDBond::kMDTimeFieldNumber;
const int MDBond::kDataTimestampFieldNumber;
const int MDBond::kTradingPhaseCodeFieldNumber;
const int MDBond::kSecurityIDSourceFieldNumber;
const int MDBond::kSecurityTypeFieldNumber;
const int MDBond::kMaxPxFieldNumber;
const int MDBond::kMinPxFieldNumber;
const int MDBond::kPreClosePxFieldNumber;
const int MDBond::kNumTradesFieldNumber;
const int MDBond::kTotalVolumeTradeFieldNumber;
const int MDBond::kTotalValueTradeFieldNumber;
const int MDBond::kLastPxFieldNumber;
const int MDBond::kOpenPxFieldNumber;
const int MDBond::kClosePxFieldNumber;
const int MDBond::kHighPxFieldNumber;
const int MDBond::kLowPxFieldNumber;
const int MDBond::kDiffPx1FieldNumber;
const int MDBond::kDiffPx2FieldNumber;
const int MDBond::kTotalBuyQtyFieldNumber;
const int MDBond::kTotalSellQtyFieldNumber;
const int MDBond::kWeightedAvgBuyPxFieldNumber;
const int MDBond::kWeightedAvgSellPxFieldNumber;
const int MDBond::kWithdrawBuyNumberFieldNumber;
const int MDBond::kWithdrawBuyAmountFieldNumber;
const int MDBond::kWithdrawBuyMoneyFieldNumber;
const int MDBond::kWithdrawSellNumberFieldNumber;
const int MDBond::kWithdrawSellAmountFieldNumber;
const int MDBond::kWithdrawSellMoneyFieldNumber;
const int MDBond::kTotalBuyNumberFieldNumber;
const int MDBond::kTotalSellNumberFieldNumber;
const int MDBond::kBuyTradeMaxDurationFieldNumber;
const int MDBond::kSellTradeMaxDurationFieldNumber;
const int MDBond::kNumBuyOrdersFieldNumber;
const int MDBond::kNumSellOrdersFieldNumber;
const int MDBond::kYieldToMaturityFieldNumber;
const int MDBond::kWeightedAvgPxFieldNumber;
const int MDBond::kWeightedAvgPxBPFieldNumber;
const int MDBond::kPreCloseWeightedAvgPxFieldNumber;
const int MDBond::kExchangeDateFieldNumber;
const int MDBond::kExchangeTimeFieldNumber;
const int MDBond::kPreCloseYieldFieldNumber;
const int MDBond::kPreWeightedAvgYieldFieldNumber;
const int MDBond::kOpenYieldFieldNumber;
const int MDBond::kHighYieldFieldNumber;
const int MDBond::kLowYieldFieldNumber;
const int MDBond::kLastYieldFieldNumber;
const int MDBond::kWeightedAvgYieldFieldNumber;
const int MDBond::kChannelNoFieldNumber;
const int MDBond::kBuyPriceQueueFieldNumber;
const int MDBond::kBuyOrderQtyQueueFieldNumber;
const int MDBond::kSellPriceQueueFieldNumber;
const int MDBond::kSellOrderQtyQueueFieldNumber;
const int MDBond::kBuyOrderQueueFieldNumber;
const int MDBond::kSellOrderQueueFieldNumber;
const int MDBond::kBuyNumOrdersQueueFieldNumber;
const int MDBond::kSellNumOrdersQueueFieldNumber;
const int MDBond::kNorminalPxFieldNumber;
const int MDBond::kShortSellSharesTradedFieldNumber;
const int MDBond::kShortSellTurnoverFieldNumber;
const int MDBond::kBuySettlTypeQueueFieldNumber;
const int MDBond::kSellSettlTypeQueueFieldNumber;
const int MDBond::kBuyYieldQueueFieldNumber;
const int MDBond::kSellYieldQueueFieldNumber;
const int MDBond::kPreMarketLastPxFieldNumber;
const int MDBond::kPreMarketTotalVolumeTradeFieldNumber;
const int MDBond::kPreMarketTotalValueTradeFieldNumber;
const int MDBond::kPreMarketHighPxFieldNumber;
const int MDBond::kPreMarketLowPxFieldNumber;
const int MDBond::kAfterHoursLastPxFieldNumber;
const int MDBond::kAfterHoursTotalVolumeTradeFieldNumber;
const int MDBond::kAfterHoursTotalValueTradeFieldNumber;
const int MDBond::kAfterHoursHighPxFieldNumber;
const int MDBond::kAfterHoursLowPxFieldNumber;
const int MDBond::kMarketPhaseCodeFieldNumber;
const int MDBond::kSubTradingPhaseCode1FieldNumber;
const int MDBond::kSubTradingPhaseCode2FieldNumber;
const int MDBond::kSubTradingPhaseCode3FieldNumber;
const int MDBond::kSubTradingPhaseCode4FieldNumber;
const int MDBond::kSubTradingPhaseCode5FieldNumber;
const int MDBond::kLastPxTypeFieldNumber;
const int MDBond::kAuctionLastPxFieldNumber;
const int MDBond::kAuctionVolumeTradeFieldNumber;
const int MDBond::kAuctionValueTradeFieldNumber;
const int MDBond::kUSConsolidateVolumeFieldNumber;
const int MDBond::kUSCompositeClosePxFieldNumber;
const int MDBond::kTradingHaltReasonFieldNumber;
const int MDBond::kOtcTotalVolumeTradeFieldNumber;
const int MDBond::kOtcTotalValueTradeFieldNumber;
const int MDBond::kOtcNumTradesFieldNumber;
const int MDBond::kDataMultiplePowerOf10FieldNumber;
const int MDBond::kReferencePxFieldNumber;
const int MDBond::kMaxBuyPriceFieldNumber;
const int MDBond::kMinBuyPriceFieldNumber;
const int MDBond::kMaxSellPriceFieldNumber;
const int MDBond::kMinSellPriceFieldNumber;
const int MDBond::kDelayTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDBond::MDBond()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDBond_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDBond)
}

void MDBond::InitAsDefaultInstance() {
}

MDBond::MDBond(const MDBond& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDBond)
}

void MDBond::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode5_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradinghaltreason_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&minsellprice_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(minsellprice_));
  _cached_size_ = 0;
}

MDBond::~MDBond() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDBond)
  SharedDtor();
}

void MDBond::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode5_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradinghaltreason_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDBond::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDBond::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDBond_descriptor_;
}

const MDBond& MDBond::default_instance() {
  protobuf_InitDefaults_MDBond_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDBond> MDBond_default_instance_;

MDBond* MDBond::New(::google::protobuf::Arena* arena) const {
  MDBond* n = new MDBond;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDBond::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDBond)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDBond, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDBond*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, weightedavgsellpx_);
  ZR_(withdrawbuynumber_, totalsellnumber_);
  ZR_(buytrademaxduration_, precloseweightedavgpx_);
  ZR_(exchangedate_, lastyield_);
  weightedavgyield_ = GOOGLE_LONGLONG(0);
  channelno_ = 0;
  ZR_(norminalpx_, shortsellturnover_);
  ZR_(premarkettotalvolumetrade_, afterhourstotalvolumetrade_);
  premarketlastpx_ = GOOGLE_LONGLONG(0);
  ZR_(afterhourstotalvaluetrade_, afterhourslowpx_);
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  subtradingphasecode4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(auctionlastpx_, uscompositeclosepx_);
  subtradingphasecode5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  lastpxtype_ = 0;
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(otctotalvolumetrade_, datamultiplepowerof10_);
  maxsellprice_ = GOOGLE_LONGLONG(0);
  minsellprice_ = GOOGLE_LONGLONG(0);
  delaytype_ = 0;

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
  buysettltypequeue_.Clear();
  sellsettltypequeue_.Clear();
  buyyieldqueue_.Clear();
  sellyieldqueue_.Clear();
}

bool MDBond::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDBond)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_DiffPx1;
        break;
      }

      // optional int64 DiffPx1 = 19;
      case 19: {
        if (tag == 152) {
         parse_DiffPx1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DiffPx2;
        break;
      }

      // optional int64 DiffPx2 = 20;
      case 20: {
        if (tag == 160) {
         parse_DiffPx2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TotalBuyQty;
        break;
      }

      // optional int64 TotalBuyQty = 21;
      case 21: {
        if (tag == 168) {
         parse_TotalBuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TotalSellQty;
        break;
      }

      // optional int64 TotalSellQty = 22;
      case 22: {
        if (tag == 176) {
         parse_TotalSellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_WeightedAvgBuyPx;
        break;
      }

      // optional int64 WeightedAvgBuyPx = 23;
      case 23: {
        if (tag == 184) {
         parse_WeightedAvgBuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgbuypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_WeightedAvgSellPx;
        break;
      }

      // optional int64 WeightedAvgSellPx = 24;
      case 24: {
        if (tag == 192) {
         parse_WeightedAvgSellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgsellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_WithdrawBuyNumber;
        break;
      }

      // optional int64 WithdrawBuyNumber = 25;
      case 25: {
        if (tag == 200) {
         parse_WithdrawBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_WithdrawBuyAmount;
        break;
      }

      // optional int64 WithdrawBuyAmount = 26;
      case 26: {
        if (tag == 208) {
         parse_WithdrawBuyAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuyamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_WithdrawBuyMoney;
        break;
      }

      // optional int64 WithdrawBuyMoney = 27;
      case 27: {
        if (tag == 216) {
         parse_WithdrawBuyMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuymoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_WithdrawSellNumber;
        break;
      }

      // optional int64 WithdrawSellNumber = 28;
      case 28: {
        if (tag == 224) {
         parse_WithdrawSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_WithdrawSellAmount;
        break;
      }

      // optional int64 WithdrawSellAmount = 29;
      case 29: {
        if (tag == 232) {
         parse_WithdrawSellAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_WithdrawSellMoney;
        break;
      }

      // optional int64 WithdrawSellMoney = 30;
      case 30: {
        if (tag == 240) {
         parse_WithdrawSellMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellmoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_TotalBuyNumber;
        break;
      }

      // optional int64 TotalBuyNumber = 31;
      case 31: {
        if (tag == 248) {
         parse_TotalBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_TotalSellNumber;
        break;
      }

      // optional int64 TotalSellNumber = 32;
      case 32: {
        if (tag == 256) {
         parse_TotalSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_BuyTradeMaxDuration;
        break;
      }

      // optional int64 BuyTradeMaxDuration = 33;
      case 33: {
        if (tag == 264) {
         parse_BuyTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buytrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_SellTradeMaxDuration;
        break;
      }

      // optional int64 SellTradeMaxDuration = 34;
      case 34: {
        if (tag == 272) {
         parse_SellTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &selltrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_NumBuyOrders;
        break;
      }

      // optional int32 NumBuyOrders = 35;
      case 35: {
        if (tag == 280) {
         parse_NumBuyOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numbuyorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_NumSellOrders;
        break;
      }

      // optional int32 NumSellOrders = 36;
      case 36: {
        if (tag == 288) {
         parse_NumSellOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numsellorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_YieldToMaturity;
        break;
      }

      // optional int64 YieldToMaturity = 37;
      case 37: {
        if (tag == 296) {
         parse_YieldToMaturity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &yieldtomaturity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_WeightedAvgPx;
        break;
      }

      // optional int64 WeightedAvgPx = 38;
      case 38: {
        if (tag == 304) {
         parse_WeightedAvgPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_WeightedAvgPxBP;
        break;
      }

      // optional int64 WeightedAvgPxBP = 39;
      case 39: {
        if (tag == 312) {
         parse_WeightedAvgPxBP:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgpxbp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_PreCloseWeightedAvgPx;
        break;
      }

      // optional int64 PreCloseWeightedAvgPx = 40;
      case 40: {
        if (tag == 320) {
         parse_PreCloseWeightedAvgPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloseweightedavgpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 41;
      case 41: {
        if (tag == 328) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(336)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 42;
      case 42: {
        if (tag == 336) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_PreCloseYield;
        break;
      }

      // optional int64 PreCloseYield = 43;
      case 43: {
        if (tag == 344) {
         parse_PreCloseYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloseyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_PreWeightedAvgYield;
        break;
      }

      // optional int64 PreWeightedAvgYield = 44;
      case 44: {
        if (tag == 352) {
         parse_PreWeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_OpenYield;
        break;
      }

      // optional int64 OpenYield = 45;
      case 45: {
        if (tag == 360) {
         parse_OpenYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_HighYield;
        break;
      }

      // optional int64 HighYield = 46;
      case 46: {
        if (tag == 368) {
         parse_HighYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(376)) goto parse_LowYield;
        break;
      }

      // optional int64 LowYield = 47;
      case 47: {
        if (tag == 376) {
         parse_LowYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(384)) goto parse_LastYield;
        break;
      }

      // optional int64 LastYield = 48;
      case 48: {
        if (tag == 384) {
         parse_LastYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(392)) goto parse_WeightedAvgYield;
        break;
      }

      // optional int64 WeightedAvgYield = 49;
      case 49: {
        if (tag == 392) {
         parse_WeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 50;
      case 50: {
        if (tag == 400) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_NorminalPx;
        break;
      }

      // optional int64 NorminalPx = 59;
      case 59: {
        if (tag == 472) {
         parse_NorminalPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &norminalpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_ShortSellSharesTraded;
        break;
      }

      // optional int64 ShortSellSharesTraded = 60;
      case 60: {
        if (tag == 480) {
         parse_ShortSellSharesTraded:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &shortsellsharestraded_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(488)) goto parse_ShortSellTurnover;
        break;
      }

      // optional int64 ShortSellTurnover = 61;
      case 61: {
        if (tag == 488) {
         parse_ShortSellTurnover:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &shortsellturnover_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(498)) goto parse_BuySettlTypeQueue;
        break;
      }

      // repeated int32 BuySettlTypeQueue = 62 [packed = true];
      case 62: {
        if (tag == 498) {
         parse_BuySettlTypeQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_buysettltypequeue())));
        } else if (tag == 496) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 498, input, this->mutable_buysettltypequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(506)) goto parse_SellSettlTypeQueue;
        break;
      }

      // repeated int32 SellSettlTypeQueue = 63 [packed = true];
      case 63: {
        if (tag == 506) {
         parse_SellSettlTypeQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_sellsettltypequeue())));
        } else if (tag == 504) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 2, 506, input, this->mutable_sellsettltypequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(514)) goto parse_BuyYieldQueue;
        break;
      }

      // repeated int64 BuyYieldQueue = 64 [packed = true];
      case 64: {
        if (tag == 514) {
         parse_BuyYieldQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyyieldqueue())));
        } else if (tag == 512) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 514, input, this->mutable_buyyieldqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(522)) goto parse_SellYieldQueue;
        break;
      }

      // repeated int64 SellYieldQueue = 65 [packed = true];
      case 65: {
        if (tag == 522) {
         parse_SellYieldQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellyieldqueue())));
        } else if (tag == 520) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 522, input, this->mutable_sellyieldqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(528)) goto parse_PreMarketLastPx;
        break;
      }

      // optional int64 PreMarketLastPx = 66;
      case 66: {
        if (tag == 528) {
         parse_PreMarketLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarketlastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(536)) goto parse_PreMarketTotalVolumeTrade;
        break;
      }

      // optional int64 PreMarketTotalVolumeTrade = 67;
      case 67: {
        if (tag == 536) {
         parse_PreMarketTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkettotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(544)) goto parse_PreMarketTotalValueTrade;
        break;
      }

      // optional int64 PreMarketTotalValueTrade = 68;
      case 68: {
        if (tag == 544) {
         parse_PreMarketTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkettotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(552)) goto parse_PreMarketHighPx;
        break;
      }

      // optional int64 PreMarketHighPx = 69;
      case 69: {
        if (tag == 552) {
         parse_PreMarketHighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkethighpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(560)) goto parse_PreMarketLowPx;
        break;
      }

      // optional int64 PreMarketLowPx = 70;
      case 70: {
        if (tag == 560) {
         parse_PreMarketLowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarketlowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(568)) goto parse_AfterHoursLastPx;
        break;
      }

      // optional int64 AfterHoursLastPx = 71;
      case 71: {
        if (tag == 568) {
         parse_AfterHoursLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourslastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(576)) goto parse_AfterHoursTotalVolumeTrade;
        break;
      }

      // optional int64 AfterHoursTotalVolumeTrade = 72;
      case 72: {
        if (tag == 576) {
         parse_AfterHoursTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourstotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(584)) goto parse_AfterHoursTotalValueTrade;
        break;
      }

      // optional int64 AfterHoursTotalValueTrade = 73;
      case 73: {
        if (tag == 584) {
         parse_AfterHoursTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourstotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(592)) goto parse_AfterHoursHighPx;
        break;
      }

      // optional int64 AfterHoursHighPx = 74;
      case 74: {
        if (tag == 592) {
         parse_AfterHoursHighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourshighpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(600)) goto parse_AfterHoursLowPx;
        break;
      }

      // optional int64 AfterHoursLowPx = 75;
      case 75: {
        if (tag == 600) {
         parse_AfterHoursLowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourslowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(610)) goto parse_MarketPhaseCode;
        break;
      }

      // optional string MarketPhaseCode = 76;
      case 76: {
        if (tag == 610) {
         parse_MarketPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketphasecode().data(), this->marketphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.MarketPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(618)) goto parse_SubTradingPhaseCode1;
        break;
      }

      // optional string SubTradingPhaseCode1 = 77;
      case 77: {
        if (tag == 618) {
         parse_SubTradingPhaseCode1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_subtradingphasecode1()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->subtradingphasecode1().data(), this->subtradingphasecode1().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(626)) goto parse_SubTradingPhaseCode2;
        break;
      }

      // optional string SubTradingPhaseCode2 = 78;
      case 78: {
        if (tag == 626) {
         parse_SubTradingPhaseCode2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_subtradingphasecode2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->subtradingphasecode2().data(), this->subtradingphasecode2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(634)) goto parse_SubTradingPhaseCode3;
        break;
      }

      // optional string SubTradingPhaseCode3 = 79;
      case 79: {
        if (tag == 634) {
         parse_SubTradingPhaseCode3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_subtradingphasecode3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->subtradingphasecode3().data(), this->subtradingphasecode3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(642)) goto parse_SubTradingPhaseCode4;
        break;
      }

      // optional string SubTradingPhaseCode4 = 80;
      case 80: {
        if (tag == 642) {
         parse_SubTradingPhaseCode4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_subtradingphasecode4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->subtradingphasecode4().data(), this->subtradingphasecode4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(650)) goto parse_SubTradingPhaseCode5;
        break;
      }

      // optional string SubTradingPhaseCode5 = 81;
      case 81: {
        if (tag == 650) {
         parse_SubTradingPhaseCode5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_subtradingphasecode5()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->subtradingphasecode5().data(), this->subtradingphasecode5().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(656)) goto parse_LastPxType;
        break;
      }

      // optional int32 LastPxType = 82;
      case 82: {
        if (tag == 656) {
         parse_LastPxType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &lastpxtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(664)) goto parse_AuctionLastPx;
        break;
      }

      // optional int64 AuctionLastPx = 83;
      case 83: {
        if (tag == 664) {
         parse_AuctionLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &auctionlastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(672)) goto parse_AuctionVolumeTrade;
        break;
      }

      // optional int64 AuctionVolumeTrade = 84;
      case 84: {
        if (tag == 672) {
         parse_AuctionVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &auctionvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(680)) goto parse_AuctionValueTrade;
        break;
      }

      // optional int64 AuctionValueTrade = 85;
      case 85: {
        if (tag == 680) {
         parse_AuctionValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &auctionvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(688)) goto parse_USConsolidateVolume;
        break;
      }

      // optional int64 USConsolidateVolume = 86;
      case 86: {
        if (tag == 688) {
         parse_USConsolidateVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &usconsolidatevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(696)) goto parse_USCompositeClosePx;
        break;
      }

      // optional int64 USCompositeClosePx = 87;
      case 87: {
        if (tag == 696) {
         parse_USCompositeClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &uscompositeclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(706)) goto parse_TradingHaltReason;
        break;
      }

      // optional string TradingHaltReason = 88;
      case 88: {
        if (tag == 706) {
         parse_TradingHaltReason:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradinghaltreason()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradinghaltreason().data(), this->tradinghaltreason().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBond.TradingHaltReason"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(712)) goto parse_OtcTotalVolumeTrade;
        break;
      }

      // optional int64 OtcTotalVolumeTrade = 89;
      case 89: {
        if (tag == 712) {
         parse_OtcTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otctotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(720)) goto parse_OtcTotalValueTrade;
        break;
      }

      // optional int64 OtcTotalValueTrade = 90;
      case 90: {
        if (tag == 720) {
         parse_OtcTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otctotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(728)) goto parse_OtcNumTrades;
        break;
      }

      // optional int64 OtcNumTrades = 91;
      case 91: {
        if (tag == 728) {
         parse_OtcNumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otcnumtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(736)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 92;
      case 92: {
        if (tag == 736) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(744)) goto parse_ReferencePx;
        break;
      }

      // optional int64 ReferencePx = 93;
      case 93: {
        if (tag == 744) {
         parse_ReferencePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &referencepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(752)) goto parse_MaxBuyPrice;
        break;
      }

      // optional int64 MaxBuyPrice = 94;
      case 94: {
        if (tag == 752) {
         parse_MaxBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(760)) goto parse_MinBuyPrice;
        break;
      }

      // optional int64 MinBuyPrice = 95;
      case 95: {
        if (tag == 760) {
         parse_MinBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(768)) goto parse_MaxSellPrice;
        break;
      }

      // optional int64 MaxSellPrice = 96;
      case 96: {
        if (tag == 768) {
         parse_MaxSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(776)) goto parse_MinSellPrice;
        break;
      }

      // optional int64 MinSellPrice = 97;
      case 97: {
        if (tag == 776) {
         parse_MinSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_DelayType;
        break;
      }

      // optional int32 DelayType = 101;
      case 101: {
        if (tag == 808) {
         parse_DelayType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDBond)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDBond)
  return false;
#undef DO_
}

void MDBond::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDBond)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->diffpx1(), output);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->diffpx2(), output);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->totalbuyqty(), output);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->totalsellqty(), output);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->weightedavgbuypx(), output);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->weightedavgsellpx(), output);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->withdrawbuynumber(), output);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->withdrawbuyamount(), output);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->withdrawbuymoney(), output);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->withdrawsellnumber(), output);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->withdrawsellamount(), output);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->withdrawsellmoney(), output);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->totalbuynumber(), output);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->totalsellnumber(), output);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->buytrademaxduration(), output);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->selltrademaxduration(), output);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(35, this->numbuyorders(), output);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(36, this->numsellorders(), output);
  }

  // optional int64 YieldToMaturity = 37;
  if (this->yieldtomaturity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(37, this->yieldtomaturity(), output);
  }

  // optional int64 WeightedAvgPx = 38;
  if (this->weightedavgpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(38, this->weightedavgpx(), output);
  }

  // optional int64 WeightedAvgPxBP = 39;
  if (this->weightedavgpxbp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(39, this->weightedavgpxbp(), output);
  }

  // optional int64 PreCloseWeightedAvgPx = 40;
  if (this->precloseweightedavgpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(40, this->precloseweightedavgpx(), output);
  }

  // optional int32 ExchangeDate = 41;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(41, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 42;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(42, this->exchangetime(), output);
  }

  // optional int64 PreCloseYield = 43;
  if (this->precloseyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(43, this->precloseyield(), output);
  }

  // optional int64 PreWeightedAvgYield = 44;
  if (this->preweightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(44, this->preweightedavgyield(), output);
  }

  // optional int64 OpenYield = 45;
  if (this->openyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(45, this->openyield(), output);
  }

  // optional int64 HighYield = 46;
  if (this->highyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(46, this->highyield(), output);
  }

  // optional int64 LowYield = 47;
  if (this->lowyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(47, this->lowyield(), output);
  }

  // optional int64 LastYield = 48;
  if (this->lastyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(48, this->lastyield(), output);
  }

  // optional int64 WeightedAvgYield = 49;
  if (this->weightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(49, this->weightedavgyield(), output);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(50, this->channelno(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(59, this->norminalpx(), output);
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(60, this->shortsellsharestraded(), output);
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(61, this->shortsellturnover(), output);
  }

  // repeated int32 BuySettlTypeQueue = 62 [packed = true];
  if (this->buysettltypequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(62, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buysettltypequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buysettltypequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->buysettltypequeue(i), output);
  }

  // repeated int32 SellSettlTypeQueue = 63 [packed = true];
  if (this->sellsettltypequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(63, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellsettltypequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellsettltypequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->sellsettltypequeue(i), output);
  }

  // repeated int64 BuyYieldQueue = 64 [packed = true];
  if (this->buyyieldqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(64, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyyieldqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyyieldqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyyieldqueue(i), output);
  }

  // repeated int64 SellYieldQueue = 65 [packed = true];
  if (this->sellyieldqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(65, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellyieldqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellyieldqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellyieldqueue(i), output);
  }

  // optional int64 PreMarketLastPx = 66;
  if (this->premarketlastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(66, this->premarketlastpx(), output);
  }

  // optional int64 PreMarketTotalVolumeTrade = 67;
  if (this->premarkettotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(67, this->premarkettotalvolumetrade(), output);
  }

  // optional int64 PreMarketTotalValueTrade = 68;
  if (this->premarkettotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(68, this->premarkettotalvaluetrade(), output);
  }

  // optional int64 PreMarketHighPx = 69;
  if (this->premarkethighpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(69, this->premarkethighpx(), output);
  }

  // optional int64 PreMarketLowPx = 70;
  if (this->premarketlowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(70, this->premarketlowpx(), output);
  }

  // optional int64 AfterHoursLastPx = 71;
  if (this->afterhourslastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(71, this->afterhourslastpx(), output);
  }

  // optional int64 AfterHoursTotalVolumeTrade = 72;
  if (this->afterhourstotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(72, this->afterhourstotalvolumetrade(), output);
  }

  // optional int64 AfterHoursTotalValueTrade = 73;
  if (this->afterhourstotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(73, this->afterhourstotalvaluetrade(), output);
  }

  // optional int64 AfterHoursHighPx = 74;
  if (this->afterhourshighpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(74, this->afterhourshighpx(), output);
  }

  // optional int64 AfterHoursLowPx = 75;
  if (this->afterhourslowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(75, this->afterhourslowpx(), output);
  }

  // optional string MarketPhaseCode = 76;
  if (this->marketphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketphasecode().data(), this->marketphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.MarketPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      76, this->marketphasecode(), output);
  }

  // optional string SubTradingPhaseCode1 = 77;
  if (this->subtradingphasecode1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode1().data(), this->subtradingphasecode1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      77, this->subtradingphasecode1(), output);
  }

  // optional string SubTradingPhaseCode2 = 78;
  if (this->subtradingphasecode2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode2().data(), this->subtradingphasecode2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      78, this->subtradingphasecode2(), output);
  }

  // optional string SubTradingPhaseCode3 = 79;
  if (this->subtradingphasecode3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode3().data(), this->subtradingphasecode3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      79, this->subtradingphasecode3(), output);
  }

  // optional string SubTradingPhaseCode4 = 80;
  if (this->subtradingphasecode4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode4().data(), this->subtradingphasecode4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      80, this->subtradingphasecode4(), output);
  }

  // optional string SubTradingPhaseCode5 = 81;
  if (this->subtradingphasecode5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode5().data(), this->subtradingphasecode5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      81, this->subtradingphasecode5(), output);
  }

  // optional int32 LastPxType = 82;
  if (this->lastpxtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(82, this->lastpxtype(), output);
  }

  // optional int64 AuctionLastPx = 83;
  if (this->auctionlastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(83, this->auctionlastpx(), output);
  }

  // optional int64 AuctionVolumeTrade = 84;
  if (this->auctionvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(84, this->auctionvolumetrade(), output);
  }

  // optional int64 AuctionValueTrade = 85;
  if (this->auctionvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(85, this->auctionvaluetrade(), output);
  }

  // optional int64 USConsolidateVolume = 86;
  if (this->usconsolidatevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(86, this->usconsolidatevolume(), output);
  }

  // optional int64 USCompositeClosePx = 87;
  if (this->uscompositeclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(87, this->uscompositeclosepx(), output);
  }

  // optional string TradingHaltReason = 88;
  if (this->tradinghaltreason().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradinghaltreason().data(), this->tradinghaltreason().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.TradingHaltReason");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      88, this->tradinghaltreason(), output);
  }

  // optional int64 OtcTotalVolumeTrade = 89;
  if (this->otctotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(89, this->otctotalvolumetrade(), output);
  }

  // optional int64 OtcTotalValueTrade = 90;
  if (this->otctotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(90, this->otctotalvaluetrade(), output);
  }

  // optional int64 OtcNumTrades = 91;
  if (this->otcnumtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(91, this->otcnumtrades(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 92;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(92, this->datamultiplepowerof10(), output);
  }

  // optional int64 ReferencePx = 93;
  if (this->referencepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(93, this->referencepx(), output);
  }

  // optional int64 MaxBuyPrice = 94;
  if (this->maxbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(94, this->maxbuyprice(), output);
  }

  // optional int64 MinBuyPrice = 95;
  if (this->minbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(95, this->minbuyprice(), output);
  }

  // optional int64 MaxSellPrice = 96;
  if (this->maxsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(96, this->maxsellprice(), output);
  }

  // optional int64 MinSellPrice = 97;
  if (this->minsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(97, this->minsellprice(), output);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(101, this->delaytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDBond)
}

::google::protobuf::uint8* MDBond::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDBond)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->diffpx1(), target);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->diffpx2(), target);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->totalbuyqty(), target);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->totalsellqty(), target);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->weightedavgbuypx(), target);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->weightedavgsellpx(), target);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->withdrawbuynumber(), target);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->withdrawbuyamount(), target);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->withdrawbuymoney(), target);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->withdrawsellnumber(), target);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->withdrawsellamount(), target);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->withdrawsellmoney(), target);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->totalbuynumber(), target);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->totalsellnumber(), target);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->buytrademaxduration(), target);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->selltrademaxduration(), target);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(35, this->numbuyorders(), target);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(36, this->numsellorders(), target);
  }

  // optional int64 YieldToMaturity = 37;
  if (this->yieldtomaturity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(37, this->yieldtomaturity(), target);
  }

  // optional int64 WeightedAvgPx = 38;
  if (this->weightedavgpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(38, this->weightedavgpx(), target);
  }

  // optional int64 WeightedAvgPxBP = 39;
  if (this->weightedavgpxbp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(39, this->weightedavgpxbp(), target);
  }

  // optional int64 PreCloseWeightedAvgPx = 40;
  if (this->precloseweightedavgpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(40, this->precloseweightedavgpx(), target);
  }

  // optional int32 ExchangeDate = 41;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(41, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 42;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(42, this->exchangetime(), target);
  }

  // optional int64 PreCloseYield = 43;
  if (this->precloseyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(43, this->precloseyield(), target);
  }

  // optional int64 PreWeightedAvgYield = 44;
  if (this->preweightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(44, this->preweightedavgyield(), target);
  }

  // optional int64 OpenYield = 45;
  if (this->openyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(45, this->openyield(), target);
  }

  // optional int64 HighYield = 46;
  if (this->highyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(46, this->highyield(), target);
  }

  // optional int64 LowYield = 47;
  if (this->lowyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(47, this->lowyield(), target);
  }

  // optional int64 LastYield = 48;
  if (this->lastyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(48, this->lastyield(), target);
  }

  // optional int64 WeightedAvgYield = 49;
  if (this->weightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(49, this->weightedavgyield(), target);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(50, this->channelno(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(59, this->norminalpx(), target);
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(60, this->shortsellsharestraded(), target);
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(61, this->shortsellturnover(), target);
  }

  // repeated int32 BuySettlTypeQueue = 62 [packed = true];
  if (this->buysettltypequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      62,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buysettltypequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buysettltypequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->buysettltypequeue(i), target);
  }

  // repeated int32 SellSettlTypeQueue = 63 [packed = true];
  if (this->sellsettltypequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      63,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellsettltypequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellsettltypequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->sellsettltypequeue(i), target);
  }

  // repeated int64 BuyYieldQueue = 64 [packed = true];
  if (this->buyyieldqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      64,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyyieldqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyyieldqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyyieldqueue(i), target);
  }

  // repeated int64 SellYieldQueue = 65 [packed = true];
  if (this->sellyieldqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      65,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellyieldqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellyieldqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellyieldqueue(i), target);
  }

  // optional int64 PreMarketLastPx = 66;
  if (this->premarketlastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(66, this->premarketlastpx(), target);
  }

  // optional int64 PreMarketTotalVolumeTrade = 67;
  if (this->premarkettotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(67, this->premarkettotalvolumetrade(), target);
  }

  // optional int64 PreMarketTotalValueTrade = 68;
  if (this->premarkettotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(68, this->premarkettotalvaluetrade(), target);
  }

  // optional int64 PreMarketHighPx = 69;
  if (this->premarkethighpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(69, this->premarkethighpx(), target);
  }

  // optional int64 PreMarketLowPx = 70;
  if (this->premarketlowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(70, this->premarketlowpx(), target);
  }

  // optional int64 AfterHoursLastPx = 71;
  if (this->afterhourslastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(71, this->afterhourslastpx(), target);
  }

  // optional int64 AfterHoursTotalVolumeTrade = 72;
  if (this->afterhourstotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(72, this->afterhourstotalvolumetrade(), target);
  }

  // optional int64 AfterHoursTotalValueTrade = 73;
  if (this->afterhourstotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(73, this->afterhourstotalvaluetrade(), target);
  }

  // optional int64 AfterHoursHighPx = 74;
  if (this->afterhourshighpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(74, this->afterhourshighpx(), target);
  }

  // optional int64 AfterHoursLowPx = 75;
  if (this->afterhourslowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(75, this->afterhourslowpx(), target);
  }

  // optional string MarketPhaseCode = 76;
  if (this->marketphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketphasecode().data(), this->marketphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.MarketPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        76, this->marketphasecode(), target);
  }

  // optional string SubTradingPhaseCode1 = 77;
  if (this->subtradingphasecode1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode1().data(), this->subtradingphasecode1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        77, this->subtradingphasecode1(), target);
  }

  // optional string SubTradingPhaseCode2 = 78;
  if (this->subtradingphasecode2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode2().data(), this->subtradingphasecode2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        78, this->subtradingphasecode2(), target);
  }

  // optional string SubTradingPhaseCode3 = 79;
  if (this->subtradingphasecode3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode3().data(), this->subtradingphasecode3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        79, this->subtradingphasecode3(), target);
  }

  // optional string SubTradingPhaseCode4 = 80;
  if (this->subtradingphasecode4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode4().data(), this->subtradingphasecode4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        80, this->subtradingphasecode4(), target);
  }

  // optional string SubTradingPhaseCode5 = 81;
  if (this->subtradingphasecode5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->subtradingphasecode5().data(), this->subtradingphasecode5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        81, this->subtradingphasecode5(), target);
  }

  // optional int32 LastPxType = 82;
  if (this->lastpxtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(82, this->lastpxtype(), target);
  }

  // optional int64 AuctionLastPx = 83;
  if (this->auctionlastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(83, this->auctionlastpx(), target);
  }

  // optional int64 AuctionVolumeTrade = 84;
  if (this->auctionvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(84, this->auctionvolumetrade(), target);
  }

  // optional int64 AuctionValueTrade = 85;
  if (this->auctionvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(85, this->auctionvaluetrade(), target);
  }

  // optional int64 USConsolidateVolume = 86;
  if (this->usconsolidatevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(86, this->usconsolidatevolume(), target);
  }

  // optional int64 USCompositeClosePx = 87;
  if (this->uscompositeclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(87, this->uscompositeclosepx(), target);
  }

  // optional string TradingHaltReason = 88;
  if (this->tradinghaltreason().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradinghaltreason().data(), this->tradinghaltreason().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBond.TradingHaltReason");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        88, this->tradinghaltreason(), target);
  }

  // optional int64 OtcTotalVolumeTrade = 89;
  if (this->otctotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(89, this->otctotalvolumetrade(), target);
  }

  // optional int64 OtcTotalValueTrade = 90;
  if (this->otctotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(90, this->otctotalvaluetrade(), target);
  }

  // optional int64 OtcNumTrades = 91;
  if (this->otcnumtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(91, this->otcnumtrades(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 92;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(92, this->datamultiplepowerof10(), target);
  }

  // optional int64 ReferencePx = 93;
  if (this->referencepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(93, this->referencepx(), target);
  }

  // optional int64 MaxBuyPrice = 94;
  if (this->maxbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(94, this->maxbuyprice(), target);
  }

  // optional int64 MinBuyPrice = 95;
  if (this->minbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(95, this->minbuyprice(), target);
  }

  // optional int64 MaxSellPrice = 96;
  if (this->maxsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(96, this->maxsellprice(), target);
  }

  // optional int64 MinSellPrice = 97;
  if (this->minsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(97, this->minsellprice(), target);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(101, this->delaytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDBond)
  return target;
}

size_t MDBond::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDBond)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx1());
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx2());
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuyqty());
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellqty());
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgbuypx());
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgsellpx());
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuynumber());
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuyamount());
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuymoney());
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellnumber());
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellamount());
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellmoney());
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuynumber());
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellnumber());
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buytrademaxduration());
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->selltrademaxduration());
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numbuyorders());
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numsellorders());
  }

  // optional int64 YieldToMaturity = 37;
  if (this->yieldtomaturity() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->yieldtomaturity());
  }

  // optional int64 WeightedAvgPx = 38;
  if (this->weightedavgpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgpx());
  }

  // optional int64 WeightedAvgPxBP = 39;
  if (this->weightedavgpxbp() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgpxbp());
  }

  // optional int64 PreCloseWeightedAvgPx = 40;
  if (this->precloseweightedavgpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloseweightedavgpx());
  }

  // optional int32 ExchangeDate = 41;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 42;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 PreCloseYield = 43;
  if (this->precloseyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloseyield());
  }

  // optional int64 PreWeightedAvgYield = 44;
  if (this->preweightedavgyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedavgyield());
  }

  // optional int64 OpenYield = 45;
  if (this->openyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openyield());
  }

  // optional int64 HighYield = 46;
  if (this->highyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highyield());
  }

  // optional int64 LowYield = 47;
  if (this->lowyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowyield());
  }

  // optional int64 LastYield = 48;
  if (this->lastyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastyield());
  }

  // optional int64 WeightedAvgYield = 49;
  if (this->weightedavgyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgyield());
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->norminalpx());
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->shortsellsharestraded());
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->shortsellturnover());
  }

  // optional int64 PreMarketLastPx = 66;
  if (this->premarketlastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarketlastpx());
  }

  // optional int64 PreMarketTotalVolumeTrade = 67;
  if (this->premarkettotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkettotalvolumetrade());
  }

  // optional int64 PreMarketTotalValueTrade = 68;
  if (this->premarkettotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkettotalvaluetrade());
  }

  // optional int64 PreMarketHighPx = 69;
  if (this->premarkethighpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkethighpx());
  }

  // optional int64 PreMarketLowPx = 70;
  if (this->premarketlowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarketlowpx());
  }

  // optional int64 AfterHoursLastPx = 71;
  if (this->afterhourslastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourslastpx());
  }

  // optional int64 AfterHoursTotalVolumeTrade = 72;
  if (this->afterhourstotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourstotalvolumetrade());
  }

  // optional int64 AfterHoursTotalValueTrade = 73;
  if (this->afterhourstotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourstotalvaluetrade());
  }

  // optional int64 AfterHoursHighPx = 74;
  if (this->afterhourshighpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourshighpx());
  }

  // optional int64 AfterHoursLowPx = 75;
  if (this->afterhourslowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourslowpx());
  }

  // optional string MarketPhaseCode = 76;
  if (this->marketphasecode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketphasecode());
  }

  // optional string SubTradingPhaseCode1 = 77;
  if (this->subtradingphasecode1().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->subtradingphasecode1());
  }

  // optional string SubTradingPhaseCode2 = 78;
  if (this->subtradingphasecode2().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->subtradingphasecode2());
  }

  // optional string SubTradingPhaseCode3 = 79;
  if (this->subtradingphasecode3().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->subtradingphasecode3());
  }

  // optional string SubTradingPhaseCode4 = 80;
  if (this->subtradingphasecode4().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->subtradingphasecode4());
  }

  // optional string SubTradingPhaseCode5 = 81;
  if (this->subtradingphasecode5().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->subtradingphasecode5());
  }

  // optional int32 LastPxType = 82;
  if (this->lastpxtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->lastpxtype());
  }

  // optional int64 AuctionLastPx = 83;
  if (this->auctionlastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->auctionlastpx());
  }

  // optional int64 AuctionVolumeTrade = 84;
  if (this->auctionvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->auctionvolumetrade());
  }

  // optional int64 AuctionValueTrade = 85;
  if (this->auctionvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->auctionvaluetrade());
  }

  // optional int64 USConsolidateVolume = 86;
  if (this->usconsolidatevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->usconsolidatevolume());
  }

  // optional int64 USCompositeClosePx = 87;
  if (this->uscompositeclosepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->uscompositeclosepx());
  }

  // optional string TradingHaltReason = 88;
  if (this->tradinghaltreason().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradinghaltreason());
  }

  // optional int64 OtcTotalVolumeTrade = 89;
  if (this->otctotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otctotalvolumetrade());
  }

  // optional int64 OtcTotalValueTrade = 90;
  if (this->otctotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otctotalvaluetrade());
  }

  // optional int64 OtcNumTrades = 91;
  if (this->otcnumtrades() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otcnumtrades());
  }

  // optional int32 DataMultiplePowerOf10 = 92;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 ReferencePx = 93;
  if (this->referencepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->referencepx());
  }

  // optional int64 MaxBuyPrice = 94;
  if (this->maxbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxbuyprice());
  }

  // optional int64 MinBuyPrice = 95;
  if (this->minbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minbuyprice());
  }

  // optional int64 MaxSellPrice = 96;
  if (this->maxsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxsellprice());
  }

  // optional int64 MinSellPrice = 97;
  if (this->minsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minsellprice());
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaytype());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 BuySettlTypeQueue = 62 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buysettltypequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->buysettltypequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buysettltypequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int32 SellSettlTypeQueue = 63 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellsettltypequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->sellsettltypequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellsettltypequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyYieldQueue = 64 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyyieldqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyyieldqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyyieldqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellYieldQueue = 65 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellyieldqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellyieldqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellyieldqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDBond::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDBond)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDBond* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDBond>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDBond)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDBond)
    UnsafeMergeFrom(*source);
  }
}

void MDBond::MergeFrom(const MDBond& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDBond)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDBond::UnsafeMergeFrom(const MDBond& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  buysettltypequeue_.UnsafeMergeFrom(from.buysettltypequeue_);
  sellsettltypequeue_.UnsafeMergeFrom(from.sellsettltypequeue_);
  buyyieldqueue_.UnsafeMergeFrom(from.buyyieldqueue_);
  sellyieldqueue_.UnsafeMergeFrom(from.sellyieldqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.diffpx1() != 0) {
    set_diffpx1(from.diffpx1());
  }
  if (from.diffpx2() != 0) {
    set_diffpx2(from.diffpx2());
  }
  if (from.totalbuyqty() != 0) {
    set_totalbuyqty(from.totalbuyqty());
  }
  if (from.totalsellqty() != 0) {
    set_totalsellqty(from.totalsellqty());
  }
  if (from.weightedavgbuypx() != 0) {
    set_weightedavgbuypx(from.weightedavgbuypx());
  }
  if (from.weightedavgsellpx() != 0) {
    set_weightedavgsellpx(from.weightedavgsellpx());
  }
  if (from.withdrawbuynumber() != 0) {
    set_withdrawbuynumber(from.withdrawbuynumber());
  }
  if (from.withdrawbuyamount() != 0) {
    set_withdrawbuyamount(from.withdrawbuyamount());
  }
  if (from.withdrawbuymoney() != 0) {
    set_withdrawbuymoney(from.withdrawbuymoney());
  }
  if (from.withdrawsellnumber() != 0) {
    set_withdrawsellnumber(from.withdrawsellnumber());
  }
  if (from.withdrawsellamount() != 0) {
    set_withdrawsellamount(from.withdrawsellamount());
  }
  if (from.withdrawsellmoney() != 0) {
    set_withdrawsellmoney(from.withdrawsellmoney());
  }
  if (from.totalbuynumber() != 0) {
    set_totalbuynumber(from.totalbuynumber());
  }
  if (from.totalsellnumber() != 0) {
    set_totalsellnumber(from.totalsellnumber());
  }
  if (from.buytrademaxduration() != 0) {
    set_buytrademaxduration(from.buytrademaxduration());
  }
  if (from.selltrademaxduration() != 0) {
    set_selltrademaxduration(from.selltrademaxduration());
  }
  if (from.numbuyorders() != 0) {
    set_numbuyorders(from.numbuyorders());
  }
  if (from.numsellorders() != 0) {
    set_numsellorders(from.numsellorders());
  }
  if (from.yieldtomaturity() != 0) {
    set_yieldtomaturity(from.yieldtomaturity());
  }
  if (from.weightedavgpx() != 0) {
    set_weightedavgpx(from.weightedavgpx());
  }
  if (from.weightedavgpxbp() != 0) {
    set_weightedavgpxbp(from.weightedavgpxbp());
  }
  if (from.precloseweightedavgpx() != 0) {
    set_precloseweightedavgpx(from.precloseweightedavgpx());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.precloseyield() != 0) {
    set_precloseyield(from.precloseyield());
  }
  if (from.preweightedavgyield() != 0) {
    set_preweightedavgyield(from.preweightedavgyield());
  }
  if (from.openyield() != 0) {
    set_openyield(from.openyield());
  }
  if (from.highyield() != 0) {
    set_highyield(from.highyield());
  }
  if (from.lowyield() != 0) {
    set_lowyield(from.lowyield());
  }
  if (from.lastyield() != 0) {
    set_lastyield(from.lastyield());
  }
  if (from.weightedavgyield() != 0) {
    set_weightedavgyield(from.weightedavgyield());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.norminalpx() != 0) {
    set_norminalpx(from.norminalpx());
  }
  if (from.shortsellsharestraded() != 0) {
    set_shortsellsharestraded(from.shortsellsharestraded());
  }
  if (from.shortsellturnover() != 0) {
    set_shortsellturnover(from.shortsellturnover());
  }
  if (from.premarketlastpx() != 0) {
    set_premarketlastpx(from.premarketlastpx());
  }
  if (from.premarkettotalvolumetrade() != 0) {
    set_premarkettotalvolumetrade(from.premarkettotalvolumetrade());
  }
  if (from.premarkettotalvaluetrade() != 0) {
    set_premarkettotalvaluetrade(from.premarkettotalvaluetrade());
  }
  if (from.premarkethighpx() != 0) {
    set_premarkethighpx(from.premarkethighpx());
  }
  if (from.premarketlowpx() != 0) {
    set_premarketlowpx(from.premarketlowpx());
  }
  if (from.afterhourslastpx() != 0) {
    set_afterhourslastpx(from.afterhourslastpx());
  }
  if (from.afterhourstotalvolumetrade() != 0) {
    set_afterhourstotalvolumetrade(from.afterhourstotalvolumetrade());
  }
  if (from.afterhourstotalvaluetrade() != 0) {
    set_afterhourstotalvaluetrade(from.afterhourstotalvaluetrade());
  }
  if (from.afterhourshighpx() != 0) {
    set_afterhourshighpx(from.afterhourshighpx());
  }
  if (from.afterhourslowpx() != 0) {
    set_afterhourslowpx(from.afterhourslowpx());
  }
  if (from.marketphasecode().size() > 0) {

    marketphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketphasecode_);
  }
  if (from.subtradingphasecode1().size() > 0) {

    subtradingphasecode1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.subtradingphasecode1_);
  }
  if (from.subtradingphasecode2().size() > 0) {

    subtradingphasecode2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.subtradingphasecode2_);
  }
  if (from.subtradingphasecode3().size() > 0) {

    subtradingphasecode3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.subtradingphasecode3_);
  }
  if (from.subtradingphasecode4().size() > 0) {

    subtradingphasecode4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.subtradingphasecode4_);
  }
  if (from.subtradingphasecode5().size() > 0) {

    subtradingphasecode5_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.subtradingphasecode5_);
  }
  if (from.lastpxtype() != 0) {
    set_lastpxtype(from.lastpxtype());
  }
  if (from.auctionlastpx() != 0) {
    set_auctionlastpx(from.auctionlastpx());
  }
  if (from.auctionvolumetrade() != 0) {
    set_auctionvolumetrade(from.auctionvolumetrade());
  }
  if (from.auctionvaluetrade() != 0) {
    set_auctionvaluetrade(from.auctionvaluetrade());
  }
  if (from.usconsolidatevolume() != 0) {
    set_usconsolidatevolume(from.usconsolidatevolume());
  }
  if (from.uscompositeclosepx() != 0) {
    set_uscompositeclosepx(from.uscompositeclosepx());
  }
  if (from.tradinghaltreason().size() > 0) {

    tradinghaltreason_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradinghaltreason_);
  }
  if (from.otctotalvolumetrade() != 0) {
    set_otctotalvolumetrade(from.otctotalvolumetrade());
  }
  if (from.otctotalvaluetrade() != 0) {
    set_otctotalvaluetrade(from.otctotalvaluetrade());
  }
  if (from.otcnumtrades() != 0) {
    set_otcnumtrades(from.otcnumtrades());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.referencepx() != 0) {
    set_referencepx(from.referencepx());
  }
  if (from.maxbuyprice() != 0) {
    set_maxbuyprice(from.maxbuyprice());
  }
  if (from.minbuyprice() != 0) {
    set_minbuyprice(from.minbuyprice());
  }
  if (from.maxsellprice() != 0) {
    set_maxsellprice(from.maxsellprice());
  }
  if (from.minsellprice() != 0) {
    set_minsellprice(from.minsellprice());
  }
  if (from.delaytype() != 0) {
    set_delaytype(from.delaytype());
  }
}

void MDBond::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDBond)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDBond::CopyFrom(const MDBond& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDBond)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDBond::IsInitialized() const {

  return true;
}

void MDBond::Swap(MDBond* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDBond::InternalSwap(MDBond* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(diffpx1_, other->diffpx1_);
  std::swap(diffpx2_, other->diffpx2_);
  std::swap(totalbuyqty_, other->totalbuyqty_);
  std::swap(totalsellqty_, other->totalsellqty_);
  std::swap(weightedavgbuypx_, other->weightedavgbuypx_);
  std::swap(weightedavgsellpx_, other->weightedavgsellpx_);
  std::swap(withdrawbuynumber_, other->withdrawbuynumber_);
  std::swap(withdrawbuyamount_, other->withdrawbuyamount_);
  std::swap(withdrawbuymoney_, other->withdrawbuymoney_);
  std::swap(withdrawsellnumber_, other->withdrawsellnumber_);
  std::swap(withdrawsellamount_, other->withdrawsellamount_);
  std::swap(withdrawsellmoney_, other->withdrawsellmoney_);
  std::swap(totalbuynumber_, other->totalbuynumber_);
  std::swap(totalsellnumber_, other->totalsellnumber_);
  std::swap(buytrademaxduration_, other->buytrademaxduration_);
  std::swap(selltrademaxduration_, other->selltrademaxduration_);
  std::swap(numbuyorders_, other->numbuyorders_);
  std::swap(numsellorders_, other->numsellorders_);
  std::swap(yieldtomaturity_, other->yieldtomaturity_);
  std::swap(weightedavgpx_, other->weightedavgpx_);
  std::swap(weightedavgpxbp_, other->weightedavgpxbp_);
  std::swap(precloseweightedavgpx_, other->precloseweightedavgpx_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(precloseyield_, other->precloseyield_);
  std::swap(preweightedavgyield_, other->preweightedavgyield_);
  std::swap(openyield_, other->openyield_);
  std::swap(highyield_, other->highyield_);
  std::swap(lowyield_, other->lowyield_);
  std::swap(lastyield_, other->lastyield_);
  std::swap(weightedavgyield_, other->weightedavgyield_);
  std::swap(channelno_, other->channelno_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(norminalpx_, other->norminalpx_);
  std::swap(shortsellsharestraded_, other->shortsellsharestraded_);
  std::swap(shortsellturnover_, other->shortsellturnover_);
  buysettltypequeue_.UnsafeArenaSwap(&other->buysettltypequeue_);
  sellsettltypequeue_.UnsafeArenaSwap(&other->sellsettltypequeue_);
  buyyieldqueue_.UnsafeArenaSwap(&other->buyyieldqueue_);
  sellyieldqueue_.UnsafeArenaSwap(&other->sellyieldqueue_);
  std::swap(premarketlastpx_, other->premarketlastpx_);
  std::swap(premarkettotalvolumetrade_, other->premarkettotalvolumetrade_);
  std::swap(premarkettotalvaluetrade_, other->premarkettotalvaluetrade_);
  std::swap(premarkethighpx_, other->premarkethighpx_);
  std::swap(premarketlowpx_, other->premarketlowpx_);
  std::swap(afterhourslastpx_, other->afterhourslastpx_);
  std::swap(afterhourstotalvolumetrade_, other->afterhourstotalvolumetrade_);
  std::swap(afterhourstotalvaluetrade_, other->afterhourstotalvaluetrade_);
  std::swap(afterhourshighpx_, other->afterhourshighpx_);
  std::swap(afterhourslowpx_, other->afterhourslowpx_);
  marketphasecode_.Swap(&other->marketphasecode_);
  subtradingphasecode1_.Swap(&other->subtradingphasecode1_);
  subtradingphasecode2_.Swap(&other->subtradingphasecode2_);
  subtradingphasecode3_.Swap(&other->subtradingphasecode3_);
  subtradingphasecode4_.Swap(&other->subtradingphasecode4_);
  subtradingphasecode5_.Swap(&other->subtradingphasecode5_);
  std::swap(lastpxtype_, other->lastpxtype_);
  std::swap(auctionlastpx_, other->auctionlastpx_);
  std::swap(auctionvolumetrade_, other->auctionvolumetrade_);
  std::swap(auctionvaluetrade_, other->auctionvaluetrade_);
  std::swap(usconsolidatevolume_, other->usconsolidatevolume_);
  std::swap(uscompositeclosepx_, other->uscompositeclosepx_);
  tradinghaltreason_.Swap(&other->tradinghaltreason_);
  std::swap(otctotalvolumetrade_, other->otctotalvolumetrade_);
  std::swap(otctotalvaluetrade_, other->otctotalvaluetrade_);
  std::swap(otcnumtrades_, other->otcnumtrades_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(referencepx_, other->referencepx_);
  std::swap(maxbuyprice_, other->maxbuyprice_);
  std::swap(minbuyprice_, other->minbuyprice_);
  std::swap(maxsellprice_, other->maxsellprice_);
  std::swap(minsellprice_, other->minsellprice_);
  std::swap(delaytype_, other->delaytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDBond::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDBond_descriptor_;
  metadata.reflection = MDBond_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDBond

// optional string HTSCSecurityID = 1;
void MDBond::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
void MDBond::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
void MDBond::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
::std::string* MDBond::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDBond::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDBond::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MDDate)
  return mddate_;
}
void MDBond::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MDDate)
}

// optional int32 MDTime = 3;
void MDBond::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDBond::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MDTime)
  return mdtime_;
}
void MDBond::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDBond::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DataTimestamp)
  return datatimestamp_;
}
void MDBond::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDBond::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
void MDBond::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
void MDBond::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
::std::string* MDBond::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDBond::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDBond::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDBond::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDBond::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDBond::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDBond::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.securityType)
}

// optional int64 MaxPx = 8;
void MDBond::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxPx)
  return maxpx_;
}
void MDBond::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxPx)
}

// optional int64 MinPx = 9;
void MDBond::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinPx)
  return minpx_;
}
void MDBond::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinPx)
}

// optional int64 PreClosePx = 10;
void MDBond::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreClosePx)
  return preclosepx_;
}
void MDBond::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDBond::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumTrades)
  return numtrades_;
}
void MDBond::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDBond::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDBond::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDBond::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalValueTrade)
  return totalvaluetrade_;
}
void MDBond::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDBond::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastPx)
  return lastpx_;
}
void MDBond::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastPx)
}

// optional int64 OpenPx = 15;
void MDBond::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OpenPx)
  return openpx_;
}
void MDBond::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OpenPx)
}

// optional int64 ClosePx = 16;
void MDBond::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ClosePx)
  return closepx_;
}
void MDBond::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ClosePx)
}

// optional int64 HighPx = 17;
void MDBond::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HighPx)
  return highpx_;
}
void MDBond::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HighPx)
}

// optional int64 LowPx = 18;
void MDBond::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LowPx)
  return lowpx_;
}
void MDBond::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LowPx)
}

// optional int64 DiffPx1 = 19;
void MDBond::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DiffPx1)
  return diffpx1_;
}
void MDBond::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DiffPx1)
}

// optional int64 DiffPx2 = 20;
void MDBond::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DiffPx2)
  return diffpx2_;
}
void MDBond::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
void MDBond::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalBuyQty)
  return totalbuyqty_;
}
void MDBond::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
void MDBond::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalSellQty)
  return totalsellqty_;
}
void MDBond::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
void MDBond::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
void MDBond::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
void MDBond::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
void MDBond::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
void MDBond::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
void MDBond::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
void MDBond::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
void MDBond::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
void MDBond::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
void MDBond::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
void MDBond::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellNumber)
  return withdrawsellnumber_;
}
void MDBond::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
void MDBond::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellAmount)
  return withdrawsellamount_;
}
void MDBond::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
void MDBond::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellMoney)
  return withdrawsellmoney_;
}
void MDBond::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
void MDBond::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalBuyNumber)
  return totalbuynumber_;
}
void MDBond::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
void MDBond::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalSellNumber)
  return totalsellnumber_;
}
void MDBond::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
void MDBond::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
void MDBond::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
void MDBond::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellTradeMaxDuration)
  return selltrademaxduration_;
}
void MDBond::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
void MDBond::clear_numbuyorders() {
  numbuyorders_ = 0;
}
::google::protobuf::int32 MDBond::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumBuyOrders)
  return numbuyorders_;
}
void MDBond::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
void MDBond::clear_numsellorders() {
  numsellorders_ = 0;
}
::google::protobuf::int32 MDBond::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumSellOrders)
  return numsellorders_;
}
void MDBond::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumSellOrders)
}

// optional int64 YieldToMaturity = 37;
void MDBond::clear_yieldtomaturity() {
  yieldtomaturity_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::yieldtomaturity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.YieldToMaturity)
  return yieldtomaturity_;
}
void MDBond::set_yieldtomaturity(::google::protobuf::int64 value) {
  
  yieldtomaturity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.YieldToMaturity)
}

// optional int64 WeightedAvgPx = 38;
void MDBond::clear_weightedavgpx() {
  weightedavgpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::weightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgPx)
  return weightedavgpx_;
}
void MDBond::set_weightedavgpx(::google::protobuf::int64 value) {
  
  weightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgPx)
}

// optional int64 WeightedAvgPxBP = 39;
void MDBond::clear_weightedavgpxbp() {
  weightedavgpxbp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::weightedavgpxbp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgPxBP)
  return weightedavgpxbp_;
}
void MDBond::set_weightedavgpxbp(::google::protobuf::int64 value) {
  
  weightedavgpxbp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgPxBP)
}

// optional int64 PreCloseWeightedAvgPx = 40;
void MDBond::clear_precloseweightedavgpx() {
  precloseweightedavgpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::precloseweightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreCloseWeightedAvgPx)
  return precloseweightedavgpx_;
}
void MDBond::set_precloseweightedavgpx(::google::protobuf::int64 value) {
  
  precloseweightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreCloseWeightedAvgPx)
}

// optional int32 ExchangeDate = 41;
void MDBond::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDBond::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ExchangeDate)
  return exchangedate_;
}
void MDBond::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ExchangeDate)
}

// optional int32 ExchangeTime = 42;
void MDBond::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDBond::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ExchangeTime)
  return exchangetime_;
}
void MDBond::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ExchangeTime)
}

// optional int64 PreCloseYield = 43;
void MDBond::clear_precloseyield() {
  precloseyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreCloseYield)
  return precloseyield_;
}
void MDBond::set_precloseyield(::google::protobuf::int64 value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreCloseYield)
}

// optional int64 PreWeightedAvgYield = 44;
void MDBond::clear_preweightedavgyield() {
  preweightedavgyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreWeightedAvgYield)
  return preweightedavgyield_;
}
void MDBond::set_preweightedavgyield(::google::protobuf::int64 value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreWeightedAvgYield)
}

// optional int64 OpenYield = 45;
void MDBond::clear_openyield() {
  openyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OpenYield)
  return openyield_;
}
void MDBond::set_openyield(::google::protobuf::int64 value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OpenYield)
}

// optional int64 HighYield = 46;
void MDBond::clear_highyield() {
  highyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HighYield)
  return highyield_;
}
void MDBond::set_highyield(::google::protobuf::int64 value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HighYield)
}

// optional int64 LowYield = 47;
void MDBond::clear_lowyield() {
  lowyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LowYield)
  return lowyield_;
}
void MDBond::set_lowyield(::google::protobuf::int64 value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LowYield)
}

// optional int64 LastYield = 48;
void MDBond::clear_lastyield() {
  lastyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastYield)
  return lastyield_;
}
void MDBond::set_lastyield(::google::protobuf::int64 value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastYield)
}

// optional int64 WeightedAvgYield = 49;
void MDBond::clear_weightedavgyield() {
  weightedavgyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgYield)
  return weightedavgyield_;
}
void MDBond::set_weightedavgyield(::google::protobuf::int64 value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgYield)
}

// optional int32 ChannelNo = 50;
void MDBond::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDBond::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ChannelNo)
  return channelno_;
}
void MDBond::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDBond::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDBond::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDBond::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDBond::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
}
void MDBond::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDBond::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDBond::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDBond::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDBond::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
}
void MDBond::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDBond::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDBond::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDBond::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDBond::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
}
void MDBond::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDBond::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDBond::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDBond::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDBond::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
}
void MDBond::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDBond::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDBond::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDBond::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDBond::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
}
void MDBond::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDBond::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDBond::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDBond::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDBond::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
}
void MDBond::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDBond::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDBond::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDBond::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDBond::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
}
void MDBond::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDBond::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDBond::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDBond::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDBond::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
}
void MDBond::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int64 NorminalPx = 59;
void MDBond::clear_norminalpx() {
  norminalpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::norminalpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NorminalPx)
  return norminalpx_;
}
void MDBond::set_norminalpx(::google::protobuf::int64 value) {
  
  norminalpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NorminalPx)
}

// optional int64 ShortSellSharesTraded = 60;
void MDBond::clear_shortsellsharestraded() {
  shortsellsharestraded_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::shortsellsharestraded() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ShortSellSharesTraded)
  return shortsellsharestraded_;
}
void MDBond::set_shortsellsharestraded(::google::protobuf::int64 value) {
  
  shortsellsharestraded_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ShortSellSharesTraded)
}

// optional int64 ShortSellTurnover = 61;
void MDBond::clear_shortsellturnover() {
  shortsellturnover_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::shortsellturnover() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ShortSellTurnover)
  return shortsellturnover_;
}
void MDBond::set_shortsellturnover(::google::protobuf::int64 value) {
  
  shortsellturnover_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ShortSellTurnover)
}

// repeated int32 BuySettlTypeQueue = 62 [packed = true];
int MDBond::buysettltypequeue_size() const {
  return buysettltypequeue_.size();
}
void MDBond::clear_buysettltypequeue() {
  buysettltypequeue_.Clear();
}
::google::protobuf::int32 MDBond::buysettltypequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return buysettltypequeue_.Get(index);
}
void MDBond::set_buysettltypequeue(int index, ::google::protobuf::int32 value) {
  buysettltypequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
}
void MDBond::add_buysettltypequeue(::google::protobuf::int32 value) {
  buysettltypequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
MDBond::buysettltypequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return buysettltypequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
MDBond::mutable_buysettltypequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return &buysettltypequeue_;
}

// repeated int32 SellSettlTypeQueue = 63 [packed = true];
int MDBond::sellsettltypequeue_size() const {
  return sellsettltypequeue_.size();
}
void MDBond::clear_sellsettltypequeue() {
  sellsettltypequeue_.Clear();
}
::google::protobuf::int32 MDBond::sellsettltypequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return sellsettltypequeue_.Get(index);
}
void MDBond::set_sellsettltypequeue(int index, ::google::protobuf::int32 value) {
  sellsettltypequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
}
void MDBond::add_sellsettltypequeue(::google::protobuf::int32 value) {
  sellsettltypequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
MDBond::sellsettltypequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return sellsettltypequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
MDBond::mutable_sellsettltypequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return &sellsettltypequeue_;
}

// repeated int64 BuyYieldQueue = 64 [packed = true];
int MDBond::buyyieldqueue_size() const {
  return buyyieldqueue_.size();
}
void MDBond::clear_buyyieldqueue() {
  buyyieldqueue_.Clear();
}
::google::protobuf::int64 MDBond::buyyieldqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return buyyieldqueue_.Get(index);
}
void MDBond::set_buyyieldqueue(int index, ::google::protobuf::int64 value) {
  buyyieldqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
}
void MDBond::add_buyyieldqueue(::google::protobuf::int64 value) {
  buyyieldqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyyieldqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return buyyieldqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyyieldqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return &buyyieldqueue_;
}

// repeated int64 SellYieldQueue = 65 [packed = true];
int MDBond::sellyieldqueue_size() const {
  return sellyieldqueue_.size();
}
void MDBond::clear_sellyieldqueue() {
  sellyieldqueue_.Clear();
}
::google::protobuf::int64 MDBond::sellyieldqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return sellyieldqueue_.Get(index);
}
void MDBond::set_sellyieldqueue(int index, ::google::protobuf::int64 value) {
  sellyieldqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
}
void MDBond::add_sellyieldqueue(::google::protobuf::int64 value) {
  sellyieldqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellyieldqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return sellyieldqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellyieldqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return &sellyieldqueue_;
}

// optional int64 PreMarketLastPx = 66;
void MDBond::clear_premarketlastpx() {
  premarketlastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::premarketlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketLastPx)
  return premarketlastpx_;
}
void MDBond::set_premarketlastpx(::google::protobuf::int64 value) {
  
  premarketlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketLastPx)
}

// optional int64 PreMarketTotalVolumeTrade = 67;
void MDBond::clear_premarkettotalvolumetrade() {
  premarkettotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::premarkettotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketTotalVolumeTrade)
  return premarkettotalvolumetrade_;
}
void MDBond::set_premarkettotalvolumetrade(::google::protobuf::int64 value) {
  
  premarkettotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketTotalVolumeTrade)
}

// optional int64 PreMarketTotalValueTrade = 68;
void MDBond::clear_premarkettotalvaluetrade() {
  premarkettotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::premarkettotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketTotalValueTrade)
  return premarkettotalvaluetrade_;
}
void MDBond::set_premarkettotalvaluetrade(::google::protobuf::int64 value) {
  
  premarkettotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketTotalValueTrade)
}

// optional int64 PreMarketHighPx = 69;
void MDBond::clear_premarkethighpx() {
  premarkethighpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::premarkethighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketHighPx)
  return premarkethighpx_;
}
void MDBond::set_premarkethighpx(::google::protobuf::int64 value) {
  
  premarkethighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketHighPx)
}

// optional int64 PreMarketLowPx = 70;
void MDBond::clear_premarketlowpx() {
  premarketlowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::premarketlowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketLowPx)
  return premarketlowpx_;
}
void MDBond::set_premarketlowpx(::google::protobuf::int64 value) {
  
  premarketlowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketLowPx)
}

// optional int64 AfterHoursLastPx = 71;
void MDBond::clear_afterhourslastpx() {
  afterhourslastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::afterhourslastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursLastPx)
  return afterhourslastpx_;
}
void MDBond::set_afterhourslastpx(::google::protobuf::int64 value) {
  
  afterhourslastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursLastPx)
}

// optional int64 AfterHoursTotalVolumeTrade = 72;
void MDBond::clear_afterhourstotalvolumetrade() {
  afterhourstotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::afterhourstotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalVolumeTrade)
  return afterhourstotalvolumetrade_;
}
void MDBond::set_afterhourstotalvolumetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalVolumeTrade)
}

// optional int64 AfterHoursTotalValueTrade = 73;
void MDBond::clear_afterhourstotalvaluetrade() {
  afterhourstotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::afterhourstotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalValueTrade)
  return afterhourstotalvaluetrade_;
}
void MDBond::set_afterhourstotalvaluetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalValueTrade)
}

// optional int64 AfterHoursHighPx = 74;
void MDBond::clear_afterhourshighpx() {
  afterhourshighpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::afterhourshighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursHighPx)
  return afterhourshighpx_;
}
void MDBond::set_afterhourshighpx(::google::protobuf::int64 value) {
  
  afterhourshighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursHighPx)
}

// optional int64 AfterHoursLowPx = 75;
void MDBond::clear_afterhourslowpx() {
  afterhourslowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::afterhourslowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursLowPx)
  return afterhourslowpx_;
}
void MDBond::set_afterhourslowpx(::google::protobuf::int64 value) {
  
  afterhourslowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursLowPx)
}

// optional string MarketPhaseCode = 76;
void MDBond::clear_marketphasecode() {
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  return marketphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_marketphasecode(const ::std::string& value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
void MDBond::set_marketphasecode(const char* value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
void MDBond::set_marketphasecode(const char* value, size_t size) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
::std::string* MDBond::mutable_marketphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  return marketphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_marketphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  
  return marketphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_marketphasecode(::std::string* marketphasecode) {
  if (marketphasecode != NULL) {
    
  } else {
    
  }
  marketphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}

// optional string SubTradingPhaseCode1 = 77;
void MDBond::clear_subtradingphasecode1() {
  subtradingphasecode1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::subtradingphasecode1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  return subtradingphasecode1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_subtradingphasecode1(const ::std::string& value) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
void MDBond::set_subtradingphasecode1(const char* value) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
void MDBond::set_subtradingphasecode1(const char* value, size_t size) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
::std::string* MDBond::mutable_subtradingphasecode1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  return subtradingphasecode1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_subtradingphasecode1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  
  return subtradingphasecode1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_subtradingphasecode1(::std::string* subtradingphasecode1) {
  if (subtradingphasecode1 != NULL) {
    
  } else {
    
  }
  subtradingphasecode1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}

// optional string SubTradingPhaseCode2 = 78;
void MDBond::clear_subtradingphasecode2() {
  subtradingphasecode2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::subtradingphasecode2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  return subtradingphasecode2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_subtradingphasecode2(const ::std::string& value) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
void MDBond::set_subtradingphasecode2(const char* value) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
void MDBond::set_subtradingphasecode2(const char* value, size_t size) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
::std::string* MDBond::mutable_subtradingphasecode2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  return subtradingphasecode2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_subtradingphasecode2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  
  return subtradingphasecode2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_subtradingphasecode2(::std::string* subtradingphasecode2) {
  if (subtradingphasecode2 != NULL) {
    
  } else {
    
  }
  subtradingphasecode2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}

// optional string SubTradingPhaseCode3 = 79;
void MDBond::clear_subtradingphasecode3() {
  subtradingphasecode3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::subtradingphasecode3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  return subtradingphasecode3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_subtradingphasecode3(const ::std::string& value) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
void MDBond::set_subtradingphasecode3(const char* value) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
void MDBond::set_subtradingphasecode3(const char* value, size_t size) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
::std::string* MDBond::mutable_subtradingphasecode3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  return subtradingphasecode3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_subtradingphasecode3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  
  return subtradingphasecode3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_subtradingphasecode3(::std::string* subtradingphasecode3) {
  if (subtradingphasecode3 != NULL) {
    
  } else {
    
  }
  subtradingphasecode3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}

// optional string SubTradingPhaseCode4 = 80;
void MDBond::clear_subtradingphasecode4() {
  subtradingphasecode4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::subtradingphasecode4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  return subtradingphasecode4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_subtradingphasecode4(const ::std::string& value) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
void MDBond::set_subtradingphasecode4(const char* value) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
void MDBond::set_subtradingphasecode4(const char* value, size_t size) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
::std::string* MDBond::mutable_subtradingphasecode4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  return subtradingphasecode4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_subtradingphasecode4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  
  return subtradingphasecode4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_subtradingphasecode4(::std::string* subtradingphasecode4) {
  if (subtradingphasecode4 != NULL) {
    
  } else {
    
  }
  subtradingphasecode4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}

// optional string SubTradingPhaseCode5 = 81;
void MDBond::clear_subtradingphasecode5() {
  subtradingphasecode5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::subtradingphasecode5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  return subtradingphasecode5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_subtradingphasecode5(const ::std::string& value) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
void MDBond::set_subtradingphasecode5(const char* value) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
void MDBond::set_subtradingphasecode5(const char* value, size_t size) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
::std::string* MDBond::mutable_subtradingphasecode5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  return subtradingphasecode5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_subtradingphasecode5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  
  return subtradingphasecode5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_subtradingphasecode5(::std::string* subtradingphasecode5) {
  if (subtradingphasecode5 != NULL) {
    
  } else {
    
  }
  subtradingphasecode5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}

// optional int32 LastPxType = 82;
void MDBond::clear_lastpxtype() {
  lastpxtype_ = 0;
}
::google::protobuf::int32 MDBond::lastpxtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastPxType)
  return lastpxtype_;
}
void MDBond::set_lastpxtype(::google::protobuf::int32 value) {
  
  lastpxtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastPxType)
}

// optional int64 AuctionLastPx = 83;
void MDBond::clear_auctionlastpx() {
  auctionlastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::auctionlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionLastPx)
  return auctionlastpx_;
}
void MDBond::set_auctionlastpx(::google::protobuf::int64 value) {
  
  auctionlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionLastPx)
}

// optional int64 AuctionVolumeTrade = 84;
void MDBond::clear_auctionvolumetrade() {
  auctionvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::auctionvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionVolumeTrade)
  return auctionvolumetrade_;
}
void MDBond::set_auctionvolumetrade(::google::protobuf::int64 value) {
  
  auctionvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionVolumeTrade)
}

// optional int64 AuctionValueTrade = 85;
void MDBond::clear_auctionvaluetrade() {
  auctionvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::auctionvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionValueTrade)
  return auctionvaluetrade_;
}
void MDBond::set_auctionvaluetrade(::google::protobuf::int64 value) {
  
  auctionvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionValueTrade)
}

// optional int64 USConsolidateVolume = 86;
void MDBond::clear_usconsolidatevolume() {
  usconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::usconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.USConsolidateVolume)
  return usconsolidatevolume_;
}
void MDBond::set_usconsolidatevolume(::google::protobuf::int64 value) {
  
  usconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.USConsolidateVolume)
}

// optional int64 USCompositeClosePx = 87;
void MDBond::clear_uscompositeclosepx() {
  uscompositeclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::uscompositeclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.USCompositeClosePx)
  return uscompositeclosepx_;
}
void MDBond::set_uscompositeclosepx(::google::protobuf::int64 value) {
  
  uscompositeclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.USCompositeClosePx)
}

// optional string TradingHaltReason = 88;
void MDBond::clear_tradinghaltreason() {
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBond::tradinghaltreason() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  return tradinghaltreason_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_tradinghaltreason(const ::std::string& value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
void MDBond::set_tradinghaltreason(const char* value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
void MDBond::set_tradinghaltreason(const char* value, size_t size) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
::std::string* MDBond::mutable_tradinghaltreason() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  return tradinghaltreason_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBond::release_tradinghaltreason() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  
  return tradinghaltreason_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBond::set_allocated_tradinghaltreason(::std::string* tradinghaltreason) {
  if (tradinghaltreason != NULL) {
    
  } else {
    
  }
  tradinghaltreason_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradinghaltreason);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}

// optional int64 OtcTotalVolumeTrade = 89;
void MDBond::clear_otctotalvolumetrade() {
  otctotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::otctotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcTotalVolumeTrade)
  return otctotalvolumetrade_;
}
void MDBond::set_otctotalvolumetrade(::google::protobuf::int64 value) {
  
  otctotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcTotalVolumeTrade)
}

// optional int64 OtcTotalValueTrade = 90;
void MDBond::clear_otctotalvaluetrade() {
  otctotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::otctotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcTotalValueTrade)
  return otctotalvaluetrade_;
}
void MDBond::set_otctotalvaluetrade(::google::protobuf::int64 value) {
  
  otctotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcTotalValueTrade)
}

// optional int64 OtcNumTrades = 91;
void MDBond::clear_otcnumtrades() {
  otcnumtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::otcnumtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcNumTrades)
  return otcnumtrades_;
}
void MDBond::set_otcnumtrades(::google::protobuf::int64 value) {
  
  otcnumtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcNumTrades)
}

// optional int32 DataMultiplePowerOf10 = 92;
void MDBond::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDBond::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDBond::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DataMultiplePowerOf10)
}

// optional int64 ReferencePx = 93;
void MDBond::clear_referencepx() {
  referencepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::referencepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ReferencePx)
  return referencepx_;
}
void MDBond::set_referencepx(::google::protobuf::int64 value) {
  
  referencepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ReferencePx)
}

// optional int64 MaxBuyPrice = 94;
void MDBond::clear_maxbuyprice() {
  maxbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::maxbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxBuyPrice)
  return maxbuyprice_;
}
void MDBond::set_maxbuyprice(::google::protobuf::int64 value) {
  
  maxbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxBuyPrice)
}

// optional int64 MinBuyPrice = 95;
void MDBond::clear_minbuyprice() {
  minbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::minbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinBuyPrice)
  return minbuyprice_;
}
void MDBond::set_minbuyprice(::google::protobuf::int64 value) {
  
  minbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinBuyPrice)
}

// optional int64 MaxSellPrice = 96;
void MDBond::clear_maxsellprice() {
  maxsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::maxsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxSellPrice)
  return maxsellprice_;
}
void MDBond::set_maxsellprice(::google::protobuf::int64 value) {
  
  maxsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxSellPrice)
}

// optional int64 MinSellPrice = 97;
void MDBond::clear_minsellprice() {
  minsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBond::minsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinSellPrice)
  return minsellprice_;
}
void MDBond::set_minsellprice(::google::protobuf::int64 value) {
  
  minsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinSellPrice)
}

// optional int32 DelayType = 101;
void MDBond::clear_delaytype() {
  delaytype_ = 0;
}
::google::protobuf::int32 MDBond::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DelayType)
  return delaytype_;
}
void MDBond::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DelayType)
}

inline const MDBond* MDBond::internal_default_instance() {
  return &MDBond_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
