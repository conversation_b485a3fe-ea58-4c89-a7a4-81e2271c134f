syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsRateSnapshot {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  int32 RateSnapshotType = 16;
  RateSwapSnapshot RateSwapSnapshot = 17;
  StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
  int64 MessageNumber = 100;
}

message RateSwapSnapshot {
  int32 TradeMethod = 1;
  string TradeTerm = 2;
  double PreCloseRate = 11;
  double PreWeightedAvgRate = 12;
  double OpenRate = 13;
  double LastRate = 14;
  double HighRate = 15;
  double LowRate = 16;
  double CloseRate = 17;
  double WeightedAvgRate = 18;
  double LastVolumeTrade = 19;
  double TotalVolumeTrade = 20;
  string BenchmarkCurveName = 21;
  double SessionReferenceRate = 22;
}

message StandardisedRateSwapSnapshot {
  int32 TradeMethod = 1;
  double OpenRate = 11;
  double HighRate = 12;
  double LowRate = 13;
  double LastRate = 14;
  double LastVolumeTrade = 15;
  double TotalVolumeTrade = 16;
  double SettleRate = 17;
  string SettleRateDate = 18;
}
