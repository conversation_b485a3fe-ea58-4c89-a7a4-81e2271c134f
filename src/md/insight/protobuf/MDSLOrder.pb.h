// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLOrder.proto

#ifndef PROTOBUF_MDSLOrder_2eproto__INCLUDED
#define PROTOBUF_MDSLOrder_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSLOrder_2eproto();
void protobuf_InitDefaults_MDSLOrder_2eproto();
void protobuf_AssignDesc_MDSLOrder_2eproto();
void protobuf_ShutdownFile_MDSLOrder_2eproto();

class MDSLOrder;

// ===================================================================

class MDSLOrder : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSLOrder) */ {
 public:
  MDSLOrder();
  virtual ~MDSLOrder();

  MDSLOrder(const MDSLOrder& from);

  inline MDSLOrder& operator=(const MDSLOrder& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSLOrder& default_instance();

  static const MDSLOrder* internal_default_instance();

  void Swap(MDSLOrder* other);

  // implements Message ----------------------------------------------

  inline MDSLOrder* New() const { return New(NULL); }

  MDSLOrder* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSLOrder& from);
  void MergeFrom(const MDSLOrder& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSLOrder* other);
  void UnsafeMergeFrom(const MDSLOrder& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 7;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 7;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 8;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 8;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 OrderIndex = 9;
  void clear_orderindex();
  static const int kOrderIndexFieldNumber = 9;
  ::google::protobuf::int64 orderindex() const;
  void set_orderindex(::google::protobuf::int64 value);

  // optional int32 OrderType = 10;
  void clear_ordertype();
  static const int kOrderTypeFieldNumber = 10;
  ::google::protobuf::int32 ordertype() const;
  void set_ordertype(::google::protobuf::int32 value);

  // optional int64 OrderPrice = 11;
  void clear_orderprice();
  static const int kOrderPriceFieldNumber = 11;
  ::google::protobuf::int64 orderprice() const;
  void set_orderprice(::google::protobuf::int64 value);

  // optional int64 OrderQty = 12;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 12;
  ::google::protobuf::int64 orderqty() const;
  void set_orderqty(::google::protobuf::int64 value);

  // optional int32 OrderBSFlag = 13;
  void clear_orderbsflag();
  static const int kOrderBSFlagFieldNumber = 13;
  ::google::protobuf::int32 orderbsflag() const;
  void set_orderbsflag(::google::protobuf::int32 value);

  // optional int32 OrderTerm = 14;
  void clear_orderterm();
  static const int kOrderTermFieldNumber = 14;
  ::google::protobuf::int32 orderterm() const;
  void set_orderterm(::google::protobuf::int32 value);

  // optional string OrderNum = 15;
  void clear_ordernum();
  static const int kOrderNumFieldNumber = 15;
  const ::std::string& ordernum() const;
  void set_ordernum(const ::std::string& value);
  void set_ordernum(const char* value);
  void set_ordernum(const char* value, size_t size);
  ::std::string* mutable_ordernum();
  ::std::string* release_ordernum();
  void set_allocated_ordernum(::std::string* ordernum);

  // optional int32 DataMultiplePowerOf10 = 16;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 16;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSLOrder)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr ordernum_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 orderindex_;
  ::google::protobuf::int64 orderprice_;
  ::google::protobuf::int32 ordertype_;
  ::google::protobuf::int32 orderbsflag_;
  ::google::protobuf::int64 orderqty_;
  ::google::protobuf::int32 orderterm_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSLOrder_2eproto_impl();
  friend void  protobuf_AddDesc_MDSLOrder_2eproto_impl();
  friend void protobuf_AssignDesc_MDSLOrder_2eproto();
  friend void protobuf_ShutdownFile_MDSLOrder_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSLOrder> MDSLOrder_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLOrder

// optional string HTSCSecurityID = 1;
inline void MDSLOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
inline void MDSLOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
inline void MDSLOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}
inline ::std::string* MDSLOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSLOrder::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.MDDate)
  return mddate_;
}
inline void MDSLOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSLOrder::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.MDTime)
  return mdtime_;
}
inline void MDSLOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSLOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.DataTimestamp)
  return datatimestamp_;
}
inline void MDSLOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDSLOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSLOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSLOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDSLOrder::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSLOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSLOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.securityType)
}

// optional int32 ExchangeDate = 7;
inline void MDSLOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.ExchangeDate)
  return exchangedate_;
}
inline void MDSLOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
inline void MDSLOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.ExchangeTime)
  return exchangetime_;
}
inline void MDSLOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.ExchangeTime)
}

// optional int64 OrderIndex = 9;
inline void MDSLOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderIndex)
  return orderindex_;
}
inline void MDSLOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderIndex)
}

// optional int32 OrderType = 10;
inline void MDSLOrder::clear_ordertype() {
  ordertype_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderType)
  return ordertype_;
}
inline void MDSLOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderType)
}

// optional int64 OrderPrice = 11;
inline void MDSLOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderPrice)
  return orderprice_;
}
inline void MDSLOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderPrice)
}

// optional int64 OrderQty = 12;
inline void MDSLOrder::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderQty)
  return orderqty_;
}
inline void MDSLOrder::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderQty)
}

// optional int32 OrderBSFlag = 13;
inline void MDSLOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderBSFlag)
  return orderbsflag_;
}
inline void MDSLOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderBSFlag)
}

// optional int32 OrderTerm = 14;
inline void MDSLOrder::clear_orderterm() {
  orderterm_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::orderterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderTerm)
  return orderterm_;
}
inline void MDSLOrder::set_orderterm(::google::protobuf::int32 value) {
  
  orderterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderTerm)
}

// optional string OrderNum = 15;
inline void MDSLOrder::clear_ordernum() {
  ordernum_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLOrder::ordernum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  return ordernum_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLOrder::set_ordernum(const ::std::string& value) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
inline void MDSLOrder::set_ordernum(const char* value) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
inline void MDSLOrder::set_ordernum(const char* value, size_t size) {
  
  ordernum_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}
inline ::std::string* MDSLOrder::mutable_ordernum() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  return ordernum_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLOrder::release_ordernum() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
  
  return ordernum_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLOrder::set_allocated_ordernum(::std::string* ordernum) {
  if (ordernum != NULL) {
    
  } else {
    
  }
  ordernum_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ordernum);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLOrder.OrderNum)
}

// optional int32 DataMultiplePowerOf10 = 16;
inline void MDSLOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSLOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSLOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLOrder.DataMultiplePowerOf10)
}

inline const MDSLOrder* MDSLOrder::internal_default_instance() {
  return &MDSLOrder_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSLOrder_2eproto__INCLUDED
