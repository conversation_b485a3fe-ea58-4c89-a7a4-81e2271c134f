syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDStock {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  int64 LastPrice = 7;
  int64 OpenPrice = 8;
  int64 HighPrice = 9;
  int64 LowPrice = 10;
  int64 ClosePrice = 11;
  int64 PrevClosePrice = 12;
  int64 Volume = 13;
  int64 Turnover = 14;
  int64 TotalVolume = 15;
  int64 TotalTurnover = 16;
  repeated StockOrderBookEntry BidEntries = 17;
  repeated StockOrderBookEntry AskEntries = 18;
  int32 DataMultiplePowerOf10 = 19;
  int64 MessageNumber = 100;
}

message StockOrderBookEntry {
  int64 Price = 1;
  int64 Quantity = 2;
}
