syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDIceTrace {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  // --- ICE TRACE specific fields (from your file) ---
  string Cusip = 100;
  string IssueDate = 101;
  string MaturityDate = 102;
  string CouponRate = 103;
  string TradeDate = 104;
  string TradeTime = 105;
  string Price = 106;
  string Yield = 107;
  string Amount = 108;
  string BuyerSide = 109;
  string SellerSide = 110;
  string ExecutionVenue = 111;
  string SpecialCondition = 112;
  string SettlementDate = 113;
  string Indicator144A = 141;
  int64 NumberMaturityMonths = 142;
  string DebtTypeCode = 143;
  int64 TokenDel = 144;
  int64 CorrectionTradeSeq = 145;
  int32 RecordStaleInd = 146;
  double WeightedAverageCoupon = 147;
  int64 WeightedAverageLoanAge = 148;
  int64 WeightedAverageLoanSize = 149;
  int64 WeightedLoanValue = 150;
  double AverageMonthlySize = 151;
  double PrevMonthVolDec = 152;
  int64 MessageNumber = 100;
}
