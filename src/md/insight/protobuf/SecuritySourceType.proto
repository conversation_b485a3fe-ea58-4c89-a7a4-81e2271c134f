syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "SecuritySourceTypeProtos";
option optimize_for = SPEED;

// SecuritySourceType message represents a combination of security ID source and security type
message SecuritySourceType {
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 2;
}
