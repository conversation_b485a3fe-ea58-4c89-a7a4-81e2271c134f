// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSecurityLending.proto

#ifndef PROTOBUF_MDSecurityLending_2eproto__INCLUDED
#define PROTOBUF_MDSecurityLending_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSecurityLending_2eproto();
void protobuf_InitDefaults_MDSecurityLending_2eproto();
void protobuf_AssignDesc_MDSecurityLending_2eproto();
void protobuf_ShutdownFile_MDSecurityLending_2eproto();

class ADEstimatedSecurityLendingEntry;
class ADSecurityLendingEntry;
class ADValidSecurityLendingEntry;
class MDSecurityLending;

// ===================================================================

class MDSecurityLending : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSecurityLending) */ {
 public:
  MDSecurityLending();
  virtual ~MDSecurityLending();

  MDSecurityLending(const MDSecurityLending& from);

  inline MDSecurityLending& operator=(const MDSecurityLending& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSecurityLending& default_instance();

  static const MDSecurityLending* internal_default_instance();

  void Swap(MDSecurityLending* other);

  // implements Message ----------------------------------------------

  inline MDSecurityLending* New() const { return New(NULL); }

  MDSecurityLending* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSecurityLending& from);
  void MergeFrom(const MDSecurityLending& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSecurityLending* other);
  void UnsafeMergeFrom(const MDSecurityLending& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 PreWeightedRate = 10;
  void clear_preweightedrate();
  static const int kPreWeightedRateFieldNumber = 10;
  ::google::protobuf::int64 preweightedrate() const;
  void set_preweightedrate(::google::protobuf::int64 value);

  // optional int64 PreHighRate = 11;
  void clear_prehighrate();
  static const int kPreHighRateFieldNumber = 11;
  ::google::protobuf::int64 prehighrate() const;
  void set_prehighrate(::google::protobuf::int64 value);

  // optional int64 PreLowRate = 12;
  void clear_prelowrate();
  static const int kPreLowRateFieldNumber = 12;
  ::google::protobuf::int64 prelowrate() const;
  void set_prelowrate(::google::protobuf::int64 value);

  // optional int64 PreHtscVolume = 13;
  void clear_prehtscvolume();
  static const int kPreHtscVolumeFieldNumber = 13;
  ::google::protobuf::int64 prehtscvolume() const;
  void set_prehtscvolume(::google::protobuf::int64 value);

  // optional int64 PreMarketVolume = 14;
  void clear_premarketvolume();
  static const int kPreMarketVolumeFieldNumber = 14;
  ::google::protobuf::int64 premarketvolume() const;
  void set_premarketvolume(::google::protobuf::int64 value);

  // optional int64 WeightedRate = 15;
  void clear_weightedrate();
  static const int kWeightedRateFieldNumber = 15;
  ::google::protobuf::int64 weightedrate() const;
  void set_weightedrate(::google::protobuf::int64 value);

  // optional int64 HighRate = 16;
  void clear_highrate();
  static const int kHighRateFieldNumber = 16;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 17;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 17;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 HtscVolume = 18;
  void clear_htscvolume();
  static const int kHtscVolumeFieldNumber = 18;
  ::google::protobuf::int64 htscvolume() const;
  void set_htscvolume(::google::protobuf::int64 value);

  // optional int64 MarketVolume = 19;
  void clear_marketvolume();
  static const int kMarketVolumeFieldNumber = 19;
  ::google::protobuf::int64 marketvolume() const;
  void set_marketvolume(::google::protobuf::int64 value);

  // optional int64 BestBorrowRate = 20;
  void clear_bestborrowrate();
  static const int kBestBorrowRateFieldNumber = 20;
  ::google::protobuf::int64 bestborrowrate() const;
  void set_bestborrowrate(::google::protobuf::int64 value);

  // optional int64 BestLendRate = 21;
  void clear_bestlendrate();
  static const int kBestLendRateFieldNumber = 21;
  ::google::protobuf::int64 bestlendrate() const;
  void set_bestlendrate(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
  int validborrows_size() const;
  void clear_validborrows();
  static const int kValidBorrowsFieldNumber = 27;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validborrows(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validborrows() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
  int validalends_size() const;
  void clear_validalends();
  static const int kValidALendsFieldNumber = 28;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validalends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validalends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validalends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validalends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validalends() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
  int validblends_size() const;
  void clear_validblends();
  static const int kValidBLendsFieldNumber = 29;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validblends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validblends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validblends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validblends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validblends() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
  int validclends_size() const;
  void clear_validclends();
  static const int kValidCLendsFieldNumber = 30;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validclends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validclends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validclends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validclends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validclends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
  int alends_size() const;
  void clear_alends();
  static const int kALendsFieldNumber = 31;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& alends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_alends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_alends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_alends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      alends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
  int blends_size() const;
  void clear_blends();
  static const int kBLendsFieldNumber = 32;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& blends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_blends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_blends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_blends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      blends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
  int clends_size() const;
  void clear_clends();
  static const int kCLendsFieldNumber = 33;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& clends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_clends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_clends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_clends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      clends() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
  int validreservationborrows_size() const;
  void clear_validreservationborrows();
  static const int kValidReservationBorrowsFieldNumber = 34;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validreservationborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validreservationborrows(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validreservationborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validreservationborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validreservationborrows() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
  int validreservationlends_size() const;
  void clear_validreservationlends();
  static const int kValidReservationLendsFieldNumber = 35;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validreservationlends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validreservationlends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validreservationlends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validreservationlends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validreservationlends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
  int reservationborrows_size() const;
  void clear_reservationborrows();
  static const int kReservationBorrowsFieldNumber = 36;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& reservationborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_reservationborrows(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_reservationborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_reservationborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      reservationborrows() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
  int reservationlends_size() const;
  void clear_reservationlends();
  static const int kReservationLendsFieldNumber = 37;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& reservationlends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_reservationlends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_reservationlends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_reservationlends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      reservationlends() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
  int validotclends_size() const;
  void clear_validotclends();
  static const int kValidOtcLendsFieldNumber = 38;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validotclends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validotclends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validotclends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validotclends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validotclends() const;

  // optional int64 BestReservationBorrowRate = 39;
  void clear_bestreservationborrowrate();
  static const int kBestReservationBorrowRateFieldNumber = 39;
  ::google::protobuf::int64 bestreservationborrowrate() const;
  void set_bestreservationborrowrate(::google::protobuf::int64 value);

  // optional int64 BestReservationLendRate = 40;
  void clear_bestreservationlendrate();
  static const int kBestReservationLendRateFieldNumber = 40;
  ::google::protobuf::int64 bestreservationlendrate() const;
  void set_bestreservationlendrate(::google::protobuf::int64 value);

  // optional int64 ValidLendAmount = 41;
  void clear_validlendamount();
  static const int kValidLendAmountFieldNumber = 41;
  ::google::protobuf::int64 validlendamount() const;
  void set_validlendamount(::google::protobuf::int64 value);

  // optional int64 ValidALendAmount = 42;
  void clear_validalendamount();
  static const int kValidALendAmountFieldNumber = 42;
  ::google::protobuf::int64 validalendamount() const;
  void set_validalendamount(::google::protobuf::int64 value);

  // optional int64 ValidBLendAmount = 43;
  void clear_validblendamount();
  static const int kValidBLendAmountFieldNumber = 43;
  ::google::protobuf::int64 validblendamount() const;
  void set_validblendamount(::google::protobuf::int64 value);

  // optional int64 HtscBorrowAmount = 44;
  void clear_htscborrowamount();
  static const int kHtscBorrowAmountFieldNumber = 44;
  ::google::protobuf::int64 htscborrowamount() const;
  void set_htscborrowamount(::google::protobuf::int64 value);

  // optional int64 HtscBorrowRate = 45;
  void clear_htscborrowrate();
  static const int kHtscBorrowRateFieldNumber = 45;
  ::google::protobuf::int64 htscborrowrate() const;
  void set_htscborrowrate(::google::protobuf::int64 value);

  // optional int64 BestLoanRate = 46;
  void clear_bestloanrate();
  static const int kBestLoanRateFieldNumber = 46;
  ::google::protobuf::int64 bestloanrate() const;
  void set_bestloanrate(::google::protobuf::int64 value);

  // optional int64 HtscBorrowTradeVolume = 47;
  void clear_htscborrowtradevolume();
  static const int kHtscBorrowTradeVolumeFieldNumber = 47;
  ::google::protobuf::int64 htscborrowtradevolume() const;
  void set_htscborrowtradevolume(::google::protobuf::int64 value);

  // optional int64 HtscBorrowWeightedRate = 48;
  void clear_htscborrowweightedrate();
  static const int kHtscBorrowWeightedRateFieldNumber = 48;
  ::google::protobuf::int64 htscborrowweightedrate() const;
  void set_htscborrowweightedrate(::google::protobuf::int64 value);

  // optional int64 PreHtscBorrowTradeVolume = 49;
  void clear_prehtscborrowtradevolume();
  static const int kPreHtscBorrowTradeVolumeFieldNumber = 49;
  ::google::protobuf::int64 prehtscborrowtradevolume() const;
  void set_prehtscborrowtradevolume(::google::protobuf::int64 value);

  // optional int64 PreHtscBorrowWeightedRate = 50;
  void clear_prehtscborrowweightedrate();
  static const int kPreHtscBorrowWeightedRateFieldNumber = 50;
  ::google::protobuf::int64 prehtscborrowweightedrate() const;
  void set_prehtscborrowweightedrate(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
  int htscborrows_size() const;
  void clear_htscborrows();
  static const int kHtscBorrowsFieldNumber = 51;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& htscborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_htscborrows(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_htscborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_htscborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      htscborrows() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
  int loans_size() const;
  void clear_loans();
  static const int kLoansFieldNumber = 52;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& loans(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_loans(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_loans();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_loans();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      loans() const;

  // optional int32 DataMultiplePowerOf10 = 53;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 53;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
  int externallends_size() const;
  void clear_externallends();
  static const int kExternalLendsFieldNumber = 54;
  const ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry& externallends(int index) const;
  ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* mutable_externallends(int index);
  ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* add_externallends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >*
      mutable_externallends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >&
      externallends() const;

  // optional string HtscBorrowTerm = 57;
  void clear_htscborrowterm();
  static const int kHtscBorrowTermFieldNumber = 57;
  const ::std::string& htscborrowterm() const;
  void set_htscborrowterm(const ::std::string& value);
  void set_htscborrowterm(const char* value);
  void set_htscborrowterm(const char* value, size_t size);
  ::std::string* mutable_htscborrowterm();
  ::std::string* release_htscborrowterm();
  void set_allocated_htscborrowterm(::std::string* htscborrowterm);

  // optional int64 HtscBorrowOrderAmount = 58;
  void clear_htscborroworderamount();
  static const int kHtscBorrowOrderAmountFieldNumber = 58;
  ::google::protobuf::int64 htscborroworderamount() const;
  void set_htscborroworderamount(::google::protobuf::int64 value);

  // optional string ValidLendTerm = 59;
  void clear_validlendterm();
  static const int kValidLendTermFieldNumber = 59;
  const ::std::string& validlendterm() const;
  void set_validlendterm(const ::std::string& value);
  void set_validlendterm(const char* value);
  void set_validlendterm(const char* value, size_t size);
  ::std::string* mutable_validlendterm();
  ::std::string* release_validlendterm();
  void set_allocated_validlendterm(::std::string* validlendterm);

  // optional int64 ValidLendOrderAmount = 60;
  void clear_validlendorderamount();
  static const int kValidLendOrderAmountFieldNumber = 60;
  ::google::protobuf::int64 validlendorderamount() const;
  void set_validlendorderamount(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
  int marketloans_size() const;
  void clear_marketloans();
  static const int kMarketLoansFieldNumber = 61;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& marketloans(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_marketloans(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_marketloans();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_marketloans();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      marketloans() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
  int marketlends_size() const;
  void clear_marketlends();
  static const int kMarketLendsFieldNumber = 62;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& marketlends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_marketlends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_marketlends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_marketlends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      marketlends() const;

  // optional int64 ValidBorrowAmount = 63;
  void clear_validborrowamount();
  static const int kValidBorrowAmountFieldNumber = 63;
  ::google::protobuf::int64 validborrowamount() const;
  void set_validborrowamount(::google::protobuf::int64 value);

  // optional int64 LoanAmount = 64;
  void clear_loanamount();
  static const int kLoanAmountFieldNumber = 64;
  ::google::protobuf::int64 loanamount() const;
  void set_loanamount(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
  int marketborrows_size() const;
  void clear_marketborrows();
  static const int kMarketBorrowsFieldNumber = 65;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& marketborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_marketborrows(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_marketborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_marketborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      marketborrows() const;

  // optional int64 BorrowAmount = 66;
  void clear_borrowamount();
  static const int kBorrowAmountFieldNumber = 66;
  ::google::protobuf::int64 borrowamount() const;
  void set_borrowamount(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSecurityLending)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validborrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validalends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validblends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validclends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > alends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > blends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > clends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validreservationborrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validreservationlends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > reservationborrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > reservationlends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validotclends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > htscborrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > loans_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry > externallends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > marketloans_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > marketlends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > marketborrows_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr htscborrowterm_;
  ::google::protobuf::internal::ArenaStringPtr validlendterm_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 preweightedrate_;
  ::google::protobuf::int64 prehighrate_;
  ::google::protobuf::int64 prelowrate_;
  ::google::protobuf::int64 prehtscvolume_;
  ::google::protobuf::int64 premarketvolume_;
  ::google::protobuf::int64 weightedrate_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 htscvolume_;
  ::google::protobuf::int64 marketvolume_;
  ::google::protobuf::int64 bestborrowrate_;
  ::google::protobuf::int64 bestlendrate_;
  ::google::protobuf::int64 bestreservationborrowrate_;
  ::google::protobuf::int64 bestreservationlendrate_;
  ::google::protobuf::int64 validlendamount_;
  ::google::protobuf::int64 validalendamount_;
  ::google::protobuf::int64 validblendamount_;
  ::google::protobuf::int64 htscborrowamount_;
  ::google::protobuf::int64 htscborrowrate_;
  ::google::protobuf::int64 bestloanrate_;
  ::google::protobuf::int64 htscborrowtradevolume_;
  ::google::protobuf::int64 htscborrowweightedrate_;
  ::google::protobuf::int64 prehtscborrowtradevolume_;
  ::google::protobuf::int64 prehtscborrowweightedrate_;
  ::google::protobuf::int64 htscborroworderamount_;
  ::google::protobuf::int64 validlendorderamount_;
  ::google::protobuf::int64 validborrowamount_;
  ::google::protobuf::int64 loanamount_;
  ::google::protobuf::int64 borrowamount_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSecurityLending_2eproto_impl();
  friend void  protobuf_AddDesc_MDSecurityLending_2eproto_impl();
  friend void protobuf_AssignDesc_MDSecurityLending_2eproto();
  friend void protobuf_ShutdownFile_MDSecurityLending_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSecurityLending> MDSecurityLending_default_instance_;

// -------------------------------------------------------------------

class ADValidSecurityLendingEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry) */ {
 public:
  ADValidSecurityLendingEntry();
  virtual ~ADValidSecurityLendingEntry();

  ADValidSecurityLendingEntry(const ADValidSecurityLendingEntry& from);

  inline ADValidSecurityLendingEntry& operator=(const ADValidSecurityLendingEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADValidSecurityLendingEntry& default_instance();

  static const ADValidSecurityLendingEntry* internal_default_instance();

  void Swap(ADValidSecurityLendingEntry* other);

  // implements Message ----------------------------------------------

  inline ADValidSecurityLendingEntry* New() const { return New(NULL); }

  ADValidSecurityLendingEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADValidSecurityLendingEntry& from);
  void MergeFrom(const ADValidSecurityLendingEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADValidSecurityLendingEntry* other);
  void UnsafeMergeFrom(const ADValidSecurityLendingEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Level = 1;
  void clear_level();
  static const int kLevelFieldNumber = 1;
  ::google::protobuf::int32 level() const;
  void set_level(::google::protobuf::int32 value);

  // optional int64 Rate = 2;
  void clear_rate();
  static const int kRateFieldNumber = 2;
  ::google::protobuf::int64 rate() const;
  void set_rate(::google::protobuf::int64 value);

  // optional int32 Term = 3;
  void clear_term();
  static const int kTermFieldNumber = 3;
  ::google::protobuf::int32 term() const;
  void set_term(::google::protobuf::int32 value);

  // optional int64 Amount = 4;
  void clear_amount();
  static const int kAmountFieldNumber = 4;
  ::google::protobuf::int64 amount() const;
  void set_amount(::google::protobuf::int64 value);

  // optional bool HtscProvided = 5;
  void clear_htscprovided();
  static const int kHtscProvidedFieldNumber = 5;
  bool htscprovided() const;
  void set_htscprovided(bool value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 rate_;
  ::google::protobuf::int32 level_;
  ::google::protobuf::int32 term_;
  ::google::protobuf::int64 amount_;
  bool htscprovided_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSecurityLending_2eproto_impl();
  friend void  protobuf_AddDesc_MDSecurityLending_2eproto_impl();
  friend void protobuf_AssignDesc_MDSecurityLending_2eproto();
  friend void protobuf_ShutdownFile_MDSecurityLending_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADValidSecurityLendingEntry> ADValidSecurityLendingEntry_default_instance_;

// -------------------------------------------------------------------

class ADSecurityLendingEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADSecurityLendingEntry) */ {
 public:
  ADSecurityLendingEntry();
  virtual ~ADSecurityLendingEntry();

  ADSecurityLendingEntry(const ADSecurityLendingEntry& from);

  inline ADSecurityLendingEntry& operator=(const ADSecurityLendingEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADSecurityLendingEntry& default_instance();

  static const ADSecurityLendingEntry* internal_default_instance();

  void Swap(ADSecurityLendingEntry* other);

  // implements Message ----------------------------------------------

  inline ADSecurityLendingEntry* New() const { return New(NULL); }

  ADSecurityLendingEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADSecurityLendingEntry& from);
  void MergeFrom(const ADSecurityLendingEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADSecurityLendingEntry* other);
  void UnsafeMergeFrom(const ADSecurityLendingEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Level = 1;
  void clear_level();
  static const int kLevelFieldNumber = 1;
  ::google::protobuf::int32 level() const;
  void set_level(::google::protobuf::int32 value);

  // optional int64 Rate = 2;
  void clear_rate();
  static const int kRateFieldNumber = 2;
  ::google::protobuf::int64 rate() const;
  void set_rate(::google::protobuf::int64 value);

  // optional int32 Term = 3;
  void clear_term();
  static const int kTermFieldNumber = 3;
  ::google::protobuf::int32 term() const;
  void set_term(::google::protobuf::int32 value);

  // optional int64 TotalAmount = 4;
  void clear_totalamount();
  static const int kTotalAmountFieldNumber = 4;
  ::google::protobuf::int64 totalamount() const;
  void set_totalamount(::google::protobuf::int64 value);

  // optional int64 MatchedAmount = 5;
  void clear_matchedamount();
  static const int kMatchedAmountFieldNumber = 5;
  ::google::protobuf::int64 matchedamount() const;
  void set_matchedamount(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADSecurityLendingEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 rate_;
  ::google::protobuf::int32 level_;
  ::google::protobuf::int32 term_;
  ::google::protobuf::int64 totalamount_;
  ::google::protobuf::int64 matchedamount_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSecurityLending_2eproto_impl();
  friend void  protobuf_AddDesc_MDSecurityLending_2eproto_impl();
  friend void protobuf_AssignDesc_MDSecurityLending_2eproto();
  friend void protobuf_ShutdownFile_MDSecurityLending_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADSecurityLendingEntry> ADSecurityLendingEntry_default_instance_;

// -------------------------------------------------------------------

class ADEstimatedSecurityLendingEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry) */ {
 public:
  ADEstimatedSecurityLendingEntry();
  virtual ~ADEstimatedSecurityLendingEntry();

  ADEstimatedSecurityLendingEntry(const ADEstimatedSecurityLendingEntry& from);

  inline ADEstimatedSecurityLendingEntry& operator=(const ADEstimatedSecurityLendingEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADEstimatedSecurityLendingEntry& default_instance();

  static const ADEstimatedSecurityLendingEntry* internal_default_instance();

  void Swap(ADEstimatedSecurityLendingEntry* other);

  // implements Message ----------------------------------------------

  inline ADEstimatedSecurityLendingEntry* New() const { return New(NULL); }

  ADEstimatedSecurityLendingEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADEstimatedSecurityLendingEntry& from);
  void MergeFrom(const ADEstimatedSecurityLendingEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADEstimatedSecurityLendingEntry* other);
  void UnsafeMergeFrom(const ADEstimatedSecurityLendingEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Level = 1;
  void clear_level();
  static const int kLevelFieldNumber = 1;
  ::google::protobuf::int32 level() const;
  void set_level(::google::protobuf::int32 value);

  // optional int64 Rate = 2;
  void clear_rate();
  static const int kRateFieldNumber = 2;
  ::google::protobuf::int64 rate() const;
  void set_rate(::google::protobuf::int64 value);

  // optional string Term = 3;
  void clear_term();
  static const int kTermFieldNumber = 3;
  const ::std::string& term() const;
  void set_term(const ::std::string& value);
  void set_term(const char* value);
  void set_term(const char* value, size_t size);
  ::std::string* mutable_term();
  ::std::string* release_term();
  void set_allocated_term(::std::string* term);

  // optional int64 Amount = 4;
  void clear_amount();
  static const int kAmountFieldNumber = 4;
  ::google::protobuf::int64 amount() const;
  void set_amount(::google::protobuf::int64 value);

  // optional int32 PostponeProbability = 5;
  void clear_postponeprobability();
  static const int kPostponeProbabilityFieldNumber = 5;
  ::google::protobuf::int32 postponeprobability() const;
  void set_postponeprobability(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr term_;
  ::google::protobuf::int64 rate_;
  ::google::protobuf::int32 level_;
  ::google::protobuf::int32 postponeprobability_;
  ::google::protobuf::int64 amount_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSecurityLending_2eproto_impl();
  friend void  protobuf_AddDesc_MDSecurityLending_2eproto_impl();
  friend void protobuf_AssignDesc_MDSecurityLending_2eproto();
  friend void protobuf_ShutdownFile_MDSecurityLending_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADEstimatedSecurityLendingEntry> ADEstimatedSecurityLendingEntry_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSecurityLending

// optional string HTSCSecurityID = 1;
inline void MDSecurityLending::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSecurityLending::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
inline void MDSecurityLending::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
inline void MDSecurityLending::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}
inline ::std::string* MDSecurityLending::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSecurityLending::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSecurityLending::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSecurityLending::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MDDate)
  return mddate_;
}
inline void MDSecurityLending::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSecurityLending::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSecurityLending::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MDTime)
  return mdtime_;
}
inline void MDSecurityLending::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSecurityLending::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.DataTimestamp)
  return datatimestamp_;
}
inline void MDSecurityLending::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDSecurityLending::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSecurityLending::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
inline void MDSecurityLending::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
inline void MDSecurityLending::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}
inline ::std::string* MDSecurityLending::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSecurityLending::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDSecurityLending::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSecurityLending::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSecurityLending::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDSecurityLending::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSecurityLending::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSecurityLending::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.securityType)
}

// optional int64 PreWeightedRate = 10;
inline void MDSecurityLending::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreWeightedRate)
  return preweightedrate_;
}
inline void MDSecurityLending::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreWeightedRate)
}

// optional int64 PreHighRate = 11;
inline void MDSecurityLending::clear_prehighrate() {
  prehighrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::prehighrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHighRate)
  return prehighrate_;
}
inline void MDSecurityLending::set_prehighrate(::google::protobuf::int64 value) {
  
  prehighrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHighRate)
}

// optional int64 PreLowRate = 12;
inline void MDSecurityLending::clear_prelowrate() {
  prelowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::prelowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreLowRate)
  return prelowrate_;
}
inline void MDSecurityLending::set_prelowrate(::google::protobuf::int64 value) {
  
  prelowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreLowRate)
}

// optional int64 PreHtscVolume = 13;
inline void MDSecurityLending::clear_prehtscvolume() {
  prehtscvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::prehtscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscVolume)
  return prehtscvolume_;
}
inline void MDSecurityLending::set_prehtscvolume(::google::protobuf::int64 value) {
  
  prehtscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscVolume)
}

// optional int64 PreMarketVolume = 14;
inline void MDSecurityLending::clear_premarketvolume() {
  premarketvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::premarketvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreMarketVolume)
  return premarketvolume_;
}
inline void MDSecurityLending::set_premarketvolume(::google::protobuf::int64 value) {
  
  premarketvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreMarketVolume)
}

// optional int64 WeightedRate = 15;
inline void MDSecurityLending::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.WeightedRate)
  return weightedrate_;
}
inline void MDSecurityLending::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.WeightedRate)
}

// optional int64 HighRate = 16;
inline void MDSecurityLending::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HighRate)
  return highrate_;
}
inline void MDSecurityLending::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HighRate)
}

// optional int64 LowRate = 17;
inline void MDSecurityLending::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.LowRate)
  return lowrate_;
}
inline void MDSecurityLending::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.LowRate)
}

// optional int64 HtscVolume = 18;
inline void MDSecurityLending::clear_htscvolume() {
  htscvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscVolume)
  return htscvolume_;
}
inline void MDSecurityLending::set_htscvolume(::google::protobuf::int64 value) {
  
  htscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscVolume)
}

// optional int64 MarketVolume = 19;
inline void MDSecurityLending::clear_marketvolume() {
  marketvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::marketvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketVolume)
  return marketvolume_;
}
inline void MDSecurityLending::set_marketvolume(::google::protobuf::int64 value) {
  
  marketvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.MarketVolume)
}

// optional int64 BestBorrowRate = 20;
inline void MDSecurityLending::clear_bestborrowrate() {
  bestborrowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::bestborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestBorrowRate)
  return bestborrowrate_;
}
inline void MDSecurityLending::set_bestborrowrate(::google::protobuf::int64 value) {
  
  bestborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestBorrowRate)
}

// optional int64 BestLendRate = 21;
inline void MDSecurityLending::clear_bestlendrate() {
  bestlendrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::bestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestLendRate)
  return bestlendrate_;
}
inline void MDSecurityLending::set_bestlendrate(::google::protobuf::int64 value) {
  
  bestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestLendRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 27;
inline int MDSecurityLending::validborrows_size() const {
  return validborrows_.size();
}
inline void MDSecurityLending::clear_validborrows() {
  validborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return &validborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrows)
  return validborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 28;
inline int MDSecurityLending::validalends_size() const {
  return validalends_.size();
}
inline void MDSecurityLending::clear_validalends() {
  validalends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validalends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validalends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validalends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validalends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return &validalends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validalends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidALends)
  return validalends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 29;
inline int MDSecurityLending::validblends_size() const {
  return validblends_.size();
}
inline void MDSecurityLending::clear_validblends() {
  validblends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validblends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validblends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validblends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validblends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return &validblends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validblends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLends)
  return validblends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidCLends = 30;
inline int MDSecurityLending::validclends_size() const {
  return validclends_.size();
}
inline void MDSecurityLending::clear_validclends() {
  validclends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validclends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validclends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validclends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validclends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return &validclends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validclends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidCLends)
  return validclends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 31;
inline int MDSecurityLending::alends_size() const {
  return alends_.size();
}
inline void MDSecurityLending::clear_alends() {
  alends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::alends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_alends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_alends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_alends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return &alends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::alends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ALends)
  return alends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 32;
inline int MDSecurityLending::blends_size() const {
  return blends_.size();
}
inline void MDSecurityLending::clear_blends() {
  blends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::blends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_blends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_blends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_blends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return &blends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::blends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.BLends)
  return blends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry CLends = 33;
inline int MDSecurityLending::clends_size() const {
  return clends_.size();
}
inline void MDSecurityLending::clear_clends() {
  clends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::clends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_clends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_clends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_clends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return &clends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::clends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.CLends)
  return clends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationBorrows = 34;
inline int MDSecurityLending::validreservationborrows_size() const {
  return validreservationborrows_.size();
}
inline void MDSecurityLending::clear_validreservationborrows() {
  validreservationborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validreservationborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validreservationborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validreservationborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validreservationborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return &validreservationborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validreservationborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationBorrows)
  return validreservationborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidReservationLends = 35;
inline int MDSecurityLending::validreservationlends_size() const {
  return validreservationlends_.size();
}
inline void MDSecurityLending::clear_validreservationlends() {
  validreservationlends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validreservationlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validreservationlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validreservationlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validreservationlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return &validreservationlends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validreservationlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidReservationLends)
  return validreservationlends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationBorrows = 36;
inline int MDSecurityLending::reservationborrows_size() const {
  return reservationborrows_.size();
}
inline void MDSecurityLending::clear_reservationborrows() {
  reservationborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::reservationborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_reservationborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_reservationborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_reservationborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return &reservationborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::reservationborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationBorrows)
  return reservationborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ReservationLends = 37;
inline int MDSecurityLending::reservationlends_size() const {
  return reservationlends_.size();
}
inline void MDSecurityLending::clear_reservationlends() {
  reservationlends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSecurityLending::reservationlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::mutable_reservationlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSecurityLending::add_reservationlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSecurityLending::mutable_reservationlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return &reservationlends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSecurityLending::reservationlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ReservationLends)
  return reservationlends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidOtcLends = 38;
inline int MDSecurityLending::validotclends_size() const {
  return validotclends_.size();
}
inline void MDSecurityLending::clear_validotclends() {
  validotclends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::validotclends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_validotclends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_validotclends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_validotclends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return &validotclends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::validotclends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ValidOtcLends)
  return validotclends_;
}

// optional int64 BestReservationBorrowRate = 39;
inline void MDSecurityLending::clear_bestreservationborrowrate() {
  bestreservationborrowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::bestreservationborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationBorrowRate)
  return bestreservationborrowrate_;
}
inline void MDSecurityLending::set_bestreservationborrowrate(::google::protobuf::int64 value) {
  
  bestreservationborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationBorrowRate)
}

// optional int64 BestReservationLendRate = 40;
inline void MDSecurityLending::clear_bestreservationlendrate() {
  bestreservationlendrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::bestreservationlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationLendRate)
  return bestreservationlendrate_;
}
inline void MDSecurityLending::set_bestreservationlendrate(::google::protobuf::int64 value) {
  
  bestreservationlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestReservationLendRate)
}

// optional int64 ValidLendAmount = 41;
inline void MDSecurityLending::clear_validlendamount() {
  validlendamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::validlendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendAmount)
  return validlendamount_;
}
inline void MDSecurityLending::set_validlendamount(::google::protobuf::int64 value) {
  
  validlendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendAmount)
}

// optional int64 ValidALendAmount = 42;
inline void MDSecurityLending::clear_validalendamount() {
  validalendamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::validalendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidALendAmount)
  return validalendamount_;
}
inline void MDSecurityLending::set_validalendamount(::google::protobuf::int64 value) {
  
  validalendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidALendAmount)
}

// optional int64 ValidBLendAmount = 43;
inline void MDSecurityLending::clear_validblendamount() {
  validblendamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::validblendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLendAmount)
  return validblendamount_;
}
inline void MDSecurityLending::set_validblendamount(::google::protobuf::int64 value) {
  
  validblendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidBLendAmount)
}

// optional int64 HtscBorrowAmount = 44;
inline void MDSecurityLending::clear_htscborrowamount() {
  htscborrowamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowAmount)
  return htscborrowamount_;
}
inline void MDSecurityLending::set_htscborrowamount(::google::protobuf::int64 value) {
  
  htscborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowAmount)
}

// optional int64 HtscBorrowRate = 45;
inline void MDSecurityLending::clear_htscborrowrate() {
  htscborrowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowRate)
  return htscborrowrate_;
}
inline void MDSecurityLending::set_htscborrowrate(::google::protobuf::int64 value) {
  
  htscborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowRate)
}

// optional int64 BestLoanRate = 46;
inline void MDSecurityLending::clear_bestloanrate() {
  bestloanrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::bestloanrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BestLoanRate)
  return bestloanrate_;
}
inline void MDSecurityLending::set_bestloanrate(::google::protobuf::int64 value) {
  
  bestloanrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BestLoanRate)
}

// optional int64 HtscBorrowTradeVolume = 47;
inline void MDSecurityLending::clear_htscborrowtradevolume() {
  htscborrowtradevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscborrowtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTradeVolume)
  return htscborrowtradevolume_;
}
inline void MDSecurityLending::set_htscborrowtradevolume(::google::protobuf::int64 value) {
  
  htscborrowtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTradeVolume)
}

// optional int64 HtscBorrowWeightedRate = 48;
inline void MDSecurityLending::clear_htscborrowweightedrate() {
  htscborrowweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscborrowweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowWeightedRate)
  return htscborrowweightedrate_;
}
inline void MDSecurityLending::set_htscborrowweightedrate(::google::protobuf::int64 value) {
  
  htscborrowweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowWeightedRate)
}

// optional int64 PreHtscBorrowTradeVolume = 49;
inline void MDSecurityLending::clear_prehtscborrowtradevolume() {
  prehtscborrowtradevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::prehtscborrowtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowTradeVolume)
  return prehtscborrowtradevolume_;
}
inline void MDSecurityLending::set_prehtscborrowtradevolume(::google::protobuf::int64 value) {
  
  prehtscborrowtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowTradeVolume)
}

// optional int64 PreHtscBorrowWeightedRate = 50;
inline void MDSecurityLending::clear_prehtscborrowweightedrate() {
  prehtscborrowweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::prehtscborrowweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowWeightedRate)
  return prehtscborrowweightedrate_;
}
inline void MDSecurityLending::set_prehtscborrowweightedrate(::google::protobuf::int64 value) {
  
  prehtscborrowweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.PreHtscBorrowWeightedRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry HtscBorrows = 51;
inline int MDSecurityLending::htscborrows_size() const {
  return htscborrows_.size();
}
inline void MDSecurityLending::clear_htscborrows() {
  htscborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::htscborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_htscborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_htscborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_htscborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return &htscborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::htscborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrows)
  return htscborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry Loans = 52;
inline int MDSecurityLending::loans_size() const {
  return loans_.size();
}
inline void MDSecurityLending::clear_loans() {
  loans_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::loans(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_loans(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_loans() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_loans() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return &loans_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::loans() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.Loans)
  return loans_;
}

// optional int32 DataMultiplePowerOf10 = 53;
inline void MDSecurityLending::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSecurityLending::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSecurityLending::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry ExternalLends = 54;
inline int MDSecurityLending::externallends_size() const {
  return externallends_.size();
}
inline void MDSecurityLending::clear_externallends() {
  externallends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry& MDSecurityLending::externallends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* MDSecurityLending::mutable_externallends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry* MDSecurityLending::add_externallends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >*
MDSecurityLending::mutable_externallends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return &externallends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADEstimatedSecurityLendingEntry >&
MDSecurityLending::externallends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.ExternalLends)
  return externallends_;
}

// optional string HtscBorrowTerm = 57;
inline void MDSecurityLending::clear_htscborrowterm() {
  htscborrowterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSecurityLending::htscborrowterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  return htscborrowterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_htscborrowterm(const ::std::string& value) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
inline void MDSecurityLending::set_htscborrowterm(const char* value) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
inline void MDSecurityLending::set_htscborrowterm(const char* value, size_t size) {
  
  htscborrowterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}
inline ::std::string* MDSecurityLending::mutable_htscborrowterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  return htscborrowterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSecurityLending::release_htscborrowterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
  
  return htscborrowterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_allocated_htscborrowterm(::std::string* htscborrowterm) {
  if (htscborrowterm != NULL) {
    
  } else {
    
  }
  htscborrowterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscborrowterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowTerm)
}

// optional int64 HtscBorrowOrderAmount = 58;
inline void MDSecurityLending::clear_htscborroworderamount() {
  htscborroworderamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::htscborroworderamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowOrderAmount)
  return htscborroworderamount_;
}
inline void MDSecurityLending::set_htscborroworderamount(::google::protobuf::int64 value) {
  
  htscborroworderamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.HtscBorrowOrderAmount)
}

// optional string ValidLendTerm = 59;
inline void MDSecurityLending::clear_validlendterm() {
  validlendterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSecurityLending::validlendterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  return validlendterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_validlendterm(const ::std::string& value) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
inline void MDSecurityLending::set_validlendterm(const char* value) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
inline void MDSecurityLending::set_validlendterm(const char* value, size_t size) {
  
  validlendterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}
inline ::std::string* MDSecurityLending::mutable_validlendterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  return validlendterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSecurityLending::release_validlendterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
  
  return validlendterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSecurityLending::set_allocated_validlendterm(::std::string* validlendterm) {
  if (validlendterm != NULL) {
    
  } else {
    
  }
  validlendterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), validlendterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendTerm)
}

// optional int64 ValidLendOrderAmount = 60;
inline void MDSecurityLending::clear_validlendorderamount() {
  validlendorderamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::validlendorderamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendOrderAmount)
  return validlendorderamount_;
}
inline void MDSecurityLending::set_validlendorderamount(::google::protobuf::int64 value) {
  
  validlendorderamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidLendOrderAmount)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLoans = 61;
inline int MDSecurityLending::marketloans_size() const {
  return marketloans_.size();
}
inline void MDSecurityLending::clear_marketloans() {
  marketloans_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketloans(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketloans(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketloans() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketloans() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return &marketloans_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketloans() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLoans)
  return marketloans_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketLends = 62;
inline int MDSecurityLending::marketlends_size() const {
  return marketlends_.size();
}
inline void MDSecurityLending::clear_marketlends() {
  marketlends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return &marketlends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketLends)
  return marketlends_;
}

// optional int64 ValidBorrowAmount = 63;
inline void MDSecurityLending::clear_validborrowamount() {
  validborrowamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::validborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrowAmount)
  return validborrowamount_;
}
inline void MDSecurityLending::set_validborrowamount(::google::protobuf::int64 value) {
  
  validborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.ValidBorrowAmount)
}

// optional int64 LoanAmount = 64;
inline void MDSecurityLending::clear_loanamount() {
  loanamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::loanamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.LoanAmount)
  return loanamount_;
}
inline void MDSecurityLending::set_loanamount(::google::protobuf::int64 value) {
  
  loanamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.LoanAmount)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry MarketBorrows = 65;
inline int MDSecurityLending::marketborrows_size() const {
  return marketborrows_.size();
}
inline void MDSecurityLending::clear_marketborrows() {
  marketborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSecurityLending::marketborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::mutable_marketborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSecurityLending::add_marketborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSecurityLending::mutable_marketborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return &marketborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSecurityLending::marketborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSecurityLending.MarketBorrows)
  return marketborrows_;
}

// optional int64 BorrowAmount = 66;
inline void MDSecurityLending::clear_borrowamount() {
  borrowamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSecurityLending::borrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSecurityLending.BorrowAmount)
  return borrowamount_;
}
inline void MDSecurityLending::set_borrowamount(::google::protobuf::int64 value) {
  
  borrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSecurityLending.BorrowAmount)
}

inline const MDSecurityLending* MDSecurityLending::internal_default_instance() {
  return &MDSecurityLending_default_instance_.get();
}
// -------------------------------------------------------------------

// ADValidSecurityLendingEntry

// optional int32 Level = 1;
inline void ADValidSecurityLendingEntry::clear_level() {
  level_ = 0;
}
inline ::google::protobuf::int32 ADValidSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Level)
  return level_;
}
inline void ADValidSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
inline void ADValidSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADValidSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Rate)
  return rate_;
}
inline void ADValidSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Rate)
}

// optional int32 Term = 3;
inline void ADValidSecurityLendingEntry::clear_term() {
  term_ = 0;
}
inline ::google::protobuf::int32 ADValidSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Term)
  return term_;
}
inline void ADValidSecurityLendingEntry::set_term(::google::protobuf::int32 value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Term)
}

// optional int64 Amount = 4;
inline void ADValidSecurityLendingEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADValidSecurityLendingEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Amount)
  return amount_;
}
inline void ADValidSecurityLendingEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.Amount)
}

// optional bool HtscProvided = 5;
inline void ADValidSecurityLendingEntry::clear_htscprovided() {
  htscprovided_ = false;
}
inline bool ADValidSecurityLendingEntry::htscprovided() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.HtscProvided)
  return htscprovided_;
}
inline void ADValidSecurityLendingEntry::set_htscprovided(bool value) {
  
  htscprovided_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADValidSecurityLendingEntry.HtscProvided)
}

inline const ADValidSecurityLendingEntry* ADValidSecurityLendingEntry::internal_default_instance() {
  return &ADValidSecurityLendingEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// ADSecurityLendingEntry

// optional int32 Level = 1;
inline void ADSecurityLendingEntry::clear_level() {
  level_ = 0;
}
inline ::google::protobuf::int32 ADSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Level)
  return level_;
}
inline void ADSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
inline void ADSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Rate)
  return rate_;
}
inline void ADSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Rate)
}

// optional int32 Term = 3;
inline void ADSecurityLendingEntry::clear_term() {
  term_ = 0;
}
inline ::google::protobuf::int32 ADSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Term)
  return term_;
}
inline void ADSecurityLendingEntry::set_term(::google::protobuf::int32 value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.Term)
}

// optional int64 TotalAmount = 4;
inline void ADSecurityLendingEntry::clear_totalamount() {
  totalamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADSecurityLendingEntry::totalamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.TotalAmount)
  return totalamount_;
}
inline void ADSecurityLendingEntry::set_totalamount(::google::protobuf::int64 value) {
  
  totalamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.TotalAmount)
}

// optional int64 MatchedAmount = 5;
inline void ADSecurityLendingEntry::clear_matchedamount() {
  matchedamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADSecurityLendingEntry::matchedamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSecurityLendingEntry.MatchedAmount)
  return matchedamount_;
}
inline void ADSecurityLendingEntry::set_matchedamount(::google::protobuf::int64 value) {
  
  matchedamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSecurityLendingEntry.MatchedAmount)
}

inline const ADSecurityLendingEntry* ADSecurityLendingEntry::internal_default_instance() {
  return &ADSecurityLendingEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// ADEstimatedSecurityLendingEntry

// optional int32 Level = 1;
inline void ADEstimatedSecurityLendingEntry::clear_level() {
  level_ = 0;
}
inline ::google::protobuf::int32 ADEstimatedSecurityLendingEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Level)
  return level_;
}
inline void ADEstimatedSecurityLendingEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Level)
}

// optional int64 Rate = 2;
inline void ADEstimatedSecurityLendingEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADEstimatedSecurityLendingEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Rate)
  return rate_;
}
inline void ADEstimatedSecurityLendingEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Rate)
}

// optional string Term = 3;
inline void ADEstimatedSecurityLendingEntry::clear_term() {
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADEstimatedSecurityLendingEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  return term_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADEstimatedSecurityLendingEntry::set_term(const ::std::string& value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
inline void ADEstimatedSecurityLendingEntry::set_term(const char* value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
inline void ADEstimatedSecurityLendingEntry::set_term(const char* value, size_t size) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}
inline ::std::string* ADEstimatedSecurityLendingEntry::mutable_term() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  return term_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADEstimatedSecurityLendingEntry::release_term() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
  
  return term_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADEstimatedSecurityLendingEntry::set_allocated_term(::std::string* term) {
  if (term != NULL) {
    
  } else {
    
  }
  term_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), term);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Term)
}

// optional int64 Amount = 4;
inline void ADEstimatedSecurityLendingEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADEstimatedSecurityLendingEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Amount)
  return amount_;
}
inline void ADEstimatedSecurityLendingEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.Amount)
}

// optional int32 PostponeProbability = 5;
inline void ADEstimatedSecurityLendingEntry::clear_postponeprobability() {
  postponeprobability_ = 0;
}
inline ::google::protobuf::int32 ADEstimatedSecurityLendingEntry::postponeprobability() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.PostponeProbability)
  return postponeprobability_;
}
inline void ADEstimatedSecurityLendingEntry::set_postponeprobability(::google::protobuf::int32 value) {
  
  postponeprobability_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADEstimatedSecurityLendingEntry.PostponeProbability)
}

inline const ADEstimatedSecurityLendingEntry* ADEstimatedSecurityLendingEntry::internal_default_instance() {
  return &ADEstimatedSecurityLendingEntry_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSecurityLending_2eproto__INCLUDED
