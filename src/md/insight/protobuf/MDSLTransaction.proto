syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDSLTransaction {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  string DealID = 7;
  double LendRate = 8;
  double BorrowRate = 9;
  double Quantity = 10;
  string DealTime = 11;
  int32 DataMultiplePowerOf10 = 12;
  int64 MessageNumber = 100;
}
