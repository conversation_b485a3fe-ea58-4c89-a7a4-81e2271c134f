syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDBasicInfo {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  BasicInfoDetail BasicInfoDetail = 16;
  int64 MessageNumber = 100;
}

message BasicInfoDetail {
  string Symbol = 1;
  string Name = 2;
  string Exchange = 3;
  string Currency = 4;
  double LotSize = 5;
  double TickSize = 6;
}
