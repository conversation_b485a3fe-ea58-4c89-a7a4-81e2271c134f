// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SPFuture.proto

#ifndef PROTOBUF_SPFuture_2eproto__INCLUDED
#define PROTOBUF_SPFuture_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_SPFuture_2eproto();
void protobuf_InitDefaults_SPFuture_2eproto();
void protobuf_AssignDesc_SPFuture_2eproto();
void protobuf_ShutdownFile_SPFuture_2eproto();

class SPFuture;

// ===================================================================

class SPFuture : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SPFuture) */ {
 public:
  SPFuture();
  virtual ~SPFuture();

  SPFuture(const SPFuture& from);

  inline SPFuture& operator=(const SPFuture& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SPFuture& default_instance();

  static const SPFuture* internal_default_instance();

  void Swap(SPFuture* other);

  // implements Message ----------------------------------------------

  inline SPFuture* New() const { return New(NULL); }

  SPFuture* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SPFuture& from);
  void MergeFrom(const SPFuture& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SPFuture* other);
  void UnsafeMergeFrom(const SPFuture& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 TradingDate = 19;
  void clear_tradingdate();
  static const int kTradingDateFieldNumber = 19;
  ::google::protobuf::int32 tradingdate() const;
  void set_tradingdate(::google::protobuf::int32 value);

  // optional int64 PreOpenInterest = 20;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 20;
  ::google::protobuf::int64 preopeninterest() const;
  void set_preopeninterest(::google::protobuf::int64 value);

  // optional int64 PreSettlePrice = 21;
  void clear_presettleprice();
  static const int kPreSettlePriceFieldNumber = 21;
  ::google::protobuf::int64 presettleprice() const;
  void set_presettleprice(::google::protobuf::int64 value);

  // optional int64 OpenInterest = 22;
  void clear_openinterest();
  static const int kOpenInterestFieldNumber = 22;
  ::google::protobuf::int64 openinterest() const;
  void set_openinterest(::google::protobuf::int64 value);

  // optional int64 SettlePrice = 23;
  void clear_settleprice();
  static const int kSettlePriceFieldNumber = 23;
  ::google::protobuf::int64 settleprice() const;
  void set_settleprice(::google::protobuf::int64 value);

  // optional int64 PreDelta = 24;
  void clear_predelta();
  static const int kPreDeltaFieldNumber = 24;
  ::google::protobuf::int64 predelta() const;
  void set_predelta(::google::protobuf::int64 value);

  // optional int64 CurrDelta = 25;
  void clear_currdelta();
  static const int kCurrDeltaFieldNumber = 25;
  ::google::protobuf::int64 currdelta() const;
  void set_currdelta(::google::protobuf::int64 value);

  // optional int64 MiddlePx = 26;
  void clear_middlepx();
  static const int kMiddlePxFieldNumber = 26;
  ::google::protobuf::int64 middlepx() const;
  void set_middlepx(::google::protobuf::int64 value);

  // optional int64 ImpliedBuyPx = 27;
  void clear_impliedbuypx();
  static const int kImpliedBuyPxFieldNumber = 27;
  ::google::protobuf::int64 impliedbuypx() const;
  void set_impliedbuypx(::google::protobuf::int64 value);

  // optional int64 ImpliedBuyQty = 28;
  void clear_impliedbuyqty();
  static const int kImpliedBuyQtyFieldNumber = 28;
  ::google::protobuf::int64 impliedbuyqty() const;
  void set_impliedbuyqty(::google::protobuf::int64 value);

  // optional int64 ImpliedSellPx = 29;
  void clear_impliedsellpx();
  static const int kImpliedSellPxFieldNumber = 29;
  ::google::protobuf::int64 impliedsellpx() const;
  void set_impliedsellpx(::google::protobuf::int64 value);

  // optional int64 ImpliedSellQty = 30;
  void clear_impliedsellqty();
  static const int kImpliedSellQtyFieldNumber = 30;
  ::google::protobuf::int64 impliedsellqty() const;
  void set_impliedsellqty(::google::protobuf::int64 value);

  // optional int64 PositionTrend = 31;
  void clear_positiontrend();
  static const int kPositionTrendFieldNumber = 31;
  ::google::protobuf::int64 positiontrend() const;
  void set_positiontrend(::google::protobuf::int64 value);

  // optional int64 ChangeSpeed = 32;
  void clear_changespeed();
  static const int kChangeSpeedFieldNumber = 32;
  ::google::protobuf::int64 changespeed() const;
  void set_changespeed(::google::protobuf::int64 value);

  // optional int64 ChangeRate = 33;
  void clear_changerate();
  static const int kChangeRateFieldNumber = 33;
  ::google::protobuf::int64 changerate() const;
  void set_changerate(::google::protobuf::int64 value);

  // optional int64 ChangeValue = 34;
  void clear_changevalue();
  static const int kChangeValueFieldNumber = 34;
  ::google::protobuf::int64 changevalue() const;
  void set_changevalue(::google::protobuf::int64 value);

  // optional int64 Swing = 35;
  void clear_swing();
  static const int kSwingFieldNumber = 35;
  ::google::protobuf::int64 swing() const;
  void set_swing(::google::protobuf::int64 value);

  // optional string CommodityContractNumber = 36;
  void clear_commoditycontractnumber();
  static const int kCommodityContractNumberFieldNumber = 36;
  const ::std::string& commoditycontractnumber() const;
  void set_commoditycontractnumber(const ::std::string& value);
  void set_commoditycontractnumber(const char* value);
  void set_commoditycontractnumber(const char* value, size_t size);
  ::std::string* mutable_commoditycontractnumber();
  ::std::string* release_commoditycontractnumber();
  void set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber);

  // optional int32 ExchangeDate = 37;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 37;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 38;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 38;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 50;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 50;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 59;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 59;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SPFuture)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr commoditycontractnumber_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 preopeninterest_;
  ::google::protobuf::int64 presettleprice_;
  ::google::protobuf::int64 openinterest_;
  ::google::protobuf::int64 settleprice_;
  ::google::protobuf::int64 predelta_;
  ::google::protobuf::int64 currdelta_;
  ::google::protobuf::int64 middlepx_;
  ::google::protobuf::int64 impliedbuypx_;
  ::google::protobuf::int64 impliedbuyqty_;
  ::google::protobuf::int32 tradingdate_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 impliedsellpx_;
  ::google::protobuf::int64 impliedsellqty_;
  ::google::protobuf::int64 positiontrend_;
  ::google::protobuf::int64 changespeed_;
  ::google::protobuf::int64 changerate_;
  ::google::protobuf::int64 changevalue_;
  ::google::protobuf::int64 swing_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_SPFuture_2eproto_impl();
  friend void  protobuf_AddDesc_SPFuture_2eproto_impl();
  friend void protobuf_AssignDesc_SPFuture_2eproto();
  friend void protobuf_ShutdownFile_SPFuture_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SPFuture> SPFuture_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// SPFuture

// optional string HTSCSecurityID = 1;
inline void SPFuture::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SPFuture::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
}
inline void SPFuture::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
}
inline void SPFuture::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
}
inline ::std::string* SPFuture::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SPFuture::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SPFuture.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void SPFuture::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 SPFuture::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.MDDate)
  return mddate_;
}
inline void SPFuture::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.MDDate)
}

// optional int32 MDTime = 3;
inline void SPFuture::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 SPFuture::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.MDTime)
  return mdtime_;
}
inline void SPFuture::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void SPFuture::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.DataTimestamp)
  return datatimestamp_;
}
inline void SPFuture::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void SPFuture::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SPFuture::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
}
inline void SPFuture::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
}
inline void SPFuture::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
}
inline ::std::string* SPFuture::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SPFuture::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SPFuture.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void SPFuture::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource SPFuture::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void SPFuture::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void SPFuture::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType SPFuture::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void SPFuture::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.securityType)
}

// optional int64 MaxPx = 8;
inline void SPFuture::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.MaxPx)
  return maxpx_;
}
inline void SPFuture::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.MaxPx)
}

// optional int64 MinPx = 9;
inline void SPFuture::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.MinPx)
  return minpx_;
}
inline void SPFuture::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.MinPx)
}

// optional int64 PreClosePx = 10;
inline void SPFuture::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.PreClosePx)
  return preclosepx_;
}
inline void SPFuture::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void SPFuture::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.NumTrades)
  return numtrades_;
}
inline void SPFuture::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void SPFuture::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void SPFuture::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void SPFuture::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.TotalValueTrade)
  return totalvaluetrade_;
}
inline void SPFuture::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void SPFuture::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.LastPx)
  return lastpx_;
}
inline void SPFuture::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.LastPx)
}

// optional int64 OpenPx = 15;
inline void SPFuture::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.OpenPx)
  return openpx_;
}
inline void SPFuture::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.OpenPx)
}

// optional int64 ClosePx = 16;
inline void SPFuture::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ClosePx)
  return closepx_;
}
inline void SPFuture::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ClosePx)
}

// optional int64 HighPx = 17;
inline void SPFuture::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.HighPx)
  return highpx_;
}
inline void SPFuture::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.HighPx)
}

// optional int64 LowPx = 18;
inline void SPFuture::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.LowPx)
  return lowpx_;
}
inline void SPFuture::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.LowPx)
}

// optional int32 TradingDate = 19;
inline void SPFuture::clear_tradingdate() {
  tradingdate_ = 0;
}
inline ::google::protobuf::int32 SPFuture::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.TradingDate)
  return tradingdate_;
}
inline void SPFuture::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.TradingDate)
}

// optional int64 PreOpenInterest = 20;
inline void SPFuture::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.PreOpenInterest)
  return preopeninterest_;
}
inline void SPFuture::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.PreOpenInterest)
}

// optional int64 PreSettlePrice = 21;
inline void SPFuture::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.PreSettlePrice)
  return presettleprice_;
}
inline void SPFuture::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.PreSettlePrice)
}

// optional int64 OpenInterest = 22;
inline void SPFuture::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.OpenInterest)
  return openinterest_;
}
inline void SPFuture::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.OpenInterest)
}

// optional int64 SettlePrice = 23;
inline void SPFuture::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.SettlePrice)
  return settleprice_;
}
inline void SPFuture::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.SettlePrice)
}

// optional int64 PreDelta = 24;
inline void SPFuture::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.PreDelta)
  return predelta_;
}
inline void SPFuture::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.PreDelta)
}

// optional int64 CurrDelta = 25;
inline void SPFuture::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.CurrDelta)
  return currdelta_;
}
inline void SPFuture::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.CurrDelta)
}

// optional int64 MiddlePx = 26;
inline void SPFuture::clear_middlepx() {
  middlepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::middlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.MiddlePx)
  return middlepx_;
}
inline void SPFuture::set_middlepx(::google::protobuf::int64 value) {
  
  middlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.MiddlePx)
}

// optional int64 ImpliedBuyPx = 27;
inline void SPFuture::clear_impliedbuypx() {
  impliedbuypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::impliedbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ImpliedBuyPx)
  return impliedbuypx_;
}
inline void SPFuture::set_impliedbuypx(::google::protobuf::int64 value) {
  
  impliedbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ImpliedBuyPx)
}

// optional int64 ImpliedBuyQty = 28;
inline void SPFuture::clear_impliedbuyqty() {
  impliedbuyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::impliedbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ImpliedBuyQty)
  return impliedbuyqty_;
}
inline void SPFuture::set_impliedbuyqty(::google::protobuf::int64 value) {
  
  impliedbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ImpliedBuyQty)
}

// optional int64 ImpliedSellPx = 29;
inline void SPFuture::clear_impliedsellpx() {
  impliedsellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::impliedsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ImpliedSellPx)
  return impliedsellpx_;
}
inline void SPFuture::set_impliedsellpx(::google::protobuf::int64 value) {
  
  impliedsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ImpliedSellPx)
}

// optional int64 ImpliedSellQty = 30;
inline void SPFuture::clear_impliedsellqty() {
  impliedsellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::impliedsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ImpliedSellQty)
  return impliedsellqty_;
}
inline void SPFuture::set_impliedsellqty(::google::protobuf::int64 value) {
  
  impliedsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ImpliedSellQty)
}

// optional int64 PositionTrend = 31;
inline void SPFuture::clear_positiontrend() {
  positiontrend_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::positiontrend() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.PositionTrend)
  return positiontrend_;
}
inline void SPFuture::set_positiontrend(::google::protobuf::int64 value) {
  
  positiontrend_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.PositionTrend)
}

// optional int64 ChangeSpeed = 32;
inline void SPFuture::clear_changespeed() {
  changespeed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::changespeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ChangeSpeed)
  return changespeed_;
}
inline void SPFuture::set_changespeed(::google::protobuf::int64 value) {
  
  changespeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ChangeSpeed)
}

// optional int64 ChangeRate = 33;
inline void SPFuture::clear_changerate() {
  changerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::changerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ChangeRate)
  return changerate_;
}
inline void SPFuture::set_changerate(::google::protobuf::int64 value) {
  
  changerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ChangeRate)
}

// optional int64 ChangeValue = 34;
inline void SPFuture::clear_changevalue() {
  changevalue_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::changevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ChangeValue)
  return changevalue_;
}
inline void SPFuture::set_changevalue(::google::protobuf::int64 value) {
  
  changevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ChangeValue)
}

// optional int64 Swing = 35;
inline void SPFuture::clear_swing() {
  swing_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SPFuture::swing() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.Swing)
  return swing_;
}
inline void SPFuture::set_swing(::google::protobuf::int64 value) {
  
  swing_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.Swing)
}

// optional string CommodityContractNumber = 36;
inline void SPFuture::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SPFuture::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
}
inline void SPFuture::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
}
inline void SPFuture::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
}
inline ::std::string* SPFuture::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SPFuture::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SPFuture::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SPFuture.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
inline void SPFuture::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 SPFuture::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ExchangeDate)
  return exchangedate_;
}
inline void SPFuture::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
inline void SPFuture::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 SPFuture::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ExchangeTime)
  return exchangetime_;
}
inline void SPFuture::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ExchangeTime)
}

// optional int32 ChannelNo = 50;
inline void SPFuture::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 SPFuture::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.ChannelNo)
  return channelno_;
}
inline void SPFuture::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int SPFuture::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void SPFuture::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void SPFuture::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.BuyPriceQueue)
}
inline void SPFuture::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int SPFuture::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void SPFuture::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void SPFuture::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.BuyOrderQtyQueue)
}
inline void SPFuture::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int SPFuture::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void SPFuture::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void SPFuture::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.SellPriceQueue)
}
inline void SPFuture::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int SPFuture::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void SPFuture::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void SPFuture::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.SellOrderQtyQueue)
}
inline void SPFuture::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int SPFuture::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void SPFuture::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void SPFuture::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.BuyOrderQueue)
}
inline void SPFuture::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int SPFuture::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void SPFuture::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void SPFuture::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.SellOrderQueue)
}
inline void SPFuture::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int SPFuture::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void SPFuture::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void SPFuture::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.BuyNumOrdersQueue)
}
inline void SPFuture::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int SPFuture::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void SPFuture::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 SPFuture::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void SPFuture::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.SellNumOrdersQueue)
}
inline void SPFuture::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SPFuture.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
SPFuture::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SPFuture.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
SPFuture::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SPFuture.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
inline void SPFuture::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 SPFuture::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SPFuture.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void SPFuture::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SPFuture.DataMultiplePowerOf10)
}

inline const SPFuture* SPFuture::internal_default_instance() {
  return &SPFuture_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_SPFuture_2eproto__INCLUDED
