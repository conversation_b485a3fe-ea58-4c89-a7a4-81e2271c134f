// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBenchmark.proto

#ifndef PROTOBUF_MDCfetsBenchmark_2eproto__INCLUDED
#define PROTOBUF_MDCfetsBenchmark_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsBenchmark_2eproto();
void protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

class BondIndex;
class BondValuation;
class FixRepoRate;
class LoanPrimeRate;
class MDCfetsBenchmark;
class RateSwapCurve;
class RateSwapCurve_Curve;
class ShiborData;
class YieldCurve;
class YieldCurve_Curve;

// ===================================================================

class MDCfetsBenchmark : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsBenchmark) */ {
 public:
  MDCfetsBenchmark();
  virtual ~MDCfetsBenchmark();

  MDCfetsBenchmark(const MDCfetsBenchmark& from);

  inline MDCfetsBenchmark& operator=(const MDCfetsBenchmark& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsBenchmark& default_instance();

  static const MDCfetsBenchmark* internal_default_instance();

  void Swap(MDCfetsBenchmark* other);

  // implements Message ----------------------------------------------

  inline MDCfetsBenchmark* New() const { return New(NULL); }

  MDCfetsBenchmark* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsBenchmark& from);
  void MergeFrom(const MDCfetsBenchmark& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsBenchmark* other);
  void UnsafeMergeFrom(const MDCfetsBenchmark& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 DataMultiplePowerOf10 = 9;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 9;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 MessageNumber = 20;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 20;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // optional string ReferenceSymbol = 21;
  void clear_referencesymbol();
  static const int kReferenceSymbolFieldNumber = 21;
  const ::std::string& referencesymbol() const;
  void set_referencesymbol(const ::std::string& value);
  void set_referencesymbol(const char* value);
  void set_referencesymbol(const char* value, size_t size);
  ::std::string* mutable_referencesymbol();
  ::std::string* release_referencesymbol();
  void set_allocated_referencesymbol(::std::string* referencesymbol);

  // optional string VolatilityType = 22;
  void clear_volatilitytype();
  static const int kVolatilityTypeFieldNumber = 22;
  const ::std::string& volatilitytype() const;
  void set_volatilitytype(const ::std::string& value);
  void set_volatilitytype(const char* value);
  void set_volatilitytype(const char* value, size_t size);
  ::std::string* mutable_volatilitytype();
  ::std::string* release_volatilitytype();
  void set_allocated_volatilitytype(::std::string* volatilitytype);

  // optional string CalculateTime = 23;
  void clear_calculatetime();
  static const int kCalculateTimeFieldNumber = 23;
  const ::std::string& calculatetime() const;
  void set_calculatetime(const ::std::string& value);
  void set_calculatetime(const char* value);
  void set_calculatetime(const char* value, size_t size);
  ::std::string* mutable_calculatetime();
  ::std::string* release_calculatetime();
  void set_allocated_calculatetime(::std::string* calculatetime);

  // optional string CfetsSecurityType = 24;
  void clear_cfetssecuritytype();
  static const int kCfetsSecurityTypeFieldNumber = 24;
  const ::std::string& cfetssecuritytype() const;
  void set_cfetssecuritytype(const ::std::string& value);
  void set_cfetssecuritytype(const char* value);
  void set_cfetssecuritytype(const char* value, size_t size);
  ::std::string* mutable_cfetssecuritytype();
  ::std::string* release_cfetssecuritytype();
  void set_allocated_cfetssecuritytype(::std::string* cfetssecuritytype);

  // optional string CfetsSecuritySubType = 25;
  void clear_cfetssecuritysubtype();
  static const int kCfetsSecuritySubTypeFieldNumber = 25;
  const ::std::string& cfetssecuritysubtype() const;
  void set_cfetssecuritysubtype(const ::std::string& value);
  void set_cfetssecuritysubtype(const char* value);
  void set_cfetssecuritysubtype(const char* value, size_t size);
  ::std::string* mutable_cfetssecuritysubtype();
  ::std::string* release_cfetssecuritysubtype();
  void set_allocated_cfetssecuritysubtype(::std::string* cfetssecuritysubtype);

  // optional string CfetsSecuritySubTypeCode = 26;
  void clear_cfetssecuritysubtypecode();
  static const int kCfetsSecuritySubTypeCodeFieldNumber = 26;
  const ::std::string& cfetssecuritysubtypecode() const;
  void set_cfetssecuritysubtypecode(const ::std::string& value);
  void set_cfetssecuritysubtypecode(const char* value);
  void set_cfetssecuritysubtypecode(const char* value, size_t size);
  ::std::string* mutable_cfetssecuritysubtypecode();
  ::std::string* release_cfetssecuritysubtypecode();
  void set_allocated_cfetssecuritysubtypecode(::std::string* cfetssecuritysubtypecode);

  // optional int32 RateSwapCurveInsertMethod = 27;
  void clear_rateswapcurveinsertmethod();
  static const int kRateSwapCurveInsertMethodFieldNumber = 27;
  ::google::protobuf::int32 rateswapcurveinsertmethod() const;
  void set_rateswapcurveinsertmethod(::google::protobuf::int32 value);

  // optional bool ContingencyIndicator = 28;
  void clear_contingencyindicator();
  static const int kContingencyIndicatorFieldNumber = 28;
  bool contingencyindicator() const;
  void set_contingencyindicator(bool value);

  // optional int32 BenchmarkType = 99;
  void clear_benchmarktype();
  static const int kBenchmarkTypeFieldNumber = 99;
  ::google::protobuf::int32 benchmarktype() const;
  void set_benchmarktype(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
  int yieldcurves_size() const;
  void clear_yieldcurves();
  static const int kYieldCurvesFieldNumber = 100;
  const ::com::htsc::mdc::insight::model::YieldCurve& yieldcurves(int index) const;
  ::com::htsc::mdc::insight::model::YieldCurve* mutable_yieldcurves(int index);
  ::com::htsc::mdc::insight::model::YieldCurve* add_yieldcurves();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >*
      mutable_yieldcurves();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >&
      yieldcurves() const;

  // optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
  bool has_shibordata() const;
  void clear_shibordata();
  static const int kShiborDataFieldNumber = 101;
  const ::com::htsc::mdc::insight::model::ShiborData& shibordata() const;
  ::com::htsc::mdc::insight::model::ShiborData* mutable_shibordata();
  ::com::htsc::mdc::insight::model::ShiborData* release_shibordata();
  void set_allocated_shibordata(::com::htsc::mdc::insight::model::ShiborData* shibordata);

  // optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
  bool has_loanprimerate() const;
  void clear_loanprimerate();
  static const int kLoanPrimeRateFieldNumber = 102;
  const ::com::htsc::mdc::insight::model::LoanPrimeRate& loanprimerate() const;
  ::com::htsc::mdc::insight::model::LoanPrimeRate* mutable_loanprimerate();
  ::com::htsc::mdc::insight::model::LoanPrimeRate* release_loanprimerate();
  void set_allocated_loanprimerate(::com::htsc::mdc::insight::model::LoanPrimeRate* loanprimerate);

  // optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
  bool has_fixreporate() const;
  void clear_fixreporate();
  static const int kFixRepoRateFieldNumber = 103;
  const ::com::htsc::mdc::insight::model::FixRepoRate& fixreporate() const;
  ::com::htsc::mdc::insight::model::FixRepoRate* mutable_fixreporate();
  ::com::htsc::mdc::insight::model::FixRepoRate* release_fixreporate();
  void set_allocated_fixreporate(::com::htsc::mdc::insight::model::FixRepoRate* fixreporate);

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
  int rateswapcurves_size() const;
  void clear_rateswapcurves();
  static const int kRateSwapCurvesFieldNumber = 104;
  const ::com::htsc::mdc::insight::model::RateSwapCurve& rateswapcurves(int index) const;
  ::com::htsc::mdc::insight::model::RateSwapCurve* mutable_rateswapcurves(int index);
  ::com::htsc::mdc::insight::model::RateSwapCurve* add_rateswapcurves();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >*
      mutable_rateswapcurves();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >&
      rateswapcurves() const;

  // optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
  bool has_bondvaluation() const;
  void clear_bondvaluation();
  static const int kBondValuationFieldNumber = 105;
  const ::com::htsc::mdc::insight::model::BondValuation& bondvaluation() const;
  ::com::htsc::mdc::insight::model::BondValuation* mutable_bondvaluation();
  ::com::htsc::mdc::insight::model::BondValuation* release_bondvaluation();
  void set_allocated_bondvaluation(::com::htsc::mdc::insight::model::BondValuation* bondvaluation);

  // optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
  bool has_bondindex() const;
  void clear_bondindex();
  static const int kBondIndexFieldNumber = 106;
  const ::com::htsc::mdc::insight::model::BondIndex& bondindex() const;
  ::com::htsc::mdc::insight::model::BondIndex* mutable_bondindex();
  ::com::htsc::mdc::insight::model::BondIndex* release_bondindex();
  void set_allocated_bondindex(::com::htsc::mdc::insight::model::BondIndex* bondindex);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsBenchmark)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve > yieldcurves_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve > rateswapcurves_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::google::protobuf::internal::ArenaStringPtr referencesymbol_;
  ::google::protobuf::internal::ArenaStringPtr volatilitytype_;
  ::google::protobuf::internal::ArenaStringPtr calculatetime_;
  ::google::protobuf::internal::ArenaStringPtr cfetssecuritytype_;
  ::google::protobuf::internal::ArenaStringPtr cfetssecuritysubtype_;
  ::google::protobuf::internal::ArenaStringPtr cfetssecuritysubtypecode_;
  ::com::htsc::mdc::insight::model::ShiborData* shibordata_;
  ::com::htsc::mdc::insight::model::LoanPrimeRate* loanprimerate_;
  ::com::htsc::mdc::insight::model::FixRepoRate* fixreporate_;
  ::com::htsc::mdc::insight::model::BondValuation* bondvaluation_;
  ::com::htsc::mdc::insight::model::BondIndex* bondindex_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int64 messagenumber_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 rateswapcurveinsertmethod_;
  bool contingencyindicator_;
  ::google::protobuf::int32 benchmarktype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBenchmark> MDCfetsBenchmark_default_instance_;

// -------------------------------------------------------------------

class YieldCurve_Curve : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.YieldCurve.Curve) */ {
 public:
  YieldCurve_Curve();
  virtual ~YieldCurve_Curve();

  YieldCurve_Curve(const YieldCurve_Curve& from);

  inline YieldCurve_Curve& operator=(const YieldCurve_Curve& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const YieldCurve_Curve& default_instance();

  static const YieldCurve_Curve* internal_default_instance();

  void Swap(YieldCurve_Curve* other);

  // implements Message ----------------------------------------------

  inline YieldCurve_Curve* New() const { return New(NULL); }

  YieldCurve_Curve* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const YieldCurve_Curve& from);
  void MergeFrom(const YieldCurve_Curve& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(YieldCurve_Curve* other);
  void UnsafeMergeFrom(const YieldCurve_Curve& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string CurveType = 1;
  void clear_curvetype();
  static const int kCurveTypeFieldNumber = 1;
  const ::std::string& curvetype() const;
  void set_curvetype(const ::std::string& value);
  void set_curvetype(const char* value);
  void set_curvetype(const char* value, size_t size);
  ::std::string* mutable_curvetype();
  ::std::string* release_curvetype();
  void set_allocated_curvetype(::std::string* curvetype);

  // optional string DataSource = 2;
  void clear_datasource();
  static const int kDataSourceFieldNumber = 2;
  const ::std::string& datasource() const;
  void set_datasource(const ::std::string& value);
  void set_datasource(const char* value);
  void set_datasource(const char* value, size_t size);
  ::std::string* mutable_datasource();
  ::std::string* release_datasource();
  void set_allocated_datasource(::std::string* datasource);

  // optional double YieldPx = 3;
  void clear_yieldpx();
  static const int kYieldPxFieldNumber = 3;
  double yieldpx() const;
  void set_yieldpx(double value);

  // optional double Spread = 4;
  void clear_spread();
  static const int kSpreadFieldNumber = 4;
  double spread() const;
  void set_spread(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.YieldCurve.Curve)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr curvetype_;
  ::google::protobuf::internal::ArenaStringPtr datasource_;
  double yieldpx_;
  double spread_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<YieldCurve_Curve> YieldCurve_Curve_default_instance_;

// -------------------------------------------------------------------

class YieldCurve : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.YieldCurve) */ {
 public:
  YieldCurve();
  virtual ~YieldCurve();

  YieldCurve(const YieldCurve& from);

  inline YieldCurve& operator=(const YieldCurve& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const YieldCurve& default_instance();

  static const YieldCurve* internal_default_instance();

  void Swap(YieldCurve* other);

  // implements Message ----------------------------------------------

  inline YieldCurve* New() const { return New(NULL); }

  YieldCurve* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const YieldCurve& from);
  void MergeFrom(const YieldCurve& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(YieldCurve* other);
  void UnsafeMergeFrom(const YieldCurve& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef YieldCurve_Curve Curve;

  // accessors -------------------------------------------------------

  // optional string YieldTerm = 1;
  void clear_yieldterm();
  static const int kYieldTermFieldNumber = 1;
  const ::std::string& yieldterm() const;
  void set_yieldterm(const ::std::string& value);
  void set_yieldterm(const char* value);
  void set_yieldterm(const char* value, size_t size);
  ::std::string* mutable_yieldterm();
  ::std::string* release_yieldterm();
  void set_allocated_yieldterm(::std::string* yieldterm);

  // optional string YieldTermType = 2;
  void clear_yieldtermtype();
  static const int kYieldTermTypeFieldNumber = 2;
  const ::std::string& yieldtermtype() const;
  void set_yieldtermtype(const ::std::string& value);
  void set_yieldtermtype(const char* value);
  void set_yieldtermtype(const char* value, size_t size);
  ::std::string* mutable_yieldtermtype();
  ::std::string* release_yieldtermtype();
  void set_allocated_yieldtermtype(::std::string* yieldtermtype);

  // optional string SettlDate = 3;
  void clear_settldate();
  static const int kSettlDateFieldNumber = 3;
  const ::std::string& settldate() const;
  void set_settldate(const ::std::string& value);
  void set_settldate(const char* value);
  void set_settldate(const char* value, size_t size);
  ::std::string* mutable_settldate();
  ::std::string* release_settldate();
  void set_allocated_settldate(::std::string* settldate);

  // optional double SecTermYearly = 4;
  void clear_sectermyearly();
  static const int kSecTermYearlyFieldNumber = 4;
  double sectermyearly() const;
  void set_sectermyearly(double value);

  // repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
  int curves_size() const;
  void clear_curves();
  static const int kCurvesFieldNumber = 100;
  const ::com::htsc::mdc::insight::model::YieldCurve_Curve& curves(int index) const;
  ::com::htsc::mdc::insight::model::YieldCurve_Curve* mutable_curves(int index);
  ::com::htsc::mdc::insight::model::YieldCurve_Curve* add_curves();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >*
      mutable_curves();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >&
      curves() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.YieldCurve)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve > curves_;
  ::google::protobuf::internal::ArenaStringPtr yieldterm_;
  ::google::protobuf::internal::ArenaStringPtr yieldtermtype_;
  ::google::protobuf::internal::ArenaStringPtr settldate_;
  double sectermyearly_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<YieldCurve> YieldCurve_default_instance_;

// -------------------------------------------------------------------

class ShiborData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ShiborData) */ {
 public:
  ShiborData();
  virtual ~ShiborData();

  ShiborData(const ShiborData& from);

  inline ShiborData& operator=(const ShiborData& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ShiborData& default_instance();

  static const ShiborData* internal_default_instance();

  void Swap(ShiborData* other);

  // implements Message ----------------------------------------------

  inline ShiborData* New() const { return New(NULL); }

  ShiborData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ShiborData& from);
  void MergeFrom(const ShiborData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ShiborData* other);
  void UnsafeMergeFrom(const ShiborData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string EffectiveDate = 1;
  void clear_effectivedate();
  static const int kEffectiveDateFieldNumber = 1;
  const ::std::string& effectivedate() const;
  void set_effectivedate(const ::std::string& value);
  void set_effectivedate(const char* value);
  void set_effectivedate(const char* value, size_t size);
  ::std::string* mutable_effectivedate();
  ::std::string* release_effectivedate();
  void set_allocated_effectivedate(::std::string* effectivedate);

  // optional double Shibor = 2;
  void clear_shibor();
  static const int kShiborFieldNumber = 2;
  double shibor() const;
  void set_shibor(double value);

  // optional double ShiborChange = 3;
  void clear_shiborchange();
  static const int kShiborChangeFieldNumber = 3;
  double shiborchange() const;
  void set_shiborchange(double value);

  // optional double ShiborQuote = 4;
  void clear_shiborquote();
  static const int kShiborQuoteFieldNumber = 4;
  double shiborquote() const;
  void set_shiborquote(double value);

  // optional string ShiborQuoteTime = 5;
  void clear_shiborquotetime();
  static const int kShiborQuoteTimeFieldNumber = 5;
  const ::std::string& shiborquotetime() const;
  void set_shiborquotetime(const ::std::string& value);
  void set_shiborquotetime(const char* value);
  void set_shiborquotetime(const char* value, size_t size);
  ::std::string* mutable_shiborquotetime();
  ::std::string* release_shiborquotetime();
  void set_allocated_shiborquotetime(::std::string* shiborquotetime);

  // optional string QuotePartyID = 6;
  void clear_quotepartyid();
  static const int kQuotePartyIDFieldNumber = 6;
  const ::std::string& quotepartyid() const;
  void set_quotepartyid(const ::std::string& value);
  void set_quotepartyid(const char* value);
  void set_quotepartyid(const char* value, size_t size);
  ::std::string* mutable_quotepartyid();
  ::std::string* release_quotepartyid();
  void set_allocated_quotepartyid(::std::string* quotepartyid);

  // optional string QuotePartyShortName = 7;
  void clear_quotepartyshortname();
  static const int kQuotePartyShortNameFieldNumber = 7;
  const ::std::string& quotepartyshortname() const;
  void set_quotepartyshortname(const ::std::string& value);
  void set_quotepartyshortname(const char* value);
  void set_quotepartyshortname(const char* value, size_t size);
  ::std::string* mutable_quotepartyshortname();
  ::std::string* release_quotepartyshortname();
  void set_allocated_quotepartyshortname(::std::string* quotepartyshortname);

  // optional double MovingAverage = 8;
  void clear_movingaverage();
  static const int kMovingAverageFieldNumber = 8;
  double movingaverage() const;
  void set_movingaverage(double value);

  // optional string MovingAverageType = 9;
  void clear_movingaveragetype();
  static const int kMovingAverageTypeFieldNumber = 9;
  const ::std::string& movingaveragetype() const;
  void set_movingaveragetype(const ::std::string& value);
  void set_movingaveragetype(const char* value);
  void set_movingaveragetype(const char* value, size_t size);
  ::std::string* mutable_movingaveragetype();
  ::std::string* release_movingaveragetype();
  void set_allocated_movingaveragetype(::std::string* movingaveragetype);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ShiborData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr effectivedate_;
  ::google::protobuf::internal::ArenaStringPtr shiborquotetime_;
  ::google::protobuf::internal::ArenaStringPtr quotepartyid_;
  ::google::protobuf::internal::ArenaStringPtr quotepartyshortname_;
  ::google::protobuf::internal::ArenaStringPtr movingaveragetype_;
  double shibor_;
  double shiborchange_;
  double shiborquote_;
  double movingaverage_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ShiborData> ShiborData_default_instance_;

// -------------------------------------------------------------------

class LoanPrimeRate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.LoanPrimeRate) */ {
 public:
  LoanPrimeRate();
  virtual ~LoanPrimeRate();

  LoanPrimeRate(const LoanPrimeRate& from);

  inline LoanPrimeRate& operator=(const LoanPrimeRate& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const LoanPrimeRate& default_instance();

  static const LoanPrimeRate* internal_default_instance();

  void Swap(LoanPrimeRate* other);

  // implements Message ----------------------------------------------

  inline LoanPrimeRate* New() const { return New(NULL); }

  LoanPrimeRate* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const LoanPrimeRate& from);
  void MergeFrom(const LoanPrimeRate& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(LoanPrimeRate* other);
  void UnsafeMergeFrom(const LoanPrimeRate& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string EffectiveDate = 1;
  void clear_effectivedate();
  static const int kEffectiveDateFieldNumber = 1;
  const ::std::string& effectivedate() const;
  void set_effectivedate(const ::std::string& value);
  void set_effectivedate(const char* value);
  void set_effectivedate(const char* value, size_t size);
  ::std::string* mutable_effectivedate();
  ::std::string* release_effectivedate();
  void set_allocated_effectivedate(::std::string* effectivedate);

  // optional double Lpr = 2;
  void clear_lpr();
  static const int kLprFieldNumber = 2;
  double lpr() const;
  void set_lpr(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.LoanPrimeRate)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr effectivedate_;
  double lpr_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<LoanPrimeRate> LoanPrimeRate_default_instance_;

// -------------------------------------------------------------------

class FixRepoRate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.FixRepoRate) */ {
 public:
  FixRepoRate();
  virtual ~FixRepoRate();

  FixRepoRate(const FixRepoRate& from);

  inline FixRepoRate& operator=(const FixRepoRate& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const FixRepoRate& default_instance();

  static const FixRepoRate* internal_default_instance();

  void Swap(FixRepoRate* other);

  // implements Message ----------------------------------------------

  inline FixRepoRate* New() const { return New(NULL); }

  FixRepoRate* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const FixRepoRate& from);
  void MergeFrom(const FixRepoRate& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(FixRepoRate* other);
  void UnsafeMergeFrom(const FixRepoRate& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string EffectiveDate = 1;
  void clear_effectivedate();
  static const int kEffectiveDateFieldNumber = 1;
  const ::std::string& effectivedate() const;
  void set_effectivedate(const ::std::string& value);
  void set_effectivedate(const char* value);
  void set_effectivedate(const char* value, size_t size);
  ::std::string* mutable_effectivedate();
  ::std::string* release_effectivedate();
  void set_allocated_effectivedate(::std::string* effectivedate);

  // optional double Rate = 2;
  void clear_rate();
  static const int kRateFieldNumber = 2;
  double rate() const;
  void set_rate(double value);

  // optional int32 TermUpperLimit = 3;
  void clear_termupperlimit();
  static const int kTermUpperLimitFieldNumber = 3;
  ::google::protobuf::int32 termupperlimit() const;
  void set_termupperlimit(::google::protobuf::int32 value);

  // optional int32 TermFloorLimit = 4;
  void clear_termfloorlimit();
  static const int kTermFloorLimitFieldNumber = 4;
  ::google::protobuf::int32 termfloorlimit() const;
  void set_termfloorlimit(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.FixRepoRate)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr effectivedate_;
  double rate_;
  ::google::protobuf::int32 termupperlimit_;
  ::google::protobuf::int32 termfloorlimit_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<FixRepoRate> FixRepoRate_default_instance_;

// -------------------------------------------------------------------

class RateSwapCurve_Curve : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.RateSwapCurve.Curve) */ {
 public:
  RateSwapCurve_Curve();
  virtual ~RateSwapCurve_Curve();

  RateSwapCurve_Curve(const RateSwapCurve_Curve& from);

  inline RateSwapCurve_Curve& operator=(const RateSwapCurve_Curve& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RateSwapCurve_Curve& default_instance();

  static const RateSwapCurve_Curve* internal_default_instance();

  void Swap(RateSwapCurve_Curve* other);

  // implements Message ----------------------------------------------

  inline RateSwapCurve_Curve* New() const { return New(NULL); }

  RateSwapCurve_Curve* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RateSwapCurve_Curve& from);
  void MergeFrom(const RateSwapCurve_Curve& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RateSwapCurve_Curve* other);
  void UnsafeMergeFrom(const RateSwapCurve_Curve& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string CurveType = 1;
  void clear_curvetype();
  static const int kCurveTypeFieldNumber = 1;
  const ::std::string& curvetype() const;
  void set_curvetype(const ::std::string& value);
  void set_curvetype(const char* value);
  void set_curvetype(const char* value, size_t size);
  ::std::string* mutable_curvetype();
  ::std::string* release_curvetype();
  void set_allocated_curvetype(::std::string* curvetype);

  // optional double YieldPx = 2;
  void clear_yieldpx();
  static const int kYieldPxFieldNumber = 2;
  double yieldpx() const;
  void set_yieldpx(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.RateSwapCurve.Curve)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr curvetype_;
  double yieldpx_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RateSwapCurve_Curve> RateSwapCurve_Curve_default_instance_;

// -------------------------------------------------------------------

class RateSwapCurve : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.RateSwapCurve) */ {
 public:
  RateSwapCurve();
  virtual ~RateSwapCurve();

  RateSwapCurve(const RateSwapCurve& from);

  inline RateSwapCurve& operator=(const RateSwapCurve& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RateSwapCurve& default_instance();

  static const RateSwapCurve* internal_default_instance();

  void Swap(RateSwapCurve* other);

  // implements Message ----------------------------------------------

  inline RateSwapCurve* New() const { return New(NULL); }

  RateSwapCurve* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RateSwapCurve& from);
  void MergeFrom(const RateSwapCurve& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RateSwapCurve* other);
  void UnsafeMergeFrom(const RateSwapCurve& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef RateSwapCurve_Curve Curve;

  // accessors -------------------------------------------------------

  // optional string YieldTerm = 1;
  void clear_yieldterm();
  static const int kYieldTermFieldNumber = 1;
  const ::std::string& yieldterm() const;
  void set_yieldterm(const ::std::string& value);
  void set_yieldterm(const char* value);
  void set_yieldterm(const char* value, size_t size);
  ::std::string* mutable_yieldterm();
  ::std::string* release_yieldterm();
  void set_allocated_yieldterm(::std::string* yieldterm);

  // optional string YieldTermType = 2;
  void clear_yieldtermtype();
  static const int kYieldTermTypeFieldNumber = 2;
  const ::std::string& yieldtermtype() const;
  void set_yieldtermtype(const ::std::string& value);
  void set_yieldtermtype(const char* value);
  void set_yieldtermtype(const char* value, size_t size);
  ::std::string* mutable_yieldtermtype();
  ::std::string* release_yieldtermtype();
  void set_allocated_yieldtermtype(::std::string* yieldtermtype);

  // optional string CalculateDate = 3;
  void clear_calculatedate();
  static const int kCalculateDateFieldNumber = 3;
  const ::std::string& calculatedate() const;
  void set_calculatedate(const ::std::string& value);
  void set_calculatedate(const char* value);
  void set_calculatedate(const char* value, size_t size);
  ::std::string* mutable_calculatedate();
  ::std::string* release_calculatedate();
  void set_allocated_calculatedate(::std::string* calculatedate);

  // repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
  int curves_size() const;
  void clear_curves();
  static const int kCurvesFieldNumber = 100;
  const ::com::htsc::mdc::insight::model::RateSwapCurve_Curve& curves(int index) const;
  ::com::htsc::mdc::insight::model::RateSwapCurve_Curve* mutable_curves(int index);
  ::com::htsc::mdc::insight::model::RateSwapCurve_Curve* add_curves();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >*
      mutable_curves();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >&
      curves() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.RateSwapCurve)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve > curves_;
  ::google::protobuf::internal::ArenaStringPtr yieldterm_;
  ::google::protobuf::internal::ArenaStringPtr yieldtermtype_;
  ::google::protobuf::internal::ArenaStringPtr calculatedate_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RateSwapCurve> RateSwapCurve_default_instance_;

// -------------------------------------------------------------------

class BondValuation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BondValuation) */ {
 public:
  BondValuation();
  virtual ~BondValuation();

  BondValuation(const BondValuation& from);

  inline BondValuation& operator=(const BondValuation& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BondValuation& default_instance();

  static const BondValuation* internal_default_instance();

  void Swap(BondValuation* other);

  // implements Message ----------------------------------------------

  inline BondValuation* New() const { return New(NULL); }

  BondValuation* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BondValuation& from);
  void MergeFrom(const BondValuation& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BondValuation* other);
  void UnsafeMergeFrom(const BondValuation& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TransactDate = 1;
  void clear_transactdate();
  static const int kTransactDateFieldNumber = 1;
  const ::std::string& transactdate() const;
  void set_transactdate(const ::std::string& value);
  void set_transactdate(const char* value);
  void set_transactdate(const char* value, size_t size);
  ::std::string* mutable_transactdate();
  ::std::string* release_transactdate();
  void set_allocated_transactdate(::std::string* transactdate);

  // optional string ValuationDateTime = 2;
  void clear_valuationdatetime();
  static const int kValuationDateTimeFieldNumber = 2;
  const ::std::string& valuationdatetime() const;
  void set_valuationdatetime(const ::std::string& value);
  void set_valuationdatetime(const char* value);
  void set_valuationdatetime(const char* value, size_t size);
  ::std::string* mutable_valuationdatetime();
  ::std::string* release_valuationdatetime();
  void set_allocated_valuationdatetime(::std::string* valuationdatetime);

  // optional string Currency = 3;
  void clear_currency();
  static const int kCurrencyFieldNumber = 3;
  const ::std::string& currency() const;
  void set_currency(const ::std::string& value);
  void set_currency(const char* value);
  void set_currency(const char* value, size_t size);
  ::std::string* mutable_currency();
  ::std::string* release_currency();
  void set_allocated_currency(::std::string* currency);

  // optional string CouponRateType = 4;
  void clear_couponratetype();
  static const int kCouponRateTypeFieldNumber = 4;
  const ::std::string& couponratetype() const;
  void set_couponratetype(const ::std::string& value);
  void set_couponratetype(const char* value);
  void set_couponratetype(const char* value, size_t size);
  ::std::string* mutable_couponratetype();
  ::std::string* release_couponratetype();
  void set_allocated_couponratetype(::std::string* couponratetype);

  // optional double ValuationAmt = 5;
  void clear_valuationamt();
  static const int kValuationAmtFieldNumber = 5;
  double valuationamt() const;
  void set_valuationamt(double value);

  // optional double DirtyPrice = 6;
  void clear_dirtyprice();
  static const int kDirtyPriceFieldNumber = 6;
  double dirtyprice() const;
  void set_dirtyprice(double value);

  // optional double SpreadCNYDirtyPrice = 7;
  void clear_spreadcnydirtyprice();
  static const int kSpreadCNYDirtyPriceFieldNumber = 7;
  double spreadcnydirtyprice() const;
  void set_spreadcnydirtyprice(double value);

  // optional double SpreadCNYPrice = 8;
  void clear_spreadcnyprice();
  static const int kSpreadCNYPriceFieldNumber = 8;
  double spreadcnyprice() const;
  void set_spreadcnyprice(double value);

  // optional double ValuationYield = 9;
  void clear_valuationyield();
  static const int kValuationYieldFieldNumber = 9;
  double valuationyield() const;
  void set_valuationyield(double value);

  // optional double SpreadDuration = 10;
  void clear_spreadduration();
  static const int kSpreadDurationFieldNumber = 10;
  double spreadduration() const;
  void set_spreadduration(double value);

  // optional double SpreadConvexity = 11;
  void clear_spreadconvexity();
  static const int kSpreadConvexityFieldNumber = 11;
  double spreadconvexity() const;
  void set_spreadconvexity(double value);

  // optional double Duration = 12;
  void clear_duration();
  static const int kDurationFieldNumber = 12;
  double duration() const;
  void set_duration(double value);

  // optional double ModifyDuration = 13;
  void clear_modifyduration();
  static const int kModifyDurationFieldNumber = 13;
  double modifyduration() const;
  void set_modifyduration(double value);

  // optional double Convexity = 14;
  void clear_convexity();
  static const int kConvexityFieldNumber = 14;
  double convexity() const;
  void set_convexity(double value);

  // optional double SpreadPx = 15;
  void clear_spreadpx();
  static const int kSpreadPxFieldNumber = 15;
  double spreadpx() const;
  void set_spreadpx(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BondValuation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr transactdate_;
  ::google::protobuf::internal::ArenaStringPtr valuationdatetime_;
  ::google::protobuf::internal::ArenaStringPtr currency_;
  ::google::protobuf::internal::ArenaStringPtr couponratetype_;
  double valuationamt_;
  double dirtyprice_;
  double spreadcnydirtyprice_;
  double spreadcnyprice_;
  double valuationyield_;
  double spreadduration_;
  double spreadconvexity_;
  double duration_;
  double modifyduration_;
  double convexity_;
  double spreadpx_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BondValuation> BondValuation_default_instance_;

// -------------------------------------------------------------------

class BondIndex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BondIndex) */ {
 public:
  BondIndex();
  virtual ~BondIndex();

  BondIndex(const BondIndex& from);

  inline BondIndex& operator=(const BondIndex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BondIndex& default_instance();

  static const BondIndex* internal_default_instance();

  void Swap(BondIndex* other);

  // implements Message ----------------------------------------------

  inline BondIndex* New() const { return New(NULL); }

  BondIndex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BondIndex& from);
  void MergeFrom(const BondIndex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BondIndex* other);
  void UnsafeMergeFrom(const BondIndex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 KCurveType = 1;
  void clear_kcurvetype();
  static const int kKCurveTypeFieldNumber = 1;
  ::google::protobuf::int32 kcurvetype() const;
  void set_kcurvetype(::google::protobuf::int32 value);

  // optional double LastPx = 2;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 2;
  double lastpx() const;
  void set_lastpx(double value);

  // optional double OpenPx = 3;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 3;
  double openpx() const;
  void set_openpx(double value);

  // optional double ClosePx = 4;
  void clear_closepx();
  static const int kClosePxFieldNumber = 4;
  double closepx() const;
  void set_closepx(double value);

  // optional double HighPx = 5;
  void clear_highpx();
  static const int kHighPxFieldNumber = 5;
  double highpx() const;
  void set_highpx(double value);

  // optional double LowPx = 6;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 6;
  double lowpx() const;
  void set_lowpx(double value);

  // optional double TotalVolumeTradeMillion = 7;
  void clear_totalvolumetrademillion();
  static const int kTotalVolumeTradeMillionFieldNumber = 7;
  double totalvolumetrademillion() const;
  void set_totalvolumetrademillion(double value);

  // optional double TotalValueTradeMillion = 8;
  void clear_totalvaluetrademillion();
  static const int kTotalValueTradeMillionFieldNumber = 8;
  double totalvaluetrademillion() const;
  void set_totalvaluetrademillion(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BondIndex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double lastpx_;
  double openpx_;
  double closepx_;
  double highpx_;
  double lowpx_;
  double totalvolumetrademillion_;
  double totalvaluetrademillion_;
  ::google::protobuf::int32 kcurvetype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BondIndex> BondIndex_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBenchmark

// optional string HTSCSecurityID = 1;
inline void MDCfetsBenchmark::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
inline void MDCfetsBenchmark::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
inline void MDCfetsBenchmark::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}
inline ::std::string* MDCfetsBenchmark::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsBenchmark::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsBenchmark::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsBenchmark::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsBenchmark::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsBenchmark::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsBenchmark::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsBenchmark::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBenchmark::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDDate)
  return mddate_;
}
inline void MDCfetsBenchmark::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsBenchmark::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBenchmark::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDTime)
  return mdtime_;
}
inline void MDCfetsBenchmark::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsBenchmark::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBenchmark::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsBenchmark::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsBenchmark::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
inline void MDCfetsBenchmark::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
inline void MDCfetsBenchmark::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}
inline ::std::string* MDCfetsBenchmark::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsBenchmark::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
inline void MDCfetsBenchmark::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
inline void MDCfetsBenchmark::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}
inline ::std::string* MDCfetsBenchmark::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
inline void MDCfetsBenchmark::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBenchmark::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsBenchmark::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 20;
inline void MDCfetsBenchmark::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBenchmark::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.MessageNumber)
  return messagenumber_;
}
inline void MDCfetsBenchmark::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.MessageNumber)
}

// optional string ReferenceSymbol = 21;
inline void MDCfetsBenchmark::clear_referencesymbol() {
  referencesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::referencesymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  return referencesymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_referencesymbol(const ::std::string& value) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
inline void MDCfetsBenchmark::set_referencesymbol(const char* value) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
inline void MDCfetsBenchmark::set_referencesymbol(const char* value, size_t size) {
  
  referencesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}
inline ::std::string* MDCfetsBenchmark::mutable_referencesymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  return referencesymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_referencesymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
  
  return referencesymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_referencesymbol(::std::string* referencesymbol) {
  if (referencesymbol != NULL) {
    
  } else {
    
  }
  referencesymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), referencesymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.ReferenceSymbol)
}

// optional string VolatilityType = 22;
inline void MDCfetsBenchmark::clear_volatilitytype() {
  volatilitytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::volatilitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  return volatilitytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_volatilitytype(const ::std::string& value) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
inline void MDCfetsBenchmark::set_volatilitytype(const char* value) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
inline void MDCfetsBenchmark::set_volatilitytype(const char* value, size_t size) {
  
  volatilitytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}
inline ::std::string* MDCfetsBenchmark::mutable_volatilitytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  return volatilitytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_volatilitytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
  
  return volatilitytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_volatilitytype(::std::string* volatilitytype) {
  if (volatilitytype != NULL) {
    
  } else {
    
  }
  volatilitytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.VolatilityType)
}

// optional string CalculateTime = 23;
inline void MDCfetsBenchmark::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
inline void MDCfetsBenchmark::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
inline void MDCfetsBenchmark::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}
inline ::std::string* MDCfetsBenchmark::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CalculateTime)
}

// optional string CfetsSecurityType = 24;
inline void MDCfetsBenchmark::clear_cfetssecuritytype() {
  cfetssecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::cfetssecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  return cfetssecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_cfetssecuritytype(const ::std::string& value) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
inline void MDCfetsBenchmark::set_cfetssecuritytype(const char* value) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
inline void MDCfetsBenchmark::set_cfetssecuritytype(const char* value, size_t size) {
  
  cfetssecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}
inline ::std::string* MDCfetsBenchmark::mutable_cfetssecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  return cfetssecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_cfetssecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
  
  return cfetssecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_cfetssecuritytype(::std::string* cfetssecuritytype) {
  if (cfetssecuritytype != NULL) {
    
  } else {
    
  }
  cfetssecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecurityType)
}

// optional string CfetsSecuritySubType = 25;
inline void MDCfetsBenchmark::clear_cfetssecuritysubtype() {
  cfetssecuritysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::cfetssecuritysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  return cfetssecuritysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtype(const ::std::string& value) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtype(const char* value) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtype(const char* value, size_t size) {
  
  cfetssecuritysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}
inline ::std::string* MDCfetsBenchmark::mutable_cfetssecuritysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  return cfetssecuritysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_cfetssecuritysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
  
  return cfetssecuritysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_cfetssecuritysubtype(::std::string* cfetssecuritysubtype) {
  if (cfetssecuritysubtype != NULL) {
    
  } else {
    
  }
  cfetssecuritysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubType)
}

// optional string CfetsSecuritySubTypeCode = 26;
inline void MDCfetsBenchmark::clear_cfetssecuritysubtypecode() {
  cfetssecuritysubtypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBenchmark::cfetssecuritysubtypecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  return cfetssecuritysubtypecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const ::std::string& value) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const char* value) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
inline void MDCfetsBenchmark::set_cfetssecuritysubtypecode(const char* value, size_t size) {
  
  cfetssecuritysubtypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}
inline ::std::string* MDCfetsBenchmark::mutable_cfetssecuritysubtypecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  return cfetssecuritysubtypecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBenchmark::release_cfetssecuritysubtypecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
  
  return cfetssecuritysubtypecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBenchmark::set_allocated_cfetssecuritysubtypecode(::std::string* cfetssecuritysubtypecode) {
  if (cfetssecuritysubtypecode != NULL) {
    
  } else {
    
  }
  cfetssecuritysubtypecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cfetssecuritysubtypecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.CfetsSecuritySubTypeCode)
}

// optional int32 RateSwapCurveInsertMethod = 27;
inline void MDCfetsBenchmark::clear_rateswapcurveinsertmethod() {
  rateswapcurveinsertmethod_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBenchmark::rateswapcurveinsertmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurveInsertMethod)
  return rateswapcurveinsertmethod_;
}
inline void MDCfetsBenchmark::set_rateswapcurveinsertmethod(::google::protobuf::int32 value) {
  
  rateswapcurveinsertmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurveInsertMethod)
}

// optional bool ContingencyIndicator = 28;
inline void MDCfetsBenchmark::clear_contingencyindicator() {
  contingencyindicator_ = false;
}
inline bool MDCfetsBenchmark::contingencyindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ContingencyIndicator)
  return contingencyindicator_;
}
inline void MDCfetsBenchmark::set_contingencyindicator(bool value) {
  
  contingencyindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.ContingencyIndicator)
}

// optional int32 BenchmarkType = 99;
inline void MDCfetsBenchmark::clear_benchmarktype() {
  benchmarktype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBenchmark::benchmarktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BenchmarkType)
  return benchmarktype_;
}
inline void MDCfetsBenchmark::set_benchmarktype(::google::protobuf::int32 value) {
  
  benchmarktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBenchmark.BenchmarkType)
}

// repeated .com.htsc.mdc.insight.model.YieldCurve YieldCurves = 100;
inline int MDCfetsBenchmark::yieldcurves_size() const {
  return yieldcurves_.size();
}
inline void MDCfetsBenchmark::clear_yieldcurves() {
  yieldcurves_.Clear();
}
inline const ::com::htsc::mdc::insight::model::YieldCurve& MDCfetsBenchmark::yieldcurves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Get(index);
}
inline ::com::htsc::mdc::insight::model::YieldCurve* MDCfetsBenchmark::mutable_yieldcurves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::YieldCurve* MDCfetsBenchmark::add_yieldcurves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >*
MDCfetsBenchmark::mutable_yieldcurves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return &yieldcurves_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve >&
MDCfetsBenchmark::yieldcurves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.YieldCurves)
  return yieldcurves_;
}

// optional .com.htsc.mdc.insight.model.ShiborData ShiborData = 101;
inline bool MDCfetsBenchmark::has_shibordata() const {
  return this != internal_default_instance() && shibordata_ != NULL;
}
inline void MDCfetsBenchmark::clear_shibordata() {
  if (GetArenaNoVirtual() == NULL && shibordata_ != NULL) delete shibordata_;
  shibordata_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ShiborData& MDCfetsBenchmark::shibordata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  return shibordata_ != NULL ? *shibordata_
                         : *::com::htsc::mdc::insight::model::ShiborData::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ShiborData* MDCfetsBenchmark::mutable_shibordata() {
  
  if (shibordata_ == NULL) {
    shibordata_ = new ::com::htsc::mdc::insight::model::ShiborData;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  return shibordata_;
}
inline ::com::htsc::mdc::insight::model::ShiborData* MDCfetsBenchmark::release_shibordata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
  
  ::com::htsc::mdc::insight::model::ShiborData* temp = shibordata_;
  shibordata_ = NULL;
  return temp;
}
inline void MDCfetsBenchmark::set_allocated_shibordata(::com::htsc::mdc::insight::model::ShiborData* shibordata) {
  delete shibordata_;
  shibordata_ = shibordata;
  if (shibordata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.ShiborData)
}

// optional .com.htsc.mdc.insight.model.LoanPrimeRate LoanPrimeRate = 102;
inline bool MDCfetsBenchmark::has_loanprimerate() const {
  return this != internal_default_instance() && loanprimerate_ != NULL;
}
inline void MDCfetsBenchmark::clear_loanprimerate() {
  if (GetArenaNoVirtual() == NULL && loanprimerate_ != NULL) delete loanprimerate_;
  loanprimerate_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::LoanPrimeRate& MDCfetsBenchmark::loanprimerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  return loanprimerate_ != NULL ? *loanprimerate_
                         : *::com::htsc::mdc::insight::model::LoanPrimeRate::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::LoanPrimeRate* MDCfetsBenchmark::mutable_loanprimerate() {
  
  if (loanprimerate_ == NULL) {
    loanprimerate_ = new ::com::htsc::mdc::insight::model::LoanPrimeRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  return loanprimerate_;
}
inline ::com::htsc::mdc::insight::model::LoanPrimeRate* MDCfetsBenchmark::release_loanprimerate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
  
  ::com::htsc::mdc::insight::model::LoanPrimeRate* temp = loanprimerate_;
  loanprimerate_ = NULL;
  return temp;
}
inline void MDCfetsBenchmark::set_allocated_loanprimerate(::com::htsc::mdc::insight::model::LoanPrimeRate* loanprimerate) {
  delete loanprimerate_;
  loanprimerate_ = loanprimerate;
  if (loanprimerate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.LoanPrimeRate)
}

// optional .com.htsc.mdc.insight.model.FixRepoRate FixRepoRate = 103;
inline bool MDCfetsBenchmark::has_fixreporate() const {
  return this != internal_default_instance() && fixreporate_ != NULL;
}
inline void MDCfetsBenchmark::clear_fixreporate() {
  if (GetArenaNoVirtual() == NULL && fixreporate_ != NULL) delete fixreporate_;
  fixreporate_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::FixRepoRate& MDCfetsBenchmark::fixreporate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  return fixreporate_ != NULL ? *fixreporate_
                         : *::com::htsc::mdc::insight::model::FixRepoRate::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::FixRepoRate* MDCfetsBenchmark::mutable_fixreporate() {
  
  if (fixreporate_ == NULL) {
    fixreporate_ = new ::com::htsc::mdc::insight::model::FixRepoRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  return fixreporate_;
}
inline ::com::htsc::mdc::insight::model::FixRepoRate* MDCfetsBenchmark::release_fixreporate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
  
  ::com::htsc::mdc::insight::model::FixRepoRate* temp = fixreporate_;
  fixreporate_ = NULL;
  return temp;
}
inline void MDCfetsBenchmark::set_allocated_fixreporate(::com::htsc::mdc::insight::model::FixRepoRate* fixreporate) {
  delete fixreporate_;
  fixreporate_ = fixreporate;
  if (fixreporate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.FixRepoRate)
}

// repeated .com.htsc.mdc.insight.model.RateSwapCurve RateSwapCurves = 104;
inline int MDCfetsBenchmark::rateswapcurves_size() const {
  return rateswapcurves_.size();
}
inline void MDCfetsBenchmark::clear_rateswapcurves() {
  rateswapcurves_.Clear();
}
inline const ::com::htsc::mdc::insight::model::RateSwapCurve& MDCfetsBenchmark::rateswapcurves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Get(index);
}
inline ::com::htsc::mdc::insight::model::RateSwapCurve* MDCfetsBenchmark::mutable_rateswapcurves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::RateSwapCurve* MDCfetsBenchmark::add_rateswapcurves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >*
MDCfetsBenchmark::mutable_rateswapcurves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return &rateswapcurves_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve >&
MDCfetsBenchmark::rateswapcurves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDCfetsBenchmark.RateSwapCurves)
  return rateswapcurves_;
}

// optional .com.htsc.mdc.insight.model.BondValuation BondValuation = 105;
inline bool MDCfetsBenchmark::has_bondvaluation() const {
  return this != internal_default_instance() && bondvaluation_ != NULL;
}
inline void MDCfetsBenchmark::clear_bondvaluation() {
  if (GetArenaNoVirtual() == NULL && bondvaluation_ != NULL) delete bondvaluation_;
  bondvaluation_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::BondValuation& MDCfetsBenchmark::bondvaluation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  return bondvaluation_ != NULL ? *bondvaluation_
                         : *::com::htsc::mdc::insight::model::BondValuation::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::BondValuation* MDCfetsBenchmark::mutable_bondvaluation() {
  
  if (bondvaluation_ == NULL) {
    bondvaluation_ = new ::com::htsc::mdc::insight::model::BondValuation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  return bondvaluation_;
}
inline ::com::htsc::mdc::insight::model::BondValuation* MDCfetsBenchmark::release_bondvaluation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
  
  ::com::htsc::mdc::insight::model::BondValuation* temp = bondvaluation_;
  bondvaluation_ = NULL;
  return temp;
}
inline void MDCfetsBenchmark::set_allocated_bondvaluation(::com::htsc::mdc::insight::model::BondValuation* bondvaluation) {
  delete bondvaluation_;
  bondvaluation_ = bondvaluation;
  if (bondvaluation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondValuation)
}

// optional .com.htsc.mdc.insight.model.BondIndex BondIndex = 106;
inline bool MDCfetsBenchmark::has_bondindex() const {
  return this != internal_default_instance() && bondindex_ != NULL;
}
inline void MDCfetsBenchmark::clear_bondindex() {
  if (GetArenaNoVirtual() == NULL && bondindex_ != NULL) delete bondindex_;
  bondindex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::BondIndex& MDCfetsBenchmark::bondindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  return bondindex_ != NULL ? *bondindex_
                         : *::com::htsc::mdc::insight::model::BondIndex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::BondIndex* MDCfetsBenchmark::mutable_bondindex() {
  
  if (bondindex_ == NULL) {
    bondindex_ = new ::com::htsc::mdc::insight::model::BondIndex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  return bondindex_;
}
inline ::com::htsc::mdc::insight::model::BondIndex* MDCfetsBenchmark::release_bondindex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
  
  ::com::htsc::mdc::insight::model::BondIndex* temp = bondindex_;
  bondindex_ = NULL;
  return temp;
}
inline void MDCfetsBenchmark::set_allocated_bondindex(::com::htsc::mdc::insight::model::BondIndex* bondindex) {
  delete bondindex_;
  bondindex_ = bondindex;
  if (bondindex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBenchmark.BondIndex)
}

inline const MDCfetsBenchmark* MDCfetsBenchmark::internal_default_instance() {
  return &MDCfetsBenchmark_default_instance_.get();
}
// -------------------------------------------------------------------

// YieldCurve_Curve

// optional string CurveType = 1;
inline void YieldCurve_Curve::clear_curvetype() {
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& YieldCurve_Curve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  return curvetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve_Curve::set_curvetype(const ::std::string& value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
inline void YieldCurve_Curve::set_curvetype(const char* value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
inline void YieldCurve_Curve::set_curvetype(const char* value, size_t size) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}
inline ::std::string* YieldCurve_Curve::mutable_curvetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  return curvetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* YieldCurve_Curve::release_curvetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
  
  return curvetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve_Curve::set_allocated_curvetype(::std::string* curvetype) {
  if (curvetype != NULL) {
    
  } else {
    
  }
  curvetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.Curve.CurveType)
}

// optional string DataSource = 2;
inline void YieldCurve_Curve::clear_datasource() {
  datasource_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& YieldCurve_Curve::datasource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  return datasource_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve_Curve::set_datasource(const ::std::string& value) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
inline void YieldCurve_Curve::set_datasource(const char* value) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
inline void YieldCurve_Curve::set_datasource(const char* value, size_t size) {
  
  datasource_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}
inline ::std::string* YieldCurve_Curve::mutable_datasource() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  return datasource_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* YieldCurve_Curve::release_datasource() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
  
  return datasource_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve_Curve::set_allocated_datasource(::std::string* datasource) {
  if (datasource != NULL) {
    
  } else {
    
  }
  datasource_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datasource);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.Curve.DataSource)
}

// optional double YieldPx = 3;
inline void YieldCurve_Curve::clear_yieldpx() {
  yieldpx_ = 0;
}
inline double YieldCurve_Curve::yieldpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.YieldPx)
  return yieldpx_;
}
inline void YieldCurve_Curve::set_yieldpx(double value) {
  
  yieldpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.YieldPx)
}

// optional double Spread = 4;
inline void YieldCurve_Curve::clear_spread() {
  spread_ = 0;
}
inline double YieldCurve_Curve::spread() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curve.Spread)
  return spread_;
}
inline void YieldCurve_Curve::set_spread(double value) {
  
  spread_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.Curve.Spread)
}

inline const YieldCurve_Curve* YieldCurve_Curve::internal_default_instance() {
  return &YieldCurve_Curve_default_instance_.get();
}
// -------------------------------------------------------------------

// YieldCurve

// optional string YieldTerm = 1;
inline void YieldCurve::clear_yieldterm() {
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& YieldCurve::yieldterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  return yieldterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_yieldterm(const ::std::string& value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
inline void YieldCurve::set_yieldterm(const char* value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
inline void YieldCurve::set_yieldterm(const char* value, size_t size) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}
inline ::std::string* YieldCurve::mutable_yieldterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  return yieldterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* YieldCurve::release_yieldterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
  
  return yieldterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_allocated_yieldterm(::std::string* yieldterm) {
  if (yieldterm != NULL) {
    
  } else {
    
  }
  yieldterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.YieldTerm)
}

// optional string YieldTermType = 2;
inline void YieldCurve::clear_yieldtermtype() {
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& YieldCurve::yieldtermtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  return yieldtermtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_yieldtermtype(const ::std::string& value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
inline void YieldCurve::set_yieldtermtype(const char* value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
inline void YieldCurve::set_yieldtermtype(const char* value, size_t size) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}
inline ::std::string* YieldCurve::mutable_yieldtermtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  return yieldtermtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* YieldCurve::release_yieldtermtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
  
  return yieldtermtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_allocated_yieldtermtype(::std::string* yieldtermtype) {
  if (yieldtermtype != NULL) {
    
  } else {
    
  }
  yieldtermtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldtermtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.YieldTermType)
}

// optional string SettlDate = 3;
inline void YieldCurve::clear_settldate() {
  settldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& YieldCurve::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  return settldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_settldate(const ::std::string& value) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
inline void YieldCurve::set_settldate(const char* value) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
inline void YieldCurve::set_settldate(const char* value, size_t size) {
  
  settldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}
inline ::std::string* YieldCurve::mutable_settldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  return settldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* YieldCurve::release_settldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
  
  return settldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void YieldCurve::set_allocated_settldate(::std::string* settldate) {
  if (settldate != NULL) {
    
  } else {
    
  }
  settldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.YieldCurve.SettlDate)
}

// optional double SecTermYearly = 4;
inline void YieldCurve::clear_sectermyearly() {
  sectermyearly_ = 0;
}
inline double YieldCurve::sectermyearly() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.SecTermYearly)
  return sectermyearly_;
}
inline void YieldCurve::set_sectermyearly(double value) {
  
  sectermyearly_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.YieldCurve.SecTermYearly)
}

// repeated .com.htsc.mdc.insight.model.YieldCurve.Curve Curves = 100;
inline int YieldCurve::curves_size() const {
  return curves_.size();
}
inline void YieldCurve::clear_curves() {
  curves_.Clear();
}
inline const ::com::htsc::mdc::insight::model::YieldCurve_Curve& YieldCurve::curves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Get(index);
}
inline ::com::htsc::mdc::insight::model::YieldCurve_Curve* YieldCurve::mutable_curves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::YieldCurve_Curve* YieldCurve::add_curves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >*
YieldCurve::mutable_curves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return &curves_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::YieldCurve_Curve >&
YieldCurve::curves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.YieldCurve.Curves)
  return curves_;
}

inline const YieldCurve* YieldCurve::internal_default_instance() {
  return &YieldCurve_default_instance_.get();
}
// -------------------------------------------------------------------

// ShiborData

// optional string EffectiveDate = 1;
inline void ShiborData::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ShiborData::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
inline void ShiborData::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
inline void ShiborData::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}
inline ::std::string* ShiborData::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ShiborData::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.EffectiveDate)
}

// optional double Shibor = 2;
inline void ShiborData::clear_shibor() {
  shibor_ = 0;
}
inline double ShiborData::shibor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.Shibor)
  return shibor_;
}
inline void ShiborData::set_shibor(double value) {
  
  shibor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.Shibor)
}

// optional double ShiborChange = 3;
inline void ShiborData::clear_shiborchange() {
  shiborchange_ = 0;
}
inline double ShiborData::shiborchange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborChange)
  return shiborchange_;
}
inline void ShiborData::set_shiborchange(double value) {
  
  shiborchange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborChange)
}

// optional double ShiborQuote = 4;
inline void ShiborData::clear_shiborquote() {
  shiborquote_ = 0;
}
inline double ShiborData::shiborquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborQuote)
  return shiborquote_;
}
inline void ShiborData::set_shiborquote(double value) {
  
  shiborquote_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborQuote)
}

// optional string ShiborQuoteTime = 5;
inline void ShiborData::clear_shiborquotetime() {
  shiborquotetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ShiborData::shiborquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  return shiborquotetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_shiborquotetime(const ::std::string& value) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
inline void ShiborData::set_shiborquotetime(const char* value) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
inline void ShiborData::set_shiborquotetime(const char* value, size_t size) {
  
  shiborquotetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}
inline ::std::string* ShiborData::mutable_shiborquotetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  return shiborquotetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ShiborData::release_shiborquotetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
  
  return shiborquotetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_allocated_shiborquotetime(::std::string* shiborquotetime) {
  if (shiborquotetime != NULL) {
    
  } else {
    
  }
  shiborquotetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), shiborquotetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.ShiborQuoteTime)
}

// optional string QuotePartyID = 6;
inline void ShiborData::clear_quotepartyid() {
  quotepartyid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ShiborData::quotepartyid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  return quotepartyid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_quotepartyid(const ::std::string& value) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
inline void ShiborData::set_quotepartyid(const char* value) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
inline void ShiborData::set_quotepartyid(const char* value, size_t size) {
  
  quotepartyid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}
inline ::std::string* ShiborData::mutable_quotepartyid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  return quotepartyid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ShiborData::release_quotepartyid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
  
  return quotepartyid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_allocated_quotepartyid(::std::string* quotepartyid) {
  if (quotepartyid != NULL) {
    
  } else {
    
  }
  quotepartyid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotepartyid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.QuotePartyID)
}

// optional string QuotePartyShortName = 7;
inline void ShiborData::clear_quotepartyshortname() {
  quotepartyshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ShiborData::quotepartyshortname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  return quotepartyshortname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_quotepartyshortname(const ::std::string& value) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
inline void ShiborData::set_quotepartyshortname(const char* value) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
inline void ShiborData::set_quotepartyshortname(const char* value, size_t size) {
  
  quotepartyshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}
inline ::std::string* ShiborData::mutable_quotepartyshortname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  return quotepartyshortname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ShiborData::release_quotepartyshortname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
  
  return quotepartyshortname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_allocated_quotepartyshortname(::std::string* quotepartyshortname) {
  if (quotepartyshortname != NULL) {
    
  } else {
    
  }
  quotepartyshortname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotepartyshortname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.QuotePartyShortName)
}

// optional double MovingAverage = 8;
inline void ShiborData::clear_movingaverage() {
  movingaverage_ = 0;
}
inline double ShiborData::movingaverage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.MovingAverage)
  return movingaverage_;
}
inline void ShiborData::set_movingaverage(double value) {
  
  movingaverage_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.MovingAverage)
}

// optional string MovingAverageType = 9;
inline void ShiborData::clear_movingaveragetype() {
  movingaveragetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ShiborData::movingaveragetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  return movingaveragetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_movingaveragetype(const ::std::string& value) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
inline void ShiborData::set_movingaveragetype(const char* value) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
inline void ShiborData::set_movingaveragetype(const char* value, size_t size) {
  
  movingaveragetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}
inline ::std::string* ShiborData::mutable_movingaveragetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  return movingaveragetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ShiborData::release_movingaveragetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
  
  return movingaveragetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ShiborData::set_allocated_movingaveragetype(::std::string* movingaveragetype) {
  if (movingaveragetype != NULL) {
    
  } else {
    
  }
  movingaveragetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), movingaveragetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ShiborData.MovingAverageType)
}

inline const ShiborData* ShiborData::internal_default_instance() {
  return &ShiborData_default_instance_.get();
}
// -------------------------------------------------------------------

// LoanPrimeRate

// optional string EffectiveDate = 1;
inline void LoanPrimeRate::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& LoanPrimeRate::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void LoanPrimeRate::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
inline void LoanPrimeRate::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
inline void LoanPrimeRate::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}
inline ::std::string* LoanPrimeRate::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* LoanPrimeRate::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void LoanPrimeRate::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.LoanPrimeRate.EffectiveDate)
}

// optional double Lpr = 2;
inline void LoanPrimeRate::clear_lpr() {
  lpr_ = 0;
}
inline double LoanPrimeRate::lpr() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.LoanPrimeRate.Lpr)
  return lpr_;
}
inline void LoanPrimeRate::set_lpr(double value) {
  
  lpr_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.LoanPrimeRate.Lpr)
}

inline const LoanPrimeRate* LoanPrimeRate::internal_default_instance() {
  return &LoanPrimeRate_default_instance_.get();
}
// -------------------------------------------------------------------

// FixRepoRate

// optional string EffectiveDate = 1;
inline void FixRepoRate::clear_effectivedate() {
  effectivedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& FixRepoRate::effectivedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  return effectivedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FixRepoRate::set_effectivedate(const ::std::string& value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
inline void FixRepoRate::set_effectivedate(const char* value) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
inline void FixRepoRate::set_effectivedate(const char* value, size_t size) {
  
  effectivedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}
inline ::std::string* FixRepoRate::mutable_effectivedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  return effectivedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* FixRepoRate::release_effectivedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
  
  return effectivedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void FixRepoRate::set_allocated_effectivedate(::std::string* effectivedate) {
  if (effectivedate != NULL) {
    
  } else {
    
  }
  effectivedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), effectivedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.FixRepoRate.EffectiveDate)
}

// optional double Rate = 2;
inline void FixRepoRate::clear_rate() {
  rate_ = 0;
}
inline double FixRepoRate::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.Rate)
  return rate_;
}
inline void FixRepoRate::set_rate(double value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.Rate)
}

// optional int32 TermUpperLimit = 3;
inline void FixRepoRate::clear_termupperlimit() {
  termupperlimit_ = 0;
}
inline ::google::protobuf::int32 FixRepoRate::termupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.TermUpperLimit)
  return termupperlimit_;
}
inline void FixRepoRate::set_termupperlimit(::google::protobuf::int32 value) {
  
  termupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.TermUpperLimit)
}

// optional int32 TermFloorLimit = 4;
inline void FixRepoRate::clear_termfloorlimit() {
  termfloorlimit_ = 0;
}
inline ::google::protobuf::int32 FixRepoRate::termfloorlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.FixRepoRate.TermFloorLimit)
  return termfloorlimit_;
}
inline void FixRepoRate::set_termfloorlimit(::google::protobuf::int32 value) {
  
  termfloorlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.FixRepoRate.TermFloorLimit)
}

inline const FixRepoRate* FixRepoRate::internal_default_instance() {
  return &FixRepoRate_default_instance_.get();
}
// -------------------------------------------------------------------

// RateSwapCurve_Curve

// optional string CurveType = 1;
inline void RateSwapCurve_Curve::clear_curvetype() {
  curvetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapCurve_Curve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  return curvetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve_Curve::set_curvetype(const ::std::string& value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
inline void RateSwapCurve_Curve::set_curvetype(const char* value) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
inline void RateSwapCurve_Curve::set_curvetype(const char* value, size_t size) {
  
  curvetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}
inline ::std::string* RateSwapCurve_Curve::mutable_curvetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  return curvetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapCurve_Curve::release_curvetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
  
  return curvetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve_Curve::set_allocated_curvetype(::std::string* curvetype) {
  if (curvetype != NULL) {
    
  } else {
    
  }
  curvetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.Curve.CurveType)
}

// optional double YieldPx = 2;
inline void RateSwapCurve_Curve::clear_yieldpx() {
  yieldpx_ = 0;
}
inline double RateSwapCurve_Curve::yieldpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curve.YieldPx)
  return yieldpx_;
}
inline void RateSwapCurve_Curve::set_yieldpx(double value) {
  
  yieldpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.Curve.YieldPx)
}

inline const RateSwapCurve_Curve* RateSwapCurve_Curve::internal_default_instance() {
  return &RateSwapCurve_Curve_default_instance_.get();
}
// -------------------------------------------------------------------

// RateSwapCurve

// optional string YieldTerm = 1;
inline void RateSwapCurve::clear_yieldterm() {
  yieldterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapCurve::yieldterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  return yieldterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_yieldterm(const ::std::string& value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
inline void RateSwapCurve::set_yieldterm(const char* value) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
inline void RateSwapCurve::set_yieldterm(const char* value, size_t size) {
  
  yieldterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}
inline ::std::string* RateSwapCurve::mutable_yieldterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  return yieldterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapCurve::release_yieldterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
  
  return yieldterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_allocated_yieldterm(::std::string* yieldterm) {
  if (yieldterm != NULL) {
    
  } else {
    
  }
  yieldterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.YieldTerm)
}

// optional string YieldTermType = 2;
inline void RateSwapCurve::clear_yieldtermtype() {
  yieldtermtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapCurve::yieldtermtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  return yieldtermtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_yieldtermtype(const ::std::string& value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
inline void RateSwapCurve::set_yieldtermtype(const char* value) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
inline void RateSwapCurve::set_yieldtermtype(const char* value, size_t size) {
  
  yieldtermtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}
inline ::std::string* RateSwapCurve::mutable_yieldtermtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  return yieldtermtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapCurve::release_yieldtermtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
  
  return yieldtermtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_allocated_yieldtermtype(::std::string* yieldtermtype) {
  if (yieldtermtype != NULL) {
    
  } else {
    
  }
  yieldtermtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), yieldtermtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.YieldTermType)
}

// optional string CalculateDate = 3;
inline void RateSwapCurve::clear_calculatedate() {
  calculatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapCurve::calculatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  return calculatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_calculatedate(const ::std::string& value) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
inline void RateSwapCurve::set_calculatedate(const char* value) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
inline void RateSwapCurve::set_calculatedate(const char* value, size_t size) {
  
  calculatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}
inline ::std::string* RateSwapCurve::mutable_calculatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  return calculatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapCurve::release_calculatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
  
  return calculatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapCurve::set_allocated_calculatedate(::std::string* calculatedate) {
  if (calculatedate != NULL) {
    
  } else {
    
  }
  calculatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapCurve.CalculateDate)
}

// repeated .com.htsc.mdc.insight.model.RateSwapCurve.Curve Curves = 100;
inline int RateSwapCurve::curves_size() const {
  return curves_.size();
}
inline void RateSwapCurve::clear_curves() {
  curves_.Clear();
}
inline const ::com::htsc::mdc::insight::model::RateSwapCurve_Curve& RateSwapCurve::curves(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Get(index);
}
inline ::com::htsc::mdc::insight::model::RateSwapCurve_Curve* RateSwapCurve::mutable_curves(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::RateSwapCurve_Curve* RateSwapCurve::add_curves() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >*
RateSwapCurve::mutable_curves() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return &curves_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::RateSwapCurve_Curve >&
RateSwapCurve::curves() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.RateSwapCurve.Curves)
  return curves_;
}

inline const RateSwapCurve* RateSwapCurve::internal_default_instance() {
  return &RateSwapCurve_default_instance_.get();
}
// -------------------------------------------------------------------

// BondValuation

// optional string TransactDate = 1;
inline void BondValuation::clear_transactdate() {
  transactdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondValuation::transactdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  return transactdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_transactdate(const ::std::string& value) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
inline void BondValuation::set_transactdate(const char* value) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
inline void BondValuation::set_transactdate(const char* value, size_t size) {
  
  transactdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}
inline ::std::string* BondValuation::mutable_transactdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  return transactdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondValuation::release_transactdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.TransactDate)
  
  return transactdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_allocated_transactdate(::std::string* transactdate) {
  if (transactdate != NULL) {
    
  } else {
    
  }
  transactdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.TransactDate)
}

// optional string ValuationDateTime = 2;
inline void BondValuation::clear_valuationdatetime() {
  valuationdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondValuation::valuationdatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  return valuationdatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_valuationdatetime(const ::std::string& value) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
inline void BondValuation::set_valuationdatetime(const char* value) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
inline void BondValuation::set_valuationdatetime(const char* value, size_t size) {
  
  valuationdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}
inline ::std::string* BondValuation::mutable_valuationdatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  return valuationdatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondValuation::release_valuationdatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
  
  return valuationdatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_allocated_valuationdatetime(::std::string* valuationdatetime) {
  if (valuationdatetime != NULL) {
    
  } else {
    
  }
  valuationdatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuationdatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.ValuationDateTime)
}

// optional string Currency = 3;
inline void BondValuation::clear_currency() {
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondValuation::currency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Currency)
  return currency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_currency(const ::std::string& value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Currency)
}
inline void BondValuation::set_currency(const char* value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.Currency)
}
inline void BondValuation::set_currency(const char* value, size_t size) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.Currency)
}
inline ::std::string* BondValuation::mutable_currency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.Currency)
  return currency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondValuation::release_currency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.Currency)
  
  return currency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_allocated_currency(::std::string* currency) {
  if (currency != NULL) {
    
  } else {
    
  }
  currency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.Currency)
}

// optional string CouponRateType = 4;
inline void BondValuation::clear_couponratetype() {
  couponratetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondValuation::couponratetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  return couponratetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_couponratetype(const ::std::string& value) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
inline void BondValuation::set_couponratetype(const char* value) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
inline void BondValuation::set_couponratetype(const char* value, size_t size) {
  
  couponratetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}
inline ::std::string* BondValuation::mutable_couponratetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  return couponratetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondValuation::release_couponratetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
  
  return couponratetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondValuation::set_allocated_couponratetype(::std::string* couponratetype) {
  if (couponratetype != NULL) {
    
  } else {
    
  }
  couponratetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), couponratetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondValuation.CouponRateType)
}

// optional double ValuationAmt = 5;
inline void BondValuation::clear_valuationamt() {
  valuationamt_ = 0;
}
inline double BondValuation::valuationamt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationAmt)
  return valuationamt_;
}
inline void BondValuation::set_valuationamt(double value) {
  
  valuationamt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationAmt)
}

// optional double DirtyPrice = 6;
inline void BondValuation::clear_dirtyprice() {
  dirtyprice_ = 0;
}
inline double BondValuation::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.DirtyPrice)
  return dirtyprice_;
}
inline void BondValuation::set_dirtyprice(double value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.DirtyPrice)
}

// optional double SpreadCNYDirtyPrice = 7;
inline void BondValuation::clear_spreadcnydirtyprice() {
  spreadcnydirtyprice_ = 0;
}
inline double BondValuation::spreadcnydirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadCNYDirtyPrice)
  return spreadcnydirtyprice_;
}
inline void BondValuation::set_spreadcnydirtyprice(double value) {
  
  spreadcnydirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadCNYDirtyPrice)
}

// optional double SpreadCNYPrice = 8;
inline void BondValuation::clear_spreadcnyprice() {
  spreadcnyprice_ = 0;
}
inline double BondValuation::spreadcnyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadCNYPrice)
  return spreadcnyprice_;
}
inline void BondValuation::set_spreadcnyprice(double value) {
  
  spreadcnyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadCNYPrice)
}

// optional double ValuationYield = 9;
inline void BondValuation::clear_valuationyield() {
  valuationyield_ = 0;
}
inline double BondValuation::valuationyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ValuationYield)
  return valuationyield_;
}
inline void BondValuation::set_valuationyield(double value) {
  
  valuationyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ValuationYield)
}

// optional double SpreadDuration = 10;
inline void BondValuation::clear_spreadduration() {
  spreadduration_ = 0;
}
inline double BondValuation::spreadduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadDuration)
  return spreadduration_;
}
inline void BondValuation::set_spreadduration(double value) {
  
  spreadduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadDuration)
}

// optional double SpreadConvexity = 11;
inline void BondValuation::clear_spreadconvexity() {
  spreadconvexity_ = 0;
}
inline double BondValuation::spreadconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadConvexity)
  return spreadconvexity_;
}
inline void BondValuation::set_spreadconvexity(double value) {
  
  spreadconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadConvexity)
}

// optional double Duration = 12;
inline void BondValuation::clear_duration() {
  duration_ = 0;
}
inline double BondValuation::duration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Duration)
  return duration_;
}
inline void BondValuation::set_duration(double value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Duration)
}

// optional double ModifyDuration = 13;
inline void BondValuation::clear_modifyduration() {
  modifyduration_ = 0;
}
inline double BondValuation::modifyduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.ModifyDuration)
  return modifyduration_;
}
inline void BondValuation::set_modifyduration(double value) {
  
  modifyduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.ModifyDuration)
}

// optional double Convexity = 14;
inline void BondValuation::clear_convexity() {
  convexity_ = 0;
}
inline double BondValuation::convexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.Convexity)
  return convexity_;
}
inline void BondValuation::set_convexity(double value) {
  
  convexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.Convexity)
}

// optional double SpreadPx = 15;
inline void BondValuation::clear_spreadpx() {
  spreadpx_ = 0;
}
inline double BondValuation::spreadpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondValuation.SpreadPx)
  return spreadpx_;
}
inline void BondValuation::set_spreadpx(double value) {
  
  spreadpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondValuation.SpreadPx)
}

inline const BondValuation* BondValuation::internal_default_instance() {
  return &BondValuation_default_instance_.get();
}
// -------------------------------------------------------------------

// BondIndex

// optional int32 KCurveType = 1;
inline void BondIndex::clear_kcurvetype() {
  kcurvetype_ = 0;
}
inline ::google::protobuf::int32 BondIndex::kcurvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.KCurveType)
  return kcurvetype_;
}
inline void BondIndex::set_kcurvetype(::google::protobuf::int32 value) {
  
  kcurvetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.KCurveType)
}

// optional double LastPx = 2;
inline void BondIndex::clear_lastpx() {
  lastpx_ = 0;
}
inline double BondIndex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.LastPx)
  return lastpx_;
}
inline void BondIndex::set_lastpx(double value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.LastPx)
}

// optional double OpenPx = 3;
inline void BondIndex::clear_openpx() {
  openpx_ = 0;
}
inline double BondIndex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.OpenPx)
  return openpx_;
}
inline void BondIndex::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.OpenPx)
}

// optional double ClosePx = 4;
inline void BondIndex::clear_closepx() {
  closepx_ = 0;
}
inline double BondIndex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.ClosePx)
  return closepx_;
}
inline void BondIndex::set_closepx(double value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.ClosePx)
}

// optional double HighPx = 5;
inline void BondIndex::clear_highpx() {
  highpx_ = 0;
}
inline double BondIndex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.HighPx)
  return highpx_;
}
inline void BondIndex::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.HighPx)
}

// optional double LowPx = 6;
inline void BondIndex::clear_lowpx() {
  lowpx_ = 0;
}
inline double BondIndex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.LowPx)
  return lowpx_;
}
inline void BondIndex::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.LowPx)
}

// optional double TotalVolumeTradeMillion = 7;
inline void BondIndex::clear_totalvolumetrademillion() {
  totalvolumetrademillion_ = 0;
}
inline double BondIndex::totalvolumetrademillion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.TotalVolumeTradeMillion)
  return totalvolumetrademillion_;
}
inline void BondIndex::set_totalvolumetrademillion(double value) {
  
  totalvolumetrademillion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.TotalVolumeTradeMillion)
}

// optional double TotalValueTradeMillion = 8;
inline void BondIndex::clear_totalvaluetrademillion() {
  totalvaluetrademillion_ = 0;
}
inline double BondIndex::totalvaluetrademillion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondIndex.TotalValueTradeMillion)
  return totalvaluetrademillion_;
}
inline void BondIndex::set_totalvaluetrademillion(double value) {
  
  totalvaluetrademillion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondIndex.TotalValueTradeMillion)
}

inline const BondIndex* BondIndex::internal_default_instance() {
  return &BondIndex_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsBenchmark_2eproto__INCLUDED
