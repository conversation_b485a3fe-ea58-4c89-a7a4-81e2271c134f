syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADUpsDownsAnalysisProtos";
option optimize_for = SPEED;

// ADUpsDownsAnalysis message represents market ups/downs analysis for securities
message ADUpsDownsAnalysis {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Ups/downs count statistics
    ADUpsDownsCount UpsDownsCount = 7;
    
    // Ups/downs limit count statistics
    ADUpsDownsLimitCount UpsDownsLimitCount = 8;
    
    // Detailed partition analysis
    repeated ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 10;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 11;
    
    // Data scaling factor (power of 10 multiplier for percentage fields)
    int32 DataMultiplePowerOf10 = 12;
}

// ADUpsDownsCount represents basic ups/downs counting statistics
message ADUpsDownsCount {
    // Number of securities that went up
    int32 Ups = 1;
    
    // Number of securities that went down
    int32 Downs = 2;
    
    // Number of securities that remained equal
    int32 Equals = 3;
    
    // Previous period ups count
    int32 PreUps = 4;
    
    // Previous period downs count
    int32 PreDowns = 5;
    
    // Previous period equals count
    int32 PreEquals = 6;
    
    // Percentage of ups in current period
    double UpsPercent = 7;
    
    // Percentage of ups in previous period
    double PreUpsPercent = 8;
    
    // List of leading up security IDs
    repeated string LeadingUpIds = 9;
}

// ADUpsDownsLimitCount represents limit up/down statistics
message ADUpsDownsLimitCount {
    // Number of securities that didn't reach limit price
    int32 NoReachedLimitPx = 1;
    
    // Number of securities that hit upper limit
    int32 UpLimits = 2;
    
    // Number of securities that hit lower limit
    int32 DownLimits = 3;
    
    // Previous period count of securities that didn't reach limit
    int32 PreNoReachedLimitPx = 4;
    
    // Previous period upper limit count
    int32 PreUpLimits = 5;
    
    // Previous period lower limit count
    int32 PreDownLimits = 6;
    
    // Previous period average change percentage for up limits
    double PreUpLimitsAverageChangePercent = 7;
    
    // Current period up limits percentage
    double UpLimitsPercent = 8;
}

// ADUpsDownsPartitionDetail represents detailed partition analysis
message ADUpsDownsPartitionDetail {
    // Number of securities in this partition
    int32 Numbers = 1;
    
    // Change percentage for this partition (scaled by DataMultiplePowerOf10)
    int32 PartitionChangePercent = 2;
}
