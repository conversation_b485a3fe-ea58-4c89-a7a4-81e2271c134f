// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDIndex.proto

#ifndef PROTOBUF_MDIndex_2eproto__INCLUDED
#define PROTOBUF_MDIndex_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDIndex_2eproto();
void protobuf_InitDefaults_MDIndex_2eproto();
void protobuf_AssignDesc_MDIndex_2eproto();
void protobuf_ShutdownFile_MDIndex_2eproto();

class MDIndex;

// ===================================================================

class MDIndex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDIndex) */ {
 public:
  MDIndex();
  virtual ~MDIndex();

  MDIndex(const MDIndex& from);

  inline MDIndex& operator=(const MDIndex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDIndex& default_instance();

  static const MDIndex* internal_default_instance();

  void Swap(MDIndex* other);

  // implements Message ----------------------------------------------

  inline MDIndex* New() const { return New(NULL); }

  MDIndex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDIndex& from);
  void MergeFrom(const MDIndex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDIndex* other);
  void UnsafeMergeFrom(const MDIndex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 ChannelNo = 19;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 19;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int32 ExchangeDate = 20;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 20;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 21;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 21;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 TotalBuyVolumeTrade = 22;
  void clear_totalbuyvolumetrade();
  static const int kTotalBuyVolumeTradeFieldNumber = 22;
  ::google::protobuf::int64 totalbuyvolumetrade() const;
  void set_totalbuyvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalBuyValueTrade = 23;
  void clear_totalbuyvaluetrade();
  static const int kTotalBuyValueTradeFieldNumber = 23;
  ::google::protobuf::int64 totalbuyvaluetrade() const;
  void set_totalbuyvaluetrade(::google::protobuf::int64 value);

  // optional int64 TotalBuyNumber = 24;
  void clear_totalbuynumber();
  static const int kTotalBuyNumberFieldNumber = 24;
  ::google::protobuf::int64 totalbuynumber() const;
  void set_totalbuynumber(::google::protobuf::int64 value);

  // optional int64 TotalSellVolumeTrade = 25;
  void clear_totalsellvolumetrade();
  static const int kTotalSellVolumeTradeFieldNumber = 25;
  ::google::protobuf::int64 totalsellvolumetrade() const;
  void set_totalsellvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalSellValueTrade = 26;
  void clear_totalsellvaluetrade();
  static const int kTotalSellValueTradeFieldNumber = 26;
  ::google::protobuf::int64 totalsellvaluetrade() const;
  void set_totalsellvaluetrade(::google::protobuf::int64 value);

  // optional int64 TotalSellNumber = 27;
  void clear_totalsellnumber();
  static const int kTotalSellNumberFieldNumber = 27;
  ::google::protobuf::int64 totalsellnumber() const;
  void set_totalsellnumber(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 28;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 28;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDIndex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 totalbuyvolumetrade_;
  ::google::protobuf::int64 totalbuyvaluetrade_;
  ::google::protobuf::int64 totalbuynumber_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int64 totalsellvolumetrade_;
  ::google::protobuf::int64 totalsellvaluetrade_;
  ::google::protobuf::int64 totalsellnumber_;
  ::google::protobuf::int32 delaytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDIndex_2eproto_impl();
  friend void  protobuf_AddDesc_MDIndex_2eproto_impl();
  friend void protobuf_AssignDesc_MDIndex_2eproto();
  friend void protobuf_ShutdownFile_MDIndex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDIndex> MDIndex_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDIndex

// optional string HTSCSecurityID = 1;
inline void MDIndex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIndex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIndex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
inline void MDIndex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
inline void MDIndex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}
inline ::std::string* MDIndex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIndex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIndex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIndex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDIndex::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDIndex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MDDate)
  return mddate_;
}
inline void MDIndex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MDDate)
}

// optional int32 MDTime = 3;
inline void MDIndex::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDIndex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MDTime)
  return mdtime_;
}
inline void MDIndex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDIndex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DataTimestamp)
  return datatimestamp_;
}
inline void MDIndex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDIndex::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIndex::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIndex::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
inline void MDIndex::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
inline void MDIndex::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}
inline ::std::string* MDIndex::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIndex::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIndex::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIndex.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDIndex::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDIndex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDIndex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDIndex::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDIndex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDIndex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.securityType)
}

// optional int64 MaxPx = 8;
inline void MDIndex::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MaxPx)
  return maxpx_;
}
inline void MDIndex::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDIndex::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.MinPx)
  return minpx_;
}
inline void MDIndex::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDIndex::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.PreClosePx)
  return preclosepx_;
}
inline void MDIndex::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDIndex::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.NumTrades)
  return numtrades_;
}
inline void MDIndex::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDIndex::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDIndex::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDIndex::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDIndex::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void MDIndex::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.LastPx)
  return lastpx_;
}
inline void MDIndex::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.LastPx)
}

// optional int64 OpenPx = 15;
inline void MDIndex::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.OpenPx)
  return openpx_;
}
inline void MDIndex::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.OpenPx)
}

// optional int64 ClosePx = 16;
inline void MDIndex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ClosePx)
  return closepx_;
}
inline void MDIndex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ClosePx)
}

// optional int64 HighPx = 17;
inline void MDIndex::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.HighPx)
  return highpx_;
}
inline void MDIndex::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.HighPx)
}

// optional int64 LowPx = 18;
inline void MDIndex::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.LowPx)
  return lowpx_;
}
inline void MDIndex::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.LowPx)
}

// optional int32 ChannelNo = 19;
inline void MDIndex::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDIndex::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ChannelNo)
  return channelno_;
}
inline void MDIndex::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ChannelNo)
}

// optional int32 ExchangeDate = 20;
inline void MDIndex::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDIndex::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ExchangeDate)
  return exchangedate_;
}
inline void MDIndex::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ExchangeDate)
}

// optional int32 ExchangeTime = 21;
inline void MDIndex::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDIndex::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.ExchangeTime)
  return exchangetime_;
}
inline void MDIndex::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.ExchangeTime)
}

// optional int64 TotalBuyVolumeTrade = 22;
inline void MDIndex::clear_totalbuyvolumetrade() {
  totalbuyvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalbuyvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyVolumeTrade)
  return totalbuyvolumetrade_;
}
inline void MDIndex::set_totalbuyvolumetrade(::google::protobuf::int64 value) {
  
  totalbuyvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyVolumeTrade)
}

// optional int64 TotalBuyValueTrade = 23;
inline void MDIndex::clear_totalbuyvaluetrade() {
  totalbuyvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalbuyvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyValueTrade)
  return totalbuyvaluetrade_;
}
inline void MDIndex::set_totalbuyvaluetrade(::google::protobuf::int64 value) {
  
  totalbuyvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyValueTrade)
}

// optional int64 TotalBuyNumber = 24;
inline void MDIndex::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalBuyNumber)
  return totalbuynumber_;
}
inline void MDIndex::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalBuyNumber)
}

// optional int64 TotalSellVolumeTrade = 25;
inline void MDIndex::clear_totalsellvolumetrade() {
  totalsellvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalsellvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellVolumeTrade)
  return totalsellvolumetrade_;
}
inline void MDIndex::set_totalsellvolumetrade(::google::protobuf::int64 value) {
  
  totalsellvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellVolumeTrade)
}

// optional int64 TotalSellValueTrade = 26;
inline void MDIndex::clear_totalsellvaluetrade() {
  totalsellvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalsellvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellValueTrade)
  return totalsellvaluetrade_;
}
inline void MDIndex::set_totalsellvaluetrade(::google::protobuf::int64 value) {
  
  totalsellvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellValueTrade)
}

// optional int64 TotalSellNumber = 27;
inline void MDIndex::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIndex::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.TotalSellNumber)
  return totalsellnumber_;
}
inline void MDIndex::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.TotalSellNumber)
}

// optional int32 DataMultiplePowerOf10 = 28;
inline void MDIndex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDIndex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDIndex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DataMultiplePowerOf10)
}

// optional int32 DelayType = 101;
inline void MDIndex::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDIndex::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIndex.DelayType)
  return delaytype_;
}
inline void MDIndex::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIndex.DelayType)
}

inline const MDIndex* MDIndex::internal_default_instance() {
  return &MDIndex_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDIndex_2eproto__INCLUDED
