syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsODMSnapshot {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  ODMSnapshotDetail ODMSnapshotDetail = 16;
  int64 MessageNumber = 100;
}

message ODMSnapshotDetail {
  string InstrumentID = 1;
  double LastPrice = 2;
  double Volume = 3;
  double Turnover = 4;
  repeated ODMOrderBookEntry BidEntries = 5;
  repeated ODMOrderBookEntry AskEntries = 6;
}

message ODMOrderBookEntry {
  double Price = 1;
  double Quantity = 2;
}
