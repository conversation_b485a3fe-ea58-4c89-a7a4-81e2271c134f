// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLStatistics.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSLStatistics.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSLStatistics_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSLStatistics_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSLStatistics_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSLStatistics_2eproto() {
  protobuf_AddDesc_MDSLStatistics_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSLStatistics.proto");
  GOOGLE_CHECK(file != NULL);
  MDSLStatistics_descriptor_ = file->message_type(0);
  static const int MDSLStatistics_offsets_[12] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, htscborrowtradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, htsclendtradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, markettradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, datamultiplepowerof10_),
  };
  MDSLStatistics_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSLStatistics_descriptor_,
      MDSLStatistics::internal_default_instance(),
      MDSLStatistics_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSLStatistics),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLStatistics, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSLStatistics_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSLStatistics_descriptor_, MDSLStatistics::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSLStatistics_2eproto() {
  MDSLStatistics_default_instance_.Shutdown();
  delete MDSLStatistics_reflection_;
}

void protobuf_InitDefaults_MDSLStatistics_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSLStatistics_default_instance_.DefaultConstruct();
  MDSLStatistics_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSLStatistics_2eproto_once_);
void protobuf_InitDefaults_MDSLStatistics_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSLStatistics_2eproto_once_,
                 &protobuf_InitDefaults_MDSLStatistics_2eproto_impl);
}
void protobuf_AddDesc_MDSLStatistics_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSLStatistics_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDSLStatistics.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\027ESecurityIDSource.proto\032\023ESe"
    "curityType.proto\"\374\002\n\016MDSLStatistics\022\026\n\016H"
    "TSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006M"
    "DTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020Tr"
    "adingPhaseCode\030\005 \001(\t\022\?\n\020securityIDSource"
    "\030\006 \001(\0162%.com.htsc.mdc.model.ESecurityIDS"
    "ource\0227\n\014securityType\030\007 \001(\0162!.com.htsc.m"
    "dc.model.ESecurityType\022\035\n\025HtscBorrowTrad"
    "eVolume\030\010 \001(\003\022\033\n\023HtscLendTradeVolume\030\t \001"
    "(\003\022\031\n\021MarketTradeVolume\030\n \001(\003\022\021\n\tTradeDa"
    "te\030\013 \001(\t\022\035\n\025DataMultiplePowerOf10\030\014 \001(\005B"
    "7\n\032com.htsc.mdc.insight.modelB\024MDSLStati"
    "sticsProtosH\001\240\001\001b\006proto3", 544);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSLStatistics.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSLStatistics_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSLStatistics_2eproto_once_);
void protobuf_AddDesc_MDSLStatistics_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSLStatistics_2eproto_once_,
                 &protobuf_AddDesc_MDSLStatistics_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSLStatistics_2eproto {
  StaticDescriptorInitializer_MDSLStatistics_2eproto() {
    protobuf_AddDesc_MDSLStatistics_2eproto();
  }
} static_descriptor_initializer_MDSLStatistics_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSLStatistics::kHTSCSecurityIDFieldNumber;
const int MDSLStatistics::kMDDateFieldNumber;
const int MDSLStatistics::kMDTimeFieldNumber;
const int MDSLStatistics::kDataTimestampFieldNumber;
const int MDSLStatistics::kTradingPhaseCodeFieldNumber;
const int MDSLStatistics::kSecurityIDSourceFieldNumber;
const int MDSLStatistics::kSecurityTypeFieldNumber;
const int MDSLStatistics::kHtscBorrowTradeVolumeFieldNumber;
const int MDSLStatistics::kHtscLendTradeVolumeFieldNumber;
const int MDSLStatistics::kMarketTradeVolumeFieldNumber;
const int MDSLStatistics::kTradeDateFieldNumber;
const int MDSLStatistics::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSLStatistics::MDSLStatistics()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLStatistics_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSLStatistics)
}

void MDSLStatistics::InitAsDefaultInstance() {
}

MDSLStatistics::MDSLStatistics(const MDSLStatistics& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSLStatistics)
}

void MDSLStatistics::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSLStatistics::~MDSLStatistics() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSLStatistics)
  SharedDtor();
}

void MDSLStatistics::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSLStatistics::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSLStatistics::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSLStatistics_descriptor_;
}

const MDSLStatistics& MDSLStatistics::default_instance() {
  protobuf_InitDefaults_MDSLStatistics_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSLStatistics> MDSLStatistics_default_instance_;

MDSLStatistics* MDSLStatistics::New(::google::protobuf::Arena* arena) const {
  MDSLStatistics* n = new MDSLStatistics;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSLStatistics::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSLStatistics)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSLStatistics, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSLStatistics*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, htscborrowtradevolume_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(htsclendtradevolume_, datamultiplepowerof10_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDSLStatistics::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSLStatistics)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_HtscBorrowTradeVolume;
        break;
      }

      // optional int64 HtscBorrowTradeVolume = 8;
      case 8: {
        if (tag == 64) {
         parse_HtscBorrowTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowtradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_HtscLendTradeVolume;
        break;
      }

      // optional int64 HtscLendTradeVolume = 9;
      case 9: {
        if (tag == 72) {
         parse_HtscLendTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htsclendtradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_MarketTradeVolume;
        break;
      }

      // optional int64 MarketTradeVolume = 10;
      case 10: {
        if (tag == 80) {
         parse_MarketTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &markettradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 11;
      case 11: {
        if (tag == 90) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLStatistics.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 12;
      case 12: {
        if (tag == 96) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSLStatistics)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSLStatistics)
  return false;
#undef DO_
}

void MDSLStatistics::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSLStatistics)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 HtscBorrowTradeVolume = 8;
  if (this->htscborrowtradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->htscborrowtradevolume(), output);
  }

  // optional int64 HtscLendTradeVolume = 9;
  if (this->htsclendtradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->htsclendtradevolume(), output);
  }

  // optional int64 MarketTradeVolume = 10;
  if (this->markettradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->markettradevolume(), output);
  }

  // optional string TradeDate = 11;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->tradedate(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSLStatistics)
}

::google::protobuf::uint8* MDSLStatistics::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSLStatistics)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 HtscBorrowTradeVolume = 8;
  if (this->htscborrowtradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->htscborrowtradevolume(), target);
  }

  // optional int64 HtscLendTradeVolume = 9;
  if (this->htsclendtradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->htsclendtradevolume(), target);
  }

  // optional int64 MarketTradeVolume = 10;
  if (this->markettradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->markettradevolume(), target);
  }

  // optional string TradeDate = 11;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLStatistics.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->tradedate(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSLStatistics)
  return target;
}

size_t MDSLStatistics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSLStatistics)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 HtscBorrowTradeVolume = 8;
  if (this->htscborrowtradevolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowtradevolume());
  }

  // optional int64 HtscLendTradeVolume = 9;
  if (this->htsclendtradevolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htsclendtradevolume());
  }

  // optional int64 MarketTradeVolume = 10;
  if (this->markettradevolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->markettradevolume());
  }

  // optional string TradeDate = 11;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSLStatistics::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSLStatistics)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSLStatistics* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSLStatistics>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSLStatistics)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSLStatistics)
    UnsafeMergeFrom(*source);
  }
}

void MDSLStatistics::MergeFrom(const MDSLStatistics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSLStatistics)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSLStatistics::UnsafeMergeFrom(const MDSLStatistics& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.htscborrowtradevolume() != 0) {
    set_htscborrowtradevolume(from.htscborrowtradevolume());
  }
  if (from.htsclendtradevolume() != 0) {
    set_htsclendtradevolume(from.htsclendtradevolume());
  }
  if (from.markettradevolume() != 0) {
    set_markettradevolume(from.markettradevolume());
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDSLStatistics::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSLStatistics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSLStatistics::CopyFrom(const MDSLStatistics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSLStatistics)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSLStatistics::IsInitialized() const {

  return true;
}

void MDSLStatistics::Swap(MDSLStatistics* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSLStatistics::InternalSwap(MDSLStatistics* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(htscborrowtradevolume_, other->htscborrowtradevolume_);
  std::swap(htsclendtradevolume_, other->htsclendtradevolume_);
  std::swap(markettradevolume_, other->markettradevolume_);
  tradedate_.Swap(&other->tradedate_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSLStatistics::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSLStatistics_descriptor_;
  metadata.reflection = MDSLStatistics_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLStatistics

// optional string HTSCSecurityID = 1;
void MDSLStatistics::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLStatistics::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
}
void MDSLStatistics::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
}
void MDSLStatistics::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
}
::std::string* MDSLStatistics::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLStatistics::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLStatistics.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSLStatistics::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSLStatistics::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.MDDate)
  return mddate_;
}
void MDSLStatistics::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.MDDate)
}

// optional int32 MDTime = 3;
void MDSLStatistics::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSLStatistics::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.MDTime)
  return mdtime_;
}
void MDSLStatistics::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSLStatistics::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLStatistics::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.DataTimestamp)
  return datatimestamp_;
}
void MDSLStatistics::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSLStatistics::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLStatistics::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
}
void MDSLStatistics::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
}
void MDSLStatistics::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
}
::std::string* MDSLStatistics::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLStatistics::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLStatistics.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSLStatistics::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSLStatistics::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSLStatistics::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSLStatistics::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSLStatistics::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSLStatistics::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.securityType)
}

// optional int64 HtscBorrowTradeVolume = 8;
void MDSLStatistics::clear_htscborrowtradevolume() {
  htscborrowtradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLStatistics::htscborrowtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.HtscBorrowTradeVolume)
  return htscborrowtradevolume_;
}
void MDSLStatistics::set_htscborrowtradevolume(::google::protobuf::int64 value) {
  
  htscborrowtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.HtscBorrowTradeVolume)
}

// optional int64 HtscLendTradeVolume = 9;
void MDSLStatistics::clear_htsclendtradevolume() {
  htsclendtradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLStatistics::htsclendtradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.HtscLendTradeVolume)
  return htsclendtradevolume_;
}
void MDSLStatistics::set_htsclendtradevolume(::google::protobuf::int64 value) {
  
  htsclendtradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.HtscLendTradeVolume)
}

// optional int64 MarketTradeVolume = 10;
void MDSLStatistics::clear_markettradevolume() {
  markettradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLStatistics::markettradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.MarketTradeVolume)
  return markettradevolume_;
}
void MDSLStatistics::set_markettradevolume(::google::protobuf::int64 value) {
  
  markettradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.MarketTradeVolume)
}

// optional string TradeDate = 11;
void MDSLStatistics::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLStatistics::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
}
void MDSLStatistics::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
}
void MDSLStatistics::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
}
::std::string* MDSLStatistics::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLStatistics::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLStatistics::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLStatistics.TradeDate)
}

// optional int32 DataMultiplePowerOf10 = 12;
void MDSLStatistics::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSLStatistics::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLStatistics.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSLStatistics::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLStatistics.DataMultiplePowerOf10)
}

inline const MDSLStatistics* MDSLStatistics::internal_default_instance() {
  return &MDSLStatistics_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
