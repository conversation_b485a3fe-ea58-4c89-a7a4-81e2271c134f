syntax = "proto3";

package com.htsc.mdc.model;

enum ESecurityType {
  DefaultSecurityType = 0;
  IndexType = 1;
  StockType = 2;
  FundType = 3;
  BondType = 4;
  RepoType = 5;
  WarrantType = 6;
  OptionType = 7;
  FuturesType = 8;
  ForexType = 9;
  RateType = 10;
  NmetalType = 11;        // 注：应为 "MetalType"，但保留原始拼写
  CashBondType = 12;
  SpotType = 13;
  SPFuturesType = 14;
  CurrencyType = 15;
  BenchmarkType = 16;
  InsightType = 20;
  OtherType = 99;
}
