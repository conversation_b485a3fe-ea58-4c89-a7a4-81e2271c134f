// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDHKGreyMarket.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDHKGreyMarket.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDHKGreyMarket_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDHKGreyMarket_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDHKGreyMarket_MarketEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDHKGreyMarket_MarketEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDHKGreyMarket_OrderEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDHKGreyMarket_OrderEntry_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDHKGreyMarket_TradeEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDHKGreyMarket_TradeEntry_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDHKGreyMarket_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDHKGreyMarket_2eproto() {
  protobuf_AddDesc_MDHKGreyMarket_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDHKGreyMarket.proto");
  GOOGLE_CHECK(file != NULL);
  MDHKGreyMarket_descriptor_ = file->message_type(0);
  static const int MDHKGreyMarket_offsets_[10] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, marketentries_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, datamultiplepowerof10_),
  };
  MDHKGreyMarket_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDHKGreyMarket_descriptor_,
      MDHKGreyMarket::internal_default_instance(),
      MDHKGreyMarket_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDHKGreyMarket),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket, _internal_metadata_));
  MDHKGreyMarket_MarketEntry_descriptor_ = MDHKGreyMarket_descriptor_->nested_type(0);
  static const int MDHKGreyMarket_MarketEntry_offsets_[15] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, channeltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, validflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, buyorderentries_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, sellorderentries_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, tradeentries_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, exchangedatetime_),
  };
  MDHKGreyMarket_MarketEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDHKGreyMarket_MarketEntry_descriptor_,
      MDHKGreyMarket_MarketEntry::internal_default_instance(),
      MDHKGreyMarket_MarketEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDHKGreyMarket_MarketEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_MarketEntry, _internal_metadata_));
  MDHKGreyMarket_OrderEntry_descriptor_ = MDHKGreyMarket_descriptor_->nested_type(1);
  static const int MDHKGreyMarket_OrderEntry_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_OrderEntry, orderlevel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_OrderEntry, orderprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_OrderEntry, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_OrderEntry, numorders_),
  };
  MDHKGreyMarket_OrderEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDHKGreyMarket_OrderEntry_descriptor_,
      MDHKGreyMarket_OrderEntry::internal_default_instance(),
      MDHKGreyMarket_OrderEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDHKGreyMarket_OrderEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_OrderEntry, _internal_metadata_));
  MDHKGreyMarket_TradeEntry_descriptor_ = MDHKGreyMarket_descriptor_->nested_type(2);
  static const int MDHKGreyMarket_TradeEntry_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_TradeEntry, tradelevel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_TradeEntry, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_TradeEntry, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_TradeEntry, tradetime_),
  };
  MDHKGreyMarket_TradeEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDHKGreyMarket_TradeEntry_descriptor_,
      MDHKGreyMarket_TradeEntry::internal_default_instance(),
      MDHKGreyMarket_TradeEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDHKGreyMarket_TradeEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHKGreyMarket_TradeEntry, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDHKGreyMarket_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDHKGreyMarket_descriptor_, MDHKGreyMarket::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDHKGreyMarket_MarketEntry_descriptor_, MDHKGreyMarket_MarketEntry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDHKGreyMarket_OrderEntry_descriptor_, MDHKGreyMarket_OrderEntry::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDHKGreyMarket_TradeEntry_descriptor_, MDHKGreyMarket_TradeEntry::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDHKGreyMarket_2eproto() {
  MDHKGreyMarket_default_instance_.Shutdown();
  delete MDHKGreyMarket_reflection_;
  MDHKGreyMarket_MarketEntry_default_instance_.Shutdown();
  delete MDHKGreyMarket_MarketEntry_reflection_;
  MDHKGreyMarket_OrderEntry_default_instance_.Shutdown();
  delete MDHKGreyMarket_OrderEntry_reflection_;
  MDHKGreyMarket_TradeEntry_default_instance_.Shutdown();
  delete MDHKGreyMarket_TradeEntry_reflection_;
}

void protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDHKGreyMarket_default_instance_.DefaultConstruct();
  MDHKGreyMarket_MarketEntry_default_instance_.DefaultConstruct();
  MDHKGreyMarket_OrderEntry_default_instance_.DefaultConstruct();
  MDHKGreyMarket_TradeEntry_default_instance_.DefaultConstruct();
  MDHKGreyMarket_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDHKGreyMarket_MarketEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDHKGreyMarket_OrderEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDHKGreyMarket_TradeEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDHKGreyMarket_2eproto_once_);
void protobuf_InitDefaults_MDHKGreyMarket_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDHKGreyMarket_2eproto_once_,
                 &protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl);
}
void protobuf_AddDesc_MDHKGreyMarket_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDHKGreyMarket.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\023ESecurityType.proto\032\027ESecuri"
    "tyIDSource.proto\"\225\010\n\016MDHKGreyMarket\022\026\n\016H"
    "TSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006M"
    "DTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020Tr"
    "adingPhaseCode\030\005 \001(\t\022\?\n\020securityIDSource"
    "\030\006 \001(\0162%.com.htsc.mdc.model.ESecurityIDS"
    "ource\0227\n\014securityType\030\007 \001(\0162!.com.htsc.m"
    "dc.model.ESecurityType\022\022\n\nApplSeqNum\030\010 \001"
    "(\003\022M\n\rMarketEntries\030\t \003(\01326.com.htsc.mdc"
    ".insight.model.MDHKGreyMarket.MarketEntr"
    "y\022\035\n\025DataMultiplePowerOf10\030\n \001(\005\032\347\003\n\013Mar"
    "ketEntry\022\023\n\013ChannelType\030\001 \001(\005\022\021\n\tValidFl"
    "ag\030\002 \001(\010\022\022\n\nPreClosePx\030\003 \001(\003\022\021\n\tNumTrade"
    "s\030\004 \001(\003\022\030\n\020TotalVolumeTrade\030\005 \001(\003\022\027\n\017Tot"
    "alValueTrade\030\006 \001(\003\022\016\n\006LastPx\030\007 \001(\003\022\016\n\006Op"
    "enPx\030\010 \001(\003\022\017\n\007ClosePx\030\t \001(\003\022\016\n\006HighPx\030\n "
    "\001(\003\022\r\n\005LowPx\030\013 \001(\003\022N\n\017BuyOrderEntries\030\014 "
    "\003(\01325.com.htsc.mdc.insight.model.MDHKGre"
    "yMarket.OrderEntry\022O\n\020SellOrderEntries\030\r"
    " \003(\01325.com.htsc.mdc.insight.model.MDHKGr"
    "eyMarket.OrderEntry\022K\n\014TradeEntries\030\016 \003("
    "\01325.com.htsc.mdc.insight.model.MDHKGreyM"
    "arket.TradeEntry\022\030\n\020ExchangeDateTime\030\017 \001"
    "(\003\032Y\n\nOrderEntry\022\022\n\nOrderLevel\030\001 \001(\005\022\022\n\n"
    "OrderPrice\030\002 \001(\003\022\020\n\010OrderQty\030\003 \001(\003\022\021\n\tNu"
    "mOrders\030\004 \001(\003\032Y\n\nTradeEntry\022\022\n\nTradeLeve"
    "l\030\001 \001(\005\022\022\n\nTradePrice\030\002 \001(\003\022\020\n\010TradeQty\030"
    "\003 \001(\003\022\021\n\tTradeTime\030\004 \001(\003B7\n\032com.htsc.mdc"
    ".insight.modelB\024MDHKGreyMarketProtosH\001\240\001"
    "\001b\006proto3", 1209);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDHKGreyMarket.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDHKGreyMarket_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDHKGreyMarket_2eproto_once_);
void protobuf_AddDesc_MDHKGreyMarket_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDHKGreyMarket_2eproto_once_,
                 &protobuf_AddDesc_MDHKGreyMarket_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDHKGreyMarket_2eproto {
  StaticDescriptorInitializer_MDHKGreyMarket_2eproto() {
    protobuf_AddDesc_MDHKGreyMarket_2eproto();
  }
} static_descriptor_initializer_MDHKGreyMarket_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDHKGreyMarket_MarketEntry::kChannelTypeFieldNumber;
const int MDHKGreyMarket_MarketEntry::kValidFlagFieldNumber;
const int MDHKGreyMarket_MarketEntry::kPreClosePxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kNumTradesFieldNumber;
const int MDHKGreyMarket_MarketEntry::kTotalVolumeTradeFieldNumber;
const int MDHKGreyMarket_MarketEntry::kTotalValueTradeFieldNumber;
const int MDHKGreyMarket_MarketEntry::kLastPxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kOpenPxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kClosePxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kHighPxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kLowPxFieldNumber;
const int MDHKGreyMarket_MarketEntry::kBuyOrderEntriesFieldNumber;
const int MDHKGreyMarket_MarketEntry::kSellOrderEntriesFieldNumber;
const int MDHKGreyMarket_MarketEntry::kTradeEntriesFieldNumber;
const int MDHKGreyMarket_MarketEntry::kExchangeDateTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDHKGreyMarket_MarketEntry::MDHKGreyMarket_MarketEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
}

void MDHKGreyMarket_MarketEntry::InitAsDefaultInstance() {
}

MDHKGreyMarket_MarketEntry::MDHKGreyMarket_MarketEntry(const MDHKGreyMarket_MarketEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
}

void MDHKGreyMarket_MarketEntry::SharedCtor() {
  ::memset(&channeltype_, 0, reinterpret_cast<char*>(&exchangedatetime_) -
    reinterpret_cast<char*>(&channeltype_) + sizeof(exchangedatetime_));
  _cached_size_ = 0;
}

MDHKGreyMarket_MarketEntry::~MDHKGreyMarket_MarketEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  SharedDtor();
}

void MDHKGreyMarket_MarketEntry::SharedDtor() {
}

void MDHKGreyMarket_MarketEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDHKGreyMarket_MarketEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDHKGreyMarket_MarketEntry_descriptor_;
}

const MDHKGreyMarket_MarketEntry& MDHKGreyMarket_MarketEntry::default_instance() {
  protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_MarketEntry> MDHKGreyMarket_MarketEntry_default_instance_;

MDHKGreyMarket_MarketEntry* MDHKGreyMarket_MarketEntry::New(::google::protobuf::Arena* arena) const {
  MDHKGreyMarket_MarketEntry* n = new MDHKGreyMarket_MarketEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDHKGreyMarket_MarketEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDHKGreyMarket_MarketEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDHKGreyMarket_MarketEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(channeltype_, openpx_);
  ZR_(closepx_, exchangedatetime_);

#undef ZR_HELPER_
#undef ZR_

  buyorderentries_.Clear();
  sellorderentries_.Clear();
  tradeentries_.Clear();
}

bool MDHKGreyMarket_MarketEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 ChannelType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channeltype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_ValidFlag;
        break;
      }

      // optional bool ValidFlag = 2;
      case 2: {
        if (tag == 16) {
         parse_ValidFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &validflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 3;
      case 3: {
        if (tag == 24) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 4;
      case 4: {
        if (tag == 32) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 5;
      case 5: {
        if (tag == 40) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 6;
      case 6: {
        if (tag == 48) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 7;
      case 7: {
        if (tag == 56) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 8;
      case 8: {
        if (tag == 64) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 9;
      case 9: {
        if (tag == 72) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 10;
      case 10: {
        if (tag == 80) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 11;
      case 11: {
        if (tag == 88) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_BuyOrderEntries;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
      case 12: {
        if (tag == 98) {
         parse_BuyOrderEntries:
          DO_(input->IncrementRecursionDepth());
         parse_loop_BuyOrderEntries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_buyorderentries()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_loop_BuyOrderEntries;
        if (input->ExpectTag(106)) goto parse_loop_SellOrderEntries;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
      case 13: {
        if (tag == 106) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_SellOrderEntries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_sellorderentries()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_loop_SellOrderEntries;
        if (input->ExpectTag(114)) goto parse_loop_TradeEntries;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
      case 14: {
        if (tag == 114) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_TradeEntries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_tradeentries()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_loop_TradeEntries;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(120)) goto parse_ExchangeDateTime;
        break;
      }

      // optional int64 ExchangeDateTime = 15;
      case 15: {
        if (tag == 120) {
         parse_ExchangeDateTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &exchangedatetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  return false;
#undef DO_
}

void MDHKGreyMarket_MarketEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  // optional int32 ChannelType = 1;
  if (this->channeltype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->channeltype(), output);
  }

  // optional bool ValidFlag = 2;
  if (this->validflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->validflag(), output);
  }

  // optional int64 PreClosePx = 3;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 4;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 5;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 6;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 7;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->lastpx(), output);
  }

  // optional int64 OpenPx = 8;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->openpx(), output);
  }

  // optional int64 ClosePx = 9;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->closepx(), output);
  }

  // optional int64 HighPx = 10;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->highpx(), output);
  }

  // optional int64 LowPx = 11;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lowpx(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
  for (unsigned int i = 0, n = this->buyorderentries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, this->buyorderentries(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
  for (unsigned int i = 0, n = this->sellorderentries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, this->sellorderentries(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
  for (unsigned int i = 0, n = this->tradeentries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, this->tradeentries(i), output);
  }

  // optional int64 ExchangeDateTime = 15;
  if (this->exchangedatetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->exchangedatetime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
}

::google::protobuf::uint8* MDHKGreyMarket_MarketEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  // optional int32 ChannelType = 1;
  if (this->channeltype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->channeltype(), target);
  }

  // optional bool ValidFlag = 2;
  if (this->validflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->validflag(), target);
  }

  // optional int64 PreClosePx = 3;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 4;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 5;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 6;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 7;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->lastpx(), target);
  }

  // optional int64 OpenPx = 8;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->openpx(), target);
  }

  // optional int64 ClosePx = 9;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->closepx(), target);
  }

  // optional int64 HighPx = 10;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->highpx(), target);
  }

  // optional int64 LowPx = 11;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lowpx(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
  for (unsigned int i = 0, n = this->buyorderentries_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, this->buyorderentries(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
  for (unsigned int i = 0, n = this->sellorderentries_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, this->sellorderentries(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
  for (unsigned int i = 0, n = this->tradeentries_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, this->tradeentries(i), false, target);
  }

  // optional int64 ExchangeDateTime = 15;
  if (this->exchangedatetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->exchangedatetime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  return target;
}

size_t MDHKGreyMarket_MarketEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  size_t total_size = 0;

  // optional int32 ChannelType = 1;
  if (this->channeltype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channeltype());
  }

  // optional bool ValidFlag = 2;
  if (this->validflag() != 0) {
    total_size += 1 + 1;
  }

  // optional int64 PreClosePx = 3;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 4;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 5;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 6;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 7;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 8;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 9;
  if (this->closepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 10;
  if (this->highpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 11;
  if (this->lowpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int64 ExchangeDateTime = 15;
  if (this->exchangedatetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->exchangedatetime());
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
  {
    unsigned int count = this->buyorderentries_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->buyorderentries(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
  {
    unsigned int count = this->sellorderentries_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->sellorderentries(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
  {
    unsigned int count = this->tradeentries_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->tradeentries(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDHKGreyMarket_MarketEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDHKGreyMarket_MarketEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDHKGreyMarket_MarketEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
    UnsafeMergeFrom(*source);
  }
}

void MDHKGreyMarket_MarketEntry::MergeFrom(const MDHKGreyMarket_MarketEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDHKGreyMarket_MarketEntry::UnsafeMergeFrom(const MDHKGreyMarket_MarketEntry& from) {
  GOOGLE_DCHECK(&from != this);
  buyorderentries_.MergeFrom(from.buyorderentries_);
  sellorderentries_.MergeFrom(from.sellorderentries_);
  tradeentries_.MergeFrom(from.tradeentries_);
  if (from.channeltype() != 0) {
    set_channeltype(from.channeltype());
  }
  if (from.validflag() != 0) {
    set_validflag(from.validflag());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.exchangedatetime() != 0) {
    set_exchangedatetime(from.exchangedatetime());
  }
}

void MDHKGreyMarket_MarketEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDHKGreyMarket_MarketEntry::CopyFrom(const MDHKGreyMarket_MarketEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDHKGreyMarket_MarketEntry::IsInitialized() const {

  return true;
}

void MDHKGreyMarket_MarketEntry::Swap(MDHKGreyMarket_MarketEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDHKGreyMarket_MarketEntry::InternalSwap(MDHKGreyMarket_MarketEntry* other) {
  std::swap(channeltype_, other->channeltype_);
  std::swap(validflag_, other->validflag_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  buyorderentries_.UnsafeArenaSwap(&other->buyorderentries_);
  sellorderentries_.UnsafeArenaSwap(&other->sellorderentries_);
  tradeentries_.UnsafeArenaSwap(&other->tradeentries_);
  std::swap(exchangedatetime_, other->exchangedatetime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDHKGreyMarket_MarketEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDHKGreyMarket_MarketEntry_descriptor_;
  metadata.reflection = MDHKGreyMarket_MarketEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDHKGreyMarket_OrderEntry::kOrderLevelFieldNumber;
const int MDHKGreyMarket_OrderEntry::kOrderPriceFieldNumber;
const int MDHKGreyMarket_OrderEntry::kOrderQtyFieldNumber;
const int MDHKGreyMarket_OrderEntry::kNumOrdersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDHKGreyMarket_OrderEntry::MDHKGreyMarket_OrderEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
}

void MDHKGreyMarket_OrderEntry::InitAsDefaultInstance() {
}

MDHKGreyMarket_OrderEntry::MDHKGreyMarket_OrderEntry(const MDHKGreyMarket_OrderEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
}

void MDHKGreyMarket_OrderEntry::SharedCtor() {
  ::memset(&orderprice_, 0, reinterpret_cast<char*>(&orderlevel_) -
    reinterpret_cast<char*>(&orderprice_) + sizeof(orderlevel_));
  _cached_size_ = 0;
}

MDHKGreyMarket_OrderEntry::~MDHKGreyMarket_OrderEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  SharedDtor();
}

void MDHKGreyMarket_OrderEntry::SharedDtor() {
}

void MDHKGreyMarket_OrderEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDHKGreyMarket_OrderEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDHKGreyMarket_OrderEntry_descriptor_;
}

const MDHKGreyMarket_OrderEntry& MDHKGreyMarket_OrderEntry::default_instance() {
  protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_OrderEntry> MDHKGreyMarket_OrderEntry_default_instance_;

MDHKGreyMarket_OrderEntry* MDHKGreyMarket_OrderEntry::New(::google::protobuf::Arena* arena) const {
  MDHKGreyMarket_OrderEntry* n = new MDHKGreyMarket_OrderEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDHKGreyMarket_OrderEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDHKGreyMarket_OrderEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDHKGreyMarket_OrderEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(orderprice_, orderlevel_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDHKGreyMarket_OrderEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 OrderLevel = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &orderlevel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_OrderPrice;
        break;
      }

      // optional int64 OrderPrice = 2;
      case 2: {
        if (tag == 16) {
         parse_OrderPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_OrderQty;
        break;
      }

      // optional int64 OrderQty = 3;
      case 3: {
        if (tag == 24) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_NumOrders;
        break;
      }

      // optional int64 NumOrders = 4;
      case 4: {
        if (tag == 32) {
         parse_NumOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  return false;
#undef DO_
}

void MDHKGreyMarket_OrderEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  // optional int32 OrderLevel = 1;
  if (this->orderlevel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->orderlevel(), output);
  }

  // optional int64 OrderPrice = 2;
  if (this->orderprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->orderprice(), output);
  }

  // optional int64 OrderQty = 3;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->orderqty(), output);
  }

  // optional int64 NumOrders = 4;
  if (this->numorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->numorders(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
}

::google::protobuf::uint8* MDHKGreyMarket_OrderEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  // optional int32 OrderLevel = 1;
  if (this->orderlevel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->orderlevel(), target);
  }

  // optional int64 OrderPrice = 2;
  if (this->orderprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->orderprice(), target);
  }

  // optional int64 OrderQty = 3;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->orderqty(), target);
  }

  // optional int64 NumOrders = 4;
  if (this->numorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->numorders(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  return target;
}

size_t MDHKGreyMarket_OrderEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  size_t total_size = 0;

  // optional int32 OrderLevel = 1;
  if (this->orderlevel() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->orderlevel());
  }

  // optional int64 OrderPrice = 2;
  if (this->orderprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderprice());
  }

  // optional int64 OrderQty = 3;
  if (this->orderqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderqty());
  }

  // optional int64 NumOrders = 4;
  if (this->numorders() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numorders());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDHKGreyMarket_OrderEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDHKGreyMarket_OrderEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDHKGreyMarket_OrderEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
    UnsafeMergeFrom(*source);
  }
}

void MDHKGreyMarket_OrderEntry::MergeFrom(const MDHKGreyMarket_OrderEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDHKGreyMarket_OrderEntry::UnsafeMergeFrom(const MDHKGreyMarket_OrderEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.orderlevel() != 0) {
    set_orderlevel(from.orderlevel());
  }
  if (from.orderprice() != 0) {
    set_orderprice(from.orderprice());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.numorders() != 0) {
    set_numorders(from.numorders());
  }
}

void MDHKGreyMarket_OrderEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDHKGreyMarket_OrderEntry::CopyFrom(const MDHKGreyMarket_OrderEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDHKGreyMarket_OrderEntry::IsInitialized() const {

  return true;
}

void MDHKGreyMarket_OrderEntry::Swap(MDHKGreyMarket_OrderEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDHKGreyMarket_OrderEntry::InternalSwap(MDHKGreyMarket_OrderEntry* other) {
  std::swap(orderlevel_, other->orderlevel_);
  std::swap(orderprice_, other->orderprice_);
  std::swap(orderqty_, other->orderqty_);
  std::swap(numorders_, other->numorders_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDHKGreyMarket_OrderEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDHKGreyMarket_OrderEntry_descriptor_;
  metadata.reflection = MDHKGreyMarket_OrderEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDHKGreyMarket_TradeEntry::kTradeLevelFieldNumber;
const int MDHKGreyMarket_TradeEntry::kTradePriceFieldNumber;
const int MDHKGreyMarket_TradeEntry::kTradeQtyFieldNumber;
const int MDHKGreyMarket_TradeEntry::kTradeTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDHKGreyMarket_TradeEntry::MDHKGreyMarket_TradeEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
}

void MDHKGreyMarket_TradeEntry::InitAsDefaultInstance() {
}

MDHKGreyMarket_TradeEntry::MDHKGreyMarket_TradeEntry(const MDHKGreyMarket_TradeEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
}

void MDHKGreyMarket_TradeEntry::SharedCtor() {
  ::memset(&tradeprice_, 0, reinterpret_cast<char*>(&tradelevel_) -
    reinterpret_cast<char*>(&tradeprice_) + sizeof(tradelevel_));
  _cached_size_ = 0;
}

MDHKGreyMarket_TradeEntry::~MDHKGreyMarket_TradeEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  SharedDtor();
}

void MDHKGreyMarket_TradeEntry::SharedDtor() {
}

void MDHKGreyMarket_TradeEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDHKGreyMarket_TradeEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDHKGreyMarket_TradeEntry_descriptor_;
}

const MDHKGreyMarket_TradeEntry& MDHKGreyMarket_TradeEntry::default_instance() {
  protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_TradeEntry> MDHKGreyMarket_TradeEntry_default_instance_;

MDHKGreyMarket_TradeEntry* MDHKGreyMarket_TradeEntry::New(::google::protobuf::Arena* arena) const {
  MDHKGreyMarket_TradeEntry* n = new MDHKGreyMarket_TradeEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDHKGreyMarket_TradeEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDHKGreyMarket_TradeEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDHKGreyMarket_TradeEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(tradeprice_, tradelevel_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDHKGreyMarket_TradeEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeLevel = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradelevel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_TradePrice;
        break;
      }

      // optional int64 TradePrice = 2;
      case 2: {
        if (tag == 16) {
         parse_TradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_TradeQty;
        break;
      }

      // optional int64 TradeQty = 3;
      case 3: {
        if (tag == 24) {
         parse_TradeQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_TradeTime;
        break;
      }

      // optional int64 TradeTime = 4;
      case 4: {
        if (tag == 32) {
         parse_TradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  return false;
#undef DO_
}

void MDHKGreyMarket_TradeEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  // optional int32 TradeLevel = 1;
  if (this->tradelevel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->tradelevel(), output);
  }

  // optional int64 TradePrice = 2;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->tradeprice(), output);
  }

  // optional int64 TradeQty = 3;
  if (this->tradeqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->tradeqty(), output);
  }

  // optional int64 TradeTime = 4;
  if (this->tradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->tradetime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
}

::google::protobuf::uint8* MDHKGreyMarket_TradeEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  // optional int32 TradeLevel = 1;
  if (this->tradelevel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->tradelevel(), target);
  }

  // optional int64 TradePrice = 2;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->tradeprice(), target);
  }

  // optional int64 TradeQty = 3;
  if (this->tradeqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->tradeqty(), target);
  }

  // optional int64 TradeTime = 4;
  if (this->tradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->tradetime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  return target;
}

size_t MDHKGreyMarket_TradeEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  size_t total_size = 0;

  // optional int32 TradeLevel = 1;
  if (this->tradelevel() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradelevel());
  }

  // optional int64 TradePrice = 2;
  if (this->tradeprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeprice());
  }

  // optional int64 TradeQty = 3;
  if (this->tradeqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeqty());
  }

  // optional int64 TradeTime = 4;
  if (this->tradetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradetime());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDHKGreyMarket_TradeEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDHKGreyMarket_TradeEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDHKGreyMarket_TradeEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
    UnsafeMergeFrom(*source);
  }
}

void MDHKGreyMarket_TradeEntry::MergeFrom(const MDHKGreyMarket_TradeEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDHKGreyMarket_TradeEntry::UnsafeMergeFrom(const MDHKGreyMarket_TradeEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradelevel() != 0) {
    set_tradelevel(from.tradelevel());
  }
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.tradeqty() != 0) {
    set_tradeqty(from.tradeqty());
  }
  if (from.tradetime() != 0) {
    set_tradetime(from.tradetime());
  }
}

void MDHKGreyMarket_TradeEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDHKGreyMarket_TradeEntry::CopyFrom(const MDHKGreyMarket_TradeEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDHKGreyMarket_TradeEntry::IsInitialized() const {

  return true;
}

void MDHKGreyMarket_TradeEntry::Swap(MDHKGreyMarket_TradeEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDHKGreyMarket_TradeEntry::InternalSwap(MDHKGreyMarket_TradeEntry* other) {
  std::swap(tradelevel_, other->tradelevel_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(tradetime_, other->tradetime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDHKGreyMarket_TradeEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDHKGreyMarket_TradeEntry_descriptor_;
  metadata.reflection = MDHKGreyMarket_TradeEntry_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDHKGreyMarket::kHTSCSecurityIDFieldNumber;
const int MDHKGreyMarket::kMDDateFieldNumber;
const int MDHKGreyMarket::kMDTimeFieldNumber;
const int MDHKGreyMarket::kDataTimestampFieldNumber;
const int MDHKGreyMarket::kTradingPhaseCodeFieldNumber;
const int MDHKGreyMarket::kSecurityIDSourceFieldNumber;
const int MDHKGreyMarket::kSecurityTypeFieldNumber;
const int MDHKGreyMarket::kApplSeqNumFieldNumber;
const int MDHKGreyMarket::kMarketEntriesFieldNumber;
const int MDHKGreyMarket::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDHKGreyMarket::MDHKGreyMarket()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDHKGreyMarket)
}

void MDHKGreyMarket::InitAsDefaultInstance() {
}

MDHKGreyMarket::MDHKGreyMarket(const MDHKGreyMarket& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDHKGreyMarket)
}

void MDHKGreyMarket::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDHKGreyMarket::~MDHKGreyMarket() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDHKGreyMarket)
  SharedDtor();
}

void MDHKGreyMarket::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDHKGreyMarket::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDHKGreyMarket::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDHKGreyMarket_descriptor_;
}

const MDHKGreyMarket& MDHKGreyMarket::default_instance() {
  protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket> MDHKGreyMarket_default_instance_;

MDHKGreyMarket* MDHKGreyMarket::New(::google::protobuf::Arena* arena) const {
  MDHKGreyMarket* n = new MDHKGreyMarket;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDHKGreyMarket::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDHKGreyMarket, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDHKGreyMarket*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, applseqnum_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datamultiplepowerof10_ = 0;

#undef ZR_HELPER_
#undef ZR_

  marketentries_.Clear();
}

bool MDHKGreyMarket::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 8;
      case 8: {
        if (tag == 64) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_MarketEntries;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
      case 9: {
        if (tag == 74) {
         parse_MarketEntries:
          DO_(input->IncrementRecursionDepth());
         parse_loop_MarketEntries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_marketentries()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_MarketEntries;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(80)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 10;
      case 10: {
        if (tag == 80) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDHKGreyMarket)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDHKGreyMarket)
  return false;
#undef DO_
}

void MDHKGreyMarket::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 ApplSeqNum = 8;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->applseqnum(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
  for (unsigned int i = 0, n = this->marketentries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->marketentries(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDHKGreyMarket)
}

::google::protobuf::uint8* MDHKGreyMarket::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 ApplSeqNum = 8;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->applseqnum(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
  for (unsigned int i = 0, n = this->marketentries_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->marketentries(i), false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDHKGreyMarket)
  return target;
}

size_t MDHKGreyMarket::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 ApplSeqNum = 8;
  if (this->applseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
  {
    unsigned int count = this->marketentries_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->marketentries(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDHKGreyMarket::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDHKGreyMarket* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDHKGreyMarket>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDHKGreyMarket)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDHKGreyMarket)
    UnsafeMergeFrom(*source);
  }
}

void MDHKGreyMarket::MergeFrom(const MDHKGreyMarket& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDHKGreyMarket::UnsafeMergeFrom(const MDHKGreyMarket& from) {
  GOOGLE_DCHECK(&from != this);
  marketentries_.MergeFrom(from.marketentries_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDHKGreyMarket::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDHKGreyMarket::CopyFrom(const MDHKGreyMarket& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDHKGreyMarket)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDHKGreyMarket::IsInitialized() const {

  return true;
}

void MDHKGreyMarket::Swap(MDHKGreyMarket* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDHKGreyMarket::InternalSwap(MDHKGreyMarket* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(applseqnum_, other->applseqnum_);
  marketentries_.UnsafeArenaSwap(&other->marketentries_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDHKGreyMarket::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDHKGreyMarket_descriptor_;
  metadata.reflection = MDHKGreyMarket_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDHKGreyMarket_MarketEntry

// optional int32 ChannelType = 1;
void MDHKGreyMarket_MarketEntry::clear_channeltype() {
  channeltype_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket_MarketEntry::channeltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ChannelType)
  return channeltype_;
}
void MDHKGreyMarket_MarketEntry::set_channeltype(::google::protobuf::int32 value) {
  
  channeltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ChannelType)
}

// optional bool ValidFlag = 2;
void MDHKGreyMarket_MarketEntry::clear_validflag() {
  validflag_ = false;
}
bool MDHKGreyMarket_MarketEntry::validflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ValidFlag)
  return validflag_;
}
void MDHKGreyMarket_MarketEntry::set_validflag(bool value) {
  
  validflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ValidFlag)
}

// optional int64 PreClosePx = 3;
void MDHKGreyMarket_MarketEntry::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.PreClosePx)
  return preclosepx_;
}
void MDHKGreyMarket_MarketEntry::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.PreClosePx)
}

// optional int64 NumTrades = 4;
void MDHKGreyMarket_MarketEntry::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.NumTrades)
  return numtrades_;
}
void MDHKGreyMarket_MarketEntry::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.NumTrades)
}

// optional int64 TotalVolumeTrade = 5;
void MDHKGreyMarket_MarketEntry::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDHKGreyMarket_MarketEntry::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 6;
void MDHKGreyMarket_MarketEntry::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalValueTrade)
  return totalvaluetrade_;
}
void MDHKGreyMarket_MarketEntry::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalValueTrade)
}

// optional int64 LastPx = 7;
void MDHKGreyMarket_MarketEntry::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LastPx)
  return lastpx_;
}
void MDHKGreyMarket_MarketEntry::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LastPx)
}

// optional int64 OpenPx = 8;
void MDHKGreyMarket_MarketEntry::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.OpenPx)
  return openpx_;
}
void MDHKGreyMarket_MarketEntry::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.OpenPx)
}

// optional int64 ClosePx = 9;
void MDHKGreyMarket_MarketEntry::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ClosePx)
  return closepx_;
}
void MDHKGreyMarket_MarketEntry::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ClosePx)
}

// optional int64 HighPx = 10;
void MDHKGreyMarket_MarketEntry::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.HighPx)
  return highpx_;
}
void MDHKGreyMarket_MarketEntry::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.HighPx)
}

// optional int64 LowPx = 11;
void MDHKGreyMarket_MarketEntry::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LowPx)
  return lowpx_;
}
void MDHKGreyMarket_MarketEntry::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LowPx)
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
int MDHKGreyMarket_MarketEntry::buyorderentries_size() const {
  return buyorderentries_.size();
}
void MDHKGreyMarket_MarketEntry::clear_buyorderentries() {
  buyorderentries_.Clear();
}
const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& MDHKGreyMarket_MarketEntry::buyorderentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Get(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::mutable_buyorderentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::add_buyorderentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
MDHKGreyMarket_MarketEntry::mutable_buyorderentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return &buyorderentries_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
MDHKGreyMarket_MarketEntry::buyorderentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_;
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
int MDHKGreyMarket_MarketEntry::sellorderentries_size() const {
  return sellorderentries_.size();
}
void MDHKGreyMarket_MarketEntry::clear_sellorderentries() {
  sellorderentries_.Clear();
}
const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& MDHKGreyMarket_MarketEntry::sellorderentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Get(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::mutable_sellorderentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::add_sellorderentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
MDHKGreyMarket_MarketEntry::mutable_sellorderentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return &sellorderentries_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
MDHKGreyMarket_MarketEntry::sellorderentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_;
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
int MDHKGreyMarket_MarketEntry::tradeentries_size() const {
  return tradeentries_.size();
}
void MDHKGreyMarket_MarketEntry::clear_tradeentries() {
  tradeentries_.Clear();
}
const ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry& MDHKGreyMarket_MarketEntry::tradeentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Get(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* MDHKGreyMarket_MarketEntry::mutable_tradeentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* MDHKGreyMarket_MarketEntry::add_tradeentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >*
MDHKGreyMarket_MarketEntry::mutable_tradeentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return &tradeentries_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >&
MDHKGreyMarket_MarketEntry::tradeentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_;
}

// optional int64 ExchangeDateTime = 15;
void MDHKGreyMarket_MarketEntry::clear_exchangedatetime() {
  exchangedatetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_MarketEntry::exchangedatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ExchangeDateTime)
  return exchangedatetime_;
}
void MDHKGreyMarket_MarketEntry::set_exchangedatetime(::google::protobuf::int64 value) {
  
  exchangedatetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ExchangeDateTime)
}

inline const MDHKGreyMarket_MarketEntry* MDHKGreyMarket_MarketEntry::internal_default_instance() {
  return &MDHKGreyMarket_MarketEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket_OrderEntry

// optional int32 OrderLevel = 1;
void MDHKGreyMarket_OrderEntry::clear_orderlevel() {
  orderlevel_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket_OrderEntry::orderlevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderLevel)
  return orderlevel_;
}
void MDHKGreyMarket_OrderEntry::set_orderlevel(::google::protobuf::int32 value) {
  
  orderlevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderLevel)
}

// optional int64 OrderPrice = 2;
void MDHKGreyMarket_OrderEntry::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_OrderEntry::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderPrice)
  return orderprice_;
}
void MDHKGreyMarket_OrderEntry::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderPrice)
}

// optional int64 OrderQty = 3;
void MDHKGreyMarket_OrderEntry::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_OrderEntry::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderQty)
  return orderqty_;
}
void MDHKGreyMarket_OrderEntry::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderQty)
}

// optional int64 NumOrders = 4;
void MDHKGreyMarket_OrderEntry::clear_numorders() {
  numorders_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_OrderEntry::numorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.NumOrders)
  return numorders_;
}
void MDHKGreyMarket_OrderEntry::set_numorders(::google::protobuf::int64 value) {
  
  numorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.NumOrders)
}

inline const MDHKGreyMarket_OrderEntry* MDHKGreyMarket_OrderEntry::internal_default_instance() {
  return &MDHKGreyMarket_OrderEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket_TradeEntry

// optional int32 TradeLevel = 1;
void MDHKGreyMarket_TradeEntry::clear_tradelevel() {
  tradelevel_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket_TradeEntry::tradelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeLevel)
  return tradelevel_;
}
void MDHKGreyMarket_TradeEntry::set_tradelevel(::google::protobuf::int32 value) {
  
  tradelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeLevel)
}

// optional int64 TradePrice = 2;
void MDHKGreyMarket_TradeEntry::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradePrice)
  return tradeprice_;
}
void MDHKGreyMarket_TradeEntry::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradePrice)
}

// optional int64 TradeQty = 3;
void MDHKGreyMarket_TradeEntry::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeQty)
  return tradeqty_;
}
void MDHKGreyMarket_TradeEntry::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeQty)
}

// optional int64 TradeTime = 4;
void MDHKGreyMarket_TradeEntry::clear_tradetime() {
  tradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeTime)
  return tradetime_;
}
void MDHKGreyMarket_TradeEntry::set_tradetime(::google::protobuf::int64 value) {
  
  tradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeTime)
}

inline const MDHKGreyMarket_TradeEntry* MDHKGreyMarket_TradeEntry::internal_default_instance() {
  return &MDHKGreyMarket_TradeEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket

// optional string HTSCSecurityID = 1;
void MDHKGreyMarket::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDHKGreyMarket::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHKGreyMarket::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
void MDHKGreyMarket::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
void MDHKGreyMarket::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
::std::string* MDHKGreyMarket::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDHKGreyMarket::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHKGreyMarket::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDHKGreyMarket::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MDDate)
  return mddate_;
}
void MDHKGreyMarket::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MDDate)
}

// optional int32 MDTime = 3;
void MDHKGreyMarket::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MDTime)
  return mdtime_;
}
void MDHKGreyMarket::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDHKGreyMarket::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.DataTimestamp)
  return datatimestamp_;
}
void MDHKGreyMarket::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDHKGreyMarket::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDHKGreyMarket::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHKGreyMarket::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
void MDHKGreyMarket::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
void MDHKGreyMarket::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
::std::string* MDHKGreyMarket::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDHKGreyMarket::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHKGreyMarket::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDHKGreyMarket::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDHKGreyMarket::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDHKGreyMarket::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDHKGreyMarket::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDHKGreyMarket::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDHKGreyMarket::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.securityType)
}

// optional int64 ApplSeqNum = 8;
void MDHKGreyMarket::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHKGreyMarket::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.ApplSeqNum)
  return applseqnum_;
}
void MDHKGreyMarket::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.ApplSeqNum)
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
int MDHKGreyMarket::marketentries_size() const {
  return marketentries_.size();
}
void MDHKGreyMarket::clear_marketentries() {
  marketentries_.Clear();
}
const ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry& MDHKGreyMarket::marketentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Get(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* MDHKGreyMarket::mutable_marketentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* MDHKGreyMarket::add_marketentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >*
MDHKGreyMarket::mutable_marketentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return &marketentries_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >&
MDHKGreyMarket::marketentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_;
}

// optional int32 DataMultiplePowerOf10 = 10;
void MDHKGreyMarket::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDHKGreyMarket::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDHKGreyMarket::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.DataMultiplePowerOf10)
}

inline const MDHKGreyMarket* MDHKGreyMarket::internal_default_instance() {
  return &MDHKGreyMarket_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
