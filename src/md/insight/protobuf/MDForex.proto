syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDForex {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  double BidPrice = 10;
  double AskPrice = 11;
  double MidPrice = 12;
  double LastPrice = 13;
  double HighPrice = 14;
  double LowPrice = 15;
  double OpenPrice = 16;
  double ClosePrice = 17;
  double Volume = 18;
  int64 MessageNumber = 100;
}
