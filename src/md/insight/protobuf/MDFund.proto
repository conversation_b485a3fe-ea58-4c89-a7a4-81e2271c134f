syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDFund {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  double LastNav = 10;           // 单位净值
  double AccumulatedNav = 11;    // 累计净值
  double DailyReturn = 12;       // 日回报
  double TotalReturn = 13;       // 总回报
  double SubscriptionPrice = 14; // 申购价
  double RedemptionPrice = 15;   // 赎回价
  int64 MessageNumber = 100;
}
