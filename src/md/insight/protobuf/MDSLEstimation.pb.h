// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLEstimation.proto

#ifndef PROTOBUF_MDSLEstimation_2eproto__INCLUDED
#define PROTOBUF_MDSLEstimation_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
#include "MDSecurityLending.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSLEstimation_2eproto();
void protobuf_InitDefaults_MDSLEstimation_2eproto();
void protobuf_AssignDesc_MDSLEstimation_2eproto();
void protobuf_ShutdownFile_MDSLEstimation_2eproto();

class ADSLEstimationEntry;
class MDSLEstimation;

// ===================================================================

class MDSLEstimation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSLEstimation) */ {
 public:
  MDSLEstimation();
  virtual ~MDSLEstimation();

  MDSLEstimation(const MDSLEstimation& from);

  inline MDSLEstimation& operator=(const MDSLEstimation& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSLEstimation& default_instance();

  static const MDSLEstimation* internal_default_instance();

  void Swap(MDSLEstimation* other);

  // implements Message ----------------------------------------------

  inline MDSLEstimation* New() const { return New(NULL); }

  MDSLEstimation* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSLEstimation& from);
  void MergeFrom(const MDSLEstimation& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSLEstimation* other);
  void UnsafeMergeFrom(const MDSLEstimation& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 DataMultiplePowerOf10 = 8;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 8;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
  int longtermlends_size() const;
  void clear_longtermlends();
  static const int kLongTermLendsFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::ADSLEstimationEntry& longtermlends(int index) const;
  ::com::htsc::mdc::insight::model::ADSLEstimationEntry* mutable_longtermlends(int index);
  ::com::htsc::mdc::insight::model::ADSLEstimationEntry* add_longtermlends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >*
      mutable_longtermlends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >&
      longtermlends() const;

  // optional int64 LastPx = 10;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 10;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 11;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 11;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 HtscVolume = 14;
  void clear_htscvolume();
  static const int kHtscVolumeFieldNumber = 14;
  ::google::protobuf::int64 htscvolume() const;
  void set_htscvolume(::google::protobuf::int64 value);

  // optional int64 PreHtscVolume = 15;
  void clear_prehtscvolume();
  static const int kPreHtscVolumeFieldNumber = 15;
  ::google::protobuf::int64 prehtscvolume() const;
  void set_prehtscvolume(::google::protobuf::int64 value);

  // optional int64 WeightedRate = 16;
  void clear_weightedrate();
  static const int kWeightedRateFieldNumber = 16;
  ::google::protobuf::int64 weightedrate() const;
  void set_weightedrate(::google::protobuf::int64 value);

  // optional int64 PreWeightedRate = 17;
  void clear_preweightedrate();
  static const int kPreWeightedRateFieldNumber = 17;
  ::google::protobuf::int64 preweightedrate() const;
  void set_preweightedrate(::google::protobuf::int64 value);

  // optional int64 BestBorrowRate = 18;
  void clear_bestborrowrate();
  static const int kBestBorrowRateFieldNumber = 18;
  ::google::protobuf::int64 bestborrowrate() const;
  void set_bestborrowrate(::google::protobuf::int64 value);

  // optional int64 BestLendRate = 19;
  void clear_bestlendrate();
  static const int kBestLendRateFieldNumber = 19;
  ::google::protobuf::int64 bestlendrate() const;
  void set_bestlendrate(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
  int validborrows_size() const;
  void clear_validborrows();
  static const int kValidBorrowsFieldNumber = 20;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validborrows(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validborrows(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validborrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validborrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validborrows() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
  int validalends_size() const;
  void clear_validalends();
  static const int kValidALendsFieldNumber = 21;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validalends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validalends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validalends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validalends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validalends() const;

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
  int validblends_size() const;
  void clear_validblends();
  static const int kValidBLendsFieldNumber = 22;
  const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& validblends(int index) const;
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* mutable_validblends(int index);
  ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* add_validblends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
      mutable_validblends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
      validblends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
  int borrows_size() const;
  void clear_borrows();
  static const int kBorrowsFieldNumber = 23;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& borrows(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_borrows(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_borrows();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_borrows();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      borrows() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
  int alends_size() const;
  void clear_alends();
  static const int kALendsFieldNumber = 24;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& alends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_alends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_alends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_alends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      alends() const;

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
  int blends_size() const;
  void clear_blends();
  static const int kBLendsFieldNumber = 25;
  const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& blends(int index) const;
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* mutable_blends(int index);
  ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* add_blends();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
      mutable_blends();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
      blends() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSLEstimation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry > longtermlends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validborrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validalends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry > validblends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > borrows_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > alends_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry > blends_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 htscvolume_;
  ::google::protobuf::int64 prehtscvolume_;
  ::google::protobuf::int64 weightedrate_;
  ::google::protobuf::int64 preweightedrate_;
  ::google::protobuf::int64 bestborrowrate_;
  ::google::protobuf::int64 bestlendrate_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSLEstimation_2eproto_impl();
  friend void  protobuf_AddDesc_MDSLEstimation_2eproto_impl();
  friend void protobuf_AssignDesc_MDSLEstimation_2eproto();
  friend void protobuf_ShutdownFile_MDSLEstimation_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSLEstimation> MDSLEstimation_default_instance_;

// -------------------------------------------------------------------

class ADSLEstimationEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADSLEstimationEntry) */ {
 public:
  ADSLEstimationEntry();
  virtual ~ADSLEstimationEntry();

  ADSLEstimationEntry(const ADSLEstimationEntry& from);

  inline ADSLEstimationEntry& operator=(const ADSLEstimationEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADSLEstimationEntry& default_instance();

  static const ADSLEstimationEntry* internal_default_instance();

  void Swap(ADSLEstimationEntry* other);

  // implements Message ----------------------------------------------

  inline ADSLEstimationEntry* New() const { return New(NULL); }

  ADSLEstimationEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADSLEstimationEntry& from);
  void MergeFrom(const ADSLEstimationEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADSLEstimationEntry* other);
  void UnsafeMergeFrom(const ADSLEstimationEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Level = 1;
  void clear_level();
  static const int kLevelFieldNumber = 1;
  ::google::protobuf::int32 level() const;
  void set_level(::google::protobuf::int32 value);

  // optional int64 Rate = 2;
  void clear_rate();
  static const int kRateFieldNumber = 2;
  ::google::protobuf::int64 rate() const;
  void set_rate(::google::protobuf::int64 value);

  // optional string Term = 3;
  void clear_term();
  static const int kTermFieldNumber = 3;
  const ::std::string& term() const;
  void set_term(const ::std::string& value);
  void set_term(const char* value);
  void set_term(const char* value, size_t size);
  ::std::string* mutable_term();
  ::std::string* release_term();
  void set_allocated_term(::std::string* term);

  // optional int64 Amount = 4;
  void clear_amount();
  static const int kAmountFieldNumber = 4;
  ::google::protobuf::int64 amount() const;
  void set_amount(::google::protobuf::int64 value);

  // optional int32 PostponeProbability = 5;
  void clear_postponeprobability();
  static const int kPostponeProbabilityFieldNumber = 5;
  ::google::protobuf::int32 postponeprobability() const;
  void set_postponeprobability(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADSLEstimationEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr term_;
  ::google::protobuf::int64 rate_;
  ::google::protobuf::int32 level_;
  ::google::protobuf::int32 postponeprobability_;
  ::google::protobuf::int64 amount_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSLEstimation_2eproto_impl();
  friend void  protobuf_AddDesc_MDSLEstimation_2eproto_impl();
  friend void protobuf_AssignDesc_MDSLEstimation_2eproto();
  friend void protobuf_ShutdownFile_MDSLEstimation_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADSLEstimationEntry> ADSLEstimationEntry_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLEstimation

// optional string HTSCSecurityID = 1;
inline void MDSLEstimation::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLEstimation::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLEstimation::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
inline void MDSLEstimation::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
inline void MDSLEstimation::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
inline ::std::string* MDSLEstimation::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLEstimation::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLEstimation::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSLEstimation::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSLEstimation::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.MDDate)
  return mddate_;
}
inline void MDSLEstimation::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSLEstimation::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSLEstimation::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.MDTime)
  return mdtime_;
}
inline void MDSLEstimation::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSLEstimation::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.DataTimestamp)
  return datatimestamp_;
}
inline void MDSLEstimation::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDSLEstimation::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLEstimation::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLEstimation::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
inline void MDSLEstimation::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
inline void MDSLEstimation::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
inline ::std::string* MDSLEstimation::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLEstimation::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLEstimation::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDSLEstimation::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSLEstimation::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSLEstimation::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDSLEstimation::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSLEstimation::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSLEstimation::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.securityType)
}

// optional int32 DataMultiplePowerOf10 = 8;
inline void MDSLEstimation::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSLEstimation::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSLEstimation::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
inline int MDSLEstimation::longtermlends_size() const {
  return longtermlends_.size();
}
inline void MDSLEstimation::clear_longtermlends() {
  longtermlends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSLEstimationEntry& MDSLEstimation::longtermlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSLEstimationEntry* MDSLEstimation::mutable_longtermlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSLEstimationEntry* MDSLEstimation::add_longtermlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >*
MDSLEstimation::mutable_longtermlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return &longtermlends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >&
MDSLEstimation::longtermlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_;
}

// optional int64 LastPx = 10;
inline void MDSLEstimation::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LastPx)
  return lastpx_;
}
inline void MDSLEstimation::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.LastPx)
}

// optional int64 PreClosePx = 11;
inline void MDSLEstimation::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreClosePx)
  return preclosepx_;
}
inline void MDSLEstimation::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreClosePx)
}

// optional int64 HighRate = 12;
inline void MDSLEstimation::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HighRate)
  return highrate_;
}
inline void MDSLEstimation::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HighRate)
}

// optional int64 LowRate = 13;
inline void MDSLEstimation::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LowRate)
  return lowrate_;
}
inline void MDSLEstimation::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.LowRate)
}

// optional int64 HtscVolume = 14;
inline void MDSLEstimation::clear_htscvolume() {
  htscvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::htscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HtscVolume)
  return htscvolume_;
}
inline void MDSLEstimation::set_htscvolume(::google::protobuf::int64 value) {
  
  htscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HtscVolume)
}

// optional int64 PreHtscVolume = 15;
inline void MDSLEstimation::clear_prehtscvolume() {
  prehtscvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::prehtscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreHtscVolume)
  return prehtscvolume_;
}
inline void MDSLEstimation::set_prehtscvolume(::google::protobuf::int64 value) {
  
  prehtscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreHtscVolume)
}

// optional int64 WeightedRate = 16;
inline void MDSLEstimation::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.WeightedRate)
  return weightedrate_;
}
inline void MDSLEstimation::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.WeightedRate)
}

// optional int64 PreWeightedRate = 17;
inline void MDSLEstimation::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreWeightedRate)
  return preweightedrate_;
}
inline void MDSLEstimation::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreWeightedRate)
}

// optional int64 BestBorrowRate = 18;
inline void MDSLEstimation::clear_bestborrowrate() {
  bestborrowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::bestborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BestBorrowRate)
  return bestborrowrate_;
}
inline void MDSLEstimation::set_bestborrowrate(::google::protobuf::int64 value) {
  
  bestborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.BestBorrowRate)
}

// optional int64 BestLendRate = 19;
inline void MDSLEstimation::clear_bestlendrate() {
  bestlendrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLEstimation::bestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BestLendRate)
  return bestlendrate_;
}
inline void MDSLEstimation::set_bestlendrate(::google::protobuf::int64 value) {
  
  bestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.BestLendRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
inline int MDSLEstimation::validborrows_size() const {
  return validborrows_.size();
}
inline void MDSLEstimation::clear_validborrows() {
  validborrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return &validborrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
inline int MDSLEstimation::validalends_size() const {
  return validalends_.size();
}
inline void MDSLEstimation::clear_validalends() {
  validalends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validalends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validalends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validalends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validalends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return &validalends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validalends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
inline int MDSLEstimation::validblends_size() const {
  return validblends_.size();
}
inline void MDSLEstimation::clear_validblends() {
  validblends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validblends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validblends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validblends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validblends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return &validblends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validblends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
inline int MDSLEstimation::borrows_size() const {
  return borrows_.size();
}
inline void MDSLEstimation::clear_borrows() {
  borrows_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::borrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_borrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_borrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_borrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return &borrows_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::borrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
inline int MDSLEstimation::alends_size() const {
  return alends_.size();
}
inline void MDSLEstimation::clear_alends() {
  alends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::alends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_alends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_alends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_alends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return &alends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::alends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
inline int MDSLEstimation::blends_size() const {
  return blends_.size();
}
inline void MDSLEstimation::clear_blends() {
  blends_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::blends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_blends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_blends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_blends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return &blends_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::blends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_;
}

inline const MDSLEstimation* MDSLEstimation::internal_default_instance() {
  return &MDSLEstimation_default_instance_.get();
}
// -------------------------------------------------------------------

// ADSLEstimationEntry

// optional int32 Level = 1;
inline void ADSLEstimationEntry::clear_level() {
  level_ = 0;
}
inline ::google::protobuf::int32 ADSLEstimationEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Level)
  return level_;
}
inline void ADSLEstimationEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Level)
}

// optional int64 Rate = 2;
inline void ADSLEstimationEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADSLEstimationEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Rate)
  return rate_;
}
inline void ADSLEstimationEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Rate)
}

// optional string Term = 3;
inline void ADSLEstimationEntry::clear_term() {
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADSLEstimationEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  return term_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADSLEstimationEntry::set_term(const ::std::string& value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
inline void ADSLEstimationEntry::set_term(const char* value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
inline void ADSLEstimationEntry::set_term(const char* value, size_t size) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
inline ::std::string* ADSLEstimationEntry::mutable_term() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  return term_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADSLEstimationEntry::release_term() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  
  return term_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADSLEstimationEntry::set_allocated_term(::std::string* term) {
  if (term != NULL) {
    
  } else {
    
  }
  term_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), term);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}

// optional int64 Amount = 4;
inline void ADSLEstimationEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADSLEstimationEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Amount)
  return amount_;
}
inline void ADSLEstimationEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Amount)
}

// optional int32 PostponeProbability = 5;
inline void ADSLEstimationEntry::clear_postponeprobability() {
  postponeprobability_ = 0;
}
inline ::google::protobuf::int32 ADSLEstimationEntry::postponeprobability() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.PostponeProbability)
  return postponeprobability_;
}
inline void ADSLEstimationEntry::set_postponeprobability(::google::protobuf::int32 value) {
  
  postponeprobability_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.PostponeProbability)
}

inline const ADSLEstimationEntry* ADSLEstimationEntry::internal_default_instance() {
  return &ADSLEstimationEntry_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSLEstimation_2eproto__INCLUDED
