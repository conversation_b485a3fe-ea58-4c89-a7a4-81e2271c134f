// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSAQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDUSAQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDUSAQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDUSAQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDUSAQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDUSAQuote_2eproto() {
  protobuf_AddDesc_MDUSAQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDUSAQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDUSAQuote_descriptor_ = file->message_type(0);
  static const int MDUSAQuote_offsets_[22] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, nanosecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, timeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, bidprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, bidsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, bidsizenav_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, askprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, asksize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, asksizenav_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, bidmarket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, bidtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, askmarket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, asktime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, dataindex_),
  };
  MDUSAQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDUSAQuote_descriptor_,
      MDUSAQuote::internal_default_instance(),
      MDUSAQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDUSAQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDUSAQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDUSAQuote_descriptor_, MDUSAQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDUSAQuote_2eproto() {
  MDUSAQuote_default_instance_.Shutdown();
  delete MDUSAQuote_reflection_;
}

void protobuf_InitDefaults_MDUSAQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDUSAQuote_default_instance_.DefaultConstruct();
  MDUSAQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDUSAQuote_2eproto_once_);
void protobuf_InitDefaults_MDUSAQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDUSAQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDUSAQuote_2eproto_impl);
}
void protobuf_AddDesc_MDUSAQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDUSAQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MDUSAQuote.proto\022\032com.htsc.mdc.insight"
    ".model\032\023ESecurityType.proto\032\027ESecurityID"
    "Source.proto\"\220\004\n\nMDUSAQuote\022\026\n\016HTSCSecur"
    "ityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 "
    "\001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securityID"
    "Source\030\005 \001(\0162%.com.htsc.mdc.model.ESecur"
    "ityIDSource\0227\n\014securityType\030\006 \001(\0162!.com."
    "htsc.mdc.model.ESecurityType\022\024\n\014Exchange"
    "Date\030\007 \001(\005\022\024\n\014ExchangeTime\030\010 \001(\005\022\022\n\nNano"
    "second\030\t \001(\005\022\035\n\025DataMultiplePowerOf10\030\n "
    "\001(\005\022\021\n\tTimeIndex\030\013 \001(\005\022\020\n\010BidPrice\030\014 \001(\003"
    "\022\017\n\007BidSize\030\r \001(\003\022\022\n\nBidSizeNAV\030\016 \001(\003\022\020\n"
    "\010AskPrice\030\017 \001(\003\022\017\n\007AskSize\030\020 \001(\003\022\022\n\nAskS"
    "izeNAV\030\021 \001(\003\022\021\n\tBidMarket\030\022 \001(\005\022\017\n\007BidTi"
    "me\030\023 \001(\003\022\021\n\tAskMarket\030\024 \001(\005\022\017\n\007AskTime\030\025"
    " \001(\003\022\021\n\tDataIndex\030\026 \001(\003B3\n\032com.htsc.mdc."
    "insight.modelB\020MDUSAQuoteProtosH\001\240\001\001b\006pr"
    "oto3", 684);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDUSAQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDUSAQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDUSAQuote_2eproto_once_);
void protobuf_AddDesc_MDUSAQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDUSAQuote_2eproto_once_,
                 &protobuf_AddDesc_MDUSAQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDUSAQuote_2eproto {
  StaticDescriptorInitializer_MDUSAQuote_2eproto() {
    protobuf_AddDesc_MDUSAQuote_2eproto();
  }
} static_descriptor_initializer_MDUSAQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDUSAQuote::kHTSCSecurityIDFieldNumber;
const int MDUSAQuote::kMDDateFieldNumber;
const int MDUSAQuote::kMDTimeFieldNumber;
const int MDUSAQuote::kDataTimestampFieldNumber;
const int MDUSAQuote::kSecurityIDSourceFieldNumber;
const int MDUSAQuote::kSecurityTypeFieldNumber;
const int MDUSAQuote::kExchangeDateFieldNumber;
const int MDUSAQuote::kExchangeTimeFieldNumber;
const int MDUSAQuote::kNanosecondFieldNumber;
const int MDUSAQuote::kDataMultiplePowerOf10FieldNumber;
const int MDUSAQuote::kTimeIndexFieldNumber;
const int MDUSAQuote::kBidPriceFieldNumber;
const int MDUSAQuote::kBidSizeFieldNumber;
const int MDUSAQuote::kBidSizeNAVFieldNumber;
const int MDUSAQuote::kAskPriceFieldNumber;
const int MDUSAQuote::kAskSizeFieldNumber;
const int MDUSAQuote::kAskSizeNAVFieldNumber;
const int MDUSAQuote::kBidMarketFieldNumber;
const int MDUSAQuote::kBidTimeFieldNumber;
const int MDUSAQuote::kAskMarketFieldNumber;
const int MDUSAQuote::kAskTimeFieldNumber;
const int MDUSAQuote::kDataIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDUSAQuote::MDUSAQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDUSAQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDUSAQuote)
}

void MDUSAQuote::InitAsDefaultInstance() {
}

MDUSAQuote::MDUSAQuote(const MDUSAQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDUSAQuote)
}

void MDUSAQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&askmarket_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(askmarket_));
  _cached_size_ = 0;
}

MDUSAQuote::~MDUSAQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDUSAQuote)
  SharedDtor();
}

void MDUSAQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDUSAQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDUSAQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDUSAQuote_descriptor_;
}

const MDUSAQuote& MDUSAQuote::default_instance() {
  protobuf_InitDefaults_MDUSAQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDUSAQuote> MDUSAQuote_default_instance_;

MDUSAQuote* MDUSAQuote::New(::google::protobuf::Arena* arena) const {
  MDUSAQuote* n = new MDUSAQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDUSAQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDUSAQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDUSAQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDUSAQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(nanosecond_, timeindex_);
  ZR_(askprice_, asksize_);
  ZR_(asksizenav_, askmarket_);
  bidmarket_ = 0;

#undef ZR_HELPER_
#undef ZR_

}

bool MDUSAQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDUSAQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_Nanosecond;
        break;
      }

      // optional int32 Nanosecond = 9;
      case 9: {
        if (tag == 72) {
         parse_Nanosecond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &nanosecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 10;
      case 10: {
        if (tag == 80) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_TimeIndex;
        break;
      }

      // optional int32 TimeIndex = 11;
      case 11: {
        if (tag == 88) {
         parse_TimeIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &timeindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_BidPrice;
        break;
      }

      // optional int64 BidPrice = 12;
      case 12: {
        if (tag == 96) {
         parse_BidPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_BidSize;
        break;
      }

      // optional int64 BidSize = 13;
      case 13: {
        if (tag == 104) {
         parse_BidSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_BidSizeNAV;
        break;
      }

      // optional int64 BidSizeNAV = 14;
      case 14: {
        if (tag == 112) {
         parse_BidSizeNAV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidsizenav_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_AskPrice;
        break;
      }

      // optional int64 AskPrice = 15;
      case 15: {
        if (tag == 120) {
         parse_AskPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &askprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_AskSize;
        break;
      }

      // optional int64 AskSize = 16;
      case 16: {
        if (tag == 128) {
         parse_AskSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &asksize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AskSizeNAV;
        break;
      }

      // optional int64 AskSizeNAV = 17;
      case 17: {
        if (tag == 136) {
         parse_AskSizeNAV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &asksizenav_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_BidMarket;
        break;
      }

      // optional int32 BidMarket = 18;
      case 18: {
        if (tag == 144) {
         parse_BidMarket:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidmarket_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_BidTime;
        break;
      }

      // optional int64 BidTime = 19;
      case 19: {
        if (tag == 152) {
         parse_BidTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AskMarket;
        break;
      }

      // optional int32 AskMarket = 20;
      case 20: {
        if (tag == 160) {
         parse_AskMarket:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &askmarket_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_AskTime;
        break;
      }

      // optional int64 AskTime = 21;
      case 21: {
        if (tag == 168) {
         parse_AskTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &asktime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_DataIndex;
        break;
      }

      // optional int64 DataIndex = 22;
      case 22: {
        if (tag == 176) {
         parse_DataIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dataindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDUSAQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDUSAQuote)
  return false;
#undef DO_
}

void MDUSAQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDUSAQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->nanosecond(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->datamultiplepowerof10(), output);
  }

  // optional int32 TimeIndex = 11;
  if (this->timeindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->timeindex(), output);
  }

  // optional int64 BidPrice = 12;
  if (this->bidprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->bidprice(), output);
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->bidsize(), output);
  }

  // optional int64 BidSizeNAV = 14;
  if (this->bidsizenav() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->bidsizenav(), output);
  }

  // optional int64 AskPrice = 15;
  if (this->askprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->askprice(), output);
  }

  // optional int64 AskSize = 16;
  if (this->asksize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->asksize(), output);
  }

  // optional int64 AskSizeNAV = 17;
  if (this->asksizenav() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->asksizenav(), output);
  }

  // optional int32 BidMarket = 18;
  if (this->bidmarket() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->bidmarket(), output);
  }

  // optional int64 BidTime = 19;
  if (this->bidtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->bidtime(), output);
  }

  // optional int32 AskMarket = 20;
  if (this->askmarket() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->askmarket(), output);
  }

  // optional int64 AskTime = 21;
  if (this->asktime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->asktime(), output);
  }

  // optional int64 DataIndex = 22;
  if (this->dataindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->dataindex(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDUSAQuote)
}

::google::protobuf::uint8* MDUSAQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDUSAQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->nanosecond(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->datamultiplepowerof10(), target);
  }

  // optional int32 TimeIndex = 11;
  if (this->timeindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->timeindex(), target);
  }

  // optional int64 BidPrice = 12;
  if (this->bidprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->bidprice(), target);
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->bidsize(), target);
  }

  // optional int64 BidSizeNAV = 14;
  if (this->bidsizenav() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->bidsizenav(), target);
  }

  // optional int64 AskPrice = 15;
  if (this->askprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->askprice(), target);
  }

  // optional int64 AskSize = 16;
  if (this->asksize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->asksize(), target);
  }

  // optional int64 AskSizeNAV = 17;
  if (this->asksizenav() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->asksizenav(), target);
  }

  // optional int32 BidMarket = 18;
  if (this->bidmarket() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->bidmarket(), target);
  }

  // optional int64 BidTime = 19;
  if (this->bidtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->bidtime(), target);
  }

  // optional int32 AskMarket = 20;
  if (this->askmarket() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->askmarket(), target);
  }

  // optional int64 AskTime = 21;
  if (this->asktime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->asktime(), target);
  }

  // optional int64 DataIndex = 22;
  if (this->dataindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->dataindex(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDUSAQuote)
  return target;
}

size_t MDUSAQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDUSAQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->nanosecond());
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 TimeIndex = 11;
  if (this->timeindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->timeindex());
  }

  // optional int64 BidPrice = 12;
  if (this->bidprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidprice());
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidsize());
  }

  // optional int64 BidSizeNAV = 14;
  if (this->bidsizenav() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidsizenav());
  }

  // optional int64 AskPrice = 15;
  if (this->askprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->askprice());
  }

  // optional int64 AskSize = 16;
  if (this->asksize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->asksize());
  }

  // optional int64 AskSizeNAV = 17;
  if (this->asksizenav() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->asksizenav());
  }

  // optional int32 BidMarket = 18;
  if (this->bidmarket() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidmarket());
  }

  // optional int64 BidTime = 19;
  if (this->bidtime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidtime());
  }

  // optional int32 AskMarket = 20;
  if (this->askmarket() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->askmarket());
  }

  // optional int64 AskTime = 21;
  if (this->asktime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->asktime());
  }

  // optional int64 DataIndex = 22;
  if (this->dataindex() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dataindex());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDUSAQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDUSAQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDUSAQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDUSAQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDUSAQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDUSAQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDUSAQuote::MergeFrom(const MDUSAQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDUSAQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDUSAQuote::UnsafeMergeFrom(const MDUSAQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.nanosecond() != 0) {
    set_nanosecond(from.nanosecond());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.timeindex() != 0) {
    set_timeindex(from.timeindex());
  }
  if (from.bidprice() != 0) {
    set_bidprice(from.bidprice());
  }
  if (from.bidsize() != 0) {
    set_bidsize(from.bidsize());
  }
  if (from.bidsizenav() != 0) {
    set_bidsizenav(from.bidsizenav());
  }
  if (from.askprice() != 0) {
    set_askprice(from.askprice());
  }
  if (from.asksize() != 0) {
    set_asksize(from.asksize());
  }
  if (from.asksizenav() != 0) {
    set_asksizenav(from.asksizenav());
  }
  if (from.bidmarket() != 0) {
    set_bidmarket(from.bidmarket());
  }
  if (from.bidtime() != 0) {
    set_bidtime(from.bidtime());
  }
  if (from.askmarket() != 0) {
    set_askmarket(from.askmarket());
  }
  if (from.asktime() != 0) {
    set_asktime(from.asktime());
  }
  if (from.dataindex() != 0) {
    set_dataindex(from.dataindex());
  }
}

void MDUSAQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDUSAQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDUSAQuote::CopyFrom(const MDUSAQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDUSAQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDUSAQuote::IsInitialized() const {

  return true;
}

void MDUSAQuote::Swap(MDUSAQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDUSAQuote::InternalSwap(MDUSAQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(nanosecond_, other->nanosecond_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(timeindex_, other->timeindex_);
  std::swap(bidprice_, other->bidprice_);
  std::swap(bidsize_, other->bidsize_);
  std::swap(bidsizenav_, other->bidsizenav_);
  std::swap(askprice_, other->askprice_);
  std::swap(asksize_, other->asksize_);
  std::swap(asksizenav_, other->asksizenav_);
  std::swap(bidmarket_, other->bidmarket_);
  std::swap(bidtime_, other->bidtime_);
  std::swap(askmarket_, other->askmarket_);
  std::swap(asktime_, other->asktime_);
  std::swap(dataindex_, other->dataindex_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDUSAQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDUSAQuote_descriptor_;
  metadata.reflection = MDUSAQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSAQuote

// optional string HTSCSecurityID = 1;
void MDUSAQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSAQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
void MDUSAQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
void MDUSAQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
::std::string* MDUSAQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSAQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDUSAQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDUSAQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.MDDate)
  return mddate_;
}
void MDUSAQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDUSAQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDUSAQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.MDTime)
  return mdtime_;
}
void MDUSAQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDUSAQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataTimestamp)
  return datatimestamp_;
}
void MDUSAQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDUSAQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDUSAQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDUSAQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDUSAQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDUSAQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDUSAQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.securityType)
}

// optional int32 ExchangeDate = 7;
void MDUSAQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDUSAQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeDate)
  return exchangedate_;
}
void MDUSAQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDUSAQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDUSAQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeTime)
  return exchangetime_;
}
void MDUSAQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeTime)
}

// optional int32 Nanosecond = 9;
void MDUSAQuote::clear_nanosecond() {
  nanosecond_ = 0;
}
::google::protobuf::int32 MDUSAQuote::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.Nanosecond)
  return nanosecond_;
}
void MDUSAQuote::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.Nanosecond)
}

// optional int32 DataMultiplePowerOf10 = 10;
void MDUSAQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDUSAQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDUSAQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 11;
void MDUSAQuote::clear_timeindex() {
  timeindex_ = 0;
}
::google::protobuf::int32 MDUSAQuote::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.TimeIndex)
  return timeindex_;
}
void MDUSAQuote::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.TimeIndex)
}

// optional int64 BidPrice = 12;
void MDUSAQuote::clear_bidprice() {
  bidprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::bidprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidPrice)
  return bidprice_;
}
void MDUSAQuote::set_bidprice(::google::protobuf::int64 value) {
  
  bidprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidPrice)
}

// optional int64 BidSize = 13;
void MDUSAQuote::clear_bidsize() {
  bidsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::bidsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidSize)
  return bidsize_;
}
void MDUSAQuote::set_bidsize(::google::protobuf::int64 value) {
  
  bidsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidSize)
}

// optional int64 BidSizeNAV = 14;
void MDUSAQuote::clear_bidsizenav() {
  bidsizenav_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::bidsizenav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidSizeNAV)
  return bidsizenav_;
}
void MDUSAQuote::set_bidsizenav(::google::protobuf::int64 value) {
  
  bidsizenav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidSizeNAV)
}

// optional int64 AskPrice = 15;
void MDUSAQuote::clear_askprice() {
  askprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::askprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskPrice)
  return askprice_;
}
void MDUSAQuote::set_askprice(::google::protobuf::int64 value) {
  
  askprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskPrice)
}

// optional int64 AskSize = 16;
void MDUSAQuote::clear_asksize() {
  asksize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::asksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskSize)
  return asksize_;
}
void MDUSAQuote::set_asksize(::google::protobuf::int64 value) {
  
  asksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskSize)
}

// optional int64 AskSizeNAV = 17;
void MDUSAQuote::clear_asksizenav() {
  asksizenav_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::asksizenav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskSizeNAV)
  return asksizenav_;
}
void MDUSAQuote::set_asksizenav(::google::protobuf::int64 value) {
  
  asksizenav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskSizeNAV)
}

// optional int32 BidMarket = 18;
void MDUSAQuote::clear_bidmarket() {
  bidmarket_ = 0;
}
::google::protobuf::int32 MDUSAQuote::bidmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidMarket)
  return bidmarket_;
}
void MDUSAQuote::set_bidmarket(::google::protobuf::int32 value) {
  
  bidmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidMarket)
}

// optional int64 BidTime = 19;
void MDUSAQuote::clear_bidtime() {
  bidtime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::bidtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidTime)
  return bidtime_;
}
void MDUSAQuote::set_bidtime(::google::protobuf::int64 value) {
  
  bidtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidTime)
}

// optional int32 AskMarket = 20;
void MDUSAQuote::clear_askmarket() {
  askmarket_ = 0;
}
::google::protobuf::int32 MDUSAQuote::askmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskMarket)
  return askmarket_;
}
void MDUSAQuote::set_askmarket(::google::protobuf::int32 value) {
  
  askmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskMarket)
}

// optional int64 AskTime = 21;
void MDUSAQuote::clear_asktime() {
  asktime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::asktime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskTime)
  return asktime_;
}
void MDUSAQuote::set_asktime(::google::protobuf::int64 value) {
  
  asktime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskTime)
}

// optional int64 DataIndex = 22;
void MDUSAQuote::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAQuote::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataIndex)
  return dataindex_;
}
void MDUSAQuote::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataIndex)
}

inline const MDUSAQuote* MDUSAQuote::internal_default_instance() {
  return &MDUSAQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
