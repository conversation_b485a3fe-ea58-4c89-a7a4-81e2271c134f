// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSimpleTick.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSimpleTick.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSimpleTick_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSimpleTick_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADIndicators_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADIndicators_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSimpleTick_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSimpleTick_2eproto() {
  protobuf_AddDesc_MDSimpleTick_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSimpleTick.proto");
  GOOGLE_CHECK(file != NULL);
  MDSimpleTick_descriptor_ = file->message_type(0);
  static const int MDSimpleTick_offsets_[26] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, iopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, preiopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, openinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, settleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, presettleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, adindicators_),
  };
  MDSimpleTick_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSimpleTick_descriptor_,
      MDSimpleTick::internal_default_instance(),
      MDSimpleTick_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSimpleTick),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSimpleTick, _internal_metadata_));
  ADIndicators_descriptor_ = file->message_type(1);
  static const int ADIndicators_offsets_[21] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1101_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1102_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1103_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1104_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1105_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1106_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1107_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1108_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1109_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1110_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1111_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1112_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1113_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1114_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1115_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1116_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1117_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1118_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1119_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1120_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, ind1121_),
  };
  ADIndicators_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADIndicators_descriptor_,
      ADIndicators::internal_default_instance(),
      ADIndicators_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADIndicators),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADIndicators, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSimpleTick_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSimpleTick_descriptor_, MDSimpleTick::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADIndicators_descriptor_, ADIndicators::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSimpleTick_2eproto() {
  MDSimpleTick_default_instance_.Shutdown();
  delete MDSimpleTick_reflection_;
  ADIndicators_default_instance_.Shutdown();
  delete ADIndicators_reflection_;
}

void protobuf_InitDefaults_MDSimpleTick_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSimpleTick_default_instance_.DefaultConstruct();
  ADIndicators_default_instance_.DefaultConstruct();
  MDSimpleTick_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADIndicators_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSimpleTick_2eproto_once_);
void protobuf_InitDefaults_MDSimpleTick_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSimpleTick_2eproto_once_,
                 &protobuf_InitDefaults_MDSimpleTick_2eproto_impl);
}
void protobuf_AddDesc_MDSimpleTick_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSimpleTick_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\022MDSimpleTick.proto\022\032com.htsc.mdc.insig"
    "ht.model\032\023ESecurityType.proto\032\027ESecurity"
    "IDSource.proto\"\241\005\n\014MDSimpleTick\022\026\n\016HTSCS"
    "ecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTim"
    "e\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020Tradin"
    "gPhaseCode\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001"
    "(\0162%.com.htsc.mdc.model.ESecurityIDSourc"
    "e\0227\n\014securityType\030\007 \001(\0162!.com.htsc.mdc.m"
    "odel.ESecurityType\022\021\n\tNumTrades\030\010 \001(\003\022\030\n"
    "\020TotalVolumeTrade\030\t \001(\003\022\027\n\017TotalValueTra"
    "de\030\n \001(\003\022\022\n\nPreClosePx\030\013 \001(\003\022\016\n\006LastPx\030\014"
    " \001(\003\022\016\n\006OpenPx\030\r \001(\003\022\017\n\007ClosePx\030\016 \001(\003\022\016\n"
    "\006HighPx\030\017 \001(\003\022\r\n\005LowPx\030\020 \001(\003\022\014\n\004IOPV\030\021 \001"
    "(\003\022\017\n\007PreIOPV\030\022 \001(\003\022\024\n\014OpenInterest\030\023 \001("
    "\003\022\027\n\017PreOpenInterest\030\024 \001(\003\022\023\n\013SettlePric"
    "e\030\025 \001(\003\022\026\n\016PreSettlePrice\030\026 \001(\003\022\024\n\014Excha"
    "ngeDate\030\027 \001(\005\022\024\n\014ExchangeTime\030\030 \001(\005\022\035\n\025D"
    "ataMultiplePowerOf10\030\031 \001(\005\022>\n\014ADIndicato"
    "rs\030( \001(\0132(.com.htsc.mdc.insight.model.AD"
    "Indicators\"\363\002\n\014ADIndicators\022\017\n\007Ind1101\030\001"
    " \001(\003\022\017\n\007Ind1102\030\002 \001(\003\022\017\n\007Ind1103\030\003 \001(\003\022\017"
    "\n\007Ind1104\030\004 \001(\003\022\017\n\007Ind1105\030\005 \001(\003\022\017\n\007Ind1"
    "106\030\006 \001(\003\022\017\n\007Ind1107\030\007 \001(\003\022\017\n\007Ind1108\030\010 "
    "\001(\003\022\017\n\007Ind1109\030\t \001(\003\022\017\n\007Ind1110\030\n \001(\003\022\017\n"
    "\007Ind1111\030\013 \001(\003\022\017\n\007Ind1112\030\014 \001(\003\022\017\n\007Ind11"
    "13\030\r \001(\003\022\017\n\007Ind1114\030\016 \001(\003\022\017\n\007Ind1115\030\017 \001"
    "(\003\022\017\n\007Ind1116\030\020 \001(\003\022\017\n\007Ind1117\030\021 \001(\003\022\017\n\007"
    "Ind1118\030\022 \001(\003\022\017\n\007Ind1119\030\023 \001(\003\022\017\n\007Ind112"
    "0\030\024 \001(\003\022\017\n\007Ind1121\030\025 \001(\003B5\n\032com.htsc.mdc"
    ".insight.modelB\022MDSimpleTickProtosH\001\240\001\001b"
    "\006proto3", 1207);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSimpleTick.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSimpleTick_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSimpleTick_2eproto_once_);
void protobuf_AddDesc_MDSimpleTick_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSimpleTick_2eproto_once_,
                 &protobuf_AddDesc_MDSimpleTick_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSimpleTick_2eproto {
  StaticDescriptorInitializer_MDSimpleTick_2eproto() {
    protobuf_AddDesc_MDSimpleTick_2eproto();
  }
} static_descriptor_initializer_MDSimpleTick_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSimpleTick::kHTSCSecurityIDFieldNumber;
const int MDSimpleTick::kMDDateFieldNumber;
const int MDSimpleTick::kMDTimeFieldNumber;
const int MDSimpleTick::kDataTimestampFieldNumber;
const int MDSimpleTick::kTradingPhaseCodeFieldNumber;
const int MDSimpleTick::kSecurityIDSourceFieldNumber;
const int MDSimpleTick::kSecurityTypeFieldNumber;
const int MDSimpleTick::kNumTradesFieldNumber;
const int MDSimpleTick::kTotalVolumeTradeFieldNumber;
const int MDSimpleTick::kTotalValueTradeFieldNumber;
const int MDSimpleTick::kPreClosePxFieldNumber;
const int MDSimpleTick::kLastPxFieldNumber;
const int MDSimpleTick::kOpenPxFieldNumber;
const int MDSimpleTick::kClosePxFieldNumber;
const int MDSimpleTick::kHighPxFieldNumber;
const int MDSimpleTick::kLowPxFieldNumber;
const int MDSimpleTick::kIOPVFieldNumber;
const int MDSimpleTick::kPreIOPVFieldNumber;
const int MDSimpleTick::kOpenInterestFieldNumber;
const int MDSimpleTick::kPreOpenInterestFieldNumber;
const int MDSimpleTick::kSettlePriceFieldNumber;
const int MDSimpleTick::kPreSettlePriceFieldNumber;
const int MDSimpleTick::kExchangeDateFieldNumber;
const int MDSimpleTick::kExchangeTimeFieldNumber;
const int MDSimpleTick::kDataMultiplePowerOf10FieldNumber;
const int MDSimpleTick::kADIndicatorsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSimpleTick::MDSimpleTick()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSimpleTick_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSimpleTick)
}

void MDSimpleTick::InitAsDefaultInstance() {
  adindicators_ = const_cast< ::com::htsc::mdc::insight::model::ADIndicators*>(
      ::com::htsc::mdc::insight::model::ADIndicators::internal_default_instance());
}

MDSimpleTick::MDSimpleTick(const MDSimpleTick& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSimpleTick)
}

void MDSimpleTick::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  adindicators_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSimpleTick::~MDSimpleTick() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSimpleTick)
  SharedDtor();
}

void MDSimpleTick::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDSimpleTick_default_instance_.get()) {
    delete adindicators_;
  }
}

void MDSimpleTick::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSimpleTick::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSimpleTick_descriptor_;
}

const MDSimpleTick& MDSimpleTick::default_instance() {
  protobuf_InitDefaults_MDSimpleTick_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSimpleTick> MDSimpleTick_default_instance_;

MDSimpleTick* MDSimpleTick::New(::google::protobuf::Arena* arena) const {
  MDSimpleTick* n = new MDSimpleTick;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSimpleTick::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSimpleTick)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSimpleTick, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSimpleTick*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, numtrades_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(totalvolumetrade_, lowpx_);
  ZR_(iopv_, exchangetime_);
  datamultiplepowerof10_ = 0;
  if (GetArenaNoVirtual() == NULL && adindicators_ != NULL) delete adindicators_;
  adindicators_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDSimpleTick::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSimpleTick)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 8;
      case 8: {
        if (tag == 64) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 9;
      case 9: {
        if (tag == 72) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 10;
      case 10: {
        if (tag == 80) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 11;
      case 11: {
        if (tag == 88) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 12;
      case 12: {
        if (tag == 96) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 13;
      case 13: {
        if (tag == 104) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 14;
      case 14: {
        if (tag == 112) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 15;
      case 15: {
        if (tag == 120) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 16;
      case 16: {
        if (tag == 128) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_IOPV;
        break;
      }

      // optional int64 IOPV = 17;
      case 17: {
        if (tag == 136) {
         parse_IOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &iopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_PreIOPV;
        break;
      }

      // optional int64 PreIOPV = 18;
      case 18: {
        if (tag == 144) {
         parse_PreIOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preiopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_OpenInterest;
        break;
      }

      // optional int64 OpenInterest = 19;
      case 19: {
        if (tag == 152) {
         parse_OpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_PreOpenInterest;
        break;
      }

      // optional int64 PreOpenInterest = 20;
      case 20: {
        if (tag == 160) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_SettlePrice;
        break;
      }

      // optional int64 SettlePrice = 21;
      case 21: {
        if (tag == 168) {
         parse_SettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_PreSettlePrice;
        break;
      }

      // optional int64 PreSettlePrice = 22;
      case 22: {
        if (tag == 176) {
         parse_PreSettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 23;
      case 23: {
        if (tag == 184) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 24;
      case 24: {
        if (tag == 192) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 25;
      case 25: {
        if (tag == 200) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_ADIndicators;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
      case 40: {
        if (tag == 322) {
         parse_ADIndicators:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_adindicators()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSimpleTick)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSimpleTick)
  return false;
#undef DO_
}

void MDSimpleTick::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSimpleTick)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 NumTrades = 8;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 9;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->totalvaluetrade(), output);
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->preclosepx(), output);
  }

  // optional int64 LastPx = 12;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->lastpx(), output);
  }

  // optional int64 OpenPx = 13;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->openpx(), output);
  }

  // optional int64 ClosePx = 14;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->closepx(), output);
  }

  // optional int64 HighPx = 15;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->highpx(), output);
  }

  // optional int64 LowPx = 16;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->lowpx(), output);
  }

  // optional int64 IOPV = 17;
  if (this->iopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->iopv(), output);
  }

  // optional int64 PreIOPV = 18;
  if (this->preiopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->preiopv(), output);
  }

  // optional int64 OpenInterest = 19;
  if (this->openinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->openinterest(), output);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->preopeninterest(), output);
  }

  // optional int64 SettlePrice = 21;
  if (this->settleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->settleprice(), output);
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->presettleprice(), output);
  }

  // optional int32 ExchangeDate = 23;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(23, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 24;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(24, this->exchangetime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 25;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->datamultiplepowerof10(), output);
  }

  // optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
  if (this->has_adindicators()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      40, *this->adindicators_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSimpleTick)
}

::google::protobuf::uint8* MDSimpleTick::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSimpleTick)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 NumTrades = 8;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 9;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->totalvaluetrade(), target);
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->preclosepx(), target);
  }

  // optional int64 LastPx = 12;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->lastpx(), target);
  }

  // optional int64 OpenPx = 13;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->openpx(), target);
  }

  // optional int64 ClosePx = 14;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->closepx(), target);
  }

  // optional int64 HighPx = 15;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->highpx(), target);
  }

  // optional int64 LowPx = 16;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->lowpx(), target);
  }

  // optional int64 IOPV = 17;
  if (this->iopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->iopv(), target);
  }

  // optional int64 PreIOPV = 18;
  if (this->preiopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->preiopv(), target);
  }

  // optional int64 OpenInterest = 19;
  if (this->openinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->openinterest(), target);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->preopeninterest(), target);
  }

  // optional int64 SettlePrice = 21;
  if (this->settleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->settleprice(), target);
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->presettleprice(), target);
  }

  // optional int32 ExchangeDate = 23;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(23, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 24;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(24, this->exchangetime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 25;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->datamultiplepowerof10(), target);
  }

  // optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
  if (this->has_adindicators()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        40, *this->adindicators_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSimpleTick)
  return target;
}

size_t MDSimpleTick::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSimpleTick)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 NumTrades = 8;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 9;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 LastPx = 12;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 13;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 14;
  if (this->closepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 15;
  if (this->highpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 16;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int64 IOPV = 17;
  if (this->iopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->iopv());
  }

  // optional int64 PreIOPV = 18;
  if (this->preiopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preiopv());
  }

  // optional int64 OpenInterest = 19;
  if (this->openinterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openinterest());
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preopeninterest());
  }

  // optional int64 SettlePrice = 21;
  if (this->settleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settleprice());
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->presettleprice());
  }

  // optional int32 ExchangeDate = 23;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 24;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 25;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
  if (this->has_adindicators()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->adindicators_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSimpleTick::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSimpleTick)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSimpleTick* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSimpleTick>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSimpleTick)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSimpleTick)
    UnsafeMergeFrom(*source);
  }
}

void MDSimpleTick::MergeFrom(const MDSimpleTick& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSimpleTick)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSimpleTick::UnsafeMergeFrom(const MDSimpleTick& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.iopv() != 0) {
    set_iopv(from.iopv());
  }
  if (from.preiopv() != 0) {
    set_preiopv(from.preiopv());
  }
  if (from.openinterest() != 0) {
    set_openinterest(from.openinterest());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.settleprice() != 0) {
    set_settleprice(from.settleprice());
  }
  if (from.presettleprice() != 0) {
    set_presettleprice(from.presettleprice());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.has_adindicators()) {
    mutable_adindicators()->::com::htsc::mdc::insight::model::ADIndicators::MergeFrom(from.adindicators());
  }
}

void MDSimpleTick::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSimpleTick)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSimpleTick::CopyFrom(const MDSimpleTick& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSimpleTick)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSimpleTick::IsInitialized() const {

  return true;
}

void MDSimpleTick::Swap(MDSimpleTick* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSimpleTick::InternalSwap(MDSimpleTick* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(iopv_, other->iopv_);
  std::swap(preiopv_, other->preiopv_);
  std::swap(openinterest_, other->openinterest_);
  std::swap(preopeninterest_, other->preopeninterest_);
  std::swap(settleprice_, other->settleprice_);
  std::swap(presettleprice_, other->presettleprice_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(adindicators_, other->adindicators_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSimpleTick::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSimpleTick_descriptor_;
  metadata.reflection = MDSimpleTick_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSimpleTick

// optional string HTSCSecurityID = 1;
void MDSimpleTick::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSimpleTick::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSimpleTick::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
void MDSimpleTick::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
void MDSimpleTick::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
::std::string* MDSimpleTick::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSimpleTick::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSimpleTick::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSimpleTick::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSimpleTick::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.MDDate)
  return mddate_;
}
void MDSimpleTick::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.MDDate)
}

// optional int32 MDTime = 3;
void MDSimpleTick::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSimpleTick::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.MDTime)
  return mdtime_;
}
void MDSimpleTick::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSimpleTick::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.DataTimestamp)
  return datatimestamp_;
}
void MDSimpleTick::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSimpleTick::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSimpleTick::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSimpleTick::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
void MDSimpleTick::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
void MDSimpleTick::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
::std::string* MDSimpleTick::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSimpleTick::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSimpleTick::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSimpleTick::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSimpleTick::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSimpleTick::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSimpleTick::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSimpleTick::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSimpleTick::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.securityType)
}

// optional int64 NumTrades = 8;
void MDSimpleTick::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.NumTrades)
  return numtrades_;
}
void MDSimpleTick::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.NumTrades)
}

// optional int64 TotalVolumeTrade = 9;
void MDSimpleTick::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDSimpleTick::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 10;
void MDSimpleTick::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TotalValueTrade)
  return totalvaluetrade_;
}
void MDSimpleTick::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TotalValueTrade)
}

// optional int64 PreClosePx = 11;
void MDSimpleTick::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreClosePx)
  return preclosepx_;
}
void MDSimpleTick::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreClosePx)
}

// optional int64 LastPx = 12;
void MDSimpleTick::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.LastPx)
  return lastpx_;
}
void MDSimpleTick::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.LastPx)
}

// optional int64 OpenPx = 13;
void MDSimpleTick::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.OpenPx)
  return openpx_;
}
void MDSimpleTick::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.OpenPx)
}

// optional int64 ClosePx = 14;
void MDSimpleTick::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ClosePx)
  return closepx_;
}
void MDSimpleTick::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ClosePx)
}

// optional int64 HighPx = 15;
void MDSimpleTick::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.HighPx)
  return highpx_;
}
void MDSimpleTick::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.HighPx)
}

// optional int64 LowPx = 16;
void MDSimpleTick::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.LowPx)
  return lowpx_;
}
void MDSimpleTick::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.LowPx)
}

// optional int64 IOPV = 17;
void MDSimpleTick::clear_iopv() {
  iopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::iopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.IOPV)
  return iopv_;
}
void MDSimpleTick::set_iopv(::google::protobuf::int64 value) {
  
  iopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.IOPV)
}

// optional int64 PreIOPV = 18;
void MDSimpleTick::clear_preiopv() {
  preiopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::preiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreIOPV)
  return preiopv_;
}
void MDSimpleTick::set_preiopv(::google::protobuf::int64 value) {
  
  preiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreIOPV)
}

// optional int64 OpenInterest = 19;
void MDSimpleTick::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.OpenInterest)
  return openinterest_;
}
void MDSimpleTick::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.OpenInterest)
}

// optional int64 PreOpenInterest = 20;
void MDSimpleTick::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreOpenInterest)
  return preopeninterest_;
}
void MDSimpleTick::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreOpenInterest)
}

// optional int64 SettlePrice = 21;
void MDSimpleTick::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.SettlePrice)
  return settleprice_;
}
void MDSimpleTick::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.SettlePrice)
}

// optional int64 PreSettlePrice = 22;
void MDSimpleTick::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSimpleTick::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreSettlePrice)
  return presettleprice_;
}
void MDSimpleTick::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreSettlePrice)
}

// optional int32 ExchangeDate = 23;
void MDSimpleTick::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDSimpleTick::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeDate)
  return exchangedate_;
}
void MDSimpleTick::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeDate)
}

// optional int32 ExchangeTime = 24;
void MDSimpleTick::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDSimpleTick::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeTime)
  return exchangetime_;
}
void MDSimpleTick::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 25;
void MDSimpleTick::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSimpleTick::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSimpleTick::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.DataMultiplePowerOf10)
}

// optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
bool MDSimpleTick::has_adindicators() const {
  return this != internal_default_instance() && adindicators_ != NULL;
}
void MDSimpleTick::clear_adindicators() {
  if (GetArenaNoVirtual() == NULL && adindicators_ != NULL) delete adindicators_;
  adindicators_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADIndicators& MDSimpleTick::adindicators() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  return adindicators_ != NULL ? *adindicators_
                         : *::com::htsc::mdc::insight::model::ADIndicators::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADIndicators* MDSimpleTick::mutable_adindicators() {
  
  if (adindicators_ == NULL) {
    adindicators_ = new ::com::htsc::mdc::insight::model::ADIndicators;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  return adindicators_;
}
::com::htsc::mdc::insight::model::ADIndicators* MDSimpleTick::release_adindicators() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  
  ::com::htsc::mdc::insight::model::ADIndicators* temp = adindicators_;
  adindicators_ = NULL;
  return temp;
}
void MDSimpleTick::set_allocated_adindicators(::com::htsc::mdc::insight::model::ADIndicators* adindicators) {
  delete adindicators_;
  adindicators_ = adindicators;
  if (adindicators) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
}

inline const MDSimpleTick* MDSimpleTick::internal_default_instance() {
  return &MDSimpleTick_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADIndicators::kInd1101FieldNumber;
const int ADIndicators::kInd1102FieldNumber;
const int ADIndicators::kInd1103FieldNumber;
const int ADIndicators::kInd1104FieldNumber;
const int ADIndicators::kInd1105FieldNumber;
const int ADIndicators::kInd1106FieldNumber;
const int ADIndicators::kInd1107FieldNumber;
const int ADIndicators::kInd1108FieldNumber;
const int ADIndicators::kInd1109FieldNumber;
const int ADIndicators::kInd1110FieldNumber;
const int ADIndicators::kInd1111FieldNumber;
const int ADIndicators::kInd1112FieldNumber;
const int ADIndicators::kInd1113FieldNumber;
const int ADIndicators::kInd1114FieldNumber;
const int ADIndicators::kInd1115FieldNumber;
const int ADIndicators::kInd1116FieldNumber;
const int ADIndicators::kInd1117FieldNumber;
const int ADIndicators::kInd1118FieldNumber;
const int ADIndicators::kInd1119FieldNumber;
const int ADIndicators::kInd1120FieldNumber;
const int ADIndicators::kInd1121FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADIndicators::ADIndicators()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSimpleTick_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADIndicators)
}

void ADIndicators::InitAsDefaultInstance() {
}

ADIndicators::ADIndicators(const ADIndicators& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADIndicators)
}

void ADIndicators::SharedCtor() {
  ::memset(&ind1101_, 0, reinterpret_cast<char*>(&ind1121_) -
    reinterpret_cast<char*>(&ind1101_) + sizeof(ind1121_));
  _cached_size_ = 0;
}

ADIndicators::~ADIndicators() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADIndicators)
  SharedDtor();
}

void ADIndicators::SharedDtor() {
}

void ADIndicators::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADIndicators::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADIndicators_descriptor_;
}

const ADIndicators& ADIndicators::default_instance() {
  protobuf_InitDefaults_MDSimpleTick_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADIndicators> ADIndicators_default_instance_;

ADIndicators* ADIndicators::New(::google::protobuf::Arena* arena) const {
  ADIndicators* n = new ADIndicators;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADIndicators::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADIndicators)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADIndicators, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADIndicators*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(ind1101_, ind1108_);
  ZR_(ind1109_, ind1116_);
  ZR_(ind1117_, ind1121_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADIndicators::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADIndicators)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 Ind1101 = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1101_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Ind1102;
        break;
      }

      // optional int64 Ind1102 = 2;
      case 2: {
        if (tag == 16) {
         parse_Ind1102:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1102_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Ind1103;
        break;
      }

      // optional int64 Ind1103 = 3;
      case 3: {
        if (tag == 24) {
         parse_Ind1103:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1103_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Ind1104;
        break;
      }

      // optional int64 Ind1104 = 4;
      case 4: {
        if (tag == 32) {
         parse_Ind1104:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1104_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_Ind1105;
        break;
      }

      // optional int64 Ind1105 = 5;
      case 5: {
        if (tag == 40) {
         parse_Ind1105:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1105_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_Ind1106;
        break;
      }

      // optional int64 Ind1106 = 6;
      case 6: {
        if (tag == 48) {
         parse_Ind1106:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1106_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_Ind1107;
        break;
      }

      // optional int64 Ind1107 = 7;
      case 7: {
        if (tag == 56) {
         parse_Ind1107:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1107_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_Ind1108;
        break;
      }

      // optional int64 Ind1108 = 8;
      case 8: {
        if (tag == 64) {
         parse_Ind1108:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1108_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_Ind1109;
        break;
      }

      // optional int64 Ind1109 = 9;
      case 9: {
        if (tag == 72) {
         parse_Ind1109:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1109_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_Ind1110;
        break;
      }

      // optional int64 Ind1110 = 10;
      case 10: {
        if (tag == 80) {
         parse_Ind1110:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1110_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_Ind1111;
        break;
      }

      // optional int64 Ind1111 = 11;
      case 11: {
        if (tag == 88) {
         parse_Ind1111:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1111_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_Ind1112;
        break;
      }

      // optional int64 Ind1112 = 12;
      case 12: {
        if (tag == 96) {
         parse_Ind1112:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1112_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_Ind1113;
        break;
      }

      // optional int64 Ind1113 = 13;
      case 13: {
        if (tag == 104) {
         parse_Ind1113:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1113_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_Ind1114;
        break;
      }

      // optional int64 Ind1114 = 14;
      case 14: {
        if (tag == 112) {
         parse_Ind1114:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1114_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_Ind1115;
        break;
      }

      // optional int64 Ind1115 = 15;
      case 15: {
        if (tag == 120) {
         parse_Ind1115:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1115_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_Ind1116;
        break;
      }

      // optional int64 Ind1116 = 16;
      case 16: {
        if (tag == 128) {
         parse_Ind1116:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1116_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_Ind1117;
        break;
      }

      // optional int64 Ind1117 = 17;
      case 17: {
        if (tag == 136) {
         parse_Ind1117:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1117_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_Ind1118;
        break;
      }

      // optional int64 Ind1118 = 18;
      case 18: {
        if (tag == 144) {
         parse_Ind1118:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1118_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_Ind1119;
        break;
      }

      // optional int64 Ind1119 = 19;
      case 19: {
        if (tag == 152) {
         parse_Ind1119:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1119_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_Ind1120;
        break;
      }

      // optional int64 Ind1120 = 20;
      case 20: {
        if (tag == 160) {
         parse_Ind1120:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1120_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_Ind1121;
        break;
      }

      // optional int64 Ind1121 = 21;
      case 21: {
        if (tag == 168) {
         parse_Ind1121:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ind1121_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADIndicators)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADIndicators)
  return false;
#undef DO_
}

void ADIndicators::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADIndicators)
  // optional int64 Ind1101 = 1;
  if (this->ind1101() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->ind1101(), output);
  }

  // optional int64 Ind1102 = 2;
  if (this->ind1102() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->ind1102(), output);
  }

  // optional int64 Ind1103 = 3;
  if (this->ind1103() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->ind1103(), output);
  }

  // optional int64 Ind1104 = 4;
  if (this->ind1104() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->ind1104(), output);
  }

  // optional int64 Ind1105 = 5;
  if (this->ind1105() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->ind1105(), output);
  }

  // optional int64 Ind1106 = 6;
  if (this->ind1106() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->ind1106(), output);
  }

  // optional int64 Ind1107 = 7;
  if (this->ind1107() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->ind1107(), output);
  }

  // optional int64 Ind1108 = 8;
  if (this->ind1108() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->ind1108(), output);
  }

  // optional int64 Ind1109 = 9;
  if (this->ind1109() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->ind1109(), output);
  }

  // optional int64 Ind1110 = 10;
  if (this->ind1110() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->ind1110(), output);
  }

  // optional int64 Ind1111 = 11;
  if (this->ind1111() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->ind1111(), output);
  }

  // optional int64 Ind1112 = 12;
  if (this->ind1112() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->ind1112(), output);
  }

  // optional int64 Ind1113 = 13;
  if (this->ind1113() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->ind1113(), output);
  }

  // optional int64 Ind1114 = 14;
  if (this->ind1114() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->ind1114(), output);
  }

  // optional int64 Ind1115 = 15;
  if (this->ind1115() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->ind1115(), output);
  }

  // optional int64 Ind1116 = 16;
  if (this->ind1116() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->ind1116(), output);
  }

  // optional int64 Ind1117 = 17;
  if (this->ind1117() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->ind1117(), output);
  }

  // optional int64 Ind1118 = 18;
  if (this->ind1118() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->ind1118(), output);
  }

  // optional int64 Ind1119 = 19;
  if (this->ind1119() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->ind1119(), output);
  }

  // optional int64 Ind1120 = 20;
  if (this->ind1120() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->ind1120(), output);
  }

  // optional int64 Ind1121 = 21;
  if (this->ind1121() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->ind1121(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADIndicators)
}

::google::protobuf::uint8* ADIndicators::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADIndicators)
  // optional int64 Ind1101 = 1;
  if (this->ind1101() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->ind1101(), target);
  }

  // optional int64 Ind1102 = 2;
  if (this->ind1102() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->ind1102(), target);
  }

  // optional int64 Ind1103 = 3;
  if (this->ind1103() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->ind1103(), target);
  }

  // optional int64 Ind1104 = 4;
  if (this->ind1104() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->ind1104(), target);
  }

  // optional int64 Ind1105 = 5;
  if (this->ind1105() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->ind1105(), target);
  }

  // optional int64 Ind1106 = 6;
  if (this->ind1106() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->ind1106(), target);
  }

  // optional int64 Ind1107 = 7;
  if (this->ind1107() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->ind1107(), target);
  }

  // optional int64 Ind1108 = 8;
  if (this->ind1108() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->ind1108(), target);
  }

  // optional int64 Ind1109 = 9;
  if (this->ind1109() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->ind1109(), target);
  }

  // optional int64 Ind1110 = 10;
  if (this->ind1110() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->ind1110(), target);
  }

  // optional int64 Ind1111 = 11;
  if (this->ind1111() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->ind1111(), target);
  }

  // optional int64 Ind1112 = 12;
  if (this->ind1112() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->ind1112(), target);
  }

  // optional int64 Ind1113 = 13;
  if (this->ind1113() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->ind1113(), target);
  }

  // optional int64 Ind1114 = 14;
  if (this->ind1114() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->ind1114(), target);
  }

  // optional int64 Ind1115 = 15;
  if (this->ind1115() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->ind1115(), target);
  }

  // optional int64 Ind1116 = 16;
  if (this->ind1116() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->ind1116(), target);
  }

  // optional int64 Ind1117 = 17;
  if (this->ind1117() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->ind1117(), target);
  }

  // optional int64 Ind1118 = 18;
  if (this->ind1118() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->ind1118(), target);
  }

  // optional int64 Ind1119 = 19;
  if (this->ind1119() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->ind1119(), target);
  }

  // optional int64 Ind1120 = 20;
  if (this->ind1120() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->ind1120(), target);
  }

  // optional int64 Ind1121 = 21;
  if (this->ind1121() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->ind1121(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADIndicators)
  return target;
}

size_t ADIndicators::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADIndicators)
  size_t total_size = 0;

  // optional int64 Ind1101 = 1;
  if (this->ind1101() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1101());
  }

  // optional int64 Ind1102 = 2;
  if (this->ind1102() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1102());
  }

  // optional int64 Ind1103 = 3;
  if (this->ind1103() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1103());
  }

  // optional int64 Ind1104 = 4;
  if (this->ind1104() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1104());
  }

  // optional int64 Ind1105 = 5;
  if (this->ind1105() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1105());
  }

  // optional int64 Ind1106 = 6;
  if (this->ind1106() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1106());
  }

  // optional int64 Ind1107 = 7;
  if (this->ind1107() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1107());
  }

  // optional int64 Ind1108 = 8;
  if (this->ind1108() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1108());
  }

  // optional int64 Ind1109 = 9;
  if (this->ind1109() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1109());
  }

  // optional int64 Ind1110 = 10;
  if (this->ind1110() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1110());
  }

  // optional int64 Ind1111 = 11;
  if (this->ind1111() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1111());
  }

  // optional int64 Ind1112 = 12;
  if (this->ind1112() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1112());
  }

  // optional int64 Ind1113 = 13;
  if (this->ind1113() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1113());
  }

  // optional int64 Ind1114 = 14;
  if (this->ind1114() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1114());
  }

  // optional int64 Ind1115 = 15;
  if (this->ind1115() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1115());
  }

  // optional int64 Ind1116 = 16;
  if (this->ind1116() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1116());
  }

  // optional int64 Ind1117 = 17;
  if (this->ind1117() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1117());
  }

  // optional int64 Ind1118 = 18;
  if (this->ind1118() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1118());
  }

  // optional int64 Ind1119 = 19;
  if (this->ind1119() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1119());
  }

  // optional int64 Ind1120 = 20;
  if (this->ind1120() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1120());
  }

  // optional int64 Ind1121 = 21;
  if (this->ind1121() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ind1121());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADIndicators::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADIndicators)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADIndicators* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADIndicators>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADIndicators)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADIndicators)
    UnsafeMergeFrom(*source);
  }
}

void ADIndicators::MergeFrom(const ADIndicators& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADIndicators)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADIndicators::UnsafeMergeFrom(const ADIndicators& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.ind1101() != 0) {
    set_ind1101(from.ind1101());
  }
  if (from.ind1102() != 0) {
    set_ind1102(from.ind1102());
  }
  if (from.ind1103() != 0) {
    set_ind1103(from.ind1103());
  }
  if (from.ind1104() != 0) {
    set_ind1104(from.ind1104());
  }
  if (from.ind1105() != 0) {
    set_ind1105(from.ind1105());
  }
  if (from.ind1106() != 0) {
    set_ind1106(from.ind1106());
  }
  if (from.ind1107() != 0) {
    set_ind1107(from.ind1107());
  }
  if (from.ind1108() != 0) {
    set_ind1108(from.ind1108());
  }
  if (from.ind1109() != 0) {
    set_ind1109(from.ind1109());
  }
  if (from.ind1110() != 0) {
    set_ind1110(from.ind1110());
  }
  if (from.ind1111() != 0) {
    set_ind1111(from.ind1111());
  }
  if (from.ind1112() != 0) {
    set_ind1112(from.ind1112());
  }
  if (from.ind1113() != 0) {
    set_ind1113(from.ind1113());
  }
  if (from.ind1114() != 0) {
    set_ind1114(from.ind1114());
  }
  if (from.ind1115() != 0) {
    set_ind1115(from.ind1115());
  }
  if (from.ind1116() != 0) {
    set_ind1116(from.ind1116());
  }
  if (from.ind1117() != 0) {
    set_ind1117(from.ind1117());
  }
  if (from.ind1118() != 0) {
    set_ind1118(from.ind1118());
  }
  if (from.ind1119() != 0) {
    set_ind1119(from.ind1119());
  }
  if (from.ind1120() != 0) {
    set_ind1120(from.ind1120());
  }
  if (from.ind1121() != 0) {
    set_ind1121(from.ind1121());
  }
}

void ADIndicators::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADIndicators)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADIndicators::CopyFrom(const ADIndicators& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADIndicators)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADIndicators::IsInitialized() const {

  return true;
}

void ADIndicators::Swap(ADIndicators* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADIndicators::InternalSwap(ADIndicators* other) {
  std::swap(ind1101_, other->ind1101_);
  std::swap(ind1102_, other->ind1102_);
  std::swap(ind1103_, other->ind1103_);
  std::swap(ind1104_, other->ind1104_);
  std::swap(ind1105_, other->ind1105_);
  std::swap(ind1106_, other->ind1106_);
  std::swap(ind1107_, other->ind1107_);
  std::swap(ind1108_, other->ind1108_);
  std::swap(ind1109_, other->ind1109_);
  std::swap(ind1110_, other->ind1110_);
  std::swap(ind1111_, other->ind1111_);
  std::swap(ind1112_, other->ind1112_);
  std::swap(ind1113_, other->ind1113_);
  std::swap(ind1114_, other->ind1114_);
  std::swap(ind1115_, other->ind1115_);
  std::swap(ind1116_, other->ind1116_);
  std::swap(ind1117_, other->ind1117_);
  std::swap(ind1118_, other->ind1118_);
  std::swap(ind1119_, other->ind1119_);
  std::swap(ind1120_, other->ind1120_);
  std::swap(ind1121_, other->ind1121_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADIndicators::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADIndicators_descriptor_;
  metadata.reflection = ADIndicators_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADIndicators

// optional int64 Ind1101 = 1;
void ADIndicators::clear_ind1101() {
  ind1101_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1101() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1101)
  return ind1101_;
}
void ADIndicators::set_ind1101(::google::protobuf::int64 value) {
  
  ind1101_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1101)
}

// optional int64 Ind1102 = 2;
void ADIndicators::clear_ind1102() {
  ind1102_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1102() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1102)
  return ind1102_;
}
void ADIndicators::set_ind1102(::google::protobuf::int64 value) {
  
  ind1102_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1102)
}

// optional int64 Ind1103 = 3;
void ADIndicators::clear_ind1103() {
  ind1103_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1103() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1103)
  return ind1103_;
}
void ADIndicators::set_ind1103(::google::protobuf::int64 value) {
  
  ind1103_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1103)
}

// optional int64 Ind1104 = 4;
void ADIndicators::clear_ind1104() {
  ind1104_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1104() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1104)
  return ind1104_;
}
void ADIndicators::set_ind1104(::google::protobuf::int64 value) {
  
  ind1104_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1104)
}

// optional int64 Ind1105 = 5;
void ADIndicators::clear_ind1105() {
  ind1105_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1105() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1105)
  return ind1105_;
}
void ADIndicators::set_ind1105(::google::protobuf::int64 value) {
  
  ind1105_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1105)
}

// optional int64 Ind1106 = 6;
void ADIndicators::clear_ind1106() {
  ind1106_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1106() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1106)
  return ind1106_;
}
void ADIndicators::set_ind1106(::google::protobuf::int64 value) {
  
  ind1106_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1106)
}

// optional int64 Ind1107 = 7;
void ADIndicators::clear_ind1107() {
  ind1107_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1107() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1107)
  return ind1107_;
}
void ADIndicators::set_ind1107(::google::protobuf::int64 value) {
  
  ind1107_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1107)
}

// optional int64 Ind1108 = 8;
void ADIndicators::clear_ind1108() {
  ind1108_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1108() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1108)
  return ind1108_;
}
void ADIndicators::set_ind1108(::google::protobuf::int64 value) {
  
  ind1108_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1108)
}

// optional int64 Ind1109 = 9;
void ADIndicators::clear_ind1109() {
  ind1109_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1109() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1109)
  return ind1109_;
}
void ADIndicators::set_ind1109(::google::protobuf::int64 value) {
  
  ind1109_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1109)
}

// optional int64 Ind1110 = 10;
void ADIndicators::clear_ind1110() {
  ind1110_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1110() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1110)
  return ind1110_;
}
void ADIndicators::set_ind1110(::google::protobuf::int64 value) {
  
  ind1110_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1110)
}

// optional int64 Ind1111 = 11;
void ADIndicators::clear_ind1111() {
  ind1111_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1111() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1111)
  return ind1111_;
}
void ADIndicators::set_ind1111(::google::protobuf::int64 value) {
  
  ind1111_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1111)
}

// optional int64 Ind1112 = 12;
void ADIndicators::clear_ind1112() {
  ind1112_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1112() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1112)
  return ind1112_;
}
void ADIndicators::set_ind1112(::google::protobuf::int64 value) {
  
  ind1112_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1112)
}

// optional int64 Ind1113 = 13;
void ADIndicators::clear_ind1113() {
  ind1113_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1113() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1113)
  return ind1113_;
}
void ADIndicators::set_ind1113(::google::protobuf::int64 value) {
  
  ind1113_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1113)
}

// optional int64 Ind1114 = 14;
void ADIndicators::clear_ind1114() {
  ind1114_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1114() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1114)
  return ind1114_;
}
void ADIndicators::set_ind1114(::google::protobuf::int64 value) {
  
  ind1114_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1114)
}

// optional int64 Ind1115 = 15;
void ADIndicators::clear_ind1115() {
  ind1115_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1115() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1115)
  return ind1115_;
}
void ADIndicators::set_ind1115(::google::protobuf::int64 value) {
  
  ind1115_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1115)
}

// optional int64 Ind1116 = 16;
void ADIndicators::clear_ind1116() {
  ind1116_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1116() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1116)
  return ind1116_;
}
void ADIndicators::set_ind1116(::google::protobuf::int64 value) {
  
  ind1116_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1116)
}

// optional int64 Ind1117 = 17;
void ADIndicators::clear_ind1117() {
  ind1117_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1117() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1117)
  return ind1117_;
}
void ADIndicators::set_ind1117(::google::protobuf::int64 value) {
  
  ind1117_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1117)
}

// optional int64 Ind1118 = 18;
void ADIndicators::clear_ind1118() {
  ind1118_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1118() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1118)
  return ind1118_;
}
void ADIndicators::set_ind1118(::google::protobuf::int64 value) {
  
  ind1118_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1118)
}

// optional int64 Ind1119 = 19;
void ADIndicators::clear_ind1119() {
  ind1119_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1119() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1119)
  return ind1119_;
}
void ADIndicators::set_ind1119(::google::protobuf::int64 value) {
  
  ind1119_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1119)
}

// optional int64 Ind1120 = 20;
void ADIndicators::clear_ind1120() {
  ind1120_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1120() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1120)
  return ind1120_;
}
void ADIndicators::set_ind1120(::google::protobuf::int64 value) {
  
  ind1120_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1120)
}

// optional int64 Ind1121 = 21;
void ADIndicators::clear_ind1121() {
  ind1121_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADIndicators::ind1121() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1121)
  return ind1121_;
}
void ADIndicators::set_ind1121(::google::protobuf::int64 value) {
  
  ind1121_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1121)
}

inline const ADIndicators* ADIndicators::internal_default_instance() {
  return &ADIndicators_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
