syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADVolumeByPriceProtos";
option optimize_for = SPEED;

// ADVolumeByPrice message represents volume distribution by price levels for securities
message ADVolumeByPrice {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Total volume traded across all price levels
    int64 TotalVolumeTrade = 7;
    
    // Detailed volume breakdown by price levels
    repeated ADVolumeByPriceDetail Details = 8;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 9;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 10;
    
    // Data scaling factor (power of 10 multiplier for price and volume fields)
    int32 DataMultiplePowerOf10 = 11;
}

// ADVolumeByPriceDetail represents volume statistics for a specific price level
message ADVolumeByPriceDetail {
    // Trade price for this level (scaled by DataMultiplePowerOf10)
    int64 TradePrice = 1;
    
    // Total quantity traded at this price level
    int64 TotalQty = 2;
    
    // Buy quantity at this price level
    int64 BuyQty = 3;
    
    // Sell quantity at this price level
    int64 SellQty = 4;
    
    // Total number of trades at this price level
    int64 TotalNumbers = 5;
    
    // Number of buy trades at this price level
    int64 BuyNumbers = 6;
    
    // Number of sell trades at this price level
    int64 SellNumbers = 7;
    
    // Average volume per trade at this price level
    int64 VolumePerNumber = 8;
}
