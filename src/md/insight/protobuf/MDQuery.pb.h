// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQuery.proto

#ifndef PROTOBUF_MDQuery_2eproto__INCLUDED
#define PROTOBUF_MDQuery_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "SecuritySourceType.pb.h"
#include "InsightErrorContext.pb.h"
#include "MarketData.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDQuery_2eproto();
void protobuf_InitDefaults_MDQuery_2eproto();
void protobuf_AssignDesc_MDQuery_2eproto();
void protobuf_ShutdownFile_MDQuery_2eproto();

class MDQueryRequest;
class MDQueryResponse;
class QueryParam;

// ===================================================================

class MDQueryRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDQueryRequest) */ {
 public:
  MDQueryRequest();
  virtual ~MDQueryRequest();

  MDQueryRequest(const MDQueryRequest& from);

  inline MDQueryRequest& operator=(const MDQueryRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDQueryRequest& default_instance();

  static const MDQueryRequest* internal_default_instance();

  void Swap(MDQueryRequest* other);

  // implements Message ----------------------------------------------

  inline MDQueryRequest* New() const { return New(NULL); }

  MDQueryRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDQueryRequest& from);
  void MergeFrom(const MDQueryRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDQueryRequest* other);
  void UnsafeMergeFrom(const MDQueryRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 queryType = 1;
  void clear_querytype();
  static const int kQueryTypeFieldNumber = 1;
  ::google::protobuf::int32 querytype() const;
  void set_querytype(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
  int securitysourcetype_size() const;
  void clear_securitysourcetype();
  static const int kSecuritySourceTypeFieldNumber = 2;
  const ::com::htsc::mdc::insight::model::SecuritySourceType& securitysourcetype(int index) const;
  ::com::htsc::mdc::insight::model::SecuritySourceType* mutable_securitysourcetype(int index);
  ::com::htsc::mdc::insight::model::SecuritySourceType* add_securitysourcetype();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
      mutable_securitysourcetype();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
      securitysourcetype() const;

  // repeated string htscSecurityIDs = 3;
  int htscsecurityids_size() const;
  void clear_htscsecurityids();
  static const int kHtscSecurityIDsFieldNumber = 3;
  const ::std::string& htscsecurityids(int index) const;
  ::std::string* mutable_htscsecurityids(int index);
  void set_htscsecurityids(int index, const ::std::string& value);
  void set_htscsecurityids(int index, const char* value);
  void set_htscsecurityids(int index, const char* value, size_t size);
  ::std::string* add_htscsecurityids();
  void add_htscsecurityids(const ::std::string& value);
  void add_htscsecurityids(const char* value);
  void add_htscsecurityids(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& htscsecurityids() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_htscsecurityids();

  // repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
  int queryparams_size() const;
  void clear_queryparams();
  static const int kQueryParamsFieldNumber = 4;
  const ::com::htsc::mdc::insight::model::QueryParam& queryparams(int index) const;
  ::com::htsc::mdc::insight::model::QueryParam* mutable_queryparams(int index);
  ::com::htsc::mdc::insight::model::QueryParam* add_queryparams();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >*
      mutable_queryparams();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >&
      queryparams() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDQueryRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType > securitysourcetype_;
  ::google::protobuf::RepeatedPtrField< ::std::string> htscsecurityids_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam > queryparams_;
  ::google::protobuf::int32 querytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuery_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuery_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuery_2eproto();
  friend void protobuf_ShutdownFile_MDQuery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDQueryRequest> MDQueryRequest_default_instance_;

// -------------------------------------------------------------------

class QueryParam : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.QueryParam) */ {
 public:
  QueryParam();
  virtual ~QueryParam();

  QueryParam(const QueryParam& from);

  inline QueryParam& operator=(const QueryParam& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const QueryParam& default_instance();

  static const QueryParam* internal_default_instance();

  void Swap(QueryParam* other);

  // implements Message ----------------------------------------------

  inline QueryParam* New() const { return New(NULL); }

  QueryParam* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const QueryParam& from);
  void MergeFrom(const QueryParam& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(QueryParam* other);
  void UnsafeMergeFrom(const QueryParam& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string paramType = 1;
  void clear_paramtype();
  static const int kParamTypeFieldNumber = 1;
  const ::std::string& paramtype() const;
  void set_paramtype(const ::std::string& value);
  void set_paramtype(const char* value);
  void set_paramtype(const char* value, size_t size);
  ::std::string* mutable_paramtype();
  ::std::string* release_paramtype();
  void set_allocated_paramtype(::std::string* paramtype);

  // optional string paramValue = 2;
  void clear_paramvalue();
  static const int kParamValueFieldNumber = 2;
  const ::std::string& paramvalue() const;
  void set_paramvalue(const ::std::string& value);
  void set_paramvalue(const char* value);
  void set_paramvalue(const char* value, size_t size);
  ::std::string* mutable_paramvalue();
  ::std::string* release_paramvalue();
  void set_allocated_paramvalue(::std::string* paramvalue);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.QueryParam)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr paramtype_;
  ::google::protobuf::internal::ArenaStringPtr paramvalue_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuery_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuery_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuery_2eproto();
  friend void protobuf_ShutdownFile_MDQuery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<QueryParam> QueryParam_default_instance_;

// -------------------------------------------------------------------

class MDQueryResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDQueryResponse) */ {
 public:
  MDQueryResponse();
  virtual ~MDQueryResponse();

  MDQueryResponse(const MDQueryResponse& from);

  inline MDQueryResponse& operator=(const MDQueryResponse& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDQueryResponse& default_instance();

  static const MDQueryResponse* internal_default_instance();

  void Swap(MDQueryResponse* other);

  // implements Message ----------------------------------------------

  inline MDQueryResponse* New() const { return New(NULL); }

  MDQueryResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDQueryResponse& from);
  void MergeFrom(const MDQueryResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDQueryResponse* other);
  void UnsafeMergeFrom(const MDQueryResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 queryType = 1;
  void clear_querytype();
  static const int kQueryTypeFieldNumber = 1;
  ::google::protobuf::int32 querytype() const;
  void set_querytype(::google::protobuf::int32 value);

  // optional bool isSuccess = 2;
  void clear_issuccess();
  static const int kIsSuccessFieldNumber = 2;
  bool issuccess() const;
  void set_issuccess(bool value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  bool has_errorcontext() const;
  void clear_errorcontext();
  static const int kErrorContextFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& errorcontext() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_errorcontext();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_errorcontext();
  void set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext);

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
  bool has_marketdatastream() const;
  void clear_marketdatastream();
  static const int kMarketDataStreamFieldNumber = 4;
  const ::com::htsc::mdc::insight::model::MarketDataStream& marketdatastream() const;
  ::com::htsc::mdc::insight::model::MarketDataStream* mutable_marketdatastream();
  ::com::htsc::mdc::insight::model::MarketDataStream* release_marketdatastream();
  void set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDQueryResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext_;
  ::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream_;
  ::google::protobuf::int32 querytype_;
  bool issuccess_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuery_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuery_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuery_2eproto();
  friend void protobuf_ShutdownFile_MDQuery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDQueryResponse> MDQueryResponse_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQueryRequest

// optional int32 queryType = 1;
inline void MDQueryRequest::clear_querytype() {
  querytype_ = 0;
}
inline ::google::protobuf::int32 MDQueryRequest::querytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.queryType)
  return querytype_;
}
inline void MDQueryRequest::set_querytype(::google::protobuf::int32 value) {
  
  querytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryRequest.queryType)
}

// repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
inline int MDQueryRequest::securitysourcetype_size() const {
  return securitysourcetype_.size();
}
inline void MDQueryRequest::clear_securitysourcetype() {
  securitysourcetype_.Clear();
}
inline const ::com::htsc::mdc::insight::model::SecuritySourceType& MDQueryRequest::securitysourcetype(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Get(index);
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* MDQueryRequest::mutable_securitysourcetype(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* MDQueryRequest::add_securitysourcetype() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
MDQueryRequest::mutable_securitysourcetype() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return &securitysourcetype_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
MDQueryRequest::securitysourcetype() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_;
}

// repeated string htscSecurityIDs = 3;
inline int MDQueryRequest::htscsecurityids_size() const {
  return htscsecurityids_.size();
}
inline void MDQueryRequest::clear_htscsecurityids() {
  htscsecurityids_.Clear();
}
inline const ::std::string& MDQueryRequest::htscsecurityids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Get(index);
}
inline ::std::string* MDQueryRequest::mutable_htscsecurityids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Mutable(index);
}
inline void MDQueryRequest::set_htscsecurityids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  htscsecurityids_.Mutable(index)->assign(value);
}
inline void MDQueryRequest::set_htscsecurityids(int index, const char* value) {
  htscsecurityids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
inline void MDQueryRequest::set_htscsecurityids(int index, const char* value, size_t size) {
  htscsecurityids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
inline ::std::string* MDQueryRequest::add_htscsecurityids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Add();
}
inline void MDQueryRequest::add_htscsecurityids(const ::std::string& value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
inline void MDQueryRequest::add_htscsecurityids(const char* value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
inline void MDQueryRequest::add_htscsecurityids(const char* value, size_t size) {
  htscsecurityids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
MDQueryRequest::htscsecurityids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
MDQueryRequest::mutable_htscsecurityids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return &htscsecurityids_;
}

// repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
inline int MDQueryRequest::queryparams_size() const {
  return queryparams_.size();
}
inline void MDQueryRequest::clear_queryparams() {
  queryparams_.Clear();
}
inline const ::com::htsc::mdc::insight::model::QueryParam& MDQueryRequest::queryparams(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Get(index);
}
inline ::com::htsc::mdc::insight::model::QueryParam* MDQueryRequest::mutable_queryparams(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::QueryParam* MDQueryRequest::add_queryparams() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >*
MDQueryRequest::mutable_queryparams() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return &queryparams_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >&
MDQueryRequest::queryparams() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_;
}

inline const MDQueryRequest* MDQueryRequest::internal_default_instance() {
  return &MDQueryRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// QueryParam

// optional string paramType = 1;
inline void QueryParam::clear_paramtype() {
  paramtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& QueryParam::paramtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.QueryParam.paramType)
  return paramtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void QueryParam::set_paramtype(const ::std::string& value) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.QueryParam.paramType)
}
inline void QueryParam::set_paramtype(const char* value) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.QueryParam.paramType)
}
inline void QueryParam::set_paramtype(const char* value, size_t size) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.QueryParam.paramType)
}
inline ::std::string* QueryParam::mutable_paramtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.QueryParam.paramType)
  return paramtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* QueryParam::release_paramtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.QueryParam.paramType)
  
  return paramtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void QueryParam::set_allocated_paramtype(::std::string* paramtype) {
  if (paramtype != NULL) {
    
  } else {
    
  }
  paramtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.QueryParam.paramType)
}

// optional string paramValue = 2;
inline void QueryParam::clear_paramvalue() {
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& QueryParam::paramvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.QueryParam.paramValue)
  return paramvalue_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void QueryParam::set_paramvalue(const ::std::string& value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
inline void QueryParam::set_paramvalue(const char* value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
inline void QueryParam::set_paramvalue(const char* value, size_t size) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
inline ::std::string* QueryParam::mutable_paramvalue() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.QueryParam.paramValue)
  return paramvalue_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* QueryParam::release_paramvalue() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.QueryParam.paramValue)
  
  return paramvalue_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void QueryParam::set_allocated_paramvalue(::std::string* paramvalue) {
  if (paramvalue != NULL) {
    
  } else {
    
  }
  paramvalue_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramvalue);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.QueryParam.paramValue)
}

inline const QueryParam* QueryParam::internal_default_instance() {
  return &QueryParam_default_instance_.get();
}
// -------------------------------------------------------------------

// MDQueryResponse

// optional int32 queryType = 1;
inline void MDQueryResponse::clear_querytype() {
  querytype_ = 0;
}
inline ::google::protobuf::int32 MDQueryResponse::querytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.queryType)
  return querytype_;
}
inline void MDQueryResponse::set_querytype(::google::protobuf::int32 value) {
  
  querytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryResponse.queryType)
}

// optional bool isSuccess = 2;
inline void MDQueryResponse::clear_issuccess() {
  issuccess_ = false;
}
inline bool MDQueryResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.isSuccess)
  return issuccess_;
}
inline void MDQueryResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
inline bool MDQueryResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
inline void MDQueryResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& MDQueryResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MDQueryResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  return errorcontext_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MDQueryResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
inline void MDQueryResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
inline bool MDQueryResponse::has_marketdatastream() const {
  return this != internal_default_instance() && marketdatastream_ != NULL;
}
inline void MDQueryResponse::clear_marketdatastream() {
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MarketDataStream& MDQueryResponse::marketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  return marketdatastream_ != NULL ? *marketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* MDQueryResponse::mutable_marketdatastream() {
  
  if (marketdatastream_ == NULL) {
    marketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  return marketdatastream_;
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* MDQueryResponse::release_marketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = marketdatastream_;
  marketdatastream_ = NULL;
  return temp;
}
inline void MDQueryResponse::set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream) {
  delete marketdatastream_;
  marketdatastream_ = marketdatastream;
  if (marketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
}

inline const MDQueryResponse* MDQueryResponse::internal_default_instance() {
  return &MDQueryResponse_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDQuery_2eproto__INCLUDED
