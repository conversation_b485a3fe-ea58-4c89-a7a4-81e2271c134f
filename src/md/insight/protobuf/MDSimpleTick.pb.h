// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MD<PERSON>impleTick.proto

#ifndef PROTOBUF_MDSimpleTick_2eproto__INCLUDED
#define PROTOBUF_MDSimpleTick_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSimpleTick_2eproto();
void protobuf_InitDefaults_MDSimpleTick_2eproto();
void protobuf_AssignDesc_MDSimpleTick_2eproto();
void protobuf_ShutdownFile_MDSimpleTick_2eproto();

class ADIndicators;
class MDSimpleTick;

// ===================================================================

class MDSimpleTick : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSimpleTick) */ {
 public:
  MDSimpleTick();
  virtual ~MDSimpleTick();

  MDSimpleTick(const MDSimpleTick& from);

  inline MDSimpleTick& operator=(const MDSimpleTick& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSimpleTick& default_instance();

  static const MDSimpleTick* internal_default_instance();

  void Swap(MDSimpleTick* other);

  // implements Message ----------------------------------------------

  inline MDSimpleTick* New() const { return New(NULL); }

  MDSimpleTick* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSimpleTick& from);
  void MergeFrom(const MDSimpleTick& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSimpleTick* other);
  void UnsafeMergeFrom(const MDSimpleTick& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 NumTrades = 8;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 8;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 9;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 9;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 10;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 10;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 11;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 11;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 LastPx = 12;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 12;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 13;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 13;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 14;
  void clear_closepx();
  static const int kClosePxFieldNumber = 14;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 15;
  void clear_highpx();
  static const int kHighPxFieldNumber = 15;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 16;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 16;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int64 IOPV = 17;
  void clear_iopv();
  static const int kIOPVFieldNumber = 17;
  ::google::protobuf::int64 iopv() const;
  void set_iopv(::google::protobuf::int64 value);

  // optional int64 PreIOPV = 18;
  void clear_preiopv();
  static const int kPreIOPVFieldNumber = 18;
  ::google::protobuf::int64 preiopv() const;
  void set_preiopv(::google::protobuf::int64 value);

  // optional int64 OpenInterest = 19;
  void clear_openinterest();
  static const int kOpenInterestFieldNumber = 19;
  ::google::protobuf::int64 openinterest() const;
  void set_openinterest(::google::protobuf::int64 value);

  // optional int64 PreOpenInterest = 20;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 20;
  ::google::protobuf::int64 preopeninterest() const;
  void set_preopeninterest(::google::protobuf::int64 value);

  // optional int64 SettlePrice = 21;
  void clear_settleprice();
  static const int kSettlePriceFieldNumber = 21;
  ::google::protobuf::int64 settleprice() const;
  void set_settleprice(::google::protobuf::int64 value);

  // optional int64 PreSettlePrice = 22;
  void clear_presettleprice();
  static const int kPreSettlePriceFieldNumber = 22;
  ::google::protobuf::int64 presettleprice() const;
  void set_presettleprice(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 23;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 23;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 24;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 24;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 25;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 25;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
  bool has_adindicators() const;
  void clear_adindicators();
  static const int kADIndicatorsFieldNumber = 40;
  const ::com::htsc::mdc::insight::model::ADIndicators& adindicators() const;
  ::com::htsc::mdc::insight::model::ADIndicators* mutable_adindicators();
  ::com::htsc::mdc::insight::model::ADIndicators* release_adindicators();
  void set_allocated_adindicators(::com::htsc::mdc::insight::model::ADIndicators* adindicators);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSimpleTick)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::com::htsc::mdc::insight::model::ADIndicators* adindicators_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 iopv_;
  ::google::protobuf::int64 preiopv_;
  ::google::protobuf::int64 openinterest_;
  ::google::protobuf::int64 preopeninterest_;
  ::google::protobuf::int64 settleprice_;
  ::google::protobuf::int64 presettleprice_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSimpleTick_2eproto_impl();
  friend void  protobuf_AddDesc_MDSimpleTick_2eproto_impl();
  friend void protobuf_AssignDesc_MDSimpleTick_2eproto();
  friend void protobuf_ShutdownFile_MDSimpleTick_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSimpleTick> MDSimpleTick_default_instance_;

// -------------------------------------------------------------------

class ADIndicators : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADIndicators) */ {
 public:
  ADIndicators();
  virtual ~ADIndicators();

  ADIndicators(const ADIndicators& from);

  inline ADIndicators& operator=(const ADIndicators& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADIndicators& default_instance();

  static const ADIndicators* internal_default_instance();

  void Swap(ADIndicators* other);

  // implements Message ----------------------------------------------

  inline ADIndicators* New() const { return New(NULL); }

  ADIndicators* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADIndicators& from);
  void MergeFrom(const ADIndicators& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADIndicators* other);
  void UnsafeMergeFrom(const ADIndicators& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 Ind1101 = 1;
  void clear_ind1101();
  static const int kInd1101FieldNumber = 1;
  ::google::protobuf::int64 ind1101() const;
  void set_ind1101(::google::protobuf::int64 value);

  // optional int64 Ind1102 = 2;
  void clear_ind1102();
  static const int kInd1102FieldNumber = 2;
  ::google::protobuf::int64 ind1102() const;
  void set_ind1102(::google::protobuf::int64 value);

  // optional int64 Ind1103 = 3;
  void clear_ind1103();
  static const int kInd1103FieldNumber = 3;
  ::google::protobuf::int64 ind1103() const;
  void set_ind1103(::google::protobuf::int64 value);

  // optional int64 Ind1104 = 4;
  void clear_ind1104();
  static const int kInd1104FieldNumber = 4;
  ::google::protobuf::int64 ind1104() const;
  void set_ind1104(::google::protobuf::int64 value);

  // optional int64 Ind1105 = 5;
  void clear_ind1105();
  static const int kInd1105FieldNumber = 5;
  ::google::protobuf::int64 ind1105() const;
  void set_ind1105(::google::protobuf::int64 value);

  // optional int64 Ind1106 = 6;
  void clear_ind1106();
  static const int kInd1106FieldNumber = 6;
  ::google::protobuf::int64 ind1106() const;
  void set_ind1106(::google::protobuf::int64 value);

  // optional int64 Ind1107 = 7;
  void clear_ind1107();
  static const int kInd1107FieldNumber = 7;
  ::google::protobuf::int64 ind1107() const;
  void set_ind1107(::google::protobuf::int64 value);

  // optional int64 Ind1108 = 8;
  void clear_ind1108();
  static const int kInd1108FieldNumber = 8;
  ::google::protobuf::int64 ind1108() const;
  void set_ind1108(::google::protobuf::int64 value);

  // optional int64 Ind1109 = 9;
  void clear_ind1109();
  static const int kInd1109FieldNumber = 9;
  ::google::protobuf::int64 ind1109() const;
  void set_ind1109(::google::protobuf::int64 value);

  // optional int64 Ind1110 = 10;
  void clear_ind1110();
  static const int kInd1110FieldNumber = 10;
  ::google::protobuf::int64 ind1110() const;
  void set_ind1110(::google::protobuf::int64 value);

  // optional int64 Ind1111 = 11;
  void clear_ind1111();
  static const int kInd1111FieldNumber = 11;
  ::google::protobuf::int64 ind1111() const;
  void set_ind1111(::google::protobuf::int64 value);

  // optional int64 Ind1112 = 12;
  void clear_ind1112();
  static const int kInd1112FieldNumber = 12;
  ::google::protobuf::int64 ind1112() const;
  void set_ind1112(::google::protobuf::int64 value);

  // optional int64 Ind1113 = 13;
  void clear_ind1113();
  static const int kInd1113FieldNumber = 13;
  ::google::protobuf::int64 ind1113() const;
  void set_ind1113(::google::protobuf::int64 value);

  // optional int64 Ind1114 = 14;
  void clear_ind1114();
  static const int kInd1114FieldNumber = 14;
  ::google::protobuf::int64 ind1114() const;
  void set_ind1114(::google::protobuf::int64 value);

  // optional int64 Ind1115 = 15;
  void clear_ind1115();
  static const int kInd1115FieldNumber = 15;
  ::google::protobuf::int64 ind1115() const;
  void set_ind1115(::google::protobuf::int64 value);

  // optional int64 Ind1116 = 16;
  void clear_ind1116();
  static const int kInd1116FieldNumber = 16;
  ::google::protobuf::int64 ind1116() const;
  void set_ind1116(::google::protobuf::int64 value);

  // optional int64 Ind1117 = 17;
  void clear_ind1117();
  static const int kInd1117FieldNumber = 17;
  ::google::protobuf::int64 ind1117() const;
  void set_ind1117(::google::protobuf::int64 value);

  // optional int64 Ind1118 = 18;
  void clear_ind1118();
  static const int kInd1118FieldNumber = 18;
  ::google::protobuf::int64 ind1118() const;
  void set_ind1118(::google::protobuf::int64 value);

  // optional int64 Ind1119 = 19;
  void clear_ind1119();
  static const int kInd1119FieldNumber = 19;
  ::google::protobuf::int64 ind1119() const;
  void set_ind1119(::google::protobuf::int64 value);

  // optional int64 Ind1120 = 20;
  void clear_ind1120();
  static const int kInd1120FieldNumber = 20;
  ::google::protobuf::int64 ind1120() const;
  void set_ind1120(::google::protobuf::int64 value);

  // optional int64 Ind1121 = 21;
  void clear_ind1121();
  static const int kInd1121FieldNumber = 21;
  ::google::protobuf::int64 ind1121() const;
  void set_ind1121(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADIndicators)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 ind1101_;
  ::google::protobuf::int64 ind1102_;
  ::google::protobuf::int64 ind1103_;
  ::google::protobuf::int64 ind1104_;
  ::google::protobuf::int64 ind1105_;
  ::google::protobuf::int64 ind1106_;
  ::google::protobuf::int64 ind1107_;
  ::google::protobuf::int64 ind1108_;
  ::google::protobuf::int64 ind1109_;
  ::google::protobuf::int64 ind1110_;
  ::google::protobuf::int64 ind1111_;
  ::google::protobuf::int64 ind1112_;
  ::google::protobuf::int64 ind1113_;
  ::google::protobuf::int64 ind1114_;
  ::google::protobuf::int64 ind1115_;
  ::google::protobuf::int64 ind1116_;
  ::google::protobuf::int64 ind1117_;
  ::google::protobuf::int64 ind1118_;
  ::google::protobuf::int64 ind1119_;
  ::google::protobuf::int64 ind1120_;
  ::google::protobuf::int64 ind1121_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSimpleTick_2eproto_impl();
  friend void  protobuf_AddDesc_MDSimpleTick_2eproto_impl();
  friend void protobuf_AssignDesc_MDSimpleTick_2eproto();
  friend void protobuf_ShutdownFile_MDSimpleTick_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADIndicators> ADIndicators_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSimpleTick

// optional string HTSCSecurityID = 1;
inline void MDSimpleTick::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSimpleTick::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSimpleTick::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
inline void MDSimpleTick::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
inline void MDSimpleTick::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}
inline ::std::string* MDSimpleTick::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSimpleTick::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSimpleTick::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSimpleTick::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSimpleTick::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.MDDate)
  return mddate_;
}
inline void MDSimpleTick::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSimpleTick::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSimpleTick::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.MDTime)
  return mdtime_;
}
inline void MDSimpleTick::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSimpleTick::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.DataTimestamp)
  return datatimestamp_;
}
inline void MDSimpleTick::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDSimpleTick::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSimpleTick::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSimpleTick::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
inline void MDSimpleTick::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
inline void MDSimpleTick::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}
inline ::std::string* MDSimpleTick::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSimpleTick::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSimpleTick::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDSimpleTick::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSimpleTick::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSimpleTick::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDSimpleTick::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSimpleTick::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSimpleTick::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.securityType)
}

// optional int64 NumTrades = 8;
inline void MDSimpleTick::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.NumTrades)
  return numtrades_;
}
inline void MDSimpleTick::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.NumTrades)
}

// optional int64 TotalVolumeTrade = 9;
inline void MDSimpleTick::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDSimpleTick::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 10;
inline void MDSimpleTick::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDSimpleTick::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.TotalValueTrade)
}

// optional int64 PreClosePx = 11;
inline void MDSimpleTick::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreClosePx)
  return preclosepx_;
}
inline void MDSimpleTick::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreClosePx)
}

// optional int64 LastPx = 12;
inline void MDSimpleTick::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.LastPx)
  return lastpx_;
}
inline void MDSimpleTick::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.LastPx)
}

// optional int64 OpenPx = 13;
inline void MDSimpleTick::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.OpenPx)
  return openpx_;
}
inline void MDSimpleTick::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.OpenPx)
}

// optional int64 ClosePx = 14;
inline void MDSimpleTick::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ClosePx)
  return closepx_;
}
inline void MDSimpleTick::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ClosePx)
}

// optional int64 HighPx = 15;
inline void MDSimpleTick::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.HighPx)
  return highpx_;
}
inline void MDSimpleTick::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.HighPx)
}

// optional int64 LowPx = 16;
inline void MDSimpleTick::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.LowPx)
  return lowpx_;
}
inline void MDSimpleTick::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.LowPx)
}

// optional int64 IOPV = 17;
inline void MDSimpleTick::clear_iopv() {
  iopv_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::iopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.IOPV)
  return iopv_;
}
inline void MDSimpleTick::set_iopv(::google::protobuf::int64 value) {
  
  iopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.IOPV)
}

// optional int64 PreIOPV = 18;
inline void MDSimpleTick::clear_preiopv() {
  preiopv_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::preiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreIOPV)
  return preiopv_;
}
inline void MDSimpleTick::set_preiopv(::google::protobuf::int64 value) {
  
  preiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreIOPV)
}

// optional int64 OpenInterest = 19;
inline void MDSimpleTick::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.OpenInterest)
  return openinterest_;
}
inline void MDSimpleTick::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.OpenInterest)
}

// optional int64 PreOpenInterest = 20;
inline void MDSimpleTick::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreOpenInterest)
  return preopeninterest_;
}
inline void MDSimpleTick::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreOpenInterest)
}

// optional int64 SettlePrice = 21;
inline void MDSimpleTick::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.SettlePrice)
  return settleprice_;
}
inline void MDSimpleTick::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.SettlePrice)
}

// optional int64 PreSettlePrice = 22;
inline void MDSimpleTick::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSimpleTick::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.PreSettlePrice)
  return presettleprice_;
}
inline void MDSimpleTick::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.PreSettlePrice)
}

// optional int32 ExchangeDate = 23;
inline void MDSimpleTick::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDSimpleTick::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeDate)
  return exchangedate_;
}
inline void MDSimpleTick::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeDate)
}

// optional int32 ExchangeTime = 24;
inline void MDSimpleTick::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDSimpleTick::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeTime)
  return exchangetime_;
}
inline void MDSimpleTick::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 25;
inline void MDSimpleTick::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSimpleTick::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSimpleTick::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSimpleTick.DataMultiplePowerOf10)
}

// optional .com.htsc.mdc.insight.model.ADIndicators ADIndicators = 40;
inline bool MDSimpleTick::has_adindicators() const {
  return this != internal_default_instance() && adindicators_ != NULL;
}
inline void MDSimpleTick::clear_adindicators() {
  if (GetArenaNoVirtual() == NULL && adindicators_ != NULL) delete adindicators_;
  adindicators_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADIndicators& MDSimpleTick::adindicators() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  return adindicators_ != NULL ? *adindicators_
                         : *::com::htsc::mdc::insight::model::ADIndicators::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADIndicators* MDSimpleTick::mutable_adindicators() {
  
  if (adindicators_ == NULL) {
    adindicators_ = new ::com::htsc::mdc::insight::model::ADIndicators;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  return adindicators_;
}
inline ::com::htsc::mdc::insight::model::ADIndicators* MDSimpleTick::release_adindicators() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
  
  ::com::htsc::mdc::insight::model::ADIndicators* temp = adindicators_;
  adindicators_ = NULL;
  return temp;
}
inline void MDSimpleTick::set_allocated_adindicators(::com::htsc::mdc::insight::model::ADIndicators* adindicators) {
  delete adindicators_;
  adindicators_ = adindicators;
  if (adindicators) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSimpleTick.ADIndicators)
}

inline const MDSimpleTick* MDSimpleTick::internal_default_instance() {
  return &MDSimpleTick_default_instance_.get();
}
// -------------------------------------------------------------------

// ADIndicators

// optional int64 Ind1101 = 1;
inline void ADIndicators::clear_ind1101() {
  ind1101_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1101() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1101)
  return ind1101_;
}
inline void ADIndicators::set_ind1101(::google::protobuf::int64 value) {
  
  ind1101_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1101)
}

// optional int64 Ind1102 = 2;
inline void ADIndicators::clear_ind1102() {
  ind1102_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1102() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1102)
  return ind1102_;
}
inline void ADIndicators::set_ind1102(::google::protobuf::int64 value) {
  
  ind1102_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1102)
}

// optional int64 Ind1103 = 3;
inline void ADIndicators::clear_ind1103() {
  ind1103_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1103() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1103)
  return ind1103_;
}
inline void ADIndicators::set_ind1103(::google::protobuf::int64 value) {
  
  ind1103_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1103)
}

// optional int64 Ind1104 = 4;
inline void ADIndicators::clear_ind1104() {
  ind1104_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1104() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1104)
  return ind1104_;
}
inline void ADIndicators::set_ind1104(::google::protobuf::int64 value) {
  
  ind1104_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1104)
}

// optional int64 Ind1105 = 5;
inline void ADIndicators::clear_ind1105() {
  ind1105_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1105() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1105)
  return ind1105_;
}
inline void ADIndicators::set_ind1105(::google::protobuf::int64 value) {
  
  ind1105_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1105)
}

// optional int64 Ind1106 = 6;
inline void ADIndicators::clear_ind1106() {
  ind1106_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1106() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1106)
  return ind1106_;
}
inline void ADIndicators::set_ind1106(::google::protobuf::int64 value) {
  
  ind1106_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1106)
}

// optional int64 Ind1107 = 7;
inline void ADIndicators::clear_ind1107() {
  ind1107_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1107() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1107)
  return ind1107_;
}
inline void ADIndicators::set_ind1107(::google::protobuf::int64 value) {
  
  ind1107_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1107)
}

// optional int64 Ind1108 = 8;
inline void ADIndicators::clear_ind1108() {
  ind1108_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1108() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1108)
  return ind1108_;
}
inline void ADIndicators::set_ind1108(::google::protobuf::int64 value) {
  
  ind1108_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1108)
}

// optional int64 Ind1109 = 9;
inline void ADIndicators::clear_ind1109() {
  ind1109_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1109() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1109)
  return ind1109_;
}
inline void ADIndicators::set_ind1109(::google::protobuf::int64 value) {
  
  ind1109_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1109)
}

// optional int64 Ind1110 = 10;
inline void ADIndicators::clear_ind1110() {
  ind1110_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1110() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1110)
  return ind1110_;
}
inline void ADIndicators::set_ind1110(::google::protobuf::int64 value) {
  
  ind1110_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1110)
}

// optional int64 Ind1111 = 11;
inline void ADIndicators::clear_ind1111() {
  ind1111_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1111() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1111)
  return ind1111_;
}
inline void ADIndicators::set_ind1111(::google::protobuf::int64 value) {
  
  ind1111_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1111)
}

// optional int64 Ind1112 = 12;
inline void ADIndicators::clear_ind1112() {
  ind1112_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1112() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1112)
  return ind1112_;
}
inline void ADIndicators::set_ind1112(::google::protobuf::int64 value) {
  
  ind1112_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1112)
}

// optional int64 Ind1113 = 13;
inline void ADIndicators::clear_ind1113() {
  ind1113_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1113() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1113)
  return ind1113_;
}
inline void ADIndicators::set_ind1113(::google::protobuf::int64 value) {
  
  ind1113_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1113)
}

// optional int64 Ind1114 = 14;
inline void ADIndicators::clear_ind1114() {
  ind1114_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1114() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1114)
  return ind1114_;
}
inline void ADIndicators::set_ind1114(::google::protobuf::int64 value) {
  
  ind1114_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1114)
}

// optional int64 Ind1115 = 15;
inline void ADIndicators::clear_ind1115() {
  ind1115_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1115() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1115)
  return ind1115_;
}
inline void ADIndicators::set_ind1115(::google::protobuf::int64 value) {
  
  ind1115_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1115)
}

// optional int64 Ind1116 = 16;
inline void ADIndicators::clear_ind1116() {
  ind1116_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1116() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1116)
  return ind1116_;
}
inline void ADIndicators::set_ind1116(::google::protobuf::int64 value) {
  
  ind1116_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1116)
}

// optional int64 Ind1117 = 17;
inline void ADIndicators::clear_ind1117() {
  ind1117_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1117() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1117)
  return ind1117_;
}
inline void ADIndicators::set_ind1117(::google::protobuf::int64 value) {
  
  ind1117_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1117)
}

// optional int64 Ind1118 = 18;
inline void ADIndicators::clear_ind1118() {
  ind1118_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1118() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1118)
  return ind1118_;
}
inline void ADIndicators::set_ind1118(::google::protobuf::int64 value) {
  
  ind1118_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1118)
}

// optional int64 Ind1119 = 19;
inline void ADIndicators::clear_ind1119() {
  ind1119_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1119() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1119)
  return ind1119_;
}
inline void ADIndicators::set_ind1119(::google::protobuf::int64 value) {
  
  ind1119_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1119)
}

// optional int64 Ind1120 = 20;
inline void ADIndicators::clear_ind1120() {
  ind1120_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1120() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1120)
  return ind1120_;
}
inline void ADIndicators::set_ind1120(::google::protobuf::int64 value) {
  
  ind1120_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1120)
}

// optional int64 Ind1121 = 21;
inline void ADIndicators::clear_ind1121() {
  ind1121_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADIndicators::ind1121() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADIndicators.Ind1121)
  return ind1121_;
}
inline void ADIndicators::set_ind1121(::google::protobuf::int64 value) {
  
  ind1121_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADIndicators.Ind1121)
}

inline const ADIndicators* ADIndicators::internal_default_instance() {
  return &ADIndicators_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSimpleTick_2eproto__INCLUDED
