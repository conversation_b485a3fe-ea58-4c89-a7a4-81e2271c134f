// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: SecuritySourceType.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "SecuritySourceType.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* SecuritySourceType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SecuritySourceType_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_SecuritySourceType_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_SecuritySourceType_2eproto() {
  protobuf_AddDesc_SecuritySourceType_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "SecuritySourceType.proto");
  GOOGLE_CHECK(file != NULL);
  SecuritySourceType_descriptor_ = file->message_type(0);
  static const int SecuritySourceType_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecuritySourceType, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecuritySourceType, securitytype_),
  };
  SecuritySourceType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SecuritySourceType_descriptor_,
      SecuritySourceType::internal_default_instance(),
      SecuritySourceType_offsets_,
      -1,
      -1,
      -1,
      sizeof(SecuritySourceType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SecuritySourceType, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_SecuritySourceType_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SecuritySourceType_descriptor_, SecuritySourceType::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_SecuritySourceType_2eproto() {
  SecuritySourceType_default_instance_.Shutdown();
  delete SecuritySourceType_reflection_;
}

void protobuf_InitDefaults_SecuritySourceType_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  SecuritySourceType_default_instance_.DefaultConstruct();
  SecuritySourceType_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_SecuritySourceType_2eproto_once_);
void protobuf_InitDefaults_SecuritySourceType_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_SecuritySourceType_2eproto_once_,
                 &protobuf_InitDefaults_SecuritySourceType_2eproto_impl);
}
void protobuf_AddDesc_SecuritySourceType_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_SecuritySourceType_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\030SecuritySourceType.proto\022\032com.htsc.mdc"
    ".insight.model\032\023ESecurityType.proto\032\027ESe"
    "curityIDSource.proto\"\216\001\n\022SecuritySourceT"
    "ype\022\?\n\020securityIDSource\030\001 \001(\0162%.com.htsc"
    ".mdc.model.ESecurityIDSource\0227\n\014security"
    "Type\030\002 \001(\0162!.com.htsc.mdc.model.ESecurit"
    "yTypeB;\n\032com.htsc.mdc.insight.modelB\030Sec"
    "uritySourceTypeProtosH\001\240\001\001b\006proto3", 314);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "SecuritySourceType.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_SecuritySourceType_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_SecuritySourceType_2eproto_once_);
void protobuf_AddDesc_SecuritySourceType_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_SecuritySourceType_2eproto_once_,
                 &protobuf_AddDesc_SecuritySourceType_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_SecuritySourceType_2eproto {
  StaticDescriptorInitializer_SecuritySourceType_2eproto() {
    protobuf_AddDesc_SecuritySourceType_2eproto();
  }
} static_descriptor_initializer_SecuritySourceType_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SecuritySourceType::kSecurityIDSourceFieldNumber;
const int SecuritySourceType::kSecurityTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SecuritySourceType::SecuritySourceType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_SecuritySourceType_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SecuritySourceType)
}

void SecuritySourceType::InitAsDefaultInstance() {
}

SecuritySourceType::SecuritySourceType(const SecuritySourceType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SecuritySourceType)
}

void SecuritySourceType::SharedCtor() {
  ::memset(&securityidsource_, 0, reinterpret_cast<char*>(&securitytype_) -
    reinterpret_cast<char*>(&securityidsource_) + sizeof(securitytype_));
  _cached_size_ = 0;
}

SecuritySourceType::~SecuritySourceType() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SecuritySourceType)
  SharedDtor();
}

void SecuritySourceType::SharedDtor() {
}

void SecuritySourceType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SecuritySourceType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SecuritySourceType_descriptor_;
}

const SecuritySourceType& SecuritySourceType::default_instance() {
  protobuf_InitDefaults_SecuritySourceType_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SecuritySourceType> SecuritySourceType_default_instance_;

SecuritySourceType* SecuritySourceType::New(::google::protobuf::Arena* arena) const {
  SecuritySourceType* n = new SecuritySourceType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SecuritySourceType::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SecuritySourceType)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(SecuritySourceType, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<SecuritySourceType*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securityidsource_, securitytype_);

#undef ZR_HELPER_
#undef ZR_

}

bool SecuritySourceType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SecuritySourceType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 2;
      case 2: {
        if (tag == 16) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SecuritySourceType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SecuritySourceType)
  return false;
#undef DO_
}

void SecuritySourceType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SecuritySourceType)
  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SecuritySourceType)
}

::google::protobuf::uint8* SecuritySourceType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SecuritySourceType)
  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SecuritySourceType)
  return target;
}

size_t SecuritySourceType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SecuritySourceType)
  size_t total_size = 0;

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SecuritySourceType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SecuritySourceType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SecuritySourceType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SecuritySourceType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SecuritySourceType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SecuritySourceType)
    UnsafeMergeFrom(*source);
  }
}

void SecuritySourceType::MergeFrom(const SecuritySourceType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SecuritySourceType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SecuritySourceType::UnsafeMergeFrom(const SecuritySourceType& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
}

void SecuritySourceType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SecuritySourceType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SecuritySourceType::CopyFrom(const SecuritySourceType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SecuritySourceType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SecuritySourceType::IsInitialized() const {

  return true;
}

void SecuritySourceType::Swap(SecuritySourceType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SecuritySourceType::InternalSwap(SecuritySourceType* other) {
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SecuritySourceType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SecuritySourceType_descriptor_;
  metadata.reflection = SecuritySourceType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SecuritySourceType

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 1;
void SecuritySourceType::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource SecuritySourceType::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SecuritySourceType.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void SecuritySourceType::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SecuritySourceType.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 2;
void SecuritySourceType::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType SecuritySourceType::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SecuritySourceType.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void SecuritySourceType::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SecuritySourceType.securityType)
}

inline const SecuritySourceType* SecuritySourceType::internal_default_instance() {
  return &SecuritySourceType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
