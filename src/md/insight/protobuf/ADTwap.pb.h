// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADTwap.proto

#ifndef PROTOBUF_ADTwap_2eproto__INCLUDED
#define PROTOBUF_ADTwap_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "EMDPeriodType.pb.h"
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_ADTwap_2eproto();
void protobuf_InitDefaults_ADTwap_2eproto();
void protobuf_AssignDesc_ADTwap_2eproto();
void protobuf_ShutdownFile_ADTwap_2eproto();

class ADTwap;

// ===================================================================

class ADTwap : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADTwap) */ {
 public:
  ADTwap();
  virtual ~ADTwap();

  ADTwap(const ADTwap& from);

  inline ADTwap& operator=(const ADTwap& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADTwap& default_instance();

  static const ADTwap* internal_default_instance();

  void Swap(ADTwap* other);

  // implements Message ----------------------------------------------

  inline ADTwap* New() const { return New(NULL); }

  ADTwap* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADTwap& from);
  void MergeFrom(const ADTwap& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADTwap* other);
  void UnsafeMergeFrom(const ADTwap& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
  void clear_periodtype();
  static const int kPeriodTypeFieldNumber = 7;
  ::com::htsc::mdc::model::EMDPeriodType periodtype() const;
  void set_periodtype(::com::htsc::mdc::model::EMDPeriodType value);

  // optional int64 Twap = 8;
  void clear_twap();
  static const int kTwapFieldNumber = 8;
  ::google::protobuf::int64 twap() const;
  void set_twap(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 9;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 9;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 10;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 10;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 11;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 11;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADTwap)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 twap_;
  int periodtype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADTwap_2eproto_impl();
  friend void  protobuf_AddDesc_ADTwap_2eproto_impl();
  friend void protobuf_AssignDesc_ADTwap_2eproto();
  friend void protobuf_ShutdownFile_ADTwap_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADTwap> ADTwap_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// ADTwap

// optional string HTSCSecurityID = 1;
inline void ADTwap::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADTwap::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADTwap::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
inline void ADTwap::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
inline void ADTwap::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
inline ::std::string* ADTwap::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADTwap::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADTwap::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void ADTwap::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 ADTwap::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.MDDate)
  return mddate_;
}
inline void ADTwap::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.MDDate)
}

// optional int32 MDTime = 3;
inline void ADTwap::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 ADTwap::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.MDTime)
  return mdtime_;
}
inline void ADTwap::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void ADTwap::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADTwap::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.DataTimestamp)
  return datatimestamp_;
}
inline void ADTwap::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void ADTwap::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource ADTwap::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void ADTwap::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void ADTwap::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType ADTwap::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void ADTwap::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.securityType)
}

// optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
inline void ADTwap::clear_periodtype() {
  periodtype_ = 0;
}
inline ::com::htsc::mdc::model::EMDPeriodType ADTwap::periodtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.PeriodType)
  return static_cast< ::com::htsc::mdc::model::EMDPeriodType >(periodtype_);
}
inline void ADTwap::set_periodtype(::com::htsc::mdc::model::EMDPeriodType value) {
  
  periodtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.PeriodType)
}

// optional int64 Twap = 8;
inline void ADTwap::clear_twap() {
  twap_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADTwap::twap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.Twap)
  return twap_;
}
inline void ADTwap::set_twap(::google::protobuf::int64 value) {
  
  twap_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.Twap)
}

// optional int32 ExchangeDate = 9;
inline void ADTwap::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 ADTwap::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.ExchangeDate)
  return exchangedate_;
}
inline void ADTwap::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.ExchangeDate)
}

// optional int32 ExchangeTime = 10;
inline void ADTwap::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 ADTwap::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.ExchangeTime)
  return exchangetime_;
}
inline void ADTwap::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 11;
inline void ADTwap::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 ADTwap::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void ADTwap::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.DataMultiplePowerOf10)
}

inline const ADTwap* ADTwap::internal_default_instance() {
  return &ADTwap_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_ADTwap_2eproto__INCLUDED
