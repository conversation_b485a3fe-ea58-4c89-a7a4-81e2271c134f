// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDPlayback.proto

#ifndef PROTOBUF_MDPlayback_2eproto__INCLUDED
#define PROTOBUF_MDPlayback_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "EMarketDataType.pb.h"
#include "ESecurityIDSource.pb.h"
#include "MarketData.pb.h"
#include "InsightErrorContext.pb.h"
#include "SecuritySourceType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDPlayback_2eproto();
void protobuf_InitDefaults_MDPlayback_2eproto();
void protobuf_AssignDesc_MDPlayback_2eproto();
void protobuf_ShutdownFile_MDPlayback_2eproto();

class PlaybackControlRequest;
class PlaybackControlResponse;
class PlaybackPayload;
class PlaybackRequest;
class PlaybackResponse;
class PlaybackStatus;
class PlaybackStatusRequest;

enum EPlaybackExrightsType {
  DEFAULT_EXRIGHTS_TYPE = 0,
  NO_EXRIGHTS = 10,
  FORWARD_EXRIGHTS = 11,
  BACKWARD_EXRIGHTS = 12,
  EPlaybackExrightsType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  EPlaybackExrightsType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool EPlaybackExrightsType_IsValid(int value);
const EPlaybackExrightsType EPlaybackExrightsType_MIN = DEFAULT_EXRIGHTS_TYPE;
const EPlaybackExrightsType EPlaybackExrightsType_MAX = BACKWARD_EXRIGHTS;
const int EPlaybackExrightsType_ARRAYSIZE = EPlaybackExrightsType_MAX + 1;

const ::google::protobuf::EnumDescriptor* EPlaybackExrightsType_descriptor();
inline const ::std::string& EPlaybackExrightsType_Name(EPlaybackExrightsType value) {
  return ::google::protobuf::internal::NameOfEnum(
    EPlaybackExrightsType_descriptor(), value);
}
inline bool EPlaybackExrightsType_Parse(
    const ::std::string& name, EPlaybackExrightsType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EPlaybackExrightsType>(
    EPlaybackExrightsType_descriptor(), name, value);
}
enum EPlaybackTaskControlType {
  DEFAULT_CONTROL_TYPE = 0,
  CANCEL_TASK = 1,
  SET_PLAYBACK_RATE = 2,
  EPlaybackTaskControlType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  EPlaybackTaskControlType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool EPlaybackTaskControlType_IsValid(int value);
const EPlaybackTaskControlType EPlaybackTaskControlType_MIN = DEFAULT_CONTROL_TYPE;
const EPlaybackTaskControlType EPlaybackTaskControlType_MAX = SET_PLAYBACK_RATE;
const int EPlaybackTaskControlType_ARRAYSIZE = EPlaybackTaskControlType_MAX + 1;

const ::google::protobuf::EnumDescriptor* EPlaybackTaskControlType_descriptor();
inline const ::std::string& EPlaybackTaskControlType_Name(EPlaybackTaskControlType value) {
  return ::google::protobuf::internal::NameOfEnum(
    EPlaybackTaskControlType_descriptor(), value);
}
inline bool EPlaybackTaskControlType_Parse(
    const ::std::string& name, EPlaybackTaskControlType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EPlaybackTaskControlType>(
    EPlaybackTaskControlType_descriptor(), name, value);
}
enum EPlaybackTaskStatus {
  DEFAULT_STATUS = 0,
  INITIALIZING = 11,
  PREPARING = 12,
  PREPARED = 13,
  RUNNING = 14,
  APPENDING = 15,
  CANCELED = 16,
  COMPLETED = 17,
  FAILED = 18,
  EPlaybackTaskStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  EPlaybackTaskStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool EPlaybackTaskStatus_IsValid(int value);
const EPlaybackTaskStatus EPlaybackTaskStatus_MIN = DEFAULT_STATUS;
const EPlaybackTaskStatus EPlaybackTaskStatus_MAX = FAILED;
const int EPlaybackTaskStatus_ARRAYSIZE = EPlaybackTaskStatus_MAX + 1;

const ::google::protobuf::EnumDescriptor* EPlaybackTaskStatus_descriptor();
inline const ::std::string& EPlaybackTaskStatus_Name(EPlaybackTaskStatus value) {
  return ::google::protobuf::internal::NameOfEnum(
    EPlaybackTaskStatus_descriptor(), value);
}
inline bool EPlaybackTaskStatus_Parse(
    const ::std::string& name, EPlaybackTaskStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<EPlaybackTaskStatus>(
    EPlaybackTaskStatus_descriptor(), name, value);
}
// ===================================================================

class PlaybackRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackRequest) */ {
 public:
  PlaybackRequest();
  virtual ~PlaybackRequest();

  PlaybackRequest(const PlaybackRequest& from);

  inline PlaybackRequest& operator=(const PlaybackRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackRequest& default_instance();

  static const PlaybackRequest* internal_default_instance();

  void Swap(PlaybackRequest* other);

  // implements Message ----------------------------------------------

  inline PlaybackRequest* New() const { return New(NULL); }

  PlaybackRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackRequest& from);
  void MergeFrom(const PlaybackRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackRequest* other);
  void UnsafeMergeFrom(const PlaybackRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // repeated string htscSecurityIDs = 2;
  int htscsecurityids_size() const;
  void clear_htscsecurityids();
  static const int kHtscSecurityIDsFieldNumber = 2;
  const ::std::string& htscsecurityids(int index) const;
  ::std::string* mutable_htscsecurityids(int index);
  void set_htscsecurityids(int index, const ::std::string& value);
  void set_htscsecurityids(int index, const char* value);
  void set_htscsecurityids(int index, const char* value, size_t size);
  ::std::string* add_htscsecurityids();
  void add_htscsecurityids(const ::std::string& value);
  void add_htscsecurityids(const char* value);
  void add_htscsecurityids(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& htscsecurityids() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_htscsecurityids();

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
  int securitysourcetype_size() const;
  void clear_securitysourcetype();
  static const int kSecuritySourceTypeFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::SecuritySourceType& securitysourcetype(int index) const;
  ::com::htsc::mdc::insight::model::SecuritySourceType* mutable_securitysourcetype(int index);
  ::com::htsc::mdc::insight::model::SecuritySourceType* add_securitysourcetype();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
      mutable_securitysourcetype();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
      securitysourcetype() const;

  // optional string startTime = 4;
  void clear_starttime();
  static const int kStartTimeFieldNumber = 4;
  const ::std::string& starttime() const;
  void set_starttime(const ::std::string& value);
  void set_starttime(const char* value);
  void set_starttime(const char* value, size_t size);
  ::std::string* mutable_starttime();
  ::std::string* release_starttime();
  void set_allocated_starttime(::std::string* starttime);

  // optional string stopTime = 5;
  void clear_stoptime();
  static const int kStopTimeFieldNumber = 5;
  const ::std::string& stoptime() const;
  void set_stoptime(const ::std::string& value);
  void set_stoptime(const char* value);
  void set_stoptime(const char* value, size_t size);
  ::std::string* mutable_stoptime();
  ::std::string* release_stoptime();
  void set_allocated_stoptime(::std::string* stoptime);

  // optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
  void clear_replaydatatype();
  static const int kReplayDataTypeFieldNumber = 6;
  ::com::htsc::mdc::insight::model::EMarketDataType replaydatatype() const;
  void set_replaydatatype(::com::htsc::mdc::insight::model::EMarketDataType value);

  // optional int32 replayRate = 7;
  void clear_replayrate();
  static const int kReplayRateFieldNumber = 7;
  ::google::protobuf::int32 replayrate() const;
  void set_replayrate(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
  void clear_exrightstype();
  static const int kExrightsTypeFieldNumber = 8;
  ::com::htsc::mdc::insight::model::EPlaybackExrightsType exrightstype() const;
  void set_exrightstype(::com::htsc::mdc::insight::model::EPlaybackExrightsType value);

  // optional bool isNeedInitialData = 9;
  void clear_isneedinitialdata();
  static const int kIsNeedInitialDataFieldNumber = 9;
  bool isneedinitialdata() const;
  void set_isneedinitialdata(bool value);

  // optional string initialDataStartTime = 10;
  void clear_initialdatastarttime();
  static const int kInitialDataStartTimeFieldNumber = 10;
  const ::std::string& initialdatastarttime() const;
  void set_initialdatastarttime(const ::std::string& value);
  void set_initialdatastarttime(const char* value);
  void set_initialdatastarttime(const char* value, size_t size);
  ::std::string* mutable_initialdatastarttime();
  ::std::string* release_initialdatastarttime();
  void set_allocated_initialdatastarttime(::std::string* initialdatastarttime);

  // optional int32 replayFuncType = 11;
  void clear_replayfunctype();
  static const int kReplayFuncTypeFieldNumber = 11;
  ::google::protobuf::int32 replayfunctype() const;
  void set_replayfunctype(::google::protobuf::int32 value);

  // optional int32 sortType = 12;
  void clear_sorttype();
  static const int kSortTypeFieldNumber = 12;
  ::google::protobuf::int32 sorttype() const;
  void set_sorttype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
  void clear_idsource();
  static const int kIdSourceFieldNumber = 13;
  ::com::htsc::mdc::model::ESecurityIDSource idsource() const;
  void set_idsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 ChannelNo = 14;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 14;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int64 StartApplSeqNum = 15;
  void clear_startapplseqnum();
  static const int kStartApplSeqNumFieldNumber = 15;
  ::google::protobuf::int64 startapplseqnum() const;
  void set_startapplseqnum(::google::protobuf::int64 value);

  // optional int64 EndApplSeqNum = 16;
  void clear_endapplseqnum();
  static const int kEndApplSeqNumFieldNumber = 16;
  ::google::protobuf::int64 endapplseqnum() const;
  void set_endapplseqnum(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
  int replaydatatypesets_size() const;
  void clear_replaydatatypesets();
  static const int kReplayDataTypeSetsFieldNumber = 17;
  ::com::htsc::mdc::insight::model::EMarketDataType replaydatatypesets(int index) const;
  void set_replaydatatypesets(int index, ::com::htsc::mdc::insight::model::EMarketDataType value);
  void add_replaydatatypesets(::com::htsc::mdc::insight::model::EMarketDataType value);
  const ::google::protobuf::RepeatedField<int>& replaydatatypesets() const;
  ::google::protobuf::RepeatedField<int>* mutable_replaydatatypesets();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> htscsecurityids_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType > securitysourcetype_;
  ::google::protobuf::RepeatedField<int> replaydatatypesets_;
  mutable int _replaydatatypesets_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  ::google::protobuf::internal::ArenaStringPtr starttime_;
  ::google::protobuf::internal::ArenaStringPtr stoptime_;
  ::google::protobuf::internal::ArenaStringPtr initialdatastarttime_;
  int replaydatatype_;
  ::google::protobuf::int32 replayrate_;
  int exrightstype_;
  bool isneedinitialdata_;
  ::google::protobuf::int32 replayfunctype_;
  ::google::protobuf::int32 sorttype_;
  int idsource_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int64 startapplseqnum_;
  ::google::protobuf::int64 endapplseqnum_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackRequest> PlaybackRequest_default_instance_;

// -------------------------------------------------------------------

class PlaybackResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackResponse) */ {
 public:
  PlaybackResponse();
  virtual ~PlaybackResponse();

  PlaybackResponse(const PlaybackResponse& from);

  inline PlaybackResponse& operator=(const PlaybackResponse& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackResponse& default_instance();

  static const PlaybackResponse* internal_default_instance();

  void Swap(PlaybackResponse* other);

  // implements Message ----------------------------------------------

  inline PlaybackResponse* New() const { return New(NULL); }

  PlaybackResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackResponse& from);
  void MergeFrom(const PlaybackResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackResponse* other);
  void UnsafeMergeFrom(const PlaybackResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // optional bool isSuccess = 2;
  void clear_issuccess();
  static const int kIsSuccessFieldNumber = 2;
  bool issuccess() const;
  void set_issuccess(bool value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  bool has_errorcontext() const;
  void clear_errorcontext();
  static const int kErrorContextFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& errorcontext() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_errorcontext();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_errorcontext();
  void set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext_;
  bool issuccess_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackResponse> PlaybackResponse_default_instance_;

// -------------------------------------------------------------------

class PlaybackControlRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackControlRequest) */ {
 public:
  PlaybackControlRequest();
  virtual ~PlaybackControlRequest();

  PlaybackControlRequest(const PlaybackControlRequest& from);

  inline PlaybackControlRequest& operator=(const PlaybackControlRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackControlRequest& default_instance();

  static const PlaybackControlRequest* internal_default_instance();

  void Swap(PlaybackControlRequest* other);

  // implements Message ----------------------------------------------

  inline PlaybackControlRequest* New() const { return New(NULL); }

  PlaybackControlRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackControlRequest& from);
  void MergeFrom(const PlaybackControlRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackControlRequest* other);
  void UnsafeMergeFrom(const PlaybackControlRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
  void clear_controltype();
  static const int kControlTypeFieldNumber = 2;
  ::com::htsc::mdc::insight::model::EPlaybackTaskControlType controltype() const;
  void set_controltype(::com::htsc::mdc::insight::model::EPlaybackTaskControlType value);

  // optional int32 replayRate = 3;
  void clear_replayrate();
  static const int kReplayRateFieldNumber = 3;
  ::google::protobuf::int32 replayrate() const;
  void set_replayrate(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackControlRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  int controltype_;
  ::google::protobuf::int32 replayrate_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackControlRequest> PlaybackControlRequest_default_instance_;

// -------------------------------------------------------------------

class PlaybackControlResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackControlResponse) */ {
 public:
  PlaybackControlResponse();
  virtual ~PlaybackControlResponse();

  PlaybackControlResponse(const PlaybackControlResponse& from);

  inline PlaybackControlResponse& operator=(const PlaybackControlResponse& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackControlResponse& default_instance();

  static const PlaybackControlResponse* internal_default_instance();

  void Swap(PlaybackControlResponse* other);

  // implements Message ----------------------------------------------

  inline PlaybackControlResponse* New() const { return New(NULL); }

  PlaybackControlResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackControlResponse& from);
  void MergeFrom(const PlaybackControlResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackControlResponse* other);
  void UnsafeMergeFrom(const PlaybackControlResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // optional bool isSuccess = 2;
  void clear_issuccess();
  static const int kIsSuccessFieldNumber = 2;
  bool issuccess() const;
  void set_issuccess(bool value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  bool has_errorcontext() const;
  void clear_errorcontext();
  static const int kErrorContextFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& errorcontext() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_errorcontext();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_errorcontext();
  void set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext);

  // optional int32 currentReplayRate = 4;
  void clear_currentreplayrate();
  static const int kCurrentReplayRateFieldNumber = 4;
  ::google::protobuf::int32 currentreplayrate() const;
  void set_currentreplayrate(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackControlResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext_;
  bool issuccess_;
  ::google::protobuf::int32 currentreplayrate_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackControlResponse> PlaybackControlResponse_default_instance_;

// -------------------------------------------------------------------

class PlaybackStatusRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackStatusRequest) */ {
 public:
  PlaybackStatusRequest();
  virtual ~PlaybackStatusRequest();

  PlaybackStatusRequest(const PlaybackStatusRequest& from);

  inline PlaybackStatusRequest& operator=(const PlaybackStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackStatusRequest& default_instance();

  static const PlaybackStatusRequest* internal_default_instance();

  void Swap(PlaybackStatusRequest* other);

  // implements Message ----------------------------------------------

  inline PlaybackStatusRequest* New() const { return New(NULL); }

  PlaybackStatusRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackStatusRequest& from);
  void MergeFrom(const PlaybackStatusRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackStatusRequest* other);
  void UnsafeMergeFrom(const PlaybackStatusRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackStatusRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackStatusRequest> PlaybackStatusRequest_default_instance_;

// -------------------------------------------------------------------

class PlaybackStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackStatus) */ {
 public:
  PlaybackStatus();
  virtual ~PlaybackStatus();

  PlaybackStatus(const PlaybackStatus& from);

  inline PlaybackStatus& operator=(const PlaybackStatus& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackStatus& default_instance();

  static const PlaybackStatus* internal_default_instance();

  void Swap(PlaybackStatus* other);

  // implements Message ----------------------------------------------

  inline PlaybackStatus* New() const { return New(NULL); }

  PlaybackStatus* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackStatus& from);
  void MergeFrom(const PlaybackStatus& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackStatus* other);
  void UnsafeMergeFrom(const PlaybackStatus& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
  void clear_taskstatus();
  static const int kTaskStatusFieldNumber = 2;
  ::com::htsc::mdc::insight::model::EPlaybackTaskStatus taskstatus() const;
  void set_taskstatus(::com::htsc::mdc::insight::model::EPlaybackTaskStatus value);

  // optional int32 replayPercent = 3;
  void clear_replaypercent();
  static const int kReplayPercentFieldNumber = 3;
  ::google::protobuf::int32 replaypercent() const;
  void set_replaypercent(::google::protobuf::int32 value);

  // optional int32 currentReplayRate = 4;
  void clear_currentreplayrate();
  static const int kCurrentReplayRateFieldNumber = 4;
  ::google::protobuf::int32 currentreplayrate() const;
  void set_currentreplayrate(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  int taskstatus_;
  ::google::protobuf::int32 replaypercent_;
  ::google::protobuf::int32 currentreplayrate_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackStatus> PlaybackStatus_default_instance_;

// -------------------------------------------------------------------

class PlaybackPayload : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.PlaybackPayload) */ {
 public:
  PlaybackPayload();
  virtual ~PlaybackPayload();

  PlaybackPayload(const PlaybackPayload& from);

  inline PlaybackPayload& operator=(const PlaybackPayload& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const PlaybackPayload& default_instance();

  static const PlaybackPayload* internal_default_instance();

  void Swap(PlaybackPayload* other);

  // implements Message ----------------------------------------------

  inline PlaybackPayload* New() const { return New(NULL); }

  PlaybackPayload* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const PlaybackPayload& from);
  void MergeFrom(const PlaybackPayload& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlaybackPayload* other);
  void UnsafeMergeFrom(const PlaybackPayload& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string taskId = 1;
  void clear_taskid();
  static const int kTaskIdFieldNumber = 1;
  const ::std::string& taskid() const;
  void set_taskid(const ::std::string& value);
  void set_taskid(const char* value);
  void set_taskid(const char* value, size_t size);
  ::std::string* mutable_taskid();
  ::std::string* release_taskid();
  void set_allocated_taskid(::std::string* taskid);

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
  bool has_marketdatastream() const;
  void clear_marketdatastream();
  static const int kMarketDataStreamFieldNumber = 2;
  const ::com::htsc::mdc::insight::model::MarketDataStream& marketdatastream() const;
  ::com::htsc::mdc::insight::model::MarketDataStream* mutable_marketdatastream();
  ::com::htsc::mdc::insight::model::MarketDataStream* release_marketdatastream();
  void set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.PlaybackPayload)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr taskid_;
  ::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDPlayback_2eproto_impl();
  friend void  protobuf_AddDesc_MDPlayback_2eproto_impl();
  friend void protobuf_AssignDesc_MDPlayback_2eproto();
  friend void protobuf_ShutdownFile_MDPlayback_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<PlaybackPayload> PlaybackPayload_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackRequest

// optional string taskId = 1;
inline void PlaybackRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
inline void PlaybackRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
inline void PlaybackRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
inline ::std::string* PlaybackRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}

// repeated string htscSecurityIDs = 2;
inline int PlaybackRequest::htscsecurityids_size() const {
  return htscsecurityids_.size();
}
inline void PlaybackRequest::clear_htscsecurityids() {
  htscsecurityids_.Clear();
}
inline const ::std::string& PlaybackRequest::htscsecurityids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Get(index);
}
inline ::std::string* PlaybackRequest::mutable_htscsecurityids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Mutable(index);
}
inline void PlaybackRequest::set_htscsecurityids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  htscsecurityids_.Mutable(index)->assign(value);
}
inline void PlaybackRequest::set_htscsecurityids(int index, const char* value) {
  htscsecurityids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
inline void PlaybackRequest::set_htscsecurityids(int index, const char* value, size_t size) {
  htscsecurityids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
inline ::std::string* PlaybackRequest::add_htscsecurityids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Add();
}
inline void PlaybackRequest::add_htscsecurityids(const ::std::string& value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
inline void PlaybackRequest::add_htscsecurityids(const char* value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
inline void PlaybackRequest::add_htscsecurityids(const char* value, size_t size) {
  htscsecurityids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
PlaybackRequest::htscsecurityids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
PlaybackRequest::mutable_htscsecurityids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return &htscsecurityids_;
}

// repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
inline int PlaybackRequest::securitysourcetype_size() const {
  return securitysourcetype_.size();
}
inline void PlaybackRequest::clear_securitysourcetype() {
  securitysourcetype_.Clear();
}
inline const ::com::htsc::mdc::insight::model::SecuritySourceType& PlaybackRequest::securitysourcetype(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Get(index);
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* PlaybackRequest::mutable_securitysourcetype(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* PlaybackRequest::add_securitysourcetype() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
PlaybackRequest::mutable_securitysourcetype() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return &securitysourcetype_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
PlaybackRequest::securitysourcetype() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_;
}

// optional string startTime = 4;
inline void PlaybackRequest::clear_starttime() {
  starttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackRequest::starttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  return starttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_starttime(const ::std::string& value) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
inline void PlaybackRequest::set_starttime(const char* value) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
inline void PlaybackRequest::set_starttime(const char* value, size_t size) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
inline ::std::string* PlaybackRequest::mutable_starttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  return starttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackRequest::release_starttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  
  return starttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_allocated_starttime(::std::string* starttime) {
  if (starttime != NULL) {
    
  } else {
    
  }
  starttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), starttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}

// optional string stopTime = 5;
inline void PlaybackRequest::clear_stoptime() {
  stoptime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackRequest::stoptime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  return stoptime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_stoptime(const ::std::string& value) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
inline void PlaybackRequest::set_stoptime(const char* value) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
inline void PlaybackRequest::set_stoptime(const char* value, size_t size) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
inline ::std::string* PlaybackRequest::mutable_stoptime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  return stoptime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackRequest::release_stoptime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  
  return stoptime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_allocated_stoptime(::std::string* stoptime) {
  if (stoptime != NULL) {
    
  } else {
    
  }
  stoptime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stoptime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}

// optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
inline void PlaybackRequest::clear_replaydatatype() {
  replaydatatype_ = 0;
}
inline ::com::htsc::mdc::insight::model::EMarketDataType PlaybackRequest::replaydatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayDataType)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(replaydatatype_);
}
inline void PlaybackRequest::set_replaydatatype(::com::htsc::mdc::insight::model::EMarketDataType value) {
  
  replaydatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayDataType)
}

// optional int32 replayRate = 7;
inline void PlaybackRequest::clear_replayrate() {
  replayrate_ = 0;
}
inline ::google::protobuf::int32 PlaybackRequest::replayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayRate)
  return replayrate_;
}
inline void PlaybackRequest::set_replayrate(::google::protobuf::int32 value) {
  
  replayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayRate)
}

// optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
inline void PlaybackRequest::clear_exrightstype() {
  exrightstype_ = 0;
}
inline ::com::htsc::mdc::insight::model::EPlaybackExrightsType PlaybackRequest::exrightstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.exrightsType)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackExrightsType >(exrightstype_);
}
inline void PlaybackRequest::set_exrightstype(::com::htsc::mdc::insight::model::EPlaybackExrightsType value) {
  
  exrightstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.exrightsType)
}

// optional bool isNeedInitialData = 9;
inline void PlaybackRequest::clear_isneedinitialdata() {
  isneedinitialdata_ = false;
}
inline bool PlaybackRequest::isneedinitialdata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.isNeedInitialData)
  return isneedinitialdata_;
}
inline void PlaybackRequest::set_isneedinitialdata(bool value) {
  
  isneedinitialdata_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.isNeedInitialData)
}

// optional string initialDataStartTime = 10;
inline void PlaybackRequest::clear_initialdatastarttime() {
  initialdatastarttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackRequest::initialdatastarttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  return initialdatastarttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_initialdatastarttime(const ::std::string& value) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
inline void PlaybackRequest::set_initialdatastarttime(const char* value) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
inline void PlaybackRequest::set_initialdatastarttime(const char* value, size_t size) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
inline ::std::string* PlaybackRequest::mutable_initialdatastarttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  return initialdatastarttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackRequest::release_initialdatastarttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  
  return initialdatastarttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackRequest::set_allocated_initialdatastarttime(::std::string* initialdatastarttime) {
  if (initialdatastarttime != NULL) {
    
  } else {
    
  }
  initialdatastarttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), initialdatastarttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}

// optional int32 replayFuncType = 11;
inline void PlaybackRequest::clear_replayfunctype() {
  replayfunctype_ = 0;
}
inline ::google::protobuf::int32 PlaybackRequest::replayfunctype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayFuncType)
  return replayfunctype_;
}
inline void PlaybackRequest::set_replayfunctype(::google::protobuf::int32 value) {
  
  replayfunctype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayFuncType)
}

// optional int32 sortType = 12;
inline void PlaybackRequest::clear_sorttype() {
  sorttype_ = 0;
}
inline ::google::protobuf::int32 PlaybackRequest::sorttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.sortType)
  return sorttype_;
}
inline void PlaybackRequest::set_sorttype(::google::protobuf::int32 value) {
  
  sorttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.sortType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
inline void PlaybackRequest::clear_idsource() {
  idsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource PlaybackRequest::idsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.idSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(idsource_);
}
inline void PlaybackRequest::set_idsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  idsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.idSource)
}

// optional int32 ChannelNo = 14;
inline void PlaybackRequest::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 PlaybackRequest::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.ChannelNo)
  return channelno_;
}
inline void PlaybackRequest::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.ChannelNo)
}

// optional int64 StartApplSeqNum = 15;
inline void PlaybackRequest::clear_startapplseqnum() {
  startapplseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PlaybackRequest::startapplseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.StartApplSeqNum)
  return startapplseqnum_;
}
inline void PlaybackRequest::set_startapplseqnum(::google::protobuf::int64 value) {
  
  startapplseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.StartApplSeqNum)
}

// optional int64 EndApplSeqNum = 16;
inline void PlaybackRequest::clear_endapplseqnum() {
  endapplseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 PlaybackRequest::endapplseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.EndApplSeqNum)
  return endapplseqnum_;
}
inline void PlaybackRequest::set_endapplseqnum(::google::protobuf::int64 value) {
  
  endapplseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.EndApplSeqNum)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
inline int PlaybackRequest::replaydatatypesets_size() const {
  return replaydatatypesets_.size();
}
inline void PlaybackRequest::clear_replaydatatypesets() {
  replaydatatypesets_.Clear();
}
inline ::com::htsc::mdc::insight::model::EMarketDataType PlaybackRequest::replaydatatypesets(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(replaydatatypesets_.Get(index));
}
inline void PlaybackRequest::set_replaydatatypesets(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  replaydatatypesets_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
}
inline void PlaybackRequest::add_replaydatatypesets(::com::htsc::mdc::insight::model::EMarketDataType value) {
  replaydatatypesets_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
}
inline const ::google::protobuf::RepeatedField<int>&
PlaybackRequest::replaydatatypesets() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return replaydatatypesets_;
}
inline ::google::protobuf::RepeatedField<int>*
PlaybackRequest::mutable_replaydatatypesets() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return &replaydatatypesets_;
}

inline const PlaybackRequest* PlaybackRequest::internal_default_instance() {
  return &PlaybackRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackResponse

// optional string taskId = 1;
inline void PlaybackResponse::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackResponse::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackResponse::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
inline void PlaybackResponse::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
inline void PlaybackResponse::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
inline ::std::string* PlaybackResponse::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackResponse::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackResponse::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}

// optional bool isSuccess = 2;
inline void PlaybackResponse::clear_issuccess() {
  issuccess_ = false;
}
inline bool PlaybackResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.isSuccess)
  return issuccess_;
}
inline void PlaybackResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
inline bool PlaybackResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
inline void PlaybackResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& PlaybackResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  return errorcontext_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
inline void PlaybackResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
}

inline const PlaybackResponse* PlaybackResponse::internal_default_instance() {
  return &PlaybackResponse_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackControlRequest

// optional string taskId = 1;
inline void PlaybackControlRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackControlRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackControlRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
inline void PlaybackControlRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
inline void PlaybackControlRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
inline ::std::string* PlaybackControlRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackControlRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackControlRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}

// optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
inline void PlaybackControlRequest::clear_controltype() {
  controltype_ = 0;
}
inline ::com::htsc::mdc::insight::model::EPlaybackTaskControlType PlaybackControlRequest::controltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.controlType)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskControlType >(controltype_);
}
inline void PlaybackControlRequest::set_controltype(::com::htsc::mdc::insight::model::EPlaybackTaskControlType value) {
  
  controltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.controlType)
}

// optional int32 replayRate = 3;
inline void PlaybackControlRequest::clear_replayrate() {
  replayrate_ = 0;
}
inline ::google::protobuf::int32 PlaybackControlRequest::replayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.replayRate)
  return replayrate_;
}
inline void PlaybackControlRequest::set_replayrate(::google::protobuf::int32 value) {
  
  replayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.replayRate)
}

inline const PlaybackControlRequest* PlaybackControlRequest::internal_default_instance() {
  return &PlaybackControlRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackControlResponse

// optional string taskId = 1;
inline void PlaybackControlResponse::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackControlResponse::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackControlResponse::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
inline void PlaybackControlResponse::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
inline void PlaybackControlResponse::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
inline ::std::string* PlaybackControlResponse::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackControlResponse::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackControlResponse::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}

// optional bool isSuccess = 2;
inline void PlaybackControlResponse::clear_issuccess() {
  issuccess_ = false;
}
inline bool PlaybackControlResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.isSuccess)
  return issuccess_;
}
inline void PlaybackControlResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
inline bool PlaybackControlResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
inline void PlaybackControlResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& PlaybackControlResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackControlResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  return errorcontext_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackControlResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
inline void PlaybackControlResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
}

// optional int32 currentReplayRate = 4;
inline void PlaybackControlResponse::clear_currentreplayrate() {
  currentreplayrate_ = 0;
}
inline ::google::protobuf::int32 PlaybackControlResponse::currentreplayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.currentReplayRate)
  return currentreplayrate_;
}
inline void PlaybackControlResponse::set_currentreplayrate(::google::protobuf::int32 value) {
  
  currentreplayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.currentReplayRate)
}

inline const PlaybackControlResponse* PlaybackControlResponse::internal_default_instance() {
  return &PlaybackControlResponse_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackStatusRequest

// optional string taskId = 1;
inline void PlaybackStatusRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackStatusRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackStatusRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
inline void PlaybackStatusRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
inline void PlaybackStatusRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
inline ::std::string* PlaybackStatusRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackStatusRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackStatusRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}

inline const PlaybackStatusRequest* PlaybackStatusRequest::internal_default_instance() {
  return &PlaybackStatusRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackStatus

// optional string taskId = 1;
inline void PlaybackStatus::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackStatus::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackStatus::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
inline void PlaybackStatus::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
inline void PlaybackStatus::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
inline ::std::string* PlaybackStatus::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackStatus::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackStatus::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}

// optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
inline void PlaybackStatus::clear_taskstatus() {
  taskstatus_ = 0;
}
inline ::com::htsc::mdc::insight::model::EPlaybackTaskStatus PlaybackStatus::taskstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.taskStatus)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskStatus >(taskstatus_);
}
inline void PlaybackStatus::set_taskstatus(::com::htsc::mdc::insight::model::EPlaybackTaskStatus value) {
  
  taskstatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.taskStatus)
}

// optional int32 replayPercent = 3;
inline void PlaybackStatus::clear_replaypercent() {
  replaypercent_ = 0;
}
inline ::google::protobuf::int32 PlaybackStatus::replaypercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.replayPercent)
  return replaypercent_;
}
inline void PlaybackStatus::set_replaypercent(::google::protobuf::int32 value) {
  
  replaypercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.replayPercent)
}

// optional int32 currentReplayRate = 4;
inline void PlaybackStatus::clear_currentreplayrate() {
  currentreplayrate_ = 0;
}
inline ::google::protobuf::int32 PlaybackStatus::currentreplayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.currentReplayRate)
  return currentreplayrate_;
}
inline void PlaybackStatus::set_currentreplayrate(::google::protobuf::int32 value) {
  
  currentreplayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.currentReplayRate)
}

inline const PlaybackStatus* PlaybackStatus::internal_default_instance() {
  return &PlaybackStatus_default_instance_.get();
}
// -------------------------------------------------------------------

// PlaybackPayload

// optional string taskId = 1;
inline void PlaybackPayload::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& PlaybackPayload::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackPayload::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
inline void PlaybackPayload::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
inline void PlaybackPayload::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
inline ::std::string* PlaybackPayload::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* PlaybackPayload::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void PlaybackPayload::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
inline bool PlaybackPayload::has_marketdatastream() const {
  return this != internal_default_instance() && marketdatastream_ != NULL;
}
inline void PlaybackPayload::clear_marketdatastream() {
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MarketDataStream& PlaybackPayload::marketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  return marketdatastream_ != NULL ? *marketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* PlaybackPayload::mutable_marketdatastream() {
  
  if (marketdatastream_ == NULL) {
    marketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  return marketdatastream_;
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* PlaybackPayload::release_marketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = marketdatastream_;
  marketdatastream_ = NULL;
  return temp;
}
inline void PlaybackPayload::set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream) {
  delete marketdatastream_;
  marketdatastream_ = marketdatastream;
  if (marketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
}

inline const PlaybackPayload* PlaybackPayload::internal_default_instance() {
  return &PlaybackPayload_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::com::htsc::mdc::insight::model::EPlaybackExrightsType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::htsc::mdc::insight::model::EPlaybackExrightsType>() {
  return ::com::htsc::mdc::insight::model::EPlaybackExrightsType_descriptor();
}
template <> struct is_proto_enum< ::com::htsc::mdc::insight::model::EPlaybackTaskControlType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::htsc::mdc::insight::model::EPlaybackTaskControlType>() {
  return ::com::htsc::mdc::insight::model::EPlaybackTaskControlType_descriptor();
}
template <> struct is_proto_enum< ::com::htsc::mdc::insight::model::EPlaybackTaskStatus> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::htsc::mdc::insight::model::EPlaybackTaskStatus>() {
  return ::com::htsc::mdc::insight::model::EPlaybackTaskStatus_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDPlayback_2eproto__INCLUDED
