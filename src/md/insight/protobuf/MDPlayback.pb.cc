// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDPlayback.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDPlayback.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* PlaybackRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackResponse_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackControlRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackControlRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackControlResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackControlResponse_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackStatusRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackStatusRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackStatus_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackStatus_reflection_ = NULL;
const ::google::protobuf::Descriptor* PlaybackPayload_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  PlaybackPayload_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* EPlaybackExrightsType_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* EPlaybackTaskControlType_descriptor_ = NULL;
const ::google::protobuf::EnumDescriptor* EPlaybackTaskStatus_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDPlayback_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDPlayback_2eproto() {
  protobuf_AddDesc_MDPlayback_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDPlayback.proto");
  GOOGLE_CHECK(file != NULL);
  PlaybackRequest_descriptor_ = file->message_type(0);
  static const int PlaybackRequest_offsets_[17] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, htscsecurityids_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, securitysourcetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, starttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, stoptime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, replaydatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, replayrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, exrightstype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, isneedinitialdata_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, initialdatastarttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, replayfunctype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, sorttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, idsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, startapplseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, endapplseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, replaydatatypesets_),
  };
  PlaybackRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackRequest_descriptor_,
      PlaybackRequest::internal_default_instance(),
      PlaybackRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackRequest, _internal_metadata_));
  PlaybackResponse_descriptor_ = file->message_type(1);
  static const int PlaybackResponse_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackResponse, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackResponse, issuccess_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackResponse, errorcontext_),
  };
  PlaybackResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackResponse_descriptor_,
      PlaybackResponse::internal_default_instance(),
      PlaybackResponse_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackResponse, _internal_metadata_));
  PlaybackControlRequest_descriptor_ = file->message_type(2);
  static const int PlaybackControlRequest_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlRequest, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlRequest, controltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlRequest, replayrate_),
  };
  PlaybackControlRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackControlRequest_descriptor_,
      PlaybackControlRequest::internal_default_instance(),
      PlaybackControlRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackControlRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlRequest, _internal_metadata_));
  PlaybackControlResponse_descriptor_ = file->message_type(3);
  static const int PlaybackControlResponse_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlResponse, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlResponse, issuccess_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlResponse, errorcontext_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlResponse, currentreplayrate_),
  };
  PlaybackControlResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackControlResponse_descriptor_,
      PlaybackControlResponse::internal_default_instance(),
      PlaybackControlResponse_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackControlResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackControlResponse, _internal_metadata_));
  PlaybackStatusRequest_descriptor_ = file->message_type(4);
  static const int PlaybackStatusRequest_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatusRequest, taskid_),
  };
  PlaybackStatusRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackStatusRequest_descriptor_,
      PlaybackStatusRequest::internal_default_instance(),
      PlaybackStatusRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackStatusRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatusRequest, _internal_metadata_));
  PlaybackStatus_descriptor_ = file->message_type(5);
  static const int PlaybackStatus_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatus, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatus, taskstatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatus, replaypercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatus, currentreplayrate_),
  };
  PlaybackStatus_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackStatus_descriptor_,
      PlaybackStatus::internal_default_instance(),
      PlaybackStatus_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackStatus),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackStatus, _internal_metadata_));
  PlaybackPayload_descriptor_ = file->message_type(6);
  static const int PlaybackPayload_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackPayload, taskid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackPayload, marketdatastream_),
  };
  PlaybackPayload_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      PlaybackPayload_descriptor_,
      PlaybackPayload::internal_default_instance(),
      PlaybackPayload_offsets_,
      -1,
      -1,
      -1,
      sizeof(PlaybackPayload),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(PlaybackPayload, _internal_metadata_));
  EPlaybackExrightsType_descriptor_ = file->enum_type(0);
  EPlaybackTaskControlType_descriptor_ = file->enum_type(1);
  EPlaybackTaskStatus_descriptor_ = file->enum_type(2);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDPlayback_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackRequest_descriptor_, PlaybackRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackResponse_descriptor_, PlaybackResponse::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackControlRequest_descriptor_, PlaybackControlRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackControlResponse_descriptor_, PlaybackControlResponse::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackStatusRequest_descriptor_, PlaybackStatusRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackStatus_descriptor_, PlaybackStatus::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      PlaybackPayload_descriptor_, PlaybackPayload::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDPlayback_2eproto() {
  PlaybackRequest_default_instance_.Shutdown();
  delete PlaybackRequest_reflection_;
  PlaybackResponse_default_instance_.Shutdown();
  delete PlaybackResponse_reflection_;
  PlaybackControlRequest_default_instance_.Shutdown();
  delete PlaybackControlRequest_reflection_;
  PlaybackControlResponse_default_instance_.Shutdown();
  delete PlaybackControlResponse_reflection_;
  PlaybackStatusRequest_default_instance_.Shutdown();
  delete PlaybackStatusRequest_reflection_;
  PlaybackStatus_default_instance_.Shutdown();
  delete PlaybackStatus_reflection_;
  PlaybackPayload_default_instance_.Shutdown();
  delete PlaybackPayload_reflection_;
}

void protobuf_InitDefaults_MDPlayback_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_EMarketDataType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MarketData_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_SecuritySourceType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackRequest_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackResponse_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackControlRequest_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackControlResponse_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackStatusRequest_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackStatus_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  PlaybackPayload_default_instance_.DefaultConstruct();
  PlaybackRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackControlRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackControlResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackStatusRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackStatus_default_instance_.get_mutable()->InitAsDefaultInstance();
  PlaybackPayload_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDPlayback_2eproto_once_);
void protobuf_InitDefaults_MDPlayback_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDPlayback_2eproto_once_,
                 &protobuf_InitDefaults_MDPlayback_2eproto_impl);
}
void protobuf_AddDesc_MDPlayback_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDPlayback_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MDPlayback.proto\022\032com.htsc.mdc.insight"
    ".model\032\025EMarketDataType.proto\032\027ESecurity"
    "IDSource.proto\032\020MarketData.proto\032\031Insigh"
    "tErrorContext.proto\032\030SecuritySourceType."
    "proto\"\365\004\n\017PlaybackRequest\022\016\n\006taskId\030\001 \001("
    "\t\022\027\n\017htscSecurityIDs\030\002 \003(\t\022J\n\022securitySo"
    "urceType\030\003 \003(\0132..com.htsc.mdc.insight.mo"
    "del.SecuritySourceType\022\021\n\tstartTime\030\004 \001("
    "\t\022\020\n\010stopTime\030\005 \001(\t\022C\n\016replayDataType\030\006 "
    "\001(\0162+.com.htsc.mdc.insight.model.EMarket"
    "DataType\022\022\n\nreplayRate\030\007 \001(\005\022G\n\014exrights"
    "Type\030\010 \001(\01621.com.htsc.mdc.insight.model."
    "EPlaybackExrightsType\022\031\n\021isNeedInitialDa"
    "ta\030\t \001(\010\022\034\n\024initialDataStartTime\030\n \001(\t\022\026"
    "\n\016replayFuncType\030\013 \001(\005\022\020\n\010sortType\030\014 \001(\005"
    "\0227\n\010idSource\030\r \001(\0162%.com.htsc.mdc.model."
    "ESecurityIDSource\022\021\n\tChannelNo\030\016 \001(\005\022\027\n\017"
    "StartApplSeqNum\030\017 \001(\003\022\025\n\rEndApplSeqNum\030\020"
    " \001(\003\022G\n\022replayDataTypeSets\030\021 \003(\0162+.com.h"
    "tsc.mdc.insight.model.EMarketDataType\"|\n"
    "\020PlaybackResponse\022\016\n\006taskId\030\001 \001(\t\022\021\n\tisS"
    "uccess\030\002 \001(\010\022E\n\014errorContext\030\003 \001(\0132/.com"
    ".htsc.mdc.insight.model.InsightErrorCont"
    "ext\"\207\001\n\026PlaybackControlRequest\022\016\n\006taskId"
    "\030\001 \001(\t\022I\n\013controlType\030\002 \001(\01624.com.htsc.m"
    "dc.insight.model.EPlaybackTaskControlTyp"
    "e\022\022\n\nreplayRate\030\003 \001(\005\"\236\001\n\027PlaybackContro"
    "lResponse\022\016\n\006taskId\030\001 \001(\t\022\021\n\tisSuccess\030\002"
    " \001(\010\022E\n\014errorContext\030\003 \001(\0132/.com.htsc.md"
    "c.insight.model.InsightErrorContext\022\031\n\021c"
    "urrentReplayRate\030\004 \001(\005\"\'\n\025PlaybackStatus"
    "Request\022\016\n\006taskId\030\001 \001(\t\"\227\001\n\016PlaybackStat"
    "us\022\016\n\006taskId\030\001 \001(\t\022C\n\ntaskStatus\030\002 \001(\0162/"
    ".com.htsc.mdc.insight.model.EPlaybackTas"
    "kStatus\022\025\n\rreplayPercent\030\003 \001(\005\022\031\n\021curren"
    "tReplayRate\030\004 \001(\005\"i\n\017PlaybackPayload\022\016\n\006"
    "taskId\030\001 \001(\t\022F\n\020marketDataStream\030\002 \001(\0132,"
    ".com.htsc.mdc.insight.model.MarketDataSt"
    "ream*p\n\025EPlaybackExrightsType\022\031\n\025DEFAULT"
    "_EXRIGHTS_TYPE\020\000\022\017\n\013NO_EXRIGHTS\020\n\022\024\n\020FOR"
    "WARD_EXRIGHTS\020\013\022\025\n\021BACKWARD_EXRIGHTS\020\014*\\"
    "\n\030EPlaybackTaskControlType\022\030\n\024DEFAULT_CO"
    "NTROL_TYPE\020\000\022\017\n\013CANCEL_TASK\020\001\022\025\n\021SET_PLA"
    "YBACK_RATE\020\002*\235\001\n\023EPlaybackTaskStatus\022\022\n\016"
    "DEFAULT_STATUS\020\000\022\020\n\014INITIALIZING\020\013\022\r\n\tPR"
    "EPARING\020\014\022\014\n\010PREPARED\020\r\022\013\n\007RUNNING\020\016\022\r\n\t"
    "APPENDING\020\017\022\014\n\010CANCELED\020\020\022\r\n\tCOMPLETED\020\021"
    "\022\n\n\006FAILED\020\022B2\n\032com.htsc.mdc.insight.mod"
    "elB\017MDPlaybackProtoH\001\240\001\001b\006proto3", 1952);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDPlayback.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_EMarketDataType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MarketData_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_SecuritySourceType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDPlayback_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDPlayback_2eproto_once_);
void protobuf_AddDesc_MDPlayback_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDPlayback_2eproto_once_,
                 &protobuf_AddDesc_MDPlayback_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDPlayback_2eproto {
  StaticDescriptorInitializer_MDPlayback_2eproto() {
    protobuf_AddDesc_MDPlayback_2eproto();
  }
} static_descriptor_initializer_MDPlayback_2eproto_;
const ::google::protobuf::EnumDescriptor* EPlaybackExrightsType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EPlaybackExrightsType_descriptor_;
}
bool EPlaybackExrightsType_IsValid(int value) {
  switch (value) {
    case 0:
    case 10:
    case 11:
    case 12:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* EPlaybackTaskControlType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EPlaybackTaskControlType_descriptor_;
}
bool EPlaybackTaskControlType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* EPlaybackTaskStatus_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return EPlaybackTaskStatus_descriptor_;
}
bool EPlaybackTaskStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackRequest::kTaskIdFieldNumber;
const int PlaybackRequest::kHtscSecurityIDsFieldNumber;
const int PlaybackRequest::kSecuritySourceTypeFieldNumber;
const int PlaybackRequest::kStartTimeFieldNumber;
const int PlaybackRequest::kStopTimeFieldNumber;
const int PlaybackRequest::kReplayDataTypeFieldNumber;
const int PlaybackRequest::kReplayRateFieldNumber;
const int PlaybackRequest::kExrightsTypeFieldNumber;
const int PlaybackRequest::kIsNeedInitialDataFieldNumber;
const int PlaybackRequest::kInitialDataStartTimeFieldNumber;
const int PlaybackRequest::kReplayFuncTypeFieldNumber;
const int PlaybackRequest::kSortTypeFieldNumber;
const int PlaybackRequest::kIdSourceFieldNumber;
const int PlaybackRequest::kChannelNoFieldNumber;
const int PlaybackRequest::kStartApplSeqNumFieldNumber;
const int PlaybackRequest::kEndApplSeqNumFieldNumber;
const int PlaybackRequest::kReplayDataTypeSetsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackRequest::PlaybackRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackRequest)
}

void PlaybackRequest::InitAsDefaultInstance() {
}

PlaybackRequest::PlaybackRequest(const PlaybackRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackRequest)
}

void PlaybackRequest::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  starttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stoptime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initialdatastarttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&replaydatatype_, 0, reinterpret_cast<char*>(&endapplseqnum_) -
    reinterpret_cast<char*>(&replaydatatype_) + sizeof(endapplseqnum_));
  _cached_size_ = 0;
}

PlaybackRequest::~PlaybackRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackRequest)
  SharedDtor();
}

void PlaybackRequest::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  starttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stoptime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  initialdatastarttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PlaybackRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackRequest_descriptor_;
}

const PlaybackRequest& PlaybackRequest::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackRequest> PlaybackRequest_default_instance_;

PlaybackRequest* PlaybackRequest::New(::google::protobuf::Arena* arena) const {
  PlaybackRequest* n = new PlaybackRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackRequest)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(PlaybackRequest, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<PlaybackRequest*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(replaydatatype_, exrightstype_);
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  starttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  stoptime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(isneedinitialdata_, endapplseqnum_);
  initialdatastarttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

  htscsecurityids_.Clear();
  securitysourcetype_.Clear();
  replaydatatypesets_.Clear();
}

bool PlaybackRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackRequest.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_htscSecurityIDs;
        break;
      }

      // repeated string htscSecurityIDs = 2;
      case 2: {
        if (tag == 18) {
         parse_htscSecurityIDs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_htscsecurityids()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityids(this->htscsecurityids_size() - 1).data(),
            this->htscsecurityids(this->htscsecurityids_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_htscSecurityIDs;
        if (input->ExpectTag(26)) goto parse_securitySourceType;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
      case 3: {
        if (tag == 26) {
         parse_securitySourceType:
          DO_(input->IncrementRecursionDepth());
         parse_loop_securitySourceType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_securitysourcetype()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_securitySourceType;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(34)) goto parse_startTime;
        break;
      }

      // optional string startTime = 4;
      case 4: {
        if (tag == 34) {
         parse_startTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_starttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->starttime().data(), this->starttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackRequest.startTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_stopTime;
        break;
      }

      // optional string stopTime = 5;
      case 5: {
        if (tag == 42) {
         parse_stopTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_stoptime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->stoptime().data(), this->stoptime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackRequest.stopTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_replayDataType;
        break;
      }

      // optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
      case 6: {
        if (tag == 48) {
         parse_replayDataType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_replaydatatype(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_replayRate;
        break;
      }

      // optional int32 replayRate = 7;
      case 7: {
        if (tag == 56) {
         parse_replayRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replayrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_exrightsType;
        break;
      }

      // optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
      case 8: {
        if (tag == 64) {
         parse_exrightsType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_exrightstype(static_cast< ::com::htsc::mdc::insight::model::EPlaybackExrightsType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_isNeedInitialData;
        break;
      }

      // optional bool isNeedInitialData = 9;
      case 9: {
        if (tag == 72) {
         parse_isNeedInitialData:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isneedinitialdata_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_initialDataStartTime;
        break;
      }

      // optional string initialDataStartTime = 10;
      case 10: {
        if (tag == 82) {
         parse_initialDataStartTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_initialdatastarttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->initialdatastarttime().data(), this->initialdatastarttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_replayFuncType;
        break;
      }

      // optional int32 replayFuncType = 11;
      case 11: {
        if (tag == 88) {
         parse_replayFuncType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replayfunctype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_sortType;
        break;
      }

      // optional int32 sortType = 12;
      case 12: {
        if (tag == 96) {
         parse_sortType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sorttype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_idSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
      case 13: {
        if (tag == 104) {
         parse_idSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_idsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 14;
      case 14: {
        if (tag == 112) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_StartApplSeqNum;
        break;
      }

      // optional int64 StartApplSeqNum = 15;
      case 15: {
        if (tag == 120) {
         parse_StartApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &startapplseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_EndApplSeqNum;
        break;
      }

      // optional int64 EndApplSeqNum = 16;
      case 16: {
        if (tag == 128) {
         parse_EndApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &endapplseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_replayDataTypeSets;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
      case 17: {
        if (tag == 138) {
         parse_replayDataTypeSets:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_replaydatatypesets(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 136) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_replaydatatypesets(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackRequest)
  return false;
#undef DO_
}

void PlaybackRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // repeated string htscSecurityIDs = 2;
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityids(i).data(), this->htscsecurityids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      2, this->htscsecurityids(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
  for (unsigned int i = 0, n = this->securitysourcetype_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->securitysourcetype(i), output);
  }

  // optional string startTime = 4;
  if (this->starttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->starttime().data(), this->starttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.startTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->starttime(), output);
  }

  // optional string stopTime = 5;
  if (this->stoptime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->stoptime().data(), this->stoptime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.stopTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->stoptime(), output);
  }

  // optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
  if (this->replaydatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->replaydatatype(), output);
  }

  // optional int32 replayRate = 7;
  if (this->replayrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->replayrate(), output);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
  if (this->exrightstype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      8, this->exrightstype(), output);
  }

  // optional bool isNeedInitialData = 9;
  if (this->isneedinitialdata() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(9, this->isneedinitialdata(), output);
  }

  // optional string initialDataStartTime = 10;
  if (this->initialdatastarttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initialdatastarttime().data(), this->initialdatastarttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->initialdatastarttime(), output);
  }

  // optional int32 replayFuncType = 11;
  if (this->replayfunctype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->replayfunctype(), output);
  }

  // optional int32 sortType = 12;
  if (this->sorttype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->sorttype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
  if (this->idsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      13, this->idsource(), output);
  }

  // optional int32 ChannelNo = 14;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->channelno(), output);
  }

  // optional int64 StartApplSeqNum = 15;
  if (this->startapplseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->startapplseqnum(), output);
  }

  // optional int64 EndApplSeqNum = 16;
  if (this->endapplseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->endapplseqnum(), output);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
  if (this->replaydatatypesets_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      17,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_replaydatatypesets_cached_byte_size_);
  }
  for (int i = 0; i < this->replaydatatypesets_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->replaydatatypesets(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackRequest)
}

::google::protobuf::uint8* PlaybackRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // repeated string htscSecurityIDs = 2;
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityids(i).data(), this->htscsecurityids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(2, this->htscsecurityids(i), target);
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
  for (unsigned int i = 0, n = this->securitysourcetype_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->securitysourcetype(i), false, target);
  }

  // optional string startTime = 4;
  if (this->starttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->starttime().data(), this->starttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.startTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->starttime(), target);
  }

  // optional string stopTime = 5;
  if (this->stoptime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->stoptime().data(), this->stoptime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.stopTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->stoptime(), target);
  }

  // optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
  if (this->replaydatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->replaydatatype(), target);
  }

  // optional int32 replayRate = 7;
  if (this->replayrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->replayrate(), target);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
  if (this->exrightstype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      8, this->exrightstype(), target);
  }

  // optional bool isNeedInitialData = 9;
  if (this->isneedinitialdata() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(9, this->isneedinitialdata(), target);
  }

  // optional string initialDataStartTime = 10;
  if (this->initialdatastarttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->initialdatastarttime().data(), this->initialdatastarttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->initialdatastarttime(), target);
  }

  // optional int32 replayFuncType = 11;
  if (this->replayfunctype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->replayfunctype(), target);
  }

  // optional int32 sortType = 12;
  if (this->sorttype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->sorttype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
  if (this->idsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      13, this->idsource(), target);
  }

  // optional int32 ChannelNo = 14;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->channelno(), target);
  }

  // optional int64 StartApplSeqNum = 15;
  if (this->startapplseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->startapplseqnum(), target);
  }

  // optional int64 EndApplSeqNum = 16;
  if (this->endapplseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->endapplseqnum(), target);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
  if (this->replaydatatypesets_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      17,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _replaydatatypesets_cached_byte_size_, target);
  }
  for (int i = 0; i < this->replaydatatypesets_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->replaydatatypesets(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackRequest)
  return target;
}

size_t PlaybackRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackRequest)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional string startTime = 4;
  if (this->starttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->starttime());
  }

  // optional string stopTime = 5;
  if (this->stoptime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->stoptime());
  }

  // optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
  if (this->replaydatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->replaydatatype());
  }

  // optional int32 replayRate = 7;
  if (this->replayrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->replayrate());
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
  if (this->exrightstype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->exrightstype());
  }

  // optional bool isNeedInitialData = 9;
  if (this->isneedinitialdata() != 0) {
    total_size += 1 + 1;
  }

  // optional string initialDataStartTime = 10;
  if (this->initialdatastarttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->initialdatastarttime());
  }

  // optional int32 replayFuncType = 11;
  if (this->replayfunctype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->replayfunctype());
  }

  // optional int32 sortType = 12;
  if (this->sorttype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sorttype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
  if (this->idsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->idsource());
  }

  // optional int32 ChannelNo = 14;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 StartApplSeqNum = 15;
  if (this->startapplseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->startapplseqnum());
  }

  // optional int64 EndApplSeqNum = 16;
  if (this->endapplseqnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->endapplseqnum());
  }

  // repeated string htscSecurityIDs = 2;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->htscsecurityids_size());
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->htscsecurityids(i));
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
  {
    unsigned int count = this->securitysourcetype_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->securitysourcetype(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
  {
    size_t data_size = 0;
    unsigned int count = this->replaydatatypesets_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->replaydatatypesets(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _replaydatatypesets_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackRequest)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackRequest::MergeFrom(const PlaybackRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackRequest::UnsafeMergeFrom(const PlaybackRequest& from) {
  GOOGLE_DCHECK(&from != this);
  htscsecurityids_.UnsafeMergeFrom(from.htscsecurityids_);
  securitysourcetype_.MergeFrom(from.securitysourcetype_);
  replaydatatypesets_.UnsafeMergeFrom(from.replaydatatypesets_);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.starttime().size() > 0) {

    starttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.starttime_);
  }
  if (from.stoptime().size() > 0) {

    stoptime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.stoptime_);
  }
  if (from.replaydatatype() != 0) {
    set_replaydatatype(from.replaydatatype());
  }
  if (from.replayrate() != 0) {
    set_replayrate(from.replayrate());
  }
  if (from.exrightstype() != 0) {
    set_exrightstype(from.exrightstype());
  }
  if (from.isneedinitialdata() != 0) {
    set_isneedinitialdata(from.isneedinitialdata());
  }
  if (from.initialdatastarttime().size() > 0) {

    initialdatastarttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.initialdatastarttime_);
  }
  if (from.replayfunctype() != 0) {
    set_replayfunctype(from.replayfunctype());
  }
  if (from.sorttype() != 0) {
    set_sorttype(from.sorttype());
  }
  if (from.idsource() != 0) {
    set_idsource(from.idsource());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.startapplseqnum() != 0) {
    set_startapplseqnum(from.startapplseqnum());
  }
  if (from.endapplseqnum() != 0) {
    set_endapplseqnum(from.endapplseqnum());
  }
}

void PlaybackRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackRequest::CopyFrom(const PlaybackRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackRequest::IsInitialized() const {

  return true;
}

void PlaybackRequest::Swap(PlaybackRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackRequest::InternalSwap(PlaybackRequest* other) {
  taskid_.Swap(&other->taskid_);
  htscsecurityids_.UnsafeArenaSwap(&other->htscsecurityids_);
  securitysourcetype_.UnsafeArenaSwap(&other->securitysourcetype_);
  starttime_.Swap(&other->starttime_);
  stoptime_.Swap(&other->stoptime_);
  std::swap(replaydatatype_, other->replaydatatype_);
  std::swap(replayrate_, other->replayrate_);
  std::swap(exrightstype_, other->exrightstype_);
  std::swap(isneedinitialdata_, other->isneedinitialdata_);
  initialdatastarttime_.Swap(&other->initialdatastarttime_);
  std::swap(replayfunctype_, other->replayfunctype_);
  std::swap(sorttype_, other->sorttype_);
  std::swap(idsource_, other->idsource_);
  std::swap(channelno_, other->channelno_);
  std::swap(startapplseqnum_, other->startapplseqnum_);
  std::swap(endapplseqnum_, other->endapplseqnum_);
  replaydatatypesets_.UnsafeArenaSwap(&other->replaydatatypesets_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackRequest_descriptor_;
  metadata.reflection = PlaybackRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackRequest

// optional string taskId = 1;
void PlaybackRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
void PlaybackRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
void PlaybackRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}
::std::string* PlaybackRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.taskId)
}

// repeated string htscSecurityIDs = 2;
int PlaybackRequest::htscsecurityids_size() const {
  return htscsecurityids_.size();
}
void PlaybackRequest::clear_htscsecurityids() {
  htscsecurityids_.Clear();
}
const ::std::string& PlaybackRequest::htscsecurityids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Get(index);
}
::std::string* PlaybackRequest::mutable_htscsecurityids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Mutable(index);
}
void PlaybackRequest::set_htscsecurityids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  htscsecurityids_.Mutable(index)->assign(value);
}
void PlaybackRequest::set_htscsecurityids(int index, const char* value) {
  htscsecurityids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
void PlaybackRequest::set_htscsecurityids(int index, const char* value, size_t size) {
  htscsecurityids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
::std::string* PlaybackRequest::add_htscsecurityids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_.Add();
}
void PlaybackRequest::add_htscsecurityids(const ::std::string& value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
void PlaybackRequest::add_htscsecurityids(const char* value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
void PlaybackRequest::add_htscsecurityids(const char* value, size_t size) {
  htscsecurityids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
PlaybackRequest::htscsecurityids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return htscsecurityids_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
PlaybackRequest::mutable_htscsecurityids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.htscSecurityIDs)
  return &htscsecurityids_;
}

// repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 3;
int PlaybackRequest::securitysourcetype_size() const {
  return securitysourcetype_.size();
}
void PlaybackRequest::clear_securitysourcetype() {
  securitysourcetype_.Clear();
}
const ::com::htsc::mdc::insight::model::SecuritySourceType& PlaybackRequest::securitysourcetype(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Get(index);
}
::com::htsc::mdc::insight::model::SecuritySourceType* PlaybackRequest::mutable_securitysourcetype(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Mutable(index);
}
::com::htsc::mdc::insight::model::SecuritySourceType* PlaybackRequest::add_securitysourcetype() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
PlaybackRequest::mutable_securitysourcetype() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return &securitysourcetype_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
PlaybackRequest::securitysourcetype() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.securitySourceType)
  return securitysourcetype_;
}

// optional string startTime = 4;
void PlaybackRequest::clear_starttime() {
  starttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackRequest::starttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  return starttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_starttime(const ::std::string& value) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
void PlaybackRequest::set_starttime(const char* value) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
void PlaybackRequest::set_starttime(const char* value, size_t size) {
  
  starttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}
::std::string* PlaybackRequest::mutable_starttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  return starttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackRequest::release_starttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
  
  return starttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_allocated_starttime(::std::string* starttime) {
  if (starttime != NULL) {
    
  } else {
    
  }
  starttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), starttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.startTime)
}

// optional string stopTime = 5;
void PlaybackRequest::clear_stoptime() {
  stoptime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackRequest::stoptime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  return stoptime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_stoptime(const ::std::string& value) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
void PlaybackRequest::set_stoptime(const char* value) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
void PlaybackRequest::set_stoptime(const char* value, size_t size) {
  
  stoptime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}
::std::string* PlaybackRequest::mutable_stoptime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  return stoptime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackRequest::release_stoptime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
  
  return stoptime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_allocated_stoptime(::std::string* stoptime) {
  if (stoptime != NULL) {
    
  } else {
    
  }
  stoptime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), stoptime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.stopTime)
}

// optional .com.htsc.mdc.insight.model.EMarketDataType replayDataType = 6;
void PlaybackRequest::clear_replaydatatype() {
  replaydatatype_ = 0;
}
::com::htsc::mdc::insight::model::EMarketDataType PlaybackRequest::replaydatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayDataType)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(replaydatatype_);
}
void PlaybackRequest::set_replaydatatype(::com::htsc::mdc::insight::model::EMarketDataType value) {
  
  replaydatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayDataType)
}

// optional int32 replayRate = 7;
void PlaybackRequest::clear_replayrate() {
  replayrate_ = 0;
}
::google::protobuf::int32 PlaybackRequest::replayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayRate)
  return replayrate_;
}
void PlaybackRequest::set_replayrate(::google::protobuf::int32 value) {
  
  replayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayRate)
}

// optional .com.htsc.mdc.insight.model.EPlaybackExrightsType exrightsType = 8;
void PlaybackRequest::clear_exrightstype() {
  exrightstype_ = 0;
}
::com::htsc::mdc::insight::model::EPlaybackExrightsType PlaybackRequest::exrightstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.exrightsType)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackExrightsType >(exrightstype_);
}
void PlaybackRequest::set_exrightstype(::com::htsc::mdc::insight::model::EPlaybackExrightsType value) {
  
  exrightstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.exrightsType)
}

// optional bool isNeedInitialData = 9;
void PlaybackRequest::clear_isneedinitialdata() {
  isneedinitialdata_ = false;
}
bool PlaybackRequest::isneedinitialdata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.isNeedInitialData)
  return isneedinitialdata_;
}
void PlaybackRequest::set_isneedinitialdata(bool value) {
  
  isneedinitialdata_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.isNeedInitialData)
}

// optional string initialDataStartTime = 10;
void PlaybackRequest::clear_initialdatastarttime() {
  initialdatastarttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackRequest::initialdatastarttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  return initialdatastarttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_initialdatastarttime(const ::std::string& value) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
void PlaybackRequest::set_initialdatastarttime(const char* value) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
void PlaybackRequest::set_initialdatastarttime(const char* value, size_t size) {
  
  initialdatastarttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}
::std::string* PlaybackRequest::mutable_initialdatastarttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  return initialdatastarttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackRequest::release_initialdatastarttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
  
  return initialdatastarttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackRequest::set_allocated_initialdatastarttime(::std::string* initialdatastarttime) {
  if (initialdatastarttime != NULL) {
    
  } else {
    
  }
  initialdatastarttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), initialdatastarttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackRequest.initialDataStartTime)
}

// optional int32 replayFuncType = 11;
void PlaybackRequest::clear_replayfunctype() {
  replayfunctype_ = 0;
}
::google::protobuf::int32 PlaybackRequest::replayfunctype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayFuncType)
  return replayfunctype_;
}
void PlaybackRequest::set_replayfunctype(::google::protobuf::int32 value) {
  
  replayfunctype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayFuncType)
}

// optional int32 sortType = 12;
void PlaybackRequest::clear_sorttype() {
  sorttype_ = 0;
}
::google::protobuf::int32 PlaybackRequest::sorttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.sortType)
  return sorttype_;
}
void PlaybackRequest::set_sorttype(::google::protobuf::int32 value) {
  
  sorttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.sortType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource idSource = 13;
void PlaybackRequest::clear_idsource() {
  idsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource PlaybackRequest::idsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.idSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(idsource_);
}
void PlaybackRequest::set_idsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  idsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.idSource)
}

// optional int32 ChannelNo = 14;
void PlaybackRequest::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 PlaybackRequest::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.ChannelNo)
  return channelno_;
}
void PlaybackRequest::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.ChannelNo)
}

// optional int64 StartApplSeqNum = 15;
void PlaybackRequest::clear_startapplseqnum() {
  startapplseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 PlaybackRequest::startapplseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.StartApplSeqNum)
  return startapplseqnum_;
}
void PlaybackRequest::set_startapplseqnum(::google::protobuf::int64 value) {
  
  startapplseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.StartApplSeqNum)
}

// optional int64 EndApplSeqNum = 16;
void PlaybackRequest::clear_endapplseqnum() {
  endapplseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 PlaybackRequest::endapplseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.EndApplSeqNum)
  return endapplseqnum_;
}
void PlaybackRequest::set_endapplseqnum(::google::protobuf::int64 value) {
  
  endapplseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.EndApplSeqNum)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType replayDataTypeSets = 17;
int PlaybackRequest::replaydatatypesets_size() const {
  return replaydatatypesets_.size();
}
void PlaybackRequest::clear_replaydatatypesets() {
  replaydatatypesets_.Clear();
}
::com::htsc::mdc::insight::model::EMarketDataType PlaybackRequest::replaydatatypesets(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(replaydatatypesets_.Get(index));
}
void PlaybackRequest::set_replaydatatypesets(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  replaydatatypesets_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
}
void PlaybackRequest::add_replaydatatypesets(::com::htsc::mdc::insight::model::EMarketDataType value) {
  replaydatatypesets_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
}
const ::google::protobuf::RepeatedField<int>&
PlaybackRequest::replaydatatypesets() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return replaydatatypesets_;
}
::google::protobuf::RepeatedField<int>*
PlaybackRequest::mutable_replaydatatypesets() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.PlaybackRequest.replayDataTypeSets)
  return &replaydatatypesets_;
}

inline const PlaybackRequest* PlaybackRequest::internal_default_instance() {
  return &PlaybackRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackResponse::kTaskIdFieldNumber;
const int PlaybackResponse::kIsSuccessFieldNumber;
const int PlaybackResponse::kErrorContextFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackResponse::PlaybackResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackResponse)
}

void PlaybackResponse::InitAsDefaultInstance() {
  errorcontext_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
}

PlaybackResponse::PlaybackResponse(const PlaybackResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackResponse)
}

void PlaybackResponse::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  errorcontext_ = NULL;
  issuccess_ = false;
  _cached_size_ = 0;
}

PlaybackResponse::~PlaybackResponse() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackResponse)
  SharedDtor();
}

void PlaybackResponse::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &PlaybackResponse_default_instance_.get()) {
    delete errorcontext_;
  }
}

void PlaybackResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackResponse_descriptor_;
}

const PlaybackResponse& PlaybackResponse::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackResponse> PlaybackResponse_default_instance_;

PlaybackResponse* PlaybackResponse::New(::google::protobuf::Arena* arena) const {
  PlaybackResponse* n = new PlaybackResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackResponse)
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  issuccess_ = false;
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}

bool PlaybackResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackResponse.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_isSuccess;
        break;
      }

      // optional bool isSuccess = 2;
      case 2: {
        if (tag == 16) {
         parse_isSuccess:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issuccess_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_errorContext;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
      case 3: {
        if (tag == 26) {
         parse_errorContext:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_errorcontext()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackResponse)
  return false;
#undef DO_
}

void PlaybackResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackResponse)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackResponse.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->issuccess(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->errorcontext_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackResponse)
}

::google::protobuf::uint8* PlaybackResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackResponse)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackResponse.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->issuccess(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->errorcontext_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackResponse)
  return target;
}

size_t PlaybackResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackResponse)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    total_size += 1 + 1;
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->errorcontext_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackResponse)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackResponse::MergeFrom(const PlaybackResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackResponse::UnsafeMergeFrom(const PlaybackResponse& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.issuccess() != 0) {
    set_issuccess(from.issuccess());
  }
  if (from.has_errorcontext()) {
    mutable_errorcontext()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.errorcontext());
  }
}

void PlaybackResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackResponse::CopyFrom(const PlaybackResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackResponse::IsInitialized() const {

  return true;
}

void PlaybackResponse::Swap(PlaybackResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackResponse::InternalSwap(PlaybackResponse* other) {
  taskid_.Swap(&other->taskid_);
  std::swap(issuccess_, other->issuccess_);
  std::swap(errorcontext_, other->errorcontext_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackResponse_descriptor_;
  metadata.reflection = PlaybackResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackResponse

// optional string taskId = 1;
void PlaybackResponse::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackResponse::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackResponse::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
void PlaybackResponse::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
void PlaybackResponse::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}
::std::string* PlaybackResponse::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackResponse::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackResponse::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackResponse.taskId)
}

// optional bool isSuccess = 2;
void PlaybackResponse::clear_issuccess() {
  issuccess_ = false;
}
bool PlaybackResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.isSuccess)
  return issuccess_;
}
void PlaybackResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
bool PlaybackResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
void PlaybackResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& PlaybackResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  return errorcontext_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
void PlaybackResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackResponse.errorContext)
}

inline const PlaybackResponse* PlaybackResponse::internal_default_instance() {
  return &PlaybackResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackControlRequest::kTaskIdFieldNumber;
const int PlaybackControlRequest::kControlTypeFieldNumber;
const int PlaybackControlRequest::kReplayRateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackControlRequest::PlaybackControlRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackControlRequest)
}

void PlaybackControlRequest::InitAsDefaultInstance() {
}

PlaybackControlRequest::PlaybackControlRequest(const PlaybackControlRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackControlRequest)
}

void PlaybackControlRequest::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&controltype_, 0, reinterpret_cast<char*>(&replayrate_) -
    reinterpret_cast<char*>(&controltype_) + sizeof(replayrate_));
  _cached_size_ = 0;
}

PlaybackControlRequest::~PlaybackControlRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackControlRequest)
  SharedDtor();
}

void PlaybackControlRequest::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PlaybackControlRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackControlRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackControlRequest_descriptor_;
}

const PlaybackControlRequest& PlaybackControlRequest::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackControlRequest> PlaybackControlRequest_default_instance_;

PlaybackControlRequest* PlaybackControlRequest::New(::google::protobuf::Arena* arena) const {
  PlaybackControlRequest* n = new PlaybackControlRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackControlRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(PlaybackControlRequest, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<PlaybackControlRequest*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(controltype_, replayrate_);
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool PlaybackControlRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackControlRequest.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_controlType;
        break;
      }

      // optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
      case 2: {
        if (tag == 16) {
         parse_controlType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_controltype(static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskControlType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_replayRate;
        break;
      }

      // optional int32 replayRate = 3;
      case 3: {
        if (tag == 24) {
         parse_replayRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replayrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackControlRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackControlRequest)
  return false;
#undef DO_
}

void PlaybackControlRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackControlRequest.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
  if (this->controltype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->controltype(), output);
  }

  // optional int32 replayRate = 3;
  if (this->replayrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->replayrate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackControlRequest)
}

::google::protobuf::uint8* PlaybackControlRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackControlRequest.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
  if (this->controltype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->controltype(), target);
  }

  // optional int32 replayRate = 3;
  if (this->replayrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->replayrate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackControlRequest)
  return target;
}

size_t PlaybackControlRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
  if (this->controltype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->controltype());
  }

  // optional int32 replayRate = 3;
  if (this->replayrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->replayrate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackControlRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackControlRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackControlRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackControlRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackControlRequest)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackControlRequest::MergeFrom(const PlaybackControlRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackControlRequest::UnsafeMergeFrom(const PlaybackControlRequest& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.controltype() != 0) {
    set_controltype(from.controltype());
  }
  if (from.replayrate() != 0) {
    set_replayrate(from.replayrate());
  }
}

void PlaybackControlRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackControlRequest::CopyFrom(const PlaybackControlRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackControlRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackControlRequest::IsInitialized() const {

  return true;
}

void PlaybackControlRequest::Swap(PlaybackControlRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackControlRequest::InternalSwap(PlaybackControlRequest* other) {
  taskid_.Swap(&other->taskid_);
  std::swap(controltype_, other->controltype_);
  std::swap(replayrate_, other->replayrate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackControlRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackControlRequest_descriptor_;
  metadata.reflection = PlaybackControlRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackControlRequest

// optional string taskId = 1;
void PlaybackControlRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackControlRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackControlRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
void PlaybackControlRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
void PlaybackControlRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}
::std::string* PlaybackControlRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackControlRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackControlRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlRequest.taskId)
}

// optional .com.htsc.mdc.insight.model.EPlaybackTaskControlType controlType = 2;
void PlaybackControlRequest::clear_controltype() {
  controltype_ = 0;
}
::com::htsc::mdc::insight::model::EPlaybackTaskControlType PlaybackControlRequest::controltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.controlType)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskControlType >(controltype_);
}
void PlaybackControlRequest::set_controltype(::com::htsc::mdc::insight::model::EPlaybackTaskControlType value) {
  
  controltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.controlType)
}

// optional int32 replayRate = 3;
void PlaybackControlRequest::clear_replayrate() {
  replayrate_ = 0;
}
::google::protobuf::int32 PlaybackControlRequest::replayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlRequest.replayRate)
  return replayrate_;
}
void PlaybackControlRequest::set_replayrate(::google::protobuf::int32 value) {
  
  replayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlRequest.replayRate)
}

inline const PlaybackControlRequest* PlaybackControlRequest::internal_default_instance() {
  return &PlaybackControlRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackControlResponse::kTaskIdFieldNumber;
const int PlaybackControlResponse::kIsSuccessFieldNumber;
const int PlaybackControlResponse::kErrorContextFieldNumber;
const int PlaybackControlResponse::kCurrentReplayRateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackControlResponse::PlaybackControlResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackControlResponse)
}

void PlaybackControlResponse::InitAsDefaultInstance() {
  errorcontext_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
}

PlaybackControlResponse::PlaybackControlResponse(const PlaybackControlResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackControlResponse)
}

void PlaybackControlResponse::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  errorcontext_ = NULL;
  ::memset(&issuccess_, 0, reinterpret_cast<char*>(&currentreplayrate_) -
    reinterpret_cast<char*>(&issuccess_) + sizeof(currentreplayrate_));
  _cached_size_ = 0;
}

PlaybackControlResponse::~PlaybackControlResponse() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackControlResponse)
  SharedDtor();
}

void PlaybackControlResponse::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &PlaybackControlResponse_default_instance_.get()) {
    delete errorcontext_;
  }
}

void PlaybackControlResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackControlResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackControlResponse_descriptor_;
}

const PlaybackControlResponse& PlaybackControlResponse::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackControlResponse> PlaybackControlResponse_default_instance_;

PlaybackControlResponse* PlaybackControlResponse::New(::google::protobuf::Arena* arena) const {
  PlaybackControlResponse* n = new PlaybackControlResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackControlResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(PlaybackControlResponse, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<PlaybackControlResponse*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(issuccess_, currentreplayrate_);
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool PlaybackControlResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackControlResponse.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_isSuccess;
        break;
      }

      // optional bool isSuccess = 2;
      case 2: {
        if (tag == 16) {
         parse_isSuccess:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issuccess_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_errorContext;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
      case 3: {
        if (tag == 26) {
         parse_errorContext:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_errorcontext()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_currentReplayRate;
        break;
      }

      // optional int32 currentReplayRate = 4;
      case 4: {
        if (tag == 32) {
         parse_currentReplayRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &currentreplayrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackControlResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackControlResponse)
  return false;
#undef DO_
}

void PlaybackControlResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackControlResponse.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->issuccess(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->errorcontext_, output);
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->currentreplayrate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackControlResponse)
}

::google::protobuf::uint8* PlaybackControlResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackControlResponse.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->issuccess(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->errorcontext_, false, target);
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->currentreplayrate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackControlResponse)
  return target;
}

size_t PlaybackControlResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    total_size += 1 + 1;
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->errorcontext_);
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->currentreplayrate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackControlResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackControlResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackControlResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackControlResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackControlResponse)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackControlResponse::MergeFrom(const PlaybackControlResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackControlResponse::UnsafeMergeFrom(const PlaybackControlResponse& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.issuccess() != 0) {
    set_issuccess(from.issuccess());
  }
  if (from.has_errorcontext()) {
    mutable_errorcontext()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.errorcontext());
  }
  if (from.currentreplayrate() != 0) {
    set_currentreplayrate(from.currentreplayrate());
  }
}

void PlaybackControlResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackControlResponse::CopyFrom(const PlaybackControlResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackControlResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackControlResponse::IsInitialized() const {

  return true;
}

void PlaybackControlResponse::Swap(PlaybackControlResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackControlResponse::InternalSwap(PlaybackControlResponse* other) {
  taskid_.Swap(&other->taskid_);
  std::swap(issuccess_, other->issuccess_);
  std::swap(errorcontext_, other->errorcontext_);
  std::swap(currentreplayrate_, other->currentreplayrate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackControlResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackControlResponse_descriptor_;
  metadata.reflection = PlaybackControlResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackControlResponse

// optional string taskId = 1;
void PlaybackControlResponse::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackControlResponse::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackControlResponse::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
void PlaybackControlResponse::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
void PlaybackControlResponse::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}
::std::string* PlaybackControlResponse::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackControlResponse::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackControlResponse::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlResponse.taskId)
}

// optional bool isSuccess = 2;
void PlaybackControlResponse::clear_issuccess() {
  issuccess_ = false;
}
bool PlaybackControlResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.isSuccess)
  return issuccess_;
}
void PlaybackControlResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
bool PlaybackControlResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
void PlaybackControlResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& PlaybackControlResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackControlResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  return errorcontext_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* PlaybackControlResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
void PlaybackControlResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackControlResponse.errorContext)
}

// optional int32 currentReplayRate = 4;
void PlaybackControlResponse::clear_currentreplayrate() {
  currentreplayrate_ = 0;
}
::google::protobuf::int32 PlaybackControlResponse::currentreplayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackControlResponse.currentReplayRate)
  return currentreplayrate_;
}
void PlaybackControlResponse::set_currentreplayrate(::google::protobuf::int32 value) {
  
  currentreplayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackControlResponse.currentReplayRate)
}

inline const PlaybackControlResponse* PlaybackControlResponse::internal_default_instance() {
  return &PlaybackControlResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackStatusRequest::kTaskIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackStatusRequest::PlaybackStatusRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackStatusRequest)
}

void PlaybackStatusRequest::InitAsDefaultInstance() {
}

PlaybackStatusRequest::PlaybackStatusRequest(const PlaybackStatusRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackStatusRequest)
}

void PlaybackStatusRequest::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

PlaybackStatusRequest::~PlaybackStatusRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  SharedDtor();
}

void PlaybackStatusRequest::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PlaybackStatusRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackStatusRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackStatusRequest_descriptor_;
}

const PlaybackStatusRequest& PlaybackStatusRequest::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackStatusRequest> PlaybackStatusRequest_default_instance_;

PlaybackStatusRequest* PlaybackStatusRequest::New(::google::protobuf::Arena* arena) const {
  PlaybackStatusRequest* n = new PlaybackStatusRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackStatusRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool PlaybackStatusRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  return false;
#undef DO_
}

void PlaybackStatusRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackStatusRequest)
}

::google::protobuf::uint8* PlaybackStatusRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  return target;
}

size_t PlaybackStatusRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackStatusRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackStatusRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackStatusRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackStatusRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackStatusRequest)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackStatusRequest::MergeFrom(const PlaybackStatusRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackStatusRequest::UnsafeMergeFrom(const PlaybackStatusRequest& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
}

void PlaybackStatusRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackStatusRequest::CopyFrom(const PlaybackStatusRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackStatusRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackStatusRequest::IsInitialized() const {

  return true;
}

void PlaybackStatusRequest::Swap(PlaybackStatusRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackStatusRequest::InternalSwap(PlaybackStatusRequest* other) {
  taskid_.Swap(&other->taskid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackStatusRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackStatusRequest_descriptor_;
  metadata.reflection = PlaybackStatusRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackStatusRequest

// optional string taskId = 1;
void PlaybackStatusRequest::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackStatusRequest::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackStatusRequest::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
void PlaybackStatusRequest::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
void PlaybackStatusRequest::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}
::std::string* PlaybackStatusRequest::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackStatusRequest::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackStatusRequest::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackStatusRequest.taskId)
}

inline const PlaybackStatusRequest* PlaybackStatusRequest::internal_default_instance() {
  return &PlaybackStatusRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackStatus::kTaskIdFieldNumber;
const int PlaybackStatus::kTaskStatusFieldNumber;
const int PlaybackStatus::kReplayPercentFieldNumber;
const int PlaybackStatus::kCurrentReplayRateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackStatus::PlaybackStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackStatus)
}

void PlaybackStatus::InitAsDefaultInstance() {
}

PlaybackStatus::PlaybackStatus(const PlaybackStatus& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackStatus)
}

void PlaybackStatus::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&taskstatus_, 0, reinterpret_cast<char*>(&currentreplayrate_) -
    reinterpret_cast<char*>(&taskstatus_) + sizeof(currentreplayrate_));
  _cached_size_ = 0;
}

PlaybackStatus::~PlaybackStatus() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackStatus)
  SharedDtor();
}

void PlaybackStatus::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void PlaybackStatus::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackStatus::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackStatus_descriptor_;
}

const PlaybackStatus& PlaybackStatus::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackStatus> PlaybackStatus_default_instance_;

PlaybackStatus* PlaybackStatus::New(::google::protobuf::Arena* arena) const {
  PlaybackStatus* n = new PlaybackStatus;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackStatus)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(PlaybackStatus, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<PlaybackStatus*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(taskstatus_, currentreplayrate_);
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool PlaybackStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackStatus)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackStatus.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_taskStatus;
        break;
      }

      // optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
      case 2: {
        if (tag == 16) {
         parse_taskStatus:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_taskstatus(static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskStatus >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_replayPercent;
        break;
      }

      // optional int32 replayPercent = 3;
      case 3: {
        if (tag == 24) {
         parse_replayPercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &replaypercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_currentReplayRate;
        break;
      }

      // optional int32 currentReplayRate = 4;
      case 4: {
        if (tag == 32) {
         parse_currentReplayRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &currentreplayrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackStatus)
  return false;
#undef DO_
}

void PlaybackStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackStatus)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackStatus.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
  if (this->taskstatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->taskstatus(), output);
  }

  // optional int32 replayPercent = 3;
  if (this->replaypercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->replaypercent(), output);
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->currentreplayrate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackStatus)
}

::google::protobuf::uint8* PlaybackStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackStatus)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackStatus.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
  if (this->taskstatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->taskstatus(), target);
  }

  // optional int32 replayPercent = 3;
  if (this->replaypercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->replaypercent(), target);
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->currentreplayrate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackStatus)
  return target;
}

size_t PlaybackStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackStatus)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
  if (this->taskstatus() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->taskstatus());
  }

  // optional int32 replayPercent = 3;
  if (this->replaypercent() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->replaypercent());
  }

  // optional int32 currentReplayRate = 4;
  if (this->currentreplayrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->currentreplayrate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackStatus)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackStatus)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackStatus::MergeFrom(const PlaybackStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackStatus)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackStatus::UnsafeMergeFrom(const PlaybackStatus& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.taskstatus() != 0) {
    set_taskstatus(from.taskstatus());
  }
  if (from.replaypercent() != 0) {
    set_replaypercent(from.replaypercent());
  }
  if (from.currentreplayrate() != 0) {
    set_currentreplayrate(from.currentreplayrate());
  }
}

void PlaybackStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackStatus::CopyFrom(const PlaybackStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackStatus)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackStatus::IsInitialized() const {

  return true;
}

void PlaybackStatus::Swap(PlaybackStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackStatus::InternalSwap(PlaybackStatus* other) {
  taskid_.Swap(&other->taskid_);
  std::swap(taskstatus_, other->taskstatus_);
  std::swap(replaypercent_, other->replaypercent_);
  std::swap(currentreplayrate_, other->currentreplayrate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackStatus::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackStatus_descriptor_;
  metadata.reflection = PlaybackStatus_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackStatus

// optional string taskId = 1;
void PlaybackStatus::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackStatus::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackStatus::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
void PlaybackStatus::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
void PlaybackStatus::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}
::std::string* PlaybackStatus::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackStatus::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackStatus::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackStatus.taskId)
}

// optional .com.htsc.mdc.insight.model.EPlaybackTaskStatus taskStatus = 2;
void PlaybackStatus::clear_taskstatus() {
  taskstatus_ = 0;
}
::com::htsc::mdc::insight::model::EPlaybackTaskStatus PlaybackStatus::taskstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.taskStatus)
  return static_cast< ::com::htsc::mdc::insight::model::EPlaybackTaskStatus >(taskstatus_);
}
void PlaybackStatus::set_taskstatus(::com::htsc::mdc::insight::model::EPlaybackTaskStatus value) {
  
  taskstatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.taskStatus)
}

// optional int32 replayPercent = 3;
void PlaybackStatus::clear_replaypercent() {
  replaypercent_ = 0;
}
::google::protobuf::int32 PlaybackStatus::replaypercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.replayPercent)
  return replaypercent_;
}
void PlaybackStatus::set_replaypercent(::google::protobuf::int32 value) {
  
  replaypercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.replayPercent)
}

// optional int32 currentReplayRate = 4;
void PlaybackStatus::clear_currentreplayrate() {
  currentreplayrate_ = 0;
}
::google::protobuf::int32 PlaybackStatus::currentreplayrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackStatus.currentReplayRate)
  return currentreplayrate_;
}
void PlaybackStatus::set_currentreplayrate(::google::protobuf::int32 value) {
  
  currentreplayrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackStatus.currentReplayRate)
}

inline const PlaybackStatus* PlaybackStatus::internal_default_instance() {
  return &PlaybackStatus_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PlaybackPayload::kTaskIdFieldNumber;
const int PlaybackPayload::kMarketDataStreamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PlaybackPayload::PlaybackPayload()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDPlayback_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.PlaybackPayload)
}

void PlaybackPayload::InitAsDefaultInstance() {
  marketdatastream_ = const_cast< ::com::htsc::mdc::insight::model::MarketDataStream*>(
      ::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance());
}

PlaybackPayload::PlaybackPayload(const PlaybackPayload& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.PlaybackPayload)
}

void PlaybackPayload::SharedCtor() {
  taskid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketdatastream_ = NULL;
  _cached_size_ = 0;
}

PlaybackPayload::~PlaybackPayload() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.PlaybackPayload)
  SharedDtor();
}

void PlaybackPayload::SharedDtor() {
  taskid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &PlaybackPayload_default_instance_.get()) {
    delete marketdatastream_;
  }
}

void PlaybackPayload::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PlaybackPayload::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return PlaybackPayload_descriptor_;
}

const PlaybackPayload& PlaybackPayload::default_instance() {
  protobuf_InitDefaults_MDPlayback_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<PlaybackPayload> PlaybackPayload_default_instance_;

PlaybackPayload* PlaybackPayload::New(::google::protobuf::Arena* arena) const {
  PlaybackPayload* n = new PlaybackPayload;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PlaybackPayload::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.PlaybackPayload)
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;
}

bool PlaybackPayload::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.PlaybackPayload)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string taskId = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_taskid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->taskid().data(), this->taskid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.PlaybackPayload.taskId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_marketDataStream;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
      case 2: {
        if (tag == 18) {
         parse_marketDataStream:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_marketdatastream()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.PlaybackPayload)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.PlaybackPayload)
  return false;
#undef DO_
}

void PlaybackPayload::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.PlaybackPayload)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackPayload.taskId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->taskid(), output);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
  if (this->has_marketdatastream()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->marketdatastream_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.PlaybackPayload)
}

::google::protobuf::uint8* PlaybackPayload::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.PlaybackPayload)
  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->taskid().data(), this->taskid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.PlaybackPayload.taskId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->taskid(), target);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
  if (this->has_marketdatastream()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->marketdatastream_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.PlaybackPayload)
  return target;
}

size_t PlaybackPayload::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.PlaybackPayload)
  size_t total_size = 0;

  // optional string taskId = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->taskid());
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
  if (this->has_marketdatastream()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->marketdatastream_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PlaybackPayload::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.PlaybackPayload)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const PlaybackPayload* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PlaybackPayload>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.PlaybackPayload)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.PlaybackPayload)
    UnsafeMergeFrom(*source);
  }
}

void PlaybackPayload::MergeFrom(const PlaybackPayload& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.PlaybackPayload)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void PlaybackPayload::UnsafeMergeFrom(const PlaybackPayload& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.taskid().size() > 0) {

    taskid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.taskid_);
  }
  if (from.has_marketdatastream()) {
    mutable_marketdatastream()->::com::htsc::mdc::insight::model::MarketDataStream::MergeFrom(from.marketdatastream());
  }
}

void PlaybackPayload::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.PlaybackPayload)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaybackPayload::CopyFrom(const PlaybackPayload& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.PlaybackPayload)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool PlaybackPayload::IsInitialized() const {

  return true;
}

void PlaybackPayload::Swap(PlaybackPayload* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PlaybackPayload::InternalSwap(PlaybackPayload* other) {
  taskid_.Swap(&other->taskid_);
  std::swap(marketdatastream_, other->marketdatastream_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PlaybackPayload::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = PlaybackPayload_descriptor_;
  metadata.reflection = PlaybackPayload_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// PlaybackPayload

// optional string taskId = 1;
void PlaybackPayload::clear_taskid() {
  taskid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& PlaybackPayload::taskid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  return taskid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackPayload::set_taskid(const ::std::string& value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
void PlaybackPayload::set_taskid(const char* value) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
void PlaybackPayload::set_taskid(const char* value, size_t size) {
  
  taskid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}
::std::string* PlaybackPayload::mutable_taskid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  return taskid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* PlaybackPayload::release_taskid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
  
  return taskid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void PlaybackPayload::set_allocated_taskid(::std::string* taskid) {
  if (taskid != NULL) {
    
  } else {
    
  }
  taskid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), taskid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackPayload.taskId)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 2;
bool PlaybackPayload::has_marketdatastream() const {
  return this != internal_default_instance() && marketdatastream_ != NULL;
}
void PlaybackPayload::clear_marketdatastream() {
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;
}
const ::com::htsc::mdc::insight::model::MarketDataStream& PlaybackPayload::marketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  return marketdatastream_ != NULL ? *marketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
::com::htsc::mdc::insight::model::MarketDataStream* PlaybackPayload::mutable_marketdatastream() {
  
  if (marketdatastream_ == NULL) {
    marketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  return marketdatastream_;
}
::com::htsc::mdc::insight::model::MarketDataStream* PlaybackPayload::release_marketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = marketdatastream_;
  marketdatastream_ = NULL;
  return temp;
}
void PlaybackPayload::set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream) {
  delete marketdatastream_;
  marketdatastream_ = marketdatastream;
  if (marketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.PlaybackPayload.marketDataStream)
}

inline const PlaybackPayload* PlaybackPayload::internal_default_instance() {
  return &PlaybackPayload_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
