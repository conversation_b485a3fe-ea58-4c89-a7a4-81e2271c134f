// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDHKGreyMarket.proto

#ifndef PROTOBUF_MDHKGreyMarket_2eproto__INCLUDED
#define PROTOBUF_MDHKGreyMarket_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDHKGreyMarket_2eproto();
void protobuf_InitDefaults_MDHKGreyMarket_2eproto();
void protobuf_AssignDesc_MDHKGreyMarket_2eproto();
void protobuf_ShutdownFile_MDHKGreyMarket_2eproto();

class MDHKGreyMarket;
class MDHKGreyMarket_MarketEntry;
class MDHKGreyMarket_OrderEntry;
class MDHKGreyMarket_TradeEntry;

// ===================================================================

class MDHKGreyMarket_MarketEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry) */ {
 public:
  MDHKGreyMarket_MarketEntry();
  virtual ~MDHKGreyMarket_MarketEntry();

  MDHKGreyMarket_MarketEntry(const MDHKGreyMarket_MarketEntry& from);

  inline MDHKGreyMarket_MarketEntry& operator=(const MDHKGreyMarket_MarketEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDHKGreyMarket_MarketEntry& default_instance();

  static const MDHKGreyMarket_MarketEntry* internal_default_instance();

  void Swap(MDHKGreyMarket_MarketEntry* other);

  // implements Message ----------------------------------------------

  inline MDHKGreyMarket_MarketEntry* New() const { return New(NULL); }

  MDHKGreyMarket_MarketEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDHKGreyMarket_MarketEntry& from);
  void MergeFrom(const MDHKGreyMarket_MarketEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDHKGreyMarket_MarketEntry* other);
  void UnsafeMergeFrom(const MDHKGreyMarket_MarketEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 ChannelType = 1;
  void clear_channeltype();
  static const int kChannelTypeFieldNumber = 1;
  ::google::protobuf::int32 channeltype() const;
  void set_channeltype(::google::protobuf::int32 value);

  // optional bool ValidFlag = 2;
  void clear_validflag();
  static const int kValidFlagFieldNumber = 2;
  bool validflag() const;
  void set_validflag(bool value);

  // optional int64 PreClosePx = 3;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 3;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 4;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 4;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 5;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 5;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 6;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 6;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 7;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 7;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 8;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 8;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 9;
  void clear_closepx();
  static const int kClosePxFieldNumber = 9;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 10;
  void clear_highpx();
  static const int kHighPxFieldNumber = 10;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 11;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 11;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
  int buyorderentries_size() const;
  void clear_buyorderentries();
  static const int kBuyOrderEntriesFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& buyorderentries(int index) const;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* mutable_buyorderentries(int index);
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* add_buyorderentries();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
      mutable_buyorderentries();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
      buyorderentries() const;

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
  int sellorderentries_size() const;
  void clear_sellorderentries();
  static const int kSellOrderEntriesFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& sellorderentries(int index) const;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* mutable_sellorderentries(int index);
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* add_sellorderentries();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
      mutable_sellorderentries();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
      sellorderentries() const;

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
  int tradeentries_size() const;
  void clear_tradeentries();
  static const int kTradeEntriesFieldNumber = 14;
  const ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry& tradeentries(int index) const;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* mutable_tradeentries(int index);
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* add_tradeentries();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >*
      mutable_tradeentries();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >&
      tradeentries() const;

  // optional int64 ExchangeDateTime = 15;
  void clear_exchangedatetime();
  static const int kExchangeDateTimeFieldNumber = 15;
  ::google::protobuf::int64 exchangedatetime() const;
  void set_exchangedatetime(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry > buyorderentries_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry > sellorderentries_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry > tradeentries_;
  ::google::protobuf::int32 channeltype_;
  bool validflag_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 exchangedatetime_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl();
  friend void  protobuf_AddDesc_MDHKGreyMarket_2eproto_impl();
  friend void protobuf_AssignDesc_MDHKGreyMarket_2eproto();
  friend void protobuf_ShutdownFile_MDHKGreyMarket_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_MarketEntry> MDHKGreyMarket_MarketEntry_default_instance_;

// -------------------------------------------------------------------

class MDHKGreyMarket_OrderEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry) */ {
 public:
  MDHKGreyMarket_OrderEntry();
  virtual ~MDHKGreyMarket_OrderEntry();

  MDHKGreyMarket_OrderEntry(const MDHKGreyMarket_OrderEntry& from);

  inline MDHKGreyMarket_OrderEntry& operator=(const MDHKGreyMarket_OrderEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDHKGreyMarket_OrderEntry& default_instance();

  static const MDHKGreyMarket_OrderEntry* internal_default_instance();

  void Swap(MDHKGreyMarket_OrderEntry* other);

  // implements Message ----------------------------------------------

  inline MDHKGreyMarket_OrderEntry* New() const { return New(NULL); }

  MDHKGreyMarket_OrderEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDHKGreyMarket_OrderEntry& from);
  void MergeFrom(const MDHKGreyMarket_OrderEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDHKGreyMarket_OrderEntry* other);
  void UnsafeMergeFrom(const MDHKGreyMarket_OrderEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 OrderLevel = 1;
  void clear_orderlevel();
  static const int kOrderLevelFieldNumber = 1;
  ::google::protobuf::int32 orderlevel() const;
  void set_orderlevel(::google::protobuf::int32 value);

  // optional int64 OrderPrice = 2;
  void clear_orderprice();
  static const int kOrderPriceFieldNumber = 2;
  ::google::protobuf::int64 orderprice() const;
  void set_orderprice(::google::protobuf::int64 value);

  // optional int64 OrderQty = 3;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 3;
  ::google::protobuf::int64 orderqty() const;
  void set_orderqty(::google::protobuf::int64 value);

  // optional int64 NumOrders = 4;
  void clear_numorders();
  static const int kNumOrdersFieldNumber = 4;
  ::google::protobuf::int64 numorders() const;
  void set_numorders(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 orderprice_;
  ::google::protobuf::int64 orderqty_;
  ::google::protobuf::int64 numorders_;
  ::google::protobuf::int32 orderlevel_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl();
  friend void  protobuf_AddDesc_MDHKGreyMarket_2eproto_impl();
  friend void protobuf_AssignDesc_MDHKGreyMarket_2eproto();
  friend void protobuf_ShutdownFile_MDHKGreyMarket_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_OrderEntry> MDHKGreyMarket_OrderEntry_default_instance_;

// -------------------------------------------------------------------

class MDHKGreyMarket_TradeEntry : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry) */ {
 public:
  MDHKGreyMarket_TradeEntry();
  virtual ~MDHKGreyMarket_TradeEntry();

  MDHKGreyMarket_TradeEntry(const MDHKGreyMarket_TradeEntry& from);

  inline MDHKGreyMarket_TradeEntry& operator=(const MDHKGreyMarket_TradeEntry& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDHKGreyMarket_TradeEntry& default_instance();

  static const MDHKGreyMarket_TradeEntry* internal_default_instance();

  void Swap(MDHKGreyMarket_TradeEntry* other);

  // implements Message ----------------------------------------------

  inline MDHKGreyMarket_TradeEntry* New() const { return New(NULL); }

  MDHKGreyMarket_TradeEntry* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDHKGreyMarket_TradeEntry& from);
  void MergeFrom(const MDHKGreyMarket_TradeEntry& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDHKGreyMarket_TradeEntry* other);
  void UnsafeMergeFrom(const MDHKGreyMarket_TradeEntry& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeLevel = 1;
  void clear_tradelevel();
  static const int kTradeLevelFieldNumber = 1;
  ::google::protobuf::int32 tradelevel() const;
  void set_tradelevel(::google::protobuf::int32 value);

  // optional int64 TradePrice = 2;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 2;
  ::google::protobuf::int64 tradeprice() const;
  void set_tradeprice(::google::protobuf::int64 value);

  // optional int64 TradeQty = 3;
  void clear_tradeqty();
  static const int kTradeQtyFieldNumber = 3;
  ::google::protobuf::int64 tradeqty() const;
  void set_tradeqty(::google::protobuf::int64 value);

  // optional int64 TradeTime = 4;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 4;
  ::google::protobuf::int64 tradetime() const;
  void set_tradetime(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 tradeprice_;
  ::google::protobuf::int64 tradeqty_;
  ::google::protobuf::int64 tradetime_;
  ::google::protobuf::int32 tradelevel_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl();
  friend void  protobuf_AddDesc_MDHKGreyMarket_2eproto_impl();
  friend void protobuf_AssignDesc_MDHKGreyMarket_2eproto();
  friend void protobuf_ShutdownFile_MDHKGreyMarket_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket_TradeEntry> MDHKGreyMarket_TradeEntry_default_instance_;

// -------------------------------------------------------------------

class MDHKGreyMarket : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDHKGreyMarket) */ {
 public:
  MDHKGreyMarket();
  virtual ~MDHKGreyMarket();

  MDHKGreyMarket(const MDHKGreyMarket& from);

  inline MDHKGreyMarket& operator=(const MDHKGreyMarket& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDHKGreyMarket& default_instance();

  static const MDHKGreyMarket* internal_default_instance();

  void Swap(MDHKGreyMarket* other);

  // implements Message ----------------------------------------------

  inline MDHKGreyMarket* New() const { return New(NULL); }

  MDHKGreyMarket* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDHKGreyMarket& from);
  void MergeFrom(const MDHKGreyMarket& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDHKGreyMarket* other);
  void UnsafeMergeFrom(const MDHKGreyMarket& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  typedef MDHKGreyMarket_MarketEntry MarketEntry;
  typedef MDHKGreyMarket_OrderEntry OrderEntry;
  typedef MDHKGreyMarket_TradeEntry TradeEntry;

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 ApplSeqNum = 8;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 8;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
  int marketentries_size() const;
  void clear_marketentries();
  static const int kMarketEntriesFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry& marketentries(int index) const;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* mutable_marketentries(int index);
  ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* add_marketentries();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >*
      mutable_marketentries();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >&
      marketentries() const;

  // optional int32 DataMultiplePowerOf10 = 10;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 10;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDHKGreyMarket)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry > marketentries_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDHKGreyMarket_2eproto_impl();
  friend void  protobuf_AddDesc_MDHKGreyMarket_2eproto_impl();
  friend void protobuf_AssignDesc_MDHKGreyMarket_2eproto();
  friend void protobuf_ShutdownFile_MDHKGreyMarket_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDHKGreyMarket> MDHKGreyMarket_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDHKGreyMarket_MarketEntry

// optional int32 ChannelType = 1;
inline void MDHKGreyMarket_MarketEntry::clear_channeltype() {
  channeltype_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket_MarketEntry::channeltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ChannelType)
  return channeltype_;
}
inline void MDHKGreyMarket_MarketEntry::set_channeltype(::google::protobuf::int32 value) {
  
  channeltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ChannelType)
}

// optional bool ValidFlag = 2;
inline void MDHKGreyMarket_MarketEntry::clear_validflag() {
  validflag_ = false;
}
inline bool MDHKGreyMarket_MarketEntry::validflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ValidFlag)
  return validflag_;
}
inline void MDHKGreyMarket_MarketEntry::set_validflag(bool value) {
  
  validflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ValidFlag)
}

// optional int64 PreClosePx = 3;
inline void MDHKGreyMarket_MarketEntry::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.PreClosePx)
  return preclosepx_;
}
inline void MDHKGreyMarket_MarketEntry::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.PreClosePx)
}

// optional int64 NumTrades = 4;
inline void MDHKGreyMarket_MarketEntry::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.NumTrades)
  return numtrades_;
}
inline void MDHKGreyMarket_MarketEntry::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.NumTrades)
}

// optional int64 TotalVolumeTrade = 5;
inline void MDHKGreyMarket_MarketEntry::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDHKGreyMarket_MarketEntry::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 6;
inline void MDHKGreyMarket_MarketEntry::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDHKGreyMarket_MarketEntry::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TotalValueTrade)
}

// optional int64 LastPx = 7;
inline void MDHKGreyMarket_MarketEntry::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LastPx)
  return lastpx_;
}
inline void MDHKGreyMarket_MarketEntry::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LastPx)
}

// optional int64 OpenPx = 8;
inline void MDHKGreyMarket_MarketEntry::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.OpenPx)
  return openpx_;
}
inline void MDHKGreyMarket_MarketEntry::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.OpenPx)
}

// optional int64 ClosePx = 9;
inline void MDHKGreyMarket_MarketEntry::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ClosePx)
  return closepx_;
}
inline void MDHKGreyMarket_MarketEntry::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ClosePx)
}

// optional int64 HighPx = 10;
inline void MDHKGreyMarket_MarketEntry::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.HighPx)
  return highpx_;
}
inline void MDHKGreyMarket_MarketEntry::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.HighPx)
}

// optional int64 LowPx = 11;
inline void MDHKGreyMarket_MarketEntry::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LowPx)
  return lowpx_;
}
inline void MDHKGreyMarket_MarketEntry::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.LowPx)
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry BuyOrderEntries = 12;
inline int MDHKGreyMarket_MarketEntry::buyorderentries_size() const {
  return buyorderentries_.size();
}
inline void MDHKGreyMarket_MarketEntry::clear_buyorderentries() {
  buyorderentries_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& MDHKGreyMarket_MarketEntry::buyorderentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::mutable_buyorderentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::add_buyorderentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
MDHKGreyMarket_MarketEntry::mutable_buyorderentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return &buyorderentries_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
MDHKGreyMarket_MarketEntry::buyorderentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.BuyOrderEntries)
  return buyorderentries_;
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry SellOrderEntries = 13;
inline int MDHKGreyMarket_MarketEntry::sellorderentries_size() const {
  return sellorderentries_.size();
}
inline void MDHKGreyMarket_MarketEntry::clear_sellorderentries() {
  sellorderentries_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry& MDHKGreyMarket_MarketEntry::sellorderentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::mutable_sellorderentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry* MDHKGreyMarket_MarketEntry::add_sellorderentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >*
MDHKGreyMarket_MarketEntry::mutable_sellorderentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return &sellorderentries_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_OrderEntry >&
MDHKGreyMarket_MarketEntry::sellorderentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.SellOrderEntries)
  return sellorderentries_;
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry TradeEntries = 14;
inline int MDHKGreyMarket_MarketEntry::tradeentries_size() const {
  return tradeentries_.size();
}
inline void MDHKGreyMarket_MarketEntry::clear_tradeentries() {
  tradeentries_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry& MDHKGreyMarket_MarketEntry::tradeentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* MDHKGreyMarket_MarketEntry::mutable_tradeentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry* MDHKGreyMarket_MarketEntry::add_tradeentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >*
MDHKGreyMarket_MarketEntry::mutable_tradeentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return &tradeentries_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_TradeEntry >&
MDHKGreyMarket_MarketEntry::tradeentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.TradeEntries)
  return tradeentries_;
}

// optional int64 ExchangeDateTime = 15;
inline void MDHKGreyMarket_MarketEntry::clear_exchangedatetime() {
  exchangedatetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_MarketEntry::exchangedatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ExchangeDateTime)
  return exchangedatetime_;
}
inline void MDHKGreyMarket_MarketEntry::set_exchangedatetime(::google::protobuf::int64 value) {
  
  exchangedatetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry.ExchangeDateTime)
}

inline const MDHKGreyMarket_MarketEntry* MDHKGreyMarket_MarketEntry::internal_default_instance() {
  return &MDHKGreyMarket_MarketEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket_OrderEntry

// optional int32 OrderLevel = 1;
inline void MDHKGreyMarket_OrderEntry::clear_orderlevel() {
  orderlevel_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket_OrderEntry::orderlevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderLevel)
  return orderlevel_;
}
inline void MDHKGreyMarket_OrderEntry::set_orderlevel(::google::protobuf::int32 value) {
  
  orderlevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderLevel)
}

// optional int64 OrderPrice = 2;
inline void MDHKGreyMarket_OrderEntry::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_OrderEntry::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderPrice)
  return orderprice_;
}
inline void MDHKGreyMarket_OrderEntry::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderPrice)
}

// optional int64 OrderQty = 3;
inline void MDHKGreyMarket_OrderEntry::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_OrderEntry::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderQty)
  return orderqty_;
}
inline void MDHKGreyMarket_OrderEntry::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.OrderQty)
}

// optional int64 NumOrders = 4;
inline void MDHKGreyMarket_OrderEntry::clear_numorders() {
  numorders_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_OrderEntry::numorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.NumOrders)
  return numorders_;
}
inline void MDHKGreyMarket_OrderEntry::set_numorders(::google::protobuf::int64 value) {
  
  numorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.OrderEntry.NumOrders)
}

inline const MDHKGreyMarket_OrderEntry* MDHKGreyMarket_OrderEntry::internal_default_instance() {
  return &MDHKGreyMarket_OrderEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket_TradeEntry

// optional int32 TradeLevel = 1;
inline void MDHKGreyMarket_TradeEntry::clear_tradelevel() {
  tradelevel_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket_TradeEntry::tradelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeLevel)
  return tradelevel_;
}
inline void MDHKGreyMarket_TradeEntry::set_tradelevel(::google::protobuf::int32 value) {
  
  tradelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeLevel)
}

// optional int64 TradePrice = 2;
inline void MDHKGreyMarket_TradeEntry::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradePrice)
  return tradeprice_;
}
inline void MDHKGreyMarket_TradeEntry::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradePrice)
}

// optional int64 TradeQty = 3;
inline void MDHKGreyMarket_TradeEntry::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeQty)
  return tradeqty_;
}
inline void MDHKGreyMarket_TradeEntry::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeQty)
}

// optional int64 TradeTime = 4;
inline void MDHKGreyMarket_TradeEntry::clear_tradetime() {
  tradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket_TradeEntry::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeTime)
  return tradetime_;
}
inline void MDHKGreyMarket_TradeEntry::set_tradetime(::google::protobuf::int64 value) {
  
  tradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradeEntry.TradeTime)
}

inline const MDHKGreyMarket_TradeEntry* MDHKGreyMarket_TradeEntry::internal_default_instance() {
  return &MDHKGreyMarket_TradeEntry_default_instance_.get();
}
// -------------------------------------------------------------------

// MDHKGreyMarket

// optional string HTSCSecurityID = 1;
inline void MDHKGreyMarket::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDHKGreyMarket::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDHKGreyMarket::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
inline void MDHKGreyMarket::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
inline void MDHKGreyMarket::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}
inline ::std::string* MDHKGreyMarket::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDHKGreyMarket::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDHKGreyMarket::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHKGreyMarket.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDHKGreyMarket::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MDDate)
  return mddate_;
}
inline void MDHKGreyMarket::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MDDate)
}

// optional int32 MDTime = 3;
inline void MDHKGreyMarket::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MDTime)
  return mdtime_;
}
inline void MDHKGreyMarket::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDHKGreyMarket::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.DataTimestamp)
  return datatimestamp_;
}
inline void MDHKGreyMarket::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDHKGreyMarket::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDHKGreyMarket::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDHKGreyMarket::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
inline void MDHKGreyMarket::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
inline void MDHKGreyMarket::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}
inline ::std::string* MDHKGreyMarket::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDHKGreyMarket::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDHKGreyMarket::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHKGreyMarket.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDHKGreyMarket::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDHKGreyMarket::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDHKGreyMarket::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDHKGreyMarket::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDHKGreyMarket::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDHKGreyMarket::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.securityType)
}

// optional int64 ApplSeqNum = 8;
inline void MDHKGreyMarket::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDHKGreyMarket::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.ApplSeqNum)
  return applseqnum_;
}
inline void MDHKGreyMarket::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.ApplSeqNum)
}

// repeated .com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntry MarketEntries = 9;
inline int MDHKGreyMarket::marketentries_size() const {
  return marketentries_.size();
}
inline void MDHKGreyMarket::clear_marketentries() {
  marketentries_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry& MDHKGreyMarket::marketentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* MDHKGreyMarket::mutable_marketentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry* MDHKGreyMarket::add_marketentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >*
MDHKGreyMarket::mutable_marketentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return &marketentries_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDHKGreyMarket_MarketEntry >&
MDHKGreyMarket::marketentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHKGreyMarket.MarketEntries)
  return marketentries_;
}

// optional int32 DataMultiplePowerOf10 = 10;
inline void MDHKGreyMarket::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDHKGreyMarket::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHKGreyMarket.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDHKGreyMarket::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHKGreyMarket.DataMultiplePowerOf10)
}

inline const MDHKGreyMarket* MDHKGreyMarket::internal_default_instance() {
  return &MDHKGreyMarket_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDHKGreyMarket_2eproto__INCLUDED
