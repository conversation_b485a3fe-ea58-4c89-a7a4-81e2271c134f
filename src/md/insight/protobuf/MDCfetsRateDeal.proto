syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsRateDeal {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  int32 DealType = 16;
  RateDealDetail RateDealDetail = 17;
  int64 MessageNumber = 100;
}

message RateDealDetail {
  string DealID = 1;
  string TradeMethod = 2;
  string TradeTerm = 3;
  double DealRate = 4;
  double DealVolume = 5;
  string Counterparty = 6;
  string DealStatus = 7;
  string DealTime = 8;
  string SettlementDate = 9;
}
