// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDIceTrace.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDIceTrace.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDIceTrace_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDIceTrace_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDIceTrace_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDIceTrace_2eproto() {
  protobuf_AddDesc_MDIceTrace_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDIceTrace.proto");
  GOOGLE_CHECK(file != NULL);
  MDIceTrace_descriptor_ = file->message_type(0);
  static const int MDIceTrace_offsets_[138] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, currentprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, activitydatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradedatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradevol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, exchseq_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradeindicsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, exchmessagetimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewtradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewtradesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, enumcurrency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, maturitydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, marketphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, chg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, pctchg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, recorddel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, currencystring_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, currentdatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradeofficialdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradecondprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradecondsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradeofficialtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradeuniqueid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, updateaction_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, yesttradevol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, vwap_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradecondofficialtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradecondofficialdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionofficialtradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionofficialtradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, transactionpriceind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, couponrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, instrstatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, msgtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, controlmsgtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, weightedaveragematurity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcancelsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcorrectionsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcancelprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcorrectionprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, actiontime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, actiondate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, reasoncode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradeidentifier_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaloriginalmessagedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, extendedtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, yieldhigh_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, yieldlow_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcanceltradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradeindicsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalextendedtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalpriceindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, symbolsuffix_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicaltradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevspecialpriceindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewextendedtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevextendedtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewtradecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, canceltradeseq_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, prevtransactionpriceind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewtransactionpriceind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, originaltradeseq_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, yieldclose_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, firstsettlementdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, reportdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, marketphase_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, disseminationdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionnewdisseminationdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevdisseminationdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradecond1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctionprevtradeuniqueid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcorrectionprevtradeuniqueid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcanceltradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcorrectiontradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, historicalcorrectiontradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, bondtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, yield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, cusip_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, symbolexchticker_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, isin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, instrname2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tradesettlementdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, firsttradingdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, coupontype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, enuminterestcalctype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, issuername_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, investmentgrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, amortizationtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, disseminationflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, symbolbloombergticker_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, bloombergglobalid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, bloombergglobalidcomp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, instrlocaltype2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, symbolesignalticker_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, instrnamelocal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, mktsegmentstring_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, exchmonthcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, poolnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, masterdealid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, trancheid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, indicator144a_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, numbermaturitymonths_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, debttypecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, tokendel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, correctiontradeseq_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, recordstaleind_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, weightedaveragecoupon_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, weightedaverageloanage_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, weightedaverageloansize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, weightedloanvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, averagemonthlysize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, prevmonthvoldec_),
  };
  MDIceTrace_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDIceTrace_descriptor_,
      MDIceTrace::internal_default_instance(),
      MDIceTrace_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDIceTrace),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDIceTrace, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDIceTrace_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDIceTrace_descriptor_, MDIceTrace::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDIceTrace_2eproto() {
  MDIceTrace_default_instance_.Shutdown();
  delete MDIceTrace_reflection_;
}

void protobuf_InitDefaults_MDIceTrace_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDIceTrace_default_instance_.DefaultConstruct();
  MDIceTrace_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDIceTrace_2eproto_once_);
void protobuf_InitDefaults_MDIceTrace_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDIceTrace_2eproto_once_,
                 &protobuf_InitDefaults_MDIceTrace_2eproto_impl);
}
void protobuf_AddDesc_MDIceTrace_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDIceTrace_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MDIceTrace.proto\022\032com.htsc.mdc.insight"
    ".model\032\027ESecurityIDSource.proto\032\023ESecuri"
    "tyType.proto\"\255\035\n\nMDIceTrace\022\016\n\006MDDate\030\001 "
    "\001(\005\022\016\n\006MDTime\030\002 \001(\005\0227\n\014SecurityType\030\003 \001("
    "\0162!.com.htsc.mdc.model.ESecurityType\022\?\n\020"
    "SecurityIDSource\030\004 \001(\0162%.com.htsc.mdc.mo"
    "del.ESecurityIDSource\022\026\n\016HTSCSecurityID\030"
    "\005 \001(\t\022\024\n\014ExchangeDate\030\006 \001(\005\022\024\n\014ExchangeT"
    "ime\030\007 \001(\005\022\035\n\025DataMultiplePowerOf10\030\010 \001(\005"
    "\022\022\n\nTradePrice\030\023 \001(\001\022\021\n\tTradeSize\030\024 \001(\003\022"
    "\024\n\014CurrentPrice\030\025 \001(\001\022\030\n\020ActivityDatetim"
    "e\030\026 \001(\t\022\025\n\rTradeDatetime\030\027 \001(\t\022\020\n\010TradeV"
    "ol\030\030 \001(\003\022\017\n\007ExchSeq\030\032 \001(\003\022\026\n\016TradeIndicS"
    "ize\030\033 \001(\001\022\034\n\024ExchMessageTimestamp\030\034 \001(\003\022"
    "\037\n\027CorrectionNewTradePrice\030\036 \001(\001\022\036\n\026Corr"
    "ectionNewTradeSize\030\037 \001(\003\022 \n\030CorrectionPr"
    "evTradePrice\030  \001(\001\022\037\n\027CorrectionPrevTrad"
    "eSize\030! \001(\003\022\024\n\014EnumCurrency\030\" \001(\t\022\024\n\014Mat"
    "urityDate\030# \001(\003\022\027\n\017MarketPhaseCode\030$ \001(\005"
    "\022\013\n\003Chg\030% \001(\001\022\016\n\006PctChg\030& \001(\001\022\017\n\007ClosePx"
    "\030\' \001(\001\022\016\n\006HighPx\030( \001(\001\022\r\n\005LowPx\030) \001(\001\022\016\n"
    "\006OpenPx\030* \001(\001\022\022\n\nPreClosePx\030+ \001(\001\022\021\n\tRec"
    "ordDel\030- \001(\010\022\026\n\016CurrencyString\030. \001(\t\022\027\n\017"
    "CurrentDatetime\030/ \001(\t\022\031\n\021TradeOfficialDa"
    "te\0300 \001(\003\022\026\n\016TradeCondPrice\0301 \001(\001\022\025\n\rTrad"
    "eCondSize\0302 \001(\003\022\031\n\021TradeOfficialTime\0303 \001"
    "(\003\022\025\n\rTradeUniqueId\0304 \001(\003\022\024\n\014UpdateActio"
    "n\0305 \001(\003\022\024\n\014YestTradeVol\0306 \001(\003\022\014\n\004Vwap\0307 "
    "\001(\001\022\035\n\025TradeCondOfficialTime\0308 \001(\003\022\035\n\025Tr"
    "adeCondOfficialDate\0309 \001(\003\022#\n\033CorrectionO"
    "fficialTradeTime\030: \001(\003\022#\n\033CorrectionOffi"
    "cialTradeDate\030; \001(\003\022\033\n\023TransactionPriceI"
    "nd\030< \001(\005\022\022\n\nCouponRate\030= \001(\001\022\023\n\013InstrSta"
    "tus\030> \001(\005\022\017\n\007MsgType\030\? \001(\005\022\026\n\016ControlMsg"
    "Type\030@ \001(\005\022\037\n\027WeightedAverageMaturity\030A "
    "\001(\003\022\033\n\023HistoricalTradeSize\030B \001(\003\022\034\n\024Hist"
    "oricalCancelSize\030C \001(\003\022 \n\030HistoricalCorr"
    "ectionSize\030D \001(\003\022\033\n\023HistoricalTradeCond\030"
    "E \001(\003\022\034\n\024HistoricalTradePrice\030F \001(\001\022\035\n\025H"
    "istoricalCancelPrice\030G \001(\001\022!\n\031Historical"
    "CorrectionPrice\030H \001(\001\022\022\n\nActionTime\030I \001("
    "\003\022\022\n\nActionDate\030J \001(\003\022\022\n\nReasonCode\030K \001("
    "\005\022!\n\031HistoricalTradeIdentifier\030L \001(\003\022%\n\035"
    "HistoricalOriginalMessageDate\030M \001(\003\022\031\n\021E"
    "xtendedTradeCond\030N \001(\003\022\021\n\tYieldHigh\030O \001("
    "\001\022\020\n\010YieldLow\030P \001(\001\022!\n\031HistoricalCancelT"
    "radeDate\030Q \001(\003\022\027\n\017HistoricalYield\030R \001(\001\022"
    " \n\030HistoricalTradeIndicSize\030S \001(\001\022#\n\033His"
    "toricalExtendedTradeCond\030T \001(\003\022 \n\030Histor"
    "icalPriceIndicator\030U \001(\005\022\024\n\014SymbolSuffix"
    "\030V \001(\t\022\033\n\023HistoricalTradeDate\030W \001(\003\022\033\n\023H"
    "istoricalTradeTime\030X \001(\003\022+\n#CorrectionPr"
    "evSpecialPriceIndicator\030Y \001(\005\022&\n\036Correct"
    "ionNewExtendedTradeCond\030Z \001(\003\022\'\n\037Correct"
    "ionPrevExtendedTradeCond\030[ \001(\003\022\033\n\023Correc"
    "tionPrevYield\030\\ \001(\001\022\032\n\022CorrectionNewYiel"
    "d\030] \001(\001\022\037\n\027CorrectionPrevTradeDate\030^ \001(\t"
    "\022\037\n\027CorrectionPrevTradeTime\030_ \001(\003\022\037\n\027Cor"
    "rectionPrevTradeCond\030` \001(\005\022\036\n\026Correction"
    "NewTradeCond\030a \001(\003\022\026\n\016CancelTradeSeq\030b \001"
    "(\003\022\037\n\027PrevTransactionPriceInd\030c \001(\005\022(\n C"
    "orrectionNewTransactionPriceInd\030d \001(\003\022\030\n"
    "\020OriginalTradeSeq\030e \001(\003\022\022\n\nYieldClose\030f "
    "\001(\001\022\033\n\023FirstSettlementDate\030g \001(\003\022\022\n\nRepo"
    "rtDate\030h \001(\003\022\023\n\013MarketPhase\030i \001(\005\022\031\n\021Dis"
    "seminationDate\030j \001(\003\022&\n\036CorrectionNewDis"
    "seminationDate\030k \001(\003\022\'\n\037CorrectionPrevDi"
    "sseminationDate\030l \001(\003\022\022\n\nTradeCond1\030m \001("
    "\005\022#\n\033CorrectionPrevTradeUniqueId\030n \001(\003\022-"
    "\n%HistoricalCorrectionPrevTradeUniqueId\030"
    "o \001(\003\022!\n\031HistoricalCancelTradeTime\030p \001(\003"
    "\022%\n\035HistoricalCorrectionTradeTime\030q \001(\003\022"
    "%\n\035HistoricalCorrectionTradeDate\030r \001(\003\022\020"
    "\n\010BondType\030s \001(\005\022\r\n\005Yield\030u \001(\001\022\r\n\005Cusip"
    "\030v \001(\t\022\030\n\020SymbolExchTicker\030w \001(\t\022\014\n\004ISIN"
    "\030x \001(\t\022\022\n\nInstrName2\030y \001(\t\022\033\n\023TradeSettl"
    "ementDate\030z \001(\t\022\030\n\020FirstTradingDate\030{ \001("
    "\003\022\022\n\nCouponType\030| \001(\t\022\034\n\024EnumInterestCal"
    "cType\030} \001(\003\022\022\n\nIssuerName\030~ \001(\t\022\027\n\017Inves"
    "tmentGrade\030\177 \001(\005\022\031\n\020AmortizationType\030\200\001 "
    "\001(\t\022\032\n\021DisseminationFlag\030\201\001 \001(\t\022\036\n\025Symbo"
    "lBloombergTicker\030\202\001 \001(\t\022\032\n\021BloombergGlob"
    "alId\030\203\001 \001(\t\022\036\n\025BloombergGlobalIdComp\030\204\001 "
    "\001(\t\022\030\n\017InstrLocalType2\030\205\001 \001(\t\022\034\n\023SymbolE"
    "signalTicker\030\206\001 \001(\t\022\027\n\016InstrNameLocal\030\207\001"
    " \001(\t\022\031\n\020MktSegmentString\030\210\001 \001(\t\022\026\n\rExchM"
    "onthCode\030\211\001 \001(\t\022\023\n\nPoolNumber\030\212\001 \001(\003\022\025\n\014"
    "MasterDealId\030\213\001 \001(\t\022\022\n\tTrancheId\030\214\001 \001(\t\022"
    "\026\n\rIndicator144A\030\215\001 \001(\t\022\035\n\024NumberMaturit"
    "yMonths\030\216\001 \001(\003\022\025\n\014DebtTypeCode\030\217\001 \001(\t\022\021\n"
    "\010TokenDel\030\220\001 \001(\003\022\033\n\022CorrectionTradeSeq\030\221"
    "\001 \001(\003\022\027\n\016RecordStaleInd\030\222\001 \001(\005\022\036\n\025Weight"
    "edAverageCoupon\030\223\001 \001(\001\022\037\n\026WeightedAverag"
    "eLoanAge\030\224\001 \001(\003\022 \n\027WeightedAverageLoanSi"
    "ze\030\225\001 \001(\003\022\032\n\021WeightedLoanValue\030\226\001 \001(\003\022\033\n"
    "\022AverageMonthlySize\030\227\001 \001(\001\022\030\n\017PrevMonthV"
    "olDec\030\230\001 \001(\001B3\n\032com.htsc.mdc.insight.mod"
    "elB\020MDIceTraceProtosH\001\240\001\001b\006proto3", 3913);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDIceTrace.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDIceTrace_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDIceTrace_2eproto_once_);
void protobuf_AddDesc_MDIceTrace_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDIceTrace_2eproto_once_,
                 &protobuf_AddDesc_MDIceTrace_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDIceTrace_2eproto {
  StaticDescriptorInitializer_MDIceTrace_2eproto() {
    protobuf_AddDesc_MDIceTrace_2eproto();
  }
} static_descriptor_initializer_MDIceTrace_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDIceTrace::kMDDateFieldNumber;
const int MDIceTrace::kMDTimeFieldNumber;
const int MDIceTrace::kSecurityTypeFieldNumber;
const int MDIceTrace::kSecurityIDSourceFieldNumber;
const int MDIceTrace::kHTSCSecurityIDFieldNumber;
const int MDIceTrace::kExchangeDateFieldNumber;
const int MDIceTrace::kExchangeTimeFieldNumber;
const int MDIceTrace::kDataMultiplePowerOf10FieldNumber;
const int MDIceTrace::kTradePriceFieldNumber;
const int MDIceTrace::kTradeSizeFieldNumber;
const int MDIceTrace::kCurrentPriceFieldNumber;
const int MDIceTrace::kActivityDatetimeFieldNumber;
const int MDIceTrace::kTradeDatetimeFieldNumber;
const int MDIceTrace::kTradeVolFieldNumber;
const int MDIceTrace::kExchSeqFieldNumber;
const int MDIceTrace::kTradeIndicSizeFieldNumber;
const int MDIceTrace::kExchMessageTimestampFieldNumber;
const int MDIceTrace::kCorrectionNewTradePriceFieldNumber;
const int MDIceTrace::kCorrectionNewTradeSizeFieldNumber;
const int MDIceTrace::kCorrectionPrevTradePriceFieldNumber;
const int MDIceTrace::kCorrectionPrevTradeSizeFieldNumber;
const int MDIceTrace::kEnumCurrencyFieldNumber;
const int MDIceTrace::kMaturityDateFieldNumber;
const int MDIceTrace::kMarketPhaseCodeFieldNumber;
const int MDIceTrace::kChgFieldNumber;
const int MDIceTrace::kPctChgFieldNumber;
const int MDIceTrace::kClosePxFieldNumber;
const int MDIceTrace::kHighPxFieldNumber;
const int MDIceTrace::kLowPxFieldNumber;
const int MDIceTrace::kOpenPxFieldNumber;
const int MDIceTrace::kPreClosePxFieldNumber;
const int MDIceTrace::kRecordDelFieldNumber;
const int MDIceTrace::kCurrencyStringFieldNumber;
const int MDIceTrace::kCurrentDatetimeFieldNumber;
const int MDIceTrace::kTradeOfficialDateFieldNumber;
const int MDIceTrace::kTradeCondPriceFieldNumber;
const int MDIceTrace::kTradeCondSizeFieldNumber;
const int MDIceTrace::kTradeOfficialTimeFieldNumber;
const int MDIceTrace::kTradeUniqueIdFieldNumber;
const int MDIceTrace::kUpdateActionFieldNumber;
const int MDIceTrace::kYestTradeVolFieldNumber;
const int MDIceTrace::kVwapFieldNumber;
const int MDIceTrace::kTradeCondOfficialTimeFieldNumber;
const int MDIceTrace::kTradeCondOfficialDateFieldNumber;
const int MDIceTrace::kCorrectionOfficialTradeTimeFieldNumber;
const int MDIceTrace::kCorrectionOfficialTradeDateFieldNumber;
const int MDIceTrace::kTransactionPriceIndFieldNumber;
const int MDIceTrace::kCouponRateFieldNumber;
const int MDIceTrace::kInstrStatusFieldNumber;
const int MDIceTrace::kMsgTypeFieldNumber;
const int MDIceTrace::kControlMsgTypeFieldNumber;
const int MDIceTrace::kWeightedAverageMaturityFieldNumber;
const int MDIceTrace::kHistoricalTradeSizeFieldNumber;
const int MDIceTrace::kHistoricalCancelSizeFieldNumber;
const int MDIceTrace::kHistoricalCorrectionSizeFieldNumber;
const int MDIceTrace::kHistoricalTradeCondFieldNumber;
const int MDIceTrace::kHistoricalTradePriceFieldNumber;
const int MDIceTrace::kHistoricalCancelPriceFieldNumber;
const int MDIceTrace::kHistoricalCorrectionPriceFieldNumber;
const int MDIceTrace::kActionTimeFieldNumber;
const int MDIceTrace::kActionDateFieldNumber;
const int MDIceTrace::kReasonCodeFieldNumber;
const int MDIceTrace::kHistoricalTradeIdentifierFieldNumber;
const int MDIceTrace::kHistoricalOriginalMessageDateFieldNumber;
const int MDIceTrace::kExtendedTradeCondFieldNumber;
const int MDIceTrace::kYieldHighFieldNumber;
const int MDIceTrace::kYieldLowFieldNumber;
const int MDIceTrace::kHistoricalCancelTradeDateFieldNumber;
const int MDIceTrace::kHistoricalYieldFieldNumber;
const int MDIceTrace::kHistoricalTradeIndicSizeFieldNumber;
const int MDIceTrace::kHistoricalExtendedTradeCondFieldNumber;
const int MDIceTrace::kHistoricalPriceIndicatorFieldNumber;
const int MDIceTrace::kSymbolSuffixFieldNumber;
const int MDIceTrace::kHistoricalTradeDateFieldNumber;
const int MDIceTrace::kHistoricalTradeTimeFieldNumber;
const int MDIceTrace::kCorrectionPrevSpecialPriceIndicatorFieldNumber;
const int MDIceTrace::kCorrectionNewExtendedTradeCondFieldNumber;
const int MDIceTrace::kCorrectionPrevExtendedTradeCondFieldNumber;
const int MDIceTrace::kCorrectionPrevYieldFieldNumber;
const int MDIceTrace::kCorrectionNewYieldFieldNumber;
const int MDIceTrace::kCorrectionPrevTradeDateFieldNumber;
const int MDIceTrace::kCorrectionPrevTradeTimeFieldNumber;
const int MDIceTrace::kCorrectionPrevTradeCondFieldNumber;
const int MDIceTrace::kCorrectionNewTradeCondFieldNumber;
const int MDIceTrace::kCancelTradeSeqFieldNumber;
const int MDIceTrace::kPrevTransactionPriceIndFieldNumber;
const int MDIceTrace::kCorrectionNewTransactionPriceIndFieldNumber;
const int MDIceTrace::kOriginalTradeSeqFieldNumber;
const int MDIceTrace::kYieldCloseFieldNumber;
const int MDIceTrace::kFirstSettlementDateFieldNumber;
const int MDIceTrace::kReportDateFieldNumber;
const int MDIceTrace::kMarketPhaseFieldNumber;
const int MDIceTrace::kDisseminationDateFieldNumber;
const int MDIceTrace::kCorrectionNewDisseminationDateFieldNumber;
const int MDIceTrace::kCorrectionPrevDisseminationDateFieldNumber;
const int MDIceTrace::kTradeCond1FieldNumber;
const int MDIceTrace::kCorrectionPrevTradeUniqueIdFieldNumber;
const int MDIceTrace::kHistoricalCorrectionPrevTradeUniqueIdFieldNumber;
const int MDIceTrace::kHistoricalCancelTradeTimeFieldNumber;
const int MDIceTrace::kHistoricalCorrectionTradeTimeFieldNumber;
const int MDIceTrace::kHistoricalCorrectionTradeDateFieldNumber;
const int MDIceTrace::kBondTypeFieldNumber;
const int MDIceTrace::kYieldFieldNumber;
const int MDIceTrace::kCusipFieldNumber;
const int MDIceTrace::kSymbolExchTickerFieldNumber;
const int MDIceTrace::kISINFieldNumber;
const int MDIceTrace::kInstrName2FieldNumber;
const int MDIceTrace::kTradeSettlementDateFieldNumber;
const int MDIceTrace::kFirstTradingDateFieldNumber;
const int MDIceTrace::kCouponTypeFieldNumber;
const int MDIceTrace::kEnumInterestCalcTypeFieldNumber;
const int MDIceTrace::kIssuerNameFieldNumber;
const int MDIceTrace::kInvestmentGradeFieldNumber;
const int MDIceTrace::kAmortizationTypeFieldNumber;
const int MDIceTrace::kDisseminationFlagFieldNumber;
const int MDIceTrace::kSymbolBloombergTickerFieldNumber;
const int MDIceTrace::kBloombergGlobalIdFieldNumber;
const int MDIceTrace::kBloombergGlobalIdCompFieldNumber;
const int MDIceTrace::kInstrLocalType2FieldNumber;
const int MDIceTrace::kSymbolEsignalTickerFieldNumber;
const int MDIceTrace::kInstrNameLocalFieldNumber;
const int MDIceTrace::kMktSegmentStringFieldNumber;
const int MDIceTrace::kExchMonthCodeFieldNumber;
const int MDIceTrace::kPoolNumberFieldNumber;
const int MDIceTrace::kMasterDealIdFieldNumber;
const int MDIceTrace::kTrancheIdFieldNumber;
const int MDIceTrace::kIndicator144AFieldNumber;
const int MDIceTrace::kNumberMaturityMonthsFieldNumber;
const int MDIceTrace::kDebtTypeCodeFieldNumber;
const int MDIceTrace::kTokenDelFieldNumber;
const int MDIceTrace::kCorrectionTradeSeqFieldNumber;
const int MDIceTrace::kRecordStaleIndFieldNumber;
const int MDIceTrace::kWeightedAverageCouponFieldNumber;
const int MDIceTrace::kWeightedAverageLoanAgeFieldNumber;
const int MDIceTrace::kWeightedAverageLoanSizeFieldNumber;
const int MDIceTrace::kWeightedLoanValueFieldNumber;
const int MDIceTrace::kAverageMonthlySizeFieldNumber;
const int MDIceTrace::kPrevMonthVolDecFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDIceTrace::MDIceTrace()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDIceTrace_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDIceTrace)
}

void MDIceTrace::InitAsDefaultInstance() {
}

MDIceTrace::MDIceTrace(const MDIceTrace& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDIceTrace)
}

void MDIceTrace::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  activitydatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enumcurrency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currencystring_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currentdatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolsuffix_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  correctionprevtradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cusip_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolexchticker_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  isin_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrname2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradesettlementdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  coupontype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  issuername_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  amortizationtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  disseminationflag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolbloombergticker_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalidcomp_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrlocaltype2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolesignalticker_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrnamelocal_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mktsegmentstring_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchmonthcode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  masterdealid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  trancheid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indicator144a_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  debttypecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&recordstaleind_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(recordstaleind_));
  _cached_size_ = 0;
}

MDIceTrace::~MDIceTrace() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDIceTrace)
  SharedDtor();
}

void MDIceTrace::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  activitydatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enumcurrency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currencystring_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currentdatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolsuffix_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  correctionprevtradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cusip_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolexchticker_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  isin_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrname2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradesettlementdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  coupontype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  issuername_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  amortizationtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  disseminationflag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolbloombergticker_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalidcomp_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrlocaltype2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolesignalticker_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrnamelocal_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mktsegmentstring_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchmonthcode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  masterdealid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  trancheid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indicator144a_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  debttypecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDIceTrace::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDIceTrace::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDIceTrace_descriptor_;
}

const MDIceTrace& MDIceTrace::default_instance() {
  protobuf_InitDefaults_MDIceTrace_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDIceTrace> MDIceTrace_default_instance_;

MDIceTrace* MDIceTrace::New(::google::protobuf::Arena* arena) const {
  MDIceTrace* n = new MDIceTrace;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDIceTrace::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDIceTrace)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDIceTrace, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDIceTrace*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datamultiplepowerof10_ = 0;
  ZR_(tradeprice_, tradeindicsize_);
  activitydatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(marketphasecode_, maturitydate_);
  enumcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(chg_, preclosepx_);
  recorddel_ = false;
  ZR_(tradeofficialdate_, tradeuniqueid_);
  currencystring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currentdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updateaction_ = GOOGLE_LONGLONG(0);
  ZR_(yesttradevol_, couponrate_);
  transactionpriceind_ = 0;
  ZR_(instrstatus_, controlmsgtype_);
  ZR_(reasoncode_, historicaloriginalmessagedate_);
  ZR_(extendedtradecond_, historicalextendedtradecond_);
  historicalpriceindicator_ = 0;
  ZR_(correctionprevspecialpriceindicator_, correctionnewyield_);
  symbolsuffix_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  historicaltradedate_ = GOOGLE_LONGLONG(0);
  ZR_(correctionprevtradetime_, originaltradeseq_);
  correctionprevtradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(yieldclose_, correctionprevdisseminationdate_);
  ZR_(correctionprevtradeuniqueid_, bondtype_);
  cusip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(firsttradingdate_, enuminterestcalctype_);
  symbolexchticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  isin_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrname2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradesettlementdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  coupontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  issuername_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investmentgrade_ = 0;
  amortizationtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  disseminationflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolbloombergticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bloombergglobalidcomp_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrlocaltype2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbolesignalticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(poolnumber_, numbermaturitymonths_);
  instrnamelocal_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mktsegmentstring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchmonthcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  masterdealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  trancheid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  indicator144a_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(tokendel_, weightedloanvalue_);
  debttypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  recordstaleind_ = 0;
  ZR_(averagemonthlysize_, prevmonthvoldec_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDIceTrace::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDIceTrace)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 MDDate = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 2;
      case 2: {
        if (tag == 16) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
      case 4: {
        if (tag == 32) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_HTSCSecurityID;
        break;
      }

      // optional string HTSCSecurityID = 5;
      case 5: {
        if (tag == 42) {
         parse_HTSCSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 6;
      case 6: {
        if (tag == 48) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 8;
      case 8: {
        if (tag == 64) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_TradePrice;
        break;
      }

      // optional double TradePrice = 19;
      case 19: {
        if (tag == 153) {
         parse_TradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_TradeSize;
        break;
      }

      // optional int64 TradeSize = 20;
      case 20: {
        if (tag == 160) {
         parse_TradeSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradesize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(169)) goto parse_CurrentPrice;
        break;
      }

      // optional double CurrentPrice = 21;
      case 21: {
        if (tag == 169) {
         parse_CurrentPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &currentprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_ActivityDatetime;
        break;
      }

      // optional string ActivityDatetime = 22;
      case 22: {
        if (tag == 178) {
         parse_ActivityDatetime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_activitydatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->activitydatetime().data(), this->activitydatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_TradeDatetime;
        break;
      }

      // optional string TradeDatetime = 23;
      case 23: {
        if (tag == 186) {
         parse_TradeDatetime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedatetime().data(), this->tradedatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_TradeVol;
        break;
      }

      // optional int64 TradeVol = 24;
      case 24: {
        if (tag == 192) {
         parse_TradeVol:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradevol_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_ExchSeq;
        break;
      }

      // optional int64 ExchSeq = 26;
      case 26: {
        if (tag == 208) {
         parse_ExchSeq:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &exchseq_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(217)) goto parse_TradeIndicSize;
        break;
      }

      // optional double TradeIndicSize = 27;
      case 27: {
        if (tag == 217) {
         parse_TradeIndicSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradeindicsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_ExchMessageTimestamp;
        break;
      }

      // optional int64 ExchMessageTimestamp = 28;
      case 28: {
        if (tag == 224) {
         parse_ExchMessageTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &exchmessagetimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(241)) goto parse_CorrectionNewTradePrice;
        break;
      }

      // optional double CorrectionNewTradePrice = 30;
      case 30: {
        if (tag == 241) {
         parse_CorrectionNewTradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &correctionnewtradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_CorrectionNewTradeSize;
        break;
      }

      // optional int64 CorrectionNewTradeSize = 31;
      case 31: {
        if (tag == 248) {
         parse_CorrectionNewTradeSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionnewtradesize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(257)) goto parse_CorrectionPrevTradePrice;
        break;
      }

      // optional double CorrectionPrevTradePrice = 32;
      case 32: {
        if (tag == 257) {
         parse_CorrectionPrevTradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &correctionprevtradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_CorrectionPrevTradeSize;
        break;
      }

      // optional int64 CorrectionPrevTradeSize = 33;
      case 33: {
        if (tag == 264) {
         parse_CorrectionPrevTradeSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionprevtradesize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_EnumCurrency;
        break;
      }

      // optional string EnumCurrency = 34;
      case 34: {
        if (tag == 274) {
         parse_EnumCurrency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_enumcurrency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->enumcurrency().data(), this->enumcurrency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_MaturityDate;
        break;
      }

      // optional int64 MaturityDate = 35;
      case 35: {
        if (tag == 280) {
         parse_MaturityDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maturitydate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_MarketPhaseCode;
        break;
      }

      // optional int32 MarketPhaseCode = 36;
      case 36: {
        if (tag == 288) {
         parse_MarketPhaseCode:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &marketphasecode_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(297)) goto parse_Chg;
        break;
      }

      // optional double Chg = 37;
      case 37: {
        if (tag == 297) {
         parse_Chg:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &chg_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(305)) goto parse_PctChg;
        break;
      }

      // optional double PctChg = 38;
      case 38: {
        if (tag == 305) {
         parse_PctChg:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &pctchg_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(313)) goto parse_ClosePx;
        break;
      }

      // optional double ClosePx = 39;
      case 39: {
        if (tag == 313) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(321)) goto parse_HighPx;
        break;
      }

      // optional double HighPx = 40;
      case 40: {
        if (tag == 321) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(329)) goto parse_LowPx;
        break;
      }

      // optional double LowPx = 41;
      case 41: {
        if (tag == 329) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(337)) goto parse_OpenPx;
        break;
      }

      // optional double OpenPx = 42;
      case 42: {
        if (tag == 337) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(345)) goto parse_PreClosePx;
        break;
      }

      // optional double PreClosePx = 43;
      case 43: {
        if (tag == 345) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_RecordDel;
        break;
      }

      // optional bool RecordDel = 45;
      case 45: {
        if (tag == 360) {
         parse_RecordDel:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &recorddel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(370)) goto parse_CurrencyString;
        break;
      }

      // optional string CurrencyString = 46;
      case 46: {
        if (tag == 370) {
         parse_CurrencyString:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_currencystring()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->currencystring().data(), this->currencystring().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.CurrencyString"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(378)) goto parse_CurrentDatetime;
        break;
      }

      // optional string CurrentDatetime = 47;
      case 47: {
        if (tag == 378) {
         parse_CurrentDatetime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_currentdatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->currentdatetime().data(), this->currentdatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(384)) goto parse_TradeOfficialDate;
        break;
      }

      // optional int64 TradeOfficialDate = 48;
      case 48: {
        if (tag == 384) {
         parse_TradeOfficialDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeofficialdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(393)) goto parse_TradeCondPrice;
        break;
      }

      // optional double TradeCondPrice = 49;
      case 49: {
        if (tag == 393) {
         parse_TradeCondPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradecondprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_TradeCondSize;
        break;
      }

      // optional int64 TradeCondSize = 50;
      case 50: {
        if (tag == 400) {
         parse_TradeCondSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradecondsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(408)) goto parse_TradeOfficialTime;
        break;
      }

      // optional int64 TradeOfficialTime = 51;
      case 51: {
        if (tag == 408) {
         parse_TradeOfficialTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeofficialtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(416)) goto parse_TradeUniqueId;
        break;
      }

      // optional int64 TradeUniqueId = 52;
      case 52: {
        if (tag == 416) {
         parse_TradeUniqueId:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeuniqueid_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(424)) goto parse_UpdateAction;
        break;
      }

      // optional int64 UpdateAction = 53;
      case 53: {
        if (tag == 424) {
         parse_UpdateAction:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &updateaction_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(432)) goto parse_YestTradeVol;
        break;
      }

      // optional int64 YestTradeVol = 54;
      case 54: {
        if (tag == 432) {
         parse_YestTradeVol:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &yesttradevol_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(441)) goto parse_Vwap;
        break;
      }

      // optional double Vwap = 55;
      case 55: {
        if (tag == 441) {
         parse_Vwap:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &vwap_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(448)) goto parse_TradeCondOfficialTime;
        break;
      }

      // optional int64 TradeCondOfficialTime = 56;
      case 56: {
        if (tag == 448) {
         parse_TradeCondOfficialTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradecondofficialtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(456)) goto parse_TradeCondOfficialDate;
        break;
      }

      // optional int64 TradeCondOfficialDate = 57;
      case 57: {
        if (tag == 456) {
         parse_TradeCondOfficialDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradecondofficialdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(464)) goto parse_CorrectionOfficialTradeTime;
        break;
      }

      // optional int64 CorrectionOfficialTradeTime = 58;
      case 58: {
        if (tag == 464) {
         parse_CorrectionOfficialTradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionofficialtradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_CorrectionOfficialTradeDate;
        break;
      }

      // optional int64 CorrectionOfficialTradeDate = 59;
      case 59: {
        if (tag == 472) {
         parse_CorrectionOfficialTradeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionofficialtradedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_TransactionPriceInd;
        break;
      }

      // optional int32 TransactionPriceInd = 60;
      case 60: {
        if (tag == 480) {
         parse_TransactionPriceInd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &transactionpriceind_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(489)) goto parse_CouponRate;
        break;
      }

      // optional double CouponRate = 61;
      case 61: {
        if (tag == 489) {
         parse_CouponRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &couponrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(496)) goto parse_InstrStatus;
        break;
      }

      // optional int32 InstrStatus = 62;
      case 62: {
        if (tag == 496) {
         parse_InstrStatus:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &instrstatus_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(504)) goto parse_MsgType;
        break;
      }

      // optional int32 MsgType = 63;
      case 63: {
        if (tag == 504) {
         parse_MsgType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &msgtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(512)) goto parse_ControlMsgType;
        break;
      }

      // optional int32 ControlMsgType = 64;
      case 64: {
        if (tag == 512) {
         parse_ControlMsgType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &controlmsgtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(520)) goto parse_WeightedAverageMaturity;
        break;
      }

      // optional int64 WeightedAverageMaturity = 65;
      case 65: {
        if (tag == 520) {
         parse_WeightedAverageMaturity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedaveragematurity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(528)) goto parse_HistoricalTradeSize;
        break;
      }

      // optional int64 HistoricalTradeSize = 66;
      case 66: {
        if (tag == 528) {
         parse_HistoricalTradeSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaltradesize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(536)) goto parse_HistoricalCancelSize;
        break;
      }

      // optional int64 HistoricalCancelSize = 67;
      case 67: {
        if (tag == 536) {
         parse_HistoricalCancelSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcancelsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(544)) goto parse_HistoricalCorrectionSize;
        break;
      }

      // optional int64 HistoricalCorrectionSize = 68;
      case 68: {
        if (tag == 544) {
         parse_HistoricalCorrectionSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcorrectionsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(552)) goto parse_HistoricalTradeCond;
        break;
      }

      // optional int64 HistoricalTradeCond = 69;
      case 69: {
        if (tag == 552) {
         parse_HistoricalTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaltradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(561)) goto parse_HistoricalTradePrice;
        break;
      }

      // optional double HistoricalTradePrice = 70;
      case 70: {
        if (tag == 561) {
         parse_HistoricalTradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &historicaltradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(569)) goto parse_HistoricalCancelPrice;
        break;
      }

      // optional double HistoricalCancelPrice = 71;
      case 71: {
        if (tag == 569) {
         parse_HistoricalCancelPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &historicalcancelprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(577)) goto parse_HistoricalCorrectionPrice;
        break;
      }

      // optional double HistoricalCorrectionPrice = 72;
      case 72: {
        if (tag == 577) {
         parse_HistoricalCorrectionPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &historicalcorrectionprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(584)) goto parse_ActionTime;
        break;
      }

      // optional int64 ActionTime = 73;
      case 73: {
        if (tag == 584) {
         parse_ActionTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &actiontime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(592)) goto parse_ActionDate;
        break;
      }

      // optional int64 ActionDate = 74;
      case 74: {
        if (tag == 592) {
         parse_ActionDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &actiondate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(600)) goto parse_ReasonCode;
        break;
      }

      // optional int32 ReasonCode = 75;
      case 75: {
        if (tag == 600) {
         parse_ReasonCode:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &reasoncode_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(608)) goto parse_HistoricalTradeIdentifier;
        break;
      }

      // optional int64 HistoricalTradeIdentifier = 76;
      case 76: {
        if (tag == 608) {
         parse_HistoricalTradeIdentifier:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaltradeidentifier_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(616)) goto parse_HistoricalOriginalMessageDate;
        break;
      }

      // optional int64 HistoricalOriginalMessageDate = 77;
      case 77: {
        if (tag == 616) {
         parse_HistoricalOriginalMessageDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaloriginalmessagedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(624)) goto parse_ExtendedTradeCond;
        break;
      }

      // optional int64 ExtendedTradeCond = 78;
      case 78: {
        if (tag == 624) {
         parse_ExtendedTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &extendedtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(633)) goto parse_YieldHigh;
        break;
      }

      // optional double YieldHigh = 79;
      case 79: {
        if (tag == 633) {
         parse_YieldHigh:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldhigh_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(641)) goto parse_YieldLow;
        break;
      }

      // optional double YieldLow = 80;
      case 80: {
        if (tag == 641) {
         parse_YieldLow:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldlow_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(648)) goto parse_HistoricalCancelTradeDate;
        break;
      }

      // optional int64 HistoricalCancelTradeDate = 81;
      case 81: {
        if (tag == 648) {
         parse_HistoricalCancelTradeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcanceltradedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(657)) goto parse_HistoricalYield;
        break;
      }

      // optional double HistoricalYield = 82;
      case 82: {
        if (tag == 657) {
         parse_HistoricalYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &historicalyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(665)) goto parse_HistoricalTradeIndicSize;
        break;
      }

      // optional double HistoricalTradeIndicSize = 83;
      case 83: {
        if (tag == 665) {
         parse_HistoricalTradeIndicSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &historicaltradeindicsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(672)) goto parse_HistoricalExtendedTradeCond;
        break;
      }

      // optional int64 HistoricalExtendedTradeCond = 84;
      case 84: {
        if (tag == 672) {
         parse_HistoricalExtendedTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalextendedtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(680)) goto parse_HistoricalPriceIndicator;
        break;
      }

      // optional int32 HistoricalPriceIndicator = 85;
      case 85: {
        if (tag == 680) {
         parse_HistoricalPriceIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &historicalpriceindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(690)) goto parse_SymbolSuffix;
        break;
      }

      // optional string SymbolSuffix = 86;
      case 86: {
        if (tag == 690) {
         parse_SymbolSuffix:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbolsuffix()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbolsuffix().data(), this->symbolsuffix().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(696)) goto parse_HistoricalTradeDate;
        break;
      }

      // optional int64 HistoricalTradeDate = 87;
      case 87: {
        if (tag == 696) {
         parse_HistoricalTradeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaltradedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(704)) goto parse_HistoricalTradeTime;
        break;
      }

      // optional int64 HistoricalTradeTime = 88;
      case 88: {
        if (tag == 704) {
         parse_HistoricalTradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicaltradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(712)) goto parse_CorrectionPrevSpecialPriceIndicator;
        break;
      }

      // optional int32 CorrectionPrevSpecialPriceIndicator = 89;
      case 89: {
        if (tag == 712) {
         parse_CorrectionPrevSpecialPriceIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &correctionprevspecialpriceindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(720)) goto parse_CorrectionNewExtendedTradeCond;
        break;
      }

      // optional int64 CorrectionNewExtendedTradeCond = 90;
      case 90: {
        if (tag == 720) {
         parse_CorrectionNewExtendedTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionnewextendedtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(728)) goto parse_CorrectionPrevExtendedTradeCond;
        break;
      }

      // optional int64 CorrectionPrevExtendedTradeCond = 91;
      case 91: {
        if (tag == 728) {
         parse_CorrectionPrevExtendedTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionprevextendedtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(737)) goto parse_CorrectionPrevYield;
        break;
      }

      // optional double CorrectionPrevYield = 92;
      case 92: {
        if (tag == 737) {
         parse_CorrectionPrevYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &correctionprevyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(745)) goto parse_CorrectionNewYield;
        break;
      }

      // optional double CorrectionNewYield = 93;
      case 93: {
        if (tag == 745) {
         parse_CorrectionNewYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &correctionnewyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(754)) goto parse_CorrectionPrevTradeDate;
        break;
      }

      // optional string CorrectionPrevTradeDate = 94;
      case 94: {
        if (tag == 754) {
         parse_CorrectionPrevTradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_correctionprevtradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->correctionprevtradedate().data(), this->correctionprevtradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(760)) goto parse_CorrectionPrevTradeTime;
        break;
      }

      // optional int64 CorrectionPrevTradeTime = 95;
      case 95: {
        if (tag == 760) {
         parse_CorrectionPrevTradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionprevtradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(768)) goto parse_CorrectionPrevTradeCond;
        break;
      }

      // optional int32 CorrectionPrevTradeCond = 96;
      case 96: {
        if (tag == 768) {
         parse_CorrectionPrevTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &correctionprevtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(776)) goto parse_CorrectionNewTradeCond;
        break;
      }

      // optional int64 CorrectionNewTradeCond = 97;
      case 97: {
        if (tag == 776) {
         parse_CorrectionNewTradeCond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionnewtradecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(784)) goto parse_CancelTradeSeq;
        break;
      }

      // optional int64 CancelTradeSeq = 98;
      case 98: {
        if (tag == 784) {
         parse_CancelTradeSeq:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &canceltradeseq_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(792)) goto parse_PrevTransactionPriceInd;
        break;
      }

      // optional int32 PrevTransactionPriceInd = 99;
      case 99: {
        if (tag == 792) {
         parse_PrevTransactionPriceInd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &prevtransactionpriceind_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(800)) goto parse_CorrectionNewTransactionPriceInd;
        break;
      }

      // optional int64 CorrectionNewTransactionPriceInd = 100;
      case 100: {
        if (tag == 800) {
         parse_CorrectionNewTransactionPriceInd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionnewtransactionpriceind_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_OriginalTradeSeq;
        break;
      }

      // optional int64 OriginalTradeSeq = 101;
      case 101: {
        if (tag == 808) {
         parse_OriginalTradeSeq:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &originaltradeseq_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(817)) goto parse_YieldClose;
        break;
      }

      // optional double YieldClose = 102;
      case 102: {
        if (tag == 817) {
         parse_YieldClose:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldclose_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(824)) goto parse_FirstSettlementDate;
        break;
      }

      // optional int64 FirstSettlementDate = 103;
      case 103: {
        if (tag == 824) {
         parse_FirstSettlementDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &firstsettlementdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(832)) goto parse_ReportDate;
        break;
      }

      // optional int64 ReportDate = 104;
      case 104: {
        if (tag == 832) {
         parse_ReportDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &reportdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(840)) goto parse_MarketPhase;
        break;
      }

      // optional int32 MarketPhase = 105;
      case 105: {
        if (tag == 840) {
         parse_MarketPhase:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &marketphase_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(848)) goto parse_DisseminationDate;
        break;
      }

      // optional int64 DisseminationDate = 106;
      case 106: {
        if (tag == 848) {
         parse_DisseminationDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &disseminationdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(856)) goto parse_CorrectionNewDisseminationDate;
        break;
      }

      // optional int64 CorrectionNewDisseminationDate = 107;
      case 107: {
        if (tag == 856) {
         parse_CorrectionNewDisseminationDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionnewdisseminationdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(864)) goto parse_CorrectionPrevDisseminationDate;
        break;
      }

      // optional int64 CorrectionPrevDisseminationDate = 108;
      case 108: {
        if (tag == 864) {
         parse_CorrectionPrevDisseminationDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionprevdisseminationdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(872)) goto parse_TradeCond1;
        break;
      }

      // optional int32 TradeCond1 = 109;
      case 109: {
        if (tag == 872) {
         parse_TradeCond1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradecond1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(880)) goto parse_CorrectionPrevTradeUniqueId;
        break;
      }

      // optional int64 CorrectionPrevTradeUniqueId = 110;
      case 110: {
        if (tag == 880) {
         parse_CorrectionPrevTradeUniqueId:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctionprevtradeuniqueid_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(888)) goto parse_HistoricalCorrectionPrevTradeUniqueId;
        break;
      }

      // optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
      case 111: {
        if (tag == 888) {
         parse_HistoricalCorrectionPrevTradeUniqueId:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcorrectionprevtradeuniqueid_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(896)) goto parse_HistoricalCancelTradeTime;
        break;
      }

      // optional int64 HistoricalCancelTradeTime = 112;
      case 112: {
        if (tag == 896) {
         parse_HistoricalCancelTradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcanceltradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(904)) goto parse_HistoricalCorrectionTradeTime;
        break;
      }

      // optional int64 HistoricalCorrectionTradeTime = 113;
      case 113: {
        if (tag == 904) {
         parse_HistoricalCorrectionTradeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcorrectiontradetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(912)) goto parse_HistoricalCorrectionTradeDate;
        break;
      }

      // optional int64 HistoricalCorrectionTradeDate = 114;
      case 114: {
        if (tag == 912) {
         parse_HistoricalCorrectionTradeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historicalcorrectiontradedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(920)) goto parse_BondType;
        break;
      }

      // optional int32 BondType = 115;
      case 115: {
        if (tag == 920) {
         parse_BondType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bondtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(937)) goto parse_Yield;
        break;
      }

      // optional double Yield = 117;
      case 117: {
        if (tag == 937) {
         parse_Yield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(946)) goto parse_Cusip;
        break;
      }

      // optional string Cusip = 118;
      case 118: {
        if (tag == 946) {
         parse_Cusip:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cusip()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cusip().data(), this->cusip().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.Cusip"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(954)) goto parse_SymbolExchTicker;
        break;
      }

      // optional string SymbolExchTicker = 119;
      case 119: {
        if (tag == 954) {
         parse_SymbolExchTicker:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbolexchticker()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbolexchticker().data(), this->symbolexchticker().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(962)) goto parse_ISIN;
        break;
      }

      // optional string ISIN = 120;
      case 120: {
        if (tag == 962) {
         parse_ISIN:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_isin()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->isin().data(), this->isin().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.ISIN"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(970)) goto parse_InstrName2;
        break;
      }

      // optional string InstrName2 = 121;
      case 121: {
        if (tag == 970) {
         parse_InstrName2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrname2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrname2().data(), this->instrname2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.InstrName2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(978)) goto parse_TradeSettlementDate;
        break;
      }

      // optional string TradeSettlementDate = 122;
      case 122: {
        if (tag == 978) {
         parse_TradeSettlementDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradesettlementdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradesettlementdate().data(), this->tradesettlementdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(984)) goto parse_FirstTradingDate;
        break;
      }

      // optional int64 FirstTradingDate = 123;
      case 123: {
        if (tag == 984) {
         parse_FirstTradingDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &firsttradingdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(994)) goto parse_CouponType;
        break;
      }

      // optional string CouponType = 124;
      case 124: {
        if (tag == 994) {
         parse_CouponType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_coupontype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->coupontype().data(), this->coupontype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.CouponType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1000)) goto parse_EnumInterestCalcType;
        break;
      }

      // optional int64 EnumInterestCalcType = 125;
      case 125: {
        if (tag == 1000) {
         parse_EnumInterestCalcType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &enuminterestcalctype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1010)) goto parse_IssuerName;
        break;
      }

      // optional string IssuerName = 126;
      case 126: {
        if (tag == 1010) {
         parse_IssuerName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_issuername()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->issuername().data(), this->issuername().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.IssuerName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1016)) goto parse_InvestmentGrade;
        break;
      }

      // optional int32 InvestmentGrade = 127;
      case 127: {
        if (tag == 1016) {
         parse_InvestmentGrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &investmentgrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1026)) goto parse_AmortizationType;
        break;
      }

      // optional string AmortizationType = 128;
      case 128: {
        if (tag == 1026) {
         parse_AmortizationType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_amortizationtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->amortizationtype().data(), this->amortizationtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.AmortizationType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1034)) goto parse_DisseminationFlag;
        break;
      }

      // optional string DisseminationFlag = 129;
      case 129: {
        if (tag == 1034) {
         parse_DisseminationFlag:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_disseminationflag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->disseminationflag().data(), this->disseminationflag().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1042)) goto parse_SymbolBloombergTicker;
        break;
      }

      // optional string SymbolBloombergTicker = 130;
      case 130: {
        if (tag == 1042) {
         parse_SymbolBloombergTicker:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbolbloombergticker()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbolbloombergticker().data(), this->symbolbloombergticker().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1050)) goto parse_BloombergGlobalId;
        break;
      }

      // optional string BloombergGlobalId = 131;
      case 131: {
        if (tag == 1050) {
         parse_BloombergGlobalId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bloombergglobalid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bloombergglobalid().data(), this->bloombergglobalid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1058)) goto parse_BloombergGlobalIdComp;
        break;
      }

      // optional string BloombergGlobalIdComp = 132;
      case 132: {
        if (tag == 1058) {
         parse_BloombergGlobalIdComp:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bloombergglobalidcomp()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bloombergglobalidcomp().data(), this->bloombergglobalidcomp().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1066)) goto parse_InstrLocalType2;
        break;
      }

      // optional string InstrLocalType2 = 133;
      case 133: {
        if (tag == 1066) {
         parse_InstrLocalType2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrlocaltype2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrlocaltype2().data(), this->instrlocaltype2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1074)) goto parse_SymbolEsignalTicker;
        break;
      }

      // optional string SymbolEsignalTicker = 134;
      case 134: {
        if (tag == 1074) {
         parse_SymbolEsignalTicker:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbolesignalticker()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbolesignalticker().data(), this->symbolesignalticker().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1082)) goto parse_InstrNameLocal;
        break;
      }

      // optional string InstrNameLocal = 135;
      case 135: {
        if (tag == 1082) {
         parse_InstrNameLocal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrnamelocal()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrnamelocal().data(), this->instrnamelocal().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1090)) goto parse_MktSegmentString;
        break;
      }

      // optional string MktSegmentString = 136;
      case 136: {
        if (tag == 1090) {
         parse_MktSegmentString:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mktsegmentstring()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->mktsegmentstring().data(), this->mktsegmentstring().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1098)) goto parse_ExchMonthCode;
        break;
      }

      // optional string ExchMonthCode = 137;
      case 137: {
        if (tag == 1098) {
         parse_ExchMonthCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchmonthcode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchmonthcode().data(), this->exchmonthcode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1104)) goto parse_PoolNumber;
        break;
      }

      // optional int64 PoolNumber = 138;
      case 138: {
        if (tag == 1104) {
         parse_PoolNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &poolnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1114)) goto parse_MasterDealId;
        break;
      }

      // optional string MasterDealId = 139;
      case 139: {
        if (tag == 1114) {
         parse_MasterDealId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_masterdealid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->masterdealid().data(), this->masterdealid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.MasterDealId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1122)) goto parse_TrancheId;
        break;
      }

      // optional string TrancheId = 140;
      case 140: {
        if (tag == 1122) {
         parse_TrancheId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_trancheid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->trancheid().data(), this->trancheid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.TrancheId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1130)) goto parse_Indicator144A;
        break;
      }

      // optional string Indicator144A = 141;
      case 141: {
        if (tag == 1130) {
         parse_Indicator144A:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_indicator144a()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->indicator144a().data(), this->indicator144a().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.Indicator144A"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1136)) goto parse_NumberMaturityMonths;
        break;
      }

      // optional int64 NumberMaturityMonths = 142;
      case 142: {
        if (tag == 1136) {
         parse_NumberMaturityMonths:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numbermaturitymonths_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1146)) goto parse_DebtTypeCode;
        break;
      }

      // optional string DebtTypeCode = 143;
      case 143: {
        if (tag == 1146) {
         parse_DebtTypeCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_debttypecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->debttypecode().data(), this->debttypecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1152)) goto parse_TokenDel;
        break;
      }

      // optional int64 TokenDel = 144;
      case 144: {
        if (tag == 1152) {
         parse_TokenDel:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tokendel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1160)) goto parse_CorrectionTradeSeq;
        break;
      }

      // optional int64 CorrectionTradeSeq = 145;
      case 145: {
        if (tag == 1160) {
         parse_CorrectionTradeSeq:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &correctiontradeseq_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1168)) goto parse_RecordStaleInd;
        break;
      }

      // optional int32 RecordStaleInd = 146;
      case 146: {
        if (tag == 1168) {
         parse_RecordStaleInd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &recordstaleind_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1177)) goto parse_WeightedAverageCoupon;
        break;
      }

      // optional double WeightedAverageCoupon = 147;
      case 147: {
        if (tag == 1177) {
         parse_WeightedAverageCoupon:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedaveragecoupon_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1184)) goto parse_WeightedAverageLoanAge;
        break;
      }

      // optional int64 WeightedAverageLoanAge = 148;
      case 148: {
        if (tag == 1184) {
         parse_WeightedAverageLoanAge:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedaverageloanage_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1192)) goto parse_WeightedAverageLoanSize;
        break;
      }

      // optional int64 WeightedAverageLoanSize = 149;
      case 149: {
        if (tag == 1192) {
         parse_WeightedAverageLoanSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedaverageloansize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1200)) goto parse_WeightedLoanValue;
        break;
      }

      // optional int64 WeightedLoanValue = 150;
      case 150: {
        if (tag == 1200) {
         parse_WeightedLoanValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedloanvalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1209)) goto parse_AverageMonthlySize;
        break;
      }

      // optional double AverageMonthlySize = 151;
      case 151: {
        if (tag == 1209) {
         parse_AverageMonthlySize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &averagemonthlysize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1217)) goto parse_PrevMonthVolDec;
        break;
      }

      // optional double PrevMonthVolDec = 152;
      case 152: {
        if (tag == 1217) {
         parse_PrevMonthVolDec:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &prevmonthvoldec_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDIceTrace)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDIceTrace)
  return false;
#undef DO_
}

void MDIceTrace::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDIceTrace)
  // optional int32 MDDate = 1;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->mddate(), output);
  }

  // optional int32 MDTime = 2;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mdtime(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->securityidsource(), output);
  }

  // optional string HTSCSecurityID = 5;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->htscsecurityid(), output);
  }

  // optional int32 ExchangeDate = 6;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 7;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangetime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->datamultiplepowerof10(), output);
  }

  // optional double TradePrice = 19;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->tradeprice(), output);
  }

  // optional int64 TradeSize = 20;
  if (this->tradesize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->tradesize(), output);
  }

  // optional double CurrentPrice = 21;
  if (this->currentprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(21, this->currentprice(), output);
  }

  // optional string ActivityDatetime = 22;
  if (this->activitydatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->activitydatetime().data(), this->activitydatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->activitydatetime(), output);
  }

  // optional string TradeDatetime = 23;
  if (this->tradedatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedatetime().data(), this->tradedatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->tradedatetime(), output);
  }

  // optional int64 TradeVol = 24;
  if (this->tradevol() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->tradevol(), output);
  }

  // optional int64 ExchSeq = 26;
  if (this->exchseq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->exchseq(), output);
  }

  // optional double TradeIndicSize = 27;
  if (this->tradeindicsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(27, this->tradeindicsize(), output);
  }

  // optional int64 ExchMessageTimestamp = 28;
  if (this->exchmessagetimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->exchmessagetimestamp(), output);
  }

  // optional double CorrectionNewTradePrice = 30;
  if (this->correctionnewtradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(30, this->correctionnewtradeprice(), output);
  }

  // optional int64 CorrectionNewTradeSize = 31;
  if (this->correctionnewtradesize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->correctionnewtradesize(), output);
  }

  // optional double CorrectionPrevTradePrice = 32;
  if (this->correctionprevtradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(32, this->correctionprevtradeprice(), output);
  }

  // optional int64 CorrectionPrevTradeSize = 33;
  if (this->correctionprevtradesize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->correctionprevtradesize(), output);
  }

  // optional string EnumCurrency = 34;
  if (this->enumcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enumcurrency().data(), this->enumcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      34, this->enumcurrency(), output);
  }

  // optional int64 MaturityDate = 35;
  if (this->maturitydate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->maturitydate(), output);
  }

  // optional int32 MarketPhaseCode = 36;
  if (this->marketphasecode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(36, this->marketphasecode(), output);
  }

  // optional double Chg = 37;
  if (this->chg() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(37, this->chg(), output);
  }

  // optional double PctChg = 38;
  if (this->pctchg() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(38, this->pctchg(), output);
  }

  // optional double ClosePx = 39;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(39, this->closepx(), output);
  }

  // optional double HighPx = 40;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(40, this->highpx(), output);
  }

  // optional double LowPx = 41;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(41, this->lowpx(), output);
  }

  // optional double OpenPx = 42;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(42, this->openpx(), output);
  }

  // optional double PreClosePx = 43;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(43, this->preclosepx(), output);
  }

  // optional bool RecordDel = 45;
  if (this->recorddel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(45, this->recorddel(), output);
  }

  // optional string CurrencyString = 46;
  if (this->currencystring().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currencystring().data(), this->currencystring().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CurrencyString");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      46, this->currencystring(), output);
  }

  // optional string CurrentDatetime = 47;
  if (this->currentdatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currentdatetime().data(), this->currentdatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      47, this->currentdatetime(), output);
  }

  // optional int64 TradeOfficialDate = 48;
  if (this->tradeofficialdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(48, this->tradeofficialdate(), output);
  }

  // optional double TradeCondPrice = 49;
  if (this->tradecondprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(49, this->tradecondprice(), output);
  }

  // optional int64 TradeCondSize = 50;
  if (this->tradecondsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(50, this->tradecondsize(), output);
  }

  // optional int64 TradeOfficialTime = 51;
  if (this->tradeofficialtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(51, this->tradeofficialtime(), output);
  }

  // optional int64 TradeUniqueId = 52;
  if (this->tradeuniqueid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(52, this->tradeuniqueid(), output);
  }

  // optional int64 UpdateAction = 53;
  if (this->updateaction() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(53, this->updateaction(), output);
  }

  // optional int64 YestTradeVol = 54;
  if (this->yesttradevol() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(54, this->yesttradevol(), output);
  }

  // optional double Vwap = 55;
  if (this->vwap() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(55, this->vwap(), output);
  }

  // optional int64 TradeCondOfficialTime = 56;
  if (this->tradecondofficialtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(56, this->tradecondofficialtime(), output);
  }

  // optional int64 TradeCondOfficialDate = 57;
  if (this->tradecondofficialdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(57, this->tradecondofficialdate(), output);
  }

  // optional int64 CorrectionOfficialTradeTime = 58;
  if (this->correctionofficialtradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(58, this->correctionofficialtradetime(), output);
  }

  // optional int64 CorrectionOfficialTradeDate = 59;
  if (this->correctionofficialtradedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(59, this->correctionofficialtradedate(), output);
  }

  // optional int32 TransactionPriceInd = 60;
  if (this->transactionpriceind() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(60, this->transactionpriceind(), output);
  }

  // optional double CouponRate = 61;
  if (this->couponrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(61, this->couponrate(), output);
  }

  // optional int32 InstrStatus = 62;
  if (this->instrstatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(62, this->instrstatus(), output);
  }

  // optional int32 MsgType = 63;
  if (this->msgtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(63, this->msgtype(), output);
  }

  // optional int32 ControlMsgType = 64;
  if (this->controlmsgtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(64, this->controlmsgtype(), output);
  }

  // optional int64 WeightedAverageMaturity = 65;
  if (this->weightedaveragematurity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(65, this->weightedaveragematurity(), output);
  }

  // optional int64 HistoricalTradeSize = 66;
  if (this->historicaltradesize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(66, this->historicaltradesize(), output);
  }

  // optional int64 HistoricalCancelSize = 67;
  if (this->historicalcancelsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(67, this->historicalcancelsize(), output);
  }

  // optional int64 HistoricalCorrectionSize = 68;
  if (this->historicalcorrectionsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(68, this->historicalcorrectionsize(), output);
  }

  // optional int64 HistoricalTradeCond = 69;
  if (this->historicaltradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(69, this->historicaltradecond(), output);
  }

  // optional double HistoricalTradePrice = 70;
  if (this->historicaltradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(70, this->historicaltradeprice(), output);
  }

  // optional double HistoricalCancelPrice = 71;
  if (this->historicalcancelprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(71, this->historicalcancelprice(), output);
  }

  // optional double HistoricalCorrectionPrice = 72;
  if (this->historicalcorrectionprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(72, this->historicalcorrectionprice(), output);
  }

  // optional int64 ActionTime = 73;
  if (this->actiontime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(73, this->actiontime(), output);
  }

  // optional int64 ActionDate = 74;
  if (this->actiondate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(74, this->actiondate(), output);
  }

  // optional int32 ReasonCode = 75;
  if (this->reasoncode() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(75, this->reasoncode(), output);
  }

  // optional int64 HistoricalTradeIdentifier = 76;
  if (this->historicaltradeidentifier() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(76, this->historicaltradeidentifier(), output);
  }

  // optional int64 HistoricalOriginalMessageDate = 77;
  if (this->historicaloriginalmessagedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(77, this->historicaloriginalmessagedate(), output);
  }

  // optional int64 ExtendedTradeCond = 78;
  if (this->extendedtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(78, this->extendedtradecond(), output);
  }

  // optional double YieldHigh = 79;
  if (this->yieldhigh() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(79, this->yieldhigh(), output);
  }

  // optional double YieldLow = 80;
  if (this->yieldlow() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(80, this->yieldlow(), output);
  }

  // optional int64 HistoricalCancelTradeDate = 81;
  if (this->historicalcanceltradedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(81, this->historicalcanceltradedate(), output);
  }

  // optional double HistoricalYield = 82;
  if (this->historicalyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(82, this->historicalyield(), output);
  }

  // optional double HistoricalTradeIndicSize = 83;
  if (this->historicaltradeindicsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(83, this->historicaltradeindicsize(), output);
  }

  // optional int64 HistoricalExtendedTradeCond = 84;
  if (this->historicalextendedtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(84, this->historicalextendedtradecond(), output);
  }

  // optional int32 HistoricalPriceIndicator = 85;
  if (this->historicalpriceindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(85, this->historicalpriceindicator(), output);
  }

  // optional string SymbolSuffix = 86;
  if (this->symbolsuffix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolsuffix().data(), this->symbolsuffix().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      86, this->symbolsuffix(), output);
  }

  // optional int64 HistoricalTradeDate = 87;
  if (this->historicaltradedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(87, this->historicaltradedate(), output);
  }

  // optional int64 HistoricalTradeTime = 88;
  if (this->historicaltradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(88, this->historicaltradetime(), output);
  }

  // optional int32 CorrectionPrevSpecialPriceIndicator = 89;
  if (this->correctionprevspecialpriceindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(89, this->correctionprevspecialpriceindicator(), output);
  }

  // optional int64 CorrectionNewExtendedTradeCond = 90;
  if (this->correctionnewextendedtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(90, this->correctionnewextendedtradecond(), output);
  }

  // optional int64 CorrectionPrevExtendedTradeCond = 91;
  if (this->correctionprevextendedtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(91, this->correctionprevextendedtradecond(), output);
  }

  // optional double CorrectionPrevYield = 92;
  if (this->correctionprevyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(92, this->correctionprevyield(), output);
  }

  // optional double CorrectionNewYield = 93;
  if (this->correctionnewyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(93, this->correctionnewyield(), output);
  }

  // optional string CorrectionPrevTradeDate = 94;
  if (this->correctionprevtradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->correctionprevtradedate().data(), this->correctionprevtradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      94, this->correctionprevtradedate(), output);
  }

  // optional int64 CorrectionPrevTradeTime = 95;
  if (this->correctionprevtradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(95, this->correctionprevtradetime(), output);
  }

  // optional int32 CorrectionPrevTradeCond = 96;
  if (this->correctionprevtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(96, this->correctionprevtradecond(), output);
  }

  // optional int64 CorrectionNewTradeCond = 97;
  if (this->correctionnewtradecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(97, this->correctionnewtradecond(), output);
  }

  // optional int64 CancelTradeSeq = 98;
  if (this->canceltradeseq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(98, this->canceltradeseq(), output);
  }

  // optional int32 PrevTransactionPriceInd = 99;
  if (this->prevtransactionpriceind() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(99, this->prevtransactionpriceind(), output);
  }

  // optional int64 CorrectionNewTransactionPriceInd = 100;
  if (this->correctionnewtransactionpriceind() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(100, this->correctionnewtransactionpriceind(), output);
  }

  // optional int64 OriginalTradeSeq = 101;
  if (this->originaltradeseq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(101, this->originaltradeseq(), output);
  }

  // optional double YieldClose = 102;
  if (this->yieldclose() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(102, this->yieldclose(), output);
  }

  // optional int64 FirstSettlementDate = 103;
  if (this->firstsettlementdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(103, this->firstsettlementdate(), output);
  }

  // optional int64 ReportDate = 104;
  if (this->reportdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(104, this->reportdate(), output);
  }

  // optional int32 MarketPhase = 105;
  if (this->marketphase() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(105, this->marketphase(), output);
  }

  // optional int64 DisseminationDate = 106;
  if (this->disseminationdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(106, this->disseminationdate(), output);
  }

  // optional int64 CorrectionNewDisseminationDate = 107;
  if (this->correctionnewdisseminationdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(107, this->correctionnewdisseminationdate(), output);
  }

  // optional int64 CorrectionPrevDisseminationDate = 108;
  if (this->correctionprevdisseminationdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(108, this->correctionprevdisseminationdate(), output);
  }

  // optional int32 TradeCond1 = 109;
  if (this->tradecond1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(109, this->tradecond1(), output);
  }

  // optional int64 CorrectionPrevTradeUniqueId = 110;
  if (this->correctionprevtradeuniqueid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(110, this->correctionprevtradeuniqueid(), output);
  }

  // optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
  if (this->historicalcorrectionprevtradeuniqueid() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(111, this->historicalcorrectionprevtradeuniqueid(), output);
  }

  // optional int64 HistoricalCancelTradeTime = 112;
  if (this->historicalcanceltradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(112, this->historicalcanceltradetime(), output);
  }

  // optional int64 HistoricalCorrectionTradeTime = 113;
  if (this->historicalcorrectiontradetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(113, this->historicalcorrectiontradetime(), output);
  }

  // optional int64 HistoricalCorrectionTradeDate = 114;
  if (this->historicalcorrectiontradedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(114, this->historicalcorrectiontradedate(), output);
  }

  // optional int32 BondType = 115;
  if (this->bondtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(115, this->bondtype(), output);
  }

  // optional double Yield = 117;
  if (this->yield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(117, this->yield(), output);
  }

  // optional string Cusip = 118;
  if (this->cusip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cusip().data(), this->cusip().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.Cusip");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      118, this->cusip(), output);
  }

  // optional string SymbolExchTicker = 119;
  if (this->symbolexchticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolexchticker().data(), this->symbolexchticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      119, this->symbolexchticker(), output);
  }

  // optional string ISIN = 120;
  if (this->isin().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->isin().data(), this->isin().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ISIN");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      120, this->isin(), output);
  }

  // optional string InstrName2 = 121;
  if (this->instrname2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrname2().data(), this->instrname2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrName2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      121, this->instrname2(), output);
  }

  // optional string TradeSettlementDate = 122;
  if (this->tradesettlementdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradesettlementdate().data(), this->tradesettlementdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      122, this->tradesettlementdate(), output);
  }

  // optional int64 FirstTradingDate = 123;
  if (this->firsttradingdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(123, this->firsttradingdate(), output);
  }

  // optional string CouponType = 124;
  if (this->coupontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->coupontype().data(), this->coupontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CouponType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      124, this->coupontype(), output);
  }

  // optional int64 EnumInterestCalcType = 125;
  if (this->enuminterestcalctype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(125, this->enuminterestcalctype(), output);
  }

  // optional string IssuerName = 126;
  if (this->issuername().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->issuername().data(), this->issuername().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.IssuerName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      126, this->issuername(), output);
  }

  // optional int32 InvestmentGrade = 127;
  if (this->investmentgrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(127, this->investmentgrade(), output);
  }

  // optional string AmortizationType = 128;
  if (this->amortizationtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->amortizationtype().data(), this->amortizationtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.AmortizationType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      128, this->amortizationtype(), output);
  }

  // optional string DisseminationFlag = 129;
  if (this->disseminationflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->disseminationflag().data(), this->disseminationflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      129, this->disseminationflag(), output);
  }

  // optional string SymbolBloombergTicker = 130;
  if (this->symbolbloombergticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolbloombergticker().data(), this->symbolbloombergticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      130, this->symbolbloombergticker(), output);
  }

  // optional string BloombergGlobalId = 131;
  if (this->bloombergglobalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bloombergglobalid().data(), this->bloombergglobalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      131, this->bloombergglobalid(), output);
  }

  // optional string BloombergGlobalIdComp = 132;
  if (this->bloombergglobalidcomp().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bloombergglobalidcomp().data(), this->bloombergglobalidcomp().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      132, this->bloombergglobalidcomp(), output);
  }

  // optional string InstrLocalType2 = 133;
  if (this->instrlocaltype2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrlocaltype2().data(), this->instrlocaltype2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      133, this->instrlocaltype2(), output);
  }

  // optional string SymbolEsignalTicker = 134;
  if (this->symbolesignalticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolesignalticker().data(), this->symbolesignalticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      134, this->symbolesignalticker(), output);
  }

  // optional string InstrNameLocal = 135;
  if (this->instrnamelocal().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrnamelocal().data(), this->instrnamelocal().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      135, this->instrnamelocal(), output);
  }

  // optional string MktSegmentString = 136;
  if (this->mktsegmentstring().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mktsegmentstring().data(), this->mktsegmentstring().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      136, this->mktsegmentstring(), output);
  }

  // optional string ExchMonthCode = 137;
  if (this->exchmonthcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchmonthcode().data(), this->exchmonthcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      137, this->exchmonthcode(), output);
  }

  // optional int64 PoolNumber = 138;
  if (this->poolnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(138, this->poolnumber(), output);
  }

  // optional string MasterDealId = 139;
  if (this->masterdealid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->masterdealid().data(), this->masterdealid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.MasterDealId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      139, this->masterdealid(), output);
  }

  // optional string TrancheId = 140;
  if (this->trancheid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->trancheid().data(), this->trancheid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TrancheId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      140, this->trancheid(), output);
  }

  // optional string Indicator144A = 141;
  if (this->indicator144a().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indicator144a().data(), this->indicator144a().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.Indicator144A");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      141, this->indicator144a(), output);
  }

  // optional int64 NumberMaturityMonths = 142;
  if (this->numbermaturitymonths() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(142, this->numbermaturitymonths(), output);
  }

  // optional string DebtTypeCode = 143;
  if (this->debttypecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debttypecode().data(), this->debttypecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      143, this->debttypecode(), output);
  }

  // optional int64 TokenDel = 144;
  if (this->tokendel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(144, this->tokendel(), output);
  }

  // optional int64 CorrectionTradeSeq = 145;
  if (this->correctiontradeseq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(145, this->correctiontradeseq(), output);
  }

  // optional int32 RecordStaleInd = 146;
  if (this->recordstaleind() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(146, this->recordstaleind(), output);
  }

  // optional double WeightedAverageCoupon = 147;
  if (this->weightedaveragecoupon() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(147, this->weightedaveragecoupon(), output);
  }

  // optional int64 WeightedAverageLoanAge = 148;
  if (this->weightedaverageloanage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(148, this->weightedaverageloanage(), output);
  }

  // optional int64 WeightedAverageLoanSize = 149;
  if (this->weightedaverageloansize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(149, this->weightedaverageloansize(), output);
  }

  // optional int64 WeightedLoanValue = 150;
  if (this->weightedloanvalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(150, this->weightedloanvalue(), output);
  }

  // optional double AverageMonthlySize = 151;
  if (this->averagemonthlysize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(151, this->averagemonthlysize(), output);
  }

  // optional double PrevMonthVolDec = 152;
  if (this->prevmonthvoldec() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(152, this->prevmonthvoldec(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDIceTrace)
}

::google::protobuf::uint8* MDIceTrace::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDIceTrace)
  // optional int32 MDDate = 1;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->mddate(), target);
  }

  // optional int32 MDTime = 2;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mdtime(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->securityidsource(), target);
  }

  // optional string HTSCSecurityID = 5;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->htscsecurityid(), target);
  }

  // optional int32 ExchangeDate = 6;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 7;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangetime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->datamultiplepowerof10(), target);
  }

  // optional double TradePrice = 19;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->tradeprice(), target);
  }

  // optional int64 TradeSize = 20;
  if (this->tradesize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->tradesize(), target);
  }

  // optional double CurrentPrice = 21;
  if (this->currentprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(21, this->currentprice(), target);
  }

  // optional string ActivityDatetime = 22;
  if (this->activitydatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->activitydatetime().data(), this->activitydatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->activitydatetime(), target);
  }

  // optional string TradeDatetime = 23;
  if (this->tradedatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedatetime().data(), this->tradedatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->tradedatetime(), target);
  }

  // optional int64 TradeVol = 24;
  if (this->tradevol() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->tradevol(), target);
  }

  // optional int64 ExchSeq = 26;
  if (this->exchseq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->exchseq(), target);
  }

  // optional double TradeIndicSize = 27;
  if (this->tradeindicsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(27, this->tradeindicsize(), target);
  }

  // optional int64 ExchMessageTimestamp = 28;
  if (this->exchmessagetimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->exchmessagetimestamp(), target);
  }

  // optional double CorrectionNewTradePrice = 30;
  if (this->correctionnewtradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(30, this->correctionnewtradeprice(), target);
  }

  // optional int64 CorrectionNewTradeSize = 31;
  if (this->correctionnewtradesize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->correctionnewtradesize(), target);
  }

  // optional double CorrectionPrevTradePrice = 32;
  if (this->correctionprevtradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(32, this->correctionprevtradeprice(), target);
  }

  // optional int64 CorrectionPrevTradeSize = 33;
  if (this->correctionprevtradesize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->correctionprevtradesize(), target);
  }

  // optional string EnumCurrency = 34;
  if (this->enumcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enumcurrency().data(), this->enumcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        34, this->enumcurrency(), target);
  }

  // optional int64 MaturityDate = 35;
  if (this->maturitydate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->maturitydate(), target);
  }

  // optional int32 MarketPhaseCode = 36;
  if (this->marketphasecode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(36, this->marketphasecode(), target);
  }

  // optional double Chg = 37;
  if (this->chg() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(37, this->chg(), target);
  }

  // optional double PctChg = 38;
  if (this->pctchg() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(38, this->pctchg(), target);
  }

  // optional double ClosePx = 39;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(39, this->closepx(), target);
  }

  // optional double HighPx = 40;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(40, this->highpx(), target);
  }

  // optional double LowPx = 41;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(41, this->lowpx(), target);
  }

  // optional double OpenPx = 42;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(42, this->openpx(), target);
  }

  // optional double PreClosePx = 43;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(43, this->preclosepx(), target);
  }

  // optional bool RecordDel = 45;
  if (this->recorddel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(45, this->recorddel(), target);
  }

  // optional string CurrencyString = 46;
  if (this->currencystring().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currencystring().data(), this->currencystring().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CurrencyString");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        46, this->currencystring(), target);
  }

  // optional string CurrentDatetime = 47;
  if (this->currentdatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currentdatetime().data(), this->currentdatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        47, this->currentdatetime(), target);
  }

  // optional int64 TradeOfficialDate = 48;
  if (this->tradeofficialdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(48, this->tradeofficialdate(), target);
  }

  // optional double TradeCondPrice = 49;
  if (this->tradecondprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(49, this->tradecondprice(), target);
  }

  // optional int64 TradeCondSize = 50;
  if (this->tradecondsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(50, this->tradecondsize(), target);
  }

  // optional int64 TradeOfficialTime = 51;
  if (this->tradeofficialtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(51, this->tradeofficialtime(), target);
  }

  // optional int64 TradeUniqueId = 52;
  if (this->tradeuniqueid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(52, this->tradeuniqueid(), target);
  }

  // optional int64 UpdateAction = 53;
  if (this->updateaction() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(53, this->updateaction(), target);
  }

  // optional int64 YestTradeVol = 54;
  if (this->yesttradevol() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(54, this->yesttradevol(), target);
  }

  // optional double Vwap = 55;
  if (this->vwap() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(55, this->vwap(), target);
  }

  // optional int64 TradeCondOfficialTime = 56;
  if (this->tradecondofficialtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(56, this->tradecondofficialtime(), target);
  }

  // optional int64 TradeCondOfficialDate = 57;
  if (this->tradecondofficialdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(57, this->tradecondofficialdate(), target);
  }

  // optional int64 CorrectionOfficialTradeTime = 58;
  if (this->correctionofficialtradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(58, this->correctionofficialtradetime(), target);
  }

  // optional int64 CorrectionOfficialTradeDate = 59;
  if (this->correctionofficialtradedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(59, this->correctionofficialtradedate(), target);
  }

  // optional int32 TransactionPriceInd = 60;
  if (this->transactionpriceind() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(60, this->transactionpriceind(), target);
  }

  // optional double CouponRate = 61;
  if (this->couponrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(61, this->couponrate(), target);
  }

  // optional int32 InstrStatus = 62;
  if (this->instrstatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(62, this->instrstatus(), target);
  }

  // optional int32 MsgType = 63;
  if (this->msgtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(63, this->msgtype(), target);
  }

  // optional int32 ControlMsgType = 64;
  if (this->controlmsgtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(64, this->controlmsgtype(), target);
  }

  // optional int64 WeightedAverageMaturity = 65;
  if (this->weightedaveragematurity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(65, this->weightedaveragematurity(), target);
  }

  // optional int64 HistoricalTradeSize = 66;
  if (this->historicaltradesize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(66, this->historicaltradesize(), target);
  }

  // optional int64 HistoricalCancelSize = 67;
  if (this->historicalcancelsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(67, this->historicalcancelsize(), target);
  }

  // optional int64 HistoricalCorrectionSize = 68;
  if (this->historicalcorrectionsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(68, this->historicalcorrectionsize(), target);
  }

  // optional int64 HistoricalTradeCond = 69;
  if (this->historicaltradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(69, this->historicaltradecond(), target);
  }

  // optional double HistoricalTradePrice = 70;
  if (this->historicaltradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(70, this->historicaltradeprice(), target);
  }

  // optional double HistoricalCancelPrice = 71;
  if (this->historicalcancelprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(71, this->historicalcancelprice(), target);
  }

  // optional double HistoricalCorrectionPrice = 72;
  if (this->historicalcorrectionprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(72, this->historicalcorrectionprice(), target);
  }

  // optional int64 ActionTime = 73;
  if (this->actiontime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(73, this->actiontime(), target);
  }

  // optional int64 ActionDate = 74;
  if (this->actiondate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(74, this->actiondate(), target);
  }

  // optional int32 ReasonCode = 75;
  if (this->reasoncode() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(75, this->reasoncode(), target);
  }

  // optional int64 HistoricalTradeIdentifier = 76;
  if (this->historicaltradeidentifier() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(76, this->historicaltradeidentifier(), target);
  }

  // optional int64 HistoricalOriginalMessageDate = 77;
  if (this->historicaloriginalmessagedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(77, this->historicaloriginalmessagedate(), target);
  }

  // optional int64 ExtendedTradeCond = 78;
  if (this->extendedtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(78, this->extendedtradecond(), target);
  }

  // optional double YieldHigh = 79;
  if (this->yieldhigh() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(79, this->yieldhigh(), target);
  }

  // optional double YieldLow = 80;
  if (this->yieldlow() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(80, this->yieldlow(), target);
  }

  // optional int64 HistoricalCancelTradeDate = 81;
  if (this->historicalcanceltradedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(81, this->historicalcanceltradedate(), target);
  }

  // optional double HistoricalYield = 82;
  if (this->historicalyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(82, this->historicalyield(), target);
  }

  // optional double HistoricalTradeIndicSize = 83;
  if (this->historicaltradeindicsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(83, this->historicaltradeindicsize(), target);
  }

  // optional int64 HistoricalExtendedTradeCond = 84;
  if (this->historicalextendedtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(84, this->historicalextendedtradecond(), target);
  }

  // optional int32 HistoricalPriceIndicator = 85;
  if (this->historicalpriceindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(85, this->historicalpriceindicator(), target);
  }

  // optional string SymbolSuffix = 86;
  if (this->symbolsuffix().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolsuffix().data(), this->symbolsuffix().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        86, this->symbolsuffix(), target);
  }

  // optional int64 HistoricalTradeDate = 87;
  if (this->historicaltradedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(87, this->historicaltradedate(), target);
  }

  // optional int64 HistoricalTradeTime = 88;
  if (this->historicaltradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(88, this->historicaltradetime(), target);
  }

  // optional int32 CorrectionPrevSpecialPriceIndicator = 89;
  if (this->correctionprevspecialpriceindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(89, this->correctionprevspecialpriceindicator(), target);
  }

  // optional int64 CorrectionNewExtendedTradeCond = 90;
  if (this->correctionnewextendedtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(90, this->correctionnewextendedtradecond(), target);
  }

  // optional int64 CorrectionPrevExtendedTradeCond = 91;
  if (this->correctionprevextendedtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(91, this->correctionprevextendedtradecond(), target);
  }

  // optional double CorrectionPrevYield = 92;
  if (this->correctionprevyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(92, this->correctionprevyield(), target);
  }

  // optional double CorrectionNewYield = 93;
  if (this->correctionnewyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(93, this->correctionnewyield(), target);
  }

  // optional string CorrectionPrevTradeDate = 94;
  if (this->correctionprevtradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->correctionprevtradedate().data(), this->correctionprevtradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        94, this->correctionprevtradedate(), target);
  }

  // optional int64 CorrectionPrevTradeTime = 95;
  if (this->correctionprevtradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(95, this->correctionprevtradetime(), target);
  }

  // optional int32 CorrectionPrevTradeCond = 96;
  if (this->correctionprevtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(96, this->correctionprevtradecond(), target);
  }

  // optional int64 CorrectionNewTradeCond = 97;
  if (this->correctionnewtradecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(97, this->correctionnewtradecond(), target);
  }

  // optional int64 CancelTradeSeq = 98;
  if (this->canceltradeseq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(98, this->canceltradeseq(), target);
  }

  // optional int32 PrevTransactionPriceInd = 99;
  if (this->prevtransactionpriceind() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(99, this->prevtransactionpriceind(), target);
  }

  // optional int64 CorrectionNewTransactionPriceInd = 100;
  if (this->correctionnewtransactionpriceind() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(100, this->correctionnewtransactionpriceind(), target);
  }

  // optional int64 OriginalTradeSeq = 101;
  if (this->originaltradeseq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(101, this->originaltradeseq(), target);
  }

  // optional double YieldClose = 102;
  if (this->yieldclose() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(102, this->yieldclose(), target);
  }

  // optional int64 FirstSettlementDate = 103;
  if (this->firstsettlementdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(103, this->firstsettlementdate(), target);
  }

  // optional int64 ReportDate = 104;
  if (this->reportdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(104, this->reportdate(), target);
  }

  // optional int32 MarketPhase = 105;
  if (this->marketphase() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(105, this->marketphase(), target);
  }

  // optional int64 DisseminationDate = 106;
  if (this->disseminationdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(106, this->disseminationdate(), target);
  }

  // optional int64 CorrectionNewDisseminationDate = 107;
  if (this->correctionnewdisseminationdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(107, this->correctionnewdisseminationdate(), target);
  }

  // optional int64 CorrectionPrevDisseminationDate = 108;
  if (this->correctionprevdisseminationdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(108, this->correctionprevdisseminationdate(), target);
  }

  // optional int32 TradeCond1 = 109;
  if (this->tradecond1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(109, this->tradecond1(), target);
  }

  // optional int64 CorrectionPrevTradeUniqueId = 110;
  if (this->correctionprevtradeuniqueid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(110, this->correctionprevtradeuniqueid(), target);
  }

  // optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
  if (this->historicalcorrectionprevtradeuniqueid() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(111, this->historicalcorrectionprevtradeuniqueid(), target);
  }

  // optional int64 HistoricalCancelTradeTime = 112;
  if (this->historicalcanceltradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(112, this->historicalcanceltradetime(), target);
  }

  // optional int64 HistoricalCorrectionTradeTime = 113;
  if (this->historicalcorrectiontradetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(113, this->historicalcorrectiontradetime(), target);
  }

  // optional int64 HistoricalCorrectionTradeDate = 114;
  if (this->historicalcorrectiontradedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(114, this->historicalcorrectiontradedate(), target);
  }

  // optional int32 BondType = 115;
  if (this->bondtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(115, this->bondtype(), target);
  }

  // optional double Yield = 117;
  if (this->yield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(117, this->yield(), target);
  }

  // optional string Cusip = 118;
  if (this->cusip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cusip().data(), this->cusip().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.Cusip");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        118, this->cusip(), target);
  }

  // optional string SymbolExchTicker = 119;
  if (this->symbolexchticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolexchticker().data(), this->symbolexchticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        119, this->symbolexchticker(), target);
  }

  // optional string ISIN = 120;
  if (this->isin().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->isin().data(), this->isin().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ISIN");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        120, this->isin(), target);
  }

  // optional string InstrName2 = 121;
  if (this->instrname2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrname2().data(), this->instrname2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrName2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        121, this->instrname2(), target);
  }

  // optional string TradeSettlementDate = 122;
  if (this->tradesettlementdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradesettlementdate().data(), this->tradesettlementdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        122, this->tradesettlementdate(), target);
  }

  // optional int64 FirstTradingDate = 123;
  if (this->firsttradingdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(123, this->firsttradingdate(), target);
  }

  // optional string CouponType = 124;
  if (this->coupontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->coupontype().data(), this->coupontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.CouponType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        124, this->coupontype(), target);
  }

  // optional int64 EnumInterestCalcType = 125;
  if (this->enuminterestcalctype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(125, this->enuminterestcalctype(), target);
  }

  // optional string IssuerName = 126;
  if (this->issuername().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->issuername().data(), this->issuername().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.IssuerName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        126, this->issuername(), target);
  }

  // optional int32 InvestmentGrade = 127;
  if (this->investmentgrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(127, this->investmentgrade(), target);
  }

  // optional string AmortizationType = 128;
  if (this->amortizationtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->amortizationtype().data(), this->amortizationtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.AmortizationType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        128, this->amortizationtype(), target);
  }

  // optional string DisseminationFlag = 129;
  if (this->disseminationflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->disseminationflag().data(), this->disseminationflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        129, this->disseminationflag(), target);
  }

  // optional string SymbolBloombergTicker = 130;
  if (this->symbolbloombergticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolbloombergticker().data(), this->symbolbloombergticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        130, this->symbolbloombergticker(), target);
  }

  // optional string BloombergGlobalId = 131;
  if (this->bloombergglobalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bloombergglobalid().data(), this->bloombergglobalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        131, this->bloombergglobalid(), target);
  }

  // optional string BloombergGlobalIdComp = 132;
  if (this->bloombergglobalidcomp().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bloombergglobalidcomp().data(), this->bloombergglobalidcomp().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        132, this->bloombergglobalidcomp(), target);
  }

  // optional string InstrLocalType2 = 133;
  if (this->instrlocaltype2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrlocaltype2().data(), this->instrlocaltype2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        133, this->instrlocaltype2(), target);
  }

  // optional string SymbolEsignalTicker = 134;
  if (this->symbolesignalticker().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbolesignalticker().data(), this->symbolesignalticker().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        134, this->symbolesignalticker(), target);
  }

  // optional string InstrNameLocal = 135;
  if (this->instrnamelocal().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrnamelocal().data(), this->instrnamelocal().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        135, this->instrnamelocal(), target);
  }

  // optional string MktSegmentString = 136;
  if (this->mktsegmentstring().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mktsegmentstring().data(), this->mktsegmentstring().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        136, this->mktsegmentstring(), target);
  }

  // optional string ExchMonthCode = 137;
  if (this->exchmonthcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchmonthcode().data(), this->exchmonthcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        137, this->exchmonthcode(), target);
  }

  // optional int64 PoolNumber = 138;
  if (this->poolnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(138, this->poolnumber(), target);
  }

  // optional string MasterDealId = 139;
  if (this->masterdealid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->masterdealid().data(), this->masterdealid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.MasterDealId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        139, this->masterdealid(), target);
  }

  // optional string TrancheId = 140;
  if (this->trancheid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->trancheid().data(), this->trancheid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.TrancheId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        140, this->trancheid(), target);
  }

  // optional string Indicator144A = 141;
  if (this->indicator144a().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->indicator144a().data(), this->indicator144a().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.Indicator144A");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        141, this->indicator144a(), target);
  }

  // optional int64 NumberMaturityMonths = 142;
  if (this->numbermaturitymonths() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(142, this->numbermaturitymonths(), target);
  }

  // optional string DebtTypeCode = 143;
  if (this->debttypecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->debttypecode().data(), this->debttypecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        143, this->debttypecode(), target);
  }

  // optional int64 TokenDel = 144;
  if (this->tokendel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(144, this->tokendel(), target);
  }

  // optional int64 CorrectionTradeSeq = 145;
  if (this->correctiontradeseq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(145, this->correctiontradeseq(), target);
  }

  // optional int32 RecordStaleInd = 146;
  if (this->recordstaleind() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(146, this->recordstaleind(), target);
  }

  // optional double WeightedAverageCoupon = 147;
  if (this->weightedaveragecoupon() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(147, this->weightedaveragecoupon(), target);
  }

  // optional int64 WeightedAverageLoanAge = 148;
  if (this->weightedaverageloanage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(148, this->weightedaverageloanage(), target);
  }

  // optional int64 WeightedAverageLoanSize = 149;
  if (this->weightedaverageloansize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(149, this->weightedaverageloansize(), target);
  }

  // optional int64 WeightedLoanValue = 150;
  if (this->weightedloanvalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(150, this->weightedloanvalue(), target);
  }

  // optional double AverageMonthlySize = 151;
  if (this->averagemonthlysize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(151, this->averagemonthlysize(), target);
  }

  // optional double PrevMonthVolDec = 152;
  if (this->prevmonthvoldec() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(152, this->prevmonthvoldec(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDIceTrace)
  return target;
}

size_t MDIceTrace::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDIceTrace)
  size_t total_size = 0;

  // optional int32 MDDate = 1;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 2;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional string HTSCSecurityID = 5;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 ExchangeDate = 6;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 7;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional double TradePrice = 19;
  if (this->tradeprice() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 TradeSize = 20;
  if (this->tradesize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradesize());
  }

  // optional double CurrentPrice = 21;
  if (this->currentprice() != 0) {
    total_size += 2 + 8;
  }

  // optional string ActivityDatetime = 22;
  if (this->activitydatetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->activitydatetime());
  }

  // optional string TradeDatetime = 23;
  if (this->tradedatetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedatetime());
  }

  // optional int64 TradeVol = 24;
  if (this->tradevol() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradevol());
  }

  // optional int64 ExchSeq = 26;
  if (this->exchseq() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->exchseq());
  }

  // optional double TradeIndicSize = 27;
  if (this->tradeindicsize() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 ExchMessageTimestamp = 28;
  if (this->exchmessagetimestamp() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->exchmessagetimestamp());
  }

  // optional double CorrectionNewTradePrice = 30;
  if (this->correctionnewtradeprice() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 CorrectionNewTradeSize = 31;
  if (this->correctionnewtradesize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionnewtradesize());
  }

  // optional double CorrectionPrevTradePrice = 32;
  if (this->correctionprevtradeprice() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 CorrectionPrevTradeSize = 33;
  if (this->correctionprevtradesize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionprevtradesize());
  }

  // optional string EnumCurrency = 34;
  if (this->enumcurrency().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->enumcurrency());
  }

  // optional int64 MaturityDate = 35;
  if (this->maturitydate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maturitydate());
  }

  // optional int32 MarketPhaseCode = 36;
  if (this->marketphasecode() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->marketphasecode());
  }

  // optional double Chg = 37;
  if (this->chg() != 0) {
    total_size += 2 + 8;
  }

  // optional double PctChg = 38;
  if (this->pctchg() != 0) {
    total_size += 2 + 8;
  }

  // optional double ClosePx = 39;
  if (this->closepx() != 0) {
    total_size += 2 + 8;
  }

  // optional double HighPx = 40;
  if (this->highpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double LowPx = 41;
  if (this->lowpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double OpenPx = 42;
  if (this->openpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreClosePx = 43;
  if (this->preclosepx() != 0) {
    total_size += 2 + 8;
  }

  // optional bool RecordDel = 45;
  if (this->recorddel() != 0) {
    total_size += 2 + 1;
  }

  // optional string CurrencyString = 46;
  if (this->currencystring().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->currencystring());
  }

  // optional string CurrentDatetime = 47;
  if (this->currentdatetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->currentdatetime());
  }

  // optional int64 TradeOfficialDate = 48;
  if (this->tradeofficialdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeofficialdate());
  }

  // optional double TradeCondPrice = 49;
  if (this->tradecondprice() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 TradeCondSize = 50;
  if (this->tradecondsize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradecondsize());
  }

  // optional int64 TradeOfficialTime = 51;
  if (this->tradeofficialtime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeofficialtime());
  }

  // optional int64 TradeUniqueId = 52;
  if (this->tradeuniqueid() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeuniqueid());
  }

  // optional int64 UpdateAction = 53;
  if (this->updateaction() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->updateaction());
  }

  // optional int64 YestTradeVol = 54;
  if (this->yesttradevol() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->yesttradevol());
  }

  // optional double Vwap = 55;
  if (this->vwap() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 TradeCondOfficialTime = 56;
  if (this->tradecondofficialtime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradecondofficialtime());
  }

  // optional int64 TradeCondOfficialDate = 57;
  if (this->tradecondofficialdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradecondofficialdate());
  }

  // optional int64 CorrectionOfficialTradeTime = 58;
  if (this->correctionofficialtradetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionofficialtradetime());
  }

  // optional int64 CorrectionOfficialTradeDate = 59;
  if (this->correctionofficialtradedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionofficialtradedate());
  }

  // optional int32 TransactionPriceInd = 60;
  if (this->transactionpriceind() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->transactionpriceind());
  }

  // optional double CouponRate = 61;
  if (this->couponrate() != 0) {
    total_size += 2 + 8;
  }

  // optional int32 InstrStatus = 62;
  if (this->instrstatus() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->instrstatus());
  }

  // optional int32 MsgType = 63;
  if (this->msgtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->msgtype());
  }

  // optional int32 ControlMsgType = 64;
  if (this->controlmsgtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->controlmsgtype());
  }

  // optional int64 WeightedAverageMaturity = 65;
  if (this->weightedaveragematurity() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedaveragematurity());
  }

  // optional int64 HistoricalTradeSize = 66;
  if (this->historicaltradesize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaltradesize());
  }

  // optional int64 HistoricalCancelSize = 67;
  if (this->historicalcancelsize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcancelsize());
  }

  // optional int64 HistoricalCorrectionSize = 68;
  if (this->historicalcorrectionsize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcorrectionsize());
  }

  // optional int64 HistoricalTradeCond = 69;
  if (this->historicaltradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaltradecond());
  }

  // optional double HistoricalTradePrice = 70;
  if (this->historicaltradeprice() != 0) {
    total_size += 2 + 8;
  }

  // optional double HistoricalCancelPrice = 71;
  if (this->historicalcancelprice() != 0) {
    total_size += 2 + 8;
  }

  // optional double HistoricalCorrectionPrice = 72;
  if (this->historicalcorrectionprice() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 ActionTime = 73;
  if (this->actiontime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->actiontime());
  }

  // optional int64 ActionDate = 74;
  if (this->actiondate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->actiondate());
  }

  // optional int32 ReasonCode = 75;
  if (this->reasoncode() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->reasoncode());
  }

  // optional int64 HistoricalTradeIdentifier = 76;
  if (this->historicaltradeidentifier() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaltradeidentifier());
  }

  // optional int64 HistoricalOriginalMessageDate = 77;
  if (this->historicaloriginalmessagedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaloriginalmessagedate());
  }

  // optional int64 ExtendedTradeCond = 78;
  if (this->extendedtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->extendedtradecond());
  }

  // optional double YieldHigh = 79;
  if (this->yieldhigh() != 0) {
    total_size += 2 + 8;
  }

  // optional double YieldLow = 80;
  if (this->yieldlow() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 HistoricalCancelTradeDate = 81;
  if (this->historicalcanceltradedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcanceltradedate());
  }

  // optional double HistoricalYield = 82;
  if (this->historicalyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double HistoricalTradeIndicSize = 83;
  if (this->historicaltradeindicsize() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 HistoricalExtendedTradeCond = 84;
  if (this->historicalextendedtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalextendedtradecond());
  }

  // optional int32 HistoricalPriceIndicator = 85;
  if (this->historicalpriceindicator() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->historicalpriceindicator());
  }

  // optional string SymbolSuffix = 86;
  if (this->symbolsuffix().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbolsuffix());
  }

  // optional int64 HistoricalTradeDate = 87;
  if (this->historicaltradedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaltradedate());
  }

  // optional int64 HistoricalTradeTime = 88;
  if (this->historicaltradetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicaltradetime());
  }

  // optional int32 CorrectionPrevSpecialPriceIndicator = 89;
  if (this->correctionprevspecialpriceindicator() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->correctionprevspecialpriceindicator());
  }

  // optional int64 CorrectionNewExtendedTradeCond = 90;
  if (this->correctionnewextendedtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionnewextendedtradecond());
  }

  // optional int64 CorrectionPrevExtendedTradeCond = 91;
  if (this->correctionprevextendedtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionprevextendedtradecond());
  }

  // optional double CorrectionPrevYield = 92;
  if (this->correctionprevyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double CorrectionNewYield = 93;
  if (this->correctionnewyield() != 0) {
    total_size += 2 + 8;
  }

  // optional string CorrectionPrevTradeDate = 94;
  if (this->correctionprevtradedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->correctionprevtradedate());
  }

  // optional int64 CorrectionPrevTradeTime = 95;
  if (this->correctionprevtradetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionprevtradetime());
  }

  // optional int32 CorrectionPrevTradeCond = 96;
  if (this->correctionprevtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->correctionprevtradecond());
  }

  // optional int64 CorrectionNewTradeCond = 97;
  if (this->correctionnewtradecond() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionnewtradecond());
  }

  // optional int64 CancelTradeSeq = 98;
  if (this->canceltradeseq() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->canceltradeseq());
  }

  // optional int32 PrevTransactionPriceInd = 99;
  if (this->prevtransactionpriceind() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->prevtransactionpriceind());
  }

  // optional int64 CorrectionNewTransactionPriceInd = 100;
  if (this->correctionnewtransactionpriceind() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionnewtransactionpriceind());
  }

  // optional int64 OriginalTradeSeq = 101;
  if (this->originaltradeseq() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->originaltradeseq());
  }

  // optional double YieldClose = 102;
  if (this->yieldclose() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 FirstSettlementDate = 103;
  if (this->firstsettlementdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->firstsettlementdate());
  }

  // optional int64 ReportDate = 104;
  if (this->reportdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->reportdate());
  }

  // optional int32 MarketPhase = 105;
  if (this->marketphase() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->marketphase());
  }

  // optional int64 DisseminationDate = 106;
  if (this->disseminationdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->disseminationdate());
  }

  // optional int64 CorrectionNewDisseminationDate = 107;
  if (this->correctionnewdisseminationdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionnewdisseminationdate());
  }

  // optional int64 CorrectionPrevDisseminationDate = 108;
  if (this->correctionprevdisseminationdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionprevdisseminationdate());
  }

  // optional int32 TradeCond1 = 109;
  if (this->tradecond1() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradecond1());
  }

  // optional int64 CorrectionPrevTradeUniqueId = 110;
  if (this->correctionprevtradeuniqueid() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctionprevtradeuniqueid());
  }

  // optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
  if (this->historicalcorrectionprevtradeuniqueid() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcorrectionprevtradeuniqueid());
  }

  // optional int64 HistoricalCancelTradeTime = 112;
  if (this->historicalcanceltradetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcanceltradetime());
  }

  // optional int64 HistoricalCorrectionTradeTime = 113;
  if (this->historicalcorrectiontradetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcorrectiontradetime());
  }

  // optional int64 HistoricalCorrectionTradeDate = 114;
  if (this->historicalcorrectiontradedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historicalcorrectiontradedate());
  }

  // optional int32 BondType = 115;
  if (this->bondtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bondtype());
  }

  // optional double Yield = 117;
  if (this->yield() != 0) {
    total_size += 2 + 8;
  }

  // optional string Cusip = 118;
  if (this->cusip().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cusip());
  }

  // optional string SymbolExchTicker = 119;
  if (this->symbolexchticker().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbolexchticker());
  }

  // optional string ISIN = 120;
  if (this->isin().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->isin());
  }

  // optional string InstrName2 = 121;
  if (this->instrname2().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrname2());
  }

  // optional string TradeSettlementDate = 122;
  if (this->tradesettlementdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradesettlementdate());
  }

  // optional int64 FirstTradingDate = 123;
  if (this->firsttradingdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->firsttradingdate());
  }

  // optional string CouponType = 124;
  if (this->coupontype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->coupontype());
  }

  // optional int64 EnumInterestCalcType = 125;
  if (this->enuminterestcalctype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->enuminterestcalctype());
  }

  // optional string IssuerName = 126;
  if (this->issuername().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->issuername());
  }

  // optional int32 InvestmentGrade = 127;
  if (this->investmentgrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->investmentgrade());
  }

  // optional string AmortizationType = 128;
  if (this->amortizationtype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->amortizationtype());
  }

  // optional string DisseminationFlag = 129;
  if (this->disseminationflag().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->disseminationflag());
  }

  // optional string SymbolBloombergTicker = 130;
  if (this->symbolbloombergticker().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbolbloombergticker());
  }

  // optional string BloombergGlobalId = 131;
  if (this->bloombergglobalid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bloombergglobalid());
  }

  // optional string BloombergGlobalIdComp = 132;
  if (this->bloombergglobalidcomp().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bloombergglobalidcomp());
  }

  // optional string InstrLocalType2 = 133;
  if (this->instrlocaltype2().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrlocaltype2());
  }

  // optional string SymbolEsignalTicker = 134;
  if (this->symbolesignalticker().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbolesignalticker());
  }

  // optional string InstrNameLocal = 135;
  if (this->instrnamelocal().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrnamelocal());
  }

  // optional string MktSegmentString = 136;
  if (this->mktsegmentstring().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->mktsegmentstring());
  }

  // optional string ExchMonthCode = 137;
  if (this->exchmonthcode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchmonthcode());
  }

  // optional int64 PoolNumber = 138;
  if (this->poolnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->poolnumber());
  }

  // optional string MasterDealId = 139;
  if (this->masterdealid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->masterdealid());
  }

  // optional string TrancheId = 140;
  if (this->trancheid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->trancheid());
  }

  // optional string Indicator144A = 141;
  if (this->indicator144a().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->indicator144a());
  }

  // optional int64 NumberMaturityMonths = 142;
  if (this->numbermaturitymonths() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numbermaturitymonths());
  }

  // optional string DebtTypeCode = 143;
  if (this->debttypecode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->debttypecode());
  }

  // optional int64 TokenDel = 144;
  if (this->tokendel() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tokendel());
  }

  // optional int64 CorrectionTradeSeq = 145;
  if (this->correctiontradeseq() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->correctiontradeseq());
  }

  // optional int32 RecordStaleInd = 146;
  if (this->recordstaleind() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->recordstaleind());
  }

  // optional double WeightedAverageCoupon = 147;
  if (this->weightedaveragecoupon() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 WeightedAverageLoanAge = 148;
  if (this->weightedaverageloanage() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedaverageloanage());
  }

  // optional int64 WeightedAverageLoanSize = 149;
  if (this->weightedaverageloansize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedaverageloansize());
  }

  // optional int64 WeightedLoanValue = 150;
  if (this->weightedloanvalue() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedloanvalue());
  }

  // optional double AverageMonthlySize = 151;
  if (this->averagemonthlysize() != 0) {
    total_size += 2 + 8;
  }

  // optional double PrevMonthVolDec = 152;
  if (this->prevmonthvoldec() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDIceTrace::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDIceTrace)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDIceTrace* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDIceTrace>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDIceTrace)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDIceTrace)
    UnsafeMergeFrom(*source);
  }
}

void MDIceTrace::MergeFrom(const MDIceTrace& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDIceTrace)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDIceTrace::UnsafeMergeFrom(const MDIceTrace& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.tradesize() != 0) {
    set_tradesize(from.tradesize());
  }
  if (from.currentprice() != 0) {
    set_currentprice(from.currentprice());
  }
  if (from.activitydatetime().size() > 0) {

    activitydatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.activitydatetime_);
  }
  if (from.tradedatetime().size() > 0) {

    tradedatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedatetime_);
  }
  if (from.tradevol() != 0) {
    set_tradevol(from.tradevol());
  }
  if (from.exchseq() != 0) {
    set_exchseq(from.exchseq());
  }
  if (from.tradeindicsize() != 0) {
    set_tradeindicsize(from.tradeindicsize());
  }
  if (from.exchmessagetimestamp() != 0) {
    set_exchmessagetimestamp(from.exchmessagetimestamp());
  }
  if (from.correctionnewtradeprice() != 0) {
    set_correctionnewtradeprice(from.correctionnewtradeprice());
  }
  if (from.correctionnewtradesize() != 0) {
    set_correctionnewtradesize(from.correctionnewtradesize());
  }
  if (from.correctionprevtradeprice() != 0) {
    set_correctionprevtradeprice(from.correctionprevtradeprice());
  }
  if (from.correctionprevtradesize() != 0) {
    set_correctionprevtradesize(from.correctionprevtradesize());
  }
  if (from.enumcurrency().size() > 0) {

    enumcurrency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.enumcurrency_);
  }
  if (from.maturitydate() != 0) {
    set_maturitydate(from.maturitydate());
  }
  if (from.marketphasecode() != 0) {
    set_marketphasecode(from.marketphasecode());
  }
  if (from.chg() != 0) {
    set_chg(from.chg());
  }
  if (from.pctchg() != 0) {
    set_pctchg(from.pctchg());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.recorddel() != 0) {
    set_recorddel(from.recorddel());
  }
  if (from.currencystring().size() > 0) {

    currencystring_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.currencystring_);
  }
  if (from.currentdatetime().size() > 0) {

    currentdatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.currentdatetime_);
  }
  if (from.tradeofficialdate() != 0) {
    set_tradeofficialdate(from.tradeofficialdate());
  }
  if (from.tradecondprice() != 0) {
    set_tradecondprice(from.tradecondprice());
  }
  if (from.tradecondsize() != 0) {
    set_tradecondsize(from.tradecondsize());
  }
  if (from.tradeofficialtime() != 0) {
    set_tradeofficialtime(from.tradeofficialtime());
  }
  if (from.tradeuniqueid() != 0) {
    set_tradeuniqueid(from.tradeuniqueid());
  }
  if (from.updateaction() != 0) {
    set_updateaction(from.updateaction());
  }
  if (from.yesttradevol() != 0) {
    set_yesttradevol(from.yesttradevol());
  }
  if (from.vwap() != 0) {
    set_vwap(from.vwap());
  }
  if (from.tradecondofficialtime() != 0) {
    set_tradecondofficialtime(from.tradecondofficialtime());
  }
  if (from.tradecondofficialdate() != 0) {
    set_tradecondofficialdate(from.tradecondofficialdate());
  }
  if (from.correctionofficialtradetime() != 0) {
    set_correctionofficialtradetime(from.correctionofficialtradetime());
  }
  if (from.correctionofficialtradedate() != 0) {
    set_correctionofficialtradedate(from.correctionofficialtradedate());
  }
  if (from.transactionpriceind() != 0) {
    set_transactionpriceind(from.transactionpriceind());
  }
  if (from.couponrate() != 0) {
    set_couponrate(from.couponrate());
  }
  if (from.instrstatus() != 0) {
    set_instrstatus(from.instrstatus());
  }
  if (from.msgtype() != 0) {
    set_msgtype(from.msgtype());
  }
  if (from.controlmsgtype() != 0) {
    set_controlmsgtype(from.controlmsgtype());
  }
  if (from.weightedaveragematurity() != 0) {
    set_weightedaveragematurity(from.weightedaveragematurity());
  }
  if (from.historicaltradesize() != 0) {
    set_historicaltradesize(from.historicaltradesize());
  }
  if (from.historicalcancelsize() != 0) {
    set_historicalcancelsize(from.historicalcancelsize());
  }
  if (from.historicalcorrectionsize() != 0) {
    set_historicalcorrectionsize(from.historicalcorrectionsize());
  }
  if (from.historicaltradecond() != 0) {
    set_historicaltradecond(from.historicaltradecond());
  }
  if (from.historicaltradeprice() != 0) {
    set_historicaltradeprice(from.historicaltradeprice());
  }
  if (from.historicalcancelprice() != 0) {
    set_historicalcancelprice(from.historicalcancelprice());
  }
  if (from.historicalcorrectionprice() != 0) {
    set_historicalcorrectionprice(from.historicalcorrectionprice());
  }
  if (from.actiontime() != 0) {
    set_actiontime(from.actiontime());
  }
  if (from.actiondate() != 0) {
    set_actiondate(from.actiondate());
  }
  if (from.reasoncode() != 0) {
    set_reasoncode(from.reasoncode());
  }
  if (from.historicaltradeidentifier() != 0) {
    set_historicaltradeidentifier(from.historicaltradeidentifier());
  }
  if (from.historicaloriginalmessagedate() != 0) {
    set_historicaloriginalmessagedate(from.historicaloriginalmessagedate());
  }
  if (from.extendedtradecond() != 0) {
    set_extendedtradecond(from.extendedtradecond());
  }
  if (from.yieldhigh() != 0) {
    set_yieldhigh(from.yieldhigh());
  }
  if (from.yieldlow() != 0) {
    set_yieldlow(from.yieldlow());
  }
  if (from.historicalcanceltradedate() != 0) {
    set_historicalcanceltradedate(from.historicalcanceltradedate());
  }
  if (from.historicalyield() != 0) {
    set_historicalyield(from.historicalyield());
  }
  if (from.historicaltradeindicsize() != 0) {
    set_historicaltradeindicsize(from.historicaltradeindicsize());
  }
  if (from.historicalextendedtradecond() != 0) {
    set_historicalextendedtradecond(from.historicalextendedtradecond());
  }
  if (from.historicalpriceindicator() != 0) {
    set_historicalpriceindicator(from.historicalpriceindicator());
  }
  if (from.symbolsuffix().size() > 0) {

    symbolsuffix_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbolsuffix_);
  }
  if (from.historicaltradedate() != 0) {
    set_historicaltradedate(from.historicaltradedate());
  }
  if (from.historicaltradetime() != 0) {
    set_historicaltradetime(from.historicaltradetime());
  }
  if (from.correctionprevspecialpriceindicator() != 0) {
    set_correctionprevspecialpriceindicator(from.correctionprevspecialpriceindicator());
  }
  if (from.correctionnewextendedtradecond() != 0) {
    set_correctionnewextendedtradecond(from.correctionnewextendedtradecond());
  }
  if (from.correctionprevextendedtradecond() != 0) {
    set_correctionprevextendedtradecond(from.correctionprevextendedtradecond());
  }
  if (from.correctionprevyield() != 0) {
    set_correctionprevyield(from.correctionprevyield());
  }
  if (from.correctionnewyield() != 0) {
    set_correctionnewyield(from.correctionnewyield());
  }
  if (from.correctionprevtradedate().size() > 0) {

    correctionprevtradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.correctionprevtradedate_);
  }
  if (from.correctionprevtradetime() != 0) {
    set_correctionprevtradetime(from.correctionprevtradetime());
  }
  if (from.correctionprevtradecond() != 0) {
    set_correctionprevtradecond(from.correctionprevtradecond());
  }
  if (from.correctionnewtradecond() != 0) {
    set_correctionnewtradecond(from.correctionnewtradecond());
  }
  if (from.canceltradeseq() != 0) {
    set_canceltradeseq(from.canceltradeseq());
  }
  if (from.prevtransactionpriceind() != 0) {
    set_prevtransactionpriceind(from.prevtransactionpriceind());
  }
  if (from.correctionnewtransactionpriceind() != 0) {
    set_correctionnewtransactionpriceind(from.correctionnewtransactionpriceind());
  }
  if (from.originaltradeseq() != 0) {
    set_originaltradeseq(from.originaltradeseq());
  }
  if (from.yieldclose() != 0) {
    set_yieldclose(from.yieldclose());
  }
  if (from.firstsettlementdate() != 0) {
    set_firstsettlementdate(from.firstsettlementdate());
  }
  if (from.reportdate() != 0) {
    set_reportdate(from.reportdate());
  }
  if (from.marketphase() != 0) {
    set_marketphase(from.marketphase());
  }
  if (from.disseminationdate() != 0) {
    set_disseminationdate(from.disseminationdate());
  }
  if (from.correctionnewdisseminationdate() != 0) {
    set_correctionnewdisseminationdate(from.correctionnewdisseminationdate());
  }
  if (from.correctionprevdisseminationdate() != 0) {
    set_correctionprevdisseminationdate(from.correctionprevdisseminationdate());
  }
  if (from.tradecond1() != 0) {
    set_tradecond1(from.tradecond1());
  }
  if (from.correctionprevtradeuniqueid() != 0) {
    set_correctionprevtradeuniqueid(from.correctionprevtradeuniqueid());
  }
  if (from.historicalcorrectionprevtradeuniqueid() != 0) {
    set_historicalcorrectionprevtradeuniqueid(from.historicalcorrectionprevtradeuniqueid());
  }
  if (from.historicalcanceltradetime() != 0) {
    set_historicalcanceltradetime(from.historicalcanceltradetime());
  }
  if (from.historicalcorrectiontradetime() != 0) {
    set_historicalcorrectiontradetime(from.historicalcorrectiontradetime());
  }
  if (from.historicalcorrectiontradedate() != 0) {
    set_historicalcorrectiontradedate(from.historicalcorrectiontradedate());
  }
  if (from.bondtype() != 0) {
    set_bondtype(from.bondtype());
  }
  if (from.yield() != 0) {
    set_yield(from.yield());
  }
  if (from.cusip().size() > 0) {

    cusip_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cusip_);
  }
  if (from.symbolexchticker().size() > 0) {

    symbolexchticker_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbolexchticker_);
  }
  if (from.isin().size() > 0) {

    isin_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.isin_);
  }
  if (from.instrname2().size() > 0) {

    instrname2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrname2_);
  }
  if (from.tradesettlementdate().size() > 0) {

    tradesettlementdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradesettlementdate_);
  }
  if (from.firsttradingdate() != 0) {
    set_firsttradingdate(from.firsttradingdate());
  }
  if (from.coupontype().size() > 0) {

    coupontype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.coupontype_);
  }
  if (from.enuminterestcalctype() != 0) {
    set_enuminterestcalctype(from.enuminterestcalctype());
  }
  if (from.issuername().size() > 0) {

    issuername_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.issuername_);
  }
  if (from.investmentgrade() != 0) {
    set_investmentgrade(from.investmentgrade());
  }
  if (from.amortizationtype().size() > 0) {

    amortizationtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.amortizationtype_);
  }
  if (from.disseminationflag().size() > 0) {

    disseminationflag_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.disseminationflag_);
  }
  if (from.symbolbloombergticker().size() > 0) {

    symbolbloombergticker_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbolbloombergticker_);
  }
  if (from.bloombergglobalid().size() > 0) {

    bloombergglobalid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bloombergglobalid_);
  }
  if (from.bloombergglobalidcomp().size() > 0) {

    bloombergglobalidcomp_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bloombergglobalidcomp_);
  }
  if (from.instrlocaltype2().size() > 0) {

    instrlocaltype2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrlocaltype2_);
  }
  if (from.symbolesignalticker().size() > 0) {

    symbolesignalticker_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbolesignalticker_);
  }
  if (from.instrnamelocal().size() > 0) {

    instrnamelocal_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrnamelocal_);
  }
  if (from.mktsegmentstring().size() > 0) {

    mktsegmentstring_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.mktsegmentstring_);
  }
  if (from.exchmonthcode().size() > 0) {

    exchmonthcode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchmonthcode_);
  }
  if (from.poolnumber() != 0) {
    set_poolnumber(from.poolnumber());
  }
  if (from.masterdealid().size() > 0) {

    masterdealid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.masterdealid_);
  }
  if (from.trancheid().size() > 0) {

    trancheid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.trancheid_);
  }
  if (from.indicator144a().size() > 0) {

    indicator144a_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.indicator144a_);
  }
  if (from.numbermaturitymonths() != 0) {
    set_numbermaturitymonths(from.numbermaturitymonths());
  }
  if (from.debttypecode().size() > 0) {

    debttypecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.debttypecode_);
  }
  if (from.tokendel() != 0) {
    set_tokendel(from.tokendel());
  }
  if (from.correctiontradeseq() != 0) {
    set_correctiontradeseq(from.correctiontradeseq());
  }
  if (from.recordstaleind() != 0) {
    set_recordstaleind(from.recordstaleind());
  }
  if (from.weightedaveragecoupon() != 0) {
    set_weightedaveragecoupon(from.weightedaveragecoupon());
  }
  if (from.weightedaverageloanage() != 0) {
    set_weightedaverageloanage(from.weightedaverageloanage());
  }
  if (from.weightedaverageloansize() != 0) {
    set_weightedaverageloansize(from.weightedaverageloansize());
  }
  if (from.weightedloanvalue() != 0) {
    set_weightedloanvalue(from.weightedloanvalue());
  }
  if (from.averagemonthlysize() != 0) {
    set_averagemonthlysize(from.averagemonthlysize());
  }
  if (from.prevmonthvoldec() != 0) {
    set_prevmonthvoldec(from.prevmonthvoldec());
  }
}

void MDIceTrace::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDIceTrace)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDIceTrace::CopyFrom(const MDIceTrace& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDIceTrace)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDIceTrace::IsInitialized() const {

  return true;
}

void MDIceTrace::Swap(MDIceTrace* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDIceTrace::InternalSwap(MDIceTrace* other) {
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradesize_, other->tradesize_);
  std::swap(currentprice_, other->currentprice_);
  activitydatetime_.Swap(&other->activitydatetime_);
  tradedatetime_.Swap(&other->tradedatetime_);
  std::swap(tradevol_, other->tradevol_);
  std::swap(exchseq_, other->exchseq_);
  std::swap(tradeindicsize_, other->tradeindicsize_);
  std::swap(exchmessagetimestamp_, other->exchmessagetimestamp_);
  std::swap(correctionnewtradeprice_, other->correctionnewtradeprice_);
  std::swap(correctionnewtradesize_, other->correctionnewtradesize_);
  std::swap(correctionprevtradeprice_, other->correctionprevtradeprice_);
  std::swap(correctionprevtradesize_, other->correctionprevtradesize_);
  enumcurrency_.Swap(&other->enumcurrency_);
  std::swap(maturitydate_, other->maturitydate_);
  std::swap(marketphasecode_, other->marketphasecode_);
  std::swap(chg_, other->chg_);
  std::swap(pctchg_, other->pctchg_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(recorddel_, other->recorddel_);
  currencystring_.Swap(&other->currencystring_);
  currentdatetime_.Swap(&other->currentdatetime_);
  std::swap(tradeofficialdate_, other->tradeofficialdate_);
  std::swap(tradecondprice_, other->tradecondprice_);
  std::swap(tradecondsize_, other->tradecondsize_);
  std::swap(tradeofficialtime_, other->tradeofficialtime_);
  std::swap(tradeuniqueid_, other->tradeuniqueid_);
  std::swap(updateaction_, other->updateaction_);
  std::swap(yesttradevol_, other->yesttradevol_);
  std::swap(vwap_, other->vwap_);
  std::swap(tradecondofficialtime_, other->tradecondofficialtime_);
  std::swap(tradecondofficialdate_, other->tradecondofficialdate_);
  std::swap(correctionofficialtradetime_, other->correctionofficialtradetime_);
  std::swap(correctionofficialtradedate_, other->correctionofficialtradedate_);
  std::swap(transactionpriceind_, other->transactionpriceind_);
  std::swap(couponrate_, other->couponrate_);
  std::swap(instrstatus_, other->instrstatus_);
  std::swap(msgtype_, other->msgtype_);
  std::swap(controlmsgtype_, other->controlmsgtype_);
  std::swap(weightedaveragematurity_, other->weightedaveragematurity_);
  std::swap(historicaltradesize_, other->historicaltradesize_);
  std::swap(historicalcancelsize_, other->historicalcancelsize_);
  std::swap(historicalcorrectionsize_, other->historicalcorrectionsize_);
  std::swap(historicaltradecond_, other->historicaltradecond_);
  std::swap(historicaltradeprice_, other->historicaltradeprice_);
  std::swap(historicalcancelprice_, other->historicalcancelprice_);
  std::swap(historicalcorrectionprice_, other->historicalcorrectionprice_);
  std::swap(actiontime_, other->actiontime_);
  std::swap(actiondate_, other->actiondate_);
  std::swap(reasoncode_, other->reasoncode_);
  std::swap(historicaltradeidentifier_, other->historicaltradeidentifier_);
  std::swap(historicaloriginalmessagedate_, other->historicaloriginalmessagedate_);
  std::swap(extendedtradecond_, other->extendedtradecond_);
  std::swap(yieldhigh_, other->yieldhigh_);
  std::swap(yieldlow_, other->yieldlow_);
  std::swap(historicalcanceltradedate_, other->historicalcanceltradedate_);
  std::swap(historicalyield_, other->historicalyield_);
  std::swap(historicaltradeindicsize_, other->historicaltradeindicsize_);
  std::swap(historicalextendedtradecond_, other->historicalextendedtradecond_);
  std::swap(historicalpriceindicator_, other->historicalpriceindicator_);
  symbolsuffix_.Swap(&other->symbolsuffix_);
  std::swap(historicaltradedate_, other->historicaltradedate_);
  std::swap(historicaltradetime_, other->historicaltradetime_);
  std::swap(correctionprevspecialpriceindicator_, other->correctionprevspecialpriceindicator_);
  std::swap(correctionnewextendedtradecond_, other->correctionnewextendedtradecond_);
  std::swap(correctionprevextendedtradecond_, other->correctionprevextendedtradecond_);
  std::swap(correctionprevyield_, other->correctionprevyield_);
  std::swap(correctionnewyield_, other->correctionnewyield_);
  correctionprevtradedate_.Swap(&other->correctionprevtradedate_);
  std::swap(correctionprevtradetime_, other->correctionprevtradetime_);
  std::swap(correctionprevtradecond_, other->correctionprevtradecond_);
  std::swap(correctionnewtradecond_, other->correctionnewtradecond_);
  std::swap(canceltradeseq_, other->canceltradeseq_);
  std::swap(prevtransactionpriceind_, other->prevtransactionpriceind_);
  std::swap(correctionnewtransactionpriceind_, other->correctionnewtransactionpriceind_);
  std::swap(originaltradeseq_, other->originaltradeseq_);
  std::swap(yieldclose_, other->yieldclose_);
  std::swap(firstsettlementdate_, other->firstsettlementdate_);
  std::swap(reportdate_, other->reportdate_);
  std::swap(marketphase_, other->marketphase_);
  std::swap(disseminationdate_, other->disseminationdate_);
  std::swap(correctionnewdisseminationdate_, other->correctionnewdisseminationdate_);
  std::swap(correctionprevdisseminationdate_, other->correctionprevdisseminationdate_);
  std::swap(tradecond1_, other->tradecond1_);
  std::swap(correctionprevtradeuniqueid_, other->correctionprevtradeuniqueid_);
  std::swap(historicalcorrectionprevtradeuniqueid_, other->historicalcorrectionprevtradeuniqueid_);
  std::swap(historicalcanceltradetime_, other->historicalcanceltradetime_);
  std::swap(historicalcorrectiontradetime_, other->historicalcorrectiontradetime_);
  std::swap(historicalcorrectiontradedate_, other->historicalcorrectiontradedate_);
  std::swap(bondtype_, other->bondtype_);
  std::swap(yield_, other->yield_);
  cusip_.Swap(&other->cusip_);
  symbolexchticker_.Swap(&other->symbolexchticker_);
  isin_.Swap(&other->isin_);
  instrname2_.Swap(&other->instrname2_);
  tradesettlementdate_.Swap(&other->tradesettlementdate_);
  std::swap(firsttradingdate_, other->firsttradingdate_);
  coupontype_.Swap(&other->coupontype_);
  std::swap(enuminterestcalctype_, other->enuminterestcalctype_);
  issuername_.Swap(&other->issuername_);
  std::swap(investmentgrade_, other->investmentgrade_);
  amortizationtype_.Swap(&other->amortizationtype_);
  disseminationflag_.Swap(&other->disseminationflag_);
  symbolbloombergticker_.Swap(&other->symbolbloombergticker_);
  bloombergglobalid_.Swap(&other->bloombergglobalid_);
  bloombergglobalidcomp_.Swap(&other->bloombergglobalidcomp_);
  instrlocaltype2_.Swap(&other->instrlocaltype2_);
  symbolesignalticker_.Swap(&other->symbolesignalticker_);
  instrnamelocal_.Swap(&other->instrnamelocal_);
  mktsegmentstring_.Swap(&other->mktsegmentstring_);
  exchmonthcode_.Swap(&other->exchmonthcode_);
  std::swap(poolnumber_, other->poolnumber_);
  masterdealid_.Swap(&other->masterdealid_);
  trancheid_.Swap(&other->trancheid_);
  indicator144a_.Swap(&other->indicator144a_);
  std::swap(numbermaturitymonths_, other->numbermaturitymonths_);
  debttypecode_.Swap(&other->debttypecode_);
  std::swap(tokendel_, other->tokendel_);
  std::swap(correctiontradeseq_, other->correctiontradeseq_);
  std::swap(recordstaleind_, other->recordstaleind_);
  std::swap(weightedaveragecoupon_, other->weightedaveragecoupon_);
  std::swap(weightedaverageloanage_, other->weightedaverageloanage_);
  std::swap(weightedaverageloansize_, other->weightedaverageloansize_);
  std::swap(weightedloanvalue_, other->weightedloanvalue_);
  std::swap(averagemonthlysize_, other->averagemonthlysize_);
  std::swap(prevmonthvoldec_, other->prevmonthvoldec_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDIceTrace::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDIceTrace_descriptor_;
  metadata.reflection = MDIceTrace_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDIceTrace

// optional int32 MDDate = 1;
void MDIceTrace::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDIceTrace::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MDDate)
  return mddate_;
}
void MDIceTrace::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MDDate)
}

// optional int32 MDTime = 2;
void MDIceTrace::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDIceTrace::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MDTime)
  return mdtime_;
}
void MDIceTrace::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MDTime)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
void MDIceTrace::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDIceTrace::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDIceTrace::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
void MDIceTrace::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDIceTrace::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDIceTrace::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SecurityIDSource)
}

// optional string HTSCSecurityID = 5;
void MDIceTrace::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
void MDIceTrace::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
void MDIceTrace::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
::std::string* MDIceTrace::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}

// optional int32 ExchangeDate = 6;
void MDIceTrace::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDIceTrace::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchangeDate)
  return exchangedate_;
}
void MDIceTrace::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchangeDate)
}

// optional int32 ExchangeTime = 7;
void MDIceTrace::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDIceTrace::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchangeTime)
  return exchangetime_;
}
void MDIceTrace::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 8;
void MDIceTrace::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDIceTrace::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDIceTrace::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DataMultiplePowerOf10)
}

// optional double TradePrice = 19;
void MDIceTrace::clear_tradeprice() {
  tradeprice_ = 0;
}
double MDIceTrace::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradePrice)
  return tradeprice_;
}
void MDIceTrace::set_tradeprice(double value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradePrice)
}

// optional int64 TradeSize = 20;
void MDIceTrace::clear_tradesize() {
  tradesize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeSize)
  return tradesize_;
}
void MDIceTrace::set_tradesize(::google::protobuf::int64 value) {
  
  tradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeSize)
}

// optional double CurrentPrice = 21;
void MDIceTrace::clear_currentprice() {
  currentprice_ = 0;
}
double MDIceTrace::currentprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrentPrice)
  return currentprice_;
}
void MDIceTrace::set_currentprice(double value) {
  
  currentprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrentPrice)
}

// optional string ActivityDatetime = 22;
void MDIceTrace::clear_activitydatetime() {
  activitydatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::activitydatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  return activitydatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_activitydatetime(const ::std::string& value) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
void MDIceTrace::set_activitydatetime(const char* value) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
void MDIceTrace::set_activitydatetime(const char* value, size_t size) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
::std::string* MDIceTrace::mutable_activitydatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  return activitydatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_activitydatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  
  return activitydatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_activitydatetime(::std::string* activitydatetime) {
  if (activitydatetime != NULL) {
    
  } else {
    
  }
  activitydatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), activitydatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}

// optional string TradeDatetime = 23;
void MDIceTrace::clear_tradedatetime() {
  tradedatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::tradedatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  return tradedatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_tradedatetime(const ::std::string& value) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
void MDIceTrace::set_tradedatetime(const char* value) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
void MDIceTrace::set_tradedatetime(const char* value, size_t size) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
::std::string* MDIceTrace::mutable_tradedatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  return tradedatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_tradedatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  
  return tradedatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_tradedatetime(::std::string* tradedatetime) {
  if (tradedatetime != NULL) {
    
  } else {
    
  }
  tradedatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}

// optional int64 TradeVol = 24;
void MDIceTrace::clear_tradevol() {
  tradevol_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradevol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeVol)
  return tradevol_;
}
void MDIceTrace::set_tradevol(::google::protobuf::int64 value) {
  
  tradevol_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeVol)
}

// optional int64 ExchSeq = 26;
void MDIceTrace::clear_exchseq() {
  exchseq_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::exchseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchSeq)
  return exchseq_;
}
void MDIceTrace::set_exchseq(::google::protobuf::int64 value) {
  
  exchseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchSeq)
}

// optional double TradeIndicSize = 27;
void MDIceTrace::clear_tradeindicsize() {
  tradeindicsize_ = 0;
}
double MDIceTrace::tradeindicsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeIndicSize)
  return tradeindicsize_;
}
void MDIceTrace::set_tradeindicsize(double value) {
  
  tradeindicsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeIndicSize)
}

// optional int64 ExchMessageTimestamp = 28;
void MDIceTrace::clear_exchmessagetimestamp() {
  exchmessagetimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::exchmessagetimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchMessageTimestamp)
  return exchmessagetimestamp_;
}
void MDIceTrace::set_exchmessagetimestamp(::google::protobuf::int64 value) {
  
  exchmessagetimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchMessageTimestamp)
}

// optional double CorrectionNewTradePrice = 30;
void MDIceTrace::clear_correctionnewtradeprice() {
  correctionnewtradeprice_ = 0;
}
double MDIceTrace::correctionnewtradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradePrice)
  return correctionnewtradeprice_;
}
void MDIceTrace::set_correctionnewtradeprice(double value) {
  
  correctionnewtradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradePrice)
}

// optional int64 CorrectionNewTradeSize = 31;
void MDIceTrace::clear_correctionnewtradesize() {
  correctionnewtradesize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionnewtradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeSize)
  return correctionnewtradesize_;
}
void MDIceTrace::set_correctionnewtradesize(::google::protobuf::int64 value) {
  
  correctionnewtradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeSize)
}

// optional double CorrectionPrevTradePrice = 32;
void MDIceTrace::clear_correctionprevtradeprice() {
  correctionprevtradeprice_ = 0;
}
double MDIceTrace::correctionprevtradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradePrice)
  return correctionprevtradeprice_;
}
void MDIceTrace::set_correctionprevtradeprice(double value) {
  
  correctionprevtradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradePrice)
}

// optional int64 CorrectionPrevTradeSize = 33;
void MDIceTrace::clear_correctionprevtradesize() {
  correctionprevtradesize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionprevtradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeSize)
  return correctionprevtradesize_;
}
void MDIceTrace::set_correctionprevtradesize(::google::protobuf::int64 value) {
  
  correctionprevtradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeSize)
}

// optional string EnumCurrency = 34;
void MDIceTrace::clear_enumcurrency() {
  enumcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::enumcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  return enumcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_enumcurrency(const ::std::string& value) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
void MDIceTrace::set_enumcurrency(const char* value) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
void MDIceTrace::set_enumcurrency(const char* value, size_t size) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
::std::string* MDIceTrace::mutable_enumcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  return enumcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_enumcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  
  return enumcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_enumcurrency(::std::string* enumcurrency) {
  if (enumcurrency != NULL) {
    
  } else {
    
  }
  enumcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), enumcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}

// optional int64 MaturityDate = 35;
void MDIceTrace::clear_maturitydate() {
  maturitydate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MaturityDate)
  return maturitydate_;
}
void MDIceTrace::set_maturitydate(::google::protobuf::int64 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MaturityDate)
}

// optional int32 MarketPhaseCode = 36;
void MDIceTrace::clear_marketphasecode() {
  marketphasecode_ = 0;
}
::google::protobuf::int32 MDIceTrace::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MarketPhaseCode)
  return marketphasecode_;
}
void MDIceTrace::set_marketphasecode(::google::protobuf::int32 value) {
  
  marketphasecode_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MarketPhaseCode)
}

// optional double Chg = 37;
void MDIceTrace::clear_chg() {
  chg_ = 0;
}
double MDIceTrace::chg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Chg)
  return chg_;
}
void MDIceTrace::set_chg(double value) {
  
  chg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Chg)
}

// optional double PctChg = 38;
void MDIceTrace::clear_pctchg() {
  pctchg_ = 0;
}
double MDIceTrace::pctchg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PctChg)
  return pctchg_;
}
void MDIceTrace::set_pctchg(double value) {
  
  pctchg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PctChg)
}

// optional double ClosePx = 39;
void MDIceTrace::clear_closepx() {
  closepx_ = 0;
}
double MDIceTrace::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ClosePx)
  return closepx_;
}
void MDIceTrace::set_closepx(double value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ClosePx)
}

// optional double HighPx = 40;
void MDIceTrace::clear_highpx() {
  highpx_ = 0;
}
double MDIceTrace::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HighPx)
  return highpx_;
}
void MDIceTrace::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HighPx)
}

// optional double LowPx = 41;
void MDIceTrace::clear_lowpx() {
  lowpx_ = 0;
}
double MDIceTrace::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.LowPx)
  return lowpx_;
}
void MDIceTrace::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.LowPx)
}

// optional double OpenPx = 42;
void MDIceTrace::clear_openpx() {
  openpx_ = 0;
}
double MDIceTrace::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.OpenPx)
  return openpx_;
}
void MDIceTrace::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.OpenPx)
}

// optional double PreClosePx = 43;
void MDIceTrace::clear_preclosepx() {
  preclosepx_ = 0;
}
double MDIceTrace::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PreClosePx)
  return preclosepx_;
}
void MDIceTrace::set_preclosepx(double value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PreClosePx)
}

// optional bool RecordDel = 45;
void MDIceTrace::clear_recorddel() {
  recorddel_ = false;
}
bool MDIceTrace::recorddel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.RecordDel)
  return recorddel_;
}
void MDIceTrace::set_recorddel(bool value) {
  
  recorddel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.RecordDel)
}

// optional string CurrencyString = 46;
void MDIceTrace::clear_currencystring() {
  currencystring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::currencystring() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  return currencystring_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_currencystring(const ::std::string& value) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
void MDIceTrace::set_currencystring(const char* value) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
void MDIceTrace::set_currencystring(const char* value, size_t size) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
::std::string* MDIceTrace::mutable_currencystring() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  return currencystring_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_currencystring() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  
  return currencystring_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_currencystring(::std::string* currencystring) {
  if (currencystring != NULL) {
    
  } else {
    
  }
  currencystring_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currencystring);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}

// optional string CurrentDatetime = 47;
void MDIceTrace::clear_currentdatetime() {
  currentdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::currentdatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  return currentdatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_currentdatetime(const ::std::string& value) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
void MDIceTrace::set_currentdatetime(const char* value) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
void MDIceTrace::set_currentdatetime(const char* value, size_t size) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
::std::string* MDIceTrace::mutable_currentdatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  return currentdatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_currentdatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  
  return currentdatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_currentdatetime(::std::string* currentdatetime) {
  if (currentdatetime != NULL) {
    
  } else {
    
  }
  currentdatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currentdatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}

// optional int64 TradeOfficialDate = 48;
void MDIceTrace::clear_tradeofficialdate() {
  tradeofficialdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradeofficialdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialDate)
  return tradeofficialdate_;
}
void MDIceTrace::set_tradeofficialdate(::google::protobuf::int64 value) {
  
  tradeofficialdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialDate)
}

// optional double TradeCondPrice = 49;
void MDIceTrace::clear_tradecondprice() {
  tradecondprice_ = 0;
}
double MDIceTrace::tradecondprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondPrice)
  return tradecondprice_;
}
void MDIceTrace::set_tradecondprice(double value) {
  
  tradecondprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondPrice)
}

// optional int64 TradeCondSize = 50;
void MDIceTrace::clear_tradecondsize() {
  tradecondsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradecondsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondSize)
  return tradecondsize_;
}
void MDIceTrace::set_tradecondsize(::google::protobuf::int64 value) {
  
  tradecondsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondSize)
}

// optional int64 TradeOfficialTime = 51;
void MDIceTrace::clear_tradeofficialtime() {
  tradeofficialtime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradeofficialtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialTime)
  return tradeofficialtime_;
}
void MDIceTrace::set_tradeofficialtime(::google::protobuf::int64 value) {
  
  tradeofficialtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialTime)
}

// optional int64 TradeUniqueId = 52;
void MDIceTrace::clear_tradeuniqueid() {
  tradeuniqueid_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeUniqueId)
  return tradeuniqueid_;
}
void MDIceTrace::set_tradeuniqueid(::google::protobuf::int64 value) {
  
  tradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeUniqueId)
}

// optional int64 UpdateAction = 53;
void MDIceTrace::clear_updateaction() {
  updateaction_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::updateaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.UpdateAction)
  return updateaction_;
}
void MDIceTrace::set_updateaction(::google::protobuf::int64 value) {
  
  updateaction_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.UpdateAction)
}

// optional int64 YestTradeVol = 54;
void MDIceTrace::clear_yesttradevol() {
  yesttradevol_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::yesttradevol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YestTradeVol)
  return yesttradevol_;
}
void MDIceTrace::set_yesttradevol(::google::protobuf::int64 value) {
  
  yesttradevol_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YestTradeVol)
}

// optional double Vwap = 55;
void MDIceTrace::clear_vwap() {
  vwap_ = 0;
}
double MDIceTrace::vwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Vwap)
  return vwap_;
}
void MDIceTrace::set_vwap(double value) {
  
  vwap_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Vwap)
}

// optional int64 TradeCondOfficialTime = 56;
void MDIceTrace::clear_tradecondofficialtime() {
  tradecondofficialtime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradecondofficialtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialTime)
  return tradecondofficialtime_;
}
void MDIceTrace::set_tradecondofficialtime(::google::protobuf::int64 value) {
  
  tradecondofficialtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialTime)
}

// optional int64 TradeCondOfficialDate = 57;
void MDIceTrace::clear_tradecondofficialdate() {
  tradecondofficialdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tradecondofficialdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialDate)
  return tradecondofficialdate_;
}
void MDIceTrace::set_tradecondofficialdate(::google::protobuf::int64 value) {
  
  tradecondofficialdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialDate)
}

// optional int64 CorrectionOfficialTradeTime = 58;
void MDIceTrace::clear_correctionofficialtradetime() {
  correctionofficialtradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionofficialtradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeTime)
  return correctionofficialtradetime_;
}
void MDIceTrace::set_correctionofficialtradetime(::google::protobuf::int64 value) {
  
  correctionofficialtradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeTime)
}

// optional int64 CorrectionOfficialTradeDate = 59;
void MDIceTrace::clear_correctionofficialtradedate() {
  correctionofficialtradedate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionofficialtradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeDate)
  return correctionofficialtradedate_;
}
void MDIceTrace::set_correctionofficialtradedate(::google::protobuf::int64 value) {
  
  correctionofficialtradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeDate)
}

// optional int32 TransactionPriceInd = 60;
void MDIceTrace::clear_transactionpriceind() {
  transactionpriceind_ = 0;
}
::google::protobuf::int32 MDIceTrace::transactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TransactionPriceInd)
  return transactionpriceind_;
}
void MDIceTrace::set_transactionpriceind(::google::protobuf::int32 value) {
  
  transactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TransactionPriceInd)
}

// optional double CouponRate = 61;
void MDIceTrace::clear_couponrate() {
  couponrate_ = 0;
}
double MDIceTrace::couponrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CouponRate)
  return couponrate_;
}
void MDIceTrace::set_couponrate(double value) {
  
  couponrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CouponRate)
}

// optional int32 InstrStatus = 62;
void MDIceTrace::clear_instrstatus() {
  instrstatus_ = 0;
}
::google::protobuf::int32 MDIceTrace::instrstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrStatus)
  return instrstatus_;
}
void MDIceTrace::set_instrstatus(::google::protobuf::int32 value) {
  
  instrstatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrStatus)
}

// optional int32 MsgType = 63;
void MDIceTrace::clear_msgtype() {
  msgtype_ = 0;
}
::google::protobuf::int32 MDIceTrace::msgtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MsgType)
  return msgtype_;
}
void MDIceTrace::set_msgtype(::google::protobuf::int32 value) {
  
  msgtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MsgType)
}

// optional int32 ControlMsgType = 64;
void MDIceTrace::clear_controlmsgtype() {
  controlmsgtype_ = 0;
}
::google::protobuf::int32 MDIceTrace::controlmsgtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ControlMsgType)
  return controlmsgtype_;
}
void MDIceTrace::set_controlmsgtype(::google::protobuf::int32 value) {
  
  controlmsgtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ControlMsgType)
}

// optional int64 WeightedAverageMaturity = 65;
void MDIceTrace::clear_weightedaveragematurity() {
  weightedaveragematurity_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::weightedaveragematurity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageMaturity)
  return weightedaveragematurity_;
}
void MDIceTrace::set_weightedaveragematurity(::google::protobuf::int64 value) {
  
  weightedaveragematurity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageMaturity)
}

// optional int64 HistoricalTradeSize = 66;
void MDIceTrace::clear_historicaltradesize() {
  historicaltradesize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaltradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeSize)
  return historicaltradesize_;
}
void MDIceTrace::set_historicaltradesize(::google::protobuf::int64 value) {
  
  historicaltradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeSize)
}

// optional int64 HistoricalCancelSize = 67;
void MDIceTrace::clear_historicalcancelsize() {
  historicalcancelsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcancelsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelSize)
  return historicalcancelsize_;
}
void MDIceTrace::set_historicalcancelsize(::google::protobuf::int64 value) {
  
  historicalcancelsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelSize)
}

// optional int64 HistoricalCorrectionSize = 68;
void MDIceTrace::clear_historicalcorrectionsize() {
  historicalcorrectionsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcorrectionsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionSize)
  return historicalcorrectionsize_;
}
void MDIceTrace::set_historicalcorrectionsize(::google::protobuf::int64 value) {
  
  historicalcorrectionsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionSize)
}

// optional int64 HistoricalTradeCond = 69;
void MDIceTrace::clear_historicaltradecond() {
  historicaltradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaltradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeCond)
  return historicaltradecond_;
}
void MDIceTrace::set_historicaltradecond(::google::protobuf::int64 value) {
  
  historicaltradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeCond)
}

// optional double HistoricalTradePrice = 70;
void MDIceTrace::clear_historicaltradeprice() {
  historicaltradeprice_ = 0;
}
double MDIceTrace::historicaltradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradePrice)
  return historicaltradeprice_;
}
void MDIceTrace::set_historicaltradeprice(double value) {
  
  historicaltradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradePrice)
}

// optional double HistoricalCancelPrice = 71;
void MDIceTrace::clear_historicalcancelprice() {
  historicalcancelprice_ = 0;
}
double MDIceTrace::historicalcancelprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelPrice)
  return historicalcancelprice_;
}
void MDIceTrace::set_historicalcancelprice(double value) {
  
  historicalcancelprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelPrice)
}

// optional double HistoricalCorrectionPrice = 72;
void MDIceTrace::clear_historicalcorrectionprice() {
  historicalcorrectionprice_ = 0;
}
double MDIceTrace::historicalcorrectionprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrice)
  return historicalcorrectionprice_;
}
void MDIceTrace::set_historicalcorrectionprice(double value) {
  
  historicalcorrectionprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrice)
}

// optional int64 ActionTime = 73;
void MDIceTrace::clear_actiontime() {
  actiontime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::actiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActionTime)
  return actiontime_;
}
void MDIceTrace::set_actiontime(::google::protobuf::int64 value) {
  
  actiontime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActionTime)
}

// optional int64 ActionDate = 74;
void MDIceTrace::clear_actiondate() {
  actiondate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::actiondate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActionDate)
  return actiondate_;
}
void MDIceTrace::set_actiondate(::google::protobuf::int64 value) {
  
  actiondate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActionDate)
}

// optional int32 ReasonCode = 75;
void MDIceTrace::clear_reasoncode() {
  reasoncode_ = 0;
}
::google::protobuf::int32 MDIceTrace::reasoncode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ReasonCode)
  return reasoncode_;
}
void MDIceTrace::set_reasoncode(::google::protobuf::int32 value) {
  
  reasoncode_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ReasonCode)
}

// optional int64 HistoricalTradeIdentifier = 76;
void MDIceTrace::clear_historicaltradeidentifier() {
  historicaltradeidentifier_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaltradeidentifier() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIdentifier)
  return historicaltradeidentifier_;
}
void MDIceTrace::set_historicaltradeidentifier(::google::protobuf::int64 value) {
  
  historicaltradeidentifier_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIdentifier)
}

// optional int64 HistoricalOriginalMessageDate = 77;
void MDIceTrace::clear_historicaloriginalmessagedate() {
  historicaloriginalmessagedate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaloriginalmessagedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalOriginalMessageDate)
  return historicaloriginalmessagedate_;
}
void MDIceTrace::set_historicaloriginalmessagedate(::google::protobuf::int64 value) {
  
  historicaloriginalmessagedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalOriginalMessageDate)
}

// optional int64 ExtendedTradeCond = 78;
void MDIceTrace::clear_extendedtradecond() {
  extendedtradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::extendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExtendedTradeCond)
  return extendedtradecond_;
}
void MDIceTrace::set_extendedtradecond(::google::protobuf::int64 value) {
  
  extendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExtendedTradeCond)
}

// optional double YieldHigh = 79;
void MDIceTrace::clear_yieldhigh() {
  yieldhigh_ = 0;
}
double MDIceTrace::yieldhigh() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldHigh)
  return yieldhigh_;
}
void MDIceTrace::set_yieldhigh(double value) {
  
  yieldhigh_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldHigh)
}

// optional double YieldLow = 80;
void MDIceTrace::clear_yieldlow() {
  yieldlow_ = 0;
}
double MDIceTrace::yieldlow() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldLow)
  return yieldlow_;
}
void MDIceTrace::set_yieldlow(double value) {
  
  yieldlow_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldLow)
}

// optional int64 HistoricalCancelTradeDate = 81;
void MDIceTrace::clear_historicalcanceltradedate() {
  historicalcanceltradedate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcanceltradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeDate)
  return historicalcanceltradedate_;
}
void MDIceTrace::set_historicalcanceltradedate(::google::protobuf::int64 value) {
  
  historicalcanceltradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeDate)
}

// optional double HistoricalYield = 82;
void MDIceTrace::clear_historicalyield() {
  historicalyield_ = 0;
}
double MDIceTrace::historicalyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalYield)
  return historicalyield_;
}
void MDIceTrace::set_historicalyield(double value) {
  
  historicalyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalYield)
}

// optional double HistoricalTradeIndicSize = 83;
void MDIceTrace::clear_historicaltradeindicsize() {
  historicaltradeindicsize_ = 0;
}
double MDIceTrace::historicaltradeindicsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIndicSize)
  return historicaltradeindicsize_;
}
void MDIceTrace::set_historicaltradeindicsize(double value) {
  
  historicaltradeindicsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIndicSize)
}

// optional int64 HistoricalExtendedTradeCond = 84;
void MDIceTrace::clear_historicalextendedtradecond() {
  historicalextendedtradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalExtendedTradeCond)
  return historicalextendedtradecond_;
}
void MDIceTrace::set_historicalextendedtradecond(::google::protobuf::int64 value) {
  
  historicalextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalExtendedTradeCond)
}

// optional int32 HistoricalPriceIndicator = 85;
void MDIceTrace::clear_historicalpriceindicator() {
  historicalpriceindicator_ = 0;
}
::google::protobuf::int32 MDIceTrace::historicalpriceindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalPriceIndicator)
  return historicalpriceindicator_;
}
void MDIceTrace::set_historicalpriceindicator(::google::protobuf::int32 value) {
  
  historicalpriceindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalPriceIndicator)
}

// optional string SymbolSuffix = 86;
void MDIceTrace::clear_symbolsuffix() {
  symbolsuffix_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::symbolsuffix() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  return symbolsuffix_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_symbolsuffix(const ::std::string& value) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
void MDIceTrace::set_symbolsuffix(const char* value) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
void MDIceTrace::set_symbolsuffix(const char* value, size_t size) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
::std::string* MDIceTrace::mutable_symbolsuffix() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  return symbolsuffix_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_symbolsuffix() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  
  return symbolsuffix_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_symbolsuffix(::std::string* symbolsuffix) {
  if (symbolsuffix != NULL) {
    
  } else {
    
  }
  symbolsuffix_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolsuffix);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}

// optional int64 HistoricalTradeDate = 87;
void MDIceTrace::clear_historicaltradedate() {
  historicaltradedate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaltradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeDate)
  return historicaltradedate_;
}
void MDIceTrace::set_historicaltradedate(::google::protobuf::int64 value) {
  
  historicaltradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeDate)
}

// optional int64 HistoricalTradeTime = 88;
void MDIceTrace::clear_historicaltradetime() {
  historicaltradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicaltradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeTime)
  return historicaltradetime_;
}
void MDIceTrace::set_historicaltradetime(::google::protobuf::int64 value) {
  
  historicaltradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeTime)
}

// optional int32 CorrectionPrevSpecialPriceIndicator = 89;
void MDIceTrace::clear_correctionprevspecialpriceindicator() {
  correctionprevspecialpriceindicator_ = 0;
}
::google::protobuf::int32 MDIceTrace::correctionprevspecialpriceindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevSpecialPriceIndicator)
  return correctionprevspecialpriceindicator_;
}
void MDIceTrace::set_correctionprevspecialpriceindicator(::google::protobuf::int32 value) {
  
  correctionprevspecialpriceindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevSpecialPriceIndicator)
}

// optional int64 CorrectionNewExtendedTradeCond = 90;
void MDIceTrace::clear_correctionnewextendedtradecond() {
  correctionnewextendedtradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionnewextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewExtendedTradeCond)
  return correctionnewextendedtradecond_;
}
void MDIceTrace::set_correctionnewextendedtradecond(::google::protobuf::int64 value) {
  
  correctionnewextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewExtendedTradeCond)
}

// optional int64 CorrectionPrevExtendedTradeCond = 91;
void MDIceTrace::clear_correctionprevextendedtradecond() {
  correctionprevextendedtradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionprevextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevExtendedTradeCond)
  return correctionprevextendedtradecond_;
}
void MDIceTrace::set_correctionprevextendedtradecond(::google::protobuf::int64 value) {
  
  correctionprevextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevExtendedTradeCond)
}

// optional double CorrectionPrevYield = 92;
void MDIceTrace::clear_correctionprevyield() {
  correctionprevyield_ = 0;
}
double MDIceTrace::correctionprevyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevYield)
  return correctionprevyield_;
}
void MDIceTrace::set_correctionprevyield(double value) {
  
  correctionprevyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevYield)
}

// optional double CorrectionNewYield = 93;
void MDIceTrace::clear_correctionnewyield() {
  correctionnewyield_ = 0;
}
double MDIceTrace::correctionnewyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewYield)
  return correctionnewyield_;
}
void MDIceTrace::set_correctionnewyield(double value) {
  
  correctionnewyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewYield)
}

// optional string CorrectionPrevTradeDate = 94;
void MDIceTrace::clear_correctionprevtradedate() {
  correctionprevtradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::correctionprevtradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  return correctionprevtradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_correctionprevtradedate(const ::std::string& value) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
void MDIceTrace::set_correctionprevtradedate(const char* value) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
void MDIceTrace::set_correctionprevtradedate(const char* value, size_t size) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
::std::string* MDIceTrace::mutable_correctionprevtradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  return correctionprevtradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_correctionprevtradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  
  return correctionprevtradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_correctionprevtradedate(::std::string* correctionprevtradedate) {
  if (correctionprevtradedate != NULL) {
    
  } else {
    
  }
  correctionprevtradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), correctionprevtradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}

// optional int64 CorrectionPrevTradeTime = 95;
void MDIceTrace::clear_correctionprevtradetime() {
  correctionprevtradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionprevtradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeTime)
  return correctionprevtradetime_;
}
void MDIceTrace::set_correctionprevtradetime(::google::protobuf::int64 value) {
  
  correctionprevtradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeTime)
}

// optional int32 CorrectionPrevTradeCond = 96;
void MDIceTrace::clear_correctionprevtradecond() {
  correctionprevtradecond_ = 0;
}
::google::protobuf::int32 MDIceTrace::correctionprevtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeCond)
  return correctionprevtradecond_;
}
void MDIceTrace::set_correctionprevtradecond(::google::protobuf::int32 value) {
  
  correctionprevtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeCond)
}

// optional int64 CorrectionNewTradeCond = 97;
void MDIceTrace::clear_correctionnewtradecond() {
  correctionnewtradecond_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionnewtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeCond)
  return correctionnewtradecond_;
}
void MDIceTrace::set_correctionnewtradecond(::google::protobuf::int64 value) {
  
  correctionnewtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeCond)
}

// optional int64 CancelTradeSeq = 98;
void MDIceTrace::clear_canceltradeseq() {
  canceltradeseq_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::canceltradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CancelTradeSeq)
  return canceltradeseq_;
}
void MDIceTrace::set_canceltradeseq(::google::protobuf::int64 value) {
  
  canceltradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CancelTradeSeq)
}

// optional int32 PrevTransactionPriceInd = 99;
void MDIceTrace::clear_prevtransactionpriceind() {
  prevtransactionpriceind_ = 0;
}
::google::protobuf::int32 MDIceTrace::prevtransactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PrevTransactionPriceInd)
  return prevtransactionpriceind_;
}
void MDIceTrace::set_prevtransactionpriceind(::google::protobuf::int32 value) {
  
  prevtransactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PrevTransactionPriceInd)
}

// optional int64 CorrectionNewTransactionPriceInd = 100;
void MDIceTrace::clear_correctionnewtransactionpriceind() {
  correctionnewtransactionpriceind_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionnewtransactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTransactionPriceInd)
  return correctionnewtransactionpriceind_;
}
void MDIceTrace::set_correctionnewtransactionpriceind(::google::protobuf::int64 value) {
  
  correctionnewtransactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTransactionPriceInd)
}

// optional int64 OriginalTradeSeq = 101;
void MDIceTrace::clear_originaltradeseq() {
  originaltradeseq_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::originaltradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.OriginalTradeSeq)
  return originaltradeseq_;
}
void MDIceTrace::set_originaltradeseq(::google::protobuf::int64 value) {
  
  originaltradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.OriginalTradeSeq)
}

// optional double YieldClose = 102;
void MDIceTrace::clear_yieldclose() {
  yieldclose_ = 0;
}
double MDIceTrace::yieldclose() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldClose)
  return yieldclose_;
}
void MDIceTrace::set_yieldclose(double value) {
  
  yieldclose_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldClose)
}

// optional int64 FirstSettlementDate = 103;
void MDIceTrace::clear_firstsettlementdate() {
  firstsettlementdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::firstsettlementdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.FirstSettlementDate)
  return firstsettlementdate_;
}
void MDIceTrace::set_firstsettlementdate(::google::protobuf::int64 value) {
  
  firstsettlementdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.FirstSettlementDate)
}

// optional int64 ReportDate = 104;
void MDIceTrace::clear_reportdate() {
  reportdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::reportdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ReportDate)
  return reportdate_;
}
void MDIceTrace::set_reportdate(::google::protobuf::int64 value) {
  
  reportdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ReportDate)
}

// optional int32 MarketPhase = 105;
void MDIceTrace::clear_marketphase() {
  marketphase_ = 0;
}
::google::protobuf::int32 MDIceTrace::marketphase() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MarketPhase)
  return marketphase_;
}
void MDIceTrace::set_marketphase(::google::protobuf::int32 value) {
  
  marketphase_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MarketPhase)
}

// optional int64 DisseminationDate = 106;
void MDIceTrace::clear_disseminationdate() {
  disseminationdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::disseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DisseminationDate)
  return disseminationdate_;
}
void MDIceTrace::set_disseminationdate(::google::protobuf::int64 value) {
  
  disseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DisseminationDate)
}

// optional int64 CorrectionNewDisseminationDate = 107;
void MDIceTrace::clear_correctionnewdisseminationdate() {
  correctionnewdisseminationdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionnewdisseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewDisseminationDate)
  return correctionnewdisseminationdate_;
}
void MDIceTrace::set_correctionnewdisseminationdate(::google::protobuf::int64 value) {
  
  correctionnewdisseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewDisseminationDate)
}

// optional int64 CorrectionPrevDisseminationDate = 108;
void MDIceTrace::clear_correctionprevdisseminationdate() {
  correctionprevdisseminationdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionprevdisseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevDisseminationDate)
  return correctionprevdisseminationdate_;
}
void MDIceTrace::set_correctionprevdisseminationdate(::google::protobuf::int64 value) {
  
  correctionprevdisseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevDisseminationDate)
}

// optional int32 TradeCond1 = 109;
void MDIceTrace::clear_tradecond1() {
  tradecond1_ = 0;
}
::google::protobuf::int32 MDIceTrace::tradecond1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCond1)
  return tradecond1_;
}
void MDIceTrace::set_tradecond1(::google::protobuf::int32 value) {
  
  tradecond1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCond1)
}

// optional int64 CorrectionPrevTradeUniqueId = 110;
void MDIceTrace::clear_correctionprevtradeuniqueid() {
  correctionprevtradeuniqueid_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctionprevtradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeUniqueId)
  return correctionprevtradeuniqueid_;
}
void MDIceTrace::set_correctionprevtradeuniqueid(::google::protobuf::int64 value) {
  
  correctionprevtradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeUniqueId)
}

// optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
void MDIceTrace::clear_historicalcorrectionprevtradeuniqueid() {
  historicalcorrectionprevtradeuniqueid_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcorrectionprevtradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrevTradeUniqueId)
  return historicalcorrectionprevtradeuniqueid_;
}
void MDIceTrace::set_historicalcorrectionprevtradeuniqueid(::google::protobuf::int64 value) {
  
  historicalcorrectionprevtradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrevTradeUniqueId)
}

// optional int64 HistoricalCancelTradeTime = 112;
void MDIceTrace::clear_historicalcanceltradetime() {
  historicalcanceltradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcanceltradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeTime)
  return historicalcanceltradetime_;
}
void MDIceTrace::set_historicalcanceltradetime(::google::protobuf::int64 value) {
  
  historicalcanceltradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeTime)
}

// optional int64 HistoricalCorrectionTradeTime = 113;
void MDIceTrace::clear_historicalcorrectiontradetime() {
  historicalcorrectiontradetime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcorrectiontradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeTime)
  return historicalcorrectiontradetime_;
}
void MDIceTrace::set_historicalcorrectiontradetime(::google::protobuf::int64 value) {
  
  historicalcorrectiontradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeTime)
}

// optional int64 HistoricalCorrectionTradeDate = 114;
void MDIceTrace::clear_historicalcorrectiontradedate() {
  historicalcorrectiontradedate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::historicalcorrectiontradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeDate)
  return historicalcorrectiontradedate_;
}
void MDIceTrace::set_historicalcorrectiontradedate(::google::protobuf::int64 value) {
  
  historicalcorrectiontradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeDate)
}

// optional int32 BondType = 115;
void MDIceTrace::clear_bondtype() {
  bondtype_ = 0;
}
::google::protobuf::int32 MDIceTrace::bondtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BondType)
  return bondtype_;
}
void MDIceTrace::set_bondtype(::google::protobuf::int32 value) {
  
  bondtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BondType)
}

// optional double Yield = 117;
void MDIceTrace::clear_yield() {
  yield_ = 0;
}
double MDIceTrace::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Yield)
  return yield_;
}
void MDIceTrace::set_yield(double value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Yield)
}

// optional string Cusip = 118;
void MDIceTrace::clear_cusip() {
  cusip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::cusip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  return cusip_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_cusip(const ::std::string& value) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
void MDIceTrace::set_cusip(const char* value) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
void MDIceTrace::set_cusip(const char* value, size_t size) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
::std::string* MDIceTrace::mutable_cusip() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  return cusip_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_cusip() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  
  return cusip_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_cusip(::std::string* cusip) {
  if (cusip != NULL) {
    
  } else {
    
  }
  cusip_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cusip);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}

// optional string SymbolExchTicker = 119;
void MDIceTrace::clear_symbolexchticker() {
  symbolexchticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::symbolexchticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  return symbolexchticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_symbolexchticker(const ::std::string& value) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
void MDIceTrace::set_symbolexchticker(const char* value) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
void MDIceTrace::set_symbolexchticker(const char* value, size_t size) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
::std::string* MDIceTrace::mutable_symbolexchticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  return symbolexchticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_symbolexchticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  
  return symbolexchticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_symbolexchticker(::std::string* symbolexchticker) {
  if (symbolexchticker != NULL) {
    
  } else {
    
  }
  symbolexchticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolexchticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}

// optional string ISIN = 120;
void MDIceTrace::clear_isin() {
  isin_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::isin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  return isin_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_isin(const ::std::string& value) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
void MDIceTrace::set_isin(const char* value) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
void MDIceTrace::set_isin(const char* value, size_t size) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
::std::string* MDIceTrace::mutable_isin() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  return isin_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_isin() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  
  return isin_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_isin(::std::string* isin) {
  if (isin != NULL) {
    
  } else {
    
  }
  isin_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), isin);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}

// optional string InstrName2 = 121;
void MDIceTrace::clear_instrname2() {
  instrname2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::instrname2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  return instrname2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_instrname2(const ::std::string& value) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
void MDIceTrace::set_instrname2(const char* value) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
void MDIceTrace::set_instrname2(const char* value, size_t size) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
::std::string* MDIceTrace::mutable_instrname2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  return instrname2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_instrname2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  
  return instrname2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_instrname2(::std::string* instrname2) {
  if (instrname2 != NULL) {
    
  } else {
    
  }
  instrname2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrname2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}

// optional string TradeSettlementDate = 122;
void MDIceTrace::clear_tradesettlementdate() {
  tradesettlementdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::tradesettlementdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  return tradesettlementdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_tradesettlementdate(const ::std::string& value) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
void MDIceTrace::set_tradesettlementdate(const char* value) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
void MDIceTrace::set_tradesettlementdate(const char* value, size_t size) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
::std::string* MDIceTrace::mutable_tradesettlementdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  return tradesettlementdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_tradesettlementdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  
  return tradesettlementdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_tradesettlementdate(::std::string* tradesettlementdate) {
  if (tradesettlementdate != NULL) {
    
  } else {
    
  }
  tradesettlementdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradesettlementdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}

// optional int64 FirstTradingDate = 123;
void MDIceTrace::clear_firsttradingdate() {
  firsttradingdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::firsttradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.FirstTradingDate)
  return firsttradingdate_;
}
void MDIceTrace::set_firsttradingdate(::google::protobuf::int64 value) {
  
  firsttradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.FirstTradingDate)
}

// optional string CouponType = 124;
void MDIceTrace::clear_coupontype() {
  coupontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::coupontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  return coupontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_coupontype(const ::std::string& value) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
void MDIceTrace::set_coupontype(const char* value) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
void MDIceTrace::set_coupontype(const char* value, size_t size) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
::std::string* MDIceTrace::mutable_coupontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  return coupontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_coupontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  
  return coupontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_coupontype(::std::string* coupontype) {
  if (coupontype != NULL) {
    
  } else {
    
  }
  coupontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), coupontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}

// optional int64 EnumInterestCalcType = 125;
void MDIceTrace::clear_enuminterestcalctype() {
  enuminterestcalctype_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::enuminterestcalctype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.EnumInterestCalcType)
  return enuminterestcalctype_;
}
void MDIceTrace::set_enuminterestcalctype(::google::protobuf::int64 value) {
  
  enuminterestcalctype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.EnumInterestCalcType)
}

// optional string IssuerName = 126;
void MDIceTrace::clear_issuername() {
  issuername_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::issuername() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  return issuername_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_issuername(const ::std::string& value) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
void MDIceTrace::set_issuername(const char* value) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
void MDIceTrace::set_issuername(const char* value, size_t size) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
::std::string* MDIceTrace::mutable_issuername() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  return issuername_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_issuername() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  
  return issuername_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_issuername(::std::string* issuername) {
  if (issuername != NULL) {
    
  } else {
    
  }
  issuername_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), issuername);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}

// optional int32 InvestmentGrade = 127;
void MDIceTrace::clear_investmentgrade() {
  investmentgrade_ = 0;
}
::google::protobuf::int32 MDIceTrace::investmentgrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InvestmentGrade)
  return investmentgrade_;
}
void MDIceTrace::set_investmentgrade(::google::protobuf::int32 value) {
  
  investmentgrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InvestmentGrade)
}

// optional string AmortizationType = 128;
void MDIceTrace::clear_amortizationtype() {
  amortizationtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::amortizationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  return amortizationtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_amortizationtype(const ::std::string& value) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
void MDIceTrace::set_amortizationtype(const char* value) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
void MDIceTrace::set_amortizationtype(const char* value, size_t size) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
::std::string* MDIceTrace::mutable_amortizationtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  return amortizationtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_amortizationtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  
  return amortizationtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_amortizationtype(::std::string* amortizationtype) {
  if (amortizationtype != NULL) {
    
  } else {
    
  }
  amortizationtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), amortizationtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}

// optional string DisseminationFlag = 129;
void MDIceTrace::clear_disseminationflag() {
  disseminationflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::disseminationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  return disseminationflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_disseminationflag(const ::std::string& value) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
void MDIceTrace::set_disseminationflag(const char* value) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
void MDIceTrace::set_disseminationflag(const char* value, size_t size) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
::std::string* MDIceTrace::mutable_disseminationflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  return disseminationflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_disseminationflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  
  return disseminationflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_disseminationflag(::std::string* disseminationflag) {
  if (disseminationflag != NULL) {
    
  } else {
    
  }
  disseminationflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), disseminationflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}

// optional string SymbolBloombergTicker = 130;
void MDIceTrace::clear_symbolbloombergticker() {
  symbolbloombergticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::symbolbloombergticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  return symbolbloombergticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_symbolbloombergticker(const ::std::string& value) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
void MDIceTrace::set_symbolbloombergticker(const char* value) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
void MDIceTrace::set_symbolbloombergticker(const char* value, size_t size) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
::std::string* MDIceTrace::mutable_symbolbloombergticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  return symbolbloombergticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_symbolbloombergticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  
  return symbolbloombergticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_symbolbloombergticker(::std::string* symbolbloombergticker) {
  if (symbolbloombergticker != NULL) {
    
  } else {
    
  }
  symbolbloombergticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolbloombergticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}

// optional string BloombergGlobalId = 131;
void MDIceTrace::clear_bloombergglobalid() {
  bloombergglobalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::bloombergglobalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  return bloombergglobalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_bloombergglobalid(const ::std::string& value) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
void MDIceTrace::set_bloombergglobalid(const char* value) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
void MDIceTrace::set_bloombergglobalid(const char* value, size_t size) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
::std::string* MDIceTrace::mutable_bloombergglobalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  return bloombergglobalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_bloombergglobalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  
  return bloombergglobalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_bloombergglobalid(::std::string* bloombergglobalid) {
  if (bloombergglobalid != NULL) {
    
  } else {
    
  }
  bloombergglobalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bloombergglobalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}

// optional string BloombergGlobalIdComp = 132;
void MDIceTrace::clear_bloombergglobalidcomp() {
  bloombergglobalidcomp_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::bloombergglobalidcomp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  return bloombergglobalidcomp_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_bloombergglobalidcomp(const ::std::string& value) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
void MDIceTrace::set_bloombergglobalidcomp(const char* value) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
void MDIceTrace::set_bloombergglobalidcomp(const char* value, size_t size) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
::std::string* MDIceTrace::mutable_bloombergglobalidcomp() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  return bloombergglobalidcomp_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_bloombergglobalidcomp() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  
  return bloombergglobalidcomp_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_bloombergglobalidcomp(::std::string* bloombergglobalidcomp) {
  if (bloombergglobalidcomp != NULL) {
    
  } else {
    
  }
  bloombergglobalidcomp_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bloombergglobalidcomp);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}

// optional string InstrLocalType2 = 133;
void MDIceTrace::clear_instrlocaltype2() {
  instrlocaltype2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::instrlocaltype2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  return instrlocaltype2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_instrlocaltype2(const ::std::string& value) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
void MDIceTrace::set_instrlocaltype2(const char* value) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
void MDIceTrace::set_instrlocaltype2(const char* value, size_t size) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
::std::string* MDIceTrace::mutable_instrlocaltype2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  return instrlocaltype2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_instrlocaltype2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  
  return instrlocaltype2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_instrlocaltype2(::std::string* instrlocaltype2) {
  if (instrlocaltype2 != NULL) {
    
  } else {
    
  }
  instrlocaltype2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrlocaltype2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}

// optional string SymbolEsignalTicker = 134;
void MDIceTrace::clear_symbolesignalticker() {
  symbolesignalticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::symbolesignalticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  return symbolesignalticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_symbolesignalticker(const ::std::string& value) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
void MDIceTrace::set_symbolesignalticker(const char* value) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
void MDIceTrace::set_symbolesignalticker(const char* value, size_t size) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
::std::string* MDIceTrace::mutable_symbolesignalticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  return symbolesignalticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_symbolesignalticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  
  return symbolesignalticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_symbolesignalticker(::std::string* symbolesignalticker) {
  if (symbolesignalticker != NULL) {
    
  } else {
    
  }
  symbolesignalticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolesignalticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}

// optional string InstrNameLocal = 135;
void MDIceTrace::clear_instrnamelocal() {
  instrnamelocal_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::instrnamelocal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  return instrnamelocal_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_instrnamelocal(const ::std::string& value) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
void MDIceTrace::set_instrnamelocal(const char* value) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
void MDIceTrace::set_instrnamelocal(const char* value, size_t size) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
::std::string* MDIceTrace::mutable_instrnamelocal() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  return instrnamelocal_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_instrnamelocal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  
  return instrnamelocal_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_instrnamelocal(::std::string* instrnamelocal) {
  if (instrnamelocal != NULL) {
    
  } else {
    
  }
  instrnamelocal_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrnamelocal);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}

// optional string MktSegmentString = 136;
void MDIceTrace::clear_mktsegmentstring() {
  mktsegmentstring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::mktsegmentstring() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  return mktsegmentstring_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_mktsegmentstring(const ::std::string& value) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
void MDIceTrace::set_mktsegmentstring(const char* value) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
void MDIceTrace::set_mktsegmentstring(const char* value, size_t size) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
::std::string* MDIceTrace::mutable_mktsegmentstring() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  return mktsegmentstring_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_mktsegmentstring() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  
  return mktsegmentstring_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_mktsegmentstring(::std::string* mktsegmentstring) {
  if (mktsegmentstring != NULL) {
    
  } else {
    
  }
  mktsegmentstring_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mktsegmentstring);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}

// optional string ExchMonthCode = 137;
void MDIceTrace::clear_exchmonthcode() {
  exchmonthcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::exchmonthcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  return exchmonthcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_exchmonthcode(const ::std::string& value) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
void MDIceTrace::set_exchmonthcode(const char* value) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
void MDIceTrace::set_exchmonthcode(const char* value, size_t size) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
::std::string* MDIceTrace::mutable_exchmonthcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  return exchmonthcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_exchmonthcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  
  return exchmonthcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_exchmonthcode(::std::string* exchmonthcode) {
  if (exchmonthcode != NULL) {
    
  } else {
    
  }
  exchmonthcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchmonthcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}

// optional int64 PoolNumber = 138;
void MDIceTrace::clear_poolnumber() {
  poolnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::poolnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PoolNumber)
  return poolnumber_;
}
void MDIceTrace::set_poolnumber(::google::protobuf::int64 value) {
  
  poolnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PoolNumber)
}

// optional string MasterDealId = 139;
void MDIceTrace::clear_masterdealid() {
  masterdealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::masterdealid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  return masterdealid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_masterdealid(const ::std::string& value) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
void MDIceTrace::set_masterdealid(const char* value) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
void MDIceTrace::set_masterdealid(const char* value, size_t size) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
::std::string* MDIceTrace::mutable_masterdealid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  return masterdealid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_masterdealid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  
  return masterdealid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_masterdealid(::std::string* masterdealid) {
  if (masterdealid != NULL) {
    
  } else {
    
  }
  masterdealid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), masterdealid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}

// optional string TrancheId = 140;
void MDIceTrace::clear_trancheid() {
  trancheid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::trancheid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  return trancheid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_trancheid(const ::std::string& value) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
void MDIceTrace::set_trancheid(const char* value) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
void MDIceTrace::set_trancheid(const char* value, size_t size) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
::std::string* MDIceTrace::mutable_trancheid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  return trancheid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_trancheid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  
  return trancheid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_trancheid(::std::string* trancheid) {
  if (trancheid != NULL) {
    
  } else {
    
  }
  trancheid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), trancheid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}

// optional string Indicator144A = 141;
void MDIceTrace::clear_indicator144a() {
  indicator144a_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::indicator144a() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  return indicator144a_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_indicator144a(const ::std::string& value) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
void MDIceTrace::set_indicator144a(const char* value) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
void MDIceTrace::set_indicator144a(const char* value, size_t size) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
::std::string* MDIceTrace::mutable_indicator144a() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  return indicator144a_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_indicator144a() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  
  return indicator144a_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_indicator144a(::std::string* indicator144a) {
  if (indicator144a != NULL) {
    
  } else {
    
  }
  indicator144a_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), indicator144a);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}

// optional int64 NumberMaturityMonths = 142;
void MDIceTrace::clear_numbermaturitymonths() {
  numbermaturitymonths_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::numbermaturitymonths() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.NumberMaturityMonths)
  return numbermaturitymonths_;
}
void MDIceTrace::set_numbermaturitymonths(::google::protobuf::int64 value) {
  
  numbermaturitymonths_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.NumberMaturityMonths)
}

// optional string DebtTypeCode = 143;
void MDIceTrace::clear_debttypecode() {
  debttypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDIceTrace::debttypecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  return debttypecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_debttypecode(const ::std::string& value) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
void MDIceTrace::set_debttypecode(const char* value) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
void MDIceTrace::set_debttypecode(const char* value, size_t size) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
::std::string* MDIceTrace::mutable_debttypecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  return debttypecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDIceTrace::release_debttypecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  
  return debttypecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDIceTrace::set_allocated_debttypecode(::std::string* debttypecode) {
  if (debttypecode != NULL) {
    
  } else {
    
  }
  debttypecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), debttypecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}

// optional int64 TokenDel = 144;
void MDIceTrace::clear_tokendel() {
  tokendel_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::tokendel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TokenDel)
  return tokendel_;
}
void MDIceTrace::set_tokendel(::google::protobuf::int64 value) {
  
  tokendel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TokenDel)
}

// optional int64 CorrectionTradeSeq = 145;
void MDIceTrace::clear_correctiontradeseq() {
  correctiontradeseq_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::correctiontradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionTradeSeq)
  return correctiontradeseq_;
}
void MDIceTrace::set_correctiontradeseq(::google::protobuf::int64 value) {
  
  correctiontradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionTradeSeq)
}

// optional int32 RecordStaleInd = 146;
void MDIceTrace::clear_recordstaleind() {
  recordstaleind_ = 0;
}
::google::protobuf::int32 MDIceTrace::recordstaleind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.RecordStaleInd)
  return recordstaleind_;
}
void MDIceTrace::set_recordstaleind(::google::protobuf::int32 value) {
  
  recordstaleind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.RecordStaleInd)
}

// optional double WeightedAverageCoupon = 147;
void MDIceTrace::clear_weightedaveragecoupon() {
  weightedaveragecoupon_ = 0;
}
double MDIceTrace::weightedaveragecoupon() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageCoupon)
  return weightedaveragecoupon_;
}
void MDIceTrace::set_weightedaveragecoupon(double value) {
  
  weightedaveragecoupon_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageCoupon)
}

// optional int64 WeightedAverageLoanAge = 148;
void MDIceTrace::clear_weightedaverageloanage() {
  weightedaverageloanage_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::weightedaverageloanage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanAge)
  return weightedaverageloanage_;
}
void MDIceTrace::set_weightedaverageloanage(::google::protobuf::int64 value) {
  
  weightedaverageloanage_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanAge)
}

// optional int64 WeightedAverageLoanSize = 149;
void MDIceTrace::clear_weightedaverageloansize() {
  weightedaverageloansize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::weightedaverageloansize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanSize)
  return weightedaverageloansize_;
}
void MDIceTrace::set_weightedaverageloansize(::google::protobuf::int64 value) {
  
  weightedaverageloansize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanSize)
}

// optional int64 WeightedLoanValue = 150;
void MDIceTrace::clear_weightedloanvalue() {
  weightedloanvalue_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDIceTrace::weightedloanvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedLoanValue)
  return weightedloanvalue_;
}
void MDIceTrace::set_weightedloanvalue(::google::protobuf::int64 value) {
  
  weightedloanvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedLoanValue)
}

// optional double AverageMonthlySize = 151;
void MDIceTrace::clear_averagemonthlysize() {
  averagemonthlysize_ = 0;
}
double MDIceTrace::averagemonthlysize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.AverageMonthlySize)
  return averagemonthlysize_;
}
void MDIceTrace::set_averagemonthlysize(double value) {
  
  averagemonthlysize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.AverageMonthlySize)
}

// optional double PrevMonthVolDec = 152;
void MDIceTrace::clear_prevmonthvoldec() {
  prevmonthvoldec_ = 0;
}
double MDIceTrace::prevmonthvoldec() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PrevMonthVolDec)
  return prevmonthvoldec_;
}
void MDIceTrace::set_prevmonthvoldec(double value) {
  
  prevmonthvoldec_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PrevMonthVolDec)
}

inline const MDIceTrace* MDIceTrace::internal_default_instance() {
  return &MDIceTrace_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
