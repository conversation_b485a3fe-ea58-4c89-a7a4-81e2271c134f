// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDTransaction.proto

#ifndef PROTOBUF_MDTransaction_2eproto__INCLUDED
#define PROTOBUF_MDTransaction_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDTransaction_2eproto();
void protobuf_InitDefaults_MDTransaction_2eproto();
void protobuf_AssignDesc_MDTransaction_2eproto();
void protobuf_ShutdownFile_MDTransaction_2eproto();

class MDTransaction;

// ===================================================================

class MDTransaction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDTransaction) */ {
 public:
  MDTransaction();
  virtual ~MDTransaction();

  MDTransaction(const MDTransaction& from);

  inline MDTransaction& operator=(const MDTransaction& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDTransaction& default_instance();

  static const MDTransaction* internal_default_instance();

  void Swap(MDTransaction* other);

  // implements Message ----------------------------------------------

  inline MDTransaction* New() const { return New(NULL); }

  MDTransaction* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDTransaction& from);
  void MergeFrom(const MDTransaction& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDTransaction* other);
  void UnsafeMergeFrom(const MDTransaction& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 TradeIndex = 7;
  void clear_tradeindex();
  static const int kTradeIndexFieldNumber = 7;
  ::google::protobuf::int64 tradeindex() const;
  void set_tradeindex(::google::protobuf::int64 value);

  // optional int64 TradeBuyNo = 8;
  void clear_tradebuyno();
  static const int kTradeBuyNoFieldNumber = 8;
  ::google::protobuf::int64 tradebuyno() const;
  void set_tradebuyno(::google::protobuf::int64 value);

  // optional int64 TradeSellNo = 9;
  void clear_tradesellno();
  static const int kTradeSellNoFieldNumber = 9;
  ::google::protobuf::int64 tradesellno() const;
  void set_tradesellno(::google::protobuf::int64 value);

  // optional int32 TradeType = 10;
  void clear_tradetype();
  static const int kTradeTypeFieldNumber = 10;
  ::google::protobuf::int32 tradetype() const;
  void set_tradetype(::google::protobuf::int32 value);

  // optional int32 TradeBSFlag = 11;
  void clear_tradebsflag();
  static const int kTradeBSFlagFieldNumber = 11;
  ::google::protobuf::int32 tradebsflag() const;
  void set_tradebsflag(::google::protobuf::int32 value);

  // optional int64 TradePrice = 12;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 12;
  ::google::protobuf::int64 tradeprice() const;
  void set_tradeprice(::google::protobuf::int64 value);

  // optional int64 TradeQty = 13;
  void clear_tradeqty();
  static const int kTradeQtyFieldNumber = 13;
  ::google::protobuf::int64 tradeqty() const;
  void set_tradeqty(::google::protobuf::int64 value);

  // optional int64 TradeMoney = 14;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 14;
  ::google::protobuf::int64 trademoney() const;
  void set_trademoney(::google::protobuf::int64 value);

  // optional int64 ApplSeqNum = 15;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 15;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // optional int32 ChannelNo = 16;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 16;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int32 ExchangeDate = 17;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 17;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 18;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 18;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 TradeCleanPrice = 19;
  void clear_tradecleanprice();
  static const int kTradeCleanPriceFieldNumber = 19;
  ::google::protobuf::int64 tradecleanprice() const;
  void set_tradecleanprice(::google::protobuf::int64 value);

  // optional int64 AccruedInterestAmt = 20;
  void clear_accruedinterestamt();
  static const int kAccruedInterestAmtFieldNumber = 20;
  ::google::protobuf::int64 accruedinterestamt() const;
  void set_accruedinterestamt(::google::protobuf::int64 value);

  // optional int64 TradeDirtyPrice = 21;
  void clear_tradedirtyprice();
  static const int kTradeDirtyPriceFieldNumber = 21;
  ::google::protobuf::int64 tradedirtyprice() const;
  void set_tradedirtyprice(::google::protobuf::int64 value);

  // optional int64 MaturityYield = 22;
  void clear_maturityyield();
  static const int kMaturityYieldFieldNumber = 22;
  ::google::protobuf::int64 maturityyield() const;
  void set_maturityyield(::google::protobuf::int64 value);

  // optional string FITradingMethod = 23;
  void clear_fitradingmethod();
  static const int kFITradingMethodFieldNumber = 23;
  const ::std::string& fitradingmethod() const;
  void set_fitradingmethod(const ::std::string& value);
  void set_fitradingmethod(const char* value);
  void set_fitradingmethod(const char* value, size_t size);
  ::std::string* mutable_fitradingmethod();
  ::std::string* release_fitradingmethod();
  void set_allocated_fitradingmethod(::std::string* fitradingmethod);

  // optional int64 AccruedInterestOtd = 24;
  void clear_accruedinterestotd();
  static const int kAccruedInterestOtdFieldNumber = 24;
  ::google::protobuf::int64 accruedinterestotd() const;
  void set_accruedinterestotd(::google::protobuf::int64 value);

  // optional int64 Duration = 25;
  void clear_duration();
  static const int kDurationFieldNumber = 25;
  ::google::protobuf::int64 duration() const;
  void set_duration(::google::protobuf::int64 value);

  // optional int64 ModifiedDuration = 26;
  void clear_modifiedduration();
  static const int kModifiedDurationFieldNumber = 26;
  ::google::protobuf::int64 modifiedduration() const;
  void set_modifiedduration(::google::protobuf::int64 value);

  // optional int64 Convexity = 27;
  void clear_convexity();
  static const int kConvexityFieldNumber = 27;
  ::google::protobuf::int64 convexity() const;
  void set_convexity(::google::protobuf::int64 value);

  // optional int32 SettlPeriod = 28;
  void clear_settlperiod();
  static const int kSettlPeriodFieldNumber = 28;
  ::google::protobuf::int32 settlperiod() const;
  void set_settlperiod(::google::protobuf::int32 value);

  // optional int32 SettlType = 29;
  void clear_settltype();
  static const int kSettlTypeFieldNumber = 29;
  ::google::protobuf::int32 settltype() const;
  void set_settltype(::google::protobuf::int32 value);

  // optional int32 HKTradeType = 30;
  void clear_hktradetype();
  static const int kHKTradeTypeFieldNumber = 30;
  ::google::protobuf::int32 hktradetype() const;
  void set_hktradetype(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 31;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 31;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string SecondaryOrderID = 32;
  void clear_secondaryorderid();
  static const int kSecondaryOrderIDFieldNumber = 32;
  const ::std::string& secondaryorderid() const;
  void set_secondaryorderid(const ::std::string& value);
  void set_secondaryorderid(const char* value);
  void set_secondaryorderid(const char* value, size_t size);
  ::std::string* mutable_secondaryorderid();
  ::std::string* release_secondaryorderid();
  void set_allocated_secondaryorderid(::std::string* secondaryorderid);

  // optional int32 BidExecInstType = 33;
  void clear_bidexecinsttype();
  static const int kBidExecInstTypeFieldNumber = 33;
  ::google::protobuf::int32 bidexecinsttype() const;
  void set_bidexecinsttype(::google::protobuf::int32 value);

  // optional int64 MarginPrice = 34;
  void clear_marginprice();
  static const int kMarginPriceFieldNumber = 34;
  ::google::protobuf::int64 marginprice() const;
  void set_marginprice(::google::protobuf::int64 value);

  // optional string DealDate = 35;
  void clear_dealdate();
  static const int kDealDateFieldNumber = 35;
  const ::std::string& dealdate() const;
  void set_dealdate(const ::std::string& value);
  void set_dealdate(const char* value);
  void set_dealdate(const char* value, size_t size);
  ::std::string* mutable_dealdate();
  ::std::string* release_dealdate();
  void set_allocated_dealdate(::std::string* dealdate);

  // optional string DealTime = 36;
  void clear_dealtime();
  static const int kDealTimeFieldNumber = 36;
  const ::std::string& dealtime() const;
  void set_dealtime(const ::std::string& value);
  void set_dealtime(const char* value);
  void set_dealtime(const char* value, size_t size);
  ::std::string* mutable_dealtime();
  ::std::string* release_dealtime();
  void set_allocated_dealtime(::std::string* dealtime);

  // optional string DealNumber = 37;
  void clear_dealnumber();
  static const int kDealNumberFieldNumber = 37;
  const ::std::string& dealnumber() const;
  void set_dealnumber(const ::std::string& value);
  void set_dealnumber(const char* value);
  void set_dealnumber(const char* value, size_t size);
  ::std::string* mutable_dealnumber();
  ::std::string* release_dealnumber();
  void set_allocated_dealnumber(::std::string* dealnumber);

  // optional int32 MarketIndicator = 38;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 38;
  ::google::protobuf::int32 marketindicator() const;
  void set_marketindicator(::google::protobuf::int32 value);

  // optional int32 RepoTerm = 39;
  void clear_repoterm();
  static const int kRepoTermFieldNumber = 39;
  ::google::protobuf::int32 repoterm() const;
  void set_repoterm(::google::protobuf::int32 value);

  // optional int64 LegSettlementAmount1st = 40;
  void clear_legsettlementamount1st();
  static const int kLegSettlementAmount1StFieldNumber = 40;
  ::google::protobuf::int64 legsettlementamount1st() const;
  void set_legsettlementamount1st(::google::protobuf::int64 value);

  // optional int64 LegSettlementAmount2nd = 41;
  void clear_legsettlementamount2nd();
  static const int kLegSettlementAmount2NdFieldNumber = 41;
  ::google::protobuf::int64 legsettlementamount2nd() const;
  void set_legsettlementamount2nd(::google::protobuf::int64 value);

  // optional string BondCode = 42;
  void clear_bondcode();
  static const int kBondCodeFieldNumber = 42;
  const ::std::string& bondcode() const;
  void set_bondcode(const ::std::string& value);
  void set_bondcode(const char* value);
  void set_bondcode(const char* value, size_t size);
  ::std::string* mutable_bondcode();
  ::std::string* release_bondcode();
  void set_allocated_bondcode(::std::string* bondcode);

  // optional string BondName = 43;
  void clear_bondname();
  static const int kBondNameFieldNumber = 43;
  const ::std::string& bondname() const;
  void set_bondname(const ::std::string& value);
  void set_bondname(const char* value);
  void set_bondname(const char* value, size_t size);
  ::std::string* mutable_bondname();
  ::std::string* release_bondname();
  void set_allocated_bondname(::std::string* bondname);

  // optional int64 TotalFacevalue = 44;
  void clear_totalfacevalue();
  static const int kTotalFacevalueFieldNumber = 44;
  ::google::protobuf::int64 totalfacevalue() const;
  void set_totalfacevalue(::google::protobuf::int64 value);

  // optional int64 LegCleanPrice1st = 45;
  void clear_legcleanprice1st();
  static const int kLegCleanPrice1StFieldNumber = 45;
  ::google::protobuf::int64 legcleanprice1st() const;
  void set_legcleanprice1st(::google::protobuf::int64 value);

  // optional int64 LegCleanPrice2nd = 46;
  void clear_legcleanprice2nd();
  static const int kLegCleanPrice2NdFieldNumber = 46;
  ::google::protobuf::int64 legcleanprice2nd() const;
  void set_legcleanprice2nd(::google::protobuf::int64 value);

  // optional int64 LegYield1st = 47;
  void clear_legyield1st();
  static const int kLegYield1StFieldNumber = 47;
  ::google::protobuf::int64 legyield1st() const;
  void set_legyield1st(::google::protobuf::int64 value);

  // optional int64 LegYield2nd = 48;
  void clear_legyield2nd();
  static const int kLegYield2NdFieldNumber = 48;
  ::google::protobuf::int64 legyield2nd() const;
  void set_legyield2nd(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDTransaction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr fitradingmethod_;
  ::google::protobuf::internal::ArenaStringPtr secondaryorderid_;
  ::google::protobuf::internal::ArenaStringPtr dealdate_;
  ::google::protobuf::internal::ArenaStringPtr dealtime_;
  ::google::protobuf::internal::ArenaStringPtr dealnumber_;
  ::google::protobuf::internal::ArenaStringPtr bondcode_;
  ::google::protobuf::internal::ArenaStringPtr bondname_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 tradeindex_;
  ::google::protobuf::int64 tradebuyno_;
  ::google::protobuf::int64 tradesellno_;
  ::google::protobuf::int32 tradetype_;
  ::google::protobuf::int32 tradebsflag_;
  ::google::protobuf::int64 tradeprice_;
  ::google::protobuf::int64 tradeqty_;
  ::google::protobuf::int64 trademoney_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 tradecleanprice_;
  ::google::protobuf::int64 accruedinterestamt_;
  ::google::protobuf::int64 tradedirtyprice_;
  ::google::protobuf::int64 maturityyield_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 settlperiod_;
  ::google::protobuf::int64 accruedinterestotd_;
  ::google::protobuf::int64 duration_;
  ::google::protobuf::int64 modifiedduration_;
  ::google::protobuf::int64 convexity_;
  ::google::protobuf::int32 settltype_;
  ::google::protobuf::int32 hktradetype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 bidexecinsttype_;
  ::google::protobuf::int64 marginprice_;
  ::google::protobuf::int32 marketindicator_;
  ::google::protobuf::int32 repoterm_;
  ::google::protobuf::int64 legsettlementamount1st_;
  ::google::protobuf::int64 legsettlementamount2nd_;
  ::google::protobuf::int64 totalfacevalue_;
  ::google::protobuf::int64 legcleanprice1st_;
  ::google::protobuf::int64 legcleanprice2nd_;
  ::google::protobuf::int64 legyield1st_;
  ::google::protobuf::int64 legyield2nd_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDTransaction_2eproto_impl();
  friend void  protobuf_AddDesc_MDTransaction_2eproto_impl();
  friend void protobuf_AssignDesc_MDTransaction_2eproto();
  friend void protobuf_ShutdownFile_MDTransaction_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDTransaction> MDTransaction_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDTransaction

// optional string HTSCSecurityID = 1;
inline void MDTransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
inline void MDTransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
inline void MDTransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
inline ::std::string* MDTransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDTransaction::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MDDate)
  return mddate_;
}
inline void MDTransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MDDate)
}

// optional int32 MDTime = 3;
inline void MDTransaction::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MDTime)
  return mdtime_;
}
inline void MDTransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDTransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DataTimestamp)
  return datatimestamp_;
}
inline void MDTransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDTransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDTransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDTransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDTransaction::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDTransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDTransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.securityType)
}

// optional int64 TradeIndex = 7;
inline void MDTransaction::clear_tradeindex() {
  tradeindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeIndex)
  return tradeindex_;
}
inline void MDTransaction::set_tradeindex(::google::protobuf::int64 value) {
  
  tradeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeIndex)
}

// optional int64 TradeBuyNo = 8;
inline void MDTransaction::clear_tradebuyno() {
  tradebuyno_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradebuyno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeBuyNo)
  return tradebuyno_;
}
inline void MDTransaction::set_tradebuyno(::google::protobuf::int64 value) {
  
  tradebuyno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeBuyNo)
}

// optional int64 TradeSellNo = 9;
inline void MDTransaction::clear_tradesellno() {
  tradesellno_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradesellno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeSellNo)
  return tradesellno_;
}
inline void MDTransaction::set_tradesellno(::google::protobuf::int64 value) {
  
  tradesellno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeSellNo)
}

// optional int32 TradeType = 10;
inline void MDTransaction::clear_tradetype() {
  tradetype_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeType)
  return tradetype_;
}
inline void MDTransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeType)
}

// optional int32 TradeBSFlag = 11;
inline void MDTransaction::clear_tradebsflag() {
  tradebsflag_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::tradebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeBSFlag)
  return tradebsflag_;
}
inline void MDTransaction::set_tradebsflag(::google::protobuf::int32 value) {
  
  tradebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeBSFlag)
}

// optional int64 TradePrice = 12;
inline void MDTransaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradePrice)
  return tradeprice_;
}
inline void MDTransaction::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradePrice)
}

// optional int64 TradeQty = 13;
inline void MDTransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeQty)
  return tradeqty_;
}
inline void MDTransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeQty)
}

// optional int64 TradeMoney = 14;
inline void MDTransaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeMoney)
  return trademoney_;
}
inline void MDTransaction::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeMoney)
}

// optional int64 ApplSeqNum = 15;
inline void MDTransaction::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ApplSeqNum)
  return applseqnum_;
}
inline void MDTransaction::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ApplSeqNum)
}

// optional int32 ChannelNo = 16;
inline void MDTransaction::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ChannelNo)
  return channelno_;
}
inline void MDTransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ChannelNo)
}

// optional int32 ExchangeDate = 17;
inline void MDTransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ExchangeDate)
  return exchangedate_;
}
inline void MDTransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 18;
inline void MDTransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ExchangeTime)
  return exchangetime_;
}
inline void MDTransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ExchangeTime)
}

// optional int64 TradeCleanPrice = 19;
inline void MDTransaction::clear_tradecleanprice() {
  tradecleanprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradecleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeCleanPrice)
  return tradecleanprice_;
}
inline void MDTransaction::set_tradecleanprice(::google::protobuf::int64 value) {
  
  tradecleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeCleanPrice)
}

// optional int64 AccruedInterestAmt = 20;
inline void MDTransaction::clear_accruedinterestamt() {
  accruedinterestamt_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::accruedinterestamt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestAmt)
  return accruedinterestamt_;
}
inline void MDTransaction::set_accruedinterestamt(::google::protobuf::int64 value) {
  
  accruedinterestamt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestAmt)
}

// optional int64 TradeDirtyPrice = 21;
inline void MDTransaction::clear_tradedirtyprice() {
  tradedirtyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::tradedirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeDirtyPrice)
  return tradedirtyprice_;
}
inline void MDTransaction::set_tradedirtyprice(::google::protobuf::int64 value) {
  
  tradedirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeDirtyPrice)
}

// optional int64 MaturityYield = 22;
inline void MDTransaction::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MaturityYield)
  return maturityyield_;
}
inline void MDTransaction::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MaturityYield)
}

// optional string FITradingMethod = 23;
inline void MDTransaction::clear_fitradingmethod() {
  fitradingmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::fitradingmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  return fitradingmethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_fitradingmethod(const ::std::string& value) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
inline void MDTransaction::set_fitradingmethod(const char* value) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
inline void MDTransaction::set_fitradingmethod(const char* value, size_t size) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
inline ::std::string* MDTransaction::mutable_fitradingmethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  return fitradingmethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_fitradingmethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  
  return fitradingmethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_fitradingmethod(::std::string* fitradingmethod) {
  if (fitradingmethod != NULL) {
    
  } else {
    
  }
  fitradingmethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fitradingmethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}

// optional int64 AccruedInterestOtd = 24;
inline void MDTransaction::clear_accruedinterestotd() {
  accruedinterestotd_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::accruedinterestotd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestOtd)
  return accruedinterestotd_;
}
inline void MDTransaction::set_accruedinterestotd(::google::protobuf::int64 value) {
  
  accruedinterestotd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestOtd)
}

// optional int64 Duration = 25;
inline void MDTransaction::clear_duration() {
  duration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::duration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.Duration)
  return duration_;
}
inline void MDTransaction::set_duration(::google::protobuf::int64 value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.Duration)
}

// optional int64 ModifiedDuration = 26;
inline void MDTransaction::clear_modifiedduration() {
  modifiedduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::modifiedduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ModifiedDuration)
  return modifiedduration_;
}
inline void MDTransaction::set_modifiedduration(::google::protobuf::int64 value) {
  
  modifiedduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ModifiedDuration)
}

// optional int64 Convexity = 27;
inline void MDTransaction::clear_convexity() {
  convexity_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::convexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.Convexity)
  return convexity_;
}
inline void MDTransaction::set_convexity(::google::protobuf::int64 value) {
  
  convexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.Convexity)
}

// optional int32 SettlPeriod = 28;
inline void MDTransaction::clear_settlperiod() {
  settlperiod_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::settlperiod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SettlPeriod)
  return settlperiod_;
}
inline void MDTransaction::set_settlperiod(::google::protobuf::int32 value) {
  
  settlperiod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SettlPeriod)
}

// optional int32 SettlType = 29;
inline void MDTransaction::clear_settltype() {
  settltype_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SettlType)
  return settltype_;
}
inline void MDTransaction::set_settltype(::google::protobuf::int32 value) {
  
  settltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SettlType)
}

// optional int32 HKTradeType = 30;
inline void MDTransaction::clear_hktradetype() {
  hktradetype_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::hktradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.HKTradeType)
  return hktradetype_;
}
inline void MDTransaction::set_hktradetype(::google::protobuf::int32 value) {
  
  hktradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.HKTradeType)
}

// optional int32 DataMultiplePowerOf10 = 31;
inline void MDTransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDTransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DataMultiplePowerOf10)
}

// optional string SecondaryOrderID = 32;
inline void MDTransaction::clear_secondaryorderid() {
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::secondaryorderid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  return secondaryorderid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_secondaryorderid(const ::std::string& value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
inline void MDTransaction::set_secondaryorderid(const char* value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
inline void MDTransaction::set_secondaryorderid(const char* value, size_t size) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
inline ::std::string* MDTransaction::mutable_secondaryorderid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  return secondaryorderid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_secondaryorderid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  
  return secondaryorderid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_secondaryorderid(::std::string* secondaryorderid) {
  if (secondaryorderid != NULL) {
    
  } else {
    
  }
  secondaryorderid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secondaryorderid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}

// optional int32 BidExecInstType = 33;
inline void MDTransaction::clear_bidexecinsttype() {
  bidexecinsttype_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::bidexecinsttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BidExecInstType)
  return bidexecinsttype_;
}
inline void MDTransaction::set_bidexecinsttype(::google::protobuf::int32 value) {
  
  bidexecinsttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BidExecInstType)
}

// optional int64 MarginPrice = 34;
inline void MDTransaction::clear_marginprice() {
  marginprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::marginprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MarginPrice)
  return marginprice_;
}
inline void MDTransaction::set_marginprice(::google::protobuf::int64 value) {
  
  marginprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MarginPrice)
}

// optional string DealDate = 35;
inline void MDTransaction::clear_dealdate() {
  dealdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::dealdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  return dealdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_dealdate(const ::std::string& value) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
inline void MDTransaction::set_dealdate(const char* value) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
inline void MDTransaction::set_dealdate(const char* value, size_t size) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
inline ::std::string* MDTransaction::mutable_dealdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  return dealdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_dealdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  
  return dealdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_dealdate(::std::string* dealdate) {
  if (dealdate != NULL) {
    
  } else {
    
  }
  dealdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}

// optional string DealTime = 36;
inline void MDTransaction::clear_dealtime() {
  dealtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::dealtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  return dealtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_dealtime(const ::std::string& value) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
inline void MDTransaction::set_dealtime(const char* value) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
inline void MDTransaction::set_dealtime(const char* value, size_t size) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
inline ::std::string* MDTransaction::mutable_dealtime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  return dealtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_dealtime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  
  return dealtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_dealtime(::std::string* dealtime) {
  if (dealtime != NULL) {
    
  } else {
    
  }
  dealtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealtime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}

// optional string DealNumber = 37;
inline void MDTransaction::clear_dealnumber() {
  dealnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::dealnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  return dealnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_dealnumber(const ::std::string& value) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
inline void MDTransaction::set_dealnumber(const char* value) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
inline void MDTransaction::set_dealnumber(const char* value, size_t size) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
inline ::std::string* MDTransaction::mutable_dealnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  return dealnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_dealnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  
  return dealnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_dealnumber(::std::string* dealnumber) {
  if (dealnumber != NULL) {
    
  } else {
    
  }
  dealnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}

// optional int32 MarketIndicator = 38;
inline void MDTransaction::clear_marketindicator() {
  marketindicator_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MarketIndicator)
  return marketindicator_;
}
inline void MDTransaction::set_marketindicator(::google::protobuf::int32 value) {
  
  marketindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MarketIndicator)
}

// optional int32 RepoTerm = 39;
inline void MDTransaction::clear_repoterm() {
  repoterm_ = 0;
}
inline ::google::protobuf::int32 MDTransaction::repoterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.RepoTerm)
  return repoterm_;
}
inline void MDTransaction::set_repoterm(::google::protobuf::int32 value) {
  
  repoterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.RepoTerm)
}

// optional int64 LegSettlementAmount1st = 40;
inline void MDTransaction::clear_legsettlementamount1st() {
  legsettlementamount1st_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legsettlementamount1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount1st)
  return legsettlementamount1st_;
}
inline void MDTransaction::set_legsettlementamount1st(::google::protobuf::int64 value) {
  
  legsettlementamount1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount1st)
}

// optional int64 LegSettlementAmount2nd = 41;
inline void MDTransaction::clear_legsettlementamount2nd() {
  legsettlementamount2nd_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legsettlementamount2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount2nd)
  return legsettlementamount2nd_;
}
inline void MDTransaction::set_legsettlementamount2nd(::google::protobuf::int64 value) {
  
  legsettlementamount2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount2nd)
}

// optional string BondCode = 42;
inline void MDTransaction::clear_bondcode() {
  bondcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::bondcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  return bondcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_bondcode(const ::std::string& value) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
inline void MDTransaction::set_bondcode(const char* value) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
inline void MDTransaction::set_bondcode(const char* value, size_t size) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
inline ::std::string* MDTransaction::mutable_bondcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  return bondcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_bondcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  
  return bondcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_bondcode(::std::string* bondcode) {
  if (bondcode != NULL) {
    
  } else {
    
  }
  bondcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bondcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}

// optional string BondName = 43;
inline void MDTransaction::clear_bondname() {
  bondname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDTransaction::bondname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BondName)
  return bondname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_bondname(const ::std::string& value) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
inline void MDTransaction::set_bondname(const char* value) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
inline void MDTransaction::set_bondname(const char* value, size_t size) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
inline ::std::string* MDTransaction::mutable_bondname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.BondName)
  return bondname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDTransaction::release_bondname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.BondName)
  
  return bondname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDTransaction::set_allocated_bondname(::std::string* bondname) {
  if (bondname != NULL) {
    
  } else {
    
  }
  bondname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bondname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.BondName)
}

// optional int64 TotalFacevalue = 44;
inline void MDTransaction::clear_totalfacevalue() {
  totalfacevalue_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::totalfacevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TotalFacevalue)
  return totalfacevalue_;
}
inline void MDTransaction::set_totalfacevalue(::google::protobuf::int64 value) {
  
  totalfacevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TotalFacevalue)
}

// optional int64 LegCleanPrice1st = 45;
inline void MDTransaction::clear_legcleanprice1st() {
  legcleanprice1st_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legcleanprice1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice1st)
  return legcleanprice1st_;
}
inline void MDTransaction::set_legcleanprice1st(::google::protobuf::int64 value) {
  
  legcleanprice1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice1st)
}

// optional int64 LegCleanPrice2nd = 46;
inline void MDTransaction::clear_legcleanprice2nd() {
  legcleanprice2nd_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legcleanprice2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice2nd)
  return legcleanprice2nd_;
}
inline void MDTransaction::set_legcleanprice2nd(::google::protobuf::int64 value) {
  
  legcleanprice2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice2nd)
}

// optional int64 LegYield1st = 47;
inline void MDTransaction::clear_legyield1st() {
  legyield1st_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legyield1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegYield1st)
  return legyield1st_;
}
inline void MDTransaction::set_legyield1st(::google::protobuf::int64 value) {
  
  legyield1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegYield1st)
}

// optional int64 LegYield2nd = 48;
inline void MDTransaction::clear_legyield2nd() {
  legyield2nd_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDTransaction::legyield2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegYield2nd)
  return legyield2nd_;
}
inline void MDTransaction::set_legyield2nd(::google::protobuf::int64 value) {
  
  legyield2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegYield2nd)
}

inline const MDTransaction* MDTransaction::internal_default_instance() {
  return &MDTransaction_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDTransaction_2eproto__INCLUDED
