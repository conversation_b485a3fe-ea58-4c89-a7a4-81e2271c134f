syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsCurrencySnapshot {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  CurrencySnapshotDetail CurrencySnapshotDetail = 16;
  int64 MessageNumber = 100;
}

message CurrencySnapshotDetail {
  string BaseCurrency = 1;
  string QuoteCurrency = 2;
  double Bid = 3;
  double Ask = 4;
  double Last = 5;
  double High = 6;
  double Low = 7;
  double Volume = 8;
}
