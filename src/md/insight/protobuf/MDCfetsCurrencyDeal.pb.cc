// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsCurrencyDeal.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsCurrencyDeal.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsCurrencyDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsCurrencyDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* InterBankOfferingDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  InterBankOfferingDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* CollateralRepoDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CollateralRepoDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* OutrightRepoDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OutrightRepoDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* UnderlyingSecurityDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  UnderlyingSecurityDetail_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto() {
  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsCurrencyDeal.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsCurrencyDeal_descriptor_ = file->message_type(0);
  static const int MDCfetsCurrencyDeal_offsets_[14] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, messagenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, currencydealtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, interbankofferingdeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, collateralrepodeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, outrightrepodeal_),
  };
  MDCfetsCurrencyDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsCurrencyDeal_descriptor_,
      MDCfetsCurrencyDeal::internal_default_instance(),
      MDCfetsCurrencyDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsCurrencyDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencyDeal, _internal_metadata_));
  InterBankOfferingDeal_descriptor_ = file->message_type(1);
  static const int InterBankOfferingDeal_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, tradeyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, term_),
  };
  InterBankOfferingDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      InterBankOfferingDeal_descriptor_,
      InterBankOfferingDeal::internal_default_instance(),
      InterBankOfferingDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(InterBankOfferingDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingDeal, _internal_metadata_));
  CollateralRepoDeal_descriptor_ = file->message_type(2);
  static const int CollateralRepoDeal_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, tradeyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, term_),
  };
  CollateralRepoDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CollateralRepoDeal_descriptor_,
      CollateralRepoDeal::internal_default_instance(),
      CollateralRepoDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(CollateralRepoDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoDeal, _internal_metadata_));
  OutrightRepoDeal_descriptor_ = file->message_type(3);
  static const int OutrightRepoDeal_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, tradeyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, term_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, totaltrademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, totalsettlcurramt_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, totalsettlcurramt2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, securitydetaillist_),
  };
  OutrightRepoDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OutrightRepoDeal_descriptor_,
      OutrightRepoDeal::internal_default_instance(),
      OutrightRepoDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(OutrightRepoDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoDeal, _internal_metadata_));
  UnderlyingSecurityDetail_descriptor_ = file->message_type(4);
  static const int UnderlyingSecurityDetail_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingsettlcurramt_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingsettlcurramt2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingpx2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, underlyingyield2_),
  };
  UnderlyingSecurityDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      UnderlyingSecurityDetail_descriptor_,
      UnderlyingSecurityDetail::internal_default_instance(),
      UnderlyingSecurityDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(UnderlyingSecurityDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UnderlyingSecurityDetail, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsCurrencyDeal_descriptor_, MDCfetsCurrencyDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      InterBankOfferingDeal_descriptor_, InterBankOfferingDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CollateralRepoDeal_descriptor_, CollateralRepoDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OutrightRepoDeal_descriptor_, OutrightRepoDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      UnderlyingSecurityDetail_descriptor_, UnderlyingSecurityDetail::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto() {
  MDCfetsCurrencyDeal_default_instance_.Shutdown();
  delete MDCfetsCurrencyDeal_reflection_;
  InterBankOfferingDeal_default_instance_.Shutdown();
  delete InterBankOfferingDeal_reflection_;
  CollateralRepoDeal_default_instance_.Shutdown();
  delete CollateralRepoDeal_reflection_;
  OutrightRepoDeal_default_instance_.Shutdown();
  delete OutrightRepoDeal_reflection_;
  UnderlyingSecurityDetail_default_instance_.Shutdown();
  delete UnderlyingSecurityDetail_reflection_;
}

void protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsCurrencyDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  InterBankOfferingDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  CollateralRepoDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  OutrightRepoDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  UnderlyingSecurityDetail_default_instance_.DefaultConstruct();
  MDCfetsCurrencyDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  InterBankOfferingDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  CollateralRepoDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  OutrightRepoDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  UnderlyingSecurityDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_once_);
void protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031MDCfetsCurrencyDeal.proto\022\032com.htsc.md"
    "c.insight.model\032\027ESecurityIDSource.proto"
    "\032\023ESecurityType.proto\"\303\004\n\023MDCfetsCurrenc"
    "yDeal\022\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n\014Securit"
    "yType\030\002 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\?\n\020SecurityIDSource\030\003 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\022\016\n\006MDDat"
    "e\030\004 \001(\005\022\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp"
    "\030\006 \001(\003\022\024\n\014TransactTime\030\007 \001(\t\022\027\n\017MarketIn"
    "dicator\030\010 \001(\t\022\035\n\025DataMultiplePowerOf10\030\t"
    " \001(\005\022\025\n\rMessageNumber\030\020 \001(\003\022\030\n\020CurrencyD"
    "ealType\030\025 \001(\005\022P\n\025InterBankOfferingDeal\030\026"
    " \001(\01321.com.htsc.mdc.insight.model.InterB"
    "ankOfferingDeal\022J\n\022CollateralRepoDeal\030\027 "
    "\001(\0132..com.htsc.mdc.insight.model.Collate"
    "ralRepoDeal\022F\n\020OutrightRepoDeal\030\030 \001(\0132,."
    "com.htsc.mdc.insight.model.OutrightRepoD"
    "eal\"s\n\025InterBankOfferingDeal\022\021\n\tTradeDat"
    "e\030\001 \001(\t\022\021\n\tTradeTime\030\002 \001(\t\022\022\n\nTradeYield"
    "\030\003 \001(\001\022\022\n\nTradeMoney\030\004 \001(\001\022\014\n\004Term\030\005 \001(\001"
    "\"p\n\022CollateralRepoDeal\022\021\n\tTradeDate\030\001 \001("
    "\t\022\021\n\tTradeTime\030\002 \001(\t\022\022\n\nTradeYield\030\003 \001(\001"
    "\022\022\n\nTradeMoney\030\004 \001(\001\022\014\n\004Term\030\005 \001(\001\"\374\001\n\020O"
    "utrightRepoDeal\022\021\n\tTradeDate\030\001 \001(\t\022\021\n\tTr"
    "adeTime\030\002 \001(\t\022\022\n\nTradeYield\030\003 \001(\001\022\014\n\004Ter"
    "m\030\004 \001(\001\022\027\n\017TotalTradeMoney\030\005 \001(\001\022\031\n\021Tota"
    "lSettlCurrAmt\030\006 \001(\001\022\032\n\022TotalSettlCurrAmt"
    "2\030\007 \001(\001\022P\n\022SecurityDetailList\030\n \003(\01324.co"
    "m.htsc.mdc.insight.model.UnderlyingSecur"
    "ityDetail\"\212\002\n\030UnderlyingSecurityDetail\022\034"
    "\n\024UnderlyingSecurityID\030\001 \001(\t\022\030\n\020Underlyi"
    "ngSymbol\030\002 \001(\t\022\036\n\026UnderlyingSettlCurrAmt"
    "\030\003 \001(\001\022\037\n\027UnderlyingSettlCurrAmt2\030\004 \001(\001\022"
    "\025\n\rUnderlyingQty\030\005 \001(\001\022\024\n\014UnderlyingPx\030\006"
    " \001(\001\022\025\n\rUnderlyingPx2\030\007 \001(\001\022\027\n\017Underlyin"
    "gYield\030\010 \001(\001\022\030\n\020UnderlyingYield2\030\t \001(\001B<"
    "\n\032com.htsc.mdc.insight.modelB\031MDCfetsCur"
    "rencyDealProtosH\001\240\001\001b\006proto3", 1508);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsCurrencyDeal.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_once_);
void protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsCurrencyDeal_2eproto {
  StaticDescriptorInitializer_MDCfetsCurrencyDeal_2eproto() {
    protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto();
  }
} static_descriptor_initializer_MDCfetsCurrencyDeal_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsCurrencyDeal::kHTSCSecurityIDFieldNumber;
const int MDCfetsCurrencyDeal::kSecurityTypeFieldNumber;
const int MDCfetsCurrencyDeal::kSecurityIDSourceFieldNumber;
const int MDCfetsCurrencyDeal::kMDDateFieldNumber;
const int MDCfetsCurrencyDeal::kMDTimeFieldNumber;
const int MDCfetsCurrencyDeal::kDataTimestampFieldNumber;
const int MDCfetsCurrencyDeal::kTransactTimeFieldNumber;
const int MDCfetsCurrencyDeal::kMarketIndicatorFieldNumber;
const int MDCfetsCurrencyDeal::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsCurrencyDeal::kMessageNumberFieldNumber;
const int MDCfetsCurrencyDeal::kCurrencyDealTypeFieldNumber;
const int MDCfetsCurrencyDeal::kInterBankOfferingDealFieldNumber;
const int MDCfetsCurrencyDeal::kCollateralRepoDealFieldNumber;
const int MDCfetsCurrencyDeal::kOutrightRepoDealFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsCurrencyDeal::MDCfetsCurrencyDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
}

void MDCfetsCurrencyDeal::InitAsDefaultInstance() {
  interbankofferingdeal_ = const_cast< ::com::htsc::mdc::insight::model::InterBankOfferingDeal*>(
      ::com::htsc::mdc::insight::model::InterBankOfferingDeal::internal_default_instance());
  collateralrepodeal_ = const_cast< ::com::htsc::mdc::insight::model::CollateralRepoDeal*>(
      ::com::htsc::mdc::insight::model::CollateralRepoDeal::internal_default_instance());
  outrightrepodeal_ = const_cast< ::com::htsc::mdc::insight::model::OutrightRepoDeal*>(
      ::com::htsc::mdc::insight::model::OutrightRepoDeal::internal_default_instance());
}

MDCfetsCurrencyDeal::MDCfetsCurrencyDeal(const MDCfetsCurrencyDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
}

void MDCfetsCurrencyDeal::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  interbankofferingdeal_ = NULL;
  collateralrepodeal_ = NULL;
  outrightrepodeal_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&currencydealtype_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(currencydealtype_));
  _cached_size_ = 0;
}

MDCfetsCurrencyDeal::~MDCfetsCurrencyDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  SharedDtor();
}

void MDCfetsCurrencyDeal::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsCurrencyDeal_default_instance_.get()) {
    delete interbankofferingdeal_;
    delete collateralrepodeal_;
    delete outrightrepodeal_;
  }
}

void MDCfetsCurrencyDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsCurrencyDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsCurrencyDeal_descriptor_;
}

const MDCfetsCurrencyDeal& MDCfetsCurrencyDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsCurrencyDeal> MDCfetsCurrencyDeal_default_instance_;

MDCfetsCurrencyDeal* MDCfetsCurrencyDeal::New(::google::protobuf::Arena* arena) const {
  MDCfetsCurrencyDeal* n = new MDCfetsCurrencyDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsCurrencyDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsCurrencyDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsCurrencyDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(messagenumber_, currencydealtype_);
  if (GetArenaNoVirtual() == NULL && interbankofferingdeal_ != NULL) delete interbankofferingdeal_;
  interbankofferingdeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && collateralrepodeal_ != NULL) delete collateralrepodeal_;
  collateralrepodeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && outrightrepodeal_ != NULL) delete outrightrepodeal_;
  outrightrepodeal_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsCurrencyDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 16;
      case 16: {
        if (tag == 128) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_CurrencyDealType;
        break;
      }

      // optional int32 CurrencyDealType = 21;
      case 21: {
        if (tag == 168) {
         parse_CurrencyDealType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &currencydealtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_InterBankOfferingDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
      case 22: {
        if (tag == 178) {
         parse_InterBankOfferingDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_interbankofferingdeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_CollateralRepoDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
      case 23: {
        if (tag == 186) {
         parse_CollateralRepoDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_collateralrepodeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_OutrightRepoDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
      case 24: {
        if (tag == 194) {
         parse_OutrightRepoDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_outrightrepodeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  return false;
#undef DO_
}

void MDCfetsCurrencyDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->messagenumber(), output);
  }

  // optional int32 CurrencyDealType = 21;
  if (this->currencydealtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->currencydealtype(), output);
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
  if (this->has_interbankofferingdeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, *this->interbankofferingdeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
  if (this->has_collateralrepodeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, *this->collateralrepodeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
  if (this->has_outrightrepodeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      24, *this->outrightrepodeal_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
}

::google::protobuf::uint8* MDCfetsCurrencyDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->messagenumber(), target);
  }

  // optional int32 CurrencyDealType = 21;
  if (this->currencydealtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->currencydealtype(), target);
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
  if (this->has_interbankofferingdeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, *this->interbankofferingdeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
  if (this->has_collateralrepodeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, *this->collateralrepodeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
  if (this->has_outrightrepodeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        24, *this->outrightrepodeal_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  return target;
}

size_t MDCfetsCurrencyDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  // optional int32 CurrencyDealType = 21;
  if (this->currencydealtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->currencydealtype());
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
  if (this->has_interbankofferingdeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->interbankofferingdeal_);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
  if (this->has_collateralrepodeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->collateralrepodeal_);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
  if (this->has_outrightrepodeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->outrightrepodeal_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsCurrencyDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsCurrencyDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsCurrencyDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsCurrencyDeal::MergeFrom(const MDCfetsCurrencyDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsCurrencyDeal::UnsafeMergeFrom(const MDCfetsCurrencyDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
  if (from.currencydealtype() != 0) {
    set_currencydealtype(from.currencydealtype());
  }
  if (from.has_interbankofferingdeal()) {
    mutable_interbankofferingdeal()->::com::htsc::mdc::insight::model::InterBankOfferingDeal::MergeFrom(from.interbankofferingdeal());
  }
  if (from.has_collateralrepodeal()) {
    mutable_collateralrepodeal()->::com::htsc::mdc::insight::model::CollateralRepoDeal::MergeFrom(from.collateralrepodeal());
  }
  if (from.has_outrightrepodeal()) {
    mutable_outrightrepodeal()->::com::htsc::mdc::insight::model::OutrightRepoDeal::MergeFrom(from.outrightrepodeal());
  }
}

void MDCfetsCurrencyDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsCurrencyDeal::CopyFrom(const MDCfetsCurrencyDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsCurrencyDeal::IsInitialized() const {

  return true;
}

void MDCfetsCurrencyDeal::Swap(MDCfetsCurrencyDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsCurrencyDeal::InternalSwap(MDCfetsCurrencyDeal* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(messagenumber_, other->messagenumber_);
  std::swap(currencydealtype_, other->currencydealtype_);
  std::swap(interbankofferingdeal_, other->interbankofferingdeal_);
  std::swap(collateralrepodeal_, other->collateralrepodeal_);
  std::swap(outrightrepodeal_, other->outrightrepodeal_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsCurrencyDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsCurrencyDeal_descriptor_;
  metadata.reflection = MDCfetsCurrencyDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsCurrencyDeal

// optional string HTSCSecurityID = 1;
void MDCfetsCurrencyDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencyDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
void MDCfetsCurrencyDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
void MDCfetsCurrencyDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
::std::string* MDCfetsCurrencyDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencyDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsCurrencyDeal::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsCurrencyDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsCurrencyDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsCurrencyDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsCurrencyDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsCurrencyDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsCurrencyDeal::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencyDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDDate)
  return mddate_;
}
void MDCfetsCurrencyDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsCurrencyDeal::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencyDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDTime)
  return mdtime_;
}
void MDCfetsCurrencyDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsCurrencyDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsCurrencyDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsCurrencyDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsCurrencyDeal::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencyDeal::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
void MDCfetsCurrencyDeal::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
void MDCfetsCurrencyDeal::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
::std::string* MDCfetsCurrencyDeal::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencyDeal::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsCurrencyDeal::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencyDeal::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
void MDCfetsCurrencyDeal::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
void MDCfetsCurrencyDeal::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
::std::string* MDCfetsCurrencyDeal::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencyDeal::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencyDeal::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDCfetsCurrencyDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencyDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsCurrencyDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 16;
void MDCfetsCurrencyDeal::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsCurrencyDeal::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MessageNumber)
  return messagenumber_;
}
void MDCfetsCurrencyDeal::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MessageNumber)
}

// optional int32 CurrencyDealType = 21;
void MDCfetsCurrencyDeal::clear_currencydealtype() {
  currencydealtype_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencyDeal::currencydealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CurrencyDealType)
  return currencydealtype_;
}
void MDCfetsCurrencyDeal::set_currencydealtype(::google::protobuf::int32 value) {
  
  currencydealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CurrencyDealType)
}

// optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
bool MDCfetsCurrencyDeal::has_interbankofferingdeal() const {
  return this != internal_default_instance() && interbankofferingdeal_ != NULL;
}
void MDCfetsCurrencyDeal::clear_interbankofferingdeal() {
  if (GetArenaNoVirtual() == NULL && interbankofferingdeal_ != NULL) delete interbankofferingdeal_;
  interbankofferingdeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::InterBankOfferingDeal& MDCfetsCurrencyDeal::interbankofferingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  return interbankofferingdeal_ != NULL ? *interbankofferingdeal_
                         : *::com::htsc::mdc::insight::model::InterBankOfferingDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::InterBankOfferingDeal* MDCfetsCurrencyDeal::mutable_interbankofferingdeal() {
  
  if (interbankofferingdeal_ == NULL) {
    interbankofferingdeal_ = new ::com::htsc::mdc::insight::model::InterBankOfferingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  return interbankofferingdeal_;
}
::com::htsc::mdc::insight::model::InterBankOfferingDeal* MDCfetsCurrencyDeal::release_interbankofferingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  
  ::com::htsc::mdc::insight::model::InterBankOfferingDeal* temp = interbankofferingdeal_;
  interbankofferingdeal_ = NULL;
  return temp;
}
void MDCfetsCurrencyDeal::set_allocated_interbankofferingdeal(::com::htsc::mdc::insight::model::InterBankOfferingDeal* interbankofferingdeal) {
  delete interbankofferingdeal_;
  interbankofferingdeal_ = interbankofferingdeal;
  if (interbankofferingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
}

// optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
bool MDCfetsCurrencyDeal::has_collateralrepodeal() const {
  return this != internal_default_instance() && collateralrepodeal_ != NULL;
}
void MDCfetsCurrencyDeal::clear_collateralrepodeal() {
  if (GetArenaNoVirtual() == NULL && collateralrepodeal_ != NULL) delete collateralrepodeal_;
  collateralrepodeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::CollateralRepoDeal& MDCfetsCurrencyDeal::collateralrepodeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  return collateralrepodeal_ != NULL ? *collateralrepodeal_
                         : *::com::htsc::mdc::insight::model::CollateralRepoDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::CollateralRepoDeal* MDCfetsCurrencyDeal::mutable_collateralrepodeal() {
  
  if (collateralrepodeal_ == NULL) {
    collateralrepodeal_ = new ::com::htsc::mdc::insight::model::CollateralRepoDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  return collateralrepodeal_;
}
::com::htsc::mdc::insight::model::CollateralRepoDeal* MDCfetsCurrencyDeal::release_collateralrepodeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  
  ::com::htsc::mdc::insight::model::CollateralRepoDeal* temp = collateralrepodeal_;
  collateralrepodeal_ = NULL;
  return temp;
}
void MDCfetsCurrencyDeal::set_allocated_collateralrepodeal(::com::htsc::mdc::insight::model::CollateralRepoDeal* collateralrepodeal) {
  delete collateralrepodeal_;
  collateralrepodeal_ = collateralrepodeal;
  if (collateralrepodeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
}

// optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
bool MDCfetsCurrencyDeal::has_outrightrepodeal() const {
  return this != internal_default_instance() && outrightrepodeal_ != NULL;
}
void MDCfetsCurrencyDeal::clear_outrightrepodeal() {
  if (GetArenaNoVirtual() == NULL && outrightrepodeal_ != NULL) delete outrightrepodeal_;
  outrightrepodeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::OutrightRepoDeal& MDCfetsCurrencyDeal::outrightrepodeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  return outrightrepodeal_ != NULL ? *outrightrepodeal_
                         : *::com::htsc::mdc::insight::model::OutrightRepoDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::OutrightRepoDeal* MDCfetsCurrencyDeal::mutable_outrightrepodeal() {
  
  if (outrightrepodeal_ == NULL) {
    outrightrepodeal_ = new ::com::htsc::mdc::insight::model::OutrightRepoDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  return outrightrepodeal_;
}
::com::htsc::mdc::insight::model::OutrightRepoDeal* MDCfetsCurrencyDeal::release_outrightrepodeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  
  ::com::htsc::mdc::insight::model::OutrightRepoDeal* temp = outrightrepodeal_;
  outrightrepodeal_ = NULL;
  return temp;
}
void MDCfetsCurrencyDeal::set_allocated_outrightrepodeal(::com::htsc::mdc::insight::model::OutrightRepoDeal* outrightrepodeal) {
  delete outrightrepodeal_;
  outrightrepodeal_ = outrightrepodeal;
  if (outrightrepodeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
}

inline const MDCfetsCurrencyDeal* MDCfetsCurrencyDeal::internal_default_instance() {
  return &MDCfetsCurrencyDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InterBankOfferingDeal::kTradeDateFieldNumber;
const int InterBankOfferingDeal::kTradeTimeFieldNumber;
const int InterBankOfferingDeal::kTradeYieldFieldNumber;
const int InterBankOfferingDeal::kTradeMoneyFieldNumber;
const int InterBankOfferingDeal::kTermFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InterBankOfferingDeal::InterBankOfferingDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.InterBankOfferingDeal)
}

void InterBankOfferingDeal::InitAsDefaultInstance() {
}

InterBankOfferingDeal::InterBankOfferingDeal(const InterBankOfferingDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.InterBankOfferingDeal)
}

void InterBankOfferingDeal::SharedCtor() {
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tradeyield_, 0, reinterpret_cast<char*>(&term_) -
    reinterpret_cast<char*>(&tradeyield_) + sizeof(term_));
  _cached_size_ = 0;
}

InterBankOfferingDeal::~InterBankOfferingDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  SharedDtor();
}

void InterBankOfferingDeal::SharedDtor() {
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void InterBankOfferingDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* InterBankOfferingDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return InterBankOfferingDeal_descriptor_;
}

const InterBankOfferingDeal& InterBankOfferingDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<InterBankOfferingDeal> InterBankOfferingDeal_default_instance_;

InterBankOfferingDeal* InterBankOfferingDeal::New(::google::protobuf::Arena* arena) const {
  InterBankOfferingDeal* n = new InterBankOfferingDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void InterBankOfferingDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(InterBankOfferingDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<InterBankOfferingDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(tradeyield_, term_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool InterBankOfferingDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TradeDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_TradeYield;
        break;
      }

      // optional double TradeYield = 3;
      case 3: {
        if (tag == 25) {
         parse_TradeYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradeyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_TradeMoney;
        break;
      }

      // optional double TradeMoney = 4;
      case 4: {
        if (tag == 33) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_Term;
        break;
      }

      // optional double Term = 5;
      case 5: {
        if (tag == 41) {
         parse_Term:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &term_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  return false;
#undef DO_
}

void InterBankOfferingDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tradedate(), output);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradetime(), output);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->tradeyield(), output);
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->trademoney(), output);
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->term(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.InterBankOfferingDeal)
}

::google::protobuf::uint8* InterBankOfferingDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tradedate(), target);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradetime(), target);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->tradeyield(), target);
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->trademoney(), target);
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->term(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  return target;
}

size_t InterBankOfferingDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  size_t total_size = 0;

  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    total_size += 1 + 8;
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    total_size += 1 + 8;
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void InterBankOfferingDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const InterBankOfferingDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InterBankOfferingDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.InterBankOfferingDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.InterBankOfferingDeal)
    UnsafeMergeFrom(*source);
  }
}

void InterBankOfferingDeal::MergeFrom(const InterBankOfferingDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void InterBankOfferingDeal::UnsafeMergeFrom(const InterBankOfferingDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.tradeyield() != 0) {
    set_tradeyield(from.tradeyield());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.term() != 0) {
    set_term(from.term());
  }
}

void InterBankOfferingDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InterBankOfferingDeal::CopyFrom(const InterBankOfferingDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.InterBankOfferingDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool InterBankOfferingDeal::IsInitialized() const {

  return true;
}

void InterBankOfferingDeal::Swap(InterBankOfferingDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void InterBankOfferingDeal::InternalSwap(InterBankOfferingDeal* other) {
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(tradeyield_, other->tradeyield_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(term_, other->term_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata InterBankOfferingDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = InterBankOfferingDeal_descriptor_;
  metadata.reflection = InterBankOfferingDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// InterBankOfferingDeal

// optional string TradeDate = 1;
void InterBankOfferingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& InterBankOfferingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void InterBankOfferingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
void InterBankOfferingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
void InterBankOfferingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
::std::string* InterBankOfferingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* InterBankOfferingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void InterBankOfferingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}

// optional string TradeTime = 2;
void InterBankOfferingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& InterBankOfferingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void InterBankOfferingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
void InterBankOfferingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
void InterBankOfferingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
::std::string* InterBankOfferingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* InterBankOfferingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void InterBankOfferingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}

// optional double TradeYield = 3;
void InterBankOfferingDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
double InterBankOfferingDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeYield)
  return tradeyield_;
}
void InterBankOfferingDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeYield)
}

// optional double TradeMoney = 4;
void InterBankOfferingDeal::clear_trademoney() {
  trademoney_ = 0;
}
double InterBankOfferingDeal::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeMoney)
  return trademoney_;
}
void InterBankOfferingDeal::set_trademoney(double value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeMoney)
}

// optional double Term = 5;
void InterBankOfferingDeal::clear_term() {
  term_ = 0;
}
double InterBankOfferingDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.Term)
  return term_;
}
void InterBankOfferingDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.Term)
}

inline const InterBankOfferingDeal* InterBankOfferingDeal::internal_default_instance() {
  return &InterBankOfferingDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollateralRepoDeal::kTradeDateFieldNumber;
const int CollateralRepoDeal::kTradeTimeFieldNumber;
const int CollateralRepoDeal::kTradeYieldFieldNumber;
const int CollateralRepoDeal::kTradeMoneyFieldNumber;
const int CollateralRepoDeal::kTermFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollateralRepoDeal::CollateralRepoDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.CollateralRepoDeal)
}

void CollateralRepoDeal::InitAsDefaultInstance() {
}

CollateralRepoDeal::CollateralRepoDeal(const CollateralRepoDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.CollateralRepoDeal)
}

void CollateralRepoDeal::SharedCtor() {
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tradeyield_, 0, reinterpret_cast<char*>(&term_) -
    reinterpret_cast<char*>(&tradeyield_) + sizeof(term_));
  _cached_size_ = 0;
}

CollateralRepoDeal::~CollateralRepoDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.CollateralRepoDeal)
  SharedDtor();
}

void CollateralRepoDeal::SharedDtor() {
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CollateralRepoDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CollateralRepoDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CollateralRepoDeal_descriptor_;
}

const CollateralRepoDeal& CollateralRepoDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CollateralRepoDeal> CollateralRepoDeal_default_instance_;

CollateralRepoDeal* CollateralRepoDeal::New(::google::protobuf::Arena* arena) const {
  CollateralRepoDeal* n = new CollateralRepoDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CollateralRepoDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(CollateralRepoDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<CollateralRepoDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(tradeyield_, term_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool CollateralRepoDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TradeDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_TradeYield;
        break;
      }

      // optional double TradeYield = 3;
      case 3: {
        if (tag == 25) {
         parse_TradeYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradeyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_TradeMoney;
        break;
      }

      // optional double TradeMoney = 4;
      case 4: {
        if (tag == 33) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_Term;
        break;
      }

      // optional double Term = 5;
      case 5: {
        if (tag == 41) {
         parse_Term:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &term_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.CollateralRepoDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.CollateralRepoDeal)
  return false;
#undef DO_
}

void CollateralRepoDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tradedate(), output);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradetime(), output);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->tradeyield(), output);
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->trademoney(), output);
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->term(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.CollateralRepoDeal)
}

::google::protobuf::uint8* CollateralRepoDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tradedate(), target);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradetime(), target);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->tradeyield(), target);
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->trademoney(), target);
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->term(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.CollateralRepoDeal)
  return target;
}

size_t CollateralRepoDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  size_t total_size = 0;

  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    total_size += 1 + 8;
  }

  // optional double TradeMoney = 4;
  if (this->trademoney() != 0) {
    total_size += 1 + 8;
  }

  // optional double Term = 5;
  if (this->term() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CollateralRepoDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CollateralRepoDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollateralRepoDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.CollateralRepoDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.CollateralRepoDeal)
    UnsafeMergeFrom(*source);
  }
}

void CollateralRepoDeal::MergeFrom(const CollateralRepoDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CollateralRepoDeal::UnsafeMergeFrom(const CollateralRepoDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.tradeyield() != 0) {
    set_tradeyield(from.tradeyield());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.term() != 0) {
    set_term(from.term());
  }
}

void CollateralRepoDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollateralRepoDeal::CopyFrom(const CollateralRepoDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.CollateralRepoDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CollateralRepoDeal::IsInitialized() const {

  return true;
}

void CollateralRepoDeal::Swap(CollateralRepoDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CollateralRepoDeal::InternalSwap(CollateralRepoDeal* other) {
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(tradeyield_, other->tradeyield_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(term_, other->term_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CollateralRepoDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CollateralRepoDeal_descriptor_;
  metadata.reflection = CollateralRepoDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CollateralRepoDeal

// optional string TradeDate = 1;
void CollateralRepoDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CollateralRepoDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
void CollateralRepoDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
void CollateralRepoDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
::std::string* CollateralRepoDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CollateralRepoDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}

// optional string TradeTime = 2;
void CollateralRepoDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CollateralRepoDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
void CollateralRepoDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
void CollateralRepoDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
::std::string* CollateralRepoDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CollateralRepoDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}

// optional double TradeYield = 3;
void CollateralRepoDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
double CollateralRepoDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeYield)
  return tradeyield_;
}
void CollateralRepoDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeYield)
}

// optional double TradeMoney = 4;
void CollateralRepoDeal::clear_trademoney() {
  trademoney_ = 0;
}
double CollateralRepoDeal::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeMoney)
  return trademoney_;
}
void CollateralRepoDeal::set_trademoney(double value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeMoney)
}

// optional double Term = 5;
void CollateralRepoDeal::clear_term() {
  term_ = 0;
}
double CollateralRepoDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.Term)
  return term_;
}
void CollateralRepoDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.Term)
}

inline const CollateralRepoDeal* CollateralRepoDeal::internal_default_instance() {
  return &CollateralRepoDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OutrightRepoDeal::kTradeDateFieldNumber;
const int OutrightRepoDeal::kTradeTimeFieldNumber;
const int OutrightRepoDeal::kTradeYieldFieldNumber;
const int OutrightRepoDeal::kTermFieldNumber;
const int OutrightRepoDeal::kTotalTradeMoneyFieldNumber;
const int OutrightRepoDeal::kTotalSettlCurrAmtFieldNumber;
const int OutrightRepoDeal::kTotalSettlCurrAmt2FieldNumber;
const int OutrightRepoDeal::kSecurityDetailListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OutrightRepoDeal::OutrightRepoDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.OutrightRepoDeal)
}

void OutrightRepoDeal::InitAsDefaultInstance() {
}

OutrightRepoDeal::OutrightRepoDeal(const OutrightRepoDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.OutrightRepoDeal)
}

void OutrightRepoDeal::SharedCtor() {
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&tradeyield_, 0, reinterpret_cast<char*>(&totalsettlcurramt2_) -
    reinterpret_cast<char*>(&tradeyield_) + sizeof(totalsettlcurramt2_));
  _cached_size_ = 0;
}

OutrightRepoDeal::~OutrightRepoDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.OutrightRepoDeal)
  SharedDtor();
}

void OutrightRepoDeal::SharedDtor() {
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OutrightRepoDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OutrightRepoDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OutrightRepoDeal_descriptor_;
}

const OutrightRepoDeal& OutrightRepoDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OutrightRepoDeal> OutrightRepoDeal_default_instance_;

OutrightRepoDeal* OutrightRepoDeal::New(::google::protobuf::Arena* arena) const {
  OutrightRepoDeal* n = new OutrightRepoDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OutrightRepoDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(OutrightRepoDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<OutrightRepoDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(tradeyield_, totalsettlcurramt2_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

  securitydetaillist_.Clear();
}

bool OutrightRepoDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TradeDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_TradeYield;
        break;
      }

      // optional double TradeYield = 3;
      case 3: {
        if (tag == 25) {
         parse_TradeYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradeyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_Term;
        break;
      }

      // optional double Term = 4;
      case 4: {
        if (tag == 33) {
         parse_Term:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &term_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_TotalTradeMoney;
        break;
      }

      // optional double TotalTradeMoney = 5;
      case 5: {
        if (tag == 41) {
         parse_TotalTradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totaltrademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_TotalSettlCurrAmt;
        break;
      }

      // optional double TotalSettlCurrAmt = 6;
      case 6: {
        if (tag == 49) {
         parse_TotalSettlCurrAmt:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalsettlcurramt_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_TotalSettlCurrAmt2;
        break;
      }

      // optional double TotalSettlCurrAmt2 = 7;
      case 7: {
        if (tag == 57) {
         parse_TotalSettlCurrAmt2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalsettlcurramt2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_SecurityDetailList;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
      case 10: {
        if (tag == 82) {
         parse_SecurityDetailList:
          DO_(input->IncrementRecursionDepth());
         parse_loop_SecurityDetailList:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_securitydetaillist()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_loop_SecurityDetailList;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.OutrightRepoDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.OutrightRepoDeal)
  return false;
#undef DO_
}

void OutrightRepoDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tradedate(), output);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradetime(), output);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->tradeyield(), output);
  }

  // optional double Term = 4;
  if (this->term() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->term(), output);
  }

  // optional double TotalTradeMoney = 5;
  if (this->totaltrademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->totaltrademoney(), output);
  }

  // optional double TotalSettlCurrAmt = 6;
  if (this->totalsettlcurramt() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->totalsettlcurramt(), output);
  }

  // optional double TotalSettlCurrAmt2 = 7;
  if (this->totalsettlcurramt2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->totalsettlcurramt2(), output);
  }

  // repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
  for (unsigned int i = 0, n = this->securitydetaillist_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, this->securitydetaillist(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.OutrightRepoDeal)
}

::google::protobuf::uint8* OutrightRepoDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tradedate(), target);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradetime(), target);
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->tradeyield(), target);
  }

  // optional double Term = 4;
  if (this->term() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->term(), target);
  }

  // optional double TotalTradeMoney = 5;
  if (this->totaltrademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->totaltrademoney(), target);
  }

  // optional double TotalSettlCurrAmt = 6;
  if (this->totalsettlcurramt() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->totalsettlcurramt(), target);
  }

  // optional double TotalSettlCurrAmt2 = 7;
  if (this->totalsettlcurramt2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->totalsettlcurramt2(), target);
  }

  // repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
  for (unsigned int i = 0, n = this->securitydetaillist_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, this->securitydetaillist(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.OutrightRepoDeal)
  return target;
}

size_t OutrightRepoDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  size_t total_size = 0;

  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional double TradeYield = 3;
  if (this->tradeyield() != 0) {
    total_size += 1 + 8;
  }

  // optional double Term = 4;
  if (this->term() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalTradeMoney = 5;
  if (this->totaltrademoney() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalSettlCurrAmt = 6;
  if (this->totalsettlcurramt() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalSettlCurrAmt2 = 7;
  if (this->totalsettlcurramt2() != 0) {
    total_size += 1 + 8;
  }

  // repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
  {
    unsigned int count = this->securitydetaillist_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->securitydetaillist(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OutrightRepoDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OutrightRepoDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OutrightRepoDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.OutrightRepoDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.OutrightRepoDeal)
    UnsafeMergeFrom(*source);
  }
}

void OutrightRepoDeal::MergeFrom(const OutrightRepoDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OutrightRepoDeal::UnsafeMergeFrom(const OutrightRepoDeal& from) {
  GOOGLE_DCHECK(&from != this);
  securitydetaillist_.MergeFrom(from.securitydetaillist_);
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.tradeyield() != 0) {
    set_tradeyield(from.tradeyield());
  }
  if (from.term() != 0) {
    set_term(from.term());
  }
  if (from.totaltrademoney() != 0) {
    set_totaltrademoney(from.totaltrademoney());
  }
  if (from.totalsettlcurramt() != 0) {
    set_totalsettlcurramt(from.totalsettlcurramt());
  }
  if (from.totalsettlcurramt2() != 0) {
    set_totalsettlcurramt2(from.totalsettlcurramt2());
  }
}

void OutrightRepoDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OutrightRepoDeal::CopyFrom(const OutrightRepoDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.OutrightRepoDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OutrightRepoDeal::IsInitialized() const {

  return true;
}

void OutrightRepoDeal::Swap(OutrightRepoDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OutrightRepoDeal::InternalSwap(OutrightRepoDeal* other) {
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(tradeyield_, other->tradeyield_);
  std::swap(term_, other->term_);
  std::swap(totaltrademoney_, other->totaltrademoney_);
  std::swap(totalsettlcurramt_, other->totalsettlcurramt_);
  std::swap(totalsettlcurramt2_, other->totalsettlcurramt2_);
  securitydetaillist_.UnsafeArenaSwap(&other->securitydetaillist_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OutrightRepoDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OutrightRepoDeal_descriptor_;
  metadata.reflection = OutrightRepoDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OutrightRepoDeal

// optional string TradeDate = 1;
void OutrightRepoDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OutrightRepoDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OutrightRepoDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
void OutrightRepoDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
void OutrightRepoDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
::std::string* OutrightRepoDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OutrightRepoDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OutrightRepoDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}

// optional string TradeTime = 2;
void OutrightRepoDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OutrightRepoDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OutrightRepoDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
void OutrightRepoDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
void OutrightRepoDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
::std::string* OutrightRepoDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OutrightRepoDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OutrightRepoDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}

// optional double TradeYield = 3;
void OutrightRepoDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
double OutrightRepoDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeYield)
  return tradeyield_;
}
void OutrightRepoDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeYield)
}

// optional double Term = 4;
void OutrightRepoDeal::clear_term() {
  term_ = 0;
}
double OutrightRepoDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.Term)
  return term_;
}
void OutrightRepoDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.Term)
}

// optional double TotalTradeMoney = 5;
void OutrightRepoDeal::clear_totaltrademoney() {
  totaltrademoney_ = 0;
}
double OutrightRepoDeal::totaltrademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalTradeMoney)
  return totaltrademoney_;
}
void OutrightRepoDeal::set_totaltrademoney(double value) {
  
  totaltrademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalTradeMoney)
}

// optional double TotalSettlCurrAmt = 6;
void OutrightRepoDeal::clear_totalsettlcurramt() {
  totalsettlcurramt_ = 0;
}
double OutrightRepoDeal::totalsettlcurramt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt)
  return totalsettlcurramt_;
}
void OutrightRepoDeal::set_totalsettlcurramt(double value) {
  
  totalsettlcurramt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt)
}

// optional double TotalSettlCurrAmt2 = 7;
void OutrightRepoDeal::clear_totalsettlcurramt2() {
  totalsettlcurramt2_ = 0;
}
double OutrightRepoDeal::totalsettlcurramt2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt2)
  return totalsettlcurramt2_;
}
void OutrightRepoDeal::set_totalsettlcurramt2(double value) {
  
  totalsettlcurramt2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt2)
}

// repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
int OutrightRepoDeal::securitydetaillist_size() const {
  return securitydetaillist_.size();
}
void OutrightRepoDeal::clear_securitydetaillist() {
  securitydetaillist_.Clear();
}
const ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail& OutrightRepoDeal::securitydetaillist(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Get(index);
}
::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* OutrightRepoDeal::mutable_securitydetaillist(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Mutable(index);
}
::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* OutrightRepoDeal::add_securitydetaillist() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >*
OutrightRepoDeal::mutable_securitydetaillist() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return &securitydetaillist_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >&
OutrightRepoDeal::securitydetaillist() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_;
}

inline const OutrightRepoDeal* OutrightRepoDeal::internal_default_instance() {
  return &OutrightRepoDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UnderlyingSecurityDetail::kUnderlyingSecurityIDFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingSymbolFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingSettlCurrAmtFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingSettlCurrAmt2FieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingQtyFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingPxFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingPx2FieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingYieldFieldNumber;
const int UnderlyingSecurityDetail::kUnderlyingYield2FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UnderlyingSecurityDetail::UnderlyingSecurityDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
}

void UnderlyingSecurityDetail::InitAsDefaultInstance() {
}

UnderlyingSecurityDetail::UnderlyingSecurityDetail(const UnderlyingSecurityDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
}

void UnderlyingSecurityDetail::SharedCtor() {
  underlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&underlyingsettlcurramt_, 0, reinterpret_cast<char*>(&underlyingyield2_) -
    reinterpret_cast<char*>(&underlyingsettlcurramt_) + sizeof(underlyingyield2_));
  _cached_size_ = 0;
}

UnderlyingSecurityDetail::~UnderlyingSecurityDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  SharedDtor();
}

void UnderlyingSecurityDetail::SharedDtor() {
  underlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void UnderlyingSecurityDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UnderlyingSecurityDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return UnderlyingSecurityDetail_descriptor_;
}

const UnderlyingSecurityDetail& UnderlyingSecurityDetail::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<UnderlyingSecurityDetail> UnderlyingSecurityDetail_default_instance_;

UnderlyingSecurityDetail* UnderlyingSecurityDetail::New(::google::protobuf::Arena* arena) const {
  UnderlyingSecurityDetail* n = new UnderlyingSecurityDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void UnderlyingSecurityDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(UnderlyingSecurityDetail, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<UnderlyingSecurityDetail*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(underlyingsettlcurramt_, underlyingyield_);
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingyield2_ = 0;

#undef ZR_HELPER_
#undef ZR_

}

bool UnderlyingSecurityDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string UnderlyingSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_UnderlyingSymbol;
        break;
      }

      // optional string UnderlyingSymbol = 2;
      case 2: {
        if (tag == 18) {
         parse_UnderlyingSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsymbol().data(), this->underlyingsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_UnderlyingSettlCurrAmt;
        break;
      }

      // optional double UnderlyingSettlCurrAmt = 3;
      case 3: {
        if (tag == 25) {
         parse_UnderlyingSettlCurrAmt:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingsettlcurramt_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_UnderlyingSettlCurrAmt2;
        break;
      }

      // optional double UnderlyingSettlCurrAmt2 = 4;
      case 4: {
        if (tag == 33) {
         parse_UnderlyingSettlCurrAmt2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingsettlcurramt2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_UnderlyingQty;
        break;
      }

      // optional double UnderlyingQty = 5;
      case 5: {
        if (tag == 41) {
         parse_UnderlyingQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_UnderlyingPx;
        break;
      }

      // optional double UnderlyingPx = 6;
      case 6: {
        if (tag == 49) {
         parse_UnderlyingPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_UnderlyingPx2;
        break;
      }

      // optional double UnderlyingPx2 = 7;
      case 7: {
        if (tag == 57) {
         parse_UnderlyingPx2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingpx2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_UnderlyingYield;
        break;
      }

      // optional double UnderlyingYield = 8;
      case 8: {
        if (tag == 65) {
         parse_UnderlyingYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(73)) goto parse_UnderlyingYield2;
        break;
      }

      // optional double UnderlyingYield2 = 9;
      case 9: {
        if (tag == 73) {
         parse_UnderlyingYield2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingyield2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  return false;
#undef DO_
}

void UnderlyingSecurityDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  // optional string UnderlyingSecurityID = 1;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->underlyingsecurityid(), output);
  }

  // optional string UnderlyingSymbol = 2;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->underlyingsymbol(), output);
  }

  // optional double UnderlyingSettlCurrAmt = 3;
  if (this->underlyingsettlcurramt() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->underlyingsettlcurramt(), output);
  }

  // optional double UnderlyingSettlCurrAmt2 = 4;
  if (this->underlyingsettlcurramt2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->underlyingsettlcurramt2(), output);
  }

  // optional double UnderlyingQty = 5;
  if (this->underlyingqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->underlyingqty(), output);
  }

  // optional double UnderlyingPx = 6;
  if (this->underlyingpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->underlyingpx(), output);
  }

  // optional double UnderlyingPx2 = 7;
  if (this->underlyingpx2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->underlyingpx2(), output);
  }

  // optional double UnderlyingYield = 8;
  if (this->underlyingyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->underlyingyield(), output);
  }

  // optional double UnderlyingYield2 = 9;
  if (this->underlyingyield2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->underlyingyield2(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
}

::google::protobuf::uint8* UnderlyingSecurityDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  // optional string UnderlyingSecurityID = 1;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->underlyingsecurityid(), target);
  }

  // optional string UnderlyingSymbol = 2;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->underlyingsymbol(), target);
  }

  // optional double UnderlyingSettlCurrAmt = 3;
  if (this->underlyingsettlcurramt() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->underlyingsettlcurramt(), target);
  }

  // optional double UnderlyingSettlCurrAmt2 = 4;
  if (this->underlyingsettlcurramt2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->underlyingsettlcurramt2(), target);
  }

  // optional double UnderlyingQty = 5;
  if (this->underlyingqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->underlyingqty(), target);
  }

  // optional double UnderlyingPx = 6;
  if (this->underlyingpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->underlyingpx(), target);
  }

  // optional double UnderlyingPx2 = 7;
  if (this->underlyingpx2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->underlyingpx2(), target);
  }

  // optional double UnderlyingYield = 8;
  if (this->underlyingyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->underlyingyield(), target);
  }

  // optional double UnderlyingYield2 = 9;
  if (this->underlyingyield2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->underlyingyield2(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  return target;
}

size_t UnderlyingSecurityDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  size_t total_size = 0;

  // optional string UnderlyingSecurityID = 1;
  if (this->underlyingsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsecurityid());
  }

  // optional string UnderlyingSymbol = 2;
  if (this->underlyingsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsymbol());
  }

  // optional double UnderlyingSettlCurrAmt = 3;
  if (this->underlyingsettlcurramt() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingSettlCurrAmt2 = 4;
  if (this->underlyingsettlcurramt2() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingQty = 5;
  if (this->underlyingqty() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingPx = 6;
  if (this->underlyingpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingPx2 = 7;
  if (this->underlyingpx2() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingYield = 8;
  if (this->underlyingyield() != 0) {
    total_size += 1 + 8;
  }

  // optional double UnderlyingYield2 = 9;
  if (this->underlyingyield2() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UnderlyingSecurityDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const UnderlyingSecurityDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UnderlyingSecurityDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
    UnsafeMergeFrom(*source);
  }
}

void UnderlyingSecurityDetail::MergeFrom(const UnderlyingSecurityDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void UnderlyingSecurityDetail::UnsafeMergeFrom(const UnderlyingSecurityDetail& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.underlyingsecurityid().size() > 0) {

    underlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsecurityid_);
  }
  if (from.underlyingsymbol().size() > 0) {

    underlyingsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsymbol_);
  }
  if (from.underlyingsettlcurramt() != 0) {
    set_underlyingsettlcurramt(from.underlyingsettlcurramt());
  }
  if (from.underlyingsettlcurramt2() != 0) {
    set_underlyingsettlcurramt2(from.underlyingsettlcurramt2());
  }
  if (from.underlyingqty() != 0) {
    set_underlyingqty(from.underlyingqty());
  }
  if (from.underlyingpx() != 0) {
    set_underlyingpx(from.underlyingpx());
  }
  if (from.underlyingpx2() != 0) {
    set_underlyingpx2(from.underlyingpx2());
  }
  if (from.underlyingyield() != 0) {
    set_underlyingyield(from.underlyingyield());
  }
  if (from.underlyingyield2() != 0) {
    set_underlyingyield2(from.underlyingyield2());
  }
}

void UnderlyingSecurityDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UnderlyingSecurityDetail::CopyFrom(const UnderlyingSecurityDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool UnderlyingSecurityDetail::IsInitialized() const {

  return true;
}

void UnderlyingSecurityDetail::Swap(UnderlyingSecurityDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void UnderlyingSecurityDetail::InternalSwap(UnderlyingSecurityDetail* other) {
  underlyingsecurityid_.Swap(&other->underlyingsecurityid_);
  underlyingsymbol_.Swap(&other->underlyingsymbol_);
  std::swap(underlyingsettlcurramt_, other->underlyingsettlcurramt_);
  std::swap(underlyingsettlcurramt2_, other->underlyingsettlcurramt2_);
  std::swap(underlyingqty_, other->underlyingqty_);
  std::swap(underlyingpx_, other->underlyingpx_);
  std::swap(underlyingpx2_, other->underlyingpx2_);
  std::swap(underlyingyield_, other->underlyingyield_);
  std::swap(underlyingyield2_, other->underlyingyield2_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UnderlyingSecurityDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = UnderlyingSecurityDetail_descriptor_;
  metadata.reflection = UnderlyingSecurityDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UnderlyingSecurityDetail

// optional string UnderlyingSecurityID = 1;
void UnderlyingSecurityDetail::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& UnderlyingSecurityDetail::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void UnderlyingSecurityDetail::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
void UnderlyingSecurityDetail::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
void UnderlyingSecurityDetail::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
::std::string* UnderlyingSecurityDetail::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* UnderlyingSecurityDetail::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void UnderlyingSecurityDetail::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}

// optional string UnderlyingSymbol = 2;
void UnderlyingSecurityDetail::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& UnderlyingSecurityDetail::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void UnderlyingSecurityDetail::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
void UnderlyingSecurityDetail::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
void UnderlyingSecurityDetail::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
::std::string* UnderlyingSecurityDetail::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* UnderlyingSecurityDetail::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void UnderlyingSecurityDetail::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}

// optional double UnderlyingSettlCurrAmt = 3;
void UnderlyingSecurityDetail::clear_underlyingsettlcurramt() {
  underlyingsettlcurramt_ = 0;
}
double UnderlyingSecurityDetail::underlyingsettlcurramt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt)
  return underlyingsettlcurramt_;
}
void UnderlyingSecurityDetail::set_underlyingsettlcurramt(double value) {
  
  underlyingsettlcurramt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt)
}

// optional double UnderlyingSettlCurrAmt2 = 4;
void UnderlyingSecurityDetail::clear_underlyingsettlcurramt2() {
  underlyingsettlcurramt2_ = 0;
}
double UnderlyingSecurityDetail::underlyingsettlcurramt2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt2)
  return underlyingsettlcurramt2_;
}
void UnderlyingSecurityDetail::set_underlyingsettlcurramt2(double value) {
  
  underlyingsettlcurramt2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt2)
}

// optional double UnderlyingQty = 5;
void UnderlyingSecurityDetail::clear_underlyingqty() {
  underlyingqty_ = 0;
}
double UnderlyingSecurityDetail::underlyingqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingQty)
  return underlyingqty_;
}
void UnderlyingSecurityDetail::set_underlyingqty(double value) {
  
  underlyingqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingQty)
}

// optional double UnderlyingPx = 6;
void UnderlyingSecurityDetail::clear_underlyingpx() {
  underlyingpx_ = 0;
}
double UnderlyingSecurityDetail::underlyingpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx)
  return underlyingpx_;
}
void UnderlyingSecurityDetail::set_underlyingpx(double value) {
  
  underlyingpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx)
}

// optional double UnderlyingPx2 = 7;
void UnderlyingSecurityDetail::clear_underlyingpx2() {
  underlyingpx2_ = 0;
}
double UnderlyingSecurityDetail::underlyingpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx2)
  return underlyingpx2_;
}
void UnderlyingSecurityDetail::set_underlyingpx2(double value) {
  
  underlyingpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx2)
}

// optional double UnderlyingYield = 8;
void UnderlyingSecurityDetail::clear_underlyingyield() {
  underlyingyield_ = 0;
}
double UnderlyingSecurityDetail::underlyingyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield)
  return underlyingyield_;
}
void UnderlyingSecurityDetail::set_underlyingyield(double value) {
  
  underlyingyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield)
}

// optional double UnderlyingYield2 = 9;
void UnderlyingSecurityDetail::clear_underlyingyield2() {
  underlyingyield2_ = 0;
}
double UnderlyingSecurityDetail::underlyingyield2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield2)
  return underlyingyield2_;
}
void UnderlyingSecurityDetail::set_underlyingyield2(double value) {
  
  underlyingyield2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield2)
}

inline const UnderlyingSecurityDetail* UnderlyingSecurityDetail::internal_default_instance() {
  return &UnderlyingSecurityDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
