// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADTwap.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "ADTwap.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* ADTwap_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADTwap_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_ADTwap_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_ADTwap_2eproto() {
  protobuf_AddDesc_ADTwap_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "ADTwap.proto");
  GOOGLE_CHECK(file != NULL);
  ADTwap_descriptor_ = file->message_type(0);
  static const int ADTwap_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, periodtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, twap_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, datamultiplepowerof10_),
  };
  ADTwap_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADTwap_descriptor_,
      ADTwap::internal_default_instance(),
      ADTwap_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADTwap),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADTwap, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_ADTwap_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADTwap_descriptor_, ADTwap::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_ADTwap_2eproto() {
  ADTwap_default_instance_.Shutdown();
  delete ADTwap_reflection_;
}

void protobuf_InitDefaults_ADTwap_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_EMDPeriodType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  ADTwap_default_instance_.DefaultConstruct();
  ADTwap_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_ADTwap_2eproto_once_);
void protobuf_InitDefaults_ADTwap_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_ADTwap_2eproto_once_,
                 &protobuf_InitDefaults_ADTwap_2eproto_impl);
}
void protobuf_AddDesc_ADTwap_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_ADTwap_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\014ADTwap.proto\022\032com.htsc.mdc.insight.mod"
    "el\032\023EMDPeriodType.proto\032\023ESecurityType.p"
    "roto\032\027ESecurityIDSource.proto\"\341\002\n\006ADTwap"
    "\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005"
    "\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022"
    "\?\n\020securityIDSource\030\005 \001(\0162%.com.htsc.mdc"
    ".model.ESecurityIDSource\0227\n\014securityType"
    "\030\006 \001(\0162!.com.htsc.mdc.model.ESecurityTyp"
    "e\0225\n\nPeriodType\030\007 \001(\0162!.com.htsc.mdc.mod"
    "el.EMDPeriodType\022\014\n\004Twap\030\010 \001(\003\022\024\n\014Exchan"
    "geDate\030\t \001(\005\022\024\n\014ExchangeTime\030\n \001(\005\022\035\n\025Da"
    "taMultiplePowerOf10\030\013 \001(\005B/\n\032com.htsc.md"
    "c.insight.modelB\014ADTwapProtosH\001\240\001\001b\006prot"
    "o3", 522);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "ADTwap.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_EMDPeriodType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_ADTwap_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_ADTwap_2eproto_once_);
void protobuf_AddDesc_ADTwap_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_ADTwap_2eproto_once_,
                 &protobuf_AddDesc_ADTwap_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_ADTwap_2eproto {
  StaticDescriptorInitializer_ADTwap_2eproto() {
    protobuf_AddDesc_ADTwap_2eproto();
  }
} static_descriptor_initializer_ADTwap_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADTwap::kHTSCSecurityIDFieldNumber;
const int ADTwap::kMDDateFieldNumber;
const int ADTwap::kMDTimeFieldNumber;
const int ADTwap::kDataTimestampFieldNumber;
const int ADTwap::kSecurityIDSourceFieldNumber;
const int ADTwap::kSecurityTypeFieldNumber;
const int ADTwap::kPeriodTypeFieldNumber;
const int ADTwap::kTwapFieldNumber;
const int ADTwap::kExchangeDateFieldNumber;
const int ADTwap::kExchangeTimeFieldNumber;
const int ADTwap::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADTwap::ADTwap()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADTwap_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADTwap)
}

void ADTwap::InitAsDefaultInstance() {
}

ADTwap::ADTwap(const ADTwap& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADTwap)
}

void ADTwap::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

ADTwap::~ADTwap() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADTwap)
  SharedDtor();
}

void ADTwap::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADTwap::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADTwap::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADTwap_descriptor_;
}

const ADTwap& ADTwap::default_instance() {
  protobuf_InitDefaults_ADTwap_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADTwap> ADTwap_default_instance_;

ADTwap* ADTwap::New(::google::protobuf::Arena* arena) const {
  ADTwap* n = new ADTwap;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADTwap::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADTwap)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADTwap, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADTwap*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, periodtype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(exchangedate_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADTwap::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADTwap)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_PeriodType;
        break;
      }

      // optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
      case 7: {
        if (tag == 56) {
         parse_PeriodType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_periodtype(static_cast< ::com::htsc::mdc::model::EMDPeriodType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_Twap;
        break;
      }

      // optional int64 Twap = 8;
      case 8: {
        if (tag == 64) {
         parse_Twap:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &twap_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 9;
      case 9: {
        if (tag == 72) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 10;
      case 10: {
        if (tag == 80) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 11;
      case 11: {
        if (tag == 88) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADTwap)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADTwap)
  return false;
#undef DO_
}

void ADTwap::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADTwap)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
  if (this->periodtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->periodtype(), output);
  }

  // optional int64 Twap = 8;
  if (this->twap() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->twap(), output);
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->exchangetime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADTwap)
}

::google::protobuf::uint8* ADTwap::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADTwap)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
  if (this->periodtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->periodtype(), target);
  }

  // optional int64 Twap = 8;
  if (this->twap() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->twap(), target);
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->exchangetime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADTwap)
  return target;
}

size_t ADTwap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADTwap)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
  if (this->periodtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->periodtype());
  }

  // optional int64 Twap = 8;
  if (this->twap() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->twap());
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADTwap::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADTwap)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADTwap* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADTwap>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADTwap)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADTwap)
    UnsafeMergeFrom(*source);
  }
}

void ADTwap::MergeFrom(const ADTwap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADTwap)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADTwap::UnsafeMergeFrom(const ADTwap& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.periodtype() != 0) {
    set_periodtype(from.periodtype());
  }
  if (from.twap() != 0) {
    set_twap(from.twap());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void ADTwap::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADTwap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADTwap::CopyFrom(const ADTwap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADTwap)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADTwap::IsInitialized() const {

  return true;
}

void ADTwap::Swap(ADTwap* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADTwap::InternalSwap(ADTwap* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(periodtype_, other->periodtype_);
  std::swap(twap_, other->twap_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADTwap::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADTwap_descriptor_;
  metadata.reflection = ADTwap_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADTwap

// optional string HTSCSecurityID = 1;
void ADTwap::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADTwap::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADTwap::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
void ADTwap::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
void ADTwap::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}
::std::string* ADTwap::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADTwap::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADTwap::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADTwap.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void ADTwap::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 ADTwap::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.MDDate)
  return mddate_;
}
void ADTwap::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.MDDate)
}

// optional int32 MDTime = 3;
void ADTwap::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 ADTwap::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.MDTime)
  return mdtime_;
}
void ADTwap::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.MDTime)
}

// optional int64 DataTimestamp = 4;
void ADTwap::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADTwap::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.DataTimestamp)
  return datatimestamp_;
}
void ADTwap::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void ADTwap::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource ADTwap::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void ADTwap::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void ADTwap::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType ADTwap::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void ADTwap::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.securityType)
}

// optional .com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
void ADTwap::clear_periodtype() {
  periodtype_ = 0;
}
::com::htsc::mdc::model::EMDPeriodType ADTwap::periodtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.PeriodType)
  return static_cast< ::com::htsc::mdc::model::EMDPeriodType >(periodtype_);
}
void ADTwap::set_periodtype(::com::htsc::mdc::model::EMDPeriodType value) {
  
  periodtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.PeriodType)
}

// optional int64 Twap = 8;
void ADTwap::clear_twap() {
  twap_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADTwap::twap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.Twap)
  return twap_;
}
void ADTwap::set_twap(::google::protobuf::int64 value) {
  
  twap_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.Twap)
}

// optional int32 ExchangeDate = 9;
void ADTwap::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 ADTwap::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.ExchangeDate)
  return exchangedate_;
}
void ADTwap::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.ExchangeDate)
}

// optional int32 ExchangeTime = 10;
void ADTwap::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 ADTwap::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.ExchangeTime)
  return exchangetime_;
}
void ADTwap::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 11;
void ADTwap::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 ADTwap::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADTwap.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void ADTwap::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADTwap.DataMultiplePowerOf10)
}

inline const ADTwap* ADTwap::internal_default_instance() {
  return &ADTwap_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
