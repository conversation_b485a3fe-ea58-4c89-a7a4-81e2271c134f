// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCnexQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCnexQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCnexQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCnexQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDBondQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDBondQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDCnyRepoQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCnyRepoQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCnexQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCnexQuote_2eproto() {
  protobuf_AddDesc_MDCnexQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCnexQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDCnexQuote_descriptor_ = file->message_type(0);
  static const int MDCnexQuote_offsets_[10] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, quotecategory_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, bondquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, cnyrepoquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, datamultiplepowerof10_),
  };
  MDCnexQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCnexQuote_descriptor_,
      MDCnexQuote::internal_default_instance(),
      MDCnexQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCnexQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexQuote, _internal_metadata_));
  MDBondQuote_descriptor_ = file->message_type(1);
  static const int MDBondQuote_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, cnexdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, issuedatatime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quoteprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotesize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, yield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotestatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, quotepricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, maturitydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, cnexsecuritytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, creditrating_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, text_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, maturitymonthyear_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, underlyingprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, matchid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, workbench_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, tenor_),
  };
  MDBondQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDBondQuote_descriptor_,
      MDBondQuote::internal_default_instance(),
      MDBondQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDBondQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBondQuote, _internal_metadata_));
  MDCnyRepoQuote_descriptor_ = file->message_type(2);
  static const int MDCnyRepoQuote_offsets_[26] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, cnexdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, issuedatatime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quotedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quotestatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, quotepricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, createdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, modifydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, baseterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, floattype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, floatprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, fixedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, volumeaboveorbelow_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, volcansplit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, specialterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, at1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, at2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, atcreditrating_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, atbanklimited_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, zhilian_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, underwriterlevel1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, atadd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, dealtype_),
  };
  MDCnyRepoQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCnyRepoQuote_descriptor_,
      MDCnyRepoQuote::internal_default_instance(),
      MDCnyRepoQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCnyRepoQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnyRepoQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCnexQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCnexQuote_descriptor_, MDCnexQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDBondQuote_descriptor_, MDBondQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCnyRepoQuote_descriptor_, MDCnyRepoQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCnexQuote_2eproto() {
  MDCnexQuote_default_instance_.Shutdown();
  delete MDCnexQuote_reflection_;
  MDBondQuote_default_instance_.Shutdown();
  delete MDBondQuote_reflection_;
  MDCnyRepoQuote_default_instance_.Shutdown();
  delete MDCnyRepoQuote_reflection_;
}

void protobuf_InitDefaults_MDCnexQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCnexQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDBondQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDCnyRepoQuote_default_instance_.DefaultConstruct();
  MDCnexQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDBondQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDCnyRepoQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCnexQuote_2eproto_once_);
void protobuf_InitDefaults_MDCnexQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCnexQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDCnexQuote_2eproto_impl);
}
void protobuf_AddDesc_MDCnexQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCnexQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\021MDCnexQuote.proto\022\032com.htsc.mdc.insigh"
    "t.model\032\027ESecurityIDSource.proto\032\023ESecur"
    "ityType.proto\"\212\003\n\013MDCnexQuote\022\026\n\016HTSCSec"
    "urityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030"
    "\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020security"
    "IDSource\030\005 \001(\0162%.com.htsc.mdc.model.ESec"
    "urityIDSource\0227\n\014securityType\030\006 \001(\0162!.co"
    "m.htsc.mdc.model.ESecurityType\022\025\n\rQuoteC"
    "ategory\030\007 \001(\005\022:\n\tBondQuote\030\010 \001(\0132\'.com.h"
    "tsc.mdc.insight.model.MDBondQuote\022@\n\014Cny"
    "RepoQuote\030\t \001(\0132*.com.htsc.mdc.insight.m"
    "odel.MDCnyRepoQuote\022\035\n\025DataMultiplePower"
    "Of10\030\n \001(\005\"\242\003\n\013MDBondQuote\022\024\n\014CnexDataTy"
    "pe\030\001 \001(\005\022\025\n\rIssueDataTime\030\002 \001(\003\022\017\n\007Quote"
    "ID\030\003 \001(\t\022\021\n\tQuoteType\030\004 \001(\005\022\022\n\nQuotePric"
    "e\030\005 \001(\003\022\021\n\tQuoteSize\030\006 \001(\003\022\r\n\005Yield\030\007 \001("
    "\003\022\021\n\tQuoteDate\030\010 \001(\005\022\021\n\tQuoteTime\030\t \001(\005\022"
    "\023\n\013QuoteStatus\030\n \001(\005\022\026\n\016QuotePriceType\030\013"
    " \001(\005\022\024\n\014MaturityDate\030\014 \001(\005\022\030\n\020CnexSecuri"
    "tyType\030\r \001(\t\022\024\n\014CreditRating\030\016 \001(\t\022\014\n\004Te"
    "xt\030\017 \001(\t\022\031\n\021MaturityMonthYear\030\020 \001(\t\022\027\n\017U"
    "nderlyingPrice\030\021 \001(\005\022\017\n\007MatchId\030\022 \001(\t\022\021\n"
    "\tWorkBench\030\023 \001(\005\022\r\n\005Tenor\030\024 \001(\t\"\224\004\n\016MDCn"
    "yRepoQuote\022\024\n\014CnexDataType\030\001 \001(\005\022\025\n\rIssu"
    "eDataTime\030\002 \001(\003\022\017\n\007QuoteID\030\003 \001(\t\022\021\n\tQuot"
    "eType\030\004 \001(\005\022\021\n\tQuoteDate\030\005 \001(\005\022\021\n\tQuoteT"
    "ime\030\006 \001(\005\022\023\n\013QuoteStatus\030\007 \001(\005\022\026\n\016QuoteP"
    "riceType\030\010 \001(\005\022\022\n\nCreateDate\030\t \001(\003\022\022\n\nMo"
    "difyDate\030\n \001(\003\022\020\n\010BaseTerm\030\013 \001(\005\022\021\n\tFloa"
    "tType\030\014 \001(\005\022\022\n\nFloatPrice\030\r \001(\t\022\021\n\tFixed"
    "Rate\030\016 \001(\t\022\016\n\006Volume\030\017 \001(\t\022\032\n\022VolumeAbov"
    "eOrBelow\030\020 \001(\005\022\023\n\013VolCanSplit\030\021 \001(\005\022\023\n\013S"
    "pecialTerm\030\022 \001(\t\022\013\n\003At1\030\023 \001(\005\022\013\n\003At2\030\024 \001"
    "(\005\022\026\n\016ATCreditRating\030\025 \001(\005\022\025\n\rATBankLimi"
    "ted\030\026 \001(\005\022\017\n\007ZhiLian\030\027 \001(\t\022\031\n\021Underwrite"
    "rLevel1\030\030 \001(\t\022\r\n\005ATAdd\030\031 \001(\t\022\020\n\010Dealtype"
    "\030\032 \001(\tB4\n\032com.htsc.mdc.insight.modelB\021MD"
    "CnexQuoteProtosH\001\240\001\001b\006proto3", 1508);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCnexQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCnexQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCnexQuote_2eproto_once_);
void protobuf_AddDesc_MDCnexQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCnexQuote_2eproto_once_,
                 &protobuf_AddDesc_MDCnexQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCnexQuote_2eproto {
  StaticDescriptorInitializer_MDCnexQuote_2eproto() {
    protobuf_AddDesc_MDCnexQuote_2eproto();
  }
} static_descriptor_initializer_MDCnexQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCnexQuote::kHTSCSecurityIDFieldNumber;
const int MDCnexQuote::kMDDateFieldNumber;
const int MDCnexQuote::kMDTimeFieldNumber;
const int MDCnexQuote::kDataTimestampFieldNumber;
const int MDCnexQuote::kSecurityIDSourceFieldNumber;
const int MDCnexQuote::kSecurityTypeFieldNumber;
const int MDCnexQuote::kQuoteCategoryFieldNumber;
const int MDCnexQuote::kBondQuoteFieldNumber;
const int MDCnexQuote::kCnyRepoQuoteFieldNumber;
const int MDCnexQuote::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCnexQuote::MDCnexQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCnexQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCnexQuote)
}

void MDCnexQuote::InitAsDefaultInstance() {
  bondquote_ = const_cast< ::com::htsc::mdc::insight::model::MDBondQuote*>(
      ::com::htsc::mdc::insight::model::MDBondQuote::internal_default_instance());
  cnyrepoquote_ = const_cast< ::com::htsc::mdc::insight::model::MDCnyRepoQuote*>(
      ::com::htsc::mdc::insight::model::MDCnyRepoQuote::internal_default_instance());
}

MDCnexQuote::MDCnexQuote(const MDCnexQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCnexQuote)
}

void MDCnexQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondquote_ = NULL;
  cnyrepoquote_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCnexQuote::~MDCnexQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCnexQuote)
  SharedDtor();
}

void MDCnexQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCnexQuote_default_instance_.get()) {
    delete bondquote_;
    delete cnyrepoquote_;
  }
}

void MDCnexQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCnexQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCnexQuote_descriptor_;
}

const MDCnexQuote& MDCnexQuote::default_instance() {
  protobuf_InitDefaults_MDCnexQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCnexQuote> MDCnexQuote_default_instance_;

MDCnexQuote* MDCnexQuote::New(::google::protobuf::Arena* arena) const {
  MDCnexQuote* n = new MDCnexQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCnexQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCnexQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCnexQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCnexQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, quotecategory_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && bondquote_ != NULL) delete bondquote_;
  bondquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && cnyrepoquote_ != NULL) delete cnyrepoquote_;
  cnyrepoquote_ = NULL;
  datamultiplepowerof10_ = 0;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCnexQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCnexQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_QuoteCategory;
        break;
      }

      // optional int32 QuoteCategory = 7;
      case 7: {
        if (tag == 56) {
         parse_QuoteCategory:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotecategory_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_BondQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
      case 8: {
        if (tag == 66) {
         parse_BondQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_CnyRepoQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
      case 9: {
        if (tag == 74) {
         parse_CnyRepoQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_cnyrepoquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 10;
      case 10: {
        if (tag == 80) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCnexQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCnexQuote)
  return false;
#undef DO_
}

void MDCnexQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCnexQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 QuoteCategory = 7;
  if (this->quotecategory() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->quotecategory(), output);
  }

  // optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
  if (this->has_bondquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->bondquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
  if (this->has_cnyrepoquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->cnyrepoquote_, output);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCnexQuote)
}

::google::protobuf::uint8* MDCnexQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCnexQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 QuoteCategory = 7;
  if (this->quotecategory() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->quotecategory(), target);
  }

  // optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
  if (this->has_bondquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, *this->bondquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
  if (this->has_cnyrepoquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->cnyrepoquote_, false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCnexQuote)
  return target;
}

size_t MDCnexQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCnexQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 QuoteCategory = 7;
  if (this->quotecategory() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotecategory());
  }

  // optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
  if (this->has_bondquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
  if (this->has_cnyrepoquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->cnyrepoquote_);
  }

  // optional int32 DataMultiplePowerOf10 = 10;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCnexQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCnexQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCnexQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCnexQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCnexQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCnexQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDCnexQuote::MergeFrom(const MDCnexQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCnexQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCnexQuote::UnsafeMergeFrom(const MDCnexQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.quotecategory() != 0) {
    set_quotecategory(from.quotecategory());
  }
  if (from.has_bondquote()) {
    mutable_bondquote()->::com::htsc::mdc::insight::model::MDBondQuote::MergeFrom(from.bondquote());
  }
  if (from.has_cnyrepoquote()) {
    mutable_cnyrepoquote()->::com::htsc::mdc::insight::model::MDCnyRepoQuote::MergeFrom(from.cnyrepoquote());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDCnexQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCnexQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCnexQuote::CopyFrom(const MDCnexQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCnexQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCnexQuote::IsInitialized() const {

  return true;
}

void MDCnexQuote::Swap(MDCnexQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCnexQuote::InternalSwap(MDCnexQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(quotecategory_, other->quotecategory_);
  std::swap(bondquote_, other->bondquote_);
  std::swap(cnyrepoquote_, other->cnyrepoquote_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCnexQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCnexQuote_descriptor_;
  metadata.reflection = MDCnexQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCnexQuote

// optional string HTSCSecurityID = 1;
void MDCnexQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
void MDCnexQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
void MDCnexQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
::std::string* MDCnexQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCnexQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCnexQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.MDDate)
  return mddate_;
}
void MDCnexQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDCnexQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCnexQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.MDTime)
  return mdtime_;
}
void MDCnexQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCnexQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.DataTimestamp)
  return datatimestamp_;
}
void MDCnexQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCnexQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCnexQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCnexQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCnexQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCnexQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCnexQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.securityType)
}

// optional int32 QuoteCategory = 7;
void MDCnexQuote::clear_quotecategory() {
  quotecategory_ = 0;
}
::google::protobuf::int32 MDCnexQuote::quotecategory() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.QuoteCategory)
  return quotecategory_;
}
void MDCnexQuote::set_quotecategory(::google::protobuf::int32 value) {
  
  quotecategory_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.QuoteCategory)
}

// optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
bool MDCnexQuote::has_bondquote() const {
  return this != internal_default_instance() && bondquote_ != NULL;
}
void MDCnexQuote::clear_bondquote() {
  if (GetArenaNoVirtual() == NULL && bondquote_ != NULL) delete bondquote_;
  bondquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDBondQuote& MDCnexQuote::bondquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  return bondquote_ != NULL ? *bondquote_
                         : *::com::htsc::mdc::insight::model::MDBondQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDBondQuote* MDCnexQuote::mutable_bondquote() {
  
  if (bondquote_ == NULL) {
    bondquote_ = new ::com::htsc::mdc::insight::model::MDBondQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  return bondquote_;
}
::com::htsc::mdc::insight::model::MDBondQuote* MDCnexQuote::release_bondquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  
  ::com::htsc::mdc::insight::model::MDBondQuote* temp = bondquote_;
  bondquote_ = NULL;
  return temp;
}
void MDCnexQuote::set_allocated_bondquote(::com::htsc::mdc::insight::model::MDBondQuote* bondquote) {
  delete bondquote_;
  bondquote_ = bondquote;
  if (bondquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
}

// optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
bool MDCnexQuote::has_cnyrepoquote() const {
  return this != internal_default_instance() && cnyrepoquote_ != NULL;
}
void MDCnexQuote::clear_cnyrepoquote() {
  if (GetArenaNoVirtual() == NULL && cnyrepoquote_ != NULL) delete cnyrepoquote_;
  cnyrepoquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCnyRepoQuote& MDCnexQuote::cnyrepoquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  return cnyrepoquote_ != NULL ? *cnyrepoquote_
                         : *::com::htsc::mdc::insight::model::MDCnyRepoQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCnyRepoQuote* MDCnexQuote::mutable_cnyrepoquote() {
  
  if (cnyrepoquote_ == NULL) {
    cnyrepoquote_ = new ::com::htsc::mdc::insight::model::MDCnyRepoQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  return cnyrepoquote_;
}
::com::htsc::mdc::insight::model::MDCnyRepoQuote* MDCnexQuote::release_cnyrepoquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  
  ::com::htsc::mdc::insight::model::MDCnyRepoQuote* temp = cnyrepoquote_;
  cnyrepoquote_ = NULL;
  return temp;
}
void MDCnexQuote::set_allocated_cnyrepoquote(::com::htsc::mdc::insight::model::MDCnyRepoQuote* cnyrepoquote) {
  delete cnyrepoquote_;
  cnyrepoquote_ = cnyrepoquote;
  if (cnyrepoquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
}

// optional int32 DataMultiplePowerOf10 = 10;
void MDCnexQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCnexQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCnexQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.DataMultiplePowerOf10)
}

inline const MDCnexQuote* MDCnexQuote::internal_default_instance() {
  return &MDCnexQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDBondQuote::kCnexDataTypeFieldNumber;
const int MDBondQuote::kIssueDataTimeFieldNumber;
const int MDBondQuote::kQuoteIDFieldNumber;
const int MDBondQuote::kQuoteTypeFieldNumber;
const int MDBondQuote::kQuotePriceFieldNumber;
const int MDBondQuote::kQuoteSizeFieldNumber;
const int MDBondQuote::kYieldFieldNumber;
const int MDBondQuote::kQuoteDateFieldNumber;
const int MDBondQuote::kQuoteTimeFieldNumber;
const int MDBondQuote::kQuoteStatusFieldNumber;
const int MDBondQuote::kQuotePriceTypeFieldNumber;
const int MDBondQuote::kMaturityDateFieldNumber;
const int MDBondQuote::kCnexSecurityTypeFieldNumber;
const int MDBondQuote::kCreditRatingFieldNumber;
const int MDBondQuote::kTextFieldNumber;
const int MDBondQuote::kMaturityMonthYearFieldNumber;
const int MDBondQuote::kUnderlyingPriceFieldNumber;
const int MDBondQuote::kMatchIdFieldNumber;
const int MDBondQuote::kWorkBenchFieldNumber;
const int MDBondQuote::kTenorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDBondQuote::MDBondQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCnexQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDBondQuote)
}

void MDBondQuote::InitAsDefaultInstance() {
}

MDBondQuote::MDBondQuote(const MDBondQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDBondQuote)
}

void MDBondQuote::SharedCtor() {
  quoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cnexsecuritytype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maturitymonthyear_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  matchid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&issuedatatime_, 0, reinterpret_cast<char*>(&workbench_) -
    reinterpret_cast<char*>(&issuedatatime_) + sizeof(workbench_));
  _cached_size_ = 0;
}

MDBondQuote::~MDBondQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDBondQuote)
  SharedDtor();
}

void MDBondQuote::SharedDtor() {
  quoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cnexsecuritytype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maturitymonthyear_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  matchid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDBondQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDBondQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDBondQuote_descriptor_;
}

const MDBondQuote& MDBondQuote::default_instance() {
  protobuf_InitDefaults_MDCnexQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDBondQuote> MDBondQuote_default_instance_;

MDBondQuote* MDBondQuote::New(::google::protobuf::Arena* arena) const {
  MDBondQuote* n = new MDBondQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDBondQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDBondQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDBondQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDBondQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(issuedatatime_, quotedate_);
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(quotetime_, maturitydate_);
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maturitymonthyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(underlyingprice_, workbench_);
  matchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDBondQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDBondQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 CnexDataType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cnexdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_IssueDataTime;
        break;
      }

      // optional int64 IssueDataTime = 2;
      case 2: {
        if (tag == 16) {
         parse_IssueDataTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &issuedatatime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_QuoteID;
        break;
      }

      // optional string QuoteID = 3;
      case 3: {
        if (tag == 26) {
         parse_QuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoteid().data(), this->quoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.QuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_QuoteType;
        break;
      }

      // optional int32 QuoteType = 4;
      case 4: {
        if (tag == 32) {
         parse_QuoteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_QuotePrice;
        break;
      }

      // optional int64 QuotePrice = 5;
      case 5: {
        if (tag == 40) {
         parse_QuotePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &quoteprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_QuoteSize;
        break;
      }

      // optional int64 QuoteSize = 6;
      case 6: {
        if (tag == 48) {
         parse_QuoteSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &quotesize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_Yield;
        break;
      }

      // optional int64 Yield = 7;
      case 7: {
        if (tag == 56) {
         parse_Yield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &yield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_QuoteDate;
        break;
      }

      // optional int32 QuoteDate = 8;
      case 8: {
        if (tag == 64) {
         parse_QuoteDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_QuoteTime;
        break;
      }

      // optional int32 QuoteTime = 9;
      case 9: {
        if (tag == 72) {
         parse_QuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_QuoteStatus;
        break;
      }

      // optional int32 QuoteStatus = 10;
      case 10: {
        if (tag == 80) {
         parse_QuoteStatus:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotestatus_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_QuotePriceType;
        break;
      }

      // optional int32 QuotePriceType = 11;
      case 11: {
        if (tag == 88) {
         parse_QuotePriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotepricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_MaturityDate;
        break;
      }

      // optional int32 MaturityDate = 12;
      case 12: {
        if (tag == 96) {
         parse_MaturityDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &maturitydate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_CnexSecurityType;
        break;
      }

      // optional string CnexSecurityType = 13;
      case 13: {
        if (tag == 106) {
         parse_CnexSecurityType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cnexsecuritytype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_CreditRating;
        break;
      }

      // optional string CreditRating = 14;
      case 14: {
        if (tag == 114) {
         parse_CreditRating:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creditrating()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creditrating().data(), this->creditrating().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.CreditRating"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_Text;
        break;
      }

      // optional string Text = 15;
      case 15: {
        if (tag == 122) {
         parse_Text:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_text()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->text().data(), this->text().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.Text"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_MaturityMonthYear;
        break;
      }

      // optional string MaturityMonthYear = 16;
      case 16: {
        if (tag == 130) {
         parse_MaturityMonthYear:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_maturitymonthyear()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->maturitymonthyear().data(), this->maturitymonthyear().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_UnderlyingPrice;
        break;
      }

      // optional int32 UnderlyingPrice = 17;
      case 17: {
        if (tag == 136) {
         parse_UnderlyingPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &underlyingprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_MatchId;
        break;
      }

      // optional string MatchId = 18;
      case 18: {
        if (tag == 146) {
         parse_MatchId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_matchid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->matchid().data(), this->matchid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.MatchId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_WorkBench;
        break;
      }

      // optional int32 WorkBench = 19;
      case 19: {
        if (tag == 152) {
         parse_WorkBench:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &workbench_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_Tenor;
        break;
      }

      // optional string Tenor = 20;
      case 20: {
        if (tag == 162) {
         parse_Tenor:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenor().data(), this->tenor().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBondQuote.Tenor"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDBondQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDBondQuote)
  return false;
#undef DO_
}

void MDBondQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDBondQuote)
  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->cnexdatatype(), output);
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->issuedatatime(), output);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.QuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->quoteid(), output);
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->quotetype(), output);
  }

  // optional int64 QuotePrice = 5;
  if (this->quoteprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->quoteprice(), output);
  }

  // optional int64 QuoteSize = 6;
  if (this->quotesize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->quotesize(), output);
  }

  // optional int64 Yield = 7;
  if (this->yield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->yield(), output);
  }

  // optional int32 QuoteDate = 8;
  if (this->quotedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->quotedate(), output);
  }

  // optional int32 QuoteTime = 9;
  if (this->quotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->quotetime(), output);
  }

  // optional int32 QuoteStatus = 10;
  if (this->quotestatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->quotestatus(), output);
  }

  // optional int32 QuotePriceType = 11;
  if (this->quotepricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->quotepricetype(), output);
  }

  // optional int32 MaturityDate = 12;
  if (this->maturitydate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->maturitydate(), output);
  }

  // optional string CnexSecurityType = 13;
  if (this->cnexsecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->cnexsecuritytype(), output);
  }

  // optional string CreditRating = 14;
  if (this->creditrating().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creditrating().data(), this->creditrating().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.CreditRating");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->creditrating(), output);
  }

  // optional string Text = 15;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.Text");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->text(), output);
  }

  // optional string MaturityMonthYear = 16;
  if (this->maturitymonthyear().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maturitymonthyear().data(), this->maturitymonthyear().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->maturitymonthyear(), output);
  }

  // optional int32 UnderlyingPrice = 17;
  if (this->underlyingprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->underlyingprice(), output);
  }

  // optional string MatchId = 18;
  if (this->matchid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->matchid().data(), this->matchid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.MatchId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->matchid(), output);
  }

  // optional int32 WorkBench = 19;
  if (this->workbench() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->workbench(), output);
  }

  // optional string Tenor = 20;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.Tenor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->tenor(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDBondQuote)
}

::google::protobuf::uint8* MDBondQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDBondQuote)
  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->cnexdatatype(), target);
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->issuedatatime(), target);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.QuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->quoteid(), target);
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->quotetype(), target);
  }

  // optional int64 QuotePrice = 5;
  if (this->quoteprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->quoteprice(), target);
  }

  // optional int64 QuoteSize = 6;
  if (this->quotesize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->quotesize(), target);
  }

  // optional int64 Yield = 7;
  if (this->yield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->yield(), target);
  }

  // optional int32 QuoteDate = 8;
  if (this->quotedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->quotedate(), target);
  }

  // optional int32 QuoteTime = 9;
  if (this->quotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->quotetime(), target);
  }

  // optional int32 QuoteStatus = 10;
  if (this->quotestatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->quotestatus(), target);
  }

  // optional int32 QuotePriceType = 11;
  if (this->quotepricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->quotepricetype(), target);
  }

  // optional int32 MaturityDate = 12;
  if (this->maturitydate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->maturitydate(), target);
  }

  // optional string CnexSecurityType = 13;
  if (this->cnexsecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->cnexsecuritytype(), target);
  }

  // optional string CreditRating = 14;
  if (this->creditrating().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creditrating().data(), this->creditrating().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.CreditRating");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->creditrating(), target);
  }

  // optional string Text = 15;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.Text");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->text(), target);
  }

  // optional string MaturityMonthYear = 16;
  if (this->maturitymonthyear().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maturitymonthyear().data(), this->maturitymonthyear().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->maturitymonthyear(), target);
  }

  // optional int32 UnderlyingPrice = 17;
  if (this->underlyingprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->underlyingprice(), target);
  }

  // optional string MatchId = 18;
  if (this->matchid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->matchid().data(), this->matchid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.MatchId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->matchid(), target);
  }

  // optional int32 WorkBench = 19;
  if (this->workbench() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->workbench(), target);
  }

  // optional string Tenor = 20;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBondQuote.Tenor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->tenor(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDBondQuote)
  return target;
}

size_t MDBondQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDBondQuote)
  size_t total_size = 0;

  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->cnexdatatype());
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->issuedatatime());
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoteid());
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetype());
  }

  // optional int64 QuotePrice = 5;
  if (this->quoteprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->quoteprice());
  }

  // optional int64 QuoteSize = 6;
  if (this->quotesize() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->quotesize());
  }

  // optional int64 Yield = 7;
  if (this->yield() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->yield());
  }

  // optional int32 QuoteDate = 8;
  if (this->quotedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotedate());
  }

  // optional int32 QuoteTime = 9;
  if (this->quotetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetime());
  }

  // optional int32 QuoteStatus = 10;
  if (this->quotestatus() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotestatus());
  }

  // optional int32 QuotePriceType = 11;
  if (this->quotepricetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotepricetype());
  }

  // optional int32 MaturityDate = 12;
  if (this->maturitydate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->maturitydate());
  }

  // optional string CnexSecurityType = 13;
  if (this->cnexsecuritytype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cnexsecuritytype());
  }

  // optional string CreditRating = 14;
  if (this->creditrating().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creditrating());
  }

  // optional string Text = 15;
  if (this->text().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->text());
  }

  // optional string MaturityMonthYear = 16;
  if (this->maturitymonthyear().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->maturitymonthyear());
  }

  // optional int32 UnderlyingPrice = 17;
  if (this->underlyingprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->underlyingprice());
  }

  // optional string MatchId = 18;
  if (this->matchid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->matchid());
  }

  // optional int32 WorkBench = 19;
  if (this->workbench() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->workbench());
  }

  // optional string Tenor = 20;
  if (this->tenor().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenor());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDBondQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDBondQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDBondQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDBondQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDBondQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDBondQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDBondQuote::MergeFrom(const MDBondQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDBondQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDBondQuote::UnsafeMergeFrom(const MDBondQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.cnexdatatype() != 0) {
    set_cnexdatatype(from.cnexdatatype());
  }
  if (from.issuedatatime() != 0) {
    set_issuedatatime(from.issuedatatime());
  }
  if (from.quoteid().size() > 0) {

    quoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoteid_);
  }
  if (from.quotetype() != 0) {
    set_quotetype(from.quotetype());
  }
  if (from.quoteprice() != 0) {
    set_quoteprice(from.quoteprice());
  }
  if (from.quotesize() != 0) {
    set_quotesize(from.quotesize());
  }
  if (from.yield() != 0) {
    set_yield(from.yield());
  }
  if (from.quotedate() != 0) {
    set_quotedate(from.quotedate());
  }
  if (from.quotetime() != 0) {
    set_quotetime(from.quotetime());
  }
  if (from.quotestatus() != 0) {
    set_quotestatus(from.quotestatus());
  }
  if (from.quotepricetype() != 0) {
    set_quotepricetype(from.quotepricetype());
  }
  if (from.maturitydate() != 0) {
    set_maturitydate(from.maturitydate());
  }
  if (from.cnexsecuritytype().size() > 0) {

    cnexsecuritytype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cnexsecuritytype_);
  }
  if (from.creditrating().size() > 0) {

    creditrating_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creditrating_);
  }
  if (from.text().size() > 0) {

    text_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.text_);
  }
  if (from.maturitymonthyear().size() > 0) {

    maturitymonthyear_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.maturitymonthyear_);
  }
  if (from.underlyingprice() != 0) {
    set_underlyingprice(from.underlyingprice());
  }
  if (from.matchid().size() > 0) {

    matchid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.matchid_);
  }
  if (from.workbench() != 0) {
    set_workbench(from.workbench());
  }
  if (from.tenor().size() > 0) {

    tenor_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenor_);
  }
}

void MDBondQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDBondQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDBondQuote::CopyFrom(const MDBondQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDBondQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDBondQuote::IsInitialized() const {

  return true;
}

void MDBondQuote::Swap(MDBondQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDBondQuote::InternalSwap(MDBondQuote* other) {
  std::swap(cnexdatatype_, other->cnexdatatype_);
  std::swap(issuedatatime_, other->issuedatatime_);
  quoteid_.Swap(&other->quoteid_);
  std::swap(quotetype_, other->quotetype_);
  std::swap(quoteprice_, other->quoteprice_);
  std::swap(quotesize_, other->quotesize_);
  std::swap(yield_, other->yield_);
  std::swap(quotedate_, other->quotedate_);
  std::swap(quotetime_, other->quotetime_);
  std::swap(quotestatus_, other->quotestatus_);
  std::swap(quotepricetype_, other->quotepricetype_);
  std::swap(maturitydate_, other->maturitydate_);
  cnexsecuritytype_.Swap(&other->cnexsecuritytype_);
  creditrating_.Swap(&other->creditrating_);
  text_.Swap(&other->text_);
  maturitymonthyear_.Swap(&other->maturitymonthyear_);
  std::swap(underlyingprice_, other->underlyingprice_);
  matchid_.Swap(&other->matchid_);
  std::swap(workbench_, other->workbench_);
  tenor_.Swap(&other->tenor_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDBondQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDBondQuote_descriptor_;
  metadata.reflection = MDBondQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDBondQuote

// optional int32 CnexDataType = 1;
void MDBondQuote::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
::google::protobuf::int32 MDBondQuote::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CnexDataType)
  return cnexdatatype_;
}
void MDBondQuote::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CnexDataType)
}

// optional int64 IssueDataTime = 2;
void MDBondQuote::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBondQuote::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.IssueDataTime)
  return issuedatatime_;
}
void MDBondQuote::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.IssueDataTime)
}

// optional string QuoteID = 3;
void MDBondQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
void MDBondQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
void MDBondQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
::std::string* MDBondQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}

// optional int32 QuoteType = 4;
void MDBondQuote::clear_quotetype() {
  quotetype_ = 0;
}
::google::protobuf::int32 MDBondQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteType)
  return quotetype_;
}
void MDBondQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteType)
}

// optional int64 QuotePrice = 5;
void MDBondQuote::clear_quoteprice() {
  quoteprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBondQuote::quoteprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuotePrice)
  return quoteprice_;
}
void MDBondQuote::set_quoteprice(::google::protobuf::int64 value) {
  
  quoteprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuotePrice)
}

// optional int64 QuoteSize = 6;
void MDBondQuote::clear_quotesize() {
  quotesize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBondQuote::quotesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteSize)
  return quotesize_;
}
void MDBondQuote::set_quotesize(::google::protobuf::int64 value) {
  
  quotesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteSize)
}

// optional int64 Yield = 7;
void MDBondQuote::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBondQuote::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Yield)
  return yield_;
}
void MDBondQuote::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Yield)
}

// optional int32 QuoteDate = 8;
void MDBondQuote::clear_quotedate() {
  quotedate_ = 0;
}
::google::protobuf::int32 MDBondQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteDate)
  return quotedate_;
}
void MDBondQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteDate)
}

// optional int32 QuoteTime = 9;
void MDBondQuote::clear_quotetime() {
  quotetime_ = 0;
}
::google::protobuf::int32 MDBondQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteTime)
  return quotetime_;
}
void MDBondQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteTime)
}

// optional int32 QuoteStatus = 10;
void MDBondQuote::clear_quotestatus() {
  quotestatus_ = 0;
}
::google::protobuf::int32 MDBondQuote::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteStatus)
  return quotestatus_;
}
void MDBondQuote::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteStatus)
}

// optional int32 QuotePriceType = 11;
void MDBondQuote::clear_quotepricetype() {
  quotepricetype_ = 0;
}
::google::protobuf::int32 MDBondQuote::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuotePriceType)
  return quotepricetype_;
}
void MDBondQuote::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuotePriceType)
}

// optional int32 MaturityDate = 12;
void MDBondQuote::clear_maturitydate() {
  maturitydate_ = 0;
}
::google::protobuf::int32 MDBondQuote::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MaturityDate)
  return maturitydate_;
}
void MDBondQuote::set_maturitydate(::google::protobuf::int32 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MaturityDate)
}

// optional string CnexSecurityType = 13;
void MDBondQuote::clear_cnexsecuritytype() {
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::cnexsecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  return cnexsecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_cnexsecuritytype(const ::std::string& value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
void MDBondQuote::set_cnexsecuritytype(const char* value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
void MDBondQuote::set_cnexsecuritytype(const char* value, size_t size) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
::std::string* MDBondQuote::mutable_cnexsecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  return cnexsecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_cnexsecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  
  return cnexsecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype) {
  if (cnexsecuritytype != NULL) {
    
  } else {
    
  }
  cnexsecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cnexsecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}

// optional string CreditRating = 14;
void MDBondQuote::clear_creditrating() {
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::creditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  return creditrating_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_creditrating(const ::std::string& value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
void MDBondQuote::set_creditrating(const char* value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
void MDBondQuote::set_creditrating(const char* value, size_t size) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
::std::string* MDBondQuote::mutable_creditrating() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  return creditrating_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_creditrating() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  
  return creditrating_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_creditrating(::std::string* creditrating) {
  if (creditrating != NULL) {
    
  } else {
    
  }
  creditrating_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creditrating);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}

// optional string Text = 15;
void MDBondQuote::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::text() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
void MDBondQuote::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
void MDBondQuote::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
::std::string* MDBondQuote::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.Text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_text() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.Text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.Text)
}

// optional string MaturityMonthYear = 16;
void MDBondQuote::clear_maturitymonthyear() {
  maturitymonthyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::maturitymonthyear() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  return maturitymonthyear_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_maturitymonthyear(const ::std::string& value) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
void MDBondQuote::set_maturitymonthyear(const char* value) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
void MDBondQuote::set_maturitymonthyear(const char* value, size_t size) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
::std::string* MDBondQuote::mutable_maturitymonthyear() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  return maturitymonthyear_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_maturitymonthyear() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  
  return maturitymonthyear_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_maturitymonthyear(::std::string* maturitymonthyear) {
  if (maturitymonthyear != NULL) {
    
  } else {
    
  }
  maturitymonthyear_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), maturitymonthyear);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}

// optional int32 UnderlyingPrice = 17;
void MDBondQuote::clear_underlyingprice() {
  underlyingprice_ = 0;
}
::google::protobuf::int32 MDBondQuote::underlyingprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.UnderlyingPrice)
  return underlyingprice_;
}
void MDBondQuote::set_underlyingprice(::google::protobuf::int32 value) {
  
  underlyingprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.UnderlyingPrice)
}

// optional string MatchId = 18;
void MDBondQuote::clear_matchid() {
  matchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::matchid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  return matchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_matchid(const ::std::string& value) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
void MDBondQuote::set_matchid(const char* value) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
void MDBondQuote::set_matchid(const char* value, size_t size) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
::std::string* MDBondQuote::mutable_matchid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  return matchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_matchid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  
  return matchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_matchid(::std::string* matchid) {
  if (matchid != NULL) {
    
  } else {
    
  }
  matchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), matchid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}

// optional int32 WorkBench = 19;
void MDBondQuote::clear_workbench() {
  workbench_ = 0;
}
::google::protobuf::int32 MDBondQuote::workbench() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.WorkBench)
  return workbench_;
}
void MDBondQuote::set_workbench(::google::protobuf::int32 value) {
  
  workbench_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.WorkBench)
}

// optional string Tenor = 20;
void MDBondQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBondQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
void MDBondQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
void MDBondQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
::std::string* MDBondQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBondQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBondQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}

inline const MDBondQuote* MDBondQuote::internal_default_instance() {
  return &MDBondQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCnyRepoQuote::kCnexDataTypeFieldNumber;
const int MDCnyRepoQuote::kIssueDataTimeFieldNumber;
const int MDCnyRepoQuote::kQuoteIDFieldNumber;
const int MDCnyRepoQuote::kQuoteTypeFieldNumber;
const int MDCnyRepoQuote::kQuoteDateFieldNumber;
const int MDCnyRepoQuote::kQuoteTimeFieldNumber;
const int MDCnyRepoQuote::kQuoteStatusFieldNumber;
const int MDCnyRepoQuote::kQuotePriceTypeFieldNumber;
const int MDCnyRepoQuote::kCreateDateFieldNumber;
const int MDCnyRepoQuote::kModifyDateFieldNumber;
const int MDCnyRepoQuote::kBaseTermFieldNumber;
const int MDCnyRepoQuote::kFloatTypeFieldNumber;
const int MDCnyRepoQuote::kFloatPriceFieldNumber;
const int MDCnyRepoQuote::kFixedRateFieldNumber;
const int MDCnyRepoQuote::kVolumeFieldNumber;
const int MDCnyRepoQuote::kVolumeAboveOrBelowFieldNumber;
const int MDCnyRepoQuote::kVolCanSplitFieldNumber;
const int MDCnyRepoQuote::kSpecialTermFieldNumber;
const int MDCnyRepoQuote::kAt1FieldNumber;
const int MDCnyRepoQuote::kAt2FieldNumber;
const int MDCnyRepoQuote::kATCreditRatingFieldNumber;
const int MDCnyRepoQuote::kATBankLimitedFieldNumber;
const int MDCnyRepoQuote::kZhiLianFieldNumber;
const int MDCnyRepoQuote::kUnderwriterLevel1FieldNumber;
const int MDCnyRepoQuote::kATAddFieldNumber;
const int MDCnyRepoQuote::kDealtypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCnyRepoQuote::MDCnyRepoQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCnexQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCnyRepoQuote)
}

void MDCnyRepoQuote::InitAsDefaultInstance() {
}

MDCnyRepoQuote::MDCnyRepoQuote(const MDCnyRepoQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCnyRepoQuote)
}

void MDCnyRepoQuote::SharedCtor() {
  quoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  floatprice_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fixedrate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volume_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  specialterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  zhilian_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underwriterlevel1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  atadd_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&issuedatatime_, 0, reinterpret_cast<char*>(&atbanklimited_) -
    reinterpret_cast<char*>(&issuedatatime_) + sizeof(atbanklimited_));
  _cached_size_ = 0;
}

MDCnyRepoQuote::~MDCnyRepoQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  SharedDtor();
}

void MDCnyRepoQuote::SharedDtor() {
  quoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  floatprice_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fixedrate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volume_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  specialterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  zhilian_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underwriterlevel1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  atadd_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDCnyRepoQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCnyRepoQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCnyRepoQuote_descriptor_;
}

const MDCnyRepoQuote& MDCnyRepoQuote::default_instance() {
  protobuf_InitDefaults_MDCnexQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCnyRepoQuote> MDCnyRepoQuote_default_instance_;

MDCnyRepoQuote* MDCnyRepoQuote::New(::google::protobuf::Arena* arena) const {
  MDCnyRepoQuote* n = new MDCnyRepoQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCnyRepoQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCnyRepoQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCnyRepoQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(issuedatatime_, quotepricetype_);
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(createdate_, volumeaboveorbelow_);
  floatprice_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fixedrate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volume_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(volcansplit_, atbanklimited_);
  specialterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  zhilian_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underwriterlevel1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  atadd_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDCnyRepoQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 CnexDataType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cnexdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_IssueDataTime;
        break;
      }

      // optional int64 IssueDataTime = 2;
      case 2: {
        if (tag == 16) {
         parse_IssueDataTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &issuedatatime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_QuoteID;
        break;
      }

      // optional string QuoteID = 3;
      case 3: {
        if (tag == 26) {
         parse_QuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoteid().data(), this->quoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_QuoteType;
        break;
      }

      // optional int32 QuoteType = 4;
      case 4: {
        if (tag == 32) {
         parse_QuoteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_QuoteDate;
        break;
      }

      // optional int32 QuoteDate = 5;
      case 5: {
        if (tag == 40) {
         parse_QuoteDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_QuoteTime;
        break;
      }

      // optional int32 QuoteTime = 6;
      case 6: {
        if (tag == 48) {
         parse_QuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_QuoteStatus;
        break;
      }

      // optional int32 QuoteStatus = 7;
      case 7: {
        if (tag == 56) {
         parse_QuoteStatus:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotestatus_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_QuotePriceType;
        break;
      }

      // optional int32 QuotePriceType = 8;
      case 8: {
        if (tag == 64) {
         parse_QuotePriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotepricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_CreateDate;
        break;
      }

      // optional int64 CreateDate = 9;
      case 9: {
        if (tag == 72) {
         parse_CreateDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &createdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ModifyDate;
        break;
      }

      // optional int64 ModifyDate = 10;
      case 10: {
        if (tag == 80) {
         parse_ModifyDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &modifydate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_BaseTerm;
        break;
      }

      // optional int32 BaseTerm = 11;
      case 11: {
        if (tag == 88) {
         parse_BaseTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &baseterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_FloatType;
        break;
      }

      // optional int32 FloatType = 12;
      case 12: {
        if (tag == 96) {
         parse_FloatType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &floattype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_FloatPrice;
        break;
      }

      // optional string FloatPrice = 13;
      case 13: {
        if (tag == 106) {
         parse_FloatPrice:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_floatprice()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->floatprice().data(), this->floatprice().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_FixedRate;
        break;
      }

      // optional string FixedRate = 14;
      case 14: {
        if (tag == 114) {
         parse_FixedRate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fixedrate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fixedrate().data(), this->fixedrate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_Volume;
        break;
      }

      // optional string Volume = 15;
      case 15: {
        if (tag == 122) {
         parse_Volume:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_volume()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->volume().data(), this->volume().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_VolumeAboveOrBelow;
        break;
      }

      // optional int32 VolumeAboveOrBelow = 16;
      case 16: {
        if (tag == 128) {
         parse_VolumeAboveOrBelow:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &volumeaboveorbelow_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_VolCanSplit;
        break;
      }

      // optional int32 VolCanSplit = 17;
      case 17: {
        if (tag == 136) {
         parse_VolCanSplit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &volcansplit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_SpecialTerm;
        break;
      }

      // optional string SpecialTerm = 18;
      case 18: {
        if (tag == 146) {
         parse_SpecialTerm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_specialterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->specialterm().data(), this->specialterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_At1;
        break;
      }

      // optional int32 At1 = 19;
      case 19: {
        if (tag == 152) {
         parse_At1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &at1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_At2;
        break;
      }

      // optional int32 At2 = 20;
      case 20: {
        if (tag == 160) {
         parse_At2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &at2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_ATCreditRating;
        break;
      }

      // optional int32 ATCreditRating = 21;
      case 21: {
        if (tag == 168) {
         parse_ATCreditRating:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &atcreditrating_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_ATBankLimited;
        break;
      }

      // optional int32 ATBankLimited = 22;
      case 22: {
        if (tag == 176) {
         parse_ATBankLimited:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &atbanklimited_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_ZhiLian;
        break;
      }

      // optional string ZhiLian = 23;
      case 23: {
        if (tag == 186) {
         parse_ZhiLian:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_zhilian()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->zhilian().data(), this->zhilian().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_UnderwriterLevel1;
        break;
      }

      // optional string UnderwriterLevel1 = 24;
      case 24: {
        if (tag == 194) {
         parse_UnderwriterLevel1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underwriterlevel1()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underwriterlevel1().data(), this->underwriterlevel1().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_ATAdd;
        break;
      }

      // optional string ATAdd = 25;
      case 25: {
        if (tag == 202) {
         parse_ATAdd:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_atadd()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->atadd().data(), this->atadd().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_Dealtype;
        break;
      }

      // optional string Dealtype = 26;
      case 26: {
        if (tag == 210) {
         parse_Dealtype:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dealtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dealtype().data(), this->dealtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  return false;
#undef DO_
}

void MDCnyRepoQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->cnexdatatype(), output);
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->issuedatatime(), output);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->quoteid(), output);
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->quotetype(), output);
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->quotedate(), output);
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->quotetime(), output);
  }

  // optional int32 QuoteStatus = 7;
  if (this->quotestatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->quotestatus(), output);
  }

  // optional int32 QuotePriceType = 8;
  if (this->quotepricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->quotepricetype(), output);
  }

  // optional int64 CreateDate = 9;
  if (this->createdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->createdate(), output);
  }

  // optional int64 ModifyDate = 10;
  if (this->modifydate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->modifydate(), output);
  }

  // optional int32 BaseTerm = 11;
  if (this->baseterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->baseterm(), output);
  }

  // optional int32 FloatType = 12;
  if (this->floattype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->floattype(), output);
  }

  // optional string FloatPrice = 13;
  if (this->floatprice().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->floatprice().data(), this->floatprice().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->floatprice(), output);
  }

  // optional string FixedRate = 14;
  if (this->fixedrate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fixedrate().data(), this->fixedrate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->fixedrate(), output);
  }

  // optional string Volume = 15;
  if (this->volume().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volume().data(), this->volume().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->volume(), output);
  }

  // optional int32 VolumeAboveOrBelow = 16;
  if (this->volumeaboveorbelow() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->volumeaboveorbelow(), output);
  }

  // optional int32 VolCanSplit = 17;
  if (this->volcansplit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->volcansplit(), output);
  }

  // optional string SpecialTerm = 18;
  if (this->specialterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->specialterm().data(), this->specialterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->specialterm(), output);
  }

  // optional int32 At1 = 19;
  if (this->at1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->at1(), output);
  }

  // optional int32 At2 = 20;
  if (this->at2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->at2(), output);
  }

  // optional int32 ATCreditRating = 21;
  if (this->atcreditrating() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->atcreditrating(), output);
  }

  // optional int32 ATBankLimited = 22;
  if (this->atbanklimited() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(22, this->atbanklimited(), output);
  }

  // optional string ZhiLian = 23;
  if (this->zhilian().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->zhilian().data(), this->zhilian().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->zhilian(), output);
  }

  // optional string UnderwriterLevel1 = 24;
  if (this->underwriterlevel1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underwriterlevel1().data(), this->underwriterlevel1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->underwriterlevel1(), output);
  }

  // optional string ATAdd = 25;
  if (this->atadd().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->atadd().data(), this->atadd().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->atadd(), output);
  }

  // optional string Dealtype = 26;
  if (this->dealtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealtype().data(), this->dealtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      26, this->dealtype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCnyRepoQuote)
}

::google::protobuf::uint8* MDCnyRepoQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->cnexdatatype(), target);
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->issuedatatime(), target);
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->quoteid(), target);
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->quotetype(), target);
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->quotedate(), target);
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->quotetime(), target);
  }

  // optional int32 QuoteStatus = 7;
  if (this->quotestatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->quotestatus(), target);
  }

  // optional int32 QuotePriceType = 8;
  if (this->quotepricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->quotepricetype(), target);
  }

  // optional int64 CreateDate = 9;
  if (this->createdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->createdate(), target);
  }

  // optional int64 ModifyDate = 10;
  if (this->modifydate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->modifydate(), target);
  }

  // optional int32 BaseTerm = 11;
  if (this->baseterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->baseterm(), target);
  }

  // optional int32 FloatType = 12;
  if (this->floattype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->floattype(), target);
  }

  // optional string FloatPrice = 13;
  if (this->floatprice().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->floatprice().data(), this->floatprice().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->floatprice(), target);
  }

  // optional string FixedRate = 14;
  if (this->fixedrate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fixedrate().data(), this->fixedrate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->fixedrate(), target);
  }

  // optional string Volume = 15;
  if (this->volume().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volume().data(), this->volume().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->volume(), target);
  }

  // optional int32 VolumeAboveOrBelow = 16;
  if (this->volumeaboveorbelow() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->volumeaboveorbelow(), target);
  }

  // optional int32 VolCanSplit = 17;
  if (this->volcansplit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->volcansplit(), target);
  }

  // optional string SpecialTerm = 18;
  if (this->specialterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->specialterm().data(), this->specialterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->specialterm(), target);
  }

  // optional int32 At1 = 19;
  if (this->at1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->at1(), target);
  }

  // optional int32 At2 = 20;
  if (this->at2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->at2(), target);
  }

  // optional int32 ATCreditRating = 21;
  if (this->atcreditrating() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->atcreditrating(), target);
  }

  // optional int32 ATBankLimited = 22;
  if (this->atbanklimited() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(22, this->atbanklimited(), target);
  }

  // optional string ZhiLian = 23;
  if (this->zhilian().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->zhilian().data(), this->zhilian().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->zhilian(), target);
  }

  // optional string UnderwriterLevel1 = 24;
  if (this->underwriterlevel1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underwriterlevel1().data(), this->underwriterlevel1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->underwriterlevel1(), target);
  }

  // optional string ATAdd = 25;
  if (this->atadd().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->atadd().data(), this->atadd().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->atadd(), target);
  }

  // optional string Dealtype = 26;
  if (this->dealtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealtype().data(), this->dealtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        26, this->dealtype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  return target;
}

size_t MDCnyRepoQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  size_t total_size = 0;

  // optional int32 CnexDataType = 1;
  if (this->cnexdatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->cnexdatatype());
  }

  // optional int64 IssueDataTime = 2;
  if (this->issuedatatime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->issuedatatime());
  }

  // optional string QuoteID = 3;
  if (this->quoteid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoteid());
  }

  // optional int32 QuoteType = 4;
  if (this->quotetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetype());
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotedate());
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetime());
  }

  // optional int32 QuoteStatus = 7;
  if (this->quotestatus() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotestatus());
  }

  // optional int32 QuotePriceType = 8;
  if (this->quotepricetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotepricetype());
  }

  // optional int64 CreateDate = 9;
  if (this->createdate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->createdate());
  }

  // optional int64 ModifyDate = 10;
  if (this->modifydate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->modifydate());
  }

  // optional int32 BaseTerm = 11;
  if (this->baseterm() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->baseterm());
  }

  // optional int32 FloatType = 12;
  if (this->floattype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->floattype());
  }

  // optional string FloatPrice = 13;
  if (this->floatprice().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->floatprice());
  }

  // optional string FixedRate = 14;
  if (this->fixedrate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fixedrate());
  }

  // optional string Volume = 15;
  if (this->volume().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->volume());
  }

  // optional int32 VolumeAboveOrBelow = 16;
  if (this->volumeaboveorbelow() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->volumeaboveorbelow());
  }

  // optional int32 VolCanSplit = 17;
  if (this->volcansplit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->volcansplit());
  }

  // optional string SpecialTerm = 18;
  if (this->specialterm().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->specialterm());
  }

  // optional int32 At1 = 19;
  if (this->at1() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->at1());
  }

  // optional int32 At2 = 20;
  if (this->at2() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->at2());
  }

  // optional int32 ATCreditRating = 21;
  if (this->atcreditrating() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->atcreditrating());
  }

  // optional int32 ATBankLimited = 22;
  if (this->atbanklimited() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->atbanklimited());
  }

  // optional string ZhiLian = 23;
  if (this->zhilian().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->zhilian());
  }

  // optional string UnderwriterLevel1 = 24;
  if (this->underwriterlevel1().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underwriterlevel1());
  }

  // optional string ATAdd = 25;
  if (this->atadd().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->atadd());
  }

  // optional string Dealtype = 26;
  if (this->dealtype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dealtype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCnyRepoQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCnyRepoQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCnyRepoQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCnyRepoQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCnyRepoQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDCnyRepoQuote::MergeFrom(const MDCnyRepoQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCnyRepoQuote::UnsafeMergeFrom(const MDCnyRepoQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.cnexdatatype() != 0) {
    set_cnexdatatype(from.cnexdatatype());
  }
  if (from.issuedatatime() != 0) {
    set_issuedatatime(from.issuedatatime());
  }
  if (from.quoteid().size() > 0) {

    quoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoteid_);
  }
  if (from.quotetype() != 0) {
    set_quotetype(from.quotetype());
  }
  if (from.quotedate() != 0) {
    set_quotedate(from.quotedate());
  }
  if (from.quotetime() != 0) {
    set_quotetime(from.quotetime());
  }
  if (from.quotestatus() != 0) {
    set_quotestatus(from.quotestatus());
  }
  if (from.quotepricetype() != 0) {
    set_quotepricetype(from.quotepricetype());
  }
  if (from.createdate() != 0) {
    set_createdate(from.createdate());
  }
  if (from.modifydate() != 0) {
    set_modifydate(from.modifydate());
  }
  if (from.baseterm() != 0) {
    set_baseterm(from.baseterm());
  }
  if (from.floattype() != 0) {
    set_floattype(from.floattype());
  }
  if (from.floatprice().size() > 0) {

    floatprice_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.floatprice_);
  }
  if (from.fixedrate().size() > 0) {

    fixedrate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fixedrate_);
  }
  if (from.volume().size() > 0) {

    volume_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.volume_);
  }
  if (from.volumeaboveorbelow() != 0) {
    set_volumeaboveorbelow(from.volumeaboveorbelow());
  }
  if (from.volcansplit() != 0) {
    set_volcansplit(from.volcansplit());
  }
  if (from.specialterm().size() > 0) {

    specialterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.specialterm_);
  }
  if (from.at1() != 0) {
    set_at1(from.at1());
  }
  if (from.at2() != 0) {
    set_at2(from.at2());
  }
  if (from.atcreditrating() != 0) {
    set_atcreditrating(from.atcreditrating());
  }
  if (from.atbanklimited() != 0) {
    set_atbanklimited(from.atbanklimited());
  }
  if (from.zhilian().size() > 0) {

    zhilian_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.zhilian_);
  }
  if (from.underwriterlevel1().size() > 0) {

    underwriterlevel1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underwriterlevel1_);
  }
  if (from.atadd().size() > 0) {

    atadd_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.atadd_);
  }
  if (from.dealtype().size() > 0) {

    dealtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dealtype_);
  }
}

void MDCnyRepoQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCnyRepoQuote::CopyFrom(const MDCnyRepoQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCnyRepoQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCnyRepoQuote::IsInitialized() const {

  return true;
}

void MDCnyRepoQuote::Swap(MDCnyRepoQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCnyRepoQuote::InternalSwap(MDCnyRepoQuote* other) {
  std::swap(cnexdatatype_, other->cnexdatatype_);
  std::swap(issuedatatime_, other->issuedatatime_);
  quoteid_.Swap(&other->quoteid_);
  std::swap(quotetype_, other->quotetype_);
  std::swap(quotedate_, other->quotedate_);
  std::swap(quotetime_, other->quotetime_);
  std::swap(quotestatus_, other->quotestatus_);
  std::swap(quotepricetype_, other->quotepricetype_);
  std::swap(createdate_, other->createdate_);
  std::swap(modifydate_, other->modifydate_);
  std::swap(baseterm_, other->baseterm_);
  std::swap(floattype_, other->floattype_);
  floatprice_.Swap(&other->floatprice_);
  fixedrate_.Swap(&other->fixedrate_);
  volume_.Swap(&other->volume_);
  std::swap(volumeaboveorbelow_, other->volumeaboveorbelow_);
  std::swap(volcansplit_, other->volcansplit_);
  specialterm_.Swap(&other->specialterm_);
  std::swap(at1_, other->at1_);
  std::swap(at2_, other->at2_);
  std::swap(atcreditrating_, other->atcreditrating_);
  std::swap(atbanklimited_, other->atbanklimited_);
  zhilian_.Swap(&other->zhilian_);
  underwriterlevel1_.Swap(&other->underwriterlevel1_);
  atadd_.Swap(&other->atadd_);
  dealtype_.Swap(&other->dealtype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCnyRepoQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCnyRepoQuote_descriptor_;
  metadata.reflection = MDCnyRepoQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCnyRepoQuote

// optional int32 CnexDataType = 1;
void MDCnyRepoQuote::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.CnexDataType)
  return cnexdatatype_;
}
void MDCnyRepoQuote::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.CnexDataType)
}

// optional int64 IssueDataTime = 2;
void MDCnyRepoQuote::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnyRepoQuote::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.IssueDataTime)
  return issuedatatime_;
}
void MDCnyRepoQuote::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.IssueDataTime)
}

// optional string QuoteID = 3;
void MDCnyRepoQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
void MDCnyRepoQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
void MDCnyRepoQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
::std::string* MDCnyRepoQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}

// optional int32 QuoteType = 4;
void MDCnyRepoQuote::clear_quotetype() {
  quotetype_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteType)
  return quotetype_;
}
void MDCnyRepoQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteType)
}

// optional int32 QuoteDate = 5;
void MDCnyRepoQuote::clear_quotedate() {
  quotedate_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteDate)
  return quotedate_;
}
void MDCnyRepoQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteDate)
}

// optional int32 QuoteTime = 6;
void MDCnyRepoQuote::clear_quotetime() {
  quotetime_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteTime)
  return quotetime_;
}
void MDCnyRepoQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteTime)
}

// optional int32 QuoteStatus = 7;
void MDCnyRepoQuote::clear_quotestatus() {
  quotestatus_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteStatus)
  return quotestatus_;
}
void MDCnyRepoQuote::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteStatus)
}

// optional int32 QuotePriceType = 8;
void MDCnyRepoQuote::clear_quotepricetype() {
  quotepricetype_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuotePriceType)
  return quotepricetype_;
}
void MDCnyRepoQuote::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuotePriceType)
}

// optional int64 CreateDate = 9;
void MDCnyRepoQuote::clear_createdate() {
  createdate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnyRepoQuote::createdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.CreateDate)
  return createdate_;
}
void MDCnyRepoQuote::set_createdate(::google::protobuf::int64 value) {
  
  createdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.CreateDate)
}

// optional int64 ModifyDate = 10;
void MDCnyRepoQuote::clear_modifydate() {
  modifydate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnyRepoQuote::modifydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ModifyDate)
  return modifydate_;
}
void MDCnyRepoQuote::set_modifydate(::google::protobuf::int64 value) {
  
  modifydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ModifyDate)
}

// optional int32 BaseTerm = 11;
void MDCnyRepoQuote::clear_baseterm() {
  baseterm_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::baseterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.BaseTerm)
  return baseterm_;
}
void MDCnyRepoQuote::set_baseterm(::google::protobuf::int32 value) {
  
  baseterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.BaseTerm)
}

// optional int32 FloatType = 12;
void MDCnyRepoQuote::clear_floattype() {
  floattype_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::floattype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatType)
  return floattype_;
}
void MDCnyRepoQuote::set_floattype(::google::protobuf::int32 value) {
  
  floattype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatType)
}

// optional string FloatPrice = 13;
void MDCnyRepoQuote::clear_floatprice() {
  floatprice_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::floatprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  return floatprice_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_floatprice(const ::std::string& value) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
void MDCnyRepoQuote::set_floatprice(const char* value) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
void MDCnyRepoQuote::set_floatprice(const char* value, size_t size) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
::std::string* MDCnyRepoQuote::mutable_floatprice() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  return floatprice_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_floatprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  
  return floatprice_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_floatprice(::std::string* floatprice) {
  if (floatprice != NULL) {
    
  } else {
    
  }
  floatprice_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), floatprice);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}

// optional string FixedRate = 14;
void MDCnyRepoQuote::clear_fixedrate() {
  fixedrate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::fixedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  return fixedrate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_fixedrate(const ::std::string& value) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
void MDCnyRepoQuote::set_fixedrate(const char* value) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
void MDCnyRepoQuote::set_fixedrate(const char* value, size_t size) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
::std::string* MDCnyRepoQuote::mutable_fixedrate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  return fixedrate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_fixedrate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  
  return fixedrate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_fixedrate(::std::string* fixedrate) {
  if (fixedrate != NULL) {
    
  } else {
    
  }
  fixedrate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fixedrate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}

// optional string Volume = 15;
void MDCnyRepoQuote::clear_volume() {
  volume_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  return volume_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_volume(const ::std::string& value) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
void MDCnyRepoQuote::set_volume(const char* value) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
void MDCnyRepoQuote::set_volume(const char* value, size_t size) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
::std::string* MDCnyRepoQuote::mutable_volume() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  return volume_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_volume() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  
  return volume_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_volume(::std::string* volume) {
  if (volume != NULL) {
    
  } else {
    
  }
  volume_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volume);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}

// optional int32 VolumeAboveOrBelow = 16;
void MDCnyRepoQuote::clear_volumeaboveorbelow() {
  volumeaboveorbelow_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::volumeaboveorbelow() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolumeAboveOrBelow)
  return volumeaboveorbelow_;
}
void MDCnyRepoQuote::set_volumeaboveorbelow(::google::protobuf::int32 value) {
  
  volumeaboveorbelow_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolumeAboveOrBelow)
}

// optional int32 VolCanSplit = 17;
void MDCnyRepoQuote::clear_volcansplit() {
  volcansplit_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::volcansplit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolCanSplit)
  return volcansplit_;
}
void MDCnyRepoQuote::set_volcansplit(::google::protobuf::int32 value) {
  
  volcansplit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolCanSplit)
}

// optional string SpecialTerm = 18;
void MDCnyRepoQuote::clear_specialterm() {
  specialterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::specialterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  return specialterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_specialterm(const ::std::string& value) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
void MDCnyRepoQuote::set_specialterm(const char* value) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
void MDCnyRepoQuote::set_specialterm(const char* value, size_t size) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
::std::string* MDCnyRepoQuote::mutable_specialterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  return specialterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_specialterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  
  return specialterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_specialterm(::std::string* specialterm) {
  if (specialterm != NULL) {
    
  } else {
    
  }
  specialterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), specialterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}

// optional int32 At1 = 19;
void MDCnyRepoQuote::clear_at1() {
  at1_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::at1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.At1)
  return at1_;
}
void MDCnyRepoQuote::set_at1(::google::protobuf::int32 value) {
  
  at1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.At1)
}

// optional int32 At2 = 20;
void MDCnyRepoQuote::clear_at2() {
  at2_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::at2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.At2)
  return at2_;
}
void MDCnyRepoQuote::set_at2(::google::protobuf::int32 value) {
  
  at2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.At2)
}

// optional int32 ATCreditRating = 21;
void MDCnyRepoQuote::clear_atcreditrating() {
  atcreditrating_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::atcreditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATCreditRating)
  return atcreditrating_;
}
void MDCnyRepoQuote::set_atcreditrating(::google::protobuf::int32 value) {
  
  atcreditrating_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATCreditRating)
}

// optional int32 ATBankLimited = 22;
void MDCnyRepoQuote::clear_atbanklimited() {
  atbanklimited_ = 0;
}
::google::protobuf::int32 MDCnyRepoQuote::atbanklimited() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATBankLimited)
  return atbanklimited_;
}
void MDCnyRepoQuote::set_atbanklimited(::google::protobuf::int32 value) {
  
  atbanklimited_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATBankLimited)
}

// optional string ZhiLian = 23;
void MDCnyRepoQuote::clear_zhilian() {
  zhilian_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::zhilian() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  return zhilian_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_zhilian(const ::std::string& value) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
void MDCnyRepoQuote::set_zhilian(const char* value) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
void MDCnyRepoQuote::set_zhilian(const char* value, size_t size) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
::std::string* MDCnyRepoQuote::mutable_zhilian() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  return zhilian_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_zhilian() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  
  return zhilian_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_zhilian(::std::string* zhilian) {
  if (zhilian != NULL) {
    
  } else {
    
  }
  zhilian_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), zhilian);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}

// optional string UnderwriterLevel1 = 24;
void MDCnyRepoQuote::clear_underwriterlevel1() {
  underwriterlevel1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::underwriterlevel1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  return underwriterlevel1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_underwriterlevel1(const ::std::string& value) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
void MDCnyRepoQuote::set_underwriterlevel1(const char* value) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
void MDCnyRepoQuote::set_underwriterlevel1(const char* value, size_t size) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
::std::string* MDCnyRepoQuote::mutable_underwriterlevel1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  return underwriterlevel1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_underwriterlevel1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  
  return underwriterlevel1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_underwriterlevel1(::std::string* underwriterlevel1) {
  if (underwriterlevel1 != NULL) {
    
  } else {
    
  }
  underwriterlevel1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underwriterlevel1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}

// optional string ATAdd = 25;
void MDCnyRepoQuote::clear_atadd() {
  atadd_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::atadd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  return atadd_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_atadd(const ::std::string& value) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
void MDCnyRepoQuote::set_atadd(const char* value) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
void MDCnyRepoQuote::set_atadd(const char* value, size_t size) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
::std::string* MDCnyRepoQuote::mutable_atadd() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  return atadd_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_atadd() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  
  return atadd_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_atadd(::std::string* atadd) {
  if (atadd != NULL) {
    
  } else {
    
  }
  atadd_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), atadd);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}

// optional string Dealtype = 26;
void MDCnyRepoQuote::clear_dealtype() {
  dealtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnyRepoQuote::dealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  return dealtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_dealtype(const ::std::string& value) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
void MDCnyRepoQuote::set_dealtype(const char* value) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
void MDCnyRepoQuote::set_dealtype(const char* value, size_t size) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
::std::string* MDCnyRepoQuote::mutable_dealtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  return dealtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnyRepoQuote::release_dealtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  
  return dealtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnyRepoQuote::set_allocated_dealtype(::std::string* dealtype) {
  if (dealtype != NULL) {
    
  } else {
    
  }
  dealtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}

inline const MDCnyRepoQuote* MDCnyRepoQuote::internal_default_instance() {
  return &MDCnyRepoQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
