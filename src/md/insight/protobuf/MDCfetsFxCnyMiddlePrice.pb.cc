// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxCnyMiddlePrice.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsFxCnyMiddlePrice.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsFxCnyMiddlePrice_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsFxCnyMiddlePrice_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsFxCnyMiddlePrice_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsFxCnyMiddlePrice_2eproto() {
  protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsFxCnyMiddlePrice.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsFxCnyMiddlePrice_descriptor_ = file->message_type(0);
  static const int MDCfetsFxCnyMiddlePrice_offsets_[12] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, securitysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, middleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, upperlimitprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, lowerlimitprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, datamultiplepowerof10_),
  };
  MDCfetsFxCnyMiddlePrice_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsFxCnyMiddlePrice_descriptor_,
      MDCfetsFxCnyMiddlePrice::internal_default_instance(),
      MDCfetsFxCnyMiddlePrice_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsFxCnyMiddlePrice),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxCnyMiddlePrice, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsFxCnyMiddlePrice_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsFxCnyMiddlePrice_descriptor_, MDCfetsFxCnyMiddlePrice::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsFxCnyMiddlePrice_2eproto() {
  MDCfetsFxCnyMiddlePrice_default_instance_.Shutdown();
  delete MDCfetsFxCnyMiddlePrice_reflection_;
}

void protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsFxCnyMiddlePrice_default_instance_.DefaultConstruct();
  MDCfetsFxCnyMiddlePrice_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto_once_);
void protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\035MDCfetsFxCnyMiddlePrice.proto\022\032com.hts"
    "c.mdc.insight.model\032\027ESecurityIDSource.p"
    "roto\032\023ESecurityType.proto\"\367\002\n\027MDCfetsFxC"
    "nyMiddlePrice\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n"
    "\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTim"
    "estamp\030\004 \001(\003\022\?\n\020securityIDSource\030\005 \001(\0162%"
    ".com.htsc.mdc.model.ESecurityIDSource\0227\n"
    "\014securityType\030\006 \001(\0162!.com.htsc.mdc.model"
    ".ESecurityType\022\027\n\017SecuritySubType\030\007 \001(\t\022"
    "\024\n\014TransactTime\030\010 \001(\t\022\023\n\013MiddlePrice\030\t \001"
    "(\003\022\027\n\017UpperLimitPrice\030\n \001(\003\022\027\n\017LowerLimi"
    "tPrice\030\013 \001(\003\022\035\n\025DataMultiplePowerOf10\030\014 "
    "\001(\005B@\n\032com.htsc.mdc.insight.modelB\035MDCfe"
    "tsFxCnyMiddlePriceProtosH\001\240\001\001b\006proto3", 557);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsFxCnyMiddlePrice.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsFxCnyMiddlePrice_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto_once_);
void protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsFxCnyMiddlePrice_2eproto {
  StaticDescriptorInitializer_MDCfetsFxCnyMiddlePrice_2eproto() {
    protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto();
  }
} static_descriptor_initializer_MDCfetsFxCnyMiddlePrice_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsFxCnyMiddlePrice::kHTSCSecurityIDFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kMDDateFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kMDTimeFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kDataTimestampFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kSecurityIDSourceFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kSecurityTypeFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kSecuritySubTypeFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kTransactTimeFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kMiddlePriceFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kUpperLimitPriceFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kLowerLimitPriceFieldNumber;
const int MDCfetsFxCnyMiddlePrice::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsFxCnyMiddlePrice::MDCfetsFxCnyMiddlePrice()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
}

void MDCfetsFxCnyMiddlePrice::InitAsDefaultInstance() {
}

MDCfetsFxCnyMiddlePrice::MDCfetsFxCnyMiddlePrice(const MDCfetsFxCnyMiddlePrice& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
}

void MDCfetsFxCnyMiddlePrice::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCfetsFxCnyMiddlePrice::~MDCfetsFxCnyMiddlePrice() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  SharedDtor();
}

void MDCfetsFxCnyMiddlePrice::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDCfetsFxCnyMiddlePrice::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsFxCnyMiddlePrice::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsFxCnyMiddlePrice_descriptor_;
}

const MDCfetsFxCnyMiddlePrice& MDCfetsFxCnyMiddlePrice::default_instance() {
  protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxCnyMiddlePrice> MDCfetsFxCnyMiddlePrice_default_instance_;

MDCfetsFxCnyMiddlePrice* MDCfetsFxCnyMiddlePrice::New(::google::protobuf::Arena* arena) const {
  MDCfetsFxCnyMiddlePrice* n = new MDCfetsFxCnyMiddlePrice;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsFxCnyMiddlePrice::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsFxCnyMiddlePrice, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsFxCnyMiddlePrice*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(middleprice_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsFxCnyMiddlePrice::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SecuritySubType;
        break;
      }

      // optional string SecuritySubType = 7;
      case 7: {
        if (tag == 58) {
         parse_SecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitysubtype().data(), this->securitysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 8;
      case 8: {
        if (tag == 66) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MiddlePrice;
        break;
      }

      // optional int64 MiddlePrice = 9;
      case 9: {
        if (tag == 72) {
         parse_MiddlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &middleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_UpperLimitPrice;
        break;
      }

      // optional int64 UpperLimitPrice = 10;
      case 10: {
        if (tag == 80) {
         parse_UpperLimitPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &upperlimitprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LowerLimitPrice;
        break;
      }

      // optional int64 LowerLimitPrice = 11;
      case 11: {
        if (tag == 88) {
         parse_LowerLimitPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowerlimitprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 12;
      case 12: {
        if (tag == 96) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  return false;
#undef DO_
}

void MDCfetsFxCnyMiddlePrice::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->securitysubtype(), output);
  }

  // optional string TransactTime = 8;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->transacttime(), output);
  }

  // optional int64 MiddlePrice = 9;
  if (this->middleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->middleprice(), output);
  }

  // optional int64 UpperLimitPrice = 10;
  if (this->upperlimitprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->upperlimitprice(), output);
  }

  // optional int64 LowerLimitPrice = 11;
  if (this->lowerlimitprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lowerlimitprice(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
}

::google::protobuf::uint8* MDCfetsFxCnyMiddlePrice::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->securitysubtype(), target);
  }

  // optional string TransactTime = 8;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->transacttime(), target);
  }

  // optional int64 MiddlePrice = 9;
  if (this->middleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->middleprice(), target);
  }

  // optional int64 UpperLimitPrice = 10;
  if (this->upperlimitprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->upperlimitprice(), target);
  }

  // optional int64 LowerLimitPrice = 11;
  if (this->lowerlimitprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lowerlimitprice(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  return target;
}

size_t MDCfetsFxCnyMiddlePrice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitysubtype());
  }

  // optional string TransactTime = 8;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional int64 MiddlePrice = 9;
  if (this->middleprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->middleprice());
  }

  // optional int64 UpperLimitPrice = 10;
  if (this->upperlimitprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->upperlimitprice());
  }

  // optional int64 LowerLimitPrice = 11;
  if (this->lowerlimitprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowerlimitprice());
  }

  // optional int32 DataMultiplePowerOf10 = 12;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsFxCnyMiddlePrice::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsFxCnyMiddlePrice* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsFxCnyMiddlePrice>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsFxCnyMiddlePrice::MergeFrom(const MDCfetsFxCnyMiddlePrice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsFxCnyMiddlePrice::UnsafeMergeFrom(const MDCfetsFxCnyMiddlePrice& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securitysubtype().size() > 0) {

    securitysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitysubtype_);
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.middleprice() != 0) {
    set_middleprice(from.middleprice());
  }
  if (from.upperlimitprice() != 0) {
    set_upperlimitprice(from.upperlimitprice());
  }
  if (from.lowerlimitprice() != 0) {
    set_lowerlimitprice(from.lowerlimitprice());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDCfetsFxCnyMiddlePrice::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsFxCnyMiddlePrice::CopyFrom(const MDCfetsFxCnyMiddlePrice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsFxCnyMiddlePrice::IsInitialized() const {

  return true;
}

void MDCfetsFxCnyMiddlePrice::Swap(MDCfetsFxCnyMiddlePrice* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsFxCnyMiddlePrice::InternalSwap(MDCfetsFxCnyMiddlePrice* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  securitysubtype_.Swap(&other->securitysubtype_);
  transacttime_.Swap(&other->transacttime_);
  std::swap(middleprice_, other->middleprice_);
  std::swap(upperlimitprice_, other->upperlimitprice_);
  std::swap(lowerlimitprice_, other->lowerlimitprice_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsFxCnyMiddlePrice::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsFxCnyMiddlePrice_descriptor_;
  metadata.reflection = MDCfetsFxCnyMiddlePrice_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxCnyMiddlePrice

// optional string HTSCSecurityID = 1;
void MDCfetsFxCnyMiddlePrice::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxCnyMiddlePrice::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
void MDCfetsFxCnyMiddlePrice::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}
::std::string* MDCfetsFxCnyMiddlePrice::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxCnyMiddlePrice::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCfetsFxCnyMiddlePrice::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDDate)
  return mddate_;
}
void MDCfetsFxCnyMiddlePrice::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDDate)
}

// optional int32 MDTime = 3;
void MDCfetsFxCnyMiddlePrice::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDTime)
  return mdtime_;
}
void MDCfetsFxCnyMiddlePrice::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCfetsFxCnyMiddlePrice::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsFxCnyMiddlePrice::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCfetsFxCnyMiddlePrice::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxCnyMiddlePrice::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsFxCnyMiddlePrice::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCfetsFxCnyMiddlePrice::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsFxCnyMiddlePrice::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsFxCnyMiddlePrice::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.securityType)
}

// optional string SecuritySubType = 7;
void MDCfetsFxCnyMiddlePrice::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxCnyMiddlePrice::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
void MDCfetsFxCnyMiddlePrice::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}
::std::string* MDCfetsFxCnyMiddlePrice::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxCnyMiddlePrice::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.SecuritySubType)
}

// optional string TransactTime = 8;
void MDCfetsFxCnyMiddlePrice::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxCnyMiddlePrice::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
void MDCfetsFxCnyMiddlePrice::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
void MDCfetsFxCnyMiddlePrice::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}
::std::string* MDCfetsFxCnyMiddlePrice::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxCnyMiddlePrice::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxCnyMiddlePrice::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.TransactTime)
}

// optional int64 MiddlePrice = 9;
void MDCfetsFxCnyMiddlePrice::clear_middleprice() {
  middleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::middleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MiddlePrice)
  return middleprice_;
}
void MDCfetsFxCnyMiddlePrice::set_middleprice(::google::protobuf::int64 value) {
  
  middleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.MiddlePrice)
}

// optional int64 UpperLimitPrice = 10;
void MDCfetsFxCnyMiddlePrice::clear_upperlimitprice() {
  upperlimitprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::upperlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.UpperLimitPrice)
  return upperlimitprice_;
}
void MDCfetsFxCnyMiddlePrice::set_upperlimitprice(::google::protobuf::int64 value) {
  
  upperlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.UpperLimitPrice)
}

// optional int64 LowerLimitPrice = 11;
void MDCfetsFxCnyMiddlePrice::clear_lowerlimitprice() {
  lowerlimitprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxCnyMiddlePrice::lowerlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.LowerLimitPrice)
  return lowerlimitprice_;
}
void MDCfetsFxCnyMiddlePrice::set_lowerlimitprice(::google::protobuf::int64 value) {
  
  lowerlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.LowerLimitPrice)
}

// optional int32 DataMultiplePowerOf10 = 12;
void MDCfetsFxCnyMiddlePrice::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsFxCnyMiddlePrice::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsFxCnyMiddlePrice::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice.DataMultiplePowerOf10)
}

inline const MDCfetsFxCnyMiddlePrice* MDCfetsFxCnyMiddlePrice::internal_default_instance() {
  return &MDCfetsFxCnyMiddlePrice_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
