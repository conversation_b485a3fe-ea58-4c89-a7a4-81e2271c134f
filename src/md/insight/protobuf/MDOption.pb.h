// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDOption.proto

#ifndef PROTOBUF_MDOption_2eproto__INCLUDED
#define PROTOBUF_MDOption_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDOption_2eproto();
void protobuf_InitDefaults_MDOption_2eproto();
void protobuf_AssignDesc_MDOption_2eproto();
void protobuf_ShutdownFile_MDOption_2eproto();

class MDOption;

// ===================================================================

class MDOption : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDOption) */ {
 public:
  MDOption();
  virtual ~MDOption();

  MDOption(const MDOption& from);

  inline MDOption& operator=(const MDOption& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDOption& default_instance();

  static const MDOption* internal_default_instance();

  void Swap(MDOption* other);

  // implements Message ----------------------------------------------

  inline MDOption* New() const { return New(NULL); }

  MDOption* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDOption& from);
  void MergeFrom(const MDOption& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDOption* other);
  void UnsafeMergeFrom(const MDOption& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int64 DiffPx1 = 19;
  void clear_diffpx1();
  static const int kDiffPx1FieldNumber = 19;
  ::google::protobuf::int64 diffpx1() const;
  void set_diffpx1(::google::protobuf::int64 value);

  // optional int64 DiffPx2 = 20;
  void clear_diffpx2();
  static const int kDiffPx2FieldNumber = 20;
  ::google::protobuf::int64 diffpx2() const;
  void set_diffpx2(::google::protobuf::int64 value);

  // optional int64 TotalBuyQty = 21;
  void clear_totalbuyqty();
  static const int kTotalBuyQtyFieldNumber = 21;
  ::google::protobuf::int64 totalbuyqty() const;
  void set_totalbuyqty(::google::protobuf::int64 value);

  // optional int64 TotalSellQty = 22;
  void clear_totalsellqty();
  static const int kTotalSellQtyFieldNumber = 22;
  ::google::protobuf::int64 totalsellqty() const;
  void set_totalsellqty(::google::protobuf::int64 value);

  // optional int64 WeightedAvgBuyPx = 23;
  void clear_weightedavgbuypx();
  static const int kWeightedAvgBuyPxFieldNumber = 23;
  ::google::protobuf::int64 weightedavgbuypx() const;
  void set_weightedavgbuypx(::google::protobuf::int64 value);

  // optional int64 WeightedAvgSellPx = 24;
  void clear_weightedavgsellpx();
  static const int kWeightedAvgSellPxFieldNumber = 24;
  ::google::protobuf::int64 weightedavgsellpx() const;
  void set_weightedavgsellpx(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyNumber = 25;
  void clear_withdrawbuynumber();
  static const int kWithdrawBuyNumberFieldNumber = 25;
  ::google::protobuf::int64 withdrawbuynumber() const;
  void set_withdrawbuynumber(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyAmount = 26;
  void clear_withdrawbuyamount();
  static const int kWithdrawBuyAmountFieldNumber = 26;
  ::google::protobuf::int64 withdrawbuyamount() const;
  void set_withdrawbuyamount(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyMoney = 27;
  void clear_withdrawbuymoney();
  static const int kWithdrawBuyMoneyFieldNumber = 27;
  ::google::protobuf::int64 withdrawbuymoney() const;
  void set_withdrawbuymoney(::google::protobuf::int64 value);

  // optional int64 WithdrawSellNumber = 28;
  void clear_withdrawsellnumber();
  static const int kWithdrawSellNumberFieldNumber = 28;
  ::google::protobuf::int64 withdrawsellnumber() const;
  void set_withdrawsellnumber(::google::protobuf::int64 value);

  // optional int64 WithdrawSellAmount = 29;
  void clear_withdrawsellamount();
  static const int kWithdrawSellAmountFieldNumber = 29;
  ::google::protobuf::int64 withdrawsellamount() const;
  void set_withdrawsellamount(::google::protobuf::int64 value);

  // optional int64 WithdrawSellMoney = 30;
  void clear_withdrawsellmoney();
  static const int kWithdrawSellMoneyFieldNumber = 30;
  ::google::protobuf::int64 withdrawsellmoney() const;
  void set_withdrawsellmoney(::google::protobuf::int64 value);

  // optional int64 TotalBuyNumber = 31;
  void clear_totalbuynumber();
  static const int kTotalBuyNumberFieldNumber = 31;
  ::google::protobuf::int64 totalbuynumber() const;
  void set_totalbuynumber(::google::protobuf::int64 value);

  // optional int64 TotalSellNumber = 32;
  void clear_totalsellnumber();
  static const int kTotalSellNumberFieldNumber = 32;
  ::google::protobuf::int64 totalsellnumber() const;
  void set_totalsellnumber(::google::protobuf::int64 value);

  // optional int64 BuyTradeMaxDuration = 33;
  void clear_buytrademaxduration();
  static const int kBuyTradeMaxDurationFieldNumber = 33;
  ::google::protobuf::int64 buytrademaxduration() const;
  void set_buytrademaxduration(::google::protobuf::int64 value);

  // optional int64 SellTradeMaxDuration = 34;
  void clear_selltrademaxduration();
  static const int kSellTradeMaxDurationFieldNumber = 34;
  ::google::protobuf::int64 selltrademaxduration() const;
  void set_selltrademaxduration(::google::protobuf::int64 value);

  // optional int32 NumBuyOrders = 35;
  void clear_numbuyorders();
  static const int kNumBuyOrdersFieldNumber = 35;
  ::google::protobuf::int32 numbuyorders() const;
  void set_numbuyorders(::google::protobuf::int32 value);

  // optional int32 NumSellOrders = 36;
  void clear_numsellorders();
  static const int kNumSellOrdersFieldNumber = 36;
  ::google::protobuf::int32 numsellorders() const;
  void set_numsellorders(::google::protobuf::int32 value);

  // optional int32 TradingDate = 37;
  void clear_tradingdate();
  static const int kTradingDateFieldNumber = 37;
  ::google::protobuf::int32 tradingdate() const;
  void set_tradingdate(::google::protobuf::int32 value);

  // optional int64 PreOpenInterest = 38;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 38;
  ::google::protobuf::int64 preopeninterest() const;
  void set_preopeninterest(::google::protobuf::int64 value);

  // optional int64 PreSettlePrice = 39;
  void clear_presettleprice();
  static const int kPreSettlePriceFieldNumber = 39;
  ::google::protobuf::int64 presettleprice() const;
  void set_presettleprice(::google::protobuf::int64 value);

  // optional int64 OpenInterest = 40;
  void clear_openinterest();
  static const int kOpenInterestFieldNumber = 40;
  ::google::protobuf::int64 openinterest() const;
  void set_openinterest(::google::protobuf::int64 value);

  // optional int64 SettlePrice = 41;
  void clear_settleprice();
  static const int kSettlePriceFieldNumber = 41;
  ::google::protobuf::int64 settleprice() const;
  void set_settleprice(::google::protobuf::int64 value);

  // optional int64 PreDelta = 42;
  void clear_predelta();
  static const int kPreDeltaFieldNumber = 42;
  ::google::protobuf::int64 predelta() const;
  void set_predelta(::google::protobuf::int64 value);

  // optional int64 CurrDelta = 43;
  void clear_currdelta();
  static const int kCurrDeltaFieldNumber = 43;
  ::google::protobuf::int64 currdelta() const;
  void set_currdelta(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 44;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 44;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 45;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 45;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 ReferencePrice = 46;
  void clear_referenceprice();
  static const int kReferencePriceFieldNumber = 46;
  ::google::protobuf::int64 referenceprice() const;
  void set_referenceprice(::google::protobuf::int64 value);

  // optional int32 ChannelNo = 50;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 50;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 59;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 59;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDOption)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 diffpx1_;
  ::google::protobuf::int64 diffpx2_;
  ::google::protobuf::int64 totalbuyqty_;
  ::google::protobuf::int64 totalsellqty_;
  ::google::protobuf::int64 weightedavgbuypx_;
  ::google::protobuf::int64 weightedavgsellpx_;
  ::google::protobuf::int64 withdrawbuynumber_;
  ::google::protobuf::int64 withdrawbuyamount_;
  ::google::protobuf::int64 withdrawbuymoney_;
  ::google::protobuf::int64 withdrawsellnumber_;
  ::google::protobuf::int64 withdrawsellamount_;
  ::google::protobuf::int64 withdrawsellmoney_;
  ::google::protobuf::int64 totalbuynumber_;
  ::google::protobuf::int64 totalsellnumber_;
  ::google::protobuf::int64 buytrademaxduration_;
  ::google::protobuf::int64 selltrademaxduration_;
  ::google::protobuf::int32 numbuyorders_;
  ::google::protobuf::int32 numsellorders_;
  ::google::protobuf::int64 preopeninterest_;
  ::google::protobuf::int64 presettleprice_;
  ::google::protobuf::int64 openinterest_;
  ::google::protobuf::int32 tradingdate_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 settleprice_;
  ::google::protobuf::int64 predelta_;
  ::google::protobuf::int64 currdelta_;
  ::google::protobuf::int64 referenceprice_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 delaytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDOption_2eproto_impl();
  friend void  protobuf_AddDesc_MDOption_2eproto_impl();
  friend void protobuf_AssignDesc_MDOption_2eproto();
  friend void protobuf_ShutdownFile_MDOption_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDOption> MDOption_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDOption

// optional string HTSCSecurityID = 1;
inline void MDOption::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOption::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOption::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
inline void MDOption::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
inline void MDOption::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
inline ::std::string* MDOption::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOption::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOption::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDOption::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDOption::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MDDate)
  return mddate_;
}
inline void MDOption::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MDDate)
}

// optional int32 MDTime = 3;
inline void MDOption::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDOption::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MDTime)
  return mdtime_;
}
inline void MDOption::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDOption::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DataTimestamp)
  return datatimestamp_;
}
inline void MDOption::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDOption::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOption::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOption::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
inline void MDOption::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
inline void MDOption::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
inline ::std::string* MDOption::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOption::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOption::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDOption::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDOption::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDOption::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDOption::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDOption::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDOption::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.securityType)
}

// optional int64 MaxPx = 8;
inline void MDOption::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MaxPx)
  return maxpx_;
}
inline void MDOption::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDOption::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MinPx)
  return minpx_;
}
inline void MDOption::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDOption::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreClosePx)
  return preclosepx_;
}
inline void MDOption::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDOption::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumTrades)
  return numtrades_;
}
inline void MDOption::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDOption::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDOption::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDOption::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDOption::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void MDOption::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.LastPx)
  return lastpx_;
}
inline void MDOption::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.LastPx)
}

// optional int64 OpenPx = 15;
inline void MDOption::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.OpenPx)
  return openpx_;
}
inline void MDOption::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.OpenPx)
}

// optional int64 ClosePx = 16;
inline void MDOption::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ClosePx)
  return closepx_;
}
inline void MDOption::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ClosePx)
}

// optional int64 HighPx = 17;
inline void MDOption::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.HighPx)
  return highpx_;
}
inline void MDOption::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.HighPx)
}

// optional int64 LowPx = 18;
inline void MDOption::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.LowPx)
  return lowpx_;
}
inline void MDOption::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.LowPx)
}

// optional int64 DiffPx1 = 19;
inline void MDOption::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DiffPx1)
  return diffpx1_;
}
inline void MDOption::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DiffPx1)
}

// optional int64 DiffPx2 = 20;
inline void MDOption::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DiffPx2)
  return diffpx2_;
}
inline void MDOption::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
inline void MDOption::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalBuyQty)
  return totalbuyqty_;
}
inline void MDOption::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
inline void MDOption::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalSellQty)
  return totalsellqty_;
}
inline void MDOption::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
inline void MDOption::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
inline void MDOption::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
inline void MDOption::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
inline void MDOption::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
inline void MDOption::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
inline void MDOption::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
inline void MDOption::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
inline void MDOption::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
inline void MDOption::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
inline void MDOption::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
inline void MDOption::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellNumber)
  return withdrawsellnumber_;
}
inline void MDOption::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
inline void MDOption::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellAmount)
  return withdrawsellamount_;
}
inline void MDOption::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
inline void MDOption::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellMoney)
  return withdrawsellmoney_;
}
inline void MDOption::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
inline void MDOption::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalBuyNumber)
  return totalbuynumber_;
}
inline void MDOption::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
inline void MDOption::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalSellNumber)
  return totalsellnumber_;
}
inline void MDOption::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
inline void MDOption::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
inline void MDOption::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
inline void MDOption::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellTradeMaxDuration)
  return selltrademaxduration_;
}
inline void MDOption::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
inline void MDOption::clear_numbuyorders() {
  numbuyorders_ = 0;
}
inline ::google::protobuf::int32 MDOption::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumBuyOrders)
  return numbuyorders_;
}
inline void MDOption::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
inline void MDOption::clear_numsellorders() {
  numsellorders_ = 0;
}
inline ::google::protobuf::int32 MDOption::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumSellOrders)
  return numsellorders_;
}
inline void MDOption::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumSellOrders)
}

// optional int32 TradingDate = 37;
inline void MDOption::clear_tradingdate() {
  tradingdate_ = 0;
}
inline ::google::protobuf::int32 MDOption::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TradingDate)
  return tradingdate_;
}
inline void MDOption::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TradingDate)
}

// optional int64 PreOpenInterest = 38;
inline void MDOption::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreOpenInterest)
  return preopeninterest_;
}
inline void MDOption::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreOpenInterest)
}

// optional int64 PreSettlePrice = 39;
inline void MDOption::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreSettlePrice)
  return presettleprice_;
}
inline void MDOption::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreSettlePrice)
}

// optional int64 OpenInterest = 40;
inline void MDOption::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.OpenInterest)
  return openinterest_;
}
inline void MDOption::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.OpenInterest)
}

// optional int64 SettlePrice = 41;
inline void MDOption::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SettlePrice)
  return settleprice_;
}
inline void MDOption::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SettlePrice)
}

// optional int64 PreDelta = 42;
inline void MDOption::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreDelta)
  return predelta_;
}
inline void MDOption::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreDelta)
}

// optional int64 CurrDelta = 43;
inline void MDOption::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.CurrDelta)
  return currdelta_;
}
inline void MDOption::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.CurrDelta)
}

// optional int32 ExchangeDate = 44;
inline void MDOption::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDOption::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ExchangeDate)
  return exchangedate_;
}
inline void MDOption::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ExchangeDate)
}

// optional int32 ExchangeTime = 45;
inline void MDOption::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDOption::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ExchangeTime)
  return exchangetime_;
}
inline void MDOption::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ExchangeTime)
}

// optional int64 ReferencePrice = 46;
inline void MDOption::clear_referenceprice() {
  referenceprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOption::referenceprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ReferencePrice)
  return referenceprice_;
}
inline void MDOption::set_referenceprice(::google::protobuf::int64 value) {
  
  referenceprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ReferencePrice)
}

// optional int32 ChannelNo = 50;
inline void MDOption::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDOption::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ChannelNo)
  return channelno_;
}
inline void MDOption::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDOption::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDOption::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDOption::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDOption::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
}
inline void MDOption::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDOption::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDOption::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDOption::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
}
inline void MDOption::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDOption::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDOption::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDOption::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDOption::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
}
inline void MDOption::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDOption::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDOption::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDOption::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
}
inline void MDOption::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDOption::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDOption::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDOption::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
}
inline void MDOption::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDOption::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDOption::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDOption::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
}
inline void MDOption::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDOption::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDOption::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDOption::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
}
inline void MDOption::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDOption::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDOption::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDOption::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDOption::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
}
inline void MDOption::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
inline void MDOption::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDOption::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDOption::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DataMultiplePowerOf10)
}

// optional int32 DelayType = 101;
inline void MDOption::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDOption::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DelayType)
  return delaytype_;
}
inline void MDOption::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DelayType)
}

inline const MDOption* MDOption::internal_default_instance() {
  return &MDOption_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDOption_2eproto__INCLUDED
