// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDFIQuote.proto

#ifndef PROTOBUF_MDFIQuote_2eproto__INCLUDED
#define PROTOBUF_MDFIQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDFIQuote_2eproto();
void protobuf_InitDefaults_MDFIQuote_2eproto();
void protobuf_AssignDesc_MDFIQuote_2eproto();
void protobuf_ShutdownFile_MDFIQuote_2eproto();

class ADFIQuote;
class MDFIQuote;

// ===================================================================

class MDFIQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDFIQuote) */ {
 public:
  MDFIQuote();
  virtual ~MDFIQuote();

  MDFIQuote(const MDFIQuote& from);

  inline MDFIQuote& operator=(const MDFIQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDFIQuote& default_instance();

  static const MDFIQuote* internal_default_instance();

  void Swap(MDFIQuote* other);

  // implements Message ----------------------------------------------

  inline MDFIQuote* New() const { return New(NULL); }

  MDFIQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDFIQuote& from);
  void MergeFrom(const MDFIQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDFIQuote* other);
  void UnsafeMergeFrom(const MDFIQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 7;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 7;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 8;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 8;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 MessageNumber = 9;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 9;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // optional int32 BrokerDataType = 10;
  void clear_brokerdatatype();
  static const int kBrokerDataTypeFieldNumber = 10;
  ::google::protobuf::int32 brokerdatatype() const;
  void set_brokerdatatype(::google::protobuf::int32 value);

  // optional int64 AccruedInterest = 11;
  void clear_accruedinterest();
  static const int kAccruedInterestFieldNumber = 11;
  ::google::protobuf::int64 accruedinterest() const;
  void set_accruedinterest(::google::protobuf::int64 value);

  // optional string BuyQuoteID = 20;
  void clear_buyquoteid();
  static const int kBuyQuoteIDFieldNumber = 20;
  const ::std::string& buyquoteid() const;
  void set_buyquoteid(const ::std::string& value);
  void set_buyquoteid(const char* value);
  void set_buyquoteid(const char* value, size_t size);
  ::std::string* mutable_buyquoteid();
  ::std::string* release_buyquoteid();
  void set_allocated_buyquoteid(::std::string* buyquoteid);

  // optional int32 BuyQuoteTime = 21;
  void clear_buyquotetime();
  static const int kBuyQuoteTimeFieldNumber = 21;
  ::google::protobuf::int32 buyquotetime() const;
  void set_buyquotetime(::google::protobuf::int32 value);

  // optional string BuyQuoter = 22;
  void clear_buyquoter();
  static const int kBuyQuoterFieldNumber = 22;
  const ::std::string& buyquoter() const;
  void set_buyquoter(const ::std::string& value);
  void set_buyquoter(const char* value);
  void set_buyquoter(const char* value, size_t size);
  ::std::string* mutable_buyquoter();
  ::std::string* release_buyquoter();
  void set_allocated_buyquoter(::std::string* buyquoter);

  // optional int64 BuyCleanPrice = 23;
  void clear_buycleanprice();
  static const int kBuyCleanPriceFieldNumber = 23;
  ::google::protobuf::int64 buycleanprice() const;
  void set_buycleanprice(::google::protobuf::int64 value);

  // optional int64 BuyVolume = 24;
  void clear_buyvolume();
  static const int kBuyVolumeFieldNumber = 24;
  ::google::protobuf::int64 buyvolume() const;
  void set_buyvolume(::google::protobuf::int64 value);

  // optional int64 BuyDirtyPrice = 25;
  void clear_buydirtyprice();
  static const int kBuyDirtyPriceFieldNumber = 25;
  ::google::protobuf::int64 buydirtyprice() const;
  void set_buydirtyprice(::google::protobuf::int64 value);

  // optional int64 BuyMaturityYield = 26;
  void clear_buymaturityyield();
  static const int kBuyMaturityYieldFieldNumber = 26;
  ::google::protobuf::int64 buymaturityyield() const;
  void set_buymaturityyield(::google::protobuf::int64 value);

  // optional string BuyQuoteComment = 27;
  void clear_buyquotecomment();
  static const int kBuyQuoteCommentFieldNumber = 27;
  const ::std::string& buyquotecomment() const;
  void set_buyquotecomment(const ::std::string& value);
  void set_buyquotecomment(const char* value);
  void set_buyquotecomment(const char* value, size_t size);
  ::std::string* mutable_buyquotecomment();
  ::std::string* release_buyquotecomment();
  void set_allocated_buyquotecomment(::std::string* buyquotecomment);

  // optional int32 BuyQuoteType = 28;
  void clear_buyquotetype();
  static const int kBuyQuoteTypeFieldNumber = 28;
  ::google::protobuf::int32 buyquotetype() const;
  void set_buyquotetype(::google::protobuf::int32 value);

  // optional int32 BuyBargainType = 29;
  void clear_buybargaintype();
  static const int kBuyBargainTypeFieldNumber = 29;
  ::google::protobuf::int32 buybargaintype() const;
  void set_buybargaintype(::google::protobuf::int32 value);

  // optional int32 BuyRelationType = 30;
  void clear_buyrelationtype();
  static const int kBuyRelationTypeFieldNumber = 30;
  ::google::protobuf::int32 buyrelationtype() const;
  void set_buyrelationtype(::google::protobuf::int32 value);

  // optional int32 BuyExerciseType = 31;
  void clear_buyexercisetype();
  static const int kBuyExerciseTypeFieldNumber = 31;
  ::google::protobuf::int32 buyexercisetype() const;
  void set_buyexercisetype(::google::protobuf::int32 value);

  // optional int32 BuyYieldType = 32;
  void clear_buyyieldtype();
  static const int kBuyYieldTypeFieldNumber = 32;
  ::google::protobuf::int32 buyyieldtype() const;
  void set_buyyieldtype(::google::protobuf::int32 value);

  // optional string SellQuoteID = 33;
  void clear_sellquoteid();
  static const int kSellQuoteIDFieldNumber = 33;
  const ::std::string& sellquoteid() const;
  void set_sellquoteid(const ::std::string& value);
  void set_sellquoteid(const char* value);
  void set_sellquoteid(const char* value, size_t size);
  ::std::string* mutable_sellquoteid();
  ::std::string* release_sellquoteid();
  void set_allocated_sellquoteid(::std::string* sellquoteid);

  // optional int32 SellQuoteTime = 34;
  void clear_sellquotetime();
  static const int kSellQuoteTimeFieldNumber = 34;
  ::google::protobuf::int32 sellquotetime() const;
  void set_sellquotetime(::google::protobuf::int32 value);

  // optional string SellQuoter = 35;
  void clear_sellquoter();
  static const int kSellQuoterFieldNumber = 35;
  const ::std::string& sellquoter() const;
  void set_sellquoter(const ::std::string& value);
  void set_sellquoter(const char* value);
  void set_sellquoter(const char* value, size_t size);
  ::std::string* mutable_sellquoter();
  ::std::string* release_sellquoter();
  void set_allocated_sellquoter(::std::string* sellquoter);

  // optional int64 SellCleanPrice = 36;
  void clear_sellcleanprice();
  static const int kSellCleanPriceFieldNumber = 36;
  ::google::protobuf::int64 sellcleanprice() const;
  void set_sellcleanprice(::google::protobuf::int64 value);

  // optional int64 SellVolume = 37;
  void clear_sellvolume();
  static const int kSellVolumeFieldNumber = 37;
  ::google::protobuf::int64 sellvolume() const;
  void set_sellvolume(::google::protobuf::int64 value);

  // optional int64 SellDirtyPrice = 38;
  void clear_selldirtyprice();
  static const int kSellDirtyPriceFieldNumber = 38;
  ::google::protobuf::int64 selldirtyprice() const;
  void set_selldirtyprice(::google::protobuf::int64 value);

  // optional int64 SellMaturityYield = 39;
  void clear_sellmaturityyield();
  static const int kSellMaturityYieldFieldNumber = 39;
  ::google::protobuf::int64 sellmaturityyield() const;
  void set_sellmaturityyield(::google::protobuf::int64 value);

  // optional string SellQuoteComment = 40;
  void clear_sellquotecomment();
  static const int kSellQuoteCommentFieldNumber = 40;
  const ::std::string& sellquotecomment() const;
  void set_sellquotecomment(const ::std::string& value);
  void set_sellquotecomment(const char* value);
  void set_sellquotecomment(const char* value, size_t size);
  ::std::string* mutable_sellquotecomment();
  ::std::string* release_sellquotecomment();
  void set_allocated_sellquotecomment(::std::string* sellquotecomment);

  // optional int32 SellQuoteType = 41;
  void clear_sellquotetype();
  static const int kSellQuoteTypeFieldNumber = 41;
  ::google::protobuf::int32 sellquotetype() const;
  void set_sellquotetype(::google::protobuf::int32 value);

  // optional int32 SellBargainType = 42;
  void clear_sellbargaintype();
  static const int kSellBargainTypeFieldNumber = 42;
  ::google::protobuf::int32 sellbargaintype() const;
  void set_sellbargaintype(::google::protobuf::int32 value);

  // optional int32 SellRelationType = 43;
  void clear_sellrelationtype();
  static const int kSellRelationTypeFieldNumber = 43;
  ::google::protobuf::int32 sellrelationtype() const;
  void set_sellrelationtype(::google::protobuf::int32 value);

  // optional int32 SellExerciseType = 44;
  void clear_sellexercisetype();
  static const int kSellExerciseTypeFieldNumber = 44;
  ::google::protobuf::int32 sellexercisetype() const;
  void set_sellexercisetype(::google::protobuf::int32 value);

  // optional int32 SellYieldType = 45;
  void clear_sellyieldtype();
  static const int kSellYieldTypeFieldNumber = 45;
  ::google::protobuf::int32 sellyieldtype() const;
  void set_sellyieldtype(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 46;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 46;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
  int quotes_size() const;
  void clear_quotes();
  static const int kQuotesFieldNumber = 47;
  const ::com::htsc::mdc::insight::model::ADFIQuote& quotes(int index) const;
  ::com::htsc::mdc::insight::model::ADFIQuote* mutable_quotes(int index);
  ::com::htsc::mdc::insight::model::ADFIQuote* add_quotes();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >*
      mutable_quotes();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >&
      quotes() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDFIQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote > quotes_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr buyquoteid_;
  ::google::protobuf::internal::ArenaStringPtr buyquoter_;
  ::google::protobuf::internal::ArenaStringPtr buyquotecomment_;
  ::google::protobuf::internal::ArenaStringPtr sellquoteid_;
  ::google::protobuf::internal::ArenaStringPtr sellquoter_;
  ::google::protobuf::internal::ArenaStringPtr sellquotecomment_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 messagenumber_;
  ::google::protobuf::int64 accruedinterest_;
  ::google::protobuf::int32 brokerdatatype_;
  ::google::protobuf::int32 buyquotetime_;
  ::google::protobuf::int64 buycleanprice_;
  ::google::protobuf::int64 buyvolume_;
  ::google::protobuf::int64 buydirtyprice_;
  ::google::protobuf::int64 buymaturityyield_;
  ::google::protobuf::int32 buyquotetype_;
  ::google::protobuf::int32 buybargaintype_;
  ::google::protobuf::int32 buyrelationtype_;
  ::google::protobuf::int32 buyexercisetype_;
  ::google::protobuf::int32 buyyieldtype_;
  ::google::protobuf::int32 sellquotetime_;
  ::google::protobuf::int64 sellcleanprice_;
  ::google::protobuf::int64 sellvolume_;
  ::google::protobuf::int64 selldirtyprice_;
  ::google::protobuf::int64 sellmaturityyield_;
  ::google::protobuf::int32 sellquotetype_;
  ::google::protobuf::int32 sellbargaintype_;
  ::google::protobuf::int32 sellrelationtype_;
  ::google::protobuf::int32 sellexercisetype_;
  ::google::protobuf::int32 sellyieldtype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDFIQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDFIQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDFIQuote_2eproto();
  friend void protobuf_ShutdownFile_MDFIQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDFIQuote> MDFIQuote_default_instance_;

// -------------------------------------------------------------------

class ADFIQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADFIQuote) */ {
 public:
  ADFIQuote();
  virtual ~ADFIQuote();

  ADFIQuote(const ADFIQuote& from);

  inline ADFIQuote& operator=(const ADFIQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADFIQuote& default_instance();

  static const ADFIQuote* internal_default_instance();

  void Swap(ADFIQuote* other);

  // implements Message ----------------------------------------------

  inline ADFIQuote* New() const { return New(NULL); }

  ADFIQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADFIQuote& from);
  void MergeFrom(const ADFIQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADFIQuote* other);
  void UnsafeMergeFrom(const ADFIQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 QuoteBSFlag = 1;
  void clear_quotebsflag();
  static const int kQuoteBSFlagFieldNumber = 1;
  ::google::protobuf::int32 quotebsflag() const;
  void set_quotebsflag(::google::protobuf::int32 value);

  // optional int32 QuoteLevel = 2;
  void clear_quotelevel();
  static const int kQuoteLevelFieldNumber = 2;
  ::google::protobuf::int32 quotelevel() const;
  void set_quotelevel(::google::protobuf::int32 value);

  // optional string QuoteID = 3;
  void clear_quoteid();
  static const int kQuoteIDFieldNumber = 3;
  const ::std::string& quoteid() const;
  void set_quoteid(const ::std::string& value);
  void set_quoteid(const char* value);
  void set_quoteid(const char* value, size_t size);
  ::std::string* mutable_quoteid();
  ::std::string* release_quoteid();
  void set_allocated_quoteid(::std::string* quoteid);

  // optional int32 QuoteTime = 4;
  void clear_quotetime();
  static const int kQuoteTimeFieldNumber = 4;
  ::google::protobuf::int32 quotetime() const;
  void set_quotetime(::google::protobuf::int32 value);

  // optional string Quoter = 5;
  void clear_quoter();
  static const int kQuoterFieldNumber = 5;
  const ::std::string& quoter() const;
  void set_quoter(const ::std::string& value);
  void set_quoter(const char* value);
  void set_quoter(const char* value, size_t size);
  ::std::string* mutable_quoter();
  ::std::string* release_quoter();
  void set_allocated_quoter(::std::string* quoter);

  // optional int64 CleanPrice = 6;
  void clear_cleanprice();
  static const int kCleanPriceFieldNumber = 6;
  ::google::protobuf::int64 cleanprice() const;
  void set_cleanprice(::google::protobuf::int64 value);

  // optional int64 Volume = 7;
  void clear_volume();
  static const int kVolumeFieldNumber = 7;
  ::google::protobuf::int64 volume() const;
  void set_volume(::google::protobuf::int64 value);

  // optional int64 DirtyPrice = 8;
  void clear_dirtyprice();
  static const int kDirtyPriceFieldNumber = 8;
  ::google::protobuf::int64 dirtyprice() const;
  void set_dirtyprice(::google::protobuf::int64 value);

  // optional int64 MaturityYield = 9;
  void clear_maturityyield();
  static const int kMaturityYieldFieldNumber = 9;
  ::google::protobuf::int64 maturityyield() const;
  void set_maturityyield(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADFIQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr quoteid_;
  ::google::protobuf::internal::ArenaStringPtr quoter_;
  ::google::protobuf::int32 quotebsflag_;
  ::google::protobuf::int32 quotelevel_;
  ::google::protobuf::int64 cleanprice_;
  ::google::protobuf::int64 volume_;
  ::google::protobuf::int64 dirtyprice_;
  ::google::protobuf::int64 maturityyield_;
  ::google::protobuf::int32 quotetime_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDFIQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDFIQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDFIQuote_2eproto();
  friend void protobuf_ShutdownFile_MDFIQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADFIQuote> ADFIQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDFIQuote

// optional string HTSCSecurityID = 1;
inline void MDFIQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
inline void MDFIQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
inline void MDFIQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}
inline ::std::string* MDFIQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDFIQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MDDate)
  return mddate_;
}
inline void MDFIQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDFIQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MDTime)
  return mdtime_;
}
inline void MDFIQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDFIQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDFIQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDFIQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDFIQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDFIQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDFIQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDFIQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDFIQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.securityType)
}

// optional int32 ExchangeDate = 7;
inline void MDFIQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.ExchangeDate)
  return exchangedate_;
}
inline void MDFIQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
inline void MDFIQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.ExchangeTime)
  return exchangetime_;
}
inline void MDFIQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.ExchangeTime)
}

// optional int64 MessageNumber = 9;
inline void MDFIQuote::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.MessageNumber)
  return messagenumber_;
}
inline void MDFIQuote::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.MessageNumber)
}

// optional int32 BrokerDataType = 10;
inline void MDFIQuote::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BrokerDataType)
  return brokerdatatype_;
}
inline void MDFIQuote::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BrokerDataType)
}

// optional int64 AccruedInterest = 11;
inline void MDFIQuote::clear_accruedinterest() {
  accruedinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::accruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.AccruedInterest)
  return accruedinterest_;
}
inline void MDFIQuote::set_accruedinterest(::google::protobuf::int64 value) {
  
  accruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.AccruedInterest)
}

// optional string BuyQuoteID = 20;
inline void MDFIQuote::clear_buyquoteid() {
  buyquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::buyquoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  return buyquoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_buyquoteid(const ::std::string& value) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
inline void MDFIQuote::set_buyquoteid(const char* value) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
inline void MDFIQuote::set_buyquoteid(const char* value, size_t size) {
  
  buyquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}
inline ::std::string* MDFIQuote::mutable_buyquoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  return buyquoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_buyquoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
  
  return buyquoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_buyquoteid(::std::string* buyquoteid) {
  if (buyquoteid != NULL) {
    
  } else {
    
  }
  buyquoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteID)
}

// optional int32 BuyQuoteTime = 21;
inline void MDFIQuote::clear_buyquotetime() {
  buyquotetime_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buyquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteTime)
  return buyquotetime_;
}
inline void MDFIQuote::set_buyquotetime(::google::protobuf::int32 value) {
  
  buyquotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteTime)
}

// optional string BuyQuoter = 22;
inline void MDFIQuote::clear_buyquoter() {
  buyquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::buyquoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  return buyquoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_buyquoter(const ::std::string& value) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
inline void MDFIQuote::set_buyquoter(const char* value) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
inline void MDFIQuote::set_buyquoter(const char* value, size_t size) {
  
  buyquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}
inline ::std::string* MDFIQuote::mutable_buyquoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  return buyquoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_buyquoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
  
  return buyquoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_buyquoter(::std::string* buyquoter) {
  if (buyquoter != NULL) {
    
  } else {
    
  }
  buyquoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoter)
}

// optional int64 BuyCleanPrice = 23;
inline void MDFIQuote::clear_buycleanprice() {
  buycleanprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::buycleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyCleanPrice)
  return buycleanprice_;
}
inline void MDFIQuote::set_buycleanprice(::google::protobuf::int64 value) {
  
  buycleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyCleanPrice)
}

// optional int64 BuyVolume = 24;
inline void MDFIQuote::clear_buyvolume() {
  buyvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::buyvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyVolume)
  return buyvolume_;
}
inline void MDFIQuote::set_buyvolume(::google::protobuf::int64 value) {
  
  buyvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyVolume)
}

// optional int64 BuyDirtyPrice = 25;
inline void MDFIQuote::clear_buydirtyprice() {
  buydirtyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::buydirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyDirtyPrice)
  return buydirtyprice_;
}
inline void MDFIQuote::set_buydirtyprice(::google::protobuf::int64 value) {
  
  buydirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyDirtyPrice)
}

// optional int64 BuyMaturityYield = 26;
inline void MDFIQuote::clear_buymaturityyield() {
  buymaturityyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::buymaturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyMaturityYield)
  return buymaturityyield_;
}
inline void MDFIQuote::set_buymaturityyield(::google::protobuf::int64 value) {
  
  buymaturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyMaturityYield)
}

// optional string BuyQuoteComment = 27;
inline void MDFIQuote::clear_buyquotecomment() {
  buyquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::buyquotecomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  return buyquotecomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_buyquotecomment(const ::std::string& value) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
inline void MDFIQuote::set_buyquotecomment(const char* value) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
inline void MDFIQuote::set_buyquotecomment(const char* value, size_t size) {
  
  buyquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}
inline ::std::string* MDFIQuote::mutable_buyquotecomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  return buyquotecomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_buyquotecomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
  
  return buyquotecomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_buyquotecomment(::std::string* buyquotecomment) {
  if (buyquotecomment != NULL) {
    
  } else {
    
  }
  buyquotecomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buyquotecomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteComment)
}

// optional int32 BuyQuoteType = 28;
inline void MDFIQuote::clear_buyquotetype() {
  buyquotetype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buyquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteType)
  return buyquotetype_;
}
inline void MDFIQuote::set_buyquotetype(::google::protobuf::int32 value) {
  
  buyquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyQuoteType)
}

// optional int32 BuyBargainType = 29;
inline void MDFIQuote::clear_buybargaintype() {
  buybargaintype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buybargaintype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyBargainType)
  return buybargaintype_;
}
inline void MDFIQuote::set_buybargaintype(::google::protobuf::int32 value) {
  
  buybargaintype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyBargainType)
}

// optional int32 BuyRelationType = 30;
inline void MDFIQuote::clear_buyrelationtype() {
  buyrelationtype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buyrelationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyRelationType)
  return buyrelationtype_;
}
inline void MDFIQuote::set_buyrelationtype(::google::protobuf::int32 value) {
  
  buyrelationtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyRelationType)
}

// optional int32 BuyExerciseType = 31;
inline void MDFIQuote::clear_buyexercisetype() {
  buyexercisetype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buyexercisetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyExerciseType)
  return buyexercisetype_;
}
inline void MDFIQuote::set_buyexercisetype(::google::protobuf::int32 value) {
  
  buyexercisetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyExerciseType)
}

// optional int32 BuyYieldType = 32;
inline void MDFIQuote::clear_buyyieldtype() {
  buyyieldtype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::buyyieldtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.BuyYieldType)
  return buyyieldtype_;
}
inline void MDFIQuote::set_buyyieldtype(::google::protobuf::int32 value) {
  
  buyyieldtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.BuyYieldType)
}

// optional string SellQuoteID = 33;
inline void MDFIQuote::clear_sellquoteid() {
  sellquoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::sellquoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  return sellquoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_sellquoteid(const ::std::string& value) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
inline void MDFIQuote::set_sellquoteid(const char* value) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
inline void MDFIQuote::set_sellquoteid(const char* value, size_t size) {
  
  sellquoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}
inline ::std::string* MDFIQuote::mutable_sellquoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  return sellquoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_sellquoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
  
  return sellquoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_sellquoteid(::std::string* sellquoteid) {
  if (sellquoteid != NULL) {
    
  } else {
    
  }
  sellquoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteID)
}

// optional int32 SellQuoteTime = 34;
inline void MDFIQuote::clear_sellquotetime() {
  sellquotetime_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellquotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteTime)
  return sellquotetime_;
}
inline void MDFIQuote::set_sellquotetime(::google::protobuf::int32 value) {
  
  sellquotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteTime)
}

// optional string SellQuoter = 35;
inline void MDFIQuote::clear_sellquoter() {
  sellquoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::sellquoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  return sellquoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_sellquoter(const ::std::string& value) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
inline void MDFIQuote::set_sellquoter(const char* value) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
inline void MDFIQuote::set_sellquoter(const char* value, size_t size) {
  
  sellquoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}
inline ::std::string* MDFIQuote::mutable_sellquoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  return sellquoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_sellquoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
  
  return sellquoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_sellquoter(::std::string* sellquoter) {
  if (sellquoter != NULL) {
    
  } else {
    
  }
  sellquoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoter)
}

// optional int64 SellCleanPrice = 36;
inline void MDFIQuote::clear_sellcleanprice() {
  sellcleanprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::sellcleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellCleanPrice)
  return sellcleanprice_;
}
inline void MDFIQuote::set_sellcleanprice(::google::protobuf::int64 value) {
  
  sellcleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellCleanPrice)
}

// optional int64 SellVolume = 37;
inline void MDFIQuote::clear_sellvolume() {
  sellvolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::sellvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellVolume)
  return sellvolume_;
}
inline void MDFIQuote::set_sellvolume(::google::protobuf::int64 value) {
  
  sellvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellVolume)
}

// optional int64 SellDirtyPrice = 38;
inline void MDFIQuote::clear_selldirtyprice() {
  selldirtyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::selldirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellDirtyPrice)
  return selldirtyprice_;
}
inline void MDFIQuote::set_selldirtyprice(::google::protobuf::int64 value) {
  
  selldirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellDirtyPrice)
}

// optional int64 SellMaturityYield = 39;
inline void MDFIQuote::clear_sellmaturityyield() {
  sellmaturityyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFIQuote::sellmaturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellMaturityYield)
  return sellmaturityyield_;
}
inline void MDFIQuote::set_sellmaturityyield(::google::protobuf::int64 value) {
  
  sellmaturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellMaturityYield)
}

// optional string SellQuoteComment = 40;
inline void MDFIQuote::clear_sellquotecomment() {
  sellquotecomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFIQuote::sellquotecomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  return sellquotecomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_sellquotecomment(const ::std::string& value) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
inline void MDFIQuote::set_sellquotecomment(const char* value) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
inline void MDFIQuote::set_sellquotecomment(const char* value, size_t size) {
  
  sellquotecomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}
inline ::std::string* MDFIQuote::mutable_sellquotecomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  return sellquotecomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFIQuote::release_sellquotecomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
  
  return sellquotecomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFIQuote::set_allocated_sellquotecomment(::std::string* sellquotecomment) {
  if (sellquotecomment != NULL) {
    
  } else {
    
  }
  sellquotecomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellquotecomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteComment)
}

// optional int32 SellQuoteType = 41;
inline void MDFIQuote::clear_sellquotetype() {
  sellquotetype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteType)
  return sellquotetype_;
}
inline void MDFIQuote::set_sellquotetype(::google::protobuf::int32 value) {
  
  sellquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellQuoteType)
}

// optional int32 SellBargainType = 42;
inline void MDFIQuote::clear_sellbargaintype() {
  sellbargaintype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellbargaintype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellBargainType)
  return sellbargaintype_;
}
inline void MDFIQuote::set_sellbargaintype(::google::protobuf::int32 value) {
  
  sellbargaintype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellBargainType)
}

// optional int32 SellRelationType = 43;
inline void MDFIQuote::clear_sellrelationtype() {
  sellrelationtype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellrelationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellRelationType)
  return sellrelationtype_;
}
inline void MDFIQuote::set_sellrelationtype(::google::protobuf::int32 value) {
  
  sellrelationtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellRelationType)
}

// optional int32 SellExerciseType = 44;
inline void MDFIQuote::clear_sellexercisetype() {
  sellexercisetype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellexercisetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellExerciseType)
  return sellexercisetype_;
}
inline void MDFIQuote::set_sellexercisetype(::google::protobuf::int32 value) {
  
  sellexercisetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellExerciseType)
}

// optional int32 SellYieldType = 45;
inline void MDFIQuote::clear_sellyieldtype() {
  sellyieldtype_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::sellyieldtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.SellYieldType)
  return sellyieldtype_;
}
inline void MDFIQuote::set_sellyieldtype(::google::protobuf::int32 value) {
  
  sellyieldtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.SellYieldType)
}

// optional int32 DataMultiplePowerOf10 = 46;
inline void MDFIQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDFIQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDFIQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFIQuote.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADFIQuote Quotes = 47;
inline int MDFIQuote::quotes_size() const {
  return quotes_.size();
}
inline void MDFIQuote::clear_quotes() {
  quotes_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADFIQuote& MDFIQuote::quotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADFIQuote* MDFIQuote::mutable_quotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADFIQuote* MDFIQuote::add_quotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >*
MDFIQuote::mutable_quotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return &quotes_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADFIQuote >&
MDFIQuote::quotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFIQuote.Quotes)
  return quotes_;
}

inline const MDFIQuote* MDFIQuote::internal_default_instance() {
  return &MDFIQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// ADFIQuote

// optional int32 QuoteBSFlag = 1;
inline void ADFIQuote::clear_quotebsflag() {
  quotebsflag_ = 0;
}
inline ::google::protobuf::int32 ADFIQuote::quotebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteBSFlag)
  return quotebsflag_;
}
inline void ADFIQuote::set_quotebsflag(::google::protobuf::int32 value) {
  
  quotebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteBSFlag)
}

// optional int32 QuoteLevel = 2;
inline void ADFIQuote::clear_quotelevel() {
  quotelevel_ = 0;
}
inline ::google::protobuf::int32 ADFIQuote::quotelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteLevel)
  return quotelevel_;
}
inline void ADFIQuote::set_quotelevel(::google::protobuf::int32 value) {
  
  quotelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteLevel)
}

// optional string QuoteID = 3;
inline void ADFIQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADFIQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADFIQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
inline void ADFIQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
inline void ADFIQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}
inline ::std::string* ADFIQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADFIQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADFIQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADFIQuote.QuoteID)
}

// optional int32 QuoteTime = 4;
inline void ADFIQuote::clear_quotetime() {
  quotetime_ = 0;
}
inline ::google::protobuf::int32 ADFIQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.QuoteTime)
  return quotetime_;
}
inline void ADFIQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.QuoteTime)
}

// optional string Quoter = 5;
inline void ADFIQuote::clear_quoter() {
  quoter_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADFIQuote::quoter() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  return quoter_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADFIQuote::set_quoter(const ::std::string& value) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
inline void ADFIQuote::set_quoter(const char* value) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
inline void ADFIQuote::set_quoter(const char* value, size_t size) {
  
  quoter_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}
inline ::std::string* ADFIQuote::mutable_quoter() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  return quoter_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADFIQuote::release_quoter() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
  
  return quoter_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADFIQuote::set_allocated_quoter(::std::string* quoter) {
  if (quoter != NULL) {
    
  } else {
    
  }
  quoter_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoter);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADFIQuote.Quoter)
}

// optional int64 CleanPrice = 6;
inline void ADFIQuote::clear_cleanprice() {
  cleanprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADFIQuote::cleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.CleanPrice)
  return cleanprice_;
}
inline void ADFIQuote::set_cleanprice(::google::protobuf::int64 value) {
  
  cleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.CleanPrice)
}

// optional int64 Volume = 7;
inline void ADFIQuote::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADFIQuote::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.Volume)
  return volume_;
}
inline void ADFIQuote::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.Volume)
}

// optional int64 DirtyPrice = 8;
inline void ADFIQuote::clear_dirtyprice() {
  dirtyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADFIQuote::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.DirtyPrice)
  return dirtyprice_;
}
inline void ADFIQuote::set_dirtyprice(::google::protobuf::int64 value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.DirtyPrice)
}

// optional int64 MaturityYield = 9;
inline void ADFIQuote::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADFIQuote::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADFIQuote.MaturityYield)
  return maturityyield_;
}
inline void ADFIQuote::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADFIQuote.MaturityYield)
}

inline const ADFIQuote* ADFIQuote::internal_default_instance() {
  return &ADFIQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDFIQuote_2eproto__INCLUDED
