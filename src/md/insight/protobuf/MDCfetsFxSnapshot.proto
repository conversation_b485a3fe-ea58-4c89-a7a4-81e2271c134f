syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsFxSnapshot {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  FxSnapshotDetail FxSnapshotDetail = 16;
  int64 MessageNumber = 100;
}

message FxSnapshotDetail {
  string CurrencyPair = 1;
  double BidPrice = 2;
  double AskPrice = 3;
  double MidPrice = 4;
  double HighPrice = 5;
  double LowPrice = 6;
  double LastPrice = 7;
  double Volume = 8;
}
