// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQBQuote.proto

#ifndef PROTOBUF_MDQBQuote_2eproto__INCLUDED
#define PROTOBUF_MDQBQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDQBQuote_2eproto();
void protobuf_InitDefaults_MDQBQuote_2eproto();
void protobuf_AssignDesc_MDQBQuote_2eproto();
void protobuf_ShutdownFile_MDQBQuote_2eproto();

class MDQBQuote;

// ===================================================================

class MDQBQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDQBQuote) */ {
 public:
  MDQBQuote();
  virtual ~MDQBQuote();

  MDQBQuote(const MDQBQuote& from);

  inline MDQBQuote& operator=(const MDQBQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDQBQuote& default_instance();

  static const MDQBQuote* internal_default_instance();

  void Swap(MDQBQuote* other);

  // implements Message ----------------------------------------------

  inline MDQBQuote* New() const { return New(NULL); }

  MDQBQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDQBQuote& from);
  void MergeFrom(const MDQBQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDQBQuote* other);
  void UnsafeMergeFrom(const MDQBQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 8;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 8;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 9;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 9;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 10;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 10;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int64 ApplSeqNum = 11;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 11;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // optional int64 BidPx = 12;
  void clear_bidpx();
  static const int kBidPxFieldNumber = 12;
  ::google::protobuf::int64 bidpx() const;
  void set_bidpx(::google::protobuf::int64 value);

  // optional int64 BidSize = 13;
  void clear_bidsize();
  static const int kBidSizeFieldNumber = 13;
  ::google::protobuf::int64 bidsize() const;
  void set_bidsize(::google::protobuf::int64 value);

  // optional int64 BidYield = 14;
  void clear_bidyield();
  static const int kBidYieldFieldNumber = 14;
  ::google::protobuf::int64 bidyield() const;
  void set_bidyield(::google::protobuf::int64 value);

  // optional int64 BidNetPrice = 15;
  void clear_bidnetprice();
  static const int kBidNetPriceFieldNumber = 15;
  ::google::protobuf::int64 bidnetprice() const;
  void set_bidnetprice(::google::protobuf::int64 value);

  // optional int32 BidBargainFlag = 16;
  void clear_bidbargainflag();
  static const int kBidBargainFlagFieldNumber = 16;
  ::google::protobuf::int32 bidbargainflag() const;
  void set_bidbargainflag(::google::protobuf::int32 value);

  // optional int32 BidRelationFlag = 17;
  void clear_bidrelationflag();
  static const int kBidRelationFlagFieldNumber = 17;
  ::google::protobuf::int32 bidrelationflag() const;
  void set_bidrelationflag(::google::protobuf::int32 value);

  // optional string BidComment = 18;
  void clear_bidcomment();
  static const int kBidCommentFieldNumber = 18;
  const ::std::string& bidcomment() const;
  void set_bidcomment(const ::std::string& value);
  void set_bidcomment(const char* value);
  void set_bidcomment(const char* value, size_t size);
  ::std::string* mutable_bidcomment();
  ::std::string* release_bidcomment();
  void set_allocated_bidcomment(::std::string* bidcomment);

  // optional string BidID = 19;
  void clear_bidid();
  static const int kBidIDFieldNumber = 19;
  const ::std::string& bidid() const;
  void set_bidid(const ::std::string& value);
  void set_bidid(const char* value);
  void set_bidid(const char* value, size_t size);
  ::std::string* mutable_bidid();
  ::std::string* release_bidid();
  void set_allocated_bidid(::std::string* bidid);

  // optional int32 BidSsDetect = 20;
  void clear_bidssdetect();
  static const int kBidSsDetectFieldNumber = 20;
  ::google::protobuf::int32 bidssdetect() const;
  void set_bidssdetect(::google::protobuf::int32 value);

  // optional int64 OfferPx = 21;
  void clear_offerpx();
  static const int kOfferPxFieldNumber = 21;
  ::google::protobuf::int64 offerpx() const;
  void set_offerpx(::google::protobuf::int64 value);

  // optional int64 OfferSize = 22;
  void clear_offersize();
  static const int kOfferSizeFieldNumber = 22;
  ::google::protobuf::int64 offersize() const;
  void set_offersize(::google::protobuf::int64 value);

  // optional int64 OfferYield = 23;
  void clear_offeryield();
  static const int kOfferYieldFieldNumber = 23;
  ::google::protobuf::int64 offeryield() const;
  void set_offeryield(::google::protobuf::int64 value);

  // optional int64 OfferNetPrice = 24;
  void clear_offernetprice();
  static const int kOfferNetPriceFieldNumber = 24;
  ::google::protobuf::int64 offernetprice() const;
  void set_offernetprice(::google::protobuf::int64 value);

  // optional int32 OfferBargainFlag = 25;
  void clear_offerbargainflag();
  static const int kOfferBargainFlagFieldNumber = 25;
  ::google::protobuf::int32 offerbargainflag() const;
  void set_offerbargainflag(::google::protobuf::int32 value);

  // optional int32 OfferRelationFlag = 26;
  void clear_offerrelationflag();
  static const int kOfferRelationFlagFieldNumber = 26;
  ::google::protobuf::int32 offerrelationflag() const;
  void set_offerrelationflag(::google::protobuf::int32 value);

  // optional string OfferComment = 27;
  void clear_offercomment();
  static const int kOfferCommentFieldNumber = 27;
  const ::std::string& offercomment() const;
  void set_offercomment(const ::std::string& value);
  void set_offercomment(const char* value);
  void set_offercomment(const char* value, size_t size);
  ::std::string* mutable_offercomment();
  ::std::string* release_offercomment();
  void set_allocated_offercomment(::std::string* offercomment);

  // optional string OfferID = 28;
  void clear_offerid();
  static const int kOfferIDFieldNumber = 28;
  const ::std::string& offerid() const;
  void set_offerid(const ::std::string& value);
  void set_offerid(const char* value);
  void set_offerid(const char* value, size_t size);
  ::std::string* mutable_offerid();
  ::std::string* release_offerid();
  void set_allocated_offerid(::std::string* offerid);

  // optional int32 OfferSsDetect = 29;
  void clear_offerssdetect();
  static const int kOfferSsDetectFieldNumber = 29;
  ::google::protobuf::int32 offerssdetect() const;
  void set_offerssdetect(::google::protobuf::int32 value);

  // optional int32 BrokerDataType = 30;
  void clear_brokerdatatype();
  static const int kBrokerDataTypeFieldNumber = 30;
  ::google::protobuf::int32 brokerdatatype() const;
  void set_brokerdatatype(::google::protobuf::int32 value);

  // optional int32 BidExerciseFlag = 31;
  void clear_bidexerciseflag();
  static const int kBidExerciseFlagFieldNumber = 31;
  ::google::protobuf::int32 bidexerciseflag() const;
  void set_bidexerciseflag(::google::protobuf::int32 value);

  // optional int32 OfrExerciseFlag = 32;
  void clear_ofrexerciseflag();
  static const int kOfrExerciseFlagFieldNumber = 32;
  ::google::protobuf::int32 ofrexerciseflag() const;
  void set_ofrexerciseflag(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 33;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 33;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 BidFullPrice = 34;
  void clear_bidfullprice();
  static const int kBidFullPriceFieldNumber = 34;
  ::google::protobuf::int64 bidfullprice() const;
  void set_bidfullprice(::google::protobuf::int64 value);

  // optional int64 OfrFullPrice = 35;
  void clear_ofrfullprice();
  static const int kOfrFullPriceFieldNumber = 35;
  ::google::protobuf::int64 ofrfullprice() const;
  void set_ofrfullprice(::google::protobuf::int64 value);

  // optional int32 BidPriceType = 36;
  void clear_bidpricetype();
  static const int kBidPriceTypeFieldNumber = 36;
  ::google::protobuf::int32 bidpricetype() const;
  void set_bidpricetype(::google::protobuf::int32 value);

  // optional int32 OfrPriceType = 37;
  void clear_ofrpricetype();
  static const int kOfrPriceTypeFieldNumber = 37;
  ::google::protobuf::int32 ofrpricetype() const;
  void set_ofrpricetype(::google::protobuf::int32 value);

  // optional string BidSettlType = 38;
  void clear_bidsettltype();
  static const int kBidSettlTypeFieldNumber = 38;
  const ::std::string& bidsettltype() const;
  void set_bidsettltype(const ::std::string& value);
  void set_bidsettltype(const char* value);
  void set_bidsettltype(const char* value, size_t size);
  ::std::string* mutable_bidsettltype();
  ::std::string* release_bidsettltype();
  void set_allocated_bidsettltype(::std::string* bidsettltype);

  // optional string OfrSettlType = 39;
  void clear_ofrsettltype();
  static const int kOfrSettlTypeFieldNumber = 39;
  const ::std::string& ofrsettltype() const;
  void set_ofrsettltype(const ::std::string& value);
  void set_ofrsettltype(const char* value);
  void set_ofrsettltype(const char* value, size_t size);
  ::std::string* mutable_ofrsettltype();
  ::std::string* release_ofrsettltype();
  void set_allocated_ofrsettltype(::std::string* ofrsettltype);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDQBQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr bidcomment_;
  ::google::protobuf::internal::ArenaStringPtr bidid_;
  ::google::protobuf::internal::ArenaStringPtr offercomment_;
  ::google::protobuf::internal::ArenaStringPtr offerid_;
  ::google::protobuf::internal::ArenaStringPtr bidsettltype_;
  ::google::protobuf::internal::ArenaStringPtr ofrsettltype_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int64 bidpx_;
  ::google::protobuf::int64 bidsize_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 bidbargainflag_;
  ::google::protobuf::int64 bidyield_;
  ::google::protobuf::int64 bidnetprice_;
  ::google::protobuf::int32 bidrelationflag_;
  ::google::protobuf::int32 bidssdetect_;
  ::google::protobuf::int64 offerpx_;
  ::google::protobuf::int64 offersize_;
  ::google::protobuf::int64 offeryield_;
  ::google::protobuf::int64 offernetprice_;
  ::google::protobuf::int32 offerbargainflag_;
  ::google::protobuf::int32 offerrelationflag_;
  ::google::protobuf::int32 offerssdetect_;
  ::google::protobuf::int32 brokerdatatype_;
  ::google::protobuf::int32 bidexerciseflag_;
  ::google::protobuf::int32 ofrexerciseflag_;
  ::google::protobuf::int64 bidfullprice_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 bidpricetype_;
  ::google::protobuf::int64 ofrfullprice_;
  ::google::protobuf::int32 ofrpricetype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQBQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQBQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQBQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQBQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDQBQuote> MDQBQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQBQuote

// optional string HTSCSecurityID = 1;
inline void MDQBQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
inline void MDQBQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
inline void MDQBQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
inline ::std::string* MDQBQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDQBQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.MDDate)
  return mddate_;
}
inline void MDQBQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDQBQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.MDTime)
  return mdtime_;
}
inline void MDQBQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDQBQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDQBQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDQBQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
inline void MDQBQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
inline void MDQBQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
inline ::std::string* MDQBQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDQBQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDQBQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDQBQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDQBQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDQBQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDQBQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.securityType)
}

// optional int32 ExchangeDate = 8;
inline void MDQBQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ExchangeDate)
  return exchangedate_;
}
inline void MDQBQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
inline void MDQBQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ExchangeTime)
  return exchangetime_;
}
inline void MDQBQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ExchangeTime)
}

// optional int32 ChannelNo = 10;
inline void MDQBQuote::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ChannelNo)
  return channelno_;
}
inline void MDQBQuote::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ChannelNo)
}

// optional int64 ApplSeqNum = 11;
inline void MDQBQuote::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ApplSeqNum)
  return applseqnum_;
}
inline void MDQBQuote::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ApplSeqNum)
}

// optional int64 BidPx = 12;
inline void MDQBQuote::clear_bidpx() {
  bidpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::bidpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidPx)
  return bidpx_;
}
inline void MDQBQuote::set_bidpx(::google::protobuf::int64 value) {
  
  bidpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidPx)
}

// optional int64 BidSize = 13;
inline void MDQBQuote::clear_bidsize() {
  bidsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::bidsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSize)
  return bidsize_;
}
inline void MDQBQuote::set_bidsize(::google::protobuf::int64 value) {
  
  bidsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSize)
}

// optional int64 BidYield = 14;
inline void MDQBQuote::clear_bidyield() {
  bidyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::bidyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidYield)
  return bidyield_;
}
inline void MDQBQuote::set_bidyield(::google::protobuf::int64 value) {
  
  bidyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidYield)
}

// optional int64 BidNetPrice = 15;
inline void MDQBQuote::clear_bidnetprice() {
  bidnetprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::bidnetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidNetPrice)
  return bidnetprice_;
}
inline void MDQBQuote::set_bidnetprice(::google::protobuf::int64 value) {
  
  bidnetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidNetPrice)
}

// optional int32 BidBargainFlag = 16;
inline void MDQBQuote::clear_bidbargainflag() {
  bidbargainflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::bidbargainflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidBargainFlag)
  return bidbargainflag_;
}
inline void MDQBQuote::set_bidbargainflag(::google::protobuf::int32 value) {
  
  bidbargainflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidBargainFlag)
}

// optional int32 BidRelationFlag = 17;
inline void MDQBQuote::clear_bidrelationflag() {
  bidrelationflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::bidrelationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidRelationFlag)
  return bidrelationflag_;
}
inline void MDQBQuote::set_bidrelationflag(::google::protobuf::int32 value) {
  
  bidrelationflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidRelationFlag)
}

// optional string BidComment = 18;
inline void MDQBQuote::clear_bidcomment() {
  bidcomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::bidcomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  return bidcomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_bidcomment(const ::std::string& value) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
inline void MDQBQuote::set_bidcomment(const char* value) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
inline void MDQBQuote::set_bidcomment(const char* value, size_t size) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
inline ::std::string* MDQBQuote::mutable_bidcomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  return bidcomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_bidcomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  
  return bidcomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_bidcomment(::std::string* bidcomment) {
  if (bidcomment != NULL) {
    
  } else {
    
  }
  bidcomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidcomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}

// optional string BidID = 19;
inline void MDQBQuote::clear_bidid() {
  bidid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::bidid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  return bidid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_bidid(const ::std::string& value) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
inline void MDQBQuote::set_bidid(const char* value) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
inline void MDQBQuote::set_bidid(const char* value, size_t size) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
inline ::std::string* MDQBQuote::mutable_bidid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  return bidid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_bidid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  
  return bidid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_bidid(::std::string* bidid) {
  if (bidid != NULL) {
    
  } else {
    
  }
  bidid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}

// optional int32 BidSsDetect = 20;
inline void MDQBQuote::clear_bidssdetect() {
  bidssdetect_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::bidssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSsDetect)
  return bidssdetect_;
}
inline void MDQBQuote::set_bidssdetect(::google::protobuf::int32 value) {
  
  bidssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSsDetect)
}

// optional int64 OfferPx = 21;
inline void MDQBQuote::clear_offerpx() {
  offerpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::offerpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferPx)
  return offerpx_;
}
inline void MDQBQuote::set_offerpx(::google::protobuf::int64 value) {
  
  offerpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferPx)
}

// optional int64 OfferSize = 22;
inline void MDQBQuote::clear_offersize() {
  offersize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::offersize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferSize)
  return offersize_;
}
inline void MDQBQuote::set_offersize(::google::protobuf::int64 value) {
  
  offersize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferSize)
}

// optional int64 OfferYield = 23;
inline void MDQBQuote::clear_offeryield() {
  offeryield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::offeryield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferYield)
  return offeryield_;
}
inline void MDQBQuote::set_offeryield(::google::protobuf::int64 value) {
  
  offeryield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferYield)
}

// optional int64 OfferNetPrice = 24;
inline void MDQBQuote::clear_offernetprice() {
  offernetprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::offernetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferNetPrice)
  return offernetprice_;
}
inline void MDQBQuote::set_offernetprice(::google::protobuf::int64 value) {
  
  offernetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferNetPrice)
}

// optional int32 OfferBargainFlag = 25;
inline void MDQBQuote::clear_offerbargainflag() {
  offerbargainflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::offerbargainflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferBargainFlag)
  return offerbargainflag_;
}
inline void MDQBQuote::set_offerbargainflag(::google::protobuf::int32 value) {
  
  offerbargainflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferBargainFlag)
}

// optional int32 OfferRelationFlag = 26;
inline void MDQBQuote::clear_offerrelationflag() {
  offerrelationflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::offerrelationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferRelationFlag)
  return offerrelationflag_;
}
inline void MDQBQuote::set_offerrelationflag(::google::protobuf::int32 value) {
  
  offerrelationflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferRelationFlag)
}

// optional string OfferComment = 27;
inline void MDQBQuote::clear_offercomment() {
  offercomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::offercomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  return offercomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_offercomment(const ::std::string& value) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
inline void MDQBQuote::set_offercomment(const char* value) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
inline void MDQBQuote::set_offercomment(const char* value, size_t size) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
inline ::std::string* MDQBQuote::mutable_offercomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  return offercomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_offercomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  
  return offercomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_offercomment(::std::string* offercomment) {
  if (offercomment != NULL) {
    
  } else {
    
  }
  offercomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offercomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}

// optional string OfferID = 28;
inline void MDQBQuote::clear_offerid() {
  offerid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::offerid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  return offerid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_offerid(const ::std::string& value) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
inline void MDQBQuote::set_offerid(const char* value) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
inline void MDQBQuote::set_offerid(const char* value, size_t size) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
inline ::std::string* MDQBQuote::mutable_offerid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  return offerid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_offerid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  
  return offerid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_offerid(::std::string* offerid) {
  if (offerid != NULL) {
    
  } else {
    
  }
  offerid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offerid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}

// optional int32 OfferSsDetect = 29;
inline void MDQBQuote::clear_offerssdetect() {
  offerssdetect_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::offerssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferSsDetect)
  return offerssdetect_;
}
inline void MDQBQuote::set_offerssdetect(::google::protobuf::int32 value) {
  
  offerssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferSsDetect)
}

// optional int32 BrokerDataType = 30;
inline void MDQBQuote::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BrokerDataType)
  return brokerdatatype_;
}
inline void MDQBQuote::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BrokerDataType)
}

// optional int32 BidExerciseFlag = 31;
inline void MDQBQuote::clear_bidexerciseflag() {
  bidexerciseflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::bidexerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidExerciseFlag)
  return bidexerciseflag_;
}
inline void MDQBQuote::set_bidexerciseflag(::google::protobuf::int32 value) {
  
  bidexerciseflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidExerciseFlag)
}

// optional int32 OfrExerciseFlag = 32;
inline void MDQBQuote::clear_ofrexerciseflag() {
  ofrexerciseflag_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::ofrexerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrExerciseFlag)
  return ofrexerciseflag_;
}
inline void MDQBQuote::set_ofrexerciseflag(::google::protobuf::int32 value) {
  
  ofrexerciseflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrExerciseFlag)
}

// optional int32 DataMultiplePowerOf10 = 33;
inline void MDQBQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDQBQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.DataMultiplePowerOf10)
}

// optional int64 BidFullPrice = 34;
inline void MDQBQuote::clear_bidfullprice() {
  bidfullprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::bidfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidFullPrice)
  return bidfullprice_;
}
inline void MDQBQuote::set_bidfullprice(::google::protobuf::int64 value) {
  
  bidfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidFullPrice)
}

// optional int64 OfrFullPrice = 35;
inline void MDQBQuote::clear_ofrfullprice() {
  ofrfullprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBQuote::ofrfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrFullPrice)
  return ofrfullprice_;
}
inline void MDQBQuote::set_ofrfullprice(::google::protobuf::int64 value) {
  
  ofrfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrFullPrice)
}

// optional int32 BidPriceType = 36;
inline void MDQBQuote::clear_bidpricetype() {
  bidpricetype_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::bidpricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidPriceType)
  return bidpricetype_;
}
inline void MDQBQuote::set_bidpricetype(::google::protobuf::int32 value) {
  
  bidpricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidPriceType)
}

// optional int32 OfrPriceType = 37;
inline void MDQBQuote::clear_ofrpricetype() {
  ofrpricetype_ = 0;
}
inline ::google::protobuf::int32 MDQBQuote::ofrpricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrPriceType)
  return ofrpricetype_;
}
inline void MDQBQuote::set_ofrpricetype(::google::protobuf::int32 value) {
  
  ofrpricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrPriceType)
}

// optional string BidSettlType = 38;
inline void MDQBQuote::clear_bidsettltype() {
  bidsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::bidsettltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  return bidsettltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_bidsettltype(const ::std::string& value) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
inline void MDQBQuote::set_bidsettltype(const char* value) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
inline void MDQBQuote::set_bidsettltype(const char* value, size_t size) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
inline ::std::string* MDQBQuote::mutable_bidsettltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  return bidsettltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_bidsettltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  
  return bidsettltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_bidsettltype(::std::string* bidsettltype) {
  if (bidsettltype != NULL) {
    
  } else {
    
  }
  bidsettltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidsettltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}

// optional string OfrSettlType = 39;
inline void MDQBQuote::clear_ofrsettltype() {
  ofrsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBQuote::ofrsettltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  return ofrsettltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_ofrsettltype(const ::std::string& value) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
inline void MDQBQuote::set_ofrsettltype(const char* value) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
inline void MDQBQuote::set_ofrsettltype(const char* value, size_t size) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
inline ::std::string* MDQBQuote::mutable_ofrsettltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  return ofrsettltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBQuote::release_ofrsettltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  
  return ofrsettltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBQuote::set_allocated_ofrsettltype(::std::string* ofrsettltype) {
  if (ofrsettltype != NULL) {
    
  } else {
    
  }
  ofrsettltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ofrsettltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}

inline const MDQBQuote* MDQBQuote::internal_default_instance() {
  return &MDQBQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDQBQuote_2eproto__INCLUDED
