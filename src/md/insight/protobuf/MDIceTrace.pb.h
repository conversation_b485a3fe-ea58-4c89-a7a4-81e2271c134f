// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDIceTrace.proto

#ifndef PROTOBUF_MDIceTrace_2eproto__INCLUDED
#define PROTOBUF_MDIceTrace_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDIceTrace_2eproto();
void protobuf_InitDefaults_MDIceTrace_2eproto();
void protobuf_AssignDesc_MDIceTrace_2eproto();
void protobuf_ShutdownFile_MDIceTrace_2eproto();

class MDIceTrace;

// ===================================================================

class MDIceTrace : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDIceTrace) */ {
 public:
  MDIceTrace();
  virtual ~MDIceTrace();

  MDIceTrace(const MDIceTrace& from);

  inline MDIceTrace& operator=(const MDIceTrace& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDIceTrace& default_instance();

  static const MDIceTrace* internal_default_instance();

  void Swap(MDIceTrace* other);

  // implements Message ----------------------------------------------

  inline MDIceTrace* New() const { return New(NULL); }

  MDIceTrace* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDIceTrace& from);
  void MergeFrom(const MDIceTrace& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDIceTrace* other);
  void UnsafeMergeFrom(const MDIceTrace& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 MDDate = 1;
  void clear_mddate();
  static const int kMDDateFieldNumber = 1;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 2;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 2;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 4;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional string HTSCSecurityID = 5;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 5;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 ExchangeDate = 6;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 6;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 7;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 7;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 8;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 8;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional double TradePrice = 19;
  void clear_tradeprice();
  static const int kTradePriceFieldNumber = 19;
  double tradeprice() const;
  void set_tradeprice(double value);

  // optional int64 TradeSize = 20;
  void clear_tradesize();
  static const int kTradeSizeFieldNumber = 20;
  ::google::protobuf::int64 tradesize() const;
  void set_tradesize(::google::protobuf::int64 value);

  // optional double CurrentPrice = 21;
  void clear_currentprice();
  static const int kCurrentPriceFieldNumber = 21;
  double currentprice() const;
  void set_currentprice(double value);

  // optional string ActivityDatetime = 22;
  void clear_activitydatetime();
  static const int kActivityDatetimeFieldNumber = 22;
  const ::std::string& activitydatetime() const;
  void set_activitydatetime(const ::std::string& value);
  void set_activitydatetime(const char* value);
  void set_activitydatetime(const char* value, size_t size);
  ::std::string* mutable_activitydatetime();
  ::std::string* release_activitydatetime();
  void set_allocated_activitydatetime(::std::string* activitydatetime);

  // optional string TradeDatetime = 23;
  void clear_tradedatetime();
  static const int kTradeDatetimeFieldNumber = 23;
  const ::std::string& tradedatetime() const;
  void set_tradedatetime(const ::std::string& value);
  void set_tradedatetime(const char* value);
  void set_tradedatetime(const char* value, size_t size);
  ::std::string* mutable_tradedatetime();
  ::std::string* release_tradedatetime();
  void set_allocated_tradedatetime(::std::string* tradedatetime);

  // optional int64 TradeVol = 24;
  void clear_tradevol();
  static const int kTradeVolFieldNumber = 24;
  ::google::protobuf::int64 tradevol() const;
  void set_tradevol(::google::protobuf::int64 value);

  // optional int64 ExchSeq = 26;
  void clear_exchseq();
  static const int kExchSeqFieldNumber = 26;
  ::google::protobuf::int64 exchseq() const;
  void set_exchseq(::google::protobuf::int64 value);

  // optional double TradeIndicSize = 27;
  void clear_tradeindicsize();
  static const int kTradeIndicSizeFieldNumber = 27;
  double tradeindicsize() const;
  void set_tradeindicsize(double value);

  // optional int64 ExchMessageTimestamp = 28;
  void clear_exchmessagetimestamp();
  static const int kExchMessageTimestampFieldNumber = 28;
  ::google::protobuf::int64 exchmessagetimestamp() const;
  void set_exchmessagetimestamp(::google::protobuf::int64 value);

  // optional double CorrectionNewTradePrice = 30;
  void clear_correctionnewtradeprice();
  static const int kCorrectionNewTradePriceFieldNumber = 30;
  double correctionnewtradeprice() const;
  void set_correctionnewtradeprice(double value);

  // optional int64 CorrectionNewTradeSize = 31;
  void clear_correctionnewtradesize();
  static const int kCorrectionNewTradeSizeFieldNumber = 31;
  ::google::protobuf::int64 correctionnewtradesize() const;
  void set_correctionnewtradesize(::google::protobuf::int64 value);

  // optional double CorrectionPrevTradePrice = 32;
  void clear_correctionprevtradeprice();
  static const int kCorrectionPrevTradePriceFieldNumber = 32;
  double correctionprevtradeprice() const;
  void set_correctionprevtradeprice(double value);

  // optional int64 CorrectionPrevTradeSize = 33;
  void clear_correctionprevtradesize();
  static const int kCorrectionPrevTradeSizeFieldNumber = 33;
  ::google::protobuf::int64 correctionprevtradesize() const;
  void set_correctionprevtradesize(::google::protobuf::int64 value);

  // optional string EnumCurrency = 34;
  void clear_enumcurrency();
  static const int kEnumCurrencyFieldNumber = 34;
  const ::std::string& enumcurrency() const;
  void set_enumcurrency(const ::std::string& value);
  void set_enumcurrency(const char* value);
  void set_enumcurrency(const char* value, size_t size);
  ::std::string* mutable_enumcurrency();
  ::std::string* release_enumcurrency();
  void set_allocated_enumcurrency(::std::string* enumcurrency);

  // optional int64 MaturityDate = 35;
  void clear_maturitydate();
  static const int kMaturityDateFieldNumber = 35;
  ::google::protobuf::int64 maturitydate() const;
  void set_maturitydate(::google::protobuf::int64 value);

  // optional int32 MarketPhaseCode = 36;
  void clear_marketphasecode();
  static const int kMarketPhaseCodeFieldNumber = 36;
  ::google::protobuf::int32 marketphasecode() const;
  void set_marketphasecode(::google::protobuf::int32 value);

  // optional double Chg = 37;
  void clear_chg();
  static const int kChgFieldNumber = 37;
  double chg() const;
  void set_chg(double value);

  // optional double PctChg = 38;
  void clear_pctchg();
  static const int kPctChgFieldNumber = 38;
  double pctchg() const;
  void set_pctchg(double value);

  // optional double ClosePx = 39;
  void clear_closepx();
  static const int kClosePxFieldNumber = 39;
  double closepx() const;
  void set_closepx(double value);

  // optional double HighPx = 40;
  void clear_highpx();
  static const int kHighPxFieldNumber = 40;
  double highpx() const;
  void set_highpx(double value);

  // optional double LowPx = 41;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 41;
  double lowpx() const;
  void set_lowpx(double value);

  // optional double OpenPx = 42;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 42;
  double openpx() const;
  void set_openpx(double value);

  // optional double PreClosePx = 43;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 43;
  double preclosepx() const;
  void set_preclosepx(double value);

  // optional bool RecordDel = 45;
  void clear_recorddel();
  static const int kRecordDelFieldNumber = 45;
  bool recorddel() const;
  void set_recorddel(bool value);

  // optional string CurrencyString = 46;
  void clear_currencystring();
  static const int kCurrencyStringFieldNumber = 46;
  const ::std::string& currencystring() const;
  void set_currencystring(const ::std::string& value);
  void set_currencystring(const char* value);
  void set_currencystring(const char* value, size_t size);
  ::std::string* mutable_currencystring();
  ::std::string* release_currencystring();
  void set_allocated_currencystring(::std::string* currencystring);

  // optional string CurrentDatetime = 47;
  void clear_currentdatetime();
  static const int kCurrentDatetimeFieldNumber = 47;
  const ::std::string& currentdatetime() const;
  void set_currentdatetime(const ::std::string& value);
  void set_currentdatetime(const char* value);
  void set_currentdatetime(const char* value, size_t size);
  ::std::string* mutable_currentdatetime();
  ::std::string* release_currentdatetime();
  void set_allocated_currentdatetime(::std::string* currentdatetime);

  // optional int64 TradeOfficialDate = 48;
  void clear_tradeofficialdate();
  static const int kTradeOfficialDateFieldNumber = 48;
  ::google::protobuf::int64 tradeofficialdate() const;
  void set_tradeofficialdate(::google::protobuf::int64 value);

  // optional double TradeCondPrice = 49;
  void clear_tradecondprice();
  static const int kTradeCondPriceFieldNumber = 49;
  double tradecondprice() const;
  void set_tradecondprice(double value);

  // optional int64 TradeCondSize = 50;
  void clear_tradecondsize();
  static const int kTradeCondSizeFieldNumber = 50;
  ::google::protobuf::int64 tradecondsize() const;
  void set_tradecondsize(::google::protobuf::int64 value);

  // optional int64 TradeOfficialTime = 51;
  void clear_tradeofficialtime();
  static const int kTradeOfficialTimeFieldNumber = 51;
  ::google::protobuf::int64 tradeofficialtime() const;
  void set_tradeofficialtime(::google::protobuf::int64 value);

  // optional int64 TradeUniqueId = 52;
  void clear_tradeuniqueid();
  static const int kTradeUniqueIdFieldNumber = 52;
  ::google::protobuf::int64 tradeuniqueid() const;
  void set_tradeuniqueid(::google::protobuf::int64 value);

  // optional int64 UpdateAction = 53;
  void clear_updateaction();
  static const int kUpdateActionFieldNumber = 53;
  ::google::protobuf::int64 updateaction() const;
  void set_updateaction(::google::protobuf::int64 value);

  // optional int64 YestTradeVol = 54;
  void clear_yesttradevol();
  static const int kYestTradeVolFieldNumber = 54;
  ::google::protobuf::int64 yesttradevol() const;
  void set_yesttradevol(::google::protobuf::int64 value);

  // optional double Vwap = 55;
  void clear_vwap();
  static const int kVwapFieldNumber = 55;
  double vwap() const;
  void set_vwap(double value);

  // optional int64 TradeCondOfficialTime = 56;
  void clear_tradecondofficialtime();
  static const int kTradeCondOfficialTimeFieldNumber = 56;
  ::google::protobuf::int64 tradecondofficialtime() const;
  void set_tradecondofficialtime(::google::protobuf::int64 value);

  // optional int64 TradeCondOfficialDate = 57;
  void clear_tradecondofficialdate();
  static const int kTradeCondOfficialDateFieldNumber = 57;
  ::google::protobuf::int64 tradecondofficialdate() const;
  void set_tradecondofficialdate(::google::protobuf::int64 value);

  // optional int64 CorrectionOfficialTradeTime = 58;
  void clear_correctionofficialtradetime();
  static const int kCorrectionOfficialTradeTimeFieldNumber = 58;
  ::google::protobuf::int64 correctionofficialtradetime() const;
  void set_correctionofficialtradetime(::google::protobuf::int64 value);

  // optional int64 CorrectionOfficialTradeDate = 59;
  void clear_correctionofficialtradedate();
  static const int kCorrectionOfficialTradeDateFieldNumber = 59;
  ::google::protobuf::int64 correctionofficialtradedate() const;
  void set_correctionofficialtradedate(::google::protobuf::int64 value);

  // optional int32 TransactionPriceInd = 60;
  void clear_transactionpriceind();
  static const int kTransactionPriceIndFieldNumber = 60;
  ::google::protobuf::int32 transactionpriceind() const;
  void set_transactionpriceind(::google::protobuf::int32 value);

  // optional double CouponRate = 61;
  void clear_couponrate();
  static const int kCouponRateFieldNumber = 61;
  double couponrate() const;
  void set_couponrate(double value);

  // optional int32 InstrStatus = 62;
  void clear_instrstatus();
  static const int kInstrStatusFieldNumber = 62;
  ::google::protobuf::int32 instrstatus() const;
  void set_instrstatus(::google::protobuf::int32 value);

  // optional int32 MsgType = 63;
  void clear_msgtype();
  static const int kMsgTypeFieldNumber = 63;
  ::google::protobuf::int32 msgtype() const;
  void set_msgtype(::google::protobuf::int32 value);

  // optional int32 ControlMsgType = 64;
  void clear_controlmsgtype();
  static const int kControlMsgTypeFieldNumber = 64;
  ::google::protobuf::int32 controlmsgtype() const;
  void set_controlmsgtype(::google::protobuf::int32 value);

  // optional int64 WeightedAverageMaturity = 65;
  void clear_weightedaveragematurity();
  static const int kWeightedAverageMaturityFieldNumber = 65;
  ::google::protobuf::int64 weightedaveragematurity() const;
  void set_weightedaveragematurity(::google::protobuf::int64 value);

  // optional int64 HistoricalTradeSize = 66;
  void clear_historicaltradesize();
  static const int kHistoricalTradeSizeFieldNumber = 66;
  ::google::protobuf::int64 historicaltradesize() const;
  void set_historicaltradesize(::google::protobuf::int64 value);

  // optional int64 HistoricalCancelSize = 67;
  void clear_historicalcancelsize();
  static const int kHistoricalCancelSizeFieldNumber = 67;
  ::google::protobuf::int64 historicalcancelsize() const;
  void set_historicalcancelsize(::google::protobuf::int64 value);

  // optional int64 HistoricalCorrectionSize = 68;
  void clear_historicalcorrectionsize();
  static const int kHistoricalCorrectionSizeFieldNumber = 68;
  ::google::protobuf::int64 historicalcorrectionsize() const;
  void set_historicalcorrectionsize(::google::protobuf::int64 value);

  // optional int64 HistoricalTradeCond = 69;
  void clear_historicaltradecond();
  static const int kHistoricalTradeCondFieldNumber = 69;
  ::google::protobuf::int64 historicaltradecond() const;
  void set_historicaltradecond(::google::protobuf::int64 value);

  // optional double HistoricalTradePrice = 70;
  void clear_historicaltradeprice();
  static const int kHistoricalTradePriceFieldNumber = 70;
  double historicaltradeprice() const;
  void set_historicaltradeprice(double value);

  // optional double HistoricalCancelPrice = 71;
  void clear_historicalcancelprice();
  static const int kHistoricalCancelPriceFieldNumber = 71;
  double historicalcancelprice() const;
  void set_historicalcancelprice(double value);

  // optional double HistoricalCorrectionPrice = 72;
  void clear_historicalcorrectionprice();
  static const int kHistoricalCorrectionPriceFieldNumber = 72;
  double historicalcorrectionprice() const;
  void set_historicalcorrectionprice(double value);

  // optional int64 ActionTime = 73;
  void clear_actiontime();
  static const int kActionTimeFieldNumber = 73;
  ::google::protobuf::int64 actiontime() const;
  void set_actiontime(::google::protobuf::int64 value);

  // optional int64 ActionDate = 74;
  void clear_actiondate();
  static const int kActionDateFieldNumber = 74;
  ::google::protobuf::int64 actiondate() const;
  void set_actiondate(::google::protobuf::int64 value);

  // optional int32 ReasonCode = 75;
  void clear_reasoncode();
  static const int kReasonCodeFieldNumber = 75;
  ::google::protobuf::int32 reasoncode() const;
  void set_reasoncode(::google::protobuf::int32 value);

  // optional int64 HistoricalTradeIdentifier = 76;
  void clear_historicaltradeidentifier();
  static const int kHistoricalTradeIdentifierFieldNumber = 76;
  ::google::protobuf::int64 historicaltradeidentifier() const;
  void set_historicaltradeidentifier(::google::protobuf::int64 value);

  // optional int64 HistoricalOriginalMessageDate = 77;
  void clear_historicaloriginalmessagedate();
  static const int kHistoricalOriginalMessageDateFieldNumber = 77;
  ::google::protobuf::int64 historicaloriginalmessagedate() const;
  void set_historicaloriginalmessagedate(::google::protobuf::int64 value);

  // optional int64 ExtendedTradeCond = 78;
  void clear_extendedtradecond();
  static const int kExtendedTradeCondFieldNumber = 78;
  ::google::protobuf::int64 extendedtradecond() const;
  void set_extendedtradecond(::google::protobuf::int64 value);

  // optional double YieldHigh = 79;
  void clear_yieldhigh();
  static const int kYieldHighFieldNumber = 79;
  double yieldhigh() const;
  void set_yieldhigh(double value);

  // optional double YieldLow = 80;
  void clear_yieldlow();
  static const int kYieldLowFieldNumber = 80;
  double yieldlow() const;
  void set_yieldlow(double value);

  // optional int64 HistoricalCancelTradeDate = 81;
  void clear_historicalcanceltradedate();
  static const int kHistoricalCancelTradeDateFieldNumber = 81;
  ::google::protobuf::int64 historicalcanceltradedate() const;
  void set_historicalcanceltradedate(::google::protobuf::int64 value);

  // optional double HistoricalYield = 82;
  void clear_historicalyield();
  static const int kHistoricalYieldFieldNumber = 82;
  double historicalyield() const;
  void set_historicalyield(double value);

  // optional double HistoricalTradeIndicSize = 83;
  void clear_historicaltradeindicsize();
  static const int kHistoricalTradeIndicSizeFieldNumber = 83;
  double historicaltradeindicsize() const;
  void set_historicaltradeindicsize(double value);

  // optional int64 HistoricalExtendedTradeCond = 84;
  void clear_historicalextendedtradecond();
  static const int kHistoricalExtendedTradeCondFieldNumber = 84;
  ::google::protobuf::int64 historicalextendedtradecond() const;
  void set_historicalextendedtradecond(::google::protobuf::int64 value);

  // optional int32 HistoricalPriceIndicator = 85;
  void clear_historicalpriceindicator();
  static const int kHistoricalPriceIndicatorFieldNumber = 85;
  ::google::protobuf::int32 historicalpriceindicator() const;
  void set_historicalpriceindicator(::google::protobuf::int32 value);

  // optional string SymbolSuffix = 86;
  void clear_symbolsuffix();
  static const int kSymbolSuffixFieldNumber = 86;
  const ::std::string& symbolsuffix() const;
  void set_symbolsuffix(const ::std::string& value);
  void set_symbolsuffix(const char* value);
  void set_symbolsuffix(const char* value, size_t size);
  ::std::string* mutable_symbolsuffix();
  ::std::string* release_symbolsuffix();
  void set_allocated_symbolsuffix(::std::string* symbolsuffix);

  // optional int64 HistoricalTradeDate = 87;
  void clear_historicaltradedate();
  static const int kHistoricalTradeDateFieldNumber = 87;
  ::google::protobuf::int64 historicaltradedate() const;
  void set_historicaltradedate(::google::protobuf::int64 value);

  // optional int64 HistoricalTradeTime = 88;
  void clear_historicaltradetime();
  static const int kHistoricalTradeTimeFieldNumber = 88;
  ::google::protobuf::int64 historicaltradetime() const;
  void set_historicaltradetime(::google::protobuf::int64 value);

  // optional int32 CorrectionPrevSpecialPriceIndicator = 89;
  void clear_correctionprevspecialpriceindicator();
  static const int kCorrectionPrevSpecialPriceIndicatorFieldNumber = 89;
  ::google::protobuf::int32 correctionprevspecialpriceindicator() const;
  void set_correctionprevspecialpriceindicator(::google::protobuf::int32 value);

  // optional int64 CorrectionNewExtendedTradeCond = 90;
  void clear_correctionnewextendedtradecond();
  static const int kCorrectionNewExtendedTradeCondFieldNumber = 90;
  ::google::protobuf::int64 correctionnewextendedtradecond() const;
  void set_correctionnewextendedtradecond(::google::protobuf::int64 value);

  // optional int64 CorrectionPrevExtendedTradeCond = 91;
  void clear_correctionprevextendedtradecond();
  static const int kCorrectionPrevExtendedTradeCondFieldNumber = 91;
  ::google::protobuf::int64 correctionprevextendedtradecond() const;
  void set_correctionprevextendedtradecond(::google::protobuf::int64 value);

  // optional double CorrectionPrevYield = 92;
  void clear_correctionprevyield();
  static const int kCorrectionPrevYieldFieldNumber = 92;
  double correctionprevyield() const;
  void set_correctionprevyield(double value);

  // optional double CorrectionNewYield = 93;
  void clear_correctionnewyield();
  static const int kCorrectionNewYieldFieldNumber = 93;
  double correctionnewyield() const;
  void set_correctionnewyield(double value);

  // optional string CorrectionPrevTradeDate = 94;
  void clear_correctionprevtradedate();
  static const int kCorrectionPrevTradeDateFieldNumber = 94;
  const ::std::string& correctionprevtradedate() const;
  void set_correctionprevtradedate(const ::std::string& value);
  void set_correctionprevtradedate(const char* value);
  void set_correctionprevtradedate(const char* value, size_t size);
  ::std::string* mutable_correctionprevtradedate();
  ::std::string* release_correctionprevtradedate();
  void set_allocated_correctionprevtradedate(::std::string* correctionprevtradedate);

  // optional int64 CorrectionPrevTradeTime = 95;
  void clear_correctionprevtradetime();
  static const int kCorrectionPrevTradeTimeFieldNumber = 95;
  ::google::protobuf::int64 correctionprevtradetime() const;
  void set_correctionprevtradetime(::google::protobuf::int64 value);

  // optional int32 CorrectionPrevTradeCond = 96;
  void clear_correctionprevtradecond();
  static const int kCorrectionPrevTradeCondFieldNumber = 96;
  ::google::protobuf::int32 correctionprevtradecond() const;
  void set_correctionprevtradecond(::google::protobuf::int32 value);

  // optional int64 CorrectionNewTradeCond = 97;
  void clear_correctionnewtradecond();
  static const int kCorrectionNewTradeCondFieldNumber = 97;
  ::google::protobuf::int64 correctionnewtradecond() const;
  void set_correctionnewtradecond(::google::protobuf::int64 value);

  // optional int64 CancelTradeSeq = 98;
  void clear_canceltradeseq();
  static const int kCancelTradeSeqFieldNumber = 98;
  ::google::protobuf::int64 canceltradeseq() const;
  void set_canceltradeseq(::google::protobuf::int64 value);

  // optional int32 PrevTransactionPriceInd = 99;
  void clear_prevtransactionpriceind();
  static const int kPrevTransactionPriceIndFieldNumber = 99;
  ::google::protobuf::int32 prevtransactionpriceind() const;
  void set_prevtransactionpriceind(::google::protobuf::int32 value);

  // optional int64 CorrectionNewTransactionPriceInd = 100;
  void clear_correctionnewtransactionpriceind();
  static const int kCorrectionNewTransactionPriceIndFieldNumber = 100;
  ::google::protobuf::int64 correctionnewtransactionpriceind() const;
  void set_correctionnewtransactionpriceind(::google::protobuf::int64 value);

  // optional int64 OriginalTradeSeq = 101;
  void clear_originaltradeseq();
  static const int kOriginalTradeSeqFieldNumber = 101;
  ::google::protobuf::int64 originaltradeseq() const;
  void set_originaltradeseq(::google::protobuf::int64 value);

  // optional double YieldClose = 102;
  void clear_yieldclose();
  static const int kYieldCloseFieldNumber = 102;
  double yieldclose() const;
  void set_yieldclose(double value);

  // optional int64 FirstSettlementDate = 103;
  void clear_firstsettlementdate();
  static const int kFirstSettlementDateFieldNumber = 103;
  ::google::protobuf::int64 firstsettlementdate() const;
  void set_firstsettlementdate(::google::protobuf::int64 value);

  // optional int64 ReportDate = 104;
  void clear_reportdate();
  static const int kReportDateFieldNumber = 104;
  ::google::protobuf::int64 reportdate() const;
  void set_reportdate(::google::protobuf::int64 value);

  // optional int32 MarketPhase = 105;
  void clear_marketphase();
  static const int kMarketPhaseFieldNumber = 105;
  ::google::protobuf::int32 marketphase() const;
  void set_marketphase(::google::protobuf::int32 value);

  // optional int64 DisseminationDate = 106;
  void clear_disseminationdate();
  static const int kDisseminationDateFieldNumber = 106;
  ::google::protobuf::int64 disseminationdate() const;
  void set_disseminationdate(::google::protobuf::int64 value);

  // optional int64 CorrectionNewDisseminationDate = 107;
  void clear_correctionnewdisseminationdate();
  static const int kCorrectionNewDisseminationDateFieldNumber = 107;
  ::google::protobuf::int64 correctionnewdisseminationdate() const;
  void set_correctionnewdisseminationdate(::google::protobuf::int64 value);

  // optional int64 CorrectionPrevDisseminationDate = 108;
  void clear_correctionprevdisseminationdate();
  static const int kCorrectionPrevDisseminationDateFieldNumber = 108;
  ::google::protobuf::int64 correctionprevdisseminationdate() const;
  void set_correctionprevdisseminationdate(::google::protobuf::int64 value);

  // optional int32 TradeCond1 = 109;
  void clear_tradecond1();
  static const int kTradeCond1FieldNumber = 109;
  ::google::protobuf::int32 tradecond1() const;
  void set_tradecond1(::google::protobuf::int32 value);

  // optional int64 CorrectionPrevTradeUniqueId = 110;
  void clear_correctionprevtradeuniqueid();
  static const int kCorrectionPrevTradeUniqueIdFieldNumber = 110;
  ::google::protobuf::int64 correctionprevtradeuniqueid() const;
  void set_correctionprevtradeuniqueid(::google::protobuf::int64 value);

  // optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
  void clear_historicalcorrectionprevtradeuniqueid();
  static const int kHistoricalCorrectionPrevTradeUniqueIdFieldNumber = 111;
  ::google::protobuf::int64 historicalcorrectionprevtradeuniqueid() const;
  void set_historicalcorrectionprevtradeuniqueid(::google::protobuf::int64 value);

  // optional int64 HistoricalCancelTradeTime = 112;
  void clear_historicalcanceltradetime();
  static const int kHistoricalCancelTradeTimeFieldNumber = 112;
  ::google::protobuf::int64 historicalcanceltradetime() const;
  void set_historicalcanceltradetime(::google::protobuf::int64 value);

  // optional int64 HistoricalCorrectionTradeTime = 113;
  void clear_historicalcorrectiontradetime();
  static const int kHistoricalCorrectionTradeTimeFieldNumber = 113;
  ::google::protobuf::int64 historicalcorrectiontradetime() const;
  void set_historicalcorrectiontradetime(::google::protobuf::int64 value);

  // optional int64 HistoricalCorrectionTradeDate = 114;
  void clear_historicalcorrectiontradedate();
  static const int kHistoricalCorrectionTradeDateFieldNumber = 114;
  ::google::protobuf::int64 historicalcorrectiontradedate() const;
  void set_historicalcorrectiontradedate(::google::protobuf::int64 value);

  // optional int32 BondType = 115;
  void clear_bondtype();
  static const int kBondTypeFieldNumber = 115;
  ::google::protobuf::int32 bondtype() const;
  void set_bondtype(::google::protobuf::int32 value);

  // optional double Yield = 117;
  void clear_yield();
  static const int kYieldFieldNumber = 117;
  double yield() const;
  void set_yield(double value);

  // optional string Cusip = 118;
  void clear_cusip();
  static const int kCusipFieldNumber = 118;
  const ::std::string& cusip() const;
  void set_cusip(const ::std::string& value);
  void set_cusip(const char* value);
  void set_cusip(const char* value, size_t size);
  ::std::string* mutable_cusip();
  ::std::string* release_cusip();
  void set_allocated_cusip(::std::string* cusip);

  // optional string SymbolExchTicker = 119;
  void clear_symbolexchticker();
  static const int kSymbolExchTickerFieldNumber = 119;
  const ::std::string& symbolexchticker() const;
  void set_symbolexchticker(const ::std::string& value);
  void set_symbolexchticker(const char* value);
  void set_symbolexchticker(const char* value, size_t size);
  ::std::string* mutable_symbolexchticker();
  ::std::string* release_symbolexchticker();
  void set_allocated_symbolexchticker(::std::string* symbolexchticker);

  // optional string ISIN = 120;
  void clear_isin();
  static const int kISINFieldNumber = 120;
  const ::std::string& isin() const;
  void set_isin(const ::std::string& value);
  void set_isin(const char* value);
  void set_isin(const char* value, size_t size);
  ::std::string* mutable_isin();
  ::std::string* release_isin();
  void set_allocated_isin(::std::string* isin);

  // optional string InstrName2 = 121;
  void clear_instrname2();
  static const int kInstrName2FieldNumber = 121;
  const ::std::string& instrname2() const;
  void set_instrname2(const ::std::string& value);
  void set_instrname2(const char* value);
  void set_instrname2(const char* value, size_t size);
  ::std::string* mutable_instrname2();
  ::std::string* release_instrname2();
  void set_allocated_instrname2(::std::string* instrname2);

  // optional string TradeSettlementDate = 122;
  void clear_tradesettlementdate();
  static const int kTradeSettlementDateFieldNumber = 122;
  const ::std::string& tradesettlementdate() const;
  void set_tradesettlementdate(const ::std::string& value);
  void set_tradesettlementdate(const char* value);
  void set_tradesettlementdate(const char* value, size_t size);
  ::std::string* mutable_tradesettlementdate();
  ::std::string* release_tradesettlementdate();
  void set_allocated_tradesettlementdate(::std::string* tradesettlementdate);

  // optional int64 FirstTradingDate = 123;
  void clear_firsttradingdate();
  static const int kFirstTradingDateFieldNumber = 123;
  ::google::protobuf::int64 firsttradingdate() const;
  void set_firsttradingdate(::google::protobuf::int64 value);

  // optional string CouponType = 124;
  void clear_coupontype();
  static const int kCouponTypeFieldNumber = 124;
  const ::std::string& coupontype() const;
  void set_coupontype(const ::std::string& value);
  void set_coupontype(const char* value);
  void set_coupontype(const char* value, size_t size);
  ::std::string* mutable_coupontype();
  ::std::string* release_coupontype();
  void set_allocated_coupontype(::std::string* coupontype);

  // optional int64 EnumInterestCalcType = 125;
  void clear_enuminterestcalctype();
  static const int kEnumInterestCalcTypeFieldNumber = 125;
  ::google::protobuf::int64 enuminterestcalctype() const;
  void set_enuminterestcalctype(::google::protobuf::int64 value);

  // optional string IssuerName = 126;
  void clear_issuername();
  static const int kIssuerNameFieldNumber = 126;
  const ::std::string& issuername() const;
  void set_issuername(const ::std::string& value);
  void set_issuername(const char* value);
  void set_issuername(const char* value, size_t size);
  ::std::string* mutable_issuername();
  ::std::string* release_issuername();
  void set_allocated_issuername(::std::string* issuername);

  // optional int32 InvestmentGrade = 127;
  void clear_investmentgrade();
  static const int kInvestmentGradeFieldNumber = 127;
  ::google::protobuf::int32 investmentgrade() const;
  void set_investmentgrade(::google::protobuf::int32 value);

  // optional string AmortizationType = 128;
  void clear_amortizationtype();
  static const int kAmortizationTypeFieldNumber = 128;
  const ::std::string& amortizationtype() const;
  void set_amortizationtype(const ::std::string& value);
  void set_amortizationtype(const char* value);
  void set_amortizationtype(const char* value, size_t size);
  ::std::string* mutable_amortizationtype();
  ::std::string* release_amortizationtype();
  void set_allocated_amortizationtype(::std::string* amortizationtype);

  // optional string DisseminationFlag = 129;
  void clear_disseminationflag();
  static const int kDisseminationFlagFieldNumber = 129;
  const ::std::string& disseminationflag() const;
  void set_disseminationflag(const ::std::string& value);
  void set_disseminationflag(const char* value);
  void set_disseminationflag(const char* value, size_t size);
  ::std::string* mutable_disseminationflag();
  ::std::string* release_disseminationflag();
  void set_allocated_disseminationflag(::std::string* disseminationflag);

  // optional string SymbolBloombergTicker = 130;
  void clear_symbolbloombergticker();
  static const int kSymbolBloombergTickerFieldNumber = 130;
  const ::std::string& symbolbloombergticker() const;
  void set_symbolbloombergticker(const ::std::string& value);
  void set_symbolbloombergticker(const char* value);
  void set_symbolbloombergticker(const char* value, size_t size);
  ::std::string* mutable_symbolbloombergticker();
  ::std::string* release_symbolbloombergticker();
  void set_allocated_symbolbloombergticker(::std::string* symbolbloombergticker);

  // optional string BloombergGlobalId = 131;
  void clear_bloombergglobalid();
  static const int kBloombergGlobalIdFieldNumber = 131;
  const ::std::string& bloombergglobalid() const;
  void set_bloombergglobalid(const ::std::string& value);
  void set_bloombergglobalid(const char* value);
  void set_bloombergglobalid(const char* value, size_t size);
  ::std::string* mutable_bloombergglobalid();
  ::std::string* release_bloombergglobalid();
  void set_allocated_bloombergglobalid(::std::string* bloombergglobalid);

  // optional string BloombergGlobalIdComp = 132;
  void clear_bloombergglobalidcomp();
  static const int kBloombergGlobalIdCompFieldNumber = 132;
  const ::std::string& bloombergglobalidcomp() const;
  void set_bloombergglobalidcomp(const ::std::string& value);
  void set_bloombergglobalidcomp(const char* value);
  void set_bloombergglobalidcomp(const char* value, size_t size);
  ::std::string* mutable_bloombergglobalidcomp();
  ::std::string* release_bloombergglobalidcomp();
  void set_allocated_bloombergglobalidcomp(::std::string* bloombergglobalidcomp);

  // optional string InstrLocalType2 = 133;
  void clear_instrlocaltype2();
  static const int kInstrLocalType2FieldNumber = 133;
  const ::std::string& instrlocaltype2() const;
  void set_instrlocaltype2(const ::std::string& value);
  void set_instrlocaltype2(const char* value);
  void set_instrlocaltype2(const char* value, size_t size);
  ::std::string* mutable_instrlocaltype2();
  ::std::string* release_instrlocaltype2();
  void set_allocated_instrlocaltype2(::std::string* instrlocaltype2);

  // optional string SymbolEsignalTicker = 134;
  void clear_symbolesignalticker();
  static const int kSymbolEsignalTickerFieldNumber = 134;
  const ::std::string& symbolesignalticker() const;
  void set_symbolesignalticker(const ::std::string& value);
  void set_symbolesignalticker(const char* value);
  void set_symbolesignalticker(const char* value, size_t size);
  ::std::string* mutable_symbolesignalticker();
  ::std::string* release_symbolesignalticker();
  void set_allocated_symbolesignalticker(::std::string* symbolesignalticker);

  // optional string InstrNameLocal = 135;
  void clear_instrnamelocal();
  static const int kInstrNameLocalFieldNumber = 135;
  const ::std::string& instrnamelocal() const;
  void set_instrnamelocal(const ::std::string& value);
  void set_instrnamelocal(const char* value);
  void set_instrnamelocal(const char* value, size_t size);
  ::std::string* mutable_instrnamelocal();
  ::std::string* release_instrnamelocal();
  void set_allocated_instrnamelocal(::std::string* instrnamelocal);

  // optional string MktSegmentString = 136;
  void clear_mktsegmentstring();
  static const int kMktSegmentStringFieldNumber = 136;
  const ::std::string& mktsegmentstring() const;
  void set_mktsegmentstring(const ::std::string& value);
  void set_mktsegmentstring(const char* value);
  void set_mktsegmentstring(const char* value, size_t size);
  ::std::string* mutable_mktsegmentstring();
  ::std::string* release_mktsegmentstring();
  void set_allocated_mktsegmentstring(::std::string* mktsegmentstring);

  // optional string ExchMonthCode = 137;
  void clear_exchmonthcode();
  static const int kExchMonthCodeFieldNumber = 137;
  const ::std::string& exchmonthcode() const;
  void set_exchmonthcode(const ::std::string& value);
  void set_exchmonthcode(const char* value);
  void set_exchmonthcode(const char* value, size_t size);
  ::std::string* mutable_exchmonthcode();
  ::std::string* release_exchmonthcode();
  void set_allocated_exchmonthcode(::std::string* exchmonthcode);

  // optional int64 PoolNumber = 138;
  void clear_poolnumber();
  static const int kPoolNumberFieldNumber = 138;
  ::google::protobuf::int64 poolnumber() const;
  void set_poolnumber(::google::protobuf::int64 value);

  // optional string MasterDealId = 139;
  void clear_masterdealid();
  static const int kMasterDealIdFieldNumber = 139;
  const ::std::string& masterdealid() const;
  void set_masterdealid(const ::std::string& value);
  void set_masterdealid(const char* value);
  void set_masterdealid(const char* value, size_t size);
  ::std::string* mutable_masterdealid();
  ::std::string* release_masterdealid();
  void set_allocated_masterdealid(::std::string* masterdealid);

  // optional string TrancheId = 140;
  void clear_trancheid();
  static const int kTrancheIdFieldNumber = 140;
  const ::std::string& trancheid() const;
  void set_trancheid(const ::std::string& value);
  void set_trancheid(const char* value);
  void set_trancheid(const char* value, size_t size);
  ::std::string* mutable_trancheid();
  ::std::string* release_trancheid();
  void set_allocated_trancheid(::std::string* trancheid);

  // optional string Indicator144A = 141;
  void clear_indicator144a();
  static const int kIndicator144AFieldNumber = 141;
  const ::std::string& indicator144a() const;
  void set_indicator144a(const ::std::string& value);
  void set_indicator144a(const char* value);
  void set_indicator144a(const char* value, size_t size);
  ::std::string* mutable_indicator144a();
  ::std::string* release_indicator144a();
  void set_allocated_indicator144a(::std::string* indicator144a);

  // optional int64 NumberMaturityMonths = 142;
  void clear_numbermaturitymonths();
  static const int kNumberMaturityMonthsFieldNumber = 142;
  ::google::protobuf::int64 numbermaturitymonths() const;
  void set_numbermaturitymonths(::google::protobuf::int64 value);

  // optional string DebtTypeCode = 143;
  void clear_debttypecode();
  static const int kDebtTypeCodeFieldNumber = 143;
  const ::std::string& debttypecode() const;
  void set_debttypecode(const ::std::string& value);
  void set_debttypecode(const char* value);
  void set_debttypecode(const char* value, size_t size);
  ::std::string* mutable_debttypecode();
  ::std::string* release_debttypecode();
  void set_allocated_debttypecode(::std::string* debttypecode);

  // optional int64 TokenDel = 144;
  void clear_tokendel();
  static const int kTokenDelFieldNumber = 144;
  ::google::protobuf::int64 tokendel() const;
  void set_tokendel(::google::protobuf::int64 value);

  // optional int64 CorrectionTradeSeq = 145;
  void clear_correctiontradeseq();
  static const int kCorrectionTradeSeqFieldNumber = 145;
  ::google::protobuf::int64 correctiontradeseq() const;
  void set_correctiontradeseq(::google::protobuf::int64 value);

  // optional int32 RecordStaleInd = 146;
  void clear_recordstaleind();
  static const int kRecordStaleIndFieldNumber = 146;
  ::google::protobuf::int32 recordstaleind() const;
  void set_recordstaleind(::google::protobuf::int32 value);

  // optional double WeightedAverageCoupon = 147;
  void clear_weightedaveragecoupon();
  static const int kWeightedAverageCouponFieldNumber = 147;
  double weightedaveragecoupon() const;
  void set_weightedaveragecoupon(double value);

  // optional int64 WeightedAverageLoanAge = 148;
  void clear_weightedaverageloanage();
  static const int kWeightedAverageLoanAgeFieldNumber = 148;
  ::google::protobuf::int64 weightedaverageloanage() const;
  void set_weightedaverageloanage(::google::protobuf::int64 value);

  // optional int64 WeightedAverageLoanSize = 149;
  void clear_weightedaverageloansize();
  static const int kWeightedAverageLoanSizeFieldNumber = 149;
  ::google::protobuf::int64 weightedaverageloansize() const;
  void set_weightedaverageloansize(::google::protobuf::int64 value);

  // optional int64 WeightedLoanValue = 150;
  void clear_weightedloanvalue();
  static const int kWeightedLoanValueFieldNumber = 150;
  ::google::protobuf::int64 weightedloanvalue() const;
  void set_weightedloanvalue(::google::protobuf::int64 value);

  // optional double AverageMonthlySize = 151;
  void clear_averagemonthlysize();
  static const int kAverageMonthlySizeFieldNumber = 151;
  double averagemonthlysize() const;
  void set_averagemonthlysize(double value);

  // optional double PrevMonthVolDec = 152;
  void clear_prevmonthvoldec();
  static const int kPrevMonthVolDecFieldNumber = 152;
  double prevmonthvoldec() const;
  void set_prevmonthvoldec(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDIceTrace)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr activitydatetime_;
  ::google::protobuf::internal::ArenaStringPtr tradedatetime_;
  ::google::protobuf::internal::ArenaStringPtr enumcurrency_;
  ::google::protobuf::internal::ArenaStringPtr currencystring_;
  ::google::protobuf::internal::ArenaStringPtr currentdatetime_;
  ::google::protobuf::internal::ArenaStringPtr symbolsuffix_;
  ::google::protobuf::internal::ArenaStringPtr correctionprevtradedate_;
  ::google::protobuf::internal::ArenaStringPtr cusip_;
  ::google::protobuf::internal::ArenaStringPtr symbolexchticker_;
  ::google::protobuf::internal::ArenaStringPtr isin_;
  ::google::protobuf::internal::ArenaStringPtr instrname2_;
  ::google::protobuf::internal::ArenaStringPtr tradesettlementdate_;
  ::google::protobuf::internal::ArenaStringPtr coupontype_;
  ::google::protobuf::internal::ArenaStringPtr issuername_;
  ::google::protobuf::internal::ArenaStringPtr amortizationtype_;
  ::google::protobuf::internal::ArenaStringPtr disseminationflag_;
  ::google::protobuf::internal::ArenaStringPtr symbolbloombergticker_;
  ::google::protobuf::internal::ArenaStringPtr bloombergglobalid_;
  ::google::protobuf::internal::ArenaStringPtr bloombergglobalidcomp_;
  ::google::protobuf::internal::ArenaStringPtr instrlocaltype2_;
  ::google::protobuf::internal::ArenaStringPtr symbolesignalticker_;
  ::google::protobuf::internal::ArenaStringPtr instrnamelocal_;
  ::google::protobuf::internal::ArenaStringPtr mktsegmentstring_;
  ::google::protobuf::internal::ArenaStringPtr exchmonthcode_;
  ::google::protobuf::internal::ArenaStringPtr masterdealid_;
  ::google::protobuf::internal::ArenaStringPtr trancheid_;
  ::google::protobuf::internal::ArenaStringPtr indicator144a_;
  ::google::protobuf::internal::ArenaStringPtr debttypecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  double tradeprice_;
  ::google::protobuf::int64 tradesize_;
  double currentprice_;
  ::google::protobuf::int64 tradevol_;
  ::google::protobuf::int64 exchseq_;
  double tradeindicsize_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 marketphasecode_;
  ::google::protobuf::int64 exchmessagetimestamp_;
  double correctionnewtradeprice_;
  ::google::protobuf::int64 correctionnewtradesize_;
  double correctionprevtradeprice_;
  ::google::protobuf::int64 correctionprevtradesize_;
  ::google::protobuf::int64 maturitydate_;
  double chg_;
  double pctchg_;
  double closepx_;
  double highpx_;
  double lowpx_;
  double openpx_;
  double preclosepx_;
  ::google::protobuf::int64 tradeofficialdate_;
  double tradecondprice_;
  ::google::protobuf::int64 tradecondsize_;
  ::google::protobuf::int64 tradeofficialtime_;
  ::google::protobuf::int64 tradeuniqueid_;
  bool recorddel_;
  ::google::protobuf::int32 transactionpriceind_;
  ::google::protobuf::int64 updateaction_;
  ::google::protobuf::int64 yesttradevol_;
  double vwap_;
  ::google::protobuf::int64 tradecondofficialtime_;
  ::google::protobuf::int64 tradecondofficialdate_;
  ::google::protobuf::int64 correctionofficialtradetime_;
  ::google::protobuf::int64 correctionofficialtradedate_;
  double couponrate_;
  ::google::protobuf::int32 instrstatus_;
  ::google::protobuf::int32 msgtype_;
  ::google::protobuf::int64 weightedaveragematurity_;
  ::google::protobuf::int64 historicaltradesize_;
  ::google::protobuf::int64 historicalcancelsize_;
  ::google::protobuf::int64 historicalcorrectionsize_;
  ::google::protobuf::int64 historicaltradecond_;
  ::google::protobuf::int32 controlmsgtype_;
  ::google::protobuf::int32 reasoncode_;
  double historicaltradeprice_;
  double historicalcancelprice_;
  double historicalcorrectionprice_;
  ::google::protobuf::int64 actiontime_;
  ::google::protobuf::int64 actiondate_;
  ::google::protobuf::int64 historicaltradeidentifier_;
  ::google::protobuf::int64 historicaloriginalmessagedate_;
  ::google::protobuf::int64 extendedtradecond_;
  double yieldhigh_;
  double yieldlow_;
  ::google::protobuf::int64 historicalcanceltradedate_;
  double historicalyield_;
  double historicaltradeindicsize_;
  ::google::protobuf::int64 historicalextendedtradecond_;
  ::google::protobuf::int64 historicaltradedate_;
  ::google::protobuf::int32 historicalpriceindicator_;
  ::google::protobuf::int32 correctionprevspecialpriceindicator_;
  ::google::protobuf::int64 historicaltradetime_;
  ::google::protobuf::int64 correctionnewextendedtradecond_;
  ::google::protobuf::int64 correctionprevextendedtradecond_;
  double correctionprevyield_;
  double correctionnewyield_;
  ::google::protobuf::int64 correctionprevtradetime_;
  ::google::protobuf::int64 correctionnewtradecond_;
  ::google::protobuf::int32 correctionprevtradecond_;
  ::google::protobuf::int32 prevtransactionpriceind_;
  ::google::protobuf::int64 canceltradeseq_;
  ::google::protobuf::int64 correctionnewtransactionpriceind_;
  ::google::protobuf::int64 originaltradeseq_;
  double yieldclose_;
  ::google::protobuf::int64 firstsettlementdate_;
  ::google::protobuf::int64 reportdate_;
  ::google::protobuf::int64 disseminationdate_;
  ::google::protobuf::int64 correctionnewdisseminationdate_;
  ::google::protobuf::int32 marketphase_;
  ::google::protobuf::int32 tradecond1_;
  ::google::protobuf::int64 correctionprevdisseminationdate_;
  ::google::protobuf::int64 correctionprevtradeuniqueid_;
  ::google::protobuf::int64 historicalcorrectionprevtradeuniqueid_;
  ::google::protobuf::int64 historicalcanceltradetime_;
  ::google::protobuf::int64 historicalcorrectiontradetime_;
  ::google::protobuf::int64 historicalcorrectiontradedate_;
  double yield_;
  ::google::protobuf::int32 bondtype_;
  ::google::protobuf::int32 investmentgrade_;
  ::google::protobuf::int64 firsttradingdate_;
  ::google::protobuf::int64 enuminterestcalctype_;
  ::google::protobuf::int64 poolnumber_;
  ::google::protobuf::int64 numbermaturitymonths_;
  ::google::protobuf::int64 tokendel_;
  ::google::protobuf::int64 correctiontradeseq_;
  double weightedaveragecoupon_;
  ::google::protobuf::int64 weightedaverageloanage_;
  ::google::protobuf::int64 weightedaverageloansize_;
  ::google::protobuf::int64 weightedloanvalue_;
  double averagemonthlysize_;
  double prevmonthvoldec_;
  ::google::protobuf::int32 recordstaleind_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDIceTrace_2eproto_impl();
  friend void  protobuf_AddDesc_MDIceTrace_2eproto_impl();
  friend void protobuf_AssignDesc_MDIceTrace_2eproto();
  friend void protobuf_ShutdownFile_MDIceTrace_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDIceTrace> MDIceTrace_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDIceTrace

// optional int32 MDDate = 1;
inline void MDIceTrace::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MDDate)
  return mddate_;
}
inline void MDIceTrace::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MDDate)
}

// optional int32 MDTime = 2;
inline void MDIceTrace::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MDTime)
  return mdtime_;
}
inline void MDIceTrace::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MDTime)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 3;
inline void MDIceTrace::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDIceTrace::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDIceTrace::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 4;
inline void MDIceTrace::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDIceTrace::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDIceTrace::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SecurityIDSource)
}

// optional string HTSCSecurityID = 5;
inline void MDIceTrace::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
inline void MDIceTrace::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
inline void MDIceTrace::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}
inline ::std::string* MDIceTrace::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.HTSCSecurityID)
}

// optional int32 ExchangeDate = 6;
inline void MDIceTrace::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchangeDate)
  return exchangedate_;
}
inline void MDIceTrace::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchangeDate)
}

// optional int32 ExchangeTime = 7;
inline void MDIceTrace::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchangeTime)
  return exchangetime_;
}
inline void MDIceTrace::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 8;
inline void MDIceTrace::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDIceTrace::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DataMultiplePowerOf10)
}

// optional double TradePrice = 19;
inline void MDIceTrace::clear_tradeprice() {
  tradeprice_ = 0;
}
inline double MDIceTrace::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradePrice)
  return tradeprice_;
}
inline void MDIceTrace::set_tradeprice(double value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradePrice)
}

// optional int64 TradeSize = 20;
inline void MDIceTrace::clear_tradesize() {
  tradesize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeSize)
  return tradesize_;
}
inline void MDIceTrace::set_tradesize(::google::protobuf::int64 value) {
  
  tradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeSize)
}

// optional double CurrentPrice = 21;
inline void MDIceTrace::clear_currentprice() {
  currentprice_ = 0;
}
inline double MDIceTrace::currentprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrentPrice)
  return currentprice_;
}
inline void MDIceTrace::set_currentprice(double value) {
  
  currentprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrentPrice)
}

// optional string ActivityDatetime = 22;
inline void MDIceTrace::clear_activitydatetime() {
  activitydatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::activitydatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  return activitydatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_activitydatetime(const ::std::string& value) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
inline void MDIceTrace::set_activitydatetime(const char* value) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
inline void MDIceTrace::set_activitydatetime(const char* value, size_t size) {
  
  activitydatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}
inline ::std::string* MDIceTrace::mutable_activitydatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  return activitydatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_activitydatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
  
  return activitydatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_activitydatetime(::std::string* activitydatetime) {
  if (activitydatetime != NULL) {
    
  } else {
    
  }
  activitydatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), activitydatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ActivityDatetime)
}

// optional string TradeDatetime = 23;
inline void MDIceTrace::clear_tradedatetime() {
  tradedatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::tradedatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  return tradedatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_tradedatetime(const ::std::string& value) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
inline void MDIceTrace::set_tradedatetime(const char* value) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
inline void MDIceTrace::set_tradedatetime(const char* value, size_t size) {
  
  tradedatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}
inline ::std::string* MDIceTrace::mutable_tradedatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  return tradedatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_tradedatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
  
  return tradedatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_tradedatetime(::std::string* tradedatetime) {
  if (tradedatetime != NULL) {
    
  } else {
    
  }
  tradedatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TradeDatetime)
}

// optional int64 TradeVol = 24;
inline void MDIceTrace::clear_tradevol() {
  tradevol_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradevol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeVol)
  return tradevol_;
}
inline void MDIceTrace::set_tradevol(::google::protobuf::int64 value) {
  
  tradevol_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeVol)
}

// optional int64 ExchSeq = 26;
inline void MDIceTrace::clear_exchseq() {
  exchseq_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::exchseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchSeq)
  return exchseq_;
}
inline void MDIceTrace::set_exchseq(::google::protobuf::int64 value) {
  
  exchseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchSeq)
}

// optional double TradeIndicSize = 27;
inline void MDIceTrace::clear_tradeindicsize() {
  tradeindicsize_ = 0;
}
inline double MDIceTrace::tradeindicsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeIndicSize)
  return tradeindicsize_;
}
inline void MDIceTrace::set_tradeindicsize(double value) {
  
  tradeindicsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeIndicSize)
}

// optional int64 ExchMessageTimestamp = 28;
inline void MDIceTrace::clear_exchmessagetimestamp() {
  exchmessagetimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::exchmessagetimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchMessageTimestamp)
  return exchmessagetimestamp_;
}
inline void MDIceTrace::set_exchmessagetimestamp(::google::protobuf::int64 value) {
  
  exchmessagetimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchMessageTimestamp)
}

// optional double CorrectionNewTradePrice = 30;
inline void MDIceTrace::clear_correctionnewtradeprice() {
  correctionnewtradeprice_ = 0;
}
inline double MDIceTrace::correctionnewtradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradePrice)
  return correctionnewtradeprice_;
}
inline void MDIceTrace::set_correctionnewtradeprice(double value) {
  
  correctionnewtradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradePrice)
}

// optional int64 CorrectionNewTradeSize = 31;
inline void MDIceTrace::clear_correctionnewtradesize() {
  correctionnewtradesize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionnewtradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeSize)
  return correctionnewtradesize_;
}
inline void MDIceTrace::set_correctionnewtradesize(::google::protobuf::int64 value) {
  
  correctionnewtradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeSize)
}

// optional double CorrectionPrevTradePrice = 32;
inline void MDIceTrace::clear_correctionprevtradeprice() {
  correctionprevtradeprice_ = 0;
}
inline double MDIceTrace::correctionprevtradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradePrice)
  return correctionprevtradeprice_;
}
inline void MDIceTrace::set_correctionprevtradeprice(double value) {
  
  correctionprevtradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradePrice)
}

// optional int64 CorrectionPrevTradeSize = 33;
inline void MDIceTrace::clear_correctionprevtradesize() {
  correctionprevtradesize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionprevtradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeSize)
  return correctionprevtradesize_;
}
inline void MDIceTrace::set_correctionprevtradesize(::google::protobuf::int64 value) {
  
  correctionprevtradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeSize)
}

// optional string EnumCurrency = 34;
inline void MDIceTrace::clear_enumcurrency() {
  enumcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::enumcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  return enumcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_enumcurrency(const ::std::string& value) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
inline void MDIceTrace::set_enumcurrency(const char* value) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
inline void MDIceTrace::set_enumcurrency(const char* value, size_t size) {
  
  enumcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}
inline ::std::string* MDIceTrace::mutable_enumcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  return enumcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_enumcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
  
  return enumcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_enumcurrency(::std::string* enumcurrency) {
  if (enumcurrency != NULL) {
    
  } else {
    
  }
  enumcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), enumcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.EnumCurrency)
}

// optional int64 MaturityDate = 35;
inline void MDIceTrace::clear_maturitydate() {
  maturitydate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MaturityDate)
  return maturitydate_;
}
inline void MDIceTrace::set_maturitydate(::google::protobuf::int64 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MaturityDate)
}

// optional int32 MarketPhaseCode = 36;
inline void MDIceTrace::clear_marketphasecode() {
  marketphasecode_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MarketPhaseCode)
  return marketphasecode_;
}
inline void MDIceTrace::set_marketphasecode(::google::protobuf::int32 value) {
  
  marketphasecode_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MarketPhaseCode)
}

// optional double Chg = 37;
inline void MDIceTrace::clear_chg() {
  chg_ = 0;
}
inline double MDIceTrace::chg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Chg)
  return chg_;
}
inline void MDIceTrace::set_chg(double value) {
  
  chg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Chg)
}

// optional double PctChg = 38;
inline void MDIceTrace::clear_pctchg() {
  pctchg_ = 0;
}
inline double MDIceTrace::pctchg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PctChg)
  return pctchg_;
}
inline void MDIceTrace::set_pctchg(double value) {
  
  pctchg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PctChg)
}

// optional double ClosePx = 39;
inline void MDIceTrace::clear_closepx() {
  closepx_ = 0;
}
inline double MDIceTrace::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ClosePx)
  return closepx_;
}
inline void MDIceTrace::set_closepx(double value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ClosePx)
}

// optional double HighPx = 40;
inline void MDIceTrace::clear_highpx() {
  highpx_ = 0;
}
inline double MDIceTrace::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HighPx)
  return highpx_;
}
inline void MDIceTrace::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HighPx)
}

// optional double LowPx = 41;
inline void MDIceTrace::clear_lowpx() {
  lowpx_ = 0;
}
inline double MDIceTrace::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.LowPx)
  return lowpx_;
}
inline void MDIceTrace::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.LowPx)
}

// optional double OpenPx = 42;
inline void MDIceTrace::clear_openpx() {
  openpx_ = 0;
}
inline double MDIceTrace::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.OpenPx)
  return openpx_;
}
inline void MDIceTrace::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.OpenPx)
}

// optional double PreClosePx = 43;
inline void MDIceTrace::clear_preclosepx() {
  preclosepx_ = 0;
}
inline double MDIceTrace::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PreClosePx)
  return preclosepx_;
}
inline void MDIceTrace::set_preclosepx(double value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PreClosePx)
}

// optional bool RecordDel = 45;
inline void MDIceTrace::clear_recorddel() {
  recorddel_ = false;
}
inline bool MDIceTrace::recorddel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.RecordDel)
  return recorddel_;
}
inline void MDIceTrace::set_recorddel(bool value) {
  
  recorddel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.RecordDel)
}

// optional string CurrencyString = 46;
inline void MDIceTrace::clear_currencystring() {
  currencystring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::currencystring() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  return currencystring_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_currencystring(const ::std::string& value) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
inline void MDIceTrace::set_currencystring(const char* value) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
inline void MDIceTrace::set_currencystring(const char* value, size_t size) {
  
  currencystring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}
inline ::std::string* MDIceTrace::mutable_currencystring() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  return currencystring_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_currencystring() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
  
  return currencystring_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_currencystring(::std::string* currencystring) {
  if (currencystring != NULL) {
    
  } else {
    
  }
  currencystring_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currencystring);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CurrencyString)
}

// optional string CurrentDatetime = 47;
inline void MDIceTrace::clear_currentdatetime() {
  currentdatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::currentdatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  return currentdatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_currentdatetime(const ::std::string& value) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
inline void MDIceTrace::set_currentdatetime(const char* value) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
inline void MDIceTrace::set_currentdatetime(const char* value, size_t size) {
  
  currentdatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}
inline ::std::string* MDIceTrace::mutable_currentdatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  return currentdatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_currentdatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
  
  return currentdatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_currentdatetime(::std::string* currentdatetime) {
  if (currentdatetime != NULL) {
    
  } else {
    
  }
  currentdatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currentdatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CurrentDatetime)
}

// optional int64 TradeOfficialDate = 48;
inline void MDIceTrace::clear_tradeofficialdate() {
  tradeofficialdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradeofficialdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialDate)
  return tradeofficialdate_;
}
inline void MDIceTrace::set_tradeofficialdate(::google::protobuf::int64 value) {
  
  tradeofficialdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialDate)
}

// optional double TradeCondPrice = 49;
inline void MDIceTrace::clear_tradecondprice() {
  tradecondprice_ = 0;
}
inline double MDIceTrace::tradecondprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondPrice)
  return tradecondprice_;
}
inline void MDIceTrace::set_tradecondprice(double value) {
  
  tradecondprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondPrice)
}

// optional int64 TradeCondSize = 50;
inline void MDIceTrace::clear_tradecondsize() {
  tradecondsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradecondsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondSize)
  return tradecondsize_;
}
inline void MDIceTrace::set_tradecondsize(::google::protobuf::int64 value) {
  
  tradecondsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondSize)
}

// optional int64 TradeOfficialTime = 51;
inline void MDIceTrace::clear_tradeofficialtime() {
  tradeofficialtime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradeofficialtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialTime)
  return tradeofficialtime_;
}
inline void MDIceTrace::set_tradeofficialtime(::google::protobuf::int64 value) {
  
  tradeofficialtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeOfficialTime)
}

// optional int64 TradeUniqueId = 52;
inline void MDIceTrace::clear_tradeuniqueid() {
  tradeuniqueid_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeUniqueId)
  return tradeuniqueid_;
}
inline void MDIceTrace::set_tradeuniqueid(::google::protobuf::int64 value) {
  
  tradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeUniqueId)
}

// optional int64 UpdateAction = 53;
inline void MDIceTrace::clear_updateaction() {
  updateaction_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::updateaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.UpdateAction)
  return updateaction_;
}
inline void MDIceTrace::set_updateaction(::google::protobuf::int64 value) {
  
  updateaction_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.UpdateAction)
}

// optional int64 YestTradeVol = 54;
inline void MDIceTrace::clear_yesttradevol() {
  yesttradevol_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::yesttradevol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YestTradeVol)
  return yesttradevol_;
}
inline void MDIceTrace::set_yesttradevol(::google::protobuf::int64 value) {
  
  yesttradevol_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YestTradeVol)
}

// optional double Vwap = 55;
inline void MDIceTrace::clear_vwap() {
  vwap_ = 0;
}
inline double MDIceTrace::vwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Vwap)
  return vwap_;
}
inline void MDIceTrace::set_vwap(double value) {
  
  vwap_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Vwap)
}

// optional int64 TradeCondOfficialTime = 56;
inline void MDIceTrace::clear_tradecondofficialtime() {
  tradecondofficialtime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradecondofficialtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialTime)
  return tradecondofficialtime_;
}
inline void MDIceTrace::set_tradecondofficialtime(::google::protobuf::int64 value) {
  
  tradecondofficialtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialTime)
}

// optional int64 TradeCondOfficialDate = 57;
inline void MDIceTrace::clear_tradecondofficialdate() {
  tradecondofficialdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tradecondofficialdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialDate)
  return tradecondofficialdate_;
}
inline void MDIceTrace::set_tradecondofficialdate(::google::protobuf::int64 value) {
  
  tradecondofficialdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCondOfficialDate)
}

// optional int64 CorrectionOfficialTradeTime = 58;
inline void MDIceTrace::clear_correctionofficialtradetime() {
  correctionofficialtradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionofficialtradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeTime)
  return correctionofficialtradetime_;
}
inline void MDIceTrace::set_correctionofficialtradetime(::google::protobuf::int64 value) {
  
  correctionofficialtradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeTime)
}

// optional int64 CorrectionOfficialTradeDate = 59;
inline void MDIceTrace::clear_correctionofficialtradedate() {
  correctionofficialtradedate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionofficialtradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeDate)
  return correctionofficialtradedate_;
}
inline void MDIceTrace::set_correctionofficialtradedate(::google::protobuf::int64 value) {
  
  correctionofficialtradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionOfficialTradeDate)
}

// optional int32 TransactionPriceInd = 60;
inline void MDIceTrace::clear_transactionpriceind() {
  transactionpriceind_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::transactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TransactionPriceInd)
  return transactionpriceind_;
}
inline void MDIceTrace::set_transactionpriceind(::google::protobuf::int32 value) {
  
  transactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TransactionPriceInd)
}

// optional double CouponRate = 61;
inline void MDIceTrace::clear_couponrate() {
  couponrate_ = 0;
}
inline double MDIceTrace::couponrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CouponRate)
  return couponrate_;
}
inline void MDIceTrace::set_couponrate(double value) {
  
  couponrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CouponRate)
}

// optional int32 InstrStatus = 62;
inline void MDIceTrace::clear_instrstatus() {
  instrstatus_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::instrstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrStatus)
  return instrstatus_;
}
inline void MDIceTrace::set_instrstatus(::google::protobuf::int32 value) {
  
  instrstatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrStatus)
}

// optional int32 MsgType = 63;
inline void MDIceTrace::clear_msgtype() {
  msgtype_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::msgtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MsgType)
  return msgtype_;
}
inline void MDIceTrace::set_msgtype(::google::protobuf::int32 value) {
  
  msgtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MsgType)
}

// optional int32 ControlMsgType = 64;
inline void MDIceTrace::clear_controlmsgtype() {
  controlmsgtype_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::controlmsgtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ControlMsgType)
  return controlmsgtype_;
}
inline void MDIceTrace::set_controlmsgtype(::google::protobuf::int32 value) {
  
  controlmsgtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ControlMsgType)
}

// optional int64 WeightedAverageMaturity = 65;
inline void MDIceTrace::clear_weightedaveragematurity() {
  weightedaveragematurity_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::weightedaveragematurity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageMaturity)
  return weightedaveragematurity_;
}
inline void MDIceTrace::set_weightedaveragematurity(::google::protobuf::int64 value) {
  
  weightedaveragematurity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageMaturity)
}

// optional int64 HistoricalTradeSize = 66;
inline void MDIceTrace::clear_historicaltradesize() {
  historicaltradesize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaltradesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeSize)
  return historicaltradesize_;
}
inline void MDIceTrace::set_historicaltradesize(::google::protobuf::int64 value) {
  
  historicaltradesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeSize)
}

// optional int64 HistoricalCancelSize = 67;
inline void MDIceTrace::clear_historicalcancelsize() {
  historicalcancelsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcancelsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelSize)
  return historicalcancelsize_;
}
inline void MDIceTrace::set_historicalcancelsize(::google::protobuf::int64 value) {
  
  historicalcancelsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelSize)
}

// optional int64 HistoricalCorrectionSize = 68;
inline void MDIceTrace::clear_historicalcorrectionsize() {
  historicalcorrectionsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcorrectionsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionSize)
  return historicalcorrectionsize_;
}
inline void MDIceTrace::set_historicalcorrectionsize(::google::protobuf::int64 value) {
  
  historicalcorrectionsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionSize)
}

// optional int64 HistoricalTradeCond = 69;
inline void MDIceTrace::clear_historicaltradecond() {
  historicaltradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaltradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeCond)
  return historicaltradecond_;
}
inline void MDIceTrace::set_historicaltradecond(::google::protobuf::int64 value) {
  
  historicaltradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeCond)
}

// optional double HistoricalTradePrice = 70;
inline void MDIceTrace::clear_historicaltradeprice() {
  historicaltradeprice_ = 0;
}
inline double MDIceTrace::historicaltradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradePrice)
  return historicaltradeprice_;
}
inline void MDIceTrace::set_historicaltradeprice(double value) {
  
  historicaltradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradePrice)
}

// optional double HistoricalCancelPrice = 71;
inline void MDIceTrace::clear_historicalcancelprice() {
  historicalcancelprice_ = 0;
}
inline double MDIceTrace::historicalcancelprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelPrice)
  return historicalcancelprice_;
}
inline void MDIceTrace::set_historicalcancelprice(double value) {
  
  historicalcancelprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelPrice)
}

// optional double HistoricalCorrectionPrice = 72;
inline void MDIceTrace::clear_historicalcorrectionprice() {
  historicalcorrectionprice_ = 0;
}
inline double MDIceTrace::historicalcorrectionprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrice)
  return historicalcorrectionprice_;
}
inline void MDIceTrace::set_historicalcorrectionprice(double value) {
  
  historicalcorrectionprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrice)
}

// optional int64 ActionTime = 73;
inline void MDIceTrace::clear_actiontime() {
  actiontime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::actiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActionTime)
  return actiontime_;
}
inline void MDIceTrace::set_actiontime(::google::protobuf::int64 value) {
  
  actiontime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActionTime)
}

// optional int64 ActionDate = 74;
inline void MDIceTrace::clear_actiondate() {
  actiondate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::actiondate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ActionDate)
  return actiondate_;
}
inline void MDIceTrace::set_actiondate(::google::protobuf::int64 value) {
  
  actiondate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ActionDate)
}

// optional int32 ReasonCode = 75;
inline void MDIceTrace::clear_reasoncode() {
  reasoncode_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::reasoncode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ReasonCode)
  return reasoncode_;
}
inline void MDIceTrace::set_reasoncode(::google::protobuf::int32 value) {
  
  reasoncode_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ReasonCode)
}

// optional int64 HistoricalTradeIdentifier = 76;
inline void MDIceTrace::clear_historicaltradeidentifier() {
  historicaltradeidentifier_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaltradeidentifier() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIdentifier)
  return historicaltradeidentifier_;
}
inline void MDIceTrace::set_historicaltradeidentifier(::google::protobuf::int64 value) {
  
  historicaltradeidentifier_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIdentifier)
}

// optional int64 HistoricalOriginalMessageDate = 77;
inline void MDIceTrace::clear_historicaloriginalmessagedate() {
  historicaloriginalmessagedate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaloriginalmessagedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalOriginalMessageDate)
  return historicaloriginalmessagedate_;
}
inline void MDIceTrace::set_historicaloriginalmessagedate(::google::protobuf::int64 value) {
  
  historicaloriginalmessagedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalOriginalMessageDate)
}

// optional int64 ExtendedTradeCond = 78;
inline void MDIceTrace::clear_extendedtradecond() {
  extendedtradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::extendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExtendedTradeCond)
  return extendedtradecond_;
}
inline void MDIceTrace::set_extendedtradecond(::google::protobuf::int64 value) {
  
  extendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExtendedTradeCond)
}

// optional double YieldHigh = 79;
inline void MDIceTrace::clear_yieldhigh() {
  yieldhigh_ = 0;
}
inline double MDIceTrace::yieldhigh() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldHigh)
  return yieldhigh_;
}
inline void MDIceTrace::set_yieldhigh(double value) {
  
  yieldhigh_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldHigh)
}

// optional double YieldLow = 80;
inline void MDIceTrace::clear_yieldlow() {
  yieldlow_ = 0;
}
inline double MDIceTrace::yieldlow() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldLow)
  return yieldlow_;
}
inline void MDIceTrace::set_yieldlow(double value) {
  
  yieldlow_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldLow)
}

// optional int64 HistoricalCancelTradeDate = 81;
inline void MDIceTrace::clear_historicalcanceltradedate() {
  historicalcanceltradedate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcanceltradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeDate)
  return historicalcanceltradedate_;
}
inline void MDIceTrace::set_historicalcanceltradedate(::google::protobuf::int64 value) {
  
  historicalcanceltradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeDate)
}

// optional double HistoricalYield = 82;
inline void MDIceTrace::clear_historicalyield() {
  historicalyield_ = 0;
}
inline double MDIceTrace::historicalyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalYield)
  return historicalyield_;
}
inline void MDIceTrace::set_historicalyield(double value) {
  
  historicalyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalYield)
}

// optional double HistoricalTradeIndicSize = 83;
inline void MDIceTrace::clear_historicaltradeindicsize() {
  historicaltradeindicsize_ = 0;
}
inline double MDIceTrace::historicaltradeindicsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIndicSize)
  return historicaltradeindicsize_;
}
inline void MDIceTrace::set_historicaltradeindicsize(double value) {
  
  historicaltradeindicsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeIndicSize)
}

// optional int64 HistoricalExtendedTradeCond = 84;
inline void MDIceTrace::clear_historicalextendedtradecond() {
  historicalextendedtradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalExtendedTradeCond)
  return historicalextendedtradecond_;
}
inline void MDIceTrace::set_historicalextendedtradecond(::google::protobuf::int64 value) {
  
  historicalextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalExtendedTradeCond)
}

// optional int32 HistoricalPriceIndicator = 85;
inline void MDIceTrace::clear_historicalpriceindicator() {
  historicalpriceindicator_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::historicalpriceindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalPriceIndicator)
  return historicalpriceindicator_;
}
inline void MDIceTrace::set_historicalpriceindicator(::google::protobuf::int32 value) {
  
  historicalpriceindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalPriceIndicator)
}

// optional string SymbolSuffix = 86;
inline void MDIceTrace::clear_symbolsuffix() {
  symbolsuffix_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::symbolsuffix() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  return symbolsuffix_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_symbolsuffix(const ::std::string& value) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
inline void MDIceTrace::set_symbolsuffix(const char* value) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
inline void MDIceTrace::set_symbolsuffix(const char* value, size_t size) {
  
  symbolsuffix_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}
inline ::std::string* MDIceTrace::mutable_symbolsuffix() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  return symbolsuffix_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_symbolsuffix() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
  
  return symbolsuffix_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_symbolsuffix(::std::string* symbolsuffix) {
  if (symbolsuffix != NULL) {
    
  } else {
    
  }
  symbolsuffix_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolsuffix);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolSuffix)
}

// optional int64 HistoricalTradeDate = 87;
inline void MDIceTrace::clear_historicaltradedate() {
  historicaltradedate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaltradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeDate)
  return historicaltradedate_;
}
inline void MDIceTrace::set_historicaltradedate(::google::protobuf::int64 value) {
  
  historicaltradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeDate)
}

// optional int64 HistoricalTradeTime = 88;
inline void MDIceTrace::clear_historicaltradetime() {
  historicaltradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicaltradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeTime)
  return historicaltradetime_;
}
inline void MDIceTrace::set_historicaltradetime(::google::protobuf::int64 value) {
  
  historicaltradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalTradeTime)
}

// optional int32 CorrectionPrevSpecialPriceIndicator = 89;
inline void MDIceTrace::clear_correctionprevspecialpriceindicator() {
  correctionprevspecialpriceindicator_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::correctionprevspecialpriceindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevSpecialPriceIndicator)
  return correctionprevspecialpriceindicator_;
}
inline void MDIceTrace::set_correctionprevspecialpriceindicator(::google::protobuf::int32 value) {
  
  correctionprevspecialpriceindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevSpecialPriceIndicator)
}

// optional int64 CorrectionNewExtendedTradeCond = 90;
inline void MDIceTrace::clear_correctionnewextendedtradecond() {
  correctionnewextendedtradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionnewextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewExtendedTradeCond)
  return correctionnewextendedtradecond_;
}
inline void MDIceTrace::set_correctionnewextendedtradecond(::google::protobuf::int64 value) {
  
  correctionnewextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewExtendedTradeCond)
}

// optional int64 CorrectionPrevExtendedTradeCond = 91;
inline void MDIceTrace::clear_correctionprevextendedtradecond() {
  correctionprevextendedtradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionprevextendedtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevExtendedTradeCond)
  return correctionprevextendedtradecond_;
}
inline void MDIceTrace::set_correctionprevextendedtradecond(::google::protobuf::int64 value) {
  
  correctionprevextendedtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevExtendedTradeCond)
}

// optional double CorrectionPrevYield = 92;
inline void MDIceTrace::clear_correctionprevyield() {
  correctionprevyield_ = 0;
}
inline double MDIceTrace::correctionprevyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevYield)
  return correctionprevyield_;
}
inline void MDIceTrace::set_correctionprevyield(double value) {
  
  correctionprevyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevYield)
}

// optional double CorrectionNewYield = 93;
inline void MDIceTrace::clear_correctionnewyield() {
  correctionnewyield_ = 0;
}
inline double MDIceTrace::correctionnewyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewYield)
  return correctionnewyield_;
}
inline void MDIceTrace::set_correctionnewyield(double value) {
  
  correctionnewyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewYield)
}

// optional string CorrectionPrevTradeDate = 94;
inline void MDIceTrace::clear_correctionprevtradedate() {
  correctionprevtradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::correctionprevtradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  return correctionprevtradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_correctionprevtradedate(const ::std::string& value) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
inline void MDIceTrace::set_correctionprevtradedate(const char* value) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
inline void MDIceTrace::set_correctionprevtradedate(const char* value, size_t size) {
  
  correctionprevtradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}
inline ::std::string* MDIceTrace::mutable_correctionprevtradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  return correctionprevtradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_correctionprevtradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
  
  return correctionprevtradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_correctionprevtradedate(::std::string* correctionprevtradedate) {
  if (correctionprevtradedate != NULL) {
    
  } else {
    
  }
  correctionprevtradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), correctionprevtradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeDate)
}

// optional int64 CorrectionPrevTradeTime = 95;
inline void MDIceTrace::clear_correctionprevtradetime() {
  correctionprevtradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionprevtradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeTime)
  return correctionprevtradetime_;
}
inline void MDIceTrace::set_correctionprevtradetime(::google::protobuf::int64 value) {
  
  correctionprevtradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeTime)
}

// optional int32 CorrectionPrevTradeCond = 96;
inline void MDIceTrace::clear_correctionprevtradecond() {
  correctionprevtradecond_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::correctionprevtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeCond)
  return correctionprevtradecond_;
}
inline void MDIceTrace::set_correctionprevtradecond(::google::protobuf::int32 value) {
  
  correctionprevtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeCond)
}

// optional int64 CorrectionNewTradeCond = 97;
inline void MDIceTrace::clear_correctionnewtradecond() {
  correctionnewtradecond_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionnewtradecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeCond)
  return correctionnewtradecond_;
}
inline void MDIceTrace::set_correctionnewtradecond(::google::protobuf::int64 value) {
  
  correctionnewtradecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTradeCond)
}

// optional int64 CancelTradeSeq = 98;
inline void MDIceTrace::clear_canceltradeseq() {
  canceltradeseq_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::canceltradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CancelTradeSeq)
  return canceltradeseq_;
}
inline void MDIceTrace::set_canceltradeseq(::google::protobuf::int64 value) {
  
  canceltradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CancelTradeSeq)
}

// optional int32 PrevTransactionPriceInd = 99;
inline void MDIceTrace::clear_prevtransactionpriceind() {
  prevtransactionpriceind_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::prevtransactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PrevTransactionPriceInd)
  return prevtransactionpriceind_;
}
inline void MDIceTrace::set_prevtransactionpriceind(::google::protobuf::int32 value) {
  
  prevtransactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PrevTransactionPriceInd)
}

// optional int64 CorrectionNewTransactionPriceInd = 100;
inline void MDIceTrace::clear_correctionnewtransactionpriceind() {
  correctionnewtransactionpriceind_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionnewtransactionpriceind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTransactionPriceInd)
  return correctionnewtransactionpriceind_;
}
inline void MDIceTrace::set_correctionnewtransactionpriceind(::google::protobuf::int64 value) {
  
  correctionnewtransactionpriceind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewTransactionPriceInd)
}

// optional int64 OriginalTradeSeq = 101;
inline void MDIceTrace::clear_originaltradeseq() {
  originaltradeseq_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::originaltradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.OriginalTradeSeq)
  return originaltradeseq_;
}
inline void MDIceTrace::set_originaltradeseq(::google::protobuf::int64 value) {
  
  originaltradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.OriginalTradeSeq)
}

// optional double YieldClose = 102;
inline void MDIceTrace::clear_yieldclose() {
  yieldclose_ = 0;
}
inline double MDIceTrace::yieldclose() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.YieldClose)
  return yieldclose_;
}
inline void MDIceTrace::set_yieldclose(double value) {
  
  yieldclose_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.YieldClose)
}

// optional int64 FirstSettlementDate = 103;
inline void MDIceTrace::clear_firstsettlementdate() {
  firstsettlementdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::firstsettlementdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.FirstSettlementDate)
  return firstsettlementdate_;
}
inline void MDIceTrace::set_firstsettlementdate(::google::protobuf::int64 value) {
  
  firstsettlementdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.FirstSettlementDate)
}

// optional int64 ReportDate = 104;
inline void MDIceTrace::clear_reportdate() {
  reportdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::reportdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ReportDate)
  return reportdate_;
}
inline void MDIceTrace::set_reportdate(::google::protobuf::int64 value) {
  
  reportdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ReportDate)
}

// optional int32 MarketPhase = 105;
inline void MDIceTrace::clear_marketphase() {
  marketphase_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::marketphase() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MarketPhase)
  return marketphase_;
}
inline void MDIceTrace::set_marketphase(::google::protobuf::int32 value) {
  
  marketphase_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MarketPhase)
}

// optional int64 DisseminationDate = 106;
inline void MDIceTrace::clear_disseminationdate() {
  disseminationdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::disseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DisseminationDate)
  return disseminationdate_;
}
inline void MDIceTrace::set_disseminationdate(::google::protobuf::int64 value) {
  
  disseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DisseminationDate)
}

// optional int64 CorrectionNewDisseminationDate = 107;
inline void MDIceTrace::clear_correctionnewdisseminationdate() {
  correctionnewdisseminationdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionnewdisseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewDisseminationDate)
  return correctionnewdisseminationdate_;
}
inline void MDIceTrace::set_correctionnewdisseminationdate(::google::protobuf::int64 value) {
  
  correctionnewdisseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionNewDisseminationDate)
}

// optional int64 CorrectionPrevDisseminationDate = 108;
inline void MDIceTrace::clear_correctionprevdisseminationdate() {
  correctionprevdisseminationdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionprevdisseminationdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevDisseminationDate)
  return correctionprevdisseminationdate_;
}
inline void MDIceTrace::set_correctionprevdisseminationdate(::google::protobuf::int64 value) {
  
  correctionprevdisseminationdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevDisseminationDate)
}

// optional int32 TradeCond1 = 109;
inline void MDIceTrace::clear_tradecond1() {
  tradecond1_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::tradecond1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeCond1)
  return tradecond1_;
}
inline void MDIceTrace::set_tradecond1(::google::protobuf::int32 value) {
  
  tradecond1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeCond1)
}

// optional int64 CorrectionPrevTradeUniqueId = 110;
inline void MDIceTrace::clear_correctionprevtradeuniqueid() {
  correctionprevtradeuniqueid_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctionprevtradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeUniqueId)
  return correctionprevtradeuniqueid_;
}
inline void MDIceTrace::set_correctionprevtradeuniqueid(::google::protobuf::int64 value) {
  
  correctionprevtradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionPrevTradeUniqueId)
}

// optional int64 HistoricalCorrectionPrevTradeUniqueId = 111;
inline void MDIceTrace::clear_historicalcorrectionprevtradeuniqueid() {
  historicalcorrectionprevtradeuniqueid_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcorrectionprevtradeuniqueid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrevTradeUniqueId)
  return historicalcorrectionprevtradeuniqueid_;
}
inline void MDIceTrace::set_historicalcorrectionprevtradeuniqueid(::google::protobuf::int64 value) {
  
  historicalcorrectionprevtradeuniqueid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionPrevTradeUniqueId)
}

// optional int64 HistoricalCancelTradeTime = 112;
inline void MDIceTrace::clear_historicalcanceltradetime() {
  historicalcanceltradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcanceltradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeTime)
  return historicalcanceltradetime_;
}
inline void MDIceTrace::set_historicalcanceltradetime(::google::protobuf::int64 value) {
  
  historicalcanceltradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCancelTradeTime)
}

// optional int64 HistoricalCorrectionTradeTime = 113;
inline void MDIceTrace::clear_historicalcorrectiontradetime() {
  historicalcorrectiontradetime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcorrectiontradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeTime)
  return historicalcorrectiontradetime_;
}
inline void MDIceTrace::set_historicalcorrectiontradetime(::google::protobuf::int64 value) {
  
  historicalcorrectiontradetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeTime)
}

// optional int64 HistoricalCorrectionTradeDate = 114;
inline void MDIceTrace::clear_historicalcorrectiontradedate() {
  historicalcorrectiontradedate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::historicalcorrectiontradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeDate)
  return historicalcorrectiontradedate_;
}
inline void MDIceTrace::set_historicalcorrectiontradedate(::google::protobuf::int64 value) {
  
  historicalcorrectiontradedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.HistoricalCorrectionTradeDate)
}

// optional int32 BondType = 115;
inline void MDIceTrace::clear_bondtype() {
  bondtype_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::bondtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BondType)
  return bondtype_;
}
inline void MDIceTrace::set_bondtype(::google::protobuf::int32 value) {
  
  bondtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BondType)
}

// optional double Yield = 117;
inline void MDIceTrace::clear_yield() {
  yield_ = 0;
}
inline double MDIceTrace::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Yield)
  return yield_;
}
inline void MDIceTrace::set_yield(double value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Yield)
}

// optional string Cusip = 118;
inline void MDIceTrace::clear_cusip() {
  cusip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::cusip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  return cusip_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_cusip(const ::std::string& value) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
inline void MDIceTrace::set_cusip(const char* value) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
inline void MDIceTrace::set_cusip(const char* value, size_t size) {
  
  cusip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}
inline ::std::string* MDIceTrace::mutable_cusip() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  return cusip_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_cusip() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
  
  return cusip_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_cusip(::std::string* cusip) {
  if (cusip != NULL) {
    
  } else {
    
  }
  cusip_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cusip);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.Cusip)
}

// optional string SymbolExchTicker = 119;
inline void MDIceTrace::clear_symbolexchticker() {
  symbolexchticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::symbolexchticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  return symbolexchticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_symbolexchticker(const ::std::string& value) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
inline void MDIceTrace::set_symbolexchticker(const char* value) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
inline void MDIceTrace::set_symbolexchticker(const char* value, size_t size) {
  
  symbolexchticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}
inline ::std::string* MDIceTrace::mutable_symbolexchticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  return symbolexchticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_symbolexchticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
  
  return symbolexchticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_symbolexchticker(::std::string* symbolexchticker) {
  if (symbolexchticker != NULL) {
    
  } else {
    
  }
  symbolexchticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolexchticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolExchTicker)
}

// optional string ISIN = 120;
inline void MDIceTrace::clear_isin() {
  isin_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::isin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  return isin_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_isin(const ::std::string& value) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
inline void MDIceTrace::set_isin(const char* value) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
inline void MDIceTrace::set_isin(const char* value, size_t size) {
  
  isin_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}
inline ::std::string* MDIceTrace::mutable_isin() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  return isin_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_isin() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
  
  return isin_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_isin(::std::string* isin) {
  if (isin != NULL) {
    
  } else {
    
  }
  isin_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), isin);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ISIN)
}

// optional string InstrName2 = 121;
inline void MDIceTrace::clear_instrname2() {
  instrname2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::instrname2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  return instrname2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_instrname2(const ::std::string& value) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
inline void MDIceTrace::set_instrname2(const char* value) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
inline void MDIceTrace::set_instrname2(const char* value, size_t size) {
  
  instrname2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}
inline ::std::string* MDIceTrace::mutable_instrname2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  return instrname2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_instrname2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
  
  return instrname2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_instrname2(::std::string* instrname2) {
  if (instrname2 != NULL) {
    
  } else {
    
  }
  instrname2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrname2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrName2)
}

// optional string TradeSettlementDate = 122;
inline void MDIceTrace::clear_tradesettlementdate() {
  tradesettlementdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::tradesettlementdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  return tradesettlementdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_tradesettlementdate(const ::std::string& value) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
inline void MDIceTrace::set_tradesettlementdate(const char* value) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
inline void MDIceTrace::set_tradesettlementdate(const char* value, size_t size) {
  
  tradesettlementdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}
inline ::std::string* MDIceTrace::mutable_tradesettlementdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  return tradesettlementdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_tradesettlementdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
  
  return tradesettlementdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_tradesettlementdate(::std::string* tradesettlementdate) {
  if (tradesettlementdate != NULL) {
    
  } else {
    
  }
  tradesettlementdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradesettlementdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TradeSettlementDate)
}

// optional int64 FirstTradingDate = 123;
inline void MDIceTrace::clear_firsttradingdate() {
  firsttradingdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::firsttradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.FirstTradingDate)
  return firsttradingdate_;
}
inline void MDIceTrace::set_firsttradingdate(::google::protobuf::int64 value) {
  
  firsttradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.FirstTradingDate)
}

// optional string CouponType = 124;
inline void MDIceTrace::clear_coupontype() {
  coupontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::coupontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  return coupontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_coupontype(const ::std::string& value) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
inline void MDIceTrace::set_coupontype(const char* value) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
inline void MDIceTrace::set_coupontype(const char* value, size_t size) {
  
  coupontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}
inline ::std::string* MDIceTrace::mutable_coupontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  return coupontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_coupontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
  
  return coupontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_coupontype(::std::string* coupontype) {
  if (coupontype != NULL) {
    
  } else {
    
  }
  coupontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), coupontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.CouponType)
}

// optional int64 EnumInterestCalcType = 125;
inline void MDIceTrace::clear_enuminterestcalctype() {
  enuminterestcalctype_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::enuminterestcalctype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.EnumInterestCalcType)
  return enuminterestcalctype_;
}
inline void MDIceTrace::set_enuminterestcalctype(::google::protobuf::int64 value) {
  
  enuminterestcalctype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.EnumInterestCalcType)
}

// optional string IssuerName = 126;
inline void MDIceTrace::clear_issuername() {
  issuername_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::issuername() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  return issuername_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_issuername(const ::std::string& value) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
inline void MDIceTrace::set_issuername(const char* value) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
inline void MDIceTrace::set_issuername(const char* value, size_t size) {
  
  issuername_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}
inline ::std::string* MDIceTrace::mutable_issuername() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  return issuername_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_issuername() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
  
  return issuername_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_issuername(::std::string* issuername) {
  if (issuername != NULL) {
    
  } else {
    
  }
  issuername_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), issuername);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.IssuerName)
}

// optional int32 InvestmentGrade = 127;
inline void MDIceTrace::clear_investmentgrade() {
  investmentgrade_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::investmentgrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InvestmentGrade)
  return investmentgrade_;
}
inline void MDIceTrace::set_investmentgrade(::google::protobuf::int32 value) {
  
  investmentgrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InvestmentGrade)
}

// optional string AmortizationType = 128;
inline void MDIceTrace::clear_amortizationtype() {
  amortizationtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::amortizationtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  return amortizationtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_amortizationtype(const ::std::string& value) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
inline void MDIceTrace::set_amortizationtype(const char* value) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
inline void MDIceTrace::set_amortizationtype(const char* value, size_t size) {
  
  amortizationtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}
inline ::std::string* MDIceTrace::mutable_amortizationtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  return amortizationtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_amortizationtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
  
  return amortizationtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_amortizationtype(::std::string* amortizationtype) {
  if (amortizationtype != NULL) {
    
  } else {
    
  }
  amortizationtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), amortizationtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.AmortizationType)
}

// optional string DisseminationFlag = 129;
inline void MDIceTrace::clear_disseminationflag() {
  disseminationflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::disseminationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  return disseminationflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_disseminationflag(const ::std::string& value) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
inline void MDIceTrace::set_disseminationflag(const char* value) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
inline void MDIceTrace::set_disseminationflag(const char* value, size_t size) {
  
  disseminationflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}
inline ::std::string* MDIceTrace::mutable_disseminationflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  return disseminationflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_disseminationflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
  
  return disseminationflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_disseminationflag(::std::string* disseminationflag) {
  if (disseminationflag != NULL) {
    
  } else {
    
  }
  disseminationflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), disseminationflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.DisseminationFlag)
}

// optional string SymbolBloombergTicker = 130;
inline void MDIceTrace::clear_symbolbloombergticker() {
  symbolbloombergticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::symbolbloombergticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  return symbolbloombergticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_symbolbloombergticker(const ::std::string& value) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
inline void MDIceTrace::set_symbolbloombergticker(const char* value) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
inline void MDIceTrace::set_symbolbloombergticker(const char* value, size_t size) {
  
  symbolbloombergticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}
inline ::std::string* MDIceTrace::mutable_symbolbloombergticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  return symbolbloombergticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_symbolbloombergticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
  
  return symbolbloombergticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_symbolbloombergticker(::std::string* symbolbloombergticker) {
  if (symbolbloombergticker != NULL) {
    
  } else {
    
  }
  symbolbloombergticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolbloombergticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolBloombergTicker)
}

// optional string BloombergGlobalId = 131;
inline void MDIceTrace::clear_bloombergglobalid() {
  bloombergglobalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::bloombergglobalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  return bloombergglobalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_bloombergglobalid(const ::std::string& value) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
inline void MDIceTrace::set_bloombergglobalid(const char* value) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
inline void MDIceTrace::set_bloombergglobalid(const char* value, size_t size) {
  
  bloombergglobalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}
inline ::std::string* MDIceTrace::mutable_bloombergglobalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  return bloombergglobalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_bloombergglobalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
  
  return bloombergglobalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_bloombergglobalid(::std::string* bloombergglobalid) {
  if (bloombergglobalid != NULL) {
    
  } else {
    
  }
  bloombergglobalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bloombergglobalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalId)
}

// optional string BloombergGlobalIdComp = 132;
inline void MDIceTrace::clear_bloombergglobalidcomp() {
  bloombergglobalidcomp_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::bloombergglobalidcomp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  return bloombergglobalidcomp_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_bloombergglobalidcomp(const ::std::string& value) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
inline void MDIceTrace::set_bloombergglobalidcomp(const char* value) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
inline void MDIceTrace::set_bloombergglobalidcomp(const char* value, size_t size) {
  
  bloombergglobalidcomp_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}
inline ::std::string* MDIceTrace::mutable_bloombergglobalidcomp() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  return bloombergglobalidcomp_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_bloombergglobalidcomp() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
  
  return bloombergglobalidcomp_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_bloombergglobalidcomp(::std::string* bloombergglobalidcomp) {
  if (bloombergglobalidcomp != NULL) {
    
  } else {
    
  }
  bloombergglobalidcomp_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bloombergglobalidcomp);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.BloombergGlobalIdComp)
}

// optional string InstrLocalType2 = 133;
inline void MDIceTrace::clear_instrlocaltype2() {
  instrlocaltype2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::instrlocaltype2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  return instrlocaltype2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_instrlocaltype2(const ::std::string& value) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
inline void MDIceTrace::set_instrlocaltype2(const char* value) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
inline void MDIceTrace::set_instrlocaltype2(const char* value, size_t size) {
  
  instrlocaltype2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}
inline ::std::string* MDIceTrace::mutable_instrlocaltype2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  return instrlocaltype2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_instrlocaltype2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
  
  return instrlocaltype2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_instrlocaltype2(::std::string* instrlocaltype2) {
  if (instrlocaltype2 != NULL) {
    
  } else {
    
  }
  instrlocaltype2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrlocaltype2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrLocalType2)
}

// optional string SymbolEsignalTicker = 134;
inline void MDIceTrace::clear_symbolesignalticker() {
  symbolesignalticker_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::symbolesignalticker() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  return symbolesignalticker_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_symbolesignalticker(const ::std::string& value) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
inline void MDIceTrace::set_symbolesignalticker(const char* value) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
inline void MDIceTrace::set_symbolesignalticker(const char* value, size_t size) {
  
  symbolesignalticker_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}
inline ::std::string* MDIceTrace::mutable_symbolesignalticker() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  return symbolesignalticker_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_symbolesignalticker() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
  
  return symbolesignalticker_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_symbolesignalticker(::std::string* symbolesignalticker) {
  if (symbolesignalticker != NULL) {
    
  } else {
    
  }
  symbolesignalticker_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbolesignalticker);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.SymbolEsignalTicker)
}

// optional string InstrNameLocal = 135;
inline void MDIceTrace::clear_instrnamelocal() {
  instrnamelocal_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::instrnamelocal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  return instrnamelocal_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_instrnamelocal(const ::std::string& value) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
inline void MDIceTrace::set_instrnamelocal(const char* value) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
inline void MDIceTrace::set_instrnamelocal(const char* value, size_t size) {
  
  instrnamelocal_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}
inline ::std::string* MDIceTrace::mutable_instrnamelocal() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  return instrnamelocal_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_instrnamelocal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
  
  return instrnamelocal_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_instrnamelocal(::std::string* instrnamelocal) {
  if (instrnamelocal != NULL) {
    
  } else {
    
  }
  instrnamelocal_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrnamelocal);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.InstrNameLocal)
}

// optional string MktSegmentString = 136;
inline void MDIceTrace::clear_mktsegmentstring() {
  mktsegmentstring_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::mktsegmentstring() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  return mktsegmentstring_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_mktsegmentstring(const ::std::string& value) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
inline void MDIceTrace::set_mktsegmentstring(const char* value) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
inline void MDIceTrace::set_mktsegmentstring(const char* value, size_t size) {
  
  mktsegmentstring_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}
inline ::std::string* MDIceTrace::mutable_mktsegmentstring() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  return mktsegmentstring_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_mktsegmentstring() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
  
  return mktsegmentstring_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_mktsegmentstring(::std::string* mktsegmentstring) {
  if (mktsegmentstring != NULL) {
    
  } else {
    
  }
  mktsegmentstring_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mktsegmentstring);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.MktSegmentString)
}

// optional string ExchMonthCode = 137;
inline void MDIceTrace::clear_exchmonthcode() {
  exchmonthcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::exchmonthcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  return exchmonthcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_exchmonthcode(const ::std::string& value) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
inline void MDIceTrace::set_exchmonthcode(const char* value) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
inline void MDIceTrace::set_exchmonthcode(const char* value, size_t size) {
  
  exchmonthcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}
inline ::std::string* MDIceTrace::mutable_exchmonthcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  return exchmonthcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_exchmonthcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
  
  return exchmonthcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_exchmonthcode(::std::string* exchmonthcode) {
  if (exchmonthcode != NULL) {
    
  } else {
    
  }
  exchmonthcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchmonthcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.ExchMonthCode)
}

// optional int64 PoolNumber = 138;
inline void MDIceTrace::clear_poolnumber() {
  poolnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::poolnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PoolNumber)
  return poolnumber_;
}
inline void MDIceTrace::set_poolnumber(::google::protobuf::int64 value) {
  
  poolnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PoolNumber)
}

// optional string MasterDealId = 139;
inline void MDIceTrace::clear_masterdealid() {
  masterdealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::masterdealid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  return masterdealid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_masterdealid(const ::std::string& value) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
inline void MDIceTrace::set_masterdealid(const char* value) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
inline void MDIceTrace::set_masterdealid(const char* value, size_t size) {
  
  masterdealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}
inline ::std::string* MDIceTrace::mutable_masterdealid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  return masterdealid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_masterdealid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
  
  return masterdealid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_masterdealid(::std::string* masterdealid) {
  if (masterdealid != NULL) {
    
  } else {
    
  }
  masterdealid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), masterdealid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.MasterDealId)
}

// optional string TrancheId = 140;
inline void MDIceTrace::clear_trancheid() {
  trancheid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::trancheid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  return trancheid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_trancheid(const ::std::string& value) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
inline void MDIceTrace::set_trancheid(const char* value) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
inline void MDIceTrace::set_trancheid(const char* value, size_t size) {
  
  trancheid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}
inline ::std::string* MDIceTrace::mutable_trancheid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  return trancheid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_trancheid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
  
  return trancheid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_trancheid(::std::string* trancheid) {
  if (trancheid != NULL) {
    
  } else {
    
  }
  trancheid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), trancheid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.TrancheId)
}

// optional string Indicator144A = 141;
inline void MDIceTrace::clear_indicator144a() {
  indicator144a_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::indicator144a() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  return indicator144a_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_indicator144a(const ::std::string& value) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
inline void MDIceTrace::set_indicator144a(const char* value) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
inline void MDIceTrace::set_indicator144a(const char* value, size_t size) {
  
  indicator144a_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}
inline ::std::string* MDIceTrace::mutable_indicator144a() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  return indicator144a_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_indicator144a() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
  
  return indicator144a_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_indicator144a(::std::string* indicator144a) {
  if (indicator144a != NULL) {
    
  } else {
    
  }
  indicator144a_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), indicator144a);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.Indicator144A)
}

// optional int64 NumberMaturityMonths = 142;
inline void MDIceTrace::clear_numbermaturitymonths() {
  numbermaturitymonths_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::numbermaturitymonths() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.NumberMaturityMonths)
  return numbermaturitymonths_;
}
inline void MDIceTrace::set_numbermaturitymonths(::google::protobuf::int64 value) {
  
  numbermaturitymonths_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.NumberMaturityMonths)
}

// optional string DebtTypeCode = 143;
inline void MDIceTrace::clear_debttypecode() {
  debttypecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDIceTrace::debttypecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  return debttypecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_debttypecode(const ::std::string& value) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
inline void MDIceTrace::set_debttypecode(const char* value) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
inline void MDIceTrace::set_debttypecode(const char* value, size_t size) {
  
  debttypecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}
inline ::std::string* MDIceTrace::mutable_debttypecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  return debttypecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDIceTrace::release_debttypecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
  
  return debttypecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDIceTrace::set_allocated_debttypecode(::std::string* debttypecode) {
  if (debttypecode != NULL) {
    
  } else {
    
  }
  debttypecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), debttypecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDIceTrace.DebtTypeCode)
}

// optional int64 TokenDel = 144;
inline void MDIceTrace::clear_tokendel() {
  tokendel_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::tokendel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.TokenDel)
  return tokendel_;
}
inline void MDIceTrace::set_tokendel(::google::protobuf::int64 value) {
  
  tokendel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.TokenDel)
}

// optional int64 CorrectionTradeSeq = 145;
inline void MDIceTrace::clear_correctiontradeseq() {
  correctiontradeseq_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::correctiontradeseq() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.CorrectionTradeSeq)
  return correctiontradeseq_;
}
inline void MDIceTrace::set_correctiontradeseq(::google::protobuf::int64 value) {
  
  correctiontradeseq_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.CorrectionTradeSeq)
}

// optional int32 RecordStaleInd = 146;
inline void MDIceTrace::clear_recordstaleind() {
  recordstaleind_ = 0;
}
inline ::google::protobuf::int32 MDIceTrace::recordstaleind() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.RecordStaleInd)
  return recordstaleind_;
}
inline void MDIceTrace::set_recordstaleind(::google::protobuf::int32 value) {
  
  recordstaleind_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.RecordStaleInd)
}

// optional double WeightedAverageCoupon = 147;
inline void MDIceTrace::clear_weightedaveragecoupon() {
  weightedaveragecoupon_ = 0;
}
inline double MDIceTrace::weightedaveragecoupon() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageCoupon)
  return weightedaveragecoupon_;
}
inline void MDIceTrace::set_weightedaveragecoupon(double value) {
  
  weightedaveragecoupon_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageCoupon)
}

// optional int64 WeightedAverageLoanAge = 148;
inline void MDIceTrace::clear_weightedaverageloanage() {
  weightedaverageloanage_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::weightedaverageloanage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanAge)
  return weightedaverageloanage_;
}
inline void MDIceTrace::set_weightedaverageloanage(::google::protobuf::int64 value) {
  
  weightedaverageloanage_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanAge)
}

// optional int64 WeightedAverageLoanSize = 149;
inline void MDIceTrace::clear_weightedaverageloansize() {
  weightedaverageloansize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::weightedaverageloansize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanSize)
  return weightedaverageloansize_;
}
inline void MDIceTrace::set_weightedaverageloansize(::google::protobuf::int64 value) {
  
  weightedaverageloansize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedAverageLoanSize)
}

// optional int64 WeightedLoanValue = 150;
inline void MDIceTrace::clear_weightedloanvalue() {
  weightedloanvalue_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDIceTrace::weightedloanvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.WeightedLoanValue)
  return weightedloanvalue_;
}
inline void MDIceTrace::set_weightedloanvalue(::google::protobuf::int64 value) {
  
  weightedloanvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.WeightedLoanValue)
}

// optional double AverageMonthlySize = 151;
inline void MDIceTrace::clear_averagemonthlysize() {
  averagemonthlysize_ = 0;
}
inline double MDIceTrace::averagemonthlysize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.AverageMonthlySize)
  return averagemonthlysize_;
}
inline void MDIceTrace::set_averagemonthlysize(double value) {
  
  averagemonthlysize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.AverageMonthlySize)
}

// optional double PrevMonthVolDec = 152;
inline void MDIceTrace::clear_prevmonthvoldec() {
  prevmonthvoldec_ = 0;
}
inline double MDIceTrace::prevmonthvoldec() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDIceTrace.PrevMonthVolDec)
  return prevmonthvoldec_;
}
inline void MDIceTrace::set_prevmonthvoldec(double value) {
  
  prevmonthvoldec_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDIceTrace.PrevMonthVolDec)
}

inline const MDIceTrace* MDIceTrace::internal_default_instance() {
  return &MDIceTrace_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDIceTrace_2eproto__INCLUDED
