// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBondDeal.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsBondDeal.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsBondDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsBondDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* CashBondTradingDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CashBondTradingDeal_reflection_ = NULL;
const ::google::protobuf::Descriptor* BondLendingDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BondLendingDeal_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsBondDeal_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsBondDeal_2eproto() {
  protobuf_AddDesc_MDCfetsBondDeal_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsBondDeal.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsBondDeal_descriptor_ = file->message_type(0);
  static const int MDCfetsBondDeal_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, messagenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, bonddealtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, cashbondtradingdeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, bondlendingdeal_),
  };
  MDCfetsBondDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsBondDeal_descriptor_,
      MDCfetsBondDeal::internal_default_instance(),
      MDCfetsBondDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsBondDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondDeal, _internal_metadata_));
  CashBondTradingDeal_descriptor_ = file->message_type(1);
  static const int CashBondTradingDeal_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, price_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, yield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, lastqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, premarketbondindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, transactionmethod_),
  };
  CashBondTradingDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CashBondTradingDeal_descriptor_,
      CashBondTradingDeal::internal_default_instance(),
      CashBondTradingDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(CashBondTradingDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingDeal, _internal_metadata_));
  BondLendingDeal_descriptor_ = file->message_type(2);
  static const int BondLendingDeal_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, price_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, underlyingsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, underlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, underlyingqty_),
  };
  BondLendingDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BondLendingDeal_descriptor_,
      BondLendingDeal::internal_default_instance(),
      BondLendingDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(BondLendingDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingDeal, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsBondDeal_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsBondDeal_descriptor_, MDCfetsBondDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CashBondTradingDeal_descriptor_, CashBondTradingDeal::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BondLendingDeal_descriptor_, BondLendingDeal::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsBondDeal_2eproto() {
  MDCfetsBondDeal_default_instance_.Shutdown();
  delete MDCfetsBondDeal_reflection_;
  CashBondTradingDeal_default_instance_.Shutdown();
  delete CashBondTradingDeal_reflection_;
  BondLendingDeal_default_instance_.Shutdown();
  delete BondLendingDeal_reflection_;
}

void protobuf_InitDefaults_MDCfetsBondDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsBondDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  CashBondTradingDeal_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BondLendingDeal_default_instance_.DefaultConstruct();
  MDCfetsBondDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  CashBondTradingDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
  BondLendingDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsBondDeal_2eproto_once_);
void protobuf_InitDefaults_MDCfetsBondDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsBondDeal_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsBondDeal_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsBondDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025MDCfetsBondDeal.proto\022\032com.htsc.mdc.in"
    "sight.model\032\027ESecurityIDSource.proto\032\023ES"
    "ecurityType.proto\"\351\003\n\017MDCfetsBondDeal\022\026\n"
    "\016HTSCSecurityID\030\001 \001(\t\0227\n\014SecurityType\030\002 "
    "\001(\0162!.com.htsc.mdc.model.ESecurityType\022\?"
    "\n\020SecurityIDSource\030\003 \001(\0162%.com.htsc.mdc."
    "model.ESecurityIDSource\022\016\n\006MDDate\030\004 \001(\005\022"
    "\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp\030\006 \001(\003\022\024"
    "\n\014TransactTime\030\007 \001(\t\022\027\n\017MarketIndicator\030"
    "\010 \001(\t\022\035\n\025DataMultiplePowerOf10\030\t \001(\005\022\025\n\r"
    "MessageNumber\030\020 \001(\003\022\024\n\014BondDealType\030\025 \001("
    "\005\022L\n\023CashBondTradingDeal\030\026 \001(\0132/.com.hts"
    "c.mdc.insight.model.CashBondTradingDeal\022"
    "D\n\017BondLendingDeal\030\027 \001(\0132+.com.htsc.mdc."
    "insight.model.BondLendingDeal\"\272\001\n\023CashBo"
    "ndTradingDeal\022\021\n\tTradeDate\030\001 \001(\t\022\021\n\tTrad"
    "eTime\030\002 \001(\t\022\r\n\005Price\030\003 \001(\001\022\r\n\005Yield\030\004 \001("
    "\001\022\017\n\007LastQty\030\005 \001(\003\022\036\n\026PreMarketBondIndic"
    "ator\030\006 \001(\010\022\023\n\013TradeMethod\030\007 \001(\005\022\031\n\021Trans"
    "actionMethod\030\010 \001(\t\"\225\001\n\017BondLendingDeal\022\021"
    "\n\tTradeDate\030\001 \001(\t\022\021\n\tTradeTime\030\002 \001(\t\022\r\n\005"
    "Price\030\003 \001(\001\022\030\n\020UnderlyingSymbol\030\004 \001(\t\022\034\n"
    "\024UnderlyingSecurityID\030\005 \001(\t\022\025\n\rUnderlyin"
    "gQty\030\006 \001(\001B8\n\032com.htsc.mdc.insight.model"
    "B\025MDCfetsBondDealProtosH\001\240\001\001b\006proto3", 996);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsBondDeal.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsBondDeal_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsBondDeal_2eproto_once_);
void protobuf_AddDesc_MDCfetsBondDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsBondDeal_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsBondDeal_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsBondDeal_2eproto {
  StaticDescriptorInitializer_MDCfetsBondDeal_2eproto() {
    protobuf_AddDesc_MDCfetsBondDeal_2eproto();
  }
} static_descriptor_initializer_MDCfetsBondDeal_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsBondDeal::kHTSCSecurityIDFieldNumber;
const int MDCfetsBondDeal::kSecurityTypeFieldNumber;
const int MDCfetsBondDeal::kSecurityIDSourceFieldNumber;
const int MDCfetsBondDeal::kMDDateFieldNumber;
const int MDCfetsBondDeal::kMDTimeFieldNumber;
const int MDCfetsBondDeal::kDataTimestampFieldNumber;
const int MDCfetsBondDeal::kTransactTimeFieldNumber;
const int MDCfetsBondDeal::kMarketIndicatorFieldNumber;
const int MDCfetsBondDeal::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsBondDeal::kMessageNumberFieldNumber;
const int MDCfetsBondDeal::kBondDealTypeFieldNumber;
const int MDCfetsBondDeal::kCashBondTradingDealFieldNumber;
const int MDCfetsBondDeal::kBondLendingDealFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsBondDeal::MDCfetsBondDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsBondDeal)
}

void MDCfetsBondDeal::InitAsDefaultInstance() {
  cashbondtradingdeal_ = const_cast< ::com::htsc::mdc::insight::model::CashBondTradingDeal*>(
      ::com::htsc::mdc::insight::model::CashBondTradingDeal::internal_default_instance());
  bondlendingdeal_ = const_cast< ::com::htsc::mdc::insight::model::BondLendingDeal*>(
      ::com::htsc::mdc::insight::model::BondLendingDeal::internal_default_instance());
}

MDCfetsBondDeal::MDCfetsBondDeal(const MDCfetsBondDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsBondDeal)
}

void MDCfetsBondDeal::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cashbondtradingdeal_ = NULL;
  bondlendingdeal_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&bonddealtype_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(bonddealtype_));
  _cached_size_ = 0;
}

MDCfetsBondDeal::~MDCfetsBondDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  SharedDtor();
}

void MDCfetsBondDeal::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsBondDeal_default_instance_.get()) {
    delete cashbondtradingdeal_;
    delete bondlendingdeal_;
  }
}

void MDCfetsBondDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsBondDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsBondDeal_descriptor_;
}

const MDCfetsBondDeal& MDCfetsBondDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBondDeal> MDCfetsBondDeal_default_instance_;

MDCfetsBondDeal* MDCfetsBondDeal::New(::google::protobuf::Arena* arena) const {
  MDCfetsBondDeal* n = new MDCfetsBondDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsBondDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsBondDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsBondDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(messagenumber_, bonddealtype_);
  if (GetArenaNoVirtual() == NULL && cashbondtradingdeal_ != NULL) delete cashbondtradingdeal_;
  cashbondtradingdeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && bondlendingdeal_ != NULL) delete bondlendingdeal_;
  bondlendingdeal_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsBondDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 16;
      case 16: {
        if (tag == 128) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_BondDealType;
        break;
      }

      // optional int32 BondDealType = 21;
      case 21: {
        if (tag == 168) {
         parse_BondDealType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bonddealtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_CashBondTradingDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
      case 22: {
        if (tag == 178) {
         parse_CashBondTradingDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_cashbondtradingdeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_BondLendingDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
      case 23: {
        if (tag == 186) {
         parse_BondLendingDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondlendingdeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  return false;
#undef DO_
}

void MDCfetsBondDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->messagenumber(), output);
  }

  // optional int32 BondDealType = 21;
  if (this->bonddealtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(21, this->bonddealtype(), output);
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
  if (this->has_cashbondtradingdeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, *this->cashbondtradingdeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
  if (this->has_bondlendingdeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, *this->bondlendingdeal_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsBondDeal)
}

::google::protobuf::uint8* MDCfetsBondDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->messagenumber(), target);
  }

  // optional int32 BondDealType = 21;
  if (this->bonddealtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(21, this->bonddealtype(), target);
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
  if (this->has_cashbondtradingdeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, *this->cashbondtradingdeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
  if (this->has_bondlendingdeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, *this->bondlendingdeal_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  return target;
}

size_t MDCfetsBondDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 MessageNumber = 16;
  if (this->messagenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  // optional int32 BondDealType = 21;
  if (this->bonddealtype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bonddealtype());
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
  if (this->has_cashbondtradingdeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->cashbondtradingdeal_);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
  if (this->has_bondlendingdeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondlendingdeal_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsBondDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsBondDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsBondDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsBondDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsBondDeal)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsBondDeal::MergeFrom(const MDCfetsBondDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsBondDeal::UnsafeMergeFrom(const MDCfetsBondDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
  if (from.bonddealtype() != 0) {
    set_bonddealtype(from.bonddealtype());
  }
  if (from.has_cashbondtradingdeal()) {
    mutable_cashbondtradingdeal()->::com::htsc::mdc::insight::model::CashBondTradingDeal::MergeFrom(from.cashbondtradingdeal());
  }
  if (from.has_bondlendingdeal()) {
    mutable_bondlendingdeal()->::com::htsc::mdc::insight::model::BondLendingDeal::MergeFrom(from.bondlendingdeal());
  }
}

void MDCfetsBondDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsBondDeal::CopyFrom(const MDCfetsBondDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBondDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsBondDeal::IsInitialized() const {

  return true;
}

void MDCfetsBondDeal::Swap(MDCfetsBondDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsBondDeal::InternalSwap(MDCfetsBondDeal* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(messagenumber_, other->messagenumber_);
  std::swap(bonddealtype_, other->bonddealtype_);
  std::swap(cashbondtradingdeal_, other->cashbondtradingdeal_);
  std::swap(bondlendingdeal_, other->bondlendingdeal_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsBondDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsBondDeal_descriptor_;
  metadata.reflection = MDCfetsBondDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBondDeal

// optional string HTSCSecurityID = 1;
void MDCfetsBondDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
void MDCfetsBondDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
void MDCfetsBondDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
::std::string* MDCfetsBondDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsBondDeal::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsBondDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsBondDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsBondDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsBondDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsBondDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsBondDeal::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsBondDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDDate)
  return mddate_;
}
void MDCfetsBondDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsBondDeal::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsBondDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDTime)
  return mdtime_;
}
void MDCfetsBondDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsBondDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBondDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsBondDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsBondDeal::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondDeal::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
void MDCfetsBondDeal::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
void MDCfetsBondDeal::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
::std::string* MDCfetsBondDeal::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondDeal::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsBondDeal::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondDeal::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
void MDCfetsBondDeal::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
void MDCfetsBondDeal::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
::std::string* MDCfetsBondDeal::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondDeal::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondDeal::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDCfetsBondDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsBondDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsBondDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 16;
void MDCfetsBondDeal::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBondDeal::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MessageNumber)
  return messagenumber_;
}
void MDCfetsBondDeal::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MessageNumber)
}

// optional int32 BondDealType = 21;
void MDCfetsBondDeal::clear_bonddealtype() {
  bonddealtype_ = 0;
}
::google::protobuf::int32 MDCfetsBondDeal::bonddealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondDealType)
  return bonddealtype_;
}
void MDCfetsBondDeal::set_bonddealtype(::google::protobuf::int32 value) {
  
  bonddealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondDealType)
}

// optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
bool MDCfetsBondDeal::has_cashbondtradingdeal() const {
  return this != internal_default_instance() && cashbondtradingdeal_ != NULL;
}
void MDCfetsBondDeal::clear_cashbondtradingdeal() {
  if (GetArenaNoVirtual() == NULL && cashbondtradingdeal_ != NULL) delete cashbondtradingdeal_;
  cashbondtradingdeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::CashBondTradingDeal& MDCfetsBondDeal::cashbondtradingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  return cashbondtradingdeal_ != NULL ? *cashbondtradingdeal_
                         : *::com::htsc::mdc::insight::model::CashBondTradingDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::CashBondTradingDeal* MDCfetsBondDeal::mutable_cashbondtradingdeal() {
  
  if (cashbondtradingdeal_ == NULL) {
    cashbondtradingdeal_ = new ::com::htsc::mdc::insight::model::CashBondTradingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  return cashbondtradingdeal_;
}
::com::htsc::mdc::insight::model::CashBondTradingDeal* MDCfetsBondDeal::release_cashbondtradingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  
  ::com::htsc::mdc::insight::model::CashBondTradingDeal* temp = cashbondtradingdeal_;
  cashbondtradingdeal_ = NULL;
  return temp;
}
void MDCfetsBondDeal::set_allocated_cashbondtradingdeal(::com::htsc::mdc::insight::model::CashBondTradingDeal* cashbondtradingdeal) {
  delete cashbondtradingdeal_;
  cashbondtradingdeal_ = cashbondtradingdeal;
  if (cashbondtradingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
}

// optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
bool MDCfetsBondDeal::has_bondlendingdeal() const {
  return this != internal_default_instance() && bondlendingdeal_ != NULL;
}
void MDCfetsBondDeal::clear_bondlendingdeal() {
  if (GetArenaNoVirtual() == NULL && bondlendingdeal_ != NULL) delete bondlendingdeal_;
  bondlendingdeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::BondLendingDeal& MDCfetsBondDeal::bondlendingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  return bondlendingdeal_ != NULL ? *bondlendingdeal_
                         : *::com::htsc::mdc::insight::model::BondLendingDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::BondLendingDeal* MDCfetsBondDeal::mutable_bondlendingdeal() {
  
  if (bondlendingdeal_ == NULL) {
    bondlendingdeal_ = new ::com::htsc::mdc::insight::model::BondLendingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  return bondlendingdeal_;
}
::com::htsc::mdc::insight::model::BondLendingDeal* MDCfetsBondDeal::release_bondlendingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  
  ::com::htsc::mdc::insight::model::BondLendingDeal* temp = bondlendingdeal_;
  bondlendingdeal_ = NULL;
  return temp;
}
void MDCfetsBondDeal::set_allocated_bondlendingdeal(::com::htsc::mdc::insight::model::BondLendingDeal* bondlendingdeal) {
  delete bondlendingdeal_;
  bondlendingdeal_ = bondlendingdeal;
  if (bondlendingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
}

inline const MDCfetsBondDeal* MDCfetsBondDeal::internal_default_instance() {
  return &MDCfetsBondDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CashBondTradingDeal::kTradeDateFieldNumber;
const int CashBondTradingDeal::kTradeTimeFieldNumber;
const int CashBondTradingDeal::kPriceFieldNumber;
const int CashBondTradingDeal::kYieldFieldNumber;
const int CashBondTradingDeal::kLastQtyFieldNumber;
const int CashBondTradingDeal::kPreMarketBondIndicatorFieldNumber;
const int CashBondTradingDeal::kTradeMethodFieldNumber;
const int CashBondTradingDeal::kTransactionMethodFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CashBondTradingDeal::CashBondTradingDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.CashBondTradingDeal)
}

void CashBondTradingDeal::InitAsDefaultInstance() {
}

CashBondTradingDeal::CashBondTradingDeal(const CashBondTradingDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.CashBondTradingDeal)
}

void CashBondTradingDeal::SharedCtor() {
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactionmethod_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&price_, 0, reinterpret_cast<char*>(&trademethod_) -
    reinterpret_cast<char*>(&price_) + sizeof(trademethod_));
  _cached_size_ = 0;
}

CashBondTradingDeal::~CashBondTradingDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.CashBondTradingDeal)
  SharedDtor();
}

void CashBondTradingDeal::SharedDtor() {
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactionmethod_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CashBondTradingDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CashBondTradingDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CashBondTradingDeal_descriptor_;
}

const CashBondTradingDeal& CashBondTradingDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CashBondTradingDeal> CashBondTradingDeal_default_instance_;

CashBondTradingDeal* CashBondTradingDeal::New(::google::protobuf::Arena* arena) const {
  CashBondTradingDeal* n = new CashBondTradingDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CashBondTradingDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(CashBondTradingDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<CashBondTradingDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(price_, trademethod_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactionmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool CashBondTradingDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TradeDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_Price;
        break;
      }

      // optional double Price = 3;
      case 3: {
        if (tag == 25) {
         parse_Price:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &price_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_Yield;
        break;
      }

      // optional double Yield = 4;
      case 4: {
        if (tag == 33) {
         parse_Yield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_LastQty;
        break;
      }

      // optional int64 LastQty = 5;
      case 5: {
        if (tag == 40) {
         parse_LastQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_PreMarketBondIndicator;
        break;
      }

      // optional bool PreMarketBondIndicator = 6;
      case 6: {
        if (tag == 48) {
         parse_PreMarketBondIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &premarketbondindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_TradeMethod;
        break;
      }

      // optional int32 TradeMethod = 7;
      case 7: {
        if (tag == 56) {
         parse_TradeMethod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_TransactionMethod;
        break;
      }

      // optional string TransactionMethod = 8;
      case 8: {
        if (tag == 66) {
         parse_TransactionMethod:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transactionmethod()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transactionmethod().data(), this->transactionmethod().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.CashBondTradingDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.CashBondTradingDeal)
  return false;
#undef DO_
}

void CashBondTradingDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tradedate(), output);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradetime(), output);
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->price(), output);
  }

  // optional double Yield = 4;
  if (this->yield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->yield(), output);
  }

  // optional int64 LastQty = 5;
  if (this->lastqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->lastqty(), output);
  }

  // optional bool PreMarketBondIndicator = 6;
  if (this->premarketbondindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(6, this->premarketbondindicator(), output);
  }

  // optional int32 TradeMethod = 7;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->trademethod(), output);
  }

  // optional string TransactionMethod = 8;
  if (this->transactionmethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactionmethod().data(), this->transactionmethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->transactionmethod(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.CashBondTradingDeal)
}

::google::protobuf::uint8* CashBondTradingDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tradedate(), target);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradetime(), target);
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->price(), target);
  }

  // optional double Yield = 4;
  if (this->yield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->yield(), target);
  }

  // optional int64 LastQty = 5;
  if (this->lastqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->lastqty(), target);
  }

  // optional bool PreMarketBondIndicator = 6;
  if (this->premarketbondindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(6, this->premarketbondindicator(), target);
  }

  // optional int32 TradeMethod = 7;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->trademethod(), target);
  }

  // optional string TransactionMethod = 8;
  if (this->transactionmethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactionmethod().data(), this->transactionmethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->transactionmethod(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.CashBondTradingDeal)
  return target;
}

size_t CashBondTradingDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  size_t total_size = 0;

  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    total_size += 1 + 8;
  }

  // optional double Yield = 4;
  if (this->yield() != 0) {
    total_size += 1 + 8;
  }

  // optional int64 LastQty = 5;
  if (this->lastqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastqty());
  }

  // optional bool PreMarketBondIndicator = 6;
  if (this->premarketbondindicator() != 0) {
    total_size += 1 + 1;
  }

  // optional int32 TradeMethod = 7;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional string TransactionMethod = 8;
  if (this->transactionmethod().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transactionmethod());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CashBondTradingDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CashBondTradingDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CashBondTradingDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.CashBondTradingDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.CashBondTradingDeal)
    UnsafeMergeFrom(*source);
  }
}

void CashBondTradingDeal::MergeFrom(const CashBondTradingDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CashBondTradingDeal::UnsafeMergeFrom(const CashBondTradingDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.price() != 0) {
    set_price(from.price());
  }
  if (from.yield() != 0) {
    set_yield(from.yield());
  }
  if (from.lastqty() != 0) {
    set_lastqty(from.lastqty());
  }
  if (from.premarketbondindicator() != 0) {
    set_premarketbondindicator(from.premarketbondindicator());
  }
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.transactionmethod().size() > 0) {

    transactionmethod_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transactionmethod_);
  }
}

void CashBondTradingDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CashBondTradingDeal::CopyFrom(const CashBondTradingDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.CashBondTradingDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CashBondTradingDeal::IsInitialized() const {

  return true;
}

void CashBondTradingDeal::Swap(CashBondTradingDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CashBondTradingDeal::InternalSwap(CashBondTradingDeal* other) {
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(price_, other->price_);
  std::swap(yield_, other->yield_);
  std::swap(lastqty_, other->lastqty_);
  std::swap(premarketbondindicator_, other->premarketbondindicator_);
  std::swap(trademethod_, other->trademethod_);
  transactionmethod_.Swap(&other->transactionmethod_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CashBondTradingDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CashBondTradingDeal_descriptor_;
  metadata.reflection = CashBondTradingDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CashBondTradingDeal

// optional string TradeDate = 1;
void CashBondTradingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CashBondTradingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
void CashBondTradingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
void CashBondTradingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
::std::string* CashBondTradingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CashBondTradingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}

// optional string TradeTime = 2;
void CashBondTradingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CashBondTradingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
void CashBondTradingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
void CashBondTradingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
::std::string* CashBondTradingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CashBondTradingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}

// optional double Price = 3;
void CashBondTradingDeal::clear_price() {
  price_ = 0;
}
double CashBondTradingDeal::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.Price)
  return price_;
}
void CashBondTradingDeal::set_price(double value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.Price)
}

// optional double Yield = 4;
void CashBondTradingDeal::clear_yield() {
  yield_ = 0;
}
double CashBondTradingDeal::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.Yield)
  return yield_;
}
void CashBondTradingDeal::set_yield(double value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.Yield)
}

// optional int64 LastQty = 5;
void CashBondTradingDeal::clear_lastqty() {
  lastqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CashBondTradingDeal::lastqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.LastQty)
  return lastqty_;
}
void CashBondTradingDeal::set_lastqty(::google::protobuf::int64 value) {
  
  lastqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.LastQty)
}

// optional bool PreMarketBondIndicator = 6;
void CashBondTradingDeal::clear_premarketbondindicator() {
  premarketbondindicator_ = false;
}
bool CashBondTradingDeal::premarketbondindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.PreMarketBondIndicator)
  return premarketbondindicator_;
}
void CashBondTradingDeal::set_premarketbondindicator(bool value) {
  
  premarketbondindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.PreMarketBondIndicator)
}

// optional int32 TradeMethod = 7;
void CashBondTradingDeal::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 CashBondTradingDeal::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeMethod)
  return trademethod_;
}
void CashBondTradingDeal::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeMethod)
}

// optional string TransactionMethod = 8;
void CashBondTradingDeal::clear_transactionmethod() {
  transactionmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CashBondTradingDeal::transactionmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  return transactionmethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_transactionmethod(const ::std::string& value) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
void CashBondTradingDeal::set_transactionmethod(const char* value) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
void CashBondTradingDeal::set_transactionmethod(const char* value, size_t size) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
::std::string* CashBondTradingDeal::mutable_transactionmethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  return transactionmethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CashBondTradingDeal::release_transactionmethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  
  return transactionmethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingDeal::set_allocated_transactionmethod(::std::string* transactionmethod) {
  if (transactionmethod != NULL) {
    
  } else {
    
  }
  transactionmethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactionmethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}

inline const CashBondTradingDeal* CashBondTradingDeal::internal_default_instance() {
  return &CashBondTradingDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BondLendingDeal::kTradeDateFieldNumber;
const int BondLendingDeal::kTradeTimeFieldNumber;
const int BondLendingDeal::kPriceFieldNumber;
const int BondLendingDeal::kUnderlyingSymbolFieldNumber;
const int BondLendingDeal::kUnderlyingSecurityIDFieldNumber;
const int BondLendingDeal::kUnderlyingQtyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BondLendingDeal::BondLendingDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BondLendingDeal)
}

void BondLendingDeal::InitAsDefaultInstance() {
}

BondLendingDeal::BondLendingDeal(const BondLendingDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BondLendingDeal)
}

void BondLendingDeal::SharedCtor() {
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&price_, 0, reinterpret_cast<char*>(&underlyingqty_) -
    reinterpret_cast<char*>(&price_) + sizeof(underlyingqty_));
  _cached_size_ = 0;
}

BondLendingDeal::~BondLendingDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BondLendingDeal)
  SharedDtor();
}

void BondLendingDeal::SharedDtor() {
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BondLendingDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BondLendingDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BondLendingDeal_descriptor_;
}

const BondLendingDeal& BondLendingDeal::default_instance() {
  protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BondLendingDeal> BondLendingDeal_default_instance_;

BondLendingDeal* BondLendingDeal::New(::google::protobuf::Arena* arena) const {
  BondLendingDeal* n = new BondLendingDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BondLendingDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BondLendingDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BondLendingDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BondLendingDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(price_, underlyingqty_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool BondLendingDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BondLendingDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string TradeDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingDeal.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingDeal.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_Price;
        break;
      }

      // optional double Price = 3;
      case 3: {
        if (tag == 25) {
         parse_Price:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &price_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_UnderlyingSymbol;
        break;
      }

      // optional string UnderlyingSymbol = 4;
      case 4: {
        if (tag == 34) {
         parse_UnderlyingSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsymbol().data(), this->underlyingsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_UnderlyingSecurityID;
        break;
      }

      // optional string UnderlyingSecurityID = 5;
      case 5: {
        if (tag == 42) {
         parse_UnderlyingSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(49)) goto parse_UnderlyingQty;
        break;
      }

      // optional double UnderlyingQty = 6;
      case 6: {
        if (tag == 49) {
         parse_UnderlyingQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &underlyingqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BondLendingDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BondLendingDeal)
  return false;
#undef DO_
}

void BondLendingDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BondLendingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->tradedate(), output);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradetime(), output);
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->price(), output);
  }

  // optional string UnderlyingSymbol = 4;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->underlyingsymbol(), output);
  }

  // optional string UnderlyingSecurityID = 5;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->underlyingsecurityid(), output);
  }

  // optional double UnderlyingQty = 6;
  if (this->underlyingqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->underlyingqty(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BondLendingDeal)
}

::google::protobuf::uint8* BondLendingDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BondLendingDeal)
  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->tradedate(), target);
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradetime(), target);
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->price(), target);
  }

  // optional string UnderlyingSymbol = 4;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->underlyingsymbol(), target);
  }

  // optional string UnderlyingSecurityID = 5;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->underlyingsecurityid(), target);
  }

  // optional double UnderlyingQty = 6;
  if (this->underlyingqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->underlyingqty(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BondLendingDeal)
  return target;
}

size_t BondLendingDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BondLendingDeal)
  size_t total_size = 0;

  // optional string TradeDate = 1;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 2;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional double Price = 3;
  if (this->price() != 0) {
    total_size += 1 + 8;
  }

  // optional string UnderlyingSymbol = 4;
  if (this->underlyingsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsymbol());
  }

  // optional string UnderlyingSecurityID = 5;
  if (this->underlyingsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsecurityid());
  }

  // optional double UnderlyingQty = 6;
  if (this->underlyingqty() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BondLendingDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BondLendingDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BondLendingDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BondLendingDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BondLendingDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BondLendingDeal)
    UnsafeMergeFrom(*source);
  }
}

void BondLendingDeal::MergeFrom(const BondLendingDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BondLendingDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BondLendingDeal::UnsafeMergeFrom(const BondLendingDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.price() != 0) {
    set_price(from.price());
  }
  if (from.underlyingsymbol().size() > 0) {

    underlyingsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsymbol_);
  }
  if (from.underlyingsecurityid().size() > 0) {

    underlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsecurityid_);
  }
  if (from.underlyingqty() != 0) {
    set_underlyingqty(from.underlyingqty());
  }
}

void BondLendingDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BondLendingDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BondLendingDeal::CopyFrom(const BondLendingDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BondLendingDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BondLendingDeal::IsInitialized() const {

  return true;
}

void BondLendingDeal::Swap(BondLendingDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BondLendingDeal::InternalSwap(BondLendingDeal* other) {
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(price_, other->price_);
  underlyingsymbol_.Swap(&other->underlyingsymbol_);
  underlyingsecurityid_.Swap(&other->underlyingsecurityid_);
  std::swap(underlyingqty_, other->underlyingqty_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BondLendingDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BondLendingDeal_descriptor_;
  metadata.reflection = BondLendingDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BondLendingDeal

// optional string TradeDate = 1;
void BondLendingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
void BondLendingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
void BondLendingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
::std::string* BondLendingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}

// optional string TradeTime = 2;
void BondLendingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
void BondLendingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
void BondLendingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
::std::string* BondLendingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}

// optional double Price = 3;
void BondLendingDeal::clear_price() {
  price_ = 0;
}
double BondLendingDeal::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.Price)
  return price_;
}
void BondLendingDeal::set_price(double value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.Price)
}

// optional string UnderlyingSymbol = 4;
void BondLendingDeal::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingDeal::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
void BondLendingDeal::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
void BondLendingDeal::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
::std::string* BondLendingDeal::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingDeal::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 5;
void BondLendingDeal::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingDeal::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
void BondLendingDeal::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
void BondLendingDeal::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
::std::string* BondLendingDeal::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingDeal::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingDeal::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}

// optional double UnderlyingQty = 6;
void BondLendingDeal::clear_underlyingqty() {
  underlyingqty_ = 0;
}
double BondLendingDeal::underlyingqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingQty)
  return underlyingqty_;
}
void BondLendingDeal::set_underlyingqty(double value) {
  
  underlyingqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingQty)
}

inline const BondLendingDeal* BondLendingDeal::internal_default_instance() {
  return &BondLendingDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
