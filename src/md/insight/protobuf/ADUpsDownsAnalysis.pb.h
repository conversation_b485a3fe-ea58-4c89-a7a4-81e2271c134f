// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADUpsDownsAnalysis.proto

#ifndef PROTOBUF_ADUpsDownsAnalysis_2eproto__INCLUDED
#define PROTOBUF_ADUpsDownsAnalysis_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_ADUpsDownsAnalysis_2eproto();
void protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto();
void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto();

class ADUpsDownsAnalysis;
class ADUpsDownsCount;
class ADUpsDownsLimitCount;
class ADUpsDownsPartitionDetail;

// ===================================================================

class ADUpsDownsAnalysis : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADUpsDownsAnalysis) */ {
 public:
  ADUpsDownsAnalysis();
  virtual ~ADUpsDownsAnalysis();

  ADUpsDownsAnalysis(const ADUpsDownsAnalysis& from);

  inline ADUpsDownsAnalysis& operator=(const ADUpsDownsAnalysis& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADUpsDownsAnalysis& default_instance();

  static const ADUpsDownsAnalysis* internal_default_instance();

  void Swap(ADUpsDownsAnalysis* other);

  // implements Message ----------------------------------------------

  inline ADUpsDownsAnalysis* New() const { return New(NULL); }

  ADUpsDownsAnalysis* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADUpsDownsAnalysis& from);
  void MergeFrom(const ADUpsDownsAnalysis& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADUpsDownsAnalysis* other);
  void UnsafeMergeFrom(const ADUpsDownsAnalysis& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
  bool has_upsdownscount() const;
  void clear_upsdownscount();
  static const int kUpsDownsCountFieldNumber = 7;
  const ::com::htsc::mdc::insight::model::ADUpsDownsCount& upsdownscount() const;
  ::com::htsc::mdc::insight::model::ADUpsDownsCount* mutable_upsdownscount();
  ::com::htsc::mdc::insight::model::ADUpsDownsCount* release_upsdownscount();
  void set_allocated_upsdownscount(::com::htsc::mdc::insight::model::ADUpsDownsCount* upsdownscount);

  // optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
  bool has_upsdownslimitcount() const;
  void clear_upsdownslimitcount();
  static const int kUpsDownsLimitCountFieldNumber = 8;
  const ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount& upsdownslimitcount() const;
  ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* mutable_upsdownslimitcount();
  ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* release_upsdownslimitcount();
  void set_allocated_upsdownslimitcount(::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* upsdownslimitcount);

  // repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
  int upsdownspartitiondetail_size() const;
  void clear_upsdownspartitiondetail();
  static const int kUpsDownsPartitionDetailFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail& upsdownspartitiondetail(int index) const;
  ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* mutable_upsdownspartitiondetail(int index);
  ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* add_upsdownspartitiondetail();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >*
      mutable_upsdownspartitiondetail();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >&
      upsdownspartitiondetail() const;

  // optional int32 ExchangeDate = 10;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 10;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 11;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 11;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 12;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 12;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADUpsDownsAnalysis)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail > upsdownspartitiondetail_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::com::htsc::mdc::insight::model::ADUpsDownsCount* upsdownscount_;
  ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* upsdownslimitcount_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl();
  friend void  protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl();
  friend void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto();
  friend void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsAnalysis> ADUpsDownsAnalysis_default_instance_;

// -------------------------------------------------------------------

class ADUpsDownsCount : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADUpsDownsCount) */ {
 public:
  ADUpsDownsCount();
  virtual ~ADUpsDownsCount();

  ADUpsDownsCount(const ADUpsDownsCount& from);

  inline ADUpsDownsCount& operator=(const ADUpsDownsCount& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADUpsDownsCount& default_instance();

  static const ADUpsDownsCount* internal_default_instance();

  void Swap(ADUpsDownsCount* other);

  // implements Message ----------------------------------------------

  inline ADUpsDownsCount* New() const { return New(NULL); }

  ADUpsDownsCount* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADUpsDownsCount& from);
  void MergeFrom(const ADUpsDownsCount& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADUpsDownsCount* other);
  void UnsafeMergeFrom(const ADUpsDownsCount& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Ups = 1;
  void clear_ups();
  static const int kUpsFieldNumber = 1;
  ::google::protobuf::int32 ups() const;
  void set_ups(::google::protobuf::int32 value);

  // optional int32 Downs = 2;
  void clear_downs();
  static const int kDownsFieldNumber = 2;
  ::google::protobuf::int32 downs() const;
  void set_downs(::google::protobuf::int32 value);

  // optional int32 Equals = 3;
  void clear_equals();
  static const int kEqualsFieldNumber = 3;
  ::google::protobuf::int32 equals() const;
  void set_equals(::google::protobuf::int32 value);

  // optional int32 PreUps = 4;
  void clear_preups();
  static const int kPreUpsFieldNumber = 4;
  ::google::protobuf::int32 preups() const;
  void set_preups(::google::protobuf::int32 value);

  // optional int32 PreDowns = 5;
  void clear_predowns();
  static const int kPreDownsFieldNumber = 5;
  ::google::protobuf::int32 predowns() const;
  void set_predowns(::google::protobuf::int32 value);

  // optional int32 PreEquals = 6;
  void clear_preequals();
  static const int kPreEqualsFieldNumber = 6;
  ::google::protobuf::int32 preequals() const;
  void set_preequals(::google::protobuf::int32 value);

  // optional double UpsPercent = 7;
  void clear_upspercent();
  static const int kUpsPercentFieldNumber = 7;
  double upspercent() const;
  void set_upspercent(double value);

  // optional double PreUpsPercent = 8;
  void clear_preupspercent();
  static const int kPreUpsPercentFieldNumber = 8;
  double preupspercent() const;
  void set_preupspercent(double value);

  // repeated string LeadingUpIds = 9;
  int leadingupids_size() const;
  void clear_leadingupids();
  static const int kLeadingUpIdsFieldNumber = 9;
  const ::std::string& leadingupids(int index) const;
  ::std::string* mutable_leadingupids(int index);
  void set_leadingupids(int index, const ::std::string& value);
  void set_leadingupids(int index, const char* value);
  void set_leadingupids(int index, const char* value, size_t size);
  ::std::string* add_leadingupids();
  void add_leadingupids(const ::std::string& value);
  void add_leadingupids(const char* value);
  void add_leadingupids(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& leadingupids() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_leadingupids();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADUpsDownsCount)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> leadingupids_;
  ::google::protobuf::int32 ups_;
  ::google::protobuf::int32 downs_;
  ::google::protobuf::int32 equals_;
  ::google::protobuf::int32 preups_;
  ::google::protobuf::int32 predowns_;
  ::google::protobuf::int32 preequals_;
  double upspercent_;
  double preupspercent_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl();
  friend void  protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl();
  friend void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto();
  friend void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsCount> ADUpsDownsCount_default_instance_;

// -------------------------------------------------------------------

class ADUpsDownsLimitCount : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADUpsDownsLimitCount) */ {
 public:
  ADUpsDownsLimitCount();
  virtual ~ADUpsDownsLimitCount();

  ADUpsDownsLimitCount(const ADUpsDownsLimitCount& from);

  inline ADUpsDownsLimitCount& operator=(const ADUpsDownsLimitCount& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADUpsDownsLimitCount& default_instance();

  static const ADUpsDownsLimitCount* internal_default_instance();

  void Swap(ADUpsDownsLimitCount* other);

  // implements Message ----------------------------------------------

  inline ADUpsDownsLimitCount* New() const { return New(NULL); }

  ADUpsDownsLimitCount* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADUpsDownsLimitCount& from);
  void MergeFrom(const ADUpsDownsLimitCount& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADUpsDownsLimitCount* other);
  void UnsafeMergeFrom(const ADUpsDownsLimitCount& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 NoReachedLimitPx = 1;
  void clear_noreachedlimitpx();
  static const int kNoReachedLimitPxFieldNumber = 1;
  ::google::protobuf::int32 noreachedlimitpx() const;
  void set_noreachedlimitpx(::google::protobuf::int32 value);

  // optional int32 UpLimits = 2;
  void clear_uplimits();
  static const int kUpLimitsFieldNumber = 2;
  ::google::protobuf::int32 uplimits() const;
  void set_uplimits(::google::protobuf::int32 value);

  // optional int32 DownLimits = 3;
  void clear_downlimits();
  static const int kDownLimitsFieldNumber = 3;
  ::google::protobuf::int32 downlimits() const;
  void set_downlimits(::google::protobuf::int32 value);

  // optional int32 PreNoReachedLimitPx = 4;
  void clear_prenoreachedlimitpx();
  static const int kPreNoReachedLimitPxFieldNumber = 4;
  ::google::protobuf::int32 prenoreachedlimitpx() const;
  void set_prenoreachedlimitpx(::google::protobuf::int32 value);

  // optional int32 PreUpLimits = 5;
  void clear_preuplimits();
  static const int kPreUpLimitsFieldNumber = 5;
  ::google::protobuf::int32 preuplimits() const;
  void set_preuplimits(::google::protobuf::int32 value);

  // optional int32 PreDownLimits = 6;
  void clear_predownlimits();
  static const int kPreDownLimitsFieldNumber = 6;
  ::google::protobuf::int32 predownlimits() const;
  void set_predownlimits(::google::protobuf::int32 value);

  // optional double PreUpLimitsAverageChangePercent = 7;
  void clear_preuplimitsaveragechangepercent();
  static const int kPreUpLimitsAverageChangePercentFieldNumber = 7;
  double preuplimitsaveragechangepercent() const;
  void set_preuplimitsaveragechangepercent(double value);

  // optional double UpLimitsPercent = 8;
  void clear_uplimitspercent();
  static const int kUpLimitsPercentFieldNumber = 8;
  double uplimitspercent() const;
  void set_uplimitspercent(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADUpsDownsLimitCount)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 noreachedlimitpx_;
  ::google::protobuf::int32 uplimits_;
  ::google::protobuf::int32 downlimits_;
  ::google::protobuf::int32 prenoreachedlimitpx_;
  ::google::protobuf::int32 preuplimits_;
  ::google::protobuf::int32 predownlimits_;
  double preuplimitsaveragechangepercent_;
  double uplimitspercent_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl();
  friend void  protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl();
  friend void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto();
  friend void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsLimitCount> ADUpsDownsLimitCount_default_instance_;

// -------------------------------------------------------------------

class ADUpsDownsPartitionDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail) */ {
 public:
  ADUpsDownsPartitionDetail();
  virtual ~ADUpsDownsPartitionDetail();

  ADUpsDownsPartitionDetail(const ADUpsDownsPartitionDetail& from);

  inline ADUpsDownsPartitionDetail& operator=(const ADUpsDownsPartitionDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ADUpsDownsPartitionDetail& default_instance();

  static const ADUpsDownsPartitionDetail* internal_default_instance();

  void Swap(ADUpsDownsPartitionDetail* other);

  // implements Message ----------------------------------------------

  inline ADUpsDownsPartitionDetail* New() const { return New(NULL); }

  ADUpsDownsPartitionDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ADUpsDownsPartitionDetail& from);
  void MergeFrom(const ADUpsDownsPartitionDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ADUpsDownsPartitionDetail* other);
  void UnsafeMergeFrom(const ADUpsDownsPartitionDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Numbers = 1;
  void clear_numbers();
  static const int kNumbersFieldNumber = 1;
  ::google::protobuf::int32 numbers() const;
  void set_numbers(::google::protobuf::int32 value);

  // optional int32 PartitionChangePercent = 2;
  void clear_partitionchangepercent();
  static const int kPartitionChangePercentFieldNumber = 2;
  ::google::protobuf::int32 partitionchangepercent() const;
  void set_partitionchangepercent(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 numbers_;
  ::google::protobuf::int32 partitionchangepercent_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto_impl();
  friend void  protobuf_AddDesc_ADUpsDownsAnalysis_2eproto_impl();
  friend void protobuf_AssignDesc_ADUpsDownsAnalysis_2eproto();
  friend void protobuf_ShutdownFile_ADUpsDownsAnalysis_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ADUpsDownsPartitionDetail> ADUpsDownsPartitionDetail_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// ADUpsDownsAnalysis

// optional string HTSCSecurityID = 1;
inline void ADUpsDownsAnalysis::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ADUpsDownsAnalysis::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADUpsDownsAnalysis::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
inline void ADUpsDownsAnalysis::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
inline void ADUpsDownsAnalysis::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}
inline ::std::string* ADUpsDownsAnalysis::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ADUpsDownsAnalysis::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ADUpsDownsAnalysis::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void ADUpsDownsAnalysis::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsAnalysis::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDDate)
  return mddate_;
}
inline void ADUpsDownsAnalysis::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDDate)
}

// optional int32 MDTime = 3;
inline void ADUpsDownsAnalysis::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsAnalysis::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDTime)
  return mdtime_;
}
inline void ADUpsDownsAnalysis::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void ADUpsDownsAnalysis::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ADUpsDownsAnalysis::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataTimestamp)
  return datatimestamp_;
}
inline void ADUpsDownsAnalysis::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void ADUpsDownsAnalysis::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource ADUpsDownsAnalysis::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void ADUpsDownsAnalysis::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void ADUpsDownsAnalysis::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType ADUpsDownsAnalysis::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void ADUpsDownsAnalysis::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.securityType)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsCount UpsDownsCount = 7;
inline bool ADUpsDownsAnalysis::has_upsdownscount() const {
  return this != internal_default_instance() && upsdownscount_ != NULL;
}
inline void ADUpsDownsAnalysis::clear_upsdownscount() {
  if (GetArenaNoVirtual() == NULL && upsdownscount_ != NULL) delete upsdownscount_;
  upsdownscount_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADUpsDownsCount& ADUpsDownsAnalysis::upsdownscount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  return upsdownscount_ != NULL ? *upsdownscount_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsCount::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsCount* ADUpsDownsAnalysis::mutable_upsdownscount() {
  
  if (upsdownscount_ == NULL) {
    upsdownscount_ = new ::com::htsc::mdc::insight::model::ADUpsDownsCount;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  return upsdownscount_;
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsCount* ADUpsDownsAnalysis::release_upsdownscount() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsCount* temp = upsdownscount_;
  upsdownscount_ = NULL;
  return temp;
}
inline void ADUpsDownsAnalysis::set_allocated_upsdownscount(::com::htsc::mdc::insight::model::ADUpsDownsCount* upsdownscount) {
  delete upsdownscount_;
  upsdownscount_ = upsdownscount;
  if (upsdownscount) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsCount)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsLimitCount UpsDownsLimitCount = 8;
inline bool ADUpsDownsAnalysis::has_upsdownslimitcount() const {
  return this != internal_default_instance() && upsdownslimitcount_ != NULL;
}
inline void ADUpsDownsAnalysis::clear_upsdownslimitcount() {
  if (GetArenaNoVirtual() == NULL && upsdownslimitcount_ != NULL) delete upsdownslimitcount_;
  upsdownslimitcount_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount& ADUpsDownsAnalysis::upsdownslimitcount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  return upsdownslimitcount_ != NULL ? *upsdownslimitcount_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsLimitCount::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* ADUpsDownsAnalysis::mutable_upsdownslimitcount() {
  
  if (upsdownslimitcount_ == NULL) {
    upsdownslimitcount_ = new ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  return upsdownslimitcount_;
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* ADUpsDownsAnalysis::release_upsdownslimitcount() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* temp = upsdownslimitcount_;
  upsdownslimitcount_ = NULL;
  return temp;
}
inline void ADUpsDownsAnalysis::set_allocated_upsdownslimitcount(::com::htsc::mdc::insight::model::ADUpsDownsLimitCount* upsdownslimitcount) {
  delete upsdownslimitcount_;
  upsdownslimitcount_ = upsdownslimitcount;
  if (upsdownslimitcount) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsLimitCount)
}

// repeated .com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail UpsDownsPartitionDetail = 9;
inline int ADUpsDownsAnalysis::upsdownspartitiondetail_size() const {
  return upsdownspartitiondetail_.size();
}
inline void ADUpsDownsAnalysis::clear_upsdownspartitiondetail() {
  upsdownspartitiondetail_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail& ADUpsDownsAnalysis::upsdownspartitiondetail(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* ADUpsDownsAnalysis::mutable_upsdownspartitiondetail(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail* ADUpsDownsAnalysis::add_upsdownspartitiondetail() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >*
ADUpsDownsAnalysis::mutable_upsdownspartitiondetail() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return &upsdownspartitiondetail_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADUpsDownsPartitionDetail >&
ADUpsDownsAnalysis::upsdownspartitiondetail() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.UpsDownsPartitionDetail)
  return upsdownspartitiondetail_;
}

// optional int32 ExchangeDate = 10;
inline void ADUpsDownsAnalysis::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsAnalysis::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeDate)
  return exchangedate_;
}
inline void ADUpsDownsAnalysis::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeDate)
}

// optional int32 ExchangeTime = 11;
inline void ADUpsDownsAnalysis::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsAnalysis::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeTime)
  return exchangetime_;
}
inline void ADUpsDownsAnalysis::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 12;
inline void ADUpsDownsAnalysis::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsAnalysis::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void ADUpsDownsAnalysis::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsAnalysis.DataMultiplePowerOf10)
}

inline const ADUpsDownsAnalysis* ADUpsDownsAnalysis::internal_default_instance() {
  return &ADUpsDownsAnalysis_default_instance_.get();
}
// -------------------------------------------------------------------

// ADUpsDownsCount

// optional int32 Ups = 1;
inline void ADUpsDownsCount::clear_ups() {
  ups_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::ups() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Ups)
  return ups_;
}
inline void ADUpsDownsCount::set_ups(::google::protobuf::int32 value) {
  
  ups_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Ups)
}

// optional int32 Downs = 2;
inline void ADUpsDownsCount::clear_downs() {
  downs_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::downs() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Downs)
  return downs_;
}
inline void ADUpsDownsCount::set_downs(::google::protobuf::int32 value) {
  
  downs_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Downs)
}

// optional int32 Equals = 3;
inline void ADUpsDownsCount::clear_equals() {
  equals_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::equals() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.Equals)
  return equals_;
}
inline void ADUpsDownsCount::set_equals(::google::protobuf::int32 value) {
  
  equals_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.Equals)
}

// optional int32 PreUps = 4;
inline void ADUpsDownsCount::clear_preups() {
  preups_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::preups() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUps)
  return preups_;
}
inline void ADUpsDownsCount::set_preups(::google::protobuf::int32 value) {
  
  preups_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUps)
}

// optional int32 PreDowns = 5;
inline void ADUpsDownsCount::clear_predowns() {
  predowns_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::predowns() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreDowns)
  return predowns_;
}
inline void ADUpsDownsCount::set_predowns(::google::protobuf::int32 value) {
  
  predowns_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreDowns)
}

// optional int32 PreEquals = 6;
inline void ADUpsDownsCount::clear_preequals() {
  preequals_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsCount::preequals() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreEquals)
  return preequals_;
}
inline void ADUpsDownsCount::set_preequals(::google::protobuf::int32 value) {
  
  preequals_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreEquals)
}

// optional double UpsPercent = 7;
inline void ADUpsDownsCount::clear_upspercent() {
  upspercent_ = 0;
}
inline double ADUpsDownsCount::upspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.UpsPercent)
  return upspercent_;
}
inline void ADUpsDownsCount::set_upspercent(double value) {
  
  upspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.UpsPercent)
}

// optional double PreUpsPercent = 8;
inline void ADUpsDownsCount::clear_preupspercent() {
  preupspercent_ = 0;
}
inline double ADUpsDownsCount::preupspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUpsPercent)
  return preupspercent_;
}
inline void ADUpsDownsCount::set_preupspercent(double value) {
  
  preupspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.PreUpsPercent)
}

// repeated string LeadingUpIds = 9;
inline int ADUpsDownsCount::leadingupids_size() const {
  return leadingupids_.size();
}
inline void ADUpsDownsCount::clear_leadingupids() {
  leadingupids_.Clear();
}
inline const ::std::string& ADUpsDownsCount::leadingupids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Get(index);
}
inline ::std::string* ADUpsDownsCount::mutable_leadingupids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Mutable(index);
}
inline void ADUpsDownsCount::set_leadingupids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  leadingupids_.Mutable(index)->assign(value);
}
inline void ADUpsDownsCount::set_leadingupids(int index, const char* value) {
  leadingupids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
inline void ADUpsDownsCount::set_leadingupids(int index, const char* value, size_t size) {
  leadingupids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
inline ::std::string* ADUpsDownsCount::add_leadingupids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_.Add();
}
inline void ADUpsDownsCount::add_leadingupids(const ::std::string& value) {
  leadingupids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
inline void ADUpsDownsCount::add_leadingupids(const char* value) {
  leadingupids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
inline void ADUpsDownsCount::add_leadingupids(const char* value, size_t size) {
  leadingupids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ADUpsDownsCount::leadingupids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return leadingupids_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ADUpsDownsCount::mutable_leadingupids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADUpsDownsCount.LeadingUpIds)
  return &leadingupids_;
}

inline const ADUpsDownsCount* ADUpsDownsCount::internal_default_instance() {
  return &ADUpsDownsCount_default_instance_.get();
}
// -------------------------------------------------------------------

// ADUpsDownsLimitCount

// optional int32 NoReachedLimitPx = 1;
inline void ADUpsDownsLimitCount::clear_noreachedlimitpx() {
  noreachedlimitpx_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::noreachedlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.NoReachedLimitPx)
  return noreachedlimitpx_;
}
inline void ADUpsDownsLimitCount::set_noreachedlimitpx(::google::protobuf::int32 value) {
  
  noreachedlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.NoReachedLimitPx)
}

// optional int32 UpLimits = 2;
inline void ADUpsDownsLimitCount::clear_uplimits() {
  uplimits_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::uplimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimits)
  return uplimits_;
}
inline void ADUpsDownsLimitCount::set_uplimits(::google::protobuf::int32 value) {
  
  uplimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimits)
}

// optional int32 DownLimits = 3;
inline void ADUpsDownsLimitCount::clear_downlimits() {
  downlimits_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::downlimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.DownLimits)
  return downlimits_;
}
inline void ADUpsDownsLimitCount::set_downlimits(::google::protobuf::int32 value) {
  
  downlimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.DownLimits)
}

// optional int32 PreNoReachedLimitPx = 4;
inline void ADUpsDownsLimitCount::clear_prenoreachedlimitpx() {
  prenoreachedlimitpx_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::prenoreachedlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreNoReachedLimitPx)
  return prenoreachedlimitpx_;
}
inline void ADUpsDownsLimitCount::set_prenoreachedlimitpx(::google::protobuf::int32 value) {
  
  prenoreachedlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreNoReachedLimitPx)
}

// optional int32 PreUpLimits = 5;
inline void ADUpsDownsLimitCount::clear_preuplimits() {
  preuplimits_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::preuplimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimits)
  return preuplimits_;
}
inline void ADUpsDownsLimitCount::set_preuplimits(::google::protobuf::int32 value) {
  
  preuplimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimits)
}

// optional int32 PreDownLimits = 6;
inline void ADUpsDownsLimitCount::clear_predownlimits() {
  predownlimits_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsLimitCount::predownlimits() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreDownLimits)
  return predownlimits_;
}
inline void ADUpsDownsLimitCount::set_predownlimits(::google::protobuf::int32 value) {
  
  predownlimits_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreDownLimits)
}

// optional double PreUpLimitsAverageChangePercent = 7;
inline void ADUpsDownsLimitCount::clear_preuplimitsaveragechangepercent() {
  preuplimitsaveragechangepercent_ = 0;
}
inline double ADUpsDownsLimitCount::preuplimitsaveragechangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimitsAverageChangePercent)
  return preuplimitsaveragechangepercent_;
}
inline void ADUpsDownsLimitCount::set_preuplimitsaveragechangepercent(double value) {
  
  preuplimitsaveragechangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.PreUpLimitsAverageChangePercent)
}

// optional double UpLimitsPercent = 8;
inline void ADUpsDownsLimitCount::clear_uplimitspercent() {
  uplimitspercent_ = 0;
}
inline double ADUpsDownsLimitCount::uplimitspercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimitsPercent)
  return uplimitspercent_;
}
inline void ADUpsDownsLimitCount::set_uplimitspercent(double value) {
  
  uplimitspercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsLimitCount.UpLimitsPercent)
}

inline const ADUpsDownsLimitCount* ADUpsDownsLimitCount::internal_default_instance() {
  return &ADUpsDownsLimitCount_default_instance_.get();
}
// -------------------------------------------------------------------

// ADUpsDownsPartitionDetail

// optional int32 Numbers = 1;
inline void ADUpsDownsPartitionDetail::clear_numbers() {
  numbers_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsPartitionDetail::numbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.Numbers)
  return numbers_;
}
inline void ADUpsDownsPartitionDetail::set_numbers(::google::protobuf::int32 value) {
  
  numbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.Numbers)
}

// optional int32 PartitionChangePercent = 2;
inline void ADUpsDownsPartitionDetail::clear_partitionchangepercent() {
  partitionchangepercent_ = 0;
}
inline ::google::protobuf::int32 ADUpsDownsPartitionDetail::partitionchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.PartitionChangePercent)
  return partitionchangepercent_;
}
inline void ADUpsDownsPartitionDetail::set_partitionchangepercent(::google::protobuf::int32 value) {
  
  partitionchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADUpsDownsPartitionDetail.PartitionChangePercent)
}

inline const ADUpsDownsPartitionDetail* ADUpsDownsPartitionDetail::internal_default_instance() {
  return &ADUpsDownsPartitionDetail_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_ADUpsDownsAnalysis_2eproto__INCLUDED
