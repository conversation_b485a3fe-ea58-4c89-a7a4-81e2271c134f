// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBondSnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsBondSnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsBondSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsBondSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* CashBondTradingSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CashBondTradingSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* BondForwardSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BondForwardSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* BondLendingSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BondLendingSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* StandardisedBondForwardSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StandardisedBondForwardSnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto() {
  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsBondSnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsBondSnapshot_descriptor_ = file->message_type(0);
  static const int MDCfetsBondSnapshot_offsets_[15] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, bondsnapshottype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, cashbondtradingsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, bondforwardsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, bondlendingsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, standardisedbondforwardsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, messagenumber_),
  };
  MDCfetsBondSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsBondSnapshot_descriptor_,
      MDCfetsBondSnapshot::internal_default_instance(),
      MDCfetsBondSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsBondSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsBondSnapshot, _internal_metadata_));
  CashBondTradingSnapshot_descriptor_ = file->message_type(1);
  static const int CashBondTradingSnapshot_offsets_[25] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, settltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, side_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, premarketbondindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, preclosecleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, preweightedavgcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, opencleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, lastcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, changepercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, highcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, lowcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, closecleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, weightedavgcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, precloseyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, preweightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, openyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, lastyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, highyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, lowyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, closeyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, weightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, tradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, cleanpxchange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, cleanpxchangepercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, yieldchangebp_),
  };
  CashBondTradingSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CashBondTradingSnapshot_descriptor_,
      CashBondTradingSnapshot::internal_default_instance(),
      CashBondTradingSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(CashBondTradingSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CashBondTradingSnapshot, _internal_metadata_));
  BondForwardSnapshot_descriptor_ = file->message_type(2);
  static const int BondForwardSnapshot_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, underlyingsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, underlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, preclosecleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, preweightedavgcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, cleanpxchangepercent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, opencleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, lastcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, highcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, lowcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, closecleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, weightedavgcleanpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, precloseyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, preweightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, openyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, lastyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, highyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, lowyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, closeyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, weightedavgyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, tradevolume_),
  };
  BondForwardSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BondForwardSnapshot_descriptor_,
      BondForwardSnapshot::internal_default_instance(),
      BondForwardSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(BondForwardSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondForwardSnapshot, _internal_metadata_));
  BondLendingSnapshot_descriptor_ = file->message_type(3);
  static const int BondLendingSnapshot_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, underlyingsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, underlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, precloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, preweightedavgrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, weightedavgrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, tradevolume_),
  };
  BondLendingSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BondLendingSnapshot_descriptor_,
      BondLendingSnapshot::internal_default_instance(),
      BondLendingSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(BondLendingSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BondLendingSnapshot, _internal_metadata_));
  StandardisedBondForwardSnapshot_descriptor_ = file->message_type(4);
  static const int StandardisedBondForwardSnapshot_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, lastvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, settlepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, settlepxdate_),
  };
  StandardisedBondForwardSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StandardisedBondForwardSnapshot_descriptor_,
      StandardisedBondForwardSnapshot::internal_default_instance(),
      StandardisedBondForwardSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(StandardisedBondForwardSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedBondForwardSnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsBondSnapshot_descriptor_, MDCfetsBondSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CashBondTradingSnapshot_descriptor_, CashBondTradingSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BondForwardSnapshot_descriptor_, BondForwardSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BondLendingSnapshot_descriptor_, BondLendingSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StandardisedBondForwardSnapshot_descriptor_, StandardisedBondForwardSnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto() {
  MDCfetsBondSnapshot_default_instance_.Shutdown();
  delete MDCfetsBondSnapshot_reflection_;
  CashBondTradingSnapshot_default_instance_.Shutdown();
  delete CashBondTradingSnapshot_reflection_;
  BondForwardSnapshot_default_instance_.Shutdown();
  delete BondForwardSnapshot_reflection_;
  BondLendingSnapshot_default_instance_.Shutdown();
  delete BondLendingSnapshot_reflection_;
  StandardisedBondForwardSnapshot_default_instance_.Shutdown();
  delete StandardisedBondForwardSnapshot_reflection_;
}

void protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsBondSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  CashBondTradingSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BondForwardSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  BondLendingSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  StandardisedBondForwardSnapshot_default_instance_.DefaultConstruct();
  MDCfetsBondSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  CashBondTradingSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  BondForwardSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  BondLendingSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  StandardisedBondForwardSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_once_);
void protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031MDCfetsBondSnapshot.proto\022\032com.htsc.md"
    "c.insight.model\032\027ESecurityIDSource.proto"
    "\032\023ESecurityType.proto\"\265\005\n\023MDCfetsBondSna"
    "pshot\022\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n\014Securit"
    "yType\030\002 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\?\n\020SecurityIDSource\030\003 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\022\016\n\006MDDat"
    "e\030\004 \001(\005\022\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp"
    "\030\006 \001(\003\022\024\n\014TransactTime\030\007 \001(\t\022\027\n\017MarketIn"
    "dicator\030\010 \001(\t\022\035\n\025DataMultiplePowerOf10\030\t"
    " \001(\005\022\030\n\020BondSnapshotType\030\020 \001(\005\022T\n\027CashBo"
    "ndTradingSnapshot\030\021 \001(\01323.com.htsc.mdc.i"
    "nsight.model.CashBondTradingSnapshot\022L\n\023"
    "BondForwardSnapshot\030\022 \001(\0132/.com.htsc.mdc"
    ".insight.model.BondForwardSnapshot\022L\n\023Bo"
    "ndLendingSnapshot\030\023 \001(\0132/.com.htsc.mdc.i"
    "nsight.model.BondLendingSnapshot\022d\n\037Stan"
    "dardisedBondForwardSnapshot\030\024 \001(\0132;.com."
    "htsc.mdc.insight.model.StandardisedBondF"
    "orwardSnapshot\022\025\n\rMessageNumber\030d \001(\003\"\321\004"
    "\n\027CashBondTradingSnapshot\022\023\n\013TradeMethod"
    "\030\001 \001(\005\022\021\n\tSettlType\030\002 \001(\t\022\014\n\004Side\030\003 \001(\t\022"
    "\036\n\026PreMarketBondIndicator\030\004 \001(\010\022\027\n\017PreCl"
    "oseCleanPx\030\013 \001(\001\022\035\n\025PreWeightedAvgCleanP"
    "x\030\014 \001(\001\022\023\n\013OpenCleanPx\030\r \001(\001\022\023\n\013LastClea"
    "nPx\030\016 \001(\001\022\025\n\rChangePercent\030\017 \001(\001\022\023\n\013High"
    "CleanPx\030\020 \001(\001\022\022\n\nLowCleanPx\030\021 \001(\001\022\024\n\014Clo"
    "seCleanPx\030\022 \001(\001\022\032\n\022WeightedAvgCleanPx\030\023 "
    "\001(\001\022\025\n\rPreCloseYield\030\024 \001(\001\022\033\n\023PreWeighte"
    "dAvgYield\030\025 \001(\001\022\021\n\tOpenYield\030\026 \001(\001\022\021\n\tLa"
    "stYield\030\027 \001(\001\022\021\n\tHighYield\030\030 \001(\001\022\020\n\010LowY"
    "ield\030\031 \001(\001\022\022\n\nCloseYield\030\032 \001(\001\022\030\n\020Weight"
    "edAvgYield\030\033 \001(\001\022\023\n\013TradeVolume\030\034 \001(\001\022\025\n"
    "\rCleanPxChange\030\035 \001(\001\022\034\n\024CleanPxChangePer"
    "cent\030\036 \001(\001\022\025\n\rYieldChangeBP\030\037 \001(\001\"\352\003\n\023Bo"
    "ndForwardSnapshot\022\030\n\020UnderlyingSymbol\030\001 "
    "\001(\t\022\034\n\024UnderlyingSecurityID\030\002 \001(\t\022\027\n\017Pre"
    "CloseCleanPx\030\013 \001(\001\022\035\n\025PreWeightedAvgClea"
    "nPx\030\014 \001(\001\022\034\n\024CleanPxChangePercent\030\r \001(\001\022"
    "\023\n\013OpenCleanPx\030\016 \001(\001\022\023\n\013LastCleanPx\030\017 \001("
    "\001\022\023\n\013HighCleanPx\030\020 \001(\001\022\022\n\nLowCleanPx\030\021 \001"
    "(\001\022\024\n\014CloseCleanPx\030\022 \001(\001\022\032\n\022WeightedAvgC"
    "leanPx\030\023 \001(\001\022\025\n\rPreCloseYield\030\024 \001(\001\022\033\n\023P"
    "reWeightedAvgYield\030\025 \001(\001\022\021\n\tOpenYield\030\026 "
    "\001(\001\022\021\n\tLastYield\030\027 \001(\001\022\021\n\tHighYield\030\030 \001("
    "\001\022\020\n\010LowYield\030\031 \001(\001\022\022\n\nCloseYield\030\032 \001(\001\022"
    "\030\n\020WeightedAvgYield\030\033 \001(\001\022\023\n\013TradeVolume"
    "\030\034 \001(\001\"\207\002\n\023BondLendingSnapshot\022\030\n\020Underl"
    "yingSymbol\030\001 \001(\t\022\034\n\024UnderlyingSecurityID"
    "\030\002 \001(\t\022\024\n\014PreCloseRate\030\013 \001(\001\022\032\n\022PreWeigh"
    "tedAvgRate\030\014 \001(\001\022\020\n\010OpenRate\030\r \001(\001\022\020\n\010La"
    "stRate\030\016 \001(\001\022\020\n\010HighRate\030\017 \001(\001\022\017\n\007LowRat"
    "e\030\020 \001(\001\022\021\n\tCloseRate\030\021 \001(\001\022\027\n\017WeightedAv"
    "gRate\030\022 \001(\001\022\023\n\013TradeVolume\030\023 \001(\001\"\320\001\n\037Sta"
    "ndardisedBondForwardSnapshot\022\023\n\013TradeMet"
    "hod\030\001 \001(\005\022\016\n\006OpenPx\030\013 \001(\001\022\016\n\006HighPx\030\014 \001("
    "\001\022\r\n\005LowPx\030\r \001(\001\022\016\n\006LastPx\030\016 \001(\001\022\030\n\020Tota"
    "lVolumeTrade\030\017 \001(\001\022\027\n\017LastVolumeTrade\030\020 "
    "\001(\001\022\020\n\010SettlePx\0302 \001(\001\022\024\n\014SettlePxDate\0303 "
    "\001(\tB<\n\032com.htsc.mdc.insight.modelB\031MDCfe"
    "tsBondSnapshotProtosH\001\240\001\001b\006proto3", 2433);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsBondSnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_once_);
void protobuf_AddDesc_MDCfetsBondSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsBondSnapshot_2eproto {
  StaticDescriptorInitializer_MDCfetsBondSnapshot_2eproto() {
    protobuf_AddDesc_MDCfetsBondSnapshot_2eproto();
  }
} static_descriptor_initializer_MDCfetsBondSnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsBondSnapshot::kHTSCSecurityIDFieldNumber;
const int MDCfetsBondSnapshot::kSecurityTypeFieldNumber;
const int MDCfetsBondSnapshot::kSecurityIDSourceFieldNumber;
const int MDCfetsBondSnapshot::kMDDateFieldNumber;
const int MDCfetsBondSnapshot::kMDTimeFieldNumber;
const int MDCfetsBondSnapshot::kDataTimestampFieldNumber;
const int MDCfetsBondSnapshot::kTransactTimeFieldNumber;
const int MDCfetsBondSnapshot::kMarketIndicatorFieldNumber;
const int MDCfetsBondSnapshot::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsBondSnapshot::kBondSnapshotTypeFieldNumber;
const int MDCfetsBondSnapshot::kCashBondTradingSnapshotFieldNumber;
const int MDCfetsBondSnapshot::kBondForwardSnapshotFieldNumber;
const int MDCfetsBondSnapshot::kBondLendingSnapshotFieldNumber;
const int MDCfetsBondSnapshot::kStandardisedBondForwardSnapshotFieldNumber;
const int MDCfetsBondSnapshot::kMessageNumberFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsBondSnapshot::MDCfetsBondSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
}

void MDCfetsBondSnapshot::InitAsDefaultInstance() {
  cashbondtradingsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::CashBondTradingSnapshot*>(
      ::com::htsc::mdc::insight::model::CashBondTradingSnapshot::internal_default_instance());
  bondforwardsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::BondForwardSnapshot*>(
      ::com::htsc::mdc::insight::model::BondForwardSnapshot::internal_default_instance());
  bondlendingsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::BondLendingSnapshot*>(
      ::com::htsc::mdc::insight::model::BondLendingSnapshot::internal_default_instance());
  standardisedbondforwardsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot*>(
      ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot::internal_default_instance());
}

MDCfetsBondSnapshot::MDCfetsBondSnapshot(const MDCfetsBondSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
}

void MDCfetsBondSnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cashbondtradingsnapshot_ = NULL;
  bondforwardsnapshot_ = NULL;
  bondlendingsnapshot_ = NULL;
  standardisedbondforwardsnapshot_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&messagenumber_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(messagenumber_));
  _cached_size_ = 0;
}

MDCfetsBondSnapshot::~MDCfetsBondSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  SharedDtor();
}

void MDCfetsBondSnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsBondSnapshot_default_instance_.get()) {
    delete cashbondtradingsnapshot_;
    delete bondforwardsnapshot_;
    delete bondlendingsnapshot_;
    delete standardisedbondforwardsnapshot_;
  }
}

void MDCfetsBondSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsBondSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsBondSnapshot_descriptor_;
}

const MDCfetsBondSnapshot& MDCfetsBondSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBondSnapshot> MDCfetsBondSnapshot_default_instance_;

MDCfetsBondSnapshot* MDCfetsBondSnapshot::New(::google::protobuf::Arena* arena) const {
  MDCfetsBondSnapshot* n = new MDCfetsBondSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsBondSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsBondSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsBondSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(datamultiplepowerof10_, messagenumber_);
  if (GetArenaNoVirtual() == NULL && cashbondtradingsnapshot_ != NULL) delete cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && bondforwardsnapshot_ != NULL) delete bondforwardsnapshot_;
  bondforwardsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && bondlendingsnapshot_ != NULL) delete bondlendingsnapshot_;
  bondlendingsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && standardisedbondforwardsnapshot_ != NULL) delete standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsBondSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_BondSnapshotType;
        break;
      }

      // optional int32 BondSnapshotType = 16;
      case 16: {
        if (tag == 128) {
         parse_BondSnapshotType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bondsnapshottype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_CashBondTradingSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
      case 17: {
        if (tag == 138) {
         parse_CashBondTradingSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_cashbondtradingsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_BondForwardSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
      case 18: {
        if (tag == 146) {
         parse_BondForwardSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondforwardsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_BondLendingSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
      case 19: {
        if (tag == 154) {
         parse_BondLendingSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_bondlendingsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_StandardisedBondForwardSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
      case 20: {
        if (tag == 162) {
         parse_StandardisedBondForwardSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_standardisedbondforwardsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(800)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 100;
      case 100: {
        if (tag == 800) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  return false;
#undef DO_
}

void MDCfetsBondSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional int32 BondSnapshotType = 16;
  if (this->bondsnapshottype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->bondsnapshottype(), output);
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
  if (this->has_cashbondtradingsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->cashbondtradingsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
  if (this->has_bondforwardsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->bondforwardsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
  if (this->has_bondlendingsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->bondlendingsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
  if (this->has_standardisedbondforwardsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->standardisedbondforwardsnapshot_, output);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(100, this->messagenumber(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
}

::google::protobuf::uint8* MDCfetsBondSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional int32 BondSnapshotType = 16;
  if (this->bondsnapshottype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->bondsnapshottype(), target);
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
  if (this->has_cashbondtradingsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->cashbondtradingsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
  if (this->has_bondforwardsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->bondforwardsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
  if (this->has_bondlendingsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->bondlendingsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
  if (this->has_standardisedbondforwardsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->standardisedbondforwardsnapshot_, false, target);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(100, this->messagenumber(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  return target;
}

size_t MDCfetsBondSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 BondSnapshotType = 16;
  if (this->bondsnapshottype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bondsnapshottype());
  }

  // optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
  if (this->has_cashbondtradingsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->cashbondtradingsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
  if (this->has_bondforwardsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondforwardsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
  if (this->has_bondlendingsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->bondlendingsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
  if (this->has_standardisedbondforwardsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->standardisedbondforwardsnapshot_);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsBondSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsBondSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsBondSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsBondSnapshot::MergeFrom(const MDCfetsBondSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsBondSnapshot::UnsafeMergeFrom(const MDCfetsBondSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.bondsnapshottype() != 0) {
    set_bondsnapshottype(from.bondsnapshottype());
  }
  if (from.has_cashbondtradingsnapshot()) {
    mutable_cashbondtradingsnapshot()->::com::htsc::mdc::insight::model::CashBondTradingSnapshot::MergeFrom(from.cashbondtradingsnapshot());
  }
  if (from.has_bondforwardsnapshot()) {
    mutable_bondforwardsnapshot()->::com::htsc::mdc::insight::model::BondForwardSnapshot::MergeFrom(from.bondforwardsnapshot());
  }
  if (from.has_bondlendingsnapshot()) {
    mutable_bondlendingsnapshot()->::com::htsc::mdc::insight::model::BondLendingSnapshot::MergeFrom(from.bondlendingsnapshot());
  }
  if (from.has_standardisedbondforwardsnapshot()) {
    mutable_standardisedbondforwardsnapshot()->::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot::MergeFrom(from.standardisedbondforwardsnapshot());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
}

void MDCfetsBondSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsBondSnapshot::CopyFrom(const MDCfetsBondSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsBondSnapshot::IsInitialized() const {

  return true;
}

void MDCfetsBondSnapshot::Swap(MDCfetsBondSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsBondSnapshot::InternalSwap(MDCfetsBondSnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(bondsnapshottype_, other->bondsnapshottype_);
  std::swap(cashbondtradingsnapshot_, other->cashbondtradingsnapshot_);
  std::swap(bondforwardsnapshot_, other->bondforwardsnapshot_);
  std::swap(bondlendingsnapshot_, other->bondlendingsnapshot_);
  std::swap(standardisedbondforwardsnapshot_, other->standardisedbondforwardsnapshot_);
  std::swap(messagenumber_, other->messagenumber_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsBondSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsBondSnapshot_descriptor_;
  metadata.reflection = MDCfetsBondSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBondSnapshot

// optional string HTSCSecurityID = 1;
void MDCfetsBondSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
void MDCfetsBondSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
void MDCfetsBondSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
::std::string* MDCfetsBondSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsBondSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsBondSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsBondSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsBondSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsBondSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsBondSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsBondSnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsBondSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDDate)
  return mddate_;
}
void MDCfetsBondSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsBondSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsBondSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDTime)
  return mdtime_;
}
void MDCfetsBondSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsBondSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBondSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsBondSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsBondSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
void MDCfetsBondSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
void MDCfetsBondSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
::std::string* MDCfetsBondSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsBondSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsBondSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
void MDCfetsBondSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
void MDCfetsBondSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
::std::string* MDCfetsBondSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsBondSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsBondSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDCfetsBondSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsBondSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsBondSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataMultiplePowerOf10)
}

// optional int32 BondSnapshotType = 16;
void MDCfetsBondSnapshot::clear_bondsnapshottype() {
  bondsnapshottype_ = 0;
}
::google::protobuf::int32 MDCfetsBondSnapshot::bondsnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondSnapshotType)
  return bondsnapshottype_;
}
void MDCfetsBondSnapshot::set_bondsnapshottype(::google::protobuf::int32 value) {
  
  bondsnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondSnapshotType)
}

// optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
bool MDCfetsBondSnapshot::has_cashbondtradingsnapshot() const {
  return this != internal_default_instance() && cashbondtradingsnapshot_ != NULL;
}
void MDCfetsBondSnapshot::clear_cashbondtradingsnapshot() {
  if (GetArenaNoVirtual() == NULL && cashbondtradingsnapshot_ != NULL) delete cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::CashBondTradingSnapshot& MDCfetsBondSnapshot::cashbondtradingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  return cashbondtradingsnapshot_ != NULL ? *cashbondtradingsnapshot_
                         : *::com::htsc::mdc::insight::model::CashBondTradingSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::CashBondTradingSnapshot* MDCfetsBondSnapshot::mutable_cashbondtradingsnapshot() {
  
  if (cashbondtradingsnapshot_ == NULL) {
    cashbondtradingsnapshot_ = new ::com::htsc::mdc::insight::model::CashBondTradingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  return cashbondtradingsnapshot_;
}
::com::htsc::mdc::insight::model::CashBondTradingSnapshot* MDCfetsBondSnapshot::release_cashbondtradingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  
  ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* temp = cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = NULL;
  return temp;
}
void MDCfetsBondSnapshot::set_allocated_cashbondtradingsnapshot(::com::htsc::mdc::insight::model::CashBondTradingSnapshot* cashbondtradingsnapshot) {
  delete cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = cashbondtradingsnapshot;
  if (cashbondtradingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
}

// optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
bool MDCfetsBondSnapshot::has_bondforwardsnapshot() const {
  return this != internal_default_instance() && bondforwardsnapshot_ != NULL;
}
void MDCfetsBondSnapshot::clear_bondforwardsnapshot() {
  if (GetArenaNoVirtual() == NULL && bondforwardsnapshot_ != NULL) delete bondforwardsnapshot_;
  bondforwardsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::BondForwardSnapshot& MDCfetsBondSnapshot::bondforwardsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  return bondforwardsnapshot_ != NULL ? *bondforwardsnapshot_
                         : *::com::htsc::mdc::insight::model::BondForwardSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::BondForwardSnapshot* MDCfetsBondSnapshot::mutable_bondforwardsnapshot() {
  
  if (bondforwardsnapshot_ == NULL) {
    bondforwardsnapshot_ = new ::com::htsc::mdc::insight::model::BondForwardSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  return bondforwardsnapshot_;
}
::com::htsc::mdc::insight::model::BondForwardSnapshot* MDCfetsBondSnapshot::release_bondforwardsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  
  ::com::htsc::mdc::insight::model::BondForwardSnapshot* temp = bondforwardsnapshot_;
  bondforwardsnapshot_ = NULL;
  return temp;
}
void MDCfetsBondSnapshot::set_allocated_bondforwardsnapshot(::com::htsc::mdc::insight::model::BondForwardSnapshot* bondforwardsnapshot) {
  delete bondforwardsnapshot_;
  bondforwardsnapshot_ = bondforwardsnapshot;
  if (bondforwardsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
}

// optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
bool MDCfetsBondSnapshot::has_bondlendingsnapshot() const {
  return this != internal_default_instance() && bondlendingsnapshot_ != NULL;
}
void MDCfetsBondSnapshot::clear_bondlendingsnapshot() {
  if (GetArenaNoVirtual() == NULL && bondlendingsnapshot_ != NULL) delete bondlendingsnapshot_;
  bondlendingsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::BondLendingSnapshot& MDCfetsBondSnapshot::bondlendingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  return bondlendingsnapshot_ != NULL ? *bondlendingsnapshot_
                         : *::com::htsc::mdc::insight::model::BondLendingSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::BondLendingSnapshot* MDCfetsBondSnapshot::mutable_bondlendingsnapshot() {
  
  if (bondlendingsnapshot_ == NULL) {
    bondlendingsnapshot_ = new ::com::htsc::mdc::insight::model::BondLendingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  return bondlendingsnapshot_;
}
::com::htsc::mdc::insight::model::BondLendingSnapshot* MDCfetsBondSnapshot::release_bondlendingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  
  ::com::htsc::mdc::insight::model::BondLendingSnapshot* temp = bondlendingsnapshot_;
  bondlendingsnapshot_ = NULL;
  return temp;
}
void MDCfetsBondSnapshot::set_allocated_bondlendingsnapshot(::com::htsc::mdc::insight::model::BondLendingSnapshot* bondlendingsnapshot) {
  delete bondlendingsnapshot_;
  bondlendingsnapshot_ = bondlendingsnapshot;
  if (bondlendingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
}

// optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
bool MDCfetsBondSnapshot::has_standardisedbondforwardsnapshot() const {
  return this != internal_default_instance() && standardisedbondforwardsnapshot_ != NULL;
}
void MDCfetsBondSnapshot::clear_standardisedbondforwardsnapshot() {
  if (GetArenaNoVirtual() == NULL && standardisedbondforwardsnapshot_ != NULL) delete standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot& MDCfetsBondSnapshot::standardisedbondforwardsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  return standardisedbondforwardsnapshot_ != NULL ? *standardisedbondforwardsnapshot_
                         : *::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* MDCfetsBondSnapshot::mutable_standardisedbondforwardsnapshot() {
  
  if (standardisedbondforwardsnapshot_ == NULL) {
    standardisedbondforwardsnapshot_ = new ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  return standardisedbondforwardsnapshot_;
}
::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* MDCfetsBondSnapshot::release_standardisedbondforwardsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  
  ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* temp = standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = NULL;
  return temp;
}
void MDCfetsBondSnapshot::set_allocated_standardisedbondforwardsnapshot(::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* standardisedbondforwardsnapshot) {
  delete standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = standardisedbondforwardsnapshot;
  if (standardisedbondforwardsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
}

// optional int64 MessageNumber = 100;
void MDCfetsBondSnapshot::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsBondSnapshot::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MessageNumber)
  return messagenumber_;
}
void MDCfetsBondSnapshot::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MessageNumber)
}

inline const MDCfetsBondSnapshot* MDCfetsBondSnapshot::internal_default_instance() {
  return &MDCfetsBondSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CashBondTradingSnapshot::kTradeMethodFieldNumber;
const int CashBondTradingSnapshot::kSettlTypeFieldNumber;
const int CashBondTradingSnapshot::kSideFieldNumber;
const int CashBondTradingSnapshot::kPreMarketBondIndicatorFieldNumber;
const int CashBondTradingSnapshot::kPreCloseCleanPxFieldNumber;
const int CashBondTradingSnapshot::kPreWeightedAvgCleanPxFieldNumber;
const int CashBondTradingSnapshot::kOpenCleanPxFieldNumber;
const int CashBondTradingSnapshot::kLastCleanPxFieldNumber;
const int CashBondTradingSnapshot::kChangePercentFieldNumber;
const int CashBondTradingSnapshot::kHighCleanPxFieldNumber;
const int CashBondTradingSnapshot::kLowCleanPxFieldNumber;
const int CashBondTradingSnapshot::kCloseCleanPxFieldNumber;
const int CashBondTradingSnapshot::kWeightedAvgCleanPxFieldNumber;
const int CashBondTradingSnapshot::kPreCloseYieldFieldNumber;
const int CashBondTradingSnapshot::kPreWeightedAvgYieldFieldNumber;
const int CashBondTradingSnapshot::kOpenYieldFieldNumber;
const int CashBondTradingSnapshot::kLastYieldFieldNumber;
const int CashBondTradingSnapshot::kHighYieldFieldNumber;
const int CashBondTradingSnapshot::kLowYieldFieldNumber;
const int CashBondTradingSnapshot::kCloseYieldFieldNumber;
const int CashBondTradingSnapshot::kWeightedAvgYieldFieldNumber;
const int CashBondTradingSnapshot::kTradeVolumeFieldNumber;
const int CashBondTradingSnapshot::kCleanPxChangeFieldNumber;
const int CashBondTradingSnapshot::kCleanPxChangePercentFieldNumber;
const int CashBondTradingSnapshot::kYieldChangeBPFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CashBondTradingSnapshot::CashBondTradingSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
}

void CashBondTradingSnapshot::InitAsDefaultInstance() {
}

CashBondTradingSnapshot::CashBondTradingSnapshot(const CashBondTradingSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
}

void CashBondTradingSnapshot::SharedCtor() {
  settltype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  side_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&trademethod_, 0, reinterpret_cast<char*>(&yieldchangebp_) -
    reinterpret_cast<char*>(&trademethod_) + sizeof(yieldchangebp_));
  _cached_size_ = 0;
}

CashBondTradingSnapshot::~CashBondTradingSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  SharedDtor();
}

void CashBondTradingSnapshot::SharedDtor() {
  settltype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  side_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CashBondTradingSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CashBondTradingSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CashBondTradingSnapshot_descriptor_;
}

const CashBondTradingSnapshot& CashBondTradingSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CashBondTradingSnapshot> CashBondTradingSnapshot_default_instance_;

CashBondTradingSnapshot* CashBondTradingSnapshot::New(::google::protobuf::Arena* arena) const {
  CashBondTradingSnapshot* n = new CashBondTradingSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CashBondTradingSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(CashBondTradingSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<CashBondTradingSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(trademethod_, lastcleanpx_);
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(changepercent_, openyield_);
  ZR_(lastyield_, cleanpxchangepercent_);
  yieldchangebp_ = 0;

#undef ZR_HELPER_
#undef ZR_

}

bool CashBondTradingSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeMethod = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_SettlType;
        break;
      }

      // optional string SettlType = 2;
      case 2: {
        if (tag == 18) {
         parse_SettlType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settltype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settltype().data(), this->settltype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Side;
        break;
      }

      // optional string Side = 3;
      case 3: {
        if (tag == 26) {
         parse_Side:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_side()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->side().data(), this->side().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_PreMarketBondIndicator;
        break;
      }

      // optional bool PreMarketBondIndicator = 4;
      case 4: {
        if (tag == 32) {
         parse_PreMarketBondIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &premarketbondindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_PreCloseCleanPx;
        break;
      }

      // optional double PreCloseCleanPx = 11;
      case 11: {
        if (tag == 89) {
         parse_PreCloseCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preclosecleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_PreWeightedAvgCleanPx;
        break;
      }

      // optional double PreWeightedAvgCleanPx = 12;
      case 12: {
        if (tag == 97) {
         parse_PreWeightedAvgCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_OpenCleanPx;
        break;
      }

      // optional double OpenCleanPx = 13;
      case 13: {
        if (tag == 105) {
         parse_OpenCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &opencleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_LastCleanPx;
        break;
      }

      // optional double LastCleanPx = 14;
      case 14: {
        if (tag == 113) {
         parse_LastCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_ChangePercent;
        break;
      }

      // optional double ChangePercent = 15;
      case 15: {
        if (tag == 121) {
         parse_ChangePercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &changepercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_HighCleanPx;
        break;
      }

      // optional double HighCleanPx = 16;
      case 16: {
        if (tag == 129) {
         parse_HighCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_LowCleanPx;
        break;
      }

      // optional double LowCleanPx = 17;
      case 17: {
        if (tag == 137) {
         parse_LowCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_CloseCleanPx;
        break;
      }

      // optional double CloseCleanPx = 18;
      case 18: {
        if (tag == 145) {
         parse_CloseCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closecleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_WeightedAvgCleanPx;
        break;
      }

      // optional double WeightedAvgCleanPx = 19;
      case 19: {
        if (tag == 153) {
         parse_WeightedAvgCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(161)) goto parse_PreCloseYield;
        break;
      }

      // optional double PreCloseYield = 20;
      case 20: {
        if (tag == 161) {
         parse_PreCloseYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &precloseyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(169)) goto parse_PreWeightedAvgYield;
        break;
      }

      // optional double PreWeightedAvgYield = 21;
      case 21: {
        if (tag == 169) {
         parse_PreWeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_OpenYield;
        break;
      }

      // optional double OpenYield = 22;
      case 22: {
        if (tag == 177) {
         parse_OpenYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(185)) goto parse_LastYield;
        break;
      }

      // optional double LastYield = 23;
      case 23: {
        if (tag == 185) {
         parse_LastYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(193)) goto parse_HighYield;
        break;
      }

      // optional double HighYield = 24;
      case 24: {
        if (tag == 193) {
         parse_HighYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(201)) goto parse_LowYield;
        break;
      }

      // optional double LowYield = 25;
      case 25: {
        if (tag == 201) {
         parse_LowYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(209)) goto parse_CloseYield;
        break;
      }

      // optional double CloseYield = 26;
      case 26: {
        if (tag == 209) {
         parse_CloseYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closeyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(217)) goto parse_WeightedAvgYield;
        break;
      }

      // optional double WeightedAvgYield = 27;
      case 27: {
        if (tag == 217) {
         parse_WeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(225)) goto parse_TradeVolume;
        break;
      }

      // optional double TradeVolume = 28;
      case 28: {
        if (tag == 225) {
         parse_TradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(233)) goto parse_CleanPxChange;
        break;
      }

      // optional double CleanPxChange = 29;
      case 29: {
        if (tag == 233) {
         parse_CleanPxChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cleanpxchange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(241)) goto parse_CleanPxChangePercent;
        break;
      }

      // optional double CleanPxChangePercent = 30;
      case 30: {
        if (tag == 241) {
         parse_CleanPxChangePercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cleanpxchangepercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(249)) goto parse_YieldChangeBP;
        break;
      }

      // optional double YieldChangeBP = 31;
      case 31: {
        if (tag == 249) {
         parse_YieldChangeBP:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &yieldchangebp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  return false;
#undef DO_
}

void CashBondTradingSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->trademethod(), output);
  }

  // optional string SettlType = 2;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->settltype(), output);
  }

  // optional string Side = 3;
  if (this->side().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->side().data(), this->side().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->side(), output);
  }

  // optional bool PreMarketBondIndicator = 4;
  if (this->premarketbondindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->premarketbondindicator(), output);
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->preclosecleanpx(), output);
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->preweightedavgcleanpx(), output);
  }

  // optional double OpenCleanPx = 13;
  if (this->opencleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->opencleanpx(), output);
  }

  // optional double LastCleanPx = 14;
  if (this->lastcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->lastcleanpx(), output);
  }

  // optional double ChangePercent = 15;
  if (this->changepercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->changepercent(), output);
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->highcleanpx(), output);
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->lowcleanpx(), output);
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->closecleanpx(), output);
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->weightedavgcleanpx(), output);
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(20, this->precloseyield(), output);
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(21, this->preweightedavgyield(), output);
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->openyield(), output);
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(23, this->lastyield(), output);
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(24, this->highyield(), output);
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(25, this->lowyield(), output);
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(26, this->closeyield(), output);
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(27, this->weightedavgyield(), output);
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(28, this->tradevolume(), output);
  }

  // optional double CleanPxChange = 29;
  if (this->cleanpxchange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(29, this->cleanpxchange(), output);
  }

  // optional double CleanPxChangePercent = 30;
  if (this->cleanpxchangepercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(30, this->cleanpxchangepercent(), output);
  }

  // optional double YieldChangeBP = 31;
  if (this->yieldchangebp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(31, this->yieldchangebp(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
}

::google::protobuf::uint8* CashBondTradingSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->trademethod(), target);
  }

  // optional string SettlType = 2;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->settltype(), target);
  }

  // optional string Side = 3;
  if (this->side().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->side().data(), this->side().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->side(), target);
  }

  // optional bool PreMarketBondIndicator = 4;
  if (this->premarketbondindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->premarketbondindicator(), target);
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->preclosecleanpx(), target);
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->preweightedavgcleanpx(), target);
  }

  // optional double OpenCleanPx = 13;
  if (this->opencleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->opencleanpx(), target);
  }

  // optional double LastCleanPx = 14;
  if (this->lastcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->lastcleanpx(), target);
  }

  // optional double ChangePercent = 15;
  if (this->changepercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->changepercent(), target);
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->highcleanpx(), target);
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->lowcleanpx(), target);
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->closecleanpx(), target);
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->weightedavgcleanpx(), target);
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(20, this->precloseyield(), target);
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(21, this->preweightedavgyield(), target);
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->openyield(), target);
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(23, this->lastyield(), target);
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(24, this->highyield(), target);
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(25, this->lowyield(), target);
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(26, this->closeyield(), target);
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(27, this->weightedavgyield(), target);
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(28, this->tradevolume(), target);
  }

  // optional double CleanPxChange = 29;
  if (this->cleanpxchange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(29, this->cleanpxchange(), target);
  }

  // optional double CleanPxChangePercent = 30;
  if (this->cleanpxchangepercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(30, this->cleanpxchangepercent(), target);
  }

  // optional double YieldChangeBP = 31;
  if (this->yieldchangebp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(31, this->yieldchangebp(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  return target;
}

size_t CashBondTradingSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  size_t total_size = 0;

  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional string SettlType = 2;
  if (this->settltype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settltype());
  }

  // optional string Side = 3;
  if (this->side().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->side());
  }

  // optional bool PreMarketBondIndicator = 4;
  if (this->premarketbondindicator() != 0) {
    total_size += 1 + 1;
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double OpenCleanPx = 13;
  if (this->opencleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastCleanPx = 14;
  if (this->lastcleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double ChangePercent = 15;
  if (this->changepercent() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    total_size += 2 + 8;
  }

  // optional double CleanPxChange = 29;
  if (this->cleanpxchange() != 0) {
    total_size += 2 + 8;
  }

  // optional double CleanPxChangePercent = 30;
  if (this->cleanpxchangepercent() != 0) {
    total_size += 2 + 8;
  }

  // optional double YieldChangeBP = 31;
  if (this->yieldchangebp() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CashBondTradingSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CashBondTradingSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CashBondTradingSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void CashBondTradingSnapshot::MergeFrom(const CashBondTradingSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CashBondTradingSnapshot::UnsafeMergeFrom(const CashBondTradingSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.settltype().size() > 0) {

    settltype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settltype_);
  }
  if (from.side().size() > 0) {

    side_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.side_);
  }
  if (from.premarketbondindicator() != 0) {
    set_premarketbondindicator(from.premarketbondindicator());
  }
  if (from.preclosecleanpx() != 0) {
    set_preclosecleanpx(from.preclosecleanpx());
  }
  if (from.preweightedavgcleanpx() != 0) {
    set_preweightedavgcleanpx(from.preweightedavgcleanpx());
  }
  if (from.opencleanpx() != 0) {
    set_opencleanpx(from.opencleanpx());
  }
  if (from.lastcleanpx() != 0) {
    set_lastcleanpx(from.lastcleanpx());
  }
  if (from.changepercent() != 0) {
    set_changepercent(from.changepercent());
  }
  if (from.highcleanpx() != 0) {
    set_highcleanpx(from.highcleanpx());
  }
  if (from.lowcleanpx() != 0) {
    set_lowcleanpx(from.lowcleanpx());
  }
  if (from.closecleanpx() != 0) {
    set_closecleanpx(from.closecleanpx());
  }
  if (from.weightedavgcleanpx() != 0) {
    set_weightedavgcleanpx(from.weightedavgcleanpx());
  }
  if (from.precloseyield() != 0) {
    set_precloseyield(from.precloseyield());
  }
  if (from.preweightedavgyield() != 0) {
    set_preweightedavgyield(from.preweightedavgyield());
  }
  if (from.openyield() != 0) {
    set_openyield(from.openyield());
  }
  if (from.lastyield() != 0) {
    set_lastyield(from.lastyield());
  }
  if (from.highyield() != 0) {
    set_highyield(from.highyield());
  }
  if (from.lowyield() != 0) {
    set_lowyield(from.lowyield());
  }
  if (from.closeyield() != 0) {
    set_closeyield(from.closeyield());
  }
  if (from.weightedavgyield() != 0) {
    set_weightedavgyield(from.weightedavgyield());
  }
  if (from.tradevolume() != 0) {
    set_tradevolume(from.tradevolume());
  }
  if (from.cleanpxchange() != 0) {
    set_cleanpxchange(from.cleanpxchange());
  }
  if (from.cleanpxchangepercent() != 0) {
    set_cleanpxchangepercent(from.cleanpxchangepercent());
  }
  if (from.yieldchangebp() != 0) {
    set_yieldchangebp(from.yieldchangebp());
  }
}

void CashBondTradingSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CashBondTradingSnapshot::CopyFrom(const CashBondTradingSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CashBondTradingSnapshot::IsInitialized() const {

  return true;
}

void CashBondTradingSnapshot::Swap(CashBondTradingSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CashBondTradingSnapshot::InternalSwap(CashBondTradingSnapshot* other) {
  std::swap(trademethod_, other->trademethod_);
  settltype_.Swap(&other->settltype_);
  side_.Swap(&other->side_);
  std::swap(premarketbondindicator_, other->premarketbondindicator_);
  std::swap(preclosecleanpx_, other->preclosecleanpx_);
  std::swap(preweightedavgcleanpx_, other->preweightedavgcleanpx_);
  std::swap(opencleanpx_, other->opencleanpx_);
  std::swap(lastcleanpx_, other->lastcleanpx_);
  std::swap(changepercent_, other->changepercent_);
  std::swap(highcleanpx_, other->highcleanpx_);
  std::swap(lowcleanpx_, other->lowcleanpx_);
  std::swap(closecleanpx_, other->closecleanpx_);
  std::swap(weightedavgcleanpx_, other->weightedavgcleanpx_);
  std::swap(precloseyield_, other->precloseyield_);
  std::swap(preweightedavgyield_, other->preweightedavgyield_);
  std::swap(openyield_, other->openyield_);
  std::swap(lastyield_, other->lastyield_);
  std::swap(highyield_, other->highyield_);
  std::swap(lowyield_, other->lowyield_);
  std::swap(closeyield_, other->closeyield_);
  std::swap(weightedavgyield_, other->weightedavgyield_);
  std::swap(tradevolume_, other->tradevolume_);
  std::swap(cleanpxchange_, other->cleanpxchange_);
  std::swap(cleanpxchangepercent_, other->cleanpxchangepercent_);
  std::swap(yieldchangebp_, other->yieldchangebp_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CashBondTradingSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CashBondTradingSnapshot_descriptor_;
  metadata.reflection = CashBondTradingSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CashBondTradingSnapshot

// optional int32 TradeMethod = 1;
void CashBondTradingSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 CashBondTradingSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeMethod)
  return trademethod_;
}
void CashBondTradingSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeMethod)
}

// optional string SettlType = 2;
void CashBondTradingSnapshot::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CashBondTradingSnapshot::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingSnapshot::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
void CashBondTradingSnapshot::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
void CashBondTradingSnapshot::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
::std::string* CashBondTradingSnapshot::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CashBondTradingSnapshot::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingSnapshot::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}

// optional string Side = 3;
void CashBondTradingSnapshot::clear_side() {
  side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CashBondTradingSnapshot::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  return side_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingSnapshot::set_side(const ::std::string& value) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
void CashBondTradingSnapshot::set_side(const char* value) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
void CashBondTradingSnapshot::set_side(const char* value, size_t size) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
::std::string* CashBondTradingSnapshot::mutable_side() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  return side_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CashBondTradingSnapshot::release_side() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  
  return side_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CashBondTradingSnapshot::set_allocated_side(::std::string* side) {
  if (side != NULL) {
    
  } else {
    
  }
  side_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), side);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}

// optional bool PreMarketBondIndicator = 4;
void CashBondTradingSnapshot::clear_premarketbondindicator() {
  premarketbondindicator_ = false;
}
bool CashBondTradingSnapshot::premarketbondindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreMarketBondIndicator)
  return premarketbondindicator_;
}
void CashBondTradingSnapshot::set_premarketbondindicator(bool value) {
  
  premarketbondindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreMarketBondIndicator)
}

// optional double PreCloseCleanPx = 11;
void CashBondTradingSnapshot::clear_preclosecleanpx() {
  preclosecleanpx_ = 0;
}
double CashBondTradingSnapshot::preclosecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseCleanPx)
  return preclosecleanpx_;
}
void CashBondTradingSnapshot::set_preclosecleanpx(double value) {
  
  preclosecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseCleanPx)
}

// optional double PreWeightedAvgCleanPx = 12;
void CashBondTradingSnapshot::clear_preweightedavgcleanpx() {
  preweightedavgcleanpx_ = 0;
}
double CashBondTradingSnapshot::preweightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgCleanPx)
  return preweightedavgcleanpx_;
}
void CashBondTradingSnapshot::set_preweightedavgcleanpx(double value) {
  
  preweightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgCleanPx)
}

// optional double OpenCleanPx = 13;
void CashBondTradingSnapshot::clear_opencleanpx() {
  opencleanpx_ = 0;
}
double CashBondTradingSnapshot::opencleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenCleanPx)
  return opencleanpx_;
}
void CashBondTradingSnapshot::set_opencleanpx(double value) {
  
  opencleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenCleanPx)
}

// optional double LastCleanPx = 14;
void CashBondTradingSnapshot::clear_lastcleanpx() {
  lastcleanpx_ = 0;
}
double CashBondTradingSnapshot::lastcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastCleanPx)
  return lastcleanpx_;
}
void CashBondTradingSnapshot::set_lastcleanpx(double value) {
  
  lastcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastCleanPx)
}

// optional double ChangePercent = 15;
void CashBondTradingSnapshot::clear_changepercent() {
  changepercent_ = 0;
}
double CashBondTradingSnapshot::changepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.ChangePercent)
  return changepercent_;
}
void CashBondTradingSnapshot::set_changepercent(double value) {
  
  changepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.ChangePercent)
}

// optional double HighCleanPx = 16;
void CashBondTradingSnapshot::clear_highcleanpx() {
  highcleanpx_ = 0;
}
double CashBondTradingSnapshot::highcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighCleanPx)
  return highcleanpx_;
}
void CashBondTradingSnapshot::set_highcleanpx(double value) {
  
  highcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighCleanPx)
}

// optional double LowCleanPx = 17;
void CashBondTradingSnapshot::clear_lowcleanpx() {
  lowcleanpx_ = 0;
}
double CashBondTradingSnapshot::lowcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowCleanPx)
  return lowcleanpx_;
}
void CashBondTradingSnapshot::set_lowcleanpx(double value) {
  
  lowcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowCleanPx)
}

// optional double CloseCleanPx = 18;
void CashBondTradingSnapshot::clear_closecleanpx() {
  closecleanpx_ = 0;
}
double CashBondTradingSnapshot::closecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseCleanPx)
  return closecleanpx_;
}
void CashBondTradingSnapshot::set_closecleanpx(double value) {
  
  closecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseCleanPx)
}

// optional double WeightedAvgCleanPx = 19;
void CashBondTradingSnapshot::clear_weightedavgcleanpx() {
  weightedavgcleanpx_ = 0;
}
double CashBondTradingSnapshot::weightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgCleanPx)
  return weightedavgcleanpx_;
}
void CashBondTradingSnapshot::set_weightedavgcleanpx(double value) {
  
  weightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgCleanPx)
}

// optional double PreCloseYield = 20;
void CashBondTradingSnapshot::clear_precloseyield() {
  precloseyield_ = 0;
}
double CashBondTradingSnapshot::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseYield)
  return precloseyield_;
}
void CashBondTradingSnapshot::set_precloseyield(double value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseYield)
}

// optional double PreWeightedAvgYield = 21;
void CashBondTradingSnapshot::clear_preweightedavgyield() {
  preweightedavgyield_ = 0;
}
double CashBondTradingSnapshot::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgYield)
  return preweightedavgyield_;
}
void CashBondTradingSnapshot::set_preweightedavgyield(double value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgYield)
}

// optional double OpenYield = 22;
void CashBondTradingSnapshot::clear_openyield() {
  openyield_ = 0;
}
double CashBondTradingSnapshot::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenYield)
  return openyield_;
}
void CashBondTradingSnapshot::set_openyield(double value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenYield)
}

// optional double LastYield = 23;
void CashBondTradingSnapshot::clear_lastyield() {
  lastyield_ = 0;
}
double CashBondTradingSnapshot::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastYield)
  return lastyield_;
}
void CashBondTradingSnapshot::set_lastyield(double value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastYield)
}

// optional double HighYield = 24;
void CashBondTradingSnapshot::clear_highyield() {
  highyield_ = 0;
}
double CashBondTradingSnapshot::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighYield)
  return highyield_;
}
void CashBondTradingSnapshot::set_highyield(double value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighYield)
}

// optional double LowYield = 25;
void CashBondTradingSnapshot::clear_lowyield() {
  lowyield_ = 0;
}
double CashBondTradingSnapshot::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowYield)
  return lowyield_;
}
void CashBondTradingSnapshot::set_lowyield(double value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowYield)
}

// optional double CloseYield = 26;
void CashBondTradingSnapshot::clear_closeyield() {
  closeyield_ = 0;
}
double CashBondTradingSnapshot::closeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseYield)
  return closeyield_;
}
void CashBondTradingSnapshot::set_closeyield(double value) {
  
  closeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseYield)
}

// optional double WeightedAvgYield = 27;
void CashBondTradingSnapshot::clear_weightedavgyield() {
  weightedavgyield_ = 0;
}
double CashBondTradingSnapshot::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgYield)
  return weightedavgyield_;
}
void CashBondTradingSnapshot::set_weightedavgyield(double value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgYield)
}

// optional double TradeVolume = 28;
void CashBondTradingSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
double CashBondTradingSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeVolume)
  return tradevolume_;
}
void CashBondTradingSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeVolume)
}

// optional double CleanPxChange = 29;
void CashBondTradingSnapshot::clear_cleanpxchange() {
  cleanpxchange_ = 0;
}
double CashBondTradingSnapshot::cleanpxchange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChange)
  return cleanpxchange_;
}
void CashBondTradingSnapshot::set_cleanpxchange(double value) {
  
  cleanpxchange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChange)
}

// optional double CleanPxChangePercent = 30;
void CashBondTradingSnapshot::clear_cleanpxchangepercent() {
  cleanpxchangepercent_ = 0;
}
double CashBondTradingSnapshot::cleanpxchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChangePercent)
  return cleanpxchangepercent_;
}
void CashBondTradingSnapshot::set_cleanpxchangepercent(double value) {
  
  cleanpxchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChangePercent)
}

// optional double YieldChangeBP = 31;
void CashBondTradingSnapshot::clear_yieldchangebp() {
  yieldchangebp_ = 0;
}
double CashBondTradingSnapshot::yieldchangebp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.YieldChangeBP)
  return yieldchangebp_;
}
void CashBondTradingSnapshot::set_yieldchangebp(double value) {
  
  yieldchangebp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.YieldChangeBP)
}

inline const CashBondTradingSnapshot* CashBondTradingSnapshot::internal_default_instance() {
  return &CashBondTradingSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BondForwardSnapshot::kUnderlyingSymbolFieldNumber;
const int BondForwardSnapshot::kUnderlyingSecurityIDFieldNumber;
const int BondForwardSnapshot::kPreCloseCleanPxFieldNumber;
const int BondForwardSnapshot::kPreWeightedAvgCleanPxFieldNumber;
const int BondForwardSnapshot::kCleanPxChangePercentFieldNumber;
const int BondForwardSnapshot::kOpenCleanPxFieldNumber;
const int BondForwardSnapshot::kLastCleanPxFieldNumber;
const int BondForwardSnapshot::kHighCleanPxFieldNumber;
const int BondForwardSnapshot::kLowCleanPxFieldNumber;
const int BondForwardSnapshot::kCloseCleanPxFieldNumber;
const int BondForwardSnapshot::kWeightedAvgCleanPxFieldNumber;
const int BondForwardSnapshot::kPreCloseYieldFieldNumber;
const int BondForwardSnapshot::kPreWeightedAvgYieldFieldNumber;
const int BondForwardSnapshot::kOpenYieldFieldNumber;
const int BondForwardSnapshot::kLastYieldFieldNumber;
const int BondForwardSnapshot::kHighYieldFieldNumber;
const int BondForwardSnapshot::kLowYieldFieldNumber;
const int BondForwardSnapshot::kCloseYieldFieldNumber;
const int BondForwardSnapshot::kWeightedAvgYieldFieldNumber;
const int BondForwardSnapshot::kTradeVolumeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BondForwardSnapshot::BondForwardSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BondForwardSnapshot)
}

void BondForwardSnapshot::InitAsDefaultInstance() {
}

BondForwardSnapshot::BondForwardSnapshot(const BondForwardSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BondForwardSnapshot)
}

void BondForwardSnapshot::SharedCtor() {
  underlyingsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&preclosecleanpx_, 0, reinterpret_cast<char*>(&tradevolume_) -
    reinterpret_cast<char*>(&preclosecleanpx_) + sizeof(tradevolume_));
  _cached_size_ = 0;
}

BondForwardSnapshot::~BondForwardSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BondForwardSnapshot)
  SharedDtor();
}

void BondForwardSnapshot::SharedDtor() {
  underlyingsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BondForwardSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BondForwardSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BondForwardSnapshot_descriptor_;
}

const BondForwardSnapshot& BondForwardSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BondForwardSnapshot> BondForwardSnapshot_default_instance_;

BondForwardSnapshot* BondForwardSnapshot::New(::google::protobuf::Arena* arena) const {
  BondForwardSnapshot* n = new BondForwardSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BondForwardSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BondForwardSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BondForwardSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(preclosecleanpx_, highcleanpx_);
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lowcleanpx_, highyield_);
  ZR_(lowyield_, tradevolume_);

#undef ZR_HELPER_
#undef ZR_

}

bool BondForwardSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string UnderlyingSymbol = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsymbol().data(), this->underlyingsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_UnderlyingSecurityID;
        break;
      }

      // optional string UnderlyingSecurityID = 2;
      case 2: {
        if (tag == 18) {
         parse_UnderlyingSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_PreCloseCleanPx;
        break;
      }

      // optional double PreCloseCleanPx = 11;
      case 11: {
        if (tag == 89) {
         parse_PreCloseCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preclosecleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_PreWeightedAvgCleanPx;
        break;
      }

      // optional double PreWeightedAvgCleanPx = 12;
      case 12: {
        if (tag == 97) {
         parse_PreWeightedAvgCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_CleanPxChangePercent;
        break;
      }

      // optional double CleanPxChangePercent = 13;
      case 13: {
        if (tag == 105) {
         parse_CleanPxChangePercent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cleanpxchangepercent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_OpenCleanPx;
        break;
      }

      // optional double OpenCleanPx = 14;
      case 14: {
        if (tag == 113) {
         parse_OpenCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &opencleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_LastCleanPx;
        break;
      }

      // optional double LastCleanPx = 15;
      case 15: {
        if (tag == 121) {
         parse_LastCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_HighCleanPx;
        break;
      }

      // optional double HighCleanPx = 16;
      case 16: {
        if (tag == 129) {
         parse_HighCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_LowCleanPx;
        break;
      }

      // optional double LowCleanPx = 17;
      case 17: {
        if (tag == 137) {
         parse_LowCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_CloseCleanPx;
        break;
      }

      // optional double CloseCleanPx = 18;
      case 18: {
        if (tag == 145) {
         parse_CloseCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closecleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_WeightedAvgCleanPx;
        break;
      }

      // optional double WeightedAvgCleanPx = 19;
      case 19: {
        if (tag == 153) {
         parse_WeightedAvgCleanPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgcleanpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(161)) goto parse_PreCloseYield;
        break;
      }

      // optional double PreCloseYield = 20;
      case 20: {
        if (tag == 161) {
         parse_PreCloseYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &precloseyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(169)) goto parse_PreWeightedAvgYield;
        break;
      }

      // optional double PreWeightedAvgYield = 21;
      case 21: {
        if (tag == 169) {
         parse_PreWeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_OpenYield;
        break;
      }

      // optional double OpenYield = 22;
      case 22: {
        if (tag == 177) {
         parse_OpenYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(185)) goto parse_LastYield;
        break;
      }

      // optional double LastYield = 23;
      case 23: {
        if (tag == 185) {
         parse_LastYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(193)) goto parse_HighYield;
        break;
      }

      // optional double HighYield = 24;
      case 24: {
        if (tag == 193) {
         parse_HighYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(201)) goto parse_LowYield;
        break;
      }

      // optional double LowYield = 25;
      case 25: {
        if (tag == 201) {
         parse_LowYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(209)) goto parse_CloseYield;
        break;
      }

      // optional double CloseYield = 26;
      case 26: {
        if (tag == 209) {
         parse_CloseYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closeyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(217)) goto parse_WeightedAvgYield;
        break;
      }

      // optional double WeightedAvgYield = 27;
      case 27: {
        if (tag == 217) {
         parse_WeightedAvgYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(225)) goto parse_TradeVolume;
        break;
      }

      // optional double TradeVolume = 28;
      case 28: {
        if (tag == 225) {
         parse_TradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BondForwardSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BondForwardSnapshot)
  return false;
#undef DO_
}

void BondForwardSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->underlyingsymbol(), output);
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->underlyingsecurityid(), output);
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->preclosecleanpx(), output);
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->preweightedavgcleanpx(), output);
  }

  // optional double CleanPxChangePercent = 13;
  if (this->cleanpxchangepercent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->cleanpxchangepercent(), output);
  }

  // optional double OpenCleanPx = 14;
  if (this->opencleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->opencleanpx(), output);
  }

  // optional double LastCleanPx = 15;
  if (this->lastcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->lastcleanpx(), output);
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->highcleanpx(), output);
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->lowcleanpx(), output);
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->closecleanpx(), output);
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->weightedavgcleanpx(), output);
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(20, this->precloseyield(), output);
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(21, this->preweightedavgyield(), output);
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->openyield(), output);
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(23, this->lastyield(), output);
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(24, this->highyield(), output);
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(25, this->lowyield(), output);
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(26, this->closeyield(), output);
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(27, this->weightedavgyield(), output);
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(28, this->tradevolume(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BondForwardSnapshot)
}

::google::protobuf::uint8* BondForwardSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->underlyingsymbol(), target);
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->underlyingsecurityid(), target);
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->preclosecleanpx(), target);
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->preweightedavgcleanpx(), target);
  }

  // optional double CleanPxChangePercent = 13;
  if (this->cleanpxchangepercent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->cleanpxchangepercent(), target);
  }

  // optional double OpenCleanPx = 14;
  if (this->opencleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->opencleanpx(), target);
  }

  // optional double LastCleanPx = 15;
  if (this->lastcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->lastcleanpx(), target);
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->highcleanpx(), target);
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->lowcleanpx(), target);
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->closecleanpx(), target);
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->weightedavgcleanpx(), target);
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(20, this->precloseyield(), target);
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(21, this->preweightedavgyield(), target);
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->openyield(), target);
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(23, this->lastyield(), target);
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(24, this->highyield(), target);
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(25, this->lowyield(), target);
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(26, this->closeyield(), target);
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(27, this->weightedavgyield(), target);
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(28, this->tradevolume(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BondForwardSnapshot)
  return target;
}

size_t BondForwardSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  size_t total_size = 0;

  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsymbol());
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsecurityid());
  }

  // optional double PreCloseCleanPx = 11;
  if (this->preclosecleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double PreWeightedAvgCleanPx = 12;
  if (this->preweightedavgcleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double CleanPxChangePercent = 13;
  if (this->cleanpxchangepercent() != 0) {
    total_size += 1 + 8;
  }

  // optional double OpenCleanPx = 14;
  if (this->opencleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastCleanPx = 15;
  if (this->lastcleanpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighCleanPx = 16;
  if (this->highcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double LowCleanPx = 17;
  if (this->lowcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseCleanPx = 18;
  if (this->closecleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgCleanPx = 19;
  if (this->weightedavgcleanpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreCloseYield = 20;
  if (this->precloseyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreWeightedAvgYield = 21;
  if (this->preweightedavgyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double OpenYield = 22;
  if (this->openyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double LastYield = 23;
  if (this->lastyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double HighYield = 24;
  if (this->highyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double LowYield = 25;
  if (this->lowyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseYield = 26;
  if (this->closeyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgYield = 27;
  if (this->weightedavgyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double TradeVolume = 28;
  if (this->tradevolume() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BondForwardSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BondForwardSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BondForwardSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BondForwardSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BondForwardSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void BondForwardSnapshot::MergeFrom(const BondForwardSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BondForwardSnapshot::UnsafeMergeFrom(const BondForwardSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.underlyingsymbol().size() > 0) {

    underlyingsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsymbol_);
  }
  if (from.underlyingsecurityid().size() > 0) {

    underlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsecurityid_);
  }
  if (from.preclosecleanpx() != 0) {
    set_preclosecleanpx(from.preclosecleanpx());
  }
  if (from.preweightedavgcleanpx() != 0) {
    set_preweightedavgcleanpx(from.preweightedavgcleanpx());
  }
  if (from.cleanpxchangepercent() != 0) {
    set_cleanpxchangepercent(from.cleanpxchangepercent());
  }
  if (from.opencleanpx() != 0) {
    set_opencleanpx(from.opencleanpx());
  }
  if (from.lastcleanpx() != 0) {
    set_lastcleanpx(from.lastcleanpx());
  }
  if (from.highcleanpx() != 0) {
    set_highcleanpx(from.highcleanpx());
  }
  if (from.lowcleanpx() != 0) {
    set_lowcleanpx(from.lowcleanpx());
  }
  if (from.closecleanpx() != 0) {
    set_closecleanpx(from.closecleanpx());
  }
  if (from.weightedavgcleanpx() != 0) {
    set_weightedavgcleanpx(from.weightedavgcleanpx());
  }
  if (from.precloseyield() != 0) {
    set_precloseyield(from.precloseyield());
  }
  if (from.preweightedavgyield() != 0) {
    set_preweightedavgyield(from.preweightedavgyield());
  }
  if (from.openyield() != 0) {
    set_openyield(from.openyield());
  }
  if (from.lastyield() != 0) {
    set_lastyield(from.lastyield());
  }
  if (from.highyield() != 0) {
    set_highyield(from.highyield());
  }
  if (from.lowyield() != 0) {
    set_lowyield(from.lowyield());
  }
  if (from.closeyield() != 0) {
    set_closeyield(from.closeyield());
  }
  if (from.weightedavgyield() != 0) {
    set_weightedavgyield(from.weightedavgyield());
  }
  if (from.tradevolume() != 0) {
    set_tradevolume(from.tradevolume());
  }
}

void BondForwardSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BondForwardSnapshot::CopyFrom(const BondForwardSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BondForwardSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BondForwardSnapshot::IsInitialized() const {

  return true;
}

void BondForwardSnapshot::Swap(BondForwardSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BondForwardSnapshot::InternalSwap(BondForwardSnapshot* other) {
  underlyingsymbol_.Swap(&other->underlyingsymbol_);
  underlyingsecurityid_.Swap(&other->underlyingsecurityid_);
  std::swap(preclosecleanpx_, other->preclosecleanpx_);
  std::swap(preweightedavgcleanpx_, other->preweightedavgcleanpx_);
  std::swap(cleanpxchangepercent_, other->cleanpxchangepercent_);
  std::swap(opencleanpx_, other->opencleanpx_);
  std::swap(lastcleanpx_, other->lastcleanpx_);
  std::swap(highcleanpx_, other->highcleanpx_);
  std::swap(lowcleanpx_, other->lowcleanpx_);
  std::swap(closecleanpx_, other->closecleanpx_);
  std::swap(weightedavgcleanpx_, other->weightedavgcleanpx_);
  std::swap(precloseyield_, other->precloseyield_);
  std::swap(preweightedavgyield_, other->preweightedavgyield_);
  std::swap(openyield_, other->openyield_);
  std::swap(lastyield_, other->lastyield_);
  std::swap(highyield_, other->highyield_);
  std::swap(lowyield_, other->lowyield_);
  std::swap(closeyield_, other->closeyield_);
  std::swap(weightedavgyield_, other->weightedavgyield_);
  std::swap(tradevolume_, other->tradevolume_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BondForwardSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BondForwardSnapshot_descriptor_;
  metadata.reflection = BondForwardSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BondForwardSnapshot

// optional string UnderlyingSymbol = 1;
void BondForwardSnapshot::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondForwardSnapshot::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondForwardSnapshot::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
void BondForwardSnapshot::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
void BondForwardSnapshot::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
::std::string* BondForwardSnapshot::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondForwardSnapshot::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondForwardSnapshot::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 2;
void BondForwardSnapshot::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondForwardSnapshot::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondForwardSnapshot::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
void BondForwardSnapshot::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
void BondForwardSnapshot::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
::std::string* BondForwardSnapshot::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondForwardSnapshot::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondForwardSnapshot::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}

// optional double PreCloseCleanPx = 11;
void BondForwardSnapshot::clear_preclosecleanpx() {
  preclosecleanpx_ = 0;
}
double BondForwardSnapshot::preclosecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseCleanPx)
  return preclosecleanpx_;
}
void BondForwardSnapshot::set_preclosecleanpx(double value) {
  
  preclosecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseCleanPx)
}

// optional double PreWeightedAvgCleanPx = 12;
void BondForwardSnapshot::clear_preweightedavgcleanpx() {
  preweightedavgcleanpx_ = 0;
}
double BondForwardSnapshot::preweightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgCleanPx)
  return preweightedavgcleanpx_;
}
void BondForwardSnapshot::set_preweightedavgcleanpx(double value) {
  
  preweightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgCleanPx)
}

// optional double CleanPxChangePercent = 13;
void BondForwardSnapshot::clear_cleanpxchangepercent() {
  cleanpxchangepercent_ = 0;
}
double BondForwardSnapshot::cleanpxchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CleanPxChangePercent)
  return cleanpxchangepercent_;
}
void BondForwardSnapshot::set_cleanpxchangepercent(double value) {
  
  cleanpxchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CleanPxChangePercent)
}

// optional double OpenCleanPx = 14;
void BondForwardSnapshot::clear_opencleanpx() {
  opencleanpx_ = 0;
}
double BondForwardSnapshot::opencleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenCleanPx)
  return opencleanpx_;
}
void BondForwardSnapshot::set_opencleanpx(double value) {
  
  opencleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenCleanPx)
}

// optional double LastCleanPx = 15;
void BondForwardSnapshot::clear_lastcleanpx() {
  lastcleanpx_ = 0;
}
double BondForwardSnapshot::lastcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LastCleanPx)
  return lastcleanpx_;
}
void BondForwardSnapshot::set_lastcleanpx(double value) {
  
  lastcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LastCleanPx)
}

// optional double HighCleanPx = 16;
void BondForwardSnapshot::clear_highcleanpx() {
  highcleanpx_ = 0;
}
double BondForwardSnapshot::highcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.HighCleanPx)
  return highcleanpx_;
}
void BondForwardSnapshot::set_highcleanpx(double value) {
  
  highcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.HighCleanPx)
}

// optional double LowCleanPx = 17;
void BondForwardSnapshot::clear_lowcleanpx() {
  lowcleanpx_ = 0;
}
double BondForwardSnapshot::lowcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LowCleanPx)
  return lowcleanpx_;
}
void BondForwardSnapshot::set_lowcleanpx(double value) {
  
  lowcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LowCleanPx)
}

// optional double CloseCleanPx = 18;
void BondForwardSnapshot::clear_closecleanpx() {
  closecleanpx_ = 0;
}
double BondForwardSnapshot::closecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseCleanPx)
  return closecleanpx_;
}
void BondForwardSnapshot::set_closecleanpx(double value) {
  
  closecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseCleanPx)
}

// optional double WeightedAvgCleanPx = 19;
void BondForwardSnapshot::clear_weightedavgcleanpx() {
  weightedavgcleanpx_ = 0;
}
double BondForwardSnapshot::weightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgCleanPx)
  return weightedavgcleanpx_;
}
void BondForwardSnapshot::set_weightedavgcleanpx(double value) {
  
  weightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgCleanPx)
}

// optional double PreCloseYield = 20;
void BondForwardSnapshot::clear_precloseyield() {
  precloseyield_ = 0;
}
double BondForwardSnapshot::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseYield)
  return precloseyield_;
}
void BondForwardSnapshot::set_precloseyield(double value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseYield)
}

// optional double PreWeightedAvgYield = 21;
void BondForwardSnapshot::clear_preweightedavgyield() {
  preweightedavgyield_ = 0;
}
double BondForwardSnapshot::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgYield)
  return preweightedavgyield_;
}
void BondForwardSnapshot::set_preweightedavgyield(double value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgYield)
}

// optional double OpenYield = 22;
void BondForwardSnapshot::clear_openyield() {
  openyield_ = 0;
}
double BondForwardSnapshot::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenYield)
  return openyield_;
}
void BondForwardSnapshot::set_openyield(double value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenYield)
}

// optional double LastYield = 23;
void BondForwardSnapshot::clear_lastyield() {
  lastyield_ = 0;
}
double BondForwardSnapshot::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LastYield)
  return lastyield_;
}
void BondForwardSnapshot::set_lastyield(double value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LastYield)
}

// optional double HighYield = 24;
void BondForwardSnapshot::clear_highyield() {
  highyield_ = 0;
}
double BondForwardSnapshot::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.HighYield)
  return highyield_;
}
void BondForwardSnapshot::set_highyield(double value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.HighYield)
}

// optional double LowYield = 25;
void BondForwardSnapshot::clear_lowyield() {
  lowyield_ = 0;
}
double BondForwardSnapshot::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LowYield)
  return lowyield_;
}
void BondForwardSnapshot::set_lowyield(double value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LowYield)
}

// optional double CloseYield = 26;
void BondForwardSnapshot::clear_closeyield() {
  closeyield_ = 0;
}
double BondForwardSnapshot::closeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseYield)
  return closeyield_;
}
void BondForwardSnapshot::set_closeyield(double value) {
  
  closeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseYield)
}

// optional double WeightedAvgYield = 27;
void BondForwardSnapshot::clear_weightedavgyield() {
  weightedavgyield_ = 0;
}
double BondForwardSnapshot::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgYield)
  return weightedavgyield_;
}
void BondForwardSnapshot::set_weightedavgyield(double value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgYield)
}

// optional double TradeVolume = 28;
void BondForwardSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
double BondForwardSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.TradeVolume)
  return tradevolume_;
}
void BondForwardSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.TradeVolume)
}

inline const BondForwardSnapshot* BondForwardSnapshot::internal_default_instance() {
  return &BondForwardSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BondLendingSnapshot::kUnderlyingSymbolFieldNumber;
const int BondLendingSnapshot::kUnderlyingSecurityIDFieldNumber;
const int BondLendingSnapshot::kPreCloseRateFieldNumber;
const int BondLendingSnapshot::kPreWeightedAvgRateFieldNumber;
const int BondLendingSnapshot::kOpenRateFieldNumber;
const int BondLendingSnapshot::kLastRateFieldNumber;
const int BondLendingSnapshot::kHighRateFieldNumber;
const int BondLendingSnapshot::kLowRateFieldNumber;
const int BondLendingSnapshot::kCloseRateFieldNumber;
const int BondLendingSnapshot::kWeightedAvgRateFieldNumber;
const int BondLendingSnapshot::kTradeVolumeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BondLendingSnapshot::BondLendingSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BondLendingSnapshot)
}

void BondLendingSnapshot::InitAsDefaultInstance() {
}

BondLendingSnapshot::BondLendingSnapshot(const BondLendingSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BondLendingSnapshot)
}

void BondLendingSnapshot::SharedCtor() {
  underlyingsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&precloserate_, 0, reinterpret_cast<char*>(&tradevolume_) -
    reinterpret_cast<char*>(&precloserate_) + sizeof(tradevolume_));
  _cached_size_ = 0;
}

BondLendingSnapshot::~BondLendingSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BondLendingSnapshot)
  SharedDtor();
}

void BondLendingSnapshot::SharedDtor() {
  underlyingsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void BondLendingSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BondLendingSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BondLendingSnapshot_descriptor_;
}

const BondLendingSnapshot& BondLendingSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BondLendingSnapshot> BondLendingSnapshot_default_instance_;

BondLendingSnapshot* BondLendingSnapshot::New(::google::protobuf::Arena* arena) const {
  BondLendingSnapshot* n = new BondLendingSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BondLendingSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BondLendingSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BondLendingSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(precloserate_, lowrate_);
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(closerate_, tradevolume_);

#undef ZR_HELPER_
#undef ZR_

}

bool BondLendingSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string UnderlyingSymbol = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsymbol().data(), this->underlyingsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_UnderlyingSecurityID;
        break;
      }

      // optional string UnderlyingSecurityID = 2;
      case 2: {
        if (tag == 18) {
         parse_UnderlyingSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_PreCloseRate;
        break;
      }

      // optional double PreCloseRate = 11;
      case 11: {
        if (tag == 89) {
         parse_PreCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &precloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_PreWeightedAvgRate;
        break;
      }

      // optional double PreWeightedAvgRate = 12;
      case 12: {
        if (tag == 97) {
         parse_PreWeightedAvgRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_OpenRate;
        break;
      }

      // optional double OpenRate = 13;
      case 13: {
        if (tag == 105) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_LastRate;
        break;
      }

      // optional double LastRate = 14;
      case 14: {
        if (tag == 113) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_HighRate;
        break;
      }

      // optional double HighRate = 15;
      case 15: {
        if (tag == 121) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_LowRate;
        break;
      }

      // optional double LowRate = 16;
      case 16: {
        if (tag == 129) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_CloseRate;
        break;
      }

      // optional double CloseRate = 17;
      case 17: {
        if (tag == 137) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_WeightedAvgRate;
        break;
      }

      // optional double WeightedAvgRate = 18;
      case 18: {
        if (tag == 145) {
         parse_WeightedAvgRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_TradeVolume;
        break;
      }

      // optional double TradeVolume = 19;
      case 19: {
        if (tag == 153) {
         parse_TradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &tradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BondLendingSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BondLendingSnapshot)
  return false;
#undef DO_
}

void BondLendingSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->underlyingsymbol(), output);
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->underlyingsecurityid(), output);
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->precloserate(), output);
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->preweightedavgrate(), output);
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->openrate(), output);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->lastrate(), output);
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->highrate(), output);
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->lowrate(), output);
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->closerate(), output);
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->weightedavgrate(), output);
  }

  // optional double TradeVolume = 19;
  if (this->tradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->tradevolume(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BondLendingSnapshot)
}

::google::protobuf::uint8* BondLendingSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsymbol().data(), this->underlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->underlyingsymbol(), target);
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->underlyingsecurityid(), target);
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->precloserate(), target);
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->preweightedavgrate(), target);
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->openrate(), target);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->lastrate(), target);
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->highrate(), target);
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->lowrate(), target);
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->closerate(), target);
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->weightedavgrate(), target);
  }

  // optional double TradeVolume = 19;
  if (this->tradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->tradevolume(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BondLendingSnapshot)
  return target;
}

size_t BondLendingSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  size_t total_size = 0;

  // optional string UnderlyingSymbol = 1;
  if (this->underlyingsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsymbol());
  }

  // optional string UnderlyingSecurityID = 2;
  if (this->underlyingsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsecurityid());
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    total_size += 1 + 8;
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double TradeVolume = 19;
  if (this->tradevolume() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BondLendingSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BondLendingSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BondLendingSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BondLendingSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BondLendingSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void BondLendingSnapshot::MergeFrom(const BondLendingSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BondLendingSnapshot::UnsafeMergeFrom(const BondLendingSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.underlyingsymbol().size() > 0) {

    underlyingsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsymbol_);
  }
  if (from.underlyingsecurityid().size() > 0) {

    underlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsecurityid_);
  }
  if (from.precloserate() != 0) {
    set_precloserate(from.precloserate());
  }
  if (from.preweightedavgrate() != 0) {
    set_preweightedavgrate(from.preweightedavgrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.weightedavgrate() != 0) {
    set_weightedavgrate(from.weightedavgrate());
  }
  if (from.tradevolume() != 0) {
    set_tradevolume(from.tradevolume());
  }
}

void BondLendingSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BondLendingSnapshot::CopyFrom(const BondLendingSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BondLendingSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BondLendingSnapshot::IsInitialized() const {

  return true;
}

void BondLendingSnapshot::Swap(BondLendingSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BondLendingSnapshot::InternalSwap(BondLendingSnapshot* other) {
  underlyingsymbol_.Swap(&other->underlyingsymbol_);
  underlyingsecurityid_.Swap(&other->underlyingsecurityid_);
  std::swap(precloserate_, other->precloserate_);
  std::swap(preweightedavgrate_, other->preweightedavgrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(closerate_, other->closerate_);
  std::swap(weightedavgrate_, other->weightedavgrate_);
  std::swap(tradevolume_, other->tradevolume_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BondLendingSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BondLendingSnapshot_descriptor_;
  metadata.reflection = BondLendingSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BondLendingSnapshot

// optional string UnderlyingSymbol = 1;
void BondLendingSnapshot::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingSnapshot::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingSnapshot::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
void BondLendingSnapshot::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
void BondLendingSnapshot::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
::std::string* BondLendingSnapshot::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingSnapshot::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingSnapshot::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 2;
void BondLendingSnapshot::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& BondLendingSnapshot::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingSnapshot::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
void BondLendingSnapshot::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
void BondLendingSnapshot::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
::std::string* BondLendingSnapshot::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* BondLendingSnapshot::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void BondLendingSnapshot::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}

// optional double PreCloseRate = 11;
void BondLendingSnapshot::clear_precloserate() {
  precloserate_ = 0;
}
double BondLendingSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.PreCloseRate)
  return precloserate_;
}
void BondLendingSnapshot::set_precloserate(double value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.PreCloseRate)
}

// optional double PreWeightedAvgRate = 12;
void BondLendingSnapshot::clear_preweightedavgrate() {
  preweightedavgrate_ = 0;
}
double BondLendingSnapshot::preweightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.PreWeightedAvgRate)
  return preweightedavgrate_;
}
void BondLendingSnapshot::set_preweightedavgrate(double value) {
  
  preweightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.PreWeightedAvgRate)
}

// optional double OpenRate = 13;
void BondLendingSnapshot::clear_openrate() {
  openrate_ = 0;
}
double BondLendingSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.OpenRate)
  return openrate_;
}
void BondLendingSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.OpenRate)
}

// optional double LastRate = 14;
void BondLendingSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
double BondLendingSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.LastRate)
  return lastrate_;
}
void BondLendingSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.LastRate)
}

// optional double HighRate = 15;
void BondLendingSnapshot::clear_highrate() {
  highrate_ = 0;
}
double BondLendingSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.HighRate)
  return highrate_;
}
void BondLendingSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.HighRate)
}

// optional double LowRate = 16;
void BondLendingSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
double BondLendingSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.LowRate)
  return lowrate_;
}
void BondLendingSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.LowRate)
}

// optional double CloseRate = 17;
void BondLendingSnapshot::clear_closerate() {
  closerate_ = 0;
}
double BondLendingSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.CloseRate)
  return closerate_;
}
void BondLendingSnapshot::set_closerate(double value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.CloseRate)
}

// optional double WeightedAvgRate = 18;
void BondLendingSnapshot::clear_weightedavgrate() {
  weightedavgrate_ = 0;
}
double BondLendingSnapshot::weightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.WeightedAvgRate)
  return weightedavgrate_;
}
void BondLendingSnapshot::set_weightedavgrate(double value) {
  
  weightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.WeightedAvgRate)
}

// optional double TradeVolume = 19;
void BondLendingSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
double BondLendingSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.TradeVolume)
  return tradevolume_;
}
void BondLendingSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.TradeVolume)
}

inline const BondLendingSnapshot* BondLendingSnapshot::internal_default_instance() {
  return &BondLendingSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StandardisedBondForwardSnapshot::kTradeMethodFieldNumber;
const int StandardisedBondForwardSnapshot::kOpenPxFieldNumber;
const int StandardisedBondForwardSnapshot::kHighPxFieldNumber;
const int StandardisedBondForwardSnapshot::kLowPxFieldNumber;
const int StandardisedBondForwardSnapshot::kLastPxFieldNumber;
const int StandardisedBondForwardSnapshot::kTotalVolumeTradeFieldNumber;
const int StandardisedBondForwardSnapshot::kLastVolumeTradeFieldNumber;
const int StandardisedBondForwardSnapshot::kSettlePxFieldNumber;
const int StandardisedBondForwardSnapshot::kSettlePxDateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StandardisedBondForwardSnapshot::StandardisedBondForwardSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
}

void StandardisedBondForwardSnapshot::InitAsDefaultInstance() {
}

StandardisedBondForwardSnapshot::StandardisedBondForwardSnapshot(const StandardisedBondForwardSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
}

void StandardisedBondForwardSnapshot::SharedCtor() {
  settlepxdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&openpx_, 0, reinterpret_cast<char*>(&trademethod_) -
    reinterpret_cast<char*>(&openpx_) + sizeof(trademethod_));
  _cached_size_ = 0;
}

StandardisedBondForwardSnapshot::~StandardisedBondForwardSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  SharedDtor();
}

void StandardisedBondForwardSnapshot::SharedDtor() {
  settlepxdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void StandardisedBondForwardSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StandardisedBondForwardSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StandardisedBondForwardSnapshot_descriptor_;
}

const StandardisedBondForwardSnapshot& StandardisedBondForwardSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StandardisedBondForwardSnapshot> StandardisedBondForwardSnapshot_default_instance_;

StandardisedBondForwardSnapshot* StandardisedBondForwardSnapshot::New(::google::protobuf::Arena* arena) const {
  StandardisedBondForwardSnapshot* n = new StandardisedBondForwardSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StandardisedBondForwardSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(StandardisedBondForwardSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<StandardisedBondForwardSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(openpx_, trademethod_);
  settlepxdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool StandardisedBondForwardSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeMethod = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_OpenPx;
        break;
      }

      // optional double OpenPx = 11;
      case 11: {
        if (tag == 89) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_HighPx;
        break;
      }

      // optional double HighPx = 12;
      case 12: {
        if (tag == 97) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_LowPx;
        break;
      }

      // optional double LowPx = 13;
      case 13: {
        if (tag == 105) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_LastPx;
        break;
      }

      // optional double LastPx = 14;
      case 14: {
        if (tag == 113) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional double TotalVolumeTrade = 15;
      case 15: {
        if (tag == 121) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_LastVolumeTrade;
        break;
      }

      // optional double LastVolumeTrade = 16;
      case 16: {
        if (tag == 129) {
         parse_LastVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(401)) goto parse_SettlePx;
        break;
      }

      // optional double SettlePx = 50;
      case 50: {
        if (tag == 401) {
         parse_SettlePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &settlepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_SettlePxDate;
        break;
      }

      // optional string SettlePxDate = 51;
      case 51: {
        if (tag == 410) {
         parse_SettlePxDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settlepxdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settlepxdate().data(), this->settlepxdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  return false;
#undef DO_
}

void StandardisedBondForwardSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->trademethod(), output);
  }

  // optional double OpenPx = 11;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->openpx(), output);
  }

  // optional double HighPx = 12;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->highpx(), output);
  }

  // optional double LowPx = 13;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->lowpx(), output);
  }

  // optional double LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->lastpx(), output);
  }

  // optional double TotalVolumeTrade = 15;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->totalvolumetrade(), output);
  }

  // optional double LastVolumeTrade = 16;
  if (this->lastvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->lastvolumetrade(), output);
  }

  // optional double SettlePx = 50;
  if (this->settlepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(50, this->settlepx(), output);
  }

  // optional string SettlePxDate = 51;
  if (this->settlepxdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settlepxdate().data(), this->settlepxdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      51, this->settlepxdate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
}

::google::protobuf::uint8* StandardisedBondForwardSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->trademethod(), target);
  }

  // optional double OpenPx = 11;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->openpx(), target);
  }

  // optional double HighPx = 12;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->highpx(), target);
  }

  // optional double LowPx = 13;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->lowpx(), target);
  }

  // optional double LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->lastpx(), target);
  }

  // optional double TotalVolumeTrade = 15;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->totalvolumetrade(), target);
  }

  // optional double LastVolumeTrade = 16;
  if (this->lastvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->lastvolumetrade(), target);
  }

  // optional double SettlePx = 50;
  if (this->settlepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(50, this->settlepx(), target);
  }

  // optional string SettlePxDate = 51;
  if (this->settlepxdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settlepxdate().data(), this->settlepxdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        51, this->settlepxdate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  return target;
}

size_t StandardisedBondForwardSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  size_t total_size = 0;

  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional double OpenPx = 11;
  if (this->openpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighPx = 12;
  if (this->highpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double LowPx = 13;
  if (this->lowpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalVolumeTrade = 15;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastVolumeTrade = 16;
  if (this->lastvolumetrade() != 0) {
    total_size += 2 + 8;
  }

  // optional double SettlePx = 50;
  if (this->settlepx() != 0) {
    total_size += 2 + 8;
  }

  // optional string SettlePxDate = 51;
  if (this->settlepxdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settlepxdate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StandardisedBondForwardSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StandardisedBondForwardSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StandardisedBondForwardSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void StandardisedBondForwardSnapshot::MergeFrom(const StandardisedBondForwardSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StandardisedBondForwardSnapshot::UnsafeMergeFrom(const StandardisedBondForwardSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.lastvolumetrade() != 0) {
    set_lastvolumetrade(from.lastvolumetrade());
  }
  if (from.settlepx() != 0) {
    set_settlepx(from.settlepx());
  }
  if (from.settlepxdate().size() > 0) {

    settlepxdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settlepxdate_);
  }
}

void StandardisedBondForwardSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StandardisedBondForwardSnapshot::CopyFrom(const StandardisedBondForwardSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StandardisedBondForwardSnapshot::IsInitialized() const {

  return true;
}

void StandardisedBondForwardSnapshot::Swap(StandardisedBondForwardSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StandardisedBondForwardSnapshot::InternalSwap(StandardisedBondForwardSnapshot* other) {
  std::swap(trademethod_, other->trademethod_);
  std::swap(openpx_, other->openpx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(lastvolumetrade_, other->lastvolumetrade_);
  std::swap(settlepx_, other->settlepx_);
  settlepxdate_.Swap(&other->settlepxdate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StandardisedBondForwardSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StandardisedBondForwardSnapshot_descriptor_;
  metadata.reflection = StandardisedBondForwardSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StandardisedBondForwardSnapshot

// optional int32 TradeMethod = 1;
void StandardisedBondForwardSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 StandardisedBondForwardSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TradeMethod)
  return trademethod_;
}
void StandardisedBondForwardSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TradeMethod)
}

// optional double OpenPx = 11;
void StandardisedBondForwardSnapshot::clear_openpx() {
  openpx_ = 0;
}
double StandardisedBondForwardSnapshot::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.OpenPx)
  return openpx_;
}
void StandardisedBondForwardSnapshot::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.OpenPx)
}

// optional double HighPx = 12;
void StandardisedBondForwardSnapshot::clear_highpx() {
  highpx_ = 0;
}
double StandardisedBondForwardSnapshot::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.HighPx)
  return highpx_;
}
void StandardisedBondForwardSnapshot::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.HighPx)
}

// optional double LowPx = 13;
void StandardisedBondForwardSnapshot::clear_lowpx() {
  lowpx_ = 0;
}
double StandardisedBondForwardSnapshot::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LowPx)
  return lowpx_;
}
void StandardisedBondForwardSnapshot::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LowPx)
}

// optional double LastPx = 14;
void StandardisedBondForwardSnapshot::clear_lastpx() {
  lastpx_ = 0;
}
double StandardisedBondForwardSnapshot::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastPx)
  return lastpx_;
}
void StandardisedBondForwardSnapshot::set_lastpx(double value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastPx)
}

// optional double TotalVolumeTrade = 15;
void StandardisedBondForwardSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
double StandardisedBondForwardSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
void StandardisedBondForwardSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TotalVolumeTrade)
}

// optional double LastVolumeTrade = 16;
void StandardisedBondForwardSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
double StandardisedBondForwardSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
void StandardisedBondForwardSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastVolumeTrade)
}

// optional double SettlePx = 50;
void StandardisedBondForwardSnapshot::clear_settlepx() {
  settlepx_ = 0;
}
double StandardisedBondForwardSnapshot::settlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePx)
  return settlepx_;
}
void StandardisedBondForwardSnapshot::set_settlepx(double value) {
  
  settlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePx)
}

// optional string SettlePxDate = 51;
void StandardisedBondForwardSnapshot::clear_settlepxdate() {
  settlepxdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& StandardisedBondForwardSnapshot::settlepxdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  return settlepxdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void StandardisedBondForwardSnapshot::set_settlepxdate(const ::std::string& value) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
void StandardisedBondForwardSnapshot::set_settlepxdate(const char* value) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
void StandardisedBondForwardSnapshot::set_settlepxdate(const char* value, size_t size) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
::std::string* StandardisedBondForwardSnapshot::mutable_settlepxdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  return settlepxdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* StandardisedBondForwardSnapshot::release_settlepxdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  
  return settlepxdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void StandardisedBondForwardSnapshot::set_allocated_settlepxdate(::std::string* settlepxdate) {
  if (settlepxdate != NULL) {
    
  } else {
    
  }
  settlepxdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settlepxdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}

inline const StandardisedBondForwardSnapshot* StandardisedBondForwardSnapshot::internal_default_instance() {
  return &StandardisedBondForwardSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
