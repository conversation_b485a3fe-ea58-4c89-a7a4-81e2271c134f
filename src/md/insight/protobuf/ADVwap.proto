syntax = "proto3";

package com.htsc.mdc.insight.model;

import "EMDPeriodType.proto";
import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADVwapProtos";
option optimize_for = SPEED;

// ADVwap message represents Volume-Weighted Average Price (VWAP) data for securities
message ADVwap {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Period type for VWAP calculation (1min, 5min, 1day, etc.)
    com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
    
    // Total volume for the VWAP calculation period
    int64 Volume = 8;
    
    // Volume-Weighted Average Price (scaled by DataMultiplePowerOf10)
    int64 Vwap = 9;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 10;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 11;
    
    // Data scaling factor (power of 10 multiplier for price fields)
    int32 DataMultiplePowerOf10 = 12;
}
