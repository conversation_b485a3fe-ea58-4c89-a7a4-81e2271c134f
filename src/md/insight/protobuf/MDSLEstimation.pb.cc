// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLEstimation.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSLEstimation.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSLEstimation_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSLEstimation_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADSLEstimationEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADSLEstimationEntry_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSLEstimation_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSLEstimation_2eproto() {
  protobuf_AddDesc_MDSLEstimation_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSLEstimation.proto");
  GOOGLE_CHECK(file != NULL);
  MDSLEstimation_descriptor_ = file->message_type(0);
  static const int MDSLEstimation_offsets_[25] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, longtermlends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, htscvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, prehtscvolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, weightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, preweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, bestborrowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, bestlendrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, validborrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, validalends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, validblends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, borrows_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, alends_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, blends_),
  };
  MDSLEstimation_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSLEstimation_descriptor_,
      MDSLEstimation::internal_default_instance(),
      MDSLEstimation_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSLEstimation),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLEstimation, _internal_metadata_));
  ADSLEstimationEntry_descriptor_ = file->message_type(1);
  static const int ADSLEstimationEntry_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, level_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, rate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, term_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, amount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, postponeprobability_),
  };
  ADSLEstimationEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADSLEstimationEntry_descriptor_,
      ADSLEstimationEntry::internal_default_instance(),
      ADSLEstimationEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADSLEstimationEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADSLEstimationEntry, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSLEstimation_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSLEstimation_descriptor_, MDSLEstimation::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADSLEstimationEntry_descriptor_, ADSLEstimationEntry::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSLEstimation_2eproto() {
  MDSLEstimation_default_instance_.Shutdown();
  delete MDSLEstimation_reflection_;
  ADSLEstimationEntry_default_instance_.Shutdown();
  delete ADSLEstimationEntry_reflection_;
}

void protobuf_InitDefaults_MDSLEstimation_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSecurityLending_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSLEstimation_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADSLEstimationEntry_default_instance_.DefaultConstruct();
  MDSLEstimation_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADSLEstimationEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSLEstimation_2eproto_once_);
void protobuf_InitDefaults_MDSLEstimation_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSLEstimation_2eproto_once_,
                 &protobuf_InitDefaults_MDSLEstimation_2eproto_impl);
}
void protobuf_AddDesc_MDSLEstimation_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSLEstimation_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDSLEstimation.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\027ESecurityIDSource.proto\032\023ESe"
    "curityType.proto\032\027MDSecurityLending.prot"
    "o\"\341\007\n\016MDSLEstimation\022\026\n\016HTSCSecurityID\030\001"
    " \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\r"
    "DataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode\030"
    "\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.ht"
    "sc.mdc.model.ESecurityIDSource\0227\n\014securi"
    "tyType\030\007 \001(\0162!.com.htsc.mdc.model.ESecur"
    "ityType\022\035\n\025DataMultiplePowerOf10\030\010 \001(\005\022F"
    "\n\rLongTermLends\030\t \003(\0132/.com.htsc.mdc.ins"
    "ight.model.ADSLEstimationEntry\022\016\n\006LastPx"
    "\030\n \001(\003\022\022\n\nPreClosePx\030\013 \001(\003\022\020\n\010HighRate\030\014"
    " \001(\003\022\017\n\007LowRate\030\r \001(\003\022\022\n\nHtscVolume\030\016 \001("
    "\003\022\025\n\rPreHtscVolume\030\017 \001(\003\022\024\n\014WeightedRate"
    "\030\020 \001(\003\022\027\n\017PreWeightedRate\030\021 \001(\003\022\026\n\016BestB"
    "orrowRate\030\022 \001(\003\022\024\n\014BestLendRate\030\023 \001(\003\022M\n"
    "\014ValidBorrows\030\024 \003(\01327.com.htsc.mdc.insig"
    "ht.model.ADValidSecurityLendingEntry\022L\n\013"
    "ValidALends\030\025 \003(\01327.com.htsc.mdc.insight"
    ".model.ADValidSecurityLendingEntry\022L\n\013Va"
    "lidBLends\030\026 \003(\01327.com.htsc.mdc.insight.m"
    "odel.ADValidSecurityLendingEntry\022C\n\007Borr"
    "ows\030\027 \003(\01322.com.htsc.mdc.insight.model.A"
    "DSecurityLendingEntry\022B\n\006ALends\030\030 \003(\01322."
    "com.htsc.mdc.insight.model.ADSecurityLen"
    "dingEntry\022B\n\006BLends\030\031 \003(\01322.com.htsc.mdc"
    ".insight.model.ADSecurityLendingEntry\"m\n"
    "\023ADSLEstimationEntry\022\r\n\005Level\030\001 \001(\005\022\014\n\004R"
    "ate\030\002 \001(\003\022\014\n\004Term\030\003 \001(\t\022\016\n\006Amount\030\004 \001(\003\022"
    "\033\n\023PostponeProbability\030\005 \001(\005B7\n\032com.htsc"
    ".mdc.insight.modelB\024MDSLEstimationProtos"
    "H\001\240\001\001b\006proto3", 1293);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSLEstimation.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSecurityLending_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSLEstimation_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSLEstimation_2eproto_once_);
void protobuf_AddDesc_MDSLEstimation_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSLEstimation_2eproto_once_,
                 &protobuf_AddDesc_MDSLEstimation_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSLEstimation_2eproto {
  StaticDescriptorInitializer_MDSLEstimation_2eproto() {
    protobuf_AddDesc_MDSLEstimation_2eproto();
  }
} static_descriptor_initializer_MDSLEstimation_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSLEstimation::kHTSCSecurityIDFieldNumber;
const int MDSLEstimation::kMDDateFieldNumber;
const int MDSLEstimation::kMDTimeFieldNumber;
const int MDSLEstimation::kDataTimestampFieldNumber;
const int MDSLEstimation::kTradingPhaseCodeFieldNumber;
const int MDSLEstimation::kSecurityIDSourceFieldNumber;
const int MDSLEstimation::kSecurityTypeFieldNumber;
const int MDSLEstimation::kDataMultiplePowerOf10FieldNumber;
const int MDSLEstimation::kLongTermLendsFieldNumber;
const int MDSLEstimation::kLastPxFieldNumber;
const int MDSLEstimation::kPreClosePxFieldNumber;
const int MDSLEstimation::kHighRateFieldNumber;
const int MDSLEstimation::kLowRateFieldNumber;
const int MDSLEstimation::kHtscVolumeFieldNumber;
const int MDSLEstimation::kPreHtscVolumeFieldNumber;
const int MDSLEstimation::kWeightedRateFieldNumber;
const int MDSLEstimation::kPreWeightedRateFieldNumber;
const int MDSLEstimation::kBestBorrowRateFieldNumber;
const int MDSLEstimation::kBestLendRateFieldNumber;
const int MDSLEstimation::kValidBorrowsFieldNumber;
const int MDSLEstimation::kValidALendsFieldNumber;
const int MDSLEstimation::kValidBLendsFieldNumber;
const int MDSLEstimation::kBorrowsFieldNumber;
const int MDSLEstimation::kALendsFieldNumber;
const int MDSLEstimation::kBLendsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSLEstimation::MDSLEstimation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLEstimation_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSLEstimation)
}

void MDSLEstimation::InitAsDefaultInstance() {
}

MDSLEstimation::MDSLEstimation(const MDSLEstimation& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSLEstimation)
}

void MDSLEstimation::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSLEstimation::~MDSLEstimation() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSLEstimation)
  SharedDtor();
}

void MDSLEstimation::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSLEstimation::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSLEstimation::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSLEstimation_descriptor_;
}

const MDSLEstimation& MDSLEstimation::default_instance() {
  protobuf_InitDefaults_MDSLEstimation_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSLEstimation> MDSLEstimation_default_instance_;

MDSLEstimation* MDSLEstimation::New(::google::protobuf::Arena* arena) const {
  MDSLEstimation* n = new MDSLEstimation;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSLEstimation::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSLEstimation)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSLEstimation, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSLEstimation*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datamultiplepowerof10_ = 0;
  ZR_(lastpx_, weightedrate_);
  ZR_(preweightedrate_, bestlendrate_);

#undef ZR_HELPER_
#undef ZR_

  longtermlends_.Clear();
  validborrows_.Clear();
  validalends_.Clear();
  validblends_.Clear();
  borrows_.Clear();
  alends_.Clear();
  blends_.Clear();
}

bool MDSLEstimation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSLEstimation)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 8;
      case 8: {
        if (tag == 64) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_LongTermLends;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
      case 9: {
        if (tag == 74) {
         parse_LongTermLends:
          DO_(input->IncrementRecursionDepth());
         parse_loop_LongTermLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_longtermlends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_loop_LongTermLends;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(80)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 10;
      case 10: {
        if (tag == 80) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 11;
      case 11: {
        if (tag == 88) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_HtscVolume;
        break;
      }

      // optional int64 HtscVolume = 14;
      case 14: {
        if (tag == 112) {
         parse_HtscVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_PreHtscVolume;
        break;
      }

      // optional int64 PreHtscVolume = 15;
      case 15: {
        if (tag == 120) {
         parse_PreHtscVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &prehtscvolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_WeightedRate;
        break;
      }

      // optional int64 WeightedRate = 16;
      case 16: {
        if (tag == 128) {
         parse_WeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_PreWeightedRate;
        break;
      }

      // optional int64 PreWeightedRate = 17;
      case 17: {
        if (tag == 136) {
         parse_PreWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_BestBorrowRate;
        break;
      }

      // optional int64 BestBorrowRate = 18;
      case 18: {
        if (tag == 144) {
         parse_BestBorrowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestborrowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_BestLendRate;
        break;
      }

      // optional int64 BestLendRate = 19;
      case 19: {
        if (tag == 152) {
         parse_BestLendRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestlendrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_ValidBorrows;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
      case 20: {
        if (tag == 162) {
         parse_ValidBorrows:
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidBorrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validborrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_loop_ValidBorrows;
        if (input->ExpectTag(170)) goto parse_loop_ValidALends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
      case 21: {
        if (tag == 170) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidALends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validalends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_loop_ValidALends;
        if (input->ExpectTag(178)) goto parse_loop_ValidBLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
      case 22: {
        if (tag == 178) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ValidBLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_validblends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_loop_ValidBLends;
        if (input->ExpectTag(186)) goto parse_loop_Borrows;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
      case 23: {
        if (tag == 186) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_Borrows:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_borrows()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_loop_Borrows;
        if (input->ExpectTag(194)) goto parse_loop_ALends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
      case 24: {
        if (tag == 194) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_ALends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_alends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_loop_ALends;
        if (input->ExpectTag(202)) goto parse_loop_BLends;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
      case 25: {
        if (tag == 202) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_BLends:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_blends()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_loop_BLends;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSLEstimation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSLEstimation)
  return false;
#undef DO_
}

void MDSLEstimation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSLEstimation)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->datamultiplepowerof10(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
  for (unsigned int i = 0, n = this->longtermlends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->longtermlends(i), output);
  }

  // optional int64 LastPx = 10;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastpx(), output);
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->preclosepx(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 HtscVolume = 14;
  if (this->htscvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->htscvolume(), output);
  }

  // optional int64 PreHtscVolume = 15;
  if (this->prehtscvolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->prehtscvolume(), output);
  }

  // optional int64 WeightedRate = 16;
  if (this->weightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->weightedrate(), output);
  }

  // optional int64 PreWeightedRate = 17;
  if (this->preweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->preweightedrate(), output);
  }

  // optional int64 BestBorrowRate = 18;
  if (this->bestborrowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->bestborrowrate(), output);
  }

  // optional int64 BestLendRate = 19;
  if (this->bestlendrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->bestlendrate(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
  for (unsigned int i = 0, n = this->validborrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, this->validborrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
  for (unsigned int i = 0, n = this->validalends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, this->validalends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
  for (unsigned int i = 0, n = this->validblends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, this->validblends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
  for (unsigned int i = 0, n = this->borrows_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, this->borrows(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
  for (unsigned int i = 0, n = this->alends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      24, this->alends(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
  for (unsigned int i = 0, n = this->blends_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      25, this->blends(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSLEstimation)
}

::google::protobuf::uint8* MDSLEstimation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSLEstimation)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->datamultiplepowerof10(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
  for (unsigned int i = 0, n = this->longtermlends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, this->longtermlends(i), false, target);
  }

  // optional int64 LastPx = 10;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastpx(), target);
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->preclosepx(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 HtscVolume = 14;
  if (this->htscvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->htscvolume(), target);
  }

  // optional int64 PreHtscVolume = 15;
  if (this->prehtscvolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->prehtscvolume(), target);
  }

  // optional int64 WeightedRate = 16;
  if (this->weightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->weightedrate(), target);
  }

  // optional int64 PreWeightedRate = 17;
  if (this->preweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->preweightedrate(), target);
  }

  // optional int64 BestBorrowRate = 18;
  if (this->bestborrowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->bestborrowrate(), target);
  }

  // optional int64 BestLendRate = 19;
  if (this->bestlendrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->bestlendrate(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
  for (unsigned int i = 0, n = this->validborrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, this->validborrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
  for (unsigned int i = 0, n = this->validalends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, this->validalends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
  for (unsigned int i = 0, n = this->validblends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, this->validblends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
  for (unsigned int i = 0, n = this->borrows_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, this->borrows(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
  for (unsigned int i = 0, n = this->alends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        24, this->alends(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
  for (unsigned int i = 0, n = this->blends_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        25, this->blends(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSLEstimation)
  return target;
}

size_t MDSLEstimation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSLEstimation)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 DataMultiplePowerOf10 = 8;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 LastPx = 10;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 PreClosePx = 11;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 HtscVolume = 14;
  if (this->htscvolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscvolume());
  }

  // optional int64 PreHtscVolume = 15;
  if (this->prehtscvolume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->prehtscvolume());
  }

  // optional int64 WeightedRate = 16;
  if (this->weightedrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedrate());
  }

  // optional int64 PreWeightedRate = 17;
  if (this->preweightedrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedrate());
  }

  // optional int64 BestBorrowRate = 18;
  if (this->bestborrowrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestborrowrate());
  }

  // optional int64 BestLendRate = 19;
  if (this->bestlendrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestlendrate());
  }

  // repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
  {
    unsigned int count = this->longtermlends_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->longtermlends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
  {
    unsigned int count = this->validborrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validborrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
  {
    unsigned int count = this->validalends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validalends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
  {
    unsigned int count = this->validblends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->validblends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
  {
    unsigned int count = this->borrows_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->borrows(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
  {
    unsigned int count = this->alends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->alends(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
  {
    unsigned int count = this->blends_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->blends(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSLEstimation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSLEstimation)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSLEstimation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSLEstimation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSLEstimation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSLEstimation)
    UnsafeMergeFrom(*source);
  }
}

void MDSLEstimation::MergeFrom(const MDSLEstimation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSLEstimation)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSLEstimation::UnsafeMergeFrom(const MDSLEstimation& from) {
  GOOGLE_DCHECK(&from != this);
  longtermlends_.MergeFrom(from.longtermlends_);
  validborrows_.MergeFrom(from.validborrows_);
  validalends_.MergeFrom(from.validalends_);
  validblends_.MergeFrom(from.validblends_);
  borrows_.MergeFrom(from.borrows_);
  alends_.MergeFrom(from.alends_);
  blends_.MergeFrom(from.blends_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.htscvolume() != 0) {
    set_htscvolume(from.htscvolume());
  }
  if (from.prehtscvolume() != 0) {
    set_prehtscvolume(from.prehtscvolume());
  }
  if (from.weightedrate() != 0) {
    set_weightedrate(from.weightedrate());
  }
  if (from.preweightedrate() != 0) {
    set_preweightedrate(from.preweightedrate());
  }
  if (from.bestborrowrate() != 0) {
    set_bestborrowrate(from.bestborrowrate());
  }
  if (from.bestlendrate() != 0) {
    set_bestlendrate(from.bestlendrate());
  }
}

void MDSLEstimation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSLEstimation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSLEstimation::CopyFrom(const MDSLEstimation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSLEstimation)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSLEstimation::IsInitialized() const {

  return true;
}

void MDSLEstimation::Swap(MDSLEstimation* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSLEstimation::InternalSwap(MDSLEstimation* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  longtermlends_.UnsafeArenaSwap(&other->longtermlends_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(htscvolume_, other->htscvolume_);
  std::swap(prehtscvolume_, other->prehtscvolume_);
  std::swap(weightedrate_, other->weightedrate_);
  std::swap(preweightedrate_, other->preweightedrate_);
  std::swap(bestborrowrate_, other->bestborrowrate_);
  std::swap(bestlendrate_, other->bestlendrate_);
  validborrows_.UnsafeArenaSwap(&other->validborrows_);
  validalends_.UnsafeArenaSwap(&other->validalends_);
  validblends_.UnsafeArenaSwap(&other->validblends_);
  borrows_.UnsafeArenaSwap(&other->borrows_);
  alends_.UnsafeArenaSwap(&other->alends_);
  blends_.UnsafeArenaSwap(&other->blends_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSLEstimation::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSLEstimation_descriptor_;
  metadata.reflection = MDSLEstimation_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLEstimation

// optional string HTSCSecurityID = 1;
void MDSLEstimation::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLEstimation::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLEstimation::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
void MDSLEstimation::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
void MDSLEstimation::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}
::std::string* MDSLEstimation::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLEstimation::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLEstimation::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLEstimation.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSLEstimation::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSLEstimation::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.MDDate)
  return mddate_;
}
void MDSLEstimation::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.MDDate)
}

// optional int32 MDTime = 3;
void MDSLEstimation::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSLEstimation::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.MDTime)
  return mdtime_;
}
void MDSLEstimation::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSLEstimation::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.DataTimestamp)
  return datatimestamp_;
}
void MDSLEstimation::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSLEstimation::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLEstimation::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLEstimation::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
void MDSLEstimation::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
void MDSLEstimation::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}
::std::string* MDSLEstimation::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLEstimation::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLEstimation::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLEstimation.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSLEstimation::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSLEstimation::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSLEstimation::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSLEstimation::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSLEstimation::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSLEstimation::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.securityType)
}

// optional int32 DataMultiplePowerOf10 = 8;
void MDSLEstimation::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSLEstimation::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSLEstimation::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADSLEstimationEntry LongTermLends = 9;
int MDSLEstimation::longtermlends_size() const {
  return longtermlends_.size();
}
void MDSLEstimation::clear_longtermlends() {
  longtermlends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSLEstimationEntry& MDSLEstimation::longtermlends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSLEstimationEntry* MDSLEstimation::mutable_longtermlends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSLEstimationEntry* MDSLEstimation::add_longtermlends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >*
MDSLEstimation::mutable_longtermlends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return &longtermlends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSLEstimationEntry >&
MDSLEstimation::longtermlends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.LongTermLends)
  return longtermlends_;
}

// optional int64 LastPx = 10;
void MDSLEstimation::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LastPx)
  return lastpx_;
}
void MDSLEstimation::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.LastPx)
}

// optional int64 PreClosePx = 11;
void MDSLEstimation::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreClosePx)
  return preclosepx_;
}
void MDSLEstimation::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreClosePx)
}

// optional int64 HighRate = 12;
void MDSLEstimation::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HighRate)
  return highrate_;
}
void MDSLEstimation::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HighRate)
}

// optional int64 LowRate = 13;
void MDSLEstimation::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.LowRate)
  return lowrate_;
}
void MDSLEstimation::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.LowRate)
}

// optional int64 HtscVolume = 14;
void MDSLEstimation::clear_htscvolume() {
  htscvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::htscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.HtscVolume)
  return htscvolume_;
}
void MDSLEstimation::set_htscvolume(::google::protobuf::int64 value) {
  
  htscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.HtscVolume)
}

// optional int64 PreHtscVolume = 15;
void MDSLEstimation::clear_prehtscvolume() {
  prehtscvolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::prehtscvolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreHtscVolume)
  return prehtscvolume_;
}
void MDSLEstimation::set_prehtscvolume(::google::protobuf::int64 value) {
  
  prehtscvolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreHtscVolume)
}

// optional int64 WeightedRate = 16;
void MDSLEstimation::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.WeightedRate)
  return weightedrate_;
}
void MDSLEstimation::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.WeightedRate)
}

// optional int64 PreWeightedRate = 17;
void MDSLEstimation::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.PreWeightedRate)
  return preweightedrate_;
}
void MDSLEstimation::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.PreWeightedRate)
}

// optional int64 BestBorrowRate = 18;
void MDSLEstimation::clear_bestborrowrate() {
  bestborrowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::bestborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BestBorrowRate)
  return bestborrowrate_;
}
void MDSLEstimation::set_bestborrowrate(::google::protobuf::int64 value) {
  
  bestborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.BestBorrowRate)
}

// optional int64 BestLendRate = 19;
void MDSLEstimation::clear_bestlendrate() {
  bestlendrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLEstimation::bestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BestLendRate)
  return bestlendrate_;
}
void MDSLEstimation::set_bestlendrate(::google::protobuf::int64 value) {
  
  bestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLEstimation.BestLendRate)
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBorrows = 20;
int MDSLEstimation::validborrows_size() const {
  return validborrows_.size();
}
void MDSLEstimation::clear_validborrows() {
  validborrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validborrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validborrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validborrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validborrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return &validborrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validborrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBorrows)
  return validborrows_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidALends = 21;
int MDSLEstimation::validalends_size() const {
  return validalends_.size();
}
void MDSLEstimation::clear_validalends() {
  validalends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validalends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validalends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validalends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validalends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return &validalends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validalends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidALends)
  return validalends_;
}

// repeated .com.htsc.mdc.insight.model.ADValidSecurityLendingEntry ValidBLends = 22;
int MDSLEstimation::validblends_size() const {
  return validblends_.size();
}
void MDSLEstimation::clear_validblends() {
  validblends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry& MDSLEstimation::validblends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Get(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::mutable_validblends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry* MDSLEstimation::add_validblends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >*
MDSLEstimation::mutable_validblends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return &validblends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADValidSecurityLendingEntry >&
MDSLEstimation::validblends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ValidBLends)
  return validblends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry Borrows = 23;
int MDSLEstimation::borrows_size() const {
  return borrows_.size();
}
void MDSLEstimation::clear_borrows() {
  borrows_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::borrows(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_borrows(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_borrows() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_borrows() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return &borrows_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::borrows() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.Borrows)
  return borrows_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry ALends = 24;
int MDSLEstimation::alends_size() const {
  return alends_.size();
}
void MDSLEstimation::clear_alends() {
  alends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::alends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_alends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_alends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_alends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return &alends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::alends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.ALends)
  return alends_;
}

// repeated .com.htsc.mdc.insight.model.ADSecurityLendingEntry BLends = 25;
int MDSLEstimation::blends_size() const {
  return blends_.size();
}
void MDSLEstimation::clear_blends() {
  blends_.Clear();
}
const ::com::htsc::mdc::insight::model::ADSecurityLendingEntry& MDSLEstimation::blends(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Get(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::mutable_blends(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADSecurityLendingEntry* MDSLEstimation::add_blends() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >*
MDSLEstimation::mutable_blends() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return &blends_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADSecurityLendingEntry >&
MDSLEstimation::blends() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSLEstimation.BLends)
  return blends_;
}

inline const MDSLEstimation* MDSLEstimation::internal_default_instance() {
  return &MDSLEstimation_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADSLEstimationEntry::kLevelFieldNumber;
const int ADSLEstimationEntry::kRateFieldNumber;
const int ADSLEstimationEntry::kTermFieldNumber;
const int ADSLEstimationEntry::kAmountFieldNumber;
const int ADSLEstimationEntry::kPostponeProbabilityFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADSLEstimationEntry::ADSLEstimationEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLEstimation_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADSLEstimationEntry)
}

void ADSLEstimationEntry::InitAsDefaultInstance() {
}

ADSLEstimationEntry::ADSLEstimationEntry(const ADSLEstimationEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADSLEstimationEntry)
}

void ADSLEstimationEntry::SharedCtor() {
  term_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&rate_, 0, reinterpret_cast<char*>(&amount_) -
    reinterpret_cast<char*>(&rate_) + sizeof(amount_));
  _cached_size_ = 0;
}

ADSLEstimationEntry::~ADSLEstimationEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  SharedDtor();
}

void ADSLEstimationEntry::SharedDtor() {
  term_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADSLEstimationEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADSLEstimationEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADSLEstimationEntry_descriptor_;
}

const ADSLEstimationEntry& ADSLEstimationEntry::default_instance() {
  protobuf_InitDefaults_MDSLEstimation_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADSLEstimationEntry> ADSLEstimationEntry_default_instance_;

ADSLEstimationEntry* ADSLEstimationEntry::New(::google::protobuf::Arena* arena) const {
  ADSLEstimationEntry* n = new ADSLEstimationEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADSLEstimationEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADSLEstimationEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADSLEstimationEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(rate_, amount_);
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ADSLEstimationEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Level = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &level_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Rate;
        break;
      }

      // optional int64 Rate = 2;
      case 2: {
        if (tag == 16) {
         parse_Rate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &rate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Term;
        break;
      }

      // optional string Term = 3;
      case 3: {
        if (tag == 26) {
         parse_Term:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_term()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->term().data(), this->term().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADSLEstimationEntry.Term"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Amount;
        break;
      }

      // optional int64 Amount = 4;
      case 4: {
        if (tag == 32) {
         parse_Amount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &amount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_PostponeProbability;
        break;
      }

      // optional int32 PostponeProbability = 5;
      case 5: {
        if (tag == 40) {
         parse_PostponeProbability:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &postponeprobability_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  return false;
#undef DO_
}

void ADSLEstimationEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->level(), output);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->rate(), output);
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->term().data(), this->term().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADSLEstimationEntry.Term");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->term(), output);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->amount(), output);
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->postponeprobability(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADSLEstimationEntry)
}

::google::protobuf::uint8* ADSLEstimationEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  // optional int32 Level = 1;
  if (this->level() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->level(), target);
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->rate(), target);
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->term().data(), this->term().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADSLEstimationEntry.Term");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->term(), target);
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->amount(), target);
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->postponeprobability(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  return target;
}

size_t ADSLEstimationEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  size_t total_size = 0;

  // optional int32 Level = 1;
  if (this->level() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->level());
  }

  // optional int64 Rate = 2;
  if (this->rate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->rate());
  }

  // optional string Term = 3;
  if (this->term().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->term());
  }

  // optional int64 Amount = 4;
  if (this->amount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->amount());
  }

  // optional int32 PostponeProbability = 5;
  if (this->postponeprobability() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->postponeprobability());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADSLEstimationEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADSLEstimationEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADSLEstimationEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADSLEstimationEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADSLEstimationEntry)
    UnsafeMergeFrom(*source);
  }
}

void ADSLEstimationEntry::MergeFrom(const ADSLEstimationEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADSLEstimationEntry::UnsafeMergeFrom(const ADSLEstimationEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.level() != 0) {
    set_level(from.level());
  }
  if (from.rate() != 0) {
    set_rate(from.rate());
  }
  if (from.term().size() > 0) {

    term_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.term_);
  }
  if (from.amount() != 0) {
    set_amount(from.amount());
  }
  if (from.postponeprobability() != 0) {
    set_postponeprobability(from.postponeprobability());
  }
}

void ADSLEstimationEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADSLEstimationEntry::CopyFrom(const ADSLEstimationEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADSLEstimationEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADSLEstimationEntry::IsInitialized() const {

  return true;
}

void ADSLEstimationEntry::Swap(ADSLEstimationEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADSLEstimationEntry::InternalSwap(ADSLEstimationEntry* other) {
  std::swap(level_, other->level_);
  std::swap(rate_, other->rate_);
  term_.Swap(&other->term_);
  std::swap(amount_, other->amount_);
  std::swap(postponeprobability_, other->postponeprobability_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADSLEstimationEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADSLEstimationEntry_descriptor_;
  metadata.reflection = ADSLEstimationEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADSLEstimationEntry

// optional int32 Level = 1;
void ADSLEstimationEntry::clear_level() {
  level_ = 0;
}
::google::protobuf::int32 ADSLEstimationEntry::level() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Level)
  return level_;
}
void ADSLEstimationEntry::set_level(::google::protobuf::int32 value) {
  
  level_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Level)
}

// optional int64 Rate = 2;
void ADSLEstimationEntry::clear_rate() {
  rate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADSLEstimationEntry::rate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Rate)
  return rate_;
}
void ADSLEstimationEntry::set_rate(::google::protobuf::int64 value) {
  
  rate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Rate)
}

// optional string Term = 3;
void ADSLEstimationEntry::clear_term() {
  term_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADSLEstimationEntry::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  return term_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADSLEstimationEntry::set_term(const ::std::string& value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
void ADSLEstimationEntry::set_term(const char* value) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
void ADSLEstimationEntry::set_term(const char* value, size_t size) {
  
  term_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}
::std::string* ADSLEstimationEntry::mutable_term() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  return term_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADSLEstimationEntry::release_term() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
  
  return term_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADSLEstimationEntry::set_allocated_term(::std::string* term) {
  if (term != NULL) {
    
  } else {
    
  }
  term_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), term);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADSLEstimationEntry.Term)
}

// optional int64 Amount = 4;
void ADSLEstimationEntry::clear_amount() {
  amount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADSLEstimationEntry::amount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.Amount)
  return amount_;
}
void ADSLEstimationEntry::set_amount(::google::protobuf::int64 value) {
  
  amount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.Amount)
}

// optional int32 PostponeProbability = 5;
void ADSLEstimationEntry::clear_postponeprobability() {
  postponeprobability_ = 0;
}
::google::protobuf::int32 ADSLEstimationEntry::postponeprobability() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADSLEstimationEntry.PostponeProbability)
  return postponeprobability_;
}
void ADSLEstimationEntry::set_postponeprobability(::google::protobuf::int32 value) {
  
  postponeprobability_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADSLEstimationEntry.PostponeProbability)
}

inline const ADSLEstimationEntry* ADSLEstimationEntry::internal_default_instance() {
  return &ADSLEstimationEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
