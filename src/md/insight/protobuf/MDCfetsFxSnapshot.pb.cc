// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxSnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsFxSnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsFxSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsFxSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* SwpSptNdfFowFxSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SwpSptNdfFowFxSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* OptionFxSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OptionFxSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* SpotClosePriceFxSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SpotClosePriceFxSnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto() {
  protobuf_AddDesc_MDCfetsFxSnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsFxSnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsFxSnapshot_descriptor_ = file->message_type(0);
  static const int MDCfetsFxSnapshot_offsets_[17] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, securitysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, forexsnapshottype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, spotfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, forwardfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, nondeliverableforwardsfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, swapfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, optionfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, spotclosepricefxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, marketindicator_),
  };
  MDCfetsFxSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsFxSnapshot_descriptor_,
      MDCfetsFxSnapshot::internal_default_instance(),
      MDCfetsFxSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsFxSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxSnapshot, _internal_metadata_));
  SwpSptNdfFowFxSnapshot_descriptor_ = file->message_type(1);
  static const int SwpSptNdfFowFxSnapshot_offsets_[27] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, valuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, netbasischange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, percentagechange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, datebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, timebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, datesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, timesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, lastratebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, lastratesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, lastallinbuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, lastallinsell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, historycloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, amountlevelrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, amountlevelallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, rateside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, allinside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, legsign_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, fillside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, dateconfirmed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, contingencywithdraw_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, contingencytradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, contingencytradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, contingencylastpx_),
  };
  SwpSptNdfFowFxSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SwpSptNdfFowFxSnapshot_descriptor_,
      SwpSptNdfFowFxSnapshot::internal_default_instance(),
      SwpSptNdfFowFxSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(SwpSptNdfFowFxSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxSnapshot, _internal_metadata_));
  OptionFxSnapshot_descriptor_ = file->message_type(2);
  static const int OptionFxSnapshot_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, fxterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, premium_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, volatility_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, premiumtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, optiontype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, dateconfirmed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, optiontypeenum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, derivativeexercisestyle_),
  };
  OptionFxSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OptionFxSnapshot_descriptor_,
      OptionFxSnapshot::internal_default_instance(),
      OptionFxSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(OptionFxSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxSnapshot, _internal_metadata_));
  SpotClosePriceFxSnapshot_descriptor_ = file->message_type(3);
  static const int SpotClosePriceFxSnapshot_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceFxSnapshot, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceFxSnapshot, updatedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceFxSnapshot, updatetime_),
  };
  SpotClosePriceFxSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SpotClosePriceFxSnapshot_descriptor_,
      SpotClosePriceFxSnapshot::internal_default_instance(),
      SpotClosePriceFxSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(SpotClosePriceFxSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceFxSnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsFxSnapshot_descriptor_, MDCfetsFxSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SwpSptNdfFowFxSnapshot_descriptor_, SwpSptNdfFowFxSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OptionFxSnapshot_descriptor_, OptionFxSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SpotClosePriceFxSnapshot_descriptor_, SpotClosePriceFxSnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto() {
  MDCfetsFxSnapshot_default_instance_.Shutdown();
  delete MDCfetsFxSnapshot_reflection_;
  SwpSptNdfFowFxSnapshot_default_instance_.Shutdown();
  delete SwpSptNdfFowFxSnapshot_reflection_;
  OptionFxSnapshot_default_instance_.Shutdown();
  delete OptionFxSnapshot_reflection_;
  SpotClosePriceFxSnapshot_default_instance_.Shutdown();
  delete SpotClosePriceFxSnapshot_reflection_;
}

void protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsFxSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SwpSptNdfFowFxSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  OptionFxSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SpotClosePriceFxSnapshot_default_instance_.DefaultConstruct();
  MDCfetsFxSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  SwpSptNdfFowFxSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  OptionFxSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  SpotClosePriceFxSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_once_);
void protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\027MDCfetsFxSnapshot.proto\022\032com.htsc.mdc."
    "insight.model\032\027ESecurityIDSource.proto\032\023"
    "ESecurityType.proto\"\303\006\n\021MDCfetsFxSnapsho"
    "t\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001("
    "\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003"
    "\022\?\n\020securityIDSource\030\005 \001(\0162%.com.htsc.md"
    "c.model.ESecurityIDSource\0227\n\014securityTyp"
    "e\030\006 \001(\0162!.com.htsc.mdc.model.ESecurityTy"
    "pe\022\027\n\017SecuritySubType\030\007 \001(\t\022\031\n\021ForexSnap"
    "shotType\030\010 \001(\005\022J\n\016spotFxSnapshot\030\t \001(\01322"
    ".com.htsc.mdc.insight.model.SwpSptNdfFow"
    "FxSnapshot\022M\n\021forwardFxSnapshot\030\n \001(\01322."
    "com.htsc.mdc.insight.model.SwpSptNdfFowF"
    "xSnapshot\022\\\n nonDeliverableForwardsFxSna"
    "pshot\030\013 \001(\01322.com.htsc.mdc.insight.model"
    ".SwpSptNdfFowFxSnapshot\022J\n\016swapFxSnapsho"
    "t\030\014 \001(\01322.com.htsc.mdc.insight.model.Swp"
    "SptNdfFowFxSnapshot\022F\n\020optionFxSnapshot\030"
    "\r \001(\0132,.com.htsc.mdc.insight.model.Optio"
    "nFxSnapshot\022V\n\030spotClosePriceFxSnapshot\030"
    "\016 \001(\01324.com.htsc.mdc.insight.model.SpotC"
    "losePriceFxSnapshot\022\035\n\025DataMultiplePower"
    "Of10\030\017 \001(\005\022\024\n\014TransactTime\030\020 \001(\t\022\027\n\017Mark"
    "etIndicator\030\024 \001(\t\"\343\004\n\026SwpSptNdfFowFxSnap"
    "shot\022\021\n\tValueDate\030\001 \001(\t\022\026\n\016NetBasisChang"
    "e\030\002 \001(\003\022\030\n\020PercentageChange\030\003 \001(\003\022\017\n\007Dat"
    "eBuy\030\004 \001(\t\022\017\n\007TimeBuy\030\005 \001(\t\022\020\n\010DateSell\030"
    "\006 \001(\t\022\020\n\010TimeSell\030\007 \001(\t\022\023\n\013LastRateBuy\030\010"
    " \001(\003\022\024\n\014LastRateSell\030\t \001(\003\022\024\n\014LastAllinB"
    "uy\030\n \001(\003\022\025\n\rLastAllinSell\030\013 \001(\003\022\020\n\010HighR"
    "ate\030\014 \001(\003\022\017\n\007LowRate\030\r \001(\003\022\020\n\010OpenRate\030\016"
    " \001(\003\022\030\n\020HistoryCloseRate\030\017 \001(\003\022\021\n\tCloseR"
    "ate\030\020 \001(\003\022\027\n\017AmountLevelRate\030\021 \001(\005\022\030\n\020Am"
    "ountLevelAllin\030\022 \001(\005\022\020\n\010RateSide\030\023 \001(\005\022\021"
    "\n\tAllinSide\030\024 \001(\005\022\017\n\007LegSign\030\025 \001(\t\022\020\n\010Fi"
    "llSide\030\026 \001(\t\022\025\n\rDateConfirmed\030\027 \001(\t\022\033\n\023C"
    "ontingencyWithdraw\030\030 \001(\010\022\034\n\024ContingencyT"
    "radeDate\030\031 \001(\t\022\034\n\024ContingencyTradeTime\030\032"
    " \001(\t\022\031\n\021ContingencyLastPx\030\033 \001(\003\"\366\001\n\020Opti"
    "onFxSnapshot\022\016\n\006FxTerm\030\001 \001(\t\022\017\n\007Premium\030"
    "\002 \001(\003\022\022\n\nVolatility\030\003 \001(\003\022\016\n\006Volume\030\004 \001("
    "\003\022\021\n\tTradeDate\030\005 \001(\t\022\021\n\tTradeTime\030\006 \001(\t\022"
    "\023\n\013PremiumType\030\007 \001(\005\022\022\n\nOptionType\030\010 \001(\t"
    "\022\025\n\rDateConfirmed\030\t \001(\t\022\026\n\016OptionTypeEnu"
    "m\030\n \001(\005\022\037\n\027DerivativeExerciseStyle\030\013 \001(\t"
    "\"S\n\030SpotClosePriceFxSnapshot\022\017\n\007ClosePx\030"
    "\001 \001(\003\022\022\n\nUpdateDate\030\002 \001(\t\022\022\n\nUpdateTime\030"
    "\003 \001(\tB:\n\032com.htsc.mdc.insight.modelB\027MDC"
    "fetsFxSnapshotProtosH\001\240\001\001b\006proto3", 1953);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsFxSnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_once_);
void protobuf_AddDesc_MDCfetsFxSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsFxSnapshot_2eproto {
  StaticDescriptorInitializer_MDCfetsFxSnapshot_2eproto() {
    protobuf_AddDesc_MDCfetsFxSnapshot_2eproto();
  }
} static_descriptor_initializer_MDCfetsFxSnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsFxSnapshot::kHTSCSecurityIDFieldNumber;
const int MDCfetsFxSnapshot::kMDDateFieldNumber;
const int MDCfetsFxSnapshot::kMDTimeFieldNumber;
const int MDCfetsFxSnapshot::kDataTimestampFieldNumber;
const int MDCfetsFxSnapshot::kSecurityIDSourceFieldNumber;
const int MDCfetsFxSnapshot::kSecurityTypeFieldNumber;
const int MDCfetsFxSnapshot::kSecuritySubTypeFieldNumber;
const int MDCfetsFxSnapshot::kForexSnapshotTypeFieldNumber;
const int MDCfetsFxSnapshot::kSpotFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kForwardFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kNonDeliverableForwardsFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kSwapFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kOptionFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kSpotClosePriceFxSnapshotFieldNumber;
const int MDCfetsFxSnapshot::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsFxSnapshot::kTransactTimeFieldNumber;
const int MDCfetsFxSnapshot::kMarketIndicatorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsFxSnapshot::MDCfetsFxSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
}

void MDCfetsFxSnapshot::InitAsDefaultInstance() {
  spotfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance());
  forwardfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance());
  nondeliverableforwardsfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance());
  swapfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance());
  optionfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::OptionFxSnapshot*>(
      ::com::htsc::mdc::insight::model::OptionFxSnapshot::internal_default_instance());
  spotclosepricefxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot*>(
      ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot::internal_default_instance());
}

MDCfetsFxSnapshot::MDCfetsFxSnapshot(const MDCfetsFxSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
}

void MDCfetsFxSnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  spotfxsnapshot_ = NULL;
  forwardfxsnapshot_ = NULL;
  nondeliverableforwardsfxsnapshot_ = NULL;
  swapfxsnapshot_ = NULL;
  optionfxsnapshot_ = NULL;
  spotclosepricefxsnapshot_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCfetsFxSnapshot::~MDCfetsFxSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  SharedDtor();
}

void MDCfetsFxSnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsFxSnapshot_default_instance_.get()) {
    delete spotfxsnapshot_;
    delete forwardfxsnapshot_;
    delete nondeliverableforwardsfxsnapshot_;
    delete swapfxsnapshot_;
    delete optionfxsnapshot_;
    delete spotclosepricefxsnapshot_;
  }
}

void MDCfetsFxSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsFxSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsFxSnapshot_descriptor_;
}

const MDCfetsFxSnapshot& MDCfetsFxSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxSnapshot> MDCfetsFxSnapshot_default_instance_;

MDCfetsFxSnapshot* MDCfetsFxSnapshot::New(::google::protobuf::Arena* arena) const {
  MDCfetsFxSnapshot* n = new MDCfetsFxSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsFxSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsFxSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsFxSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, forexsnapshottype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && spotfxsnapshot_ != NULL) delete spotfxsnapshot_;
  spotfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && forwardfxsnapshot_ != NULL) delete forwardfxsnapshot_;
  forwardfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxsnapshot_ != NULL) delete nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && swapfxsnapshot_ != NULL) delete swapfxsnapshot_;
  swapfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && optionfxsnapshot_ != NULL) delete optionfxsnapshot_;
  optionfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && spotclosepricefxsnapshot_ != NULL) delete spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = NULL;
  datamultiplepowerof10_ = 0;
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsFxSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SecuritySubType;
        break;
      }

      // optional string SecuritySubType = 7;
      case 7: {
        if (tag == 58) {
         parse_SecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitysubtype().data(), this->securitysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ForexSnapshotType;
        break;
      }

      // optional int32 ForexSnapshotType = 8;
      case 8: {
        if (tag == 64) {
         parse_ForexSnapshotType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &forexsnapshottype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_spotFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
      case 9: {
        if (tag == 74) {
         parse_spotFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spotfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_forwardFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
      case 10: {
        if (tag == 82) {
         parse_forwardFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_forwardfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_nonDeliverableForwardsFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
      case 11: {
        if (tag == 90) {
         parse_nonDeliverableForwardsFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nondeliverableforwardsfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_swapFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
      case 12: {
        if (tag == 98) {
         parse_swapFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_swapfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_optionFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
      case 13: {
        if (tag == 106) {
         parse_optionFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optionfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_spotClosePriceFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
      case 14: {
        if (tag == 114) {
         parse_spotClosePriceFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spotclosepricefxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 15;
      case 15: {
        if (tag == 120) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 16;
      case 16: {
        if (tag == 130) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 20;
      case 20: {
        if (tag == 162) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  return false;
#undef DO_
}

void MDCfetsFxSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->securitysubtype(), output);
  }

  // optional int32 ForexSnapshotType = 8;
  if (this->forexsnapshottype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->forexsnapshottype(), output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
  if (this->has_spotfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->spotfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
  if (this->has_forwardfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->forwardfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
  if (this->has_nondeliverableforwardsfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->nondeliverableforwardsfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
  if (this->has_swapfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->swapfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
  if (this->has_optionfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->optionfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
  if (this->has_spotclosepricefxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->spotclosepricefxsnapshot_, output);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->datamultiplepowerof10(), output);
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->transacttime(), output);
  }

  // optional string MarketIndicator = 20;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->marketindicator(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
}

::google::protobuf::uint8* MDCfetsFxSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->securitysubtype(), target);
  }

  // optional int32 ForexSnapshotType = 8;
  if (this->forexsnapshottype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->forexsnapshottype(), target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
  if (this->has_spotfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->spotfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
  if (this->has_forwardfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->forwardfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
  if (this->has_nondeliverableforwardsfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->nondeliverableforwardsfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
  if (this->has_swapfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->swapfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
  if (this->has_optionfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->optionfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
  if (this->has_spotclosepricefxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->spotclosepricefxsnapshot_, false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->datamultiplepowerof10(), target);
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->transacttime(), target);
  }

  // optional string MarketIndicator = 20;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->marketindicator(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  return target;
}

size_t MDCfetsFxSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitysubtype());
  }

  // optional int32 ForexSnapshotType = 8;
  if (this->forexsnapshottype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->forexsnapshottype());
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
  if (this->has_spotfxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spotfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
  if (this->has_forwardfxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->forwardfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
  if (this->has_nondeliverableforwardsfxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nondeliverableforwardsfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
  if (this->has_swapfxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->swapfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
  if (this->has_optionfxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optionfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
  if (this->has_spotclosepricefxsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spotclosepricefxsnapshot_);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 20;
  if (this->marketindicator().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsFxSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsFxSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsFxSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsFxSnapshot::MergeFrom(const MDCfetsFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsFxSnapshot::UnsafeMergeFrom(const MDCfetsFxSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securitysubtype().size() > 0) {

    securitysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitysubtype_);
  }
  if (from.forexsnapshottype() != 0) {
    set_forexsnapshottype(from.forexsnapshottype());
  }
  if (from.has_spotfxsnapshot()) {
    mutable_spotfxsnapshot()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::MergeFrom(from.spotfxsnapshot());
  }
  if (from.has_forwardfxsnapshot()) {
    mutable_forwardfxsnapshot()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::MergeFrom(from.forwardfxsnapshot());
  }
  if (from.has_nondeliverableforwardsfxsnapshot()) {
    mutable_nondeliverableforwardsfxsnapshot()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::MergeFrom(from.nondeliverableforwardsfxsnapshot());
  }
  if (from.has_swapfxsnapshot()) {
    mutable_swapfxsnapshot()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::MergeFrom(from.swapfxsnapshot());
  }
  if (from.has_optionfxsnapshot()) {
    mutable_optionfxsnapshot()->::com::htsc::mdc::insight::model::OptionFxSnapshot::MergeFrom(from.optionfxsnapshot());
  }
  if (from.has_spotclosepricefxsnapshot()) {
    mutable_spotclosepricefxsnapshot()->::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot::MergeFrom(from.spotclosepricefxsnapshot());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
}

void MDCfetsFxSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsFxSnapshot::CopyFrom(const MDCfetsFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsFxSnapshot::IsInitialized() const {

  return true;
}

void MDCfetsFxSnapshot::Swap(MDCfetsFxSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsFxSnapshot::InternalSwap(MDCfetsFxSnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  securitysubtype_.Swap(&other->securitysubtype_);
  std::swap(forexsnapshottype_, other->forexsnapshottype_);
  std::swap(spotfxsnapshot_, other->spotfxsnapshot_);
  std::swap(forwardfxsnapshot_, other->forwardfxsnapshot_);
  std::swap(nondeliverableforwardsfxsnapshot_, other->nondeliverableforwardsfxsnapshot_);
  std::swap(swapfxsnapshot_, other->swapfxsnapshot_);
  std::swap(optionfxsnapshot_, other->optionfxsnapshot_);
  std::swap(spotclosepricefxsnapshot_, other->spotclosepricefxsnapshot_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsFxSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsFxSnapshot_descriptor_;
  metadata.reflection = MDCfetsFxSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxSnapshot

// optional string HTSCSecurityID = 1;
void MDCfetsFxSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
void MDCfetsFxSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
void MDCfetsFxSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
::std::string* MDCfetsFxSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCfetsFxSnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsFxSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDDate)
  return mddate_;
}
void MDCfetsFxSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDDate)
}

// optional int32 MDTime = 3;
void MDCfetsFxSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsFxSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDTime)
  return mdtime_;
}
void MDCfetsFxSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCfetsFxSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsFxSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCfetsFxSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsFxSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCfetsFxSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsFxSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsFxSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityType)
}

// optional string SecuritySubType = 7;
void MDCfetsFxSnapshot::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxSnapshot::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
void MDCfetsFxSnapshot::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
void MDCfetsFxSnapshot::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
::std::string* MDCfetsFxSnapshot::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxSnapshot::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}

// optional int32 ForexSnapshotType = 8;
void MDCfetsFxSnapshot::clear_forexsnapshottype() {
  forexsnapshottype_ = 0;
}
::google::protobuf::int32 MDCfetsFxSnapshot::forexsnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.ForexSnapshotType)
  return forexsnapshottype_;
}
void MDCfetsFxSnapshot::set_forexsnapshottype(::google::protobuf::int32 value) {
  
  forexsnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.ForexSnapshotType)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
bool MDCfetsFxSnapshot::has_spotfxsnapshot() const {
  return this != internal_default_instance() && spotfxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_spotfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && spotfxsnapshot_ != NULL) delete spotfxsnapshot_;
  spotfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::spotfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  return spotfxsnapshot_ != NULL ? *spotfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_spotfxsnapshot() {
  
  if (spotfxsnapshot_ == NULL) {
    spotfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  return spotfxsnapshot_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_spotfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = spotfxsnapshot_;
  spotfxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_spotfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* spotfxsnapshot) {
  delete spotfxsnapshot_;
  spotfxsnapshot_ = spotfxsnapshot;
  if (spotfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
bool MDCfetsFxSnapshot::has_forwardfxsnapshot() const {
  return this != internal_default_instance() && forwardfxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_forwardfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && forwardfxsnapshot_ != NULL) delete forwardfxsnapshot_;
  forwardfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::forwardfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  return forwardfxsnapshot_ != NULL ? *forwardfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_forwardfxsnapshot() {
  
  if (forwardfxsnapshot_ == NULL) {
    forwardfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  return forwardfxsnapshot_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_forwardfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = forwardfxsnapshot_;
  forwardfxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_forwardfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* forwardfxsnapshot) {
  delete forwardfxsnapshot_;
  forwardfxsnapshot_ = forwardfxsnapshot;
  if (forwardfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
bool MDCfetsFxSnapshot::has_nondeliverableforwardsfxsnapshot() const {
  return this != internal_default_instance() && nondeliverableforwardsfxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_nondeliverableforwardsfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxsnapshot_ != NULL) delete nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::nondeliverableforwardsfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  return nondeliverableforwardsfxsnapshot_ != NULL ? *nondeliverableforwardsfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_nondeliverableforwardsfxsnapshot() {
  
  if (nondeliverableforwardsfxsnapshot_ == NULL) {
    nondeliverableforwardsfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  return nondeliverableforwardsfxsnapshot_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_nondeliverableforwardsfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_nondeliverableforwardsfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* nondeliverableforwardsfxsnapshot) {
  delete nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = nondeliverableforwardsfxsnapshot;
  if (nondeliverableforwardsfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
bool MDCfetsFxSnapshot::has_swapfxsnapshot() const {
  return this != internal_default_instance() && swapfxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_swapfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && swapfxsnapshot_ != NULL) delete swapfxsnapshot_;
  swapfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::swapfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  return swapfxsnapshot_ != NULL ? *swapfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_swapfxsnapshot() {
  
  if (swapfxsnapshot_ == NULL) {
    swapfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  return swapfxsnapshot_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_swapfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = swapfxsnapshot_;
  swapfxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_swapfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* swapfxsnapshot) {
  delete swapfxsnapshot_;
  swapfxsnapshot_ = swapfxsnapshot;
  if (swapfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
bool MDCfetsFxSnapshot::has_optionfxsnapshot() const {
  return this != internal_default_instance() && optionfxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_optionfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && optionfxsnapshot_ != NULL) delete optionfxsnapshot_;
  optionfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::OptionFxSnapshot& MDCfetsFxSnapshot::optionfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  return optionfxsnapshot_ != NULL ? *optionfxsnapshot_
                         : *::com::htsc::mdc::insight::model::OptionFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::OptionFxSnapshot* MDCfetsFxSnapshot::mutable_optionfxsnapshot() {
  
  if (optionfxsnapshot_ == NULL) {
    optionfxsnapshot_ = new ::com::htsc::mdc::insight::model::OptionFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  return optionfxsnapshot_;
}
::com::htsc::mdc::insight::model::OptionFxSnapshot* MDCfetsFxSnapshot::release_optionfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  
  ::com::htsc::mdc::insight::model::OptionFxSnapshot* temp = optionfxsnapshot_;
  optionfxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_optionfxsnapshot(::com::htsc::mdc::insight::model::OptionFxSnapshot* optionfxsnapshot) {
  delete optionfxsnapshot_;
  optionfxsnapshot_ = optionfxsnapshot;
  if (optionfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
bool MDCfetsFxSnapshot::has_spotclosepricefxsnapshot() const {
  return this != internal_default_instance() && spotclosepricefxsnapshot_ != NULL;
}
void MDCfetsFxSnapshot::clear_spotclosepricefxsnapshot() {
  if (GetArenaNoVirtual() == NULL && spotclosepricefxsnapshot_ != NULL) delete spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot& MDCfetsFxSnapshot::spotclosepricefxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  return spotclosepricefxsnapshot_ != NULL ? *spotclosepricefxsnapshot_
                         : *::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* MDCfetsFxSnapshot::mutable_spotclosepricefxsnapshot() {
  
  if (spotclosepricefxsnapshot_ == NULL) {
    spotclosepricefxsnapshot_ = new ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  return spotclosepricefxsnapshot_;
}
::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* MDCfetsFxSnapshot::release_spotclosepricefxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* temp = spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = NULL;
  return temp;
}
void MDCfetsFxSnapshot::set_allocated_spotclosepricefxsnapshot(::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* spotclosepricefxsnapshot) {
  delete spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = spotclosepricefxsnapshot;
  if (spotclosepricefxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
}

// optional int32 DataMultiplePowerOf10 = 15;
void MDCfetsFxSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsFxSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsFxSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataMultiplePowerOf10)
}

// optional string TransactTime = 16;
void MDCfetsFxSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
void MDCfetsFxSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
void MDCfetsFxSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
::std::string* MDCfetsFxSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}

// optional string MarketIndicator = 20;
void MDCfetsFxSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
void MDCfetsFxSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
void MDCfetsFxSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
::std::string* MDCfetsFxSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}

inline const MDCfetsFxSnapshot* MDCfetsFxSnapshot::internal_default_instance() {
  return &MDCfetsFxSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SwpSptNdfFowFxSnapshot::kValueDateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kNetBasisChangeFieldNumber;
const int SwpSptNdfFowFxSnapshot::kPercentageChangeFieldNumber;
const int SwpSptNdfFowFxSnapshot::kDateBuyFieldNumber;
const int SwpSptNdfFowFxSnapshot::kTimeBuyFieldNumber;
const int SwpSptNdfFowFxSnapshot::kDateSellFieldNumber;
const int SwpSptNdfFowFxSnapshot::kTimeSellFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLastRateBuyFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLastRateSellFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLastAllinBuyFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLastAllinSellFieldNumber;
const int SwpSptNdfFowFxSnapshot::kHighRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLowRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kOpenRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kHistoryCloseRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kCloseRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kAmountLevelRateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kAmountLevelAllinFieldNumber;
const int SwpSptNdfFowFxSnapshot::kRateSideFieldNumber;
const int SwpSptNdfFowFxSnapshot::kAllinSideFieldNumber;
const int SwpSptNdfFowFxSnapshot::kLegSignFieldNumber;
const int SwpSptNdfFowFxSnapshot::kFillSideFieldNumber;
const int SwpSptNdfFowFxSnapshot::kDateConfirmedFieldNumber;
const int SwpSptNdfFowFxSnapshot::kContingencyWithdrawFieldNumber;
const int SwpSptNdfFowFxSnapshot::kContingencyTradeDateFieldNumber;
const int SwpSptNdfFowFxSnapshot::kContingencyTradeTimeFieldNumber;
const int SwpSptNdfFowFxSnapshot::kContingencyLastPxFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SwpSptNdfFowFxSnapshot::SwpSptNdfFowFxSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
}

void SwpSptNdfFowFxSnapshot::InitAsDefaultInstance() {
}

SwpSptNdfFowFxSnapshot::SwpSptNdfFowFxSnapshot(const SwpSptNdfFowFxSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
}

void SwpSptNdfFowFxSnapshot::SharedCtor() {
  valuedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fillside_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencytradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencytradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&netbasischange_, 0, reinterpret_cast<char*>(&contingencywithdraw_) -
    reinterpret_cast<char*>(&netbasischange_) + sizeof(contingencywithdraw_));
  _cached_size_ = 0;
}

SwpSptNdfFowFxSnapshot::~SwpSptNdfFowFxSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  SharedDtor();
}

void SwpSptNdfFowFxSnapshot::SharedDtor() {
  valuedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fillside_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencytradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencytradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SwpSptNdfFowFxSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SwpSptNdfFowFxSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SwpSptNdfFowFxSnapshot_descriptor_;
}

const SwpSptNdfFowFxSnapshot& SwpSptNdfFowFxSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SwpSptNdfFowFxSnapshot> SwpSptNdfFowFxSnapshot_default_instance_;

SwpSptNdfFowFxSnapshot* SwpSptNdfFowFxSnapshot::New(::google::protobuf::Arena* arena) const {
  SwpSptNdfFowFxSnapshot* n = new SwpSptNdfFowFxSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SwpSptNdfFowFxSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(SwpSptNdfFowFxSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<SwpSptNdfFowFxSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(netbasischange_, lastratebuy_);
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  timesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lastratesell_, closerate_);
  ZR_(amountlevelrate_, allinside_);
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fillside_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencywithdraw_ = false;
  contingencytradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencytradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  contingencylastpx_ = GOOGLE_LONGLONG(0);

#undef ZR_HELPER_
#undef ZR_

}

bool SwpSptNdfFowFxSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ValueDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuedate().data(), this->valuedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_NetBasisChange;
        break;
      }

      // optional int64 NetBasisChange = 2;
      case 2: {
        if (tag == 16) {
         parse_NetBasisChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &netbasischange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PercentageChange;
        break;
      }

      // optional int64 PercentageChange = 3;
      case 3: {
        if (tag == 24) {
         parse_PercentageChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &percentagechange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_DateBuy;
        break;
      }

      // optional string DateBuy = 4;
      case 4: {
        if (tag == 34) {
         parse_DateBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_datebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->datebuy().data(), this->datebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TimeBuy;
        break;
      }

      // optional string TimeBuy = 5;
      case 5: {
        if (tag == 42) {
         parse_TimeBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_timebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->timebuy().data(), this->timebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_DateSell;
        break;
      }

      // optional string DateSell = 6;
      case 6: {
        if (tag == 50) {
         parse_DateSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_datesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->datesell().data(), this->datesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TimeSell;
        break;
      }

      // optional string TimeSell = 7;
      case 7: {
        if (tag == 58) {
         parse_TimeSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_timesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->timesell().data(), this->timesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastRateBuy;
        break;
      }

      // optional int64 LastRateBuy = 8;
      case 8: {
        if (tag == 64) {
         parse_LastRateBuy:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastratebuy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_LastRateSell;
        break;
      }

      // optional int64 LastRateSell = 9;
      case 9: {
        if (tag == 72) {
         parse_LastRateSell:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastratesell_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LastAllinBuy;
        break;
      }

      // optional int64 LastAllinBuy = 10;
      case 10: {
        if (tag == 80) {
         parse_LastAllinBuy:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastallinbuy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LastAllinSell;
        break;
      }

      // optional int64 LastAllinSell = 11;
      case 11: {
        if (tag == 88) {
         parse_LastAllinSell:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastallinsell_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HistoryCloseRate;
        break;
      }

      // optional int64 HistoryCloseRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HistoryCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historycloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 16;
      case 16: {
        if (tag == 128) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AmountLevelRate;
        break;
      }

      // optional int32 AmountLevelRate = 17;
      case 17: {
        if (tag == 136) {
         parse_AmountLevelRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_AmountLevelAllin;
        break;
      }

      // optional int32 AmountLevelAllin = 18;
      case 18: {
        if (tag == 144) {
         parse_AmountLevelAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_RateSide;
        break;
      }

      // optional int32 RateSide = 19;
      case 19: {
        if (tag == 152) {
         parse_RateSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AllinSide;
        break;
      }

      // optional int32 AllinSide = 20;
      case 20: {
        if (tag == 160) {
         parse_AllinSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &allinside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_LegSign;
        break;
      }

      // optional string LegSign = 21;
      case 21: {
        if (tag == 170) {
         parse_LegSign:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_legsign()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->legsign().data(), this->legsign().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_FillSide;
        break;
      }

      // optional string FillSide = 22;
      case 22: {
        if (tag == 178) {
         parse_FillSide:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fillside()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fillside().data(), this->fillside().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_DateConfirmed;
        break;
      }

      // optional string DateConfirmed = 23;
      case 23: {
        if (tag == 186) {
         parse_DateConfirmed:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dateconfirmed()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dateconfirmed().data(), this->dateconfirmed().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_ContingencyWithdraw;
        break;
      }

      // optional bool ContingencyWithdraw = 24;
      case 24: {
        if (tag == 192) {
         parse_ContingencyWithdraw:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &contingencywithdraw_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_ContingencyTradeDate;
        break;
      }

      // optional string ContingencyTradeDate = 25;
      case 25: {
        if (tag == 202) {
         parse_ContingencyTradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_contingencytradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->contingencytradedate().data(), this->contingencytradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_ContingencyTradeTime;
        break;
      }

      // optional string ContingencyTradeTime = 26;
      case 26: {
        if (tag == 210) {
         parse_ContingencyTradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_contingencytradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->contingencytradetime().data(), this->contingencytradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_ContingencyLastPx;
        break;
      }

      // optional int64 ContingencyLastPx = 27;
      case 27: {
        if (tag == 216) {
         parse_ContingencyLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &contingencylastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  return false;
#undef DO_
}

void SwpSptNdfFowFxSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valuedate(), output);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->netbasischange(), output);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->percentagechange(), output);
  }

  // optional string DateBuy = 4;
  if (this->datebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datebuy().data(), this->datebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->datebuy(), output);
  }

  // optional string TimeBuy = 5;
  if (this->timebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timebuy().data(), this->timebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->timebuy(), output);
  }

  // optional string DateSell = 6;
  if (this->datesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datesell().data(), this->datesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->datesell(), output);
  }

  // optional string TimeSell = 7;
  if (this->timesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timesell().data(), this->timesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->timesell(), output);
  }

  // optional int64 LastRateBuy = 8;
  if (this->lastratebuy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastratebuy(), output);
  }

  // optional int64 LastRateSell = 9;
  if (this->lastratesell() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->lastratesell(), output);
  }

  // optional int64 LastAllinBuy = 10;
  if (this->lastallinbuy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastallinbuy(), output);
  }

  // optional int64 LastAllinSell = 11;
  if (this->lastallinsell() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lastallinsell(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openrate(), output);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->historycloserate(), output);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closerate(), output);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->amountlevelrate(), output);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->amountlevelallin(), output);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->rateside(), output);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->allinside(), output);
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->legsign(), output);
  }

  // optional string FillSide = 22;
  if (this->fillside().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fillside().data(), this->fillside().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->fillside(), output);
  }

  // optional string DateConfirmed = 23;
  if (this->dateconfirmed().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dateconfirmed().data(), this->dateconfirmed().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->dateconfirmed(), output);
  }

  // optional bool ContingencyWithdraw = 24;
  if (this->contingencywithdraw() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(24, this->contingencywithdraw(), output);
  }

  // optional string ContingencyTradeDate = 25;
  if (this->contingencytradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->contingencytradedate().data(), this->contingencytradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      25, this->contingencytradedate(), output);
  }

  // optional string ContingencyTradeTime = 26;
  if (this->contingencytradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->contingencytradetime().data(), this->contingencytradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      26, this->contingencytradetime(), output);
  }

  // optional int64 ContingencyLastPx = 27;
  if (this->contingencylastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->contingencylastpx(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
}

::google::protobuf::uint8* SwpSptNdfFowFxSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valuedate(), target);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->netbasischange(), target);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->percentagechange(), target);
  }

  // optional string DateBuy = 4;
  if (this->datebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datebuy().data(), this->datebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->datebuy(), target);
  }

  // optional string TimeBuy = 5;
  if (this->timebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timebuy().data(), this->timebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->timebuy(), target);
  }

  // optional string DateSell = 6;
  if (this->datesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->datesell().data(), this->datesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->datesell(), target);
  }

  // optional string TimeSell = 7;
  if (this->timesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->timesell().data(), this->timesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->timesell(), target);
  }

  // optional int64 LastRateBuy = 8;
  if (this->lastratebuy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastratebuy(), target);
  }

  // optional int64 LastRateSell = 9;
  if (this->lastratesell() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->lastratesell(), target);
  }

  // optional int64 LastAllinBuy = 10;
  if (this->lastallinbuy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastallinbuy(), target);
  }

  // optional int64 LastAllinSell = 11;
  if (this->lastallinsell() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lastallinsell(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openrate(), target);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->historycloserate(), target);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closerate(), target);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->amountlevelrate(), target);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->amountlevelallin(), target);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->rateside(), target);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->allinside(), target);
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->legsign(), target);
  }

  // optional string FillSide = 22;
  if (this->fillside().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fillside().data(), this->fillside().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->fillside(), target);
  }

  // optional string DateConfirmed = 23;
  if (this->dateconfirmed().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dateconfirmed().data(), this->dateconfirmed().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->dateconfirmed(), target);
  }

  // optional bool ContingencyWithdraw = 24;
  if (this->contingencywithdraw() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(24, this->contingencywithdraw(), target);
  }

  // optional string ContingencyTradeDate = 25;
  if (this->contingencytradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->contingencytradedate().data(), this->contingencytradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        25, this->contingencytradedate(), target);
  }

  // optional string ContingencyTradeTime = 26;
  if (this->contingencytradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->contingencytradetime().data(), this->contingencytradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        26, this->contingencytradetime(), target);
  }

  // optional int64 ContingencyLastPx = 27;
  if (this->contingencylastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->contingencylastpx(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  return target;
}

size_t SwpSptNdfFowFxSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  size_t total_size = 0;

  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuedate());
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->netbasischange());
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->percentagechange());
  }

  // optional string DateBuy = 4;
  if (this->datebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->datebuy());
  }

  // optional string TimeBuy = 5;
  if (this->timebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->timebuy());
  }

  // optional string DateSell = 6;
  if (this->datesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->datesell());
  }

  // optional string TimeSell = 7;
  if (this->timesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->timesell());
  }

  // optional int64 LastRateBuy = 8;
  if (this->lastratebuy() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastratebuy());
  }

  // optional int64 LastRateSell = 9;
  if (this->lastratesell() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastratesell());
  }

  // optional int64 LastAllinBuy = 10;
  if (this->lastallinbuy() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastallinbuy());
  }

  // optional int64 LastAllinSell = 11;
  if (this->lastallinsell() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastallinsell());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historycloserate());
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelrate());
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelallin());
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateside());
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->allinside());
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->legsign());
  }

  // optional string FillSide = 22;
  if (this->fillside().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fillside());
  }

  // optional string DateConfirmed = 23;
  if (this->dateconfirmed().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dateconfirmed());
  }

  // optional bool ContingencyWithdraw = 24;
  if (this->contingencywithdraw() != 0) {
    total_size += 2 + 1;
  }

  // optional string ContingencyTradeDate = 25;
  if (this->contingencytradedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->contingencytradedate());
  }

  // optional string ContingencyTradeTime = 26;
  if (this->contingencytradetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->contingencytradetime());
  }

  // optional int64 ContingencyLastPx = 27;
  if (this->contingencylastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->contingencylastpx());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SwpSptNdfFowFxSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SwpSptNdfFowFxSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SwpSptNdfFowFxSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void SwpSptNdfFowFxSnapshot::MergeFrom(const SwpSptNdfFowFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SwpSptNdfFowFxSnapshot::UnsafeMergeFrom(const SwpSptNdfFowFxSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.valuedate().size() > 0) {

    valuedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuedate_);
  }
  if (from.netbasischange() != 0) {
    set_netbasischange(from.netbasischange());
  }
  if (from.percentagechange() != 0) {
    set_percentagechange(from.percentagechange());
  }
  if (from.datebuy().size() > 0) {

    datebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.datebuy_);
  }
  if (from.timebuy().size() > 0) {

    timebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.timebuy_);
  }
  if (from.datesell().size() > 0) {

    datesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.datesell_);
  }
  if (from.timesell().size() > 0) {

    timesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.timesell_);
  }
  if (from.lastratebuy() != 0) {
    set_lastratebuy(from.lastratebuy());
  }
  if (from.lastratesell() != 0) {
    set_lastratesell(from.lastratesell());
  }
  if (from.lastallinbuy() != 0) {
    set_lastallinbuy(from.lastallinbuy());
  }
  if (from.lastallinsell() != 0) {
    set_lastallinsell(from.lastallinsell());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.historycloserate() != 0) {
    set_historycloserate(from.historycloserate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.amountlevelrate() != 0) {
    set_amountlevelrate(from.amountlevelrate());
  }
  if (from.amountlevelallin() != 0) {
    set_amountlevelallin(from.amountlevelallin());
  }
  if (from.rateside() != 0) {
    set_rateside(from.rateside());
  }
  if (from.allinside() != 0) {
    set_allinside(from.allinside());
  }
  if (from.legsign().size() > 0) {

    legsign_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.legsign_);
  }
  if (from.fillside().size() > 0) {

    fillside_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fillside_);
  }
  if (from.dateconfirmed().size() > 0) {

    dateconfirmed_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dateconfirmed_);
  }
  if (from.contingencywithdraw() != 0) {
    set_contingencywithdraw(from.contingencywithdraw());
  }
  if (from.contingencytradedate().size() > 0) {

    contingencytradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.contingencytradedate_);
  }
  if (from.contingencytradetime().size() > 0) {

    contingencytradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.contingencytradetime_);
  }
  if (from.contingencylastpx() != 0) {
    set_contingencylastpx(from.contingencylastpx());
  }
}

void SwpSptNdfFowFxSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SwpSptNdfFowFxSnapshot::CopyFrom(const SwpSptNdfFowFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SwpSptNdfFowFxSnapshot::IsInitialized() const {

  return true;
}

void SwpSptNdfFowFxSnapshot::Swap(SwpSptNdfFowFxSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SwpSptNdfFowFxSnapshot::InternalSwap(SwpSptNdfFowFxSnapshot* other) {
  valuedate_.Swap(&other->valuedate_);
  std::swap(netbasischange_, other->netbasischange_);
  std::swap(percentagechange_, other->percentagechange_);
  datebuy_.Swap(&other->datebuy_);
  timebuy_.Swap(&other->timebuy_);
  datesell_.Swap(&other->datesell_);
  timesell_.Swap(&other->timesell_);
  std::swap(lastratebuy_, other->lastratebuy_);
  std::swap(lastratesell_, other->lastratesell_);
  std::swap(lastallinbuy_, other->lastallinbuy_);
  std::swap(lastallinsell_, other->lastallinsell_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(historycloserate_, other->historycloserate_);
  std::swap(closerate_, other->closerate_);
  std::swap(amountlevelrate_, other->amountlevelrate_);
  std::swap(amountlevelallin_, other->amountlevelallin_);
  std::swap(rateside_, other->rateside_);
  std::swap(allinside_, other->allinside_);
  legsign_.Swap(&other->legsign_);
  fillside_.Swap(&other->fillside_);
  dateconfirmed_.Swap(&other->dateconfirmed_);
  std::swap(contingencywithdraw_, other->contingencywithdraw_);
  contingencytradedate_.Swap(&other->contingencytradedate_);
  contingencytradetime_.Swap(&other->contingencytradetime_);
  std::swap(contingencylastpx_, other->contingencylastpx_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SwpSptNdfFowFxSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SwpSptNdfFowFxSnapshot_descriptor_;
  metadata.reflection = SwpSptNdfFowFxSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SwpSptNdfFowFxSnapshot

// optional string ValueDate = 1;
void SwpSptNdfFowFxSnapshot::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
void SwpSptNdfFowFxSnapshot::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
void SwpSptNdfFowFxSnapshot::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}

// optional int64 NetBasisChange = 2;
void SwpSptNdfFowFxSnapshot::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.NetBasisChange)
  return netbasischange_;
}
void SwpSptNdfFowFxSnapshot::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.NetBasisChange)
}

// optional int64 PercentageChange = 3;
void SwpSptNdfFowFxSnapshot::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.PercentageChange)
  return percentagechange_;
}
void SwpSptNdfFowFxSnapshot::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.PercentageChange)
}

// optional string DateBuy = 4;
void SwpSptNdfFowFxSnapshot::clear_datebuy() {
  datebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::datebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  return datebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_datebuy(const ::std::string& value) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
void SwpSptNdfFowFxSnapshot::set_datebuy(const char* value) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
void SwpSptNdfFowFxSnapshot::set_datebuy(const char* value, size_t size) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_datebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  return datebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_datebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  
  return datebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_datebuy(::std::string* datebuy) {
  if (datebuy != NULL) {
    
  } else {
    
  }
  datebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}

// optional string TimeBuy = 5;
void SwpSptNdfFowFxSnapshot::clear_timebuy() {
  timebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::timebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  return timebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_timebuy(const ::std::string& value) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
void SwpSptNdfFowFxSnapshot::set_timebuy(const char* value) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
void SwpSptNdfFowFxSnapshot::set_timebuy(const char* value, size_t size) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_timebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  return timebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_timebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  
  return timebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_timebuy(::std::string* timebuy) {
  if (timebuy != NULL) {
    
  } else {
    
  }
  timebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), timebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}

// optional string DateSell = 6;
void SwpSptNdfFowFxSnapshot::clear_datesell() {
  datesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::datesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  return datesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_datesell(const ::std::string& value) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
void SwpSptNdfFowFxSnapshot::set_datesell(const char* value) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
void SwpSptNdfFowFxSnapshot::set_datesell(const char* value, size_t size) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_datesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  return datesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_datesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  
  return datesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_datesell(::std::string* datesell) {
  if (datesell != NULL) {
    
  } else {
    
  }
  datesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}

// optional string TimeSell = 7;
void SwpSptNdfFowFxSnapshot::clear_timesell() {
  timesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::timesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  return timesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_timesell(const ::std::string& value) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
void SwpSptNdfFowFxSnapshot::set_timesell(const char* value) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
void SwpSptNdfFowFxSnapshot::set_timesell(const char* value, size_t size) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_timesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  return timesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_timesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  
  return timesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_timesell(::std::string* timesell) {
  if (timesell != NULL) {
    
  } else {
    
  }
  timesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), timesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}

// optional int64 LastRateBuy = 8;
void SwpSptNdfFowFxSnapshot::clear_lastratebuy() {
  lastratebuy_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateBuy)
  return lastratebuy_;
}
void SwpSptNdfFowFxSnapshot::set_lastratebuy(::google::protobuf::int64 value) {
  
  lastratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateBuy)
}

// optional int64 LastRateSell = 9;
void SwpSptNdfFowFxSnapshot::clear_lastratesell() {
  lastratesell_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateSell)
  return lastratesell_;
}
void SwpSptNdfFowFxSnapshot::set_lastratesell(::google::protobuf::int64 value) {
  
  lastratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateSell)
}

// optional int64 LastAllinBuy = 10;
void SwpSptNdfFowFxSnapshot::clear_lastallinbuy() {
  lastallinbuy_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastallinbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinBuy)
  return lastallinbuy_;
}
void SwpSptNdfFowFxSnapshot::set_lastallinbuy(::google::protobuf::int64 value) {
  
  lastallinbuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinBuy)
}

// optional int64 LastAllinSell = 11;
void SwpSptNdfFowFxSnapshot::clear_lastallinsell() {
  lastallinsell_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastallinsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinSell)
  return lastallinsell_;
}
void SwpSptNdfFowFxSnapshot::set_lastallinsell(::google::protobuf::int64 value) {
  
  lastallinsell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinSell)
}

// optional int64 HighRate = 12;
void SwpSptNdfFowFxSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HighRate)
  return highrate_;
}
void SwpSptNdfFowFxSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HighRate)
}

// optional int64 LowRate = 13;
void SwpSptNdfFowFxSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LowRate)
  return lowrate_;
}
void SwpSptNdfFowFxSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LowRate)
}

// optional int64 OpenRate = 14;
void SwpSptNdfFowFxSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.OpenRate)
  return openrate_;
}
void SwpSptNdfFowFxSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
void SwpSptNdfFowFxSnapshot::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HistoryCloseRate)
  return historycloserate_;
}
void SwpSptNdfFowFxSnapshot::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
void SwpSptNdfFowFxSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.CloseRate)
  return closerate_;
}
void SwpSptNdfFowFxSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.CloseRate)
}

// optional int32 AmountLevelRate = 17;
void SwpSptNdfFowFxSnapshot::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
::google::protobuf::int32 SwpSptNdfFowFxSnapshot::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelRate)
  return amountlevelrate_;
}
void SwpSptNdfFowFxSnapshot::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
void SwpSptNdfFowFxSnapshot::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
::google::protobuf::int32 SwpSptNdfFowFxSnapshot::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelAllin)
  return amountlevelallin_;
}
void SwpSptNdfFowFxSnapshot::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelAllin)
}

// optional int32 RateSide = 19;
void SwpSptNdfFowFxSnapshot::clear_rateside() {
  rateside_ = 0;
}
::google::protobuf::int32 SwpSptNdfFowFxSnapshot::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.RateSide)
  return rateside_;
}
void SwpSptNdfFowFxSnapshot::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.RateSide)
}

// optional int32 AllinSide = 20;
void SwpSptNdfFowFxSnapshot::clear_allinside() {
  allinside_ = 0;
}
::google::protobuf::int32 SwpSptNdfFowFxSnapshot::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AllinSide)
  return allinside_;
}
void SwpSptNdfFowFxSnapshot::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AllinSide)
}

// optional string LegSign = 21;
void SwpSptNdfFowFxSnapshot::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
void SwpSptNdfFowFxSnapshot::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
void SwpSptNdfFowFxSnapshot::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}

// optional string FillSide = 22;
void SwpSptNdfFowFxSnapshot::clear_fillside() {
  fillside_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::fillside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  return fillside_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_fillside(const ::std::string& value) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
void SwpSptNdfFowFxSnapshot::set_fillside(const char* value) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
void SwpSptNdfFowFxSnapshot::set_fillside(const char* value, size_t size) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_fillside() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  return fillside_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_fillside() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  
  return fillside_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_fillside(::std::string* fillside) {
  if (fillside != NULL) {
    
  } else {
    
  }
  fillside_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fillside);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}

// optional string DateConfirmed = 23;
void SwpSptNdfFowFxSnapshot::clear_dateconfirmed() {
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::dateconfirmed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  return dateconfirmed_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const ::std::string& value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const char* value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const char* value, size_t size) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_dateconfirmed() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  return dateconfirmed_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_dateconfirmed() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  
  return dateconfirmed_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_dateconfirmed(::std::string* dateconfirmed) {
  if (dateconfirmed != NULL) {
    
  } else {
    
  }
  dateconfirmed_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dateconfirmed);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}

// optional bool ContingencyWithdraw = 24;
void SwpSptNdfFowFxSnapshot::clear_contingencywithdraw() {
  contingencywithdraw_ = false;
}
bool SwpSptNdfFowFxSnapshot::contingencywithdraw() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyWithdraw)
  return contingencywithdraw_;
}
void SwpSptNdfFowFxSnapshot::set_contingencywithdraw(bool value) {
  
  contingencywithdraw_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyWithdraw)
}

// optional string ContingencyTradeDate = 25;
void SwpSptNdfFowFxSnapshot::clear_contingencytradedate() {
  contingencytradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::contingencytradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  return contingencytradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const ::std::string& value) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const char* value) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const char* value, size_t size) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_contingencytradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  return contingencytradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_contingencytradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  
  return contingencytradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_contingencytradedate(::std::string* contingencytradedate) {
  if (contingencytradedate != NULL) {
    
  } else {
    
  }
  contingencytradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), contingencytradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}

// optional string ContingencyTradeTime = 26;
void SwpSptNdfFowFxSnapshot::clear_contingencytradetime() {
  contingencytradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxSnapshot::contingencytradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  return contingencytradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const ::std::string& value) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const char* value) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const char* value, size_t size) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
::std::string* SwpSptNdfFowFxSnapshot::mutable_contingencytradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  return contingencytradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxSnapshot::release_contingencytradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  
  return contingencytradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxSnapshot::set_allocated_contingencytradetime(::std::string* contingencytradetime) {
  if (contingencytradetime != NULL) {
    
  } else {
    
  }
  contingencytradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), contingencytradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}

// optional int64 ContingencyLastPx = 27;
void SwpSptNdfFowFxSnapshot::clear_contingencylastpx() {
  contingencylastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxSnapshot::contingencylastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyLastPx)
  return contingencylastpx_;
}
void SwpSptNdfFowFxSnapshot::set_contingencylastpx(::google::protobuf::int64 value) {
  
  contingencylastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyLastPx)
}

inline const SwpSptNdfFowFxSnapshot* SwpSptNdfFowFxSnapshot::internal_default_instance() {
  return &SwpSptNdfFowFxSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptionFxSnapshot::kFxTermFieldNumber;
const int OptionFxSnapshot::kPremiumFieldNumber;
const int OptionFxSnapshot::kVolatilityFieldNumber;
const int OptionFxSnapshot::kVolumeFieldNumber;
const int OptionFxSnapshot::kTradeDateFieldNumber;
const int OptionFxSnapshot::kTradeTimeFieldNumber;
const int OptionFxSnapshot::kPremiumTypeFieldNumber;
const int OptionFxSnapshot::kOptionTypeFieldNumber;
const int OptionFxSnapshot::kDateConfirmedFieldNumber;
const int OptionFxSnapshot::kOptionTypeEnumFieldNumber;
const int OptionFxSnapshot::kDerivativeExerciseStyleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptionFxSnapshot::OptionFxSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.OptionFxSnapshot)
}

void OptionFxSnapshot::InitAsDefaultInstance() {
}

OptionFxSnapshot::OptionFxSnapshot(const OptionFxSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.OptionFxSnapshot)
}

void OptionFxSnapshot::SharedCtor() {
  fxterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  derivativeexercisestyle_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&premium_, 0, reinterpret_cast<char*>(&optiontypeenum_) -
    reinterpret_cast<char*>(&premium_) + sizeof(optiontypeenum_));
  _cached_size_ = 0;
}

OptionFxSnapshot::~OptionFxSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.OptionFxSnapshot)
  SharedDtor();
}

void OptionFxSnapshot::SharedDtor() {
  fxterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  derivativeexercisestyle_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OptionFxSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OptionFxSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OptionFxSnapshot_descriptor_;
}

const OptionFxSnapshot& OptionFxSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OptionFxSnapshot> OptionFxSnapshot_default_instance_;

OptionFxSnapshot* OptionFxSnapshot::New(::google::protobuf::Arena* arena) const {
  OptionFxSnapshot* n = new OptionFxSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OptionFxSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(OptionFxSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<OptionFxSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(premium_, premiumtype_);
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontypeenum_ = 0;
  derivativeexercisestyle_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool OptionFxSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string FxTerm = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fxterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fxterm().data(), this->fxterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Premium;
        break;
      }

      // optional int64 Premium = 2;
      case 2: {
        if (tag == 16) {
         parse_Premium:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premium_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Volatility;
        break;
      }

      // optional int64 Volatility = 3;
      case 3: {
        if (tag == 24) {
         parse_Volatility:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volatility_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Volume;
        break;
      }

      // optional int64 Volume = 4;
      case 4: {
        if (tag == 32) {
         parse_Volume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 5;
      case 5: {
        if (tag == 42) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 6;
      case 6: {
        if (tag == 50) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_PremiumType;
        break;
      }

      // optional int32 PremiumType = 7;
      case 7: {
        if (tag == 56) {
         parse_PremiumType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &premiumtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_OptionType;
        break;
      }

      // optional string OptionType = 8;
      case 8: {
        if (tag == 66) {
         parse_OptionType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optiontype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optiontype().data(), this->optiontype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_DateConfirmed;
        break;
      }

      // optional string DateConfirmed = 9;
      case 9: {
        if (tag == 74) {
         parse_DateConfirmed:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dateconfirmed()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dateconfirmed().data(), this->dateconfirmed().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_OptionTypeEnum;
        break;
      }

      // optional int32 OptionTypeEnum = 10;
      case 10: {
        if (tag == 80) {
         parse_OptionTypeEnum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &optiontypeenum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_DerivativeExerciseStyle;
        break;
      }

      // optional string DerivativeExerciseStyle = 11;
      case 11: {
        if (tag == 90) {
         parse_DerivativeExerciseStyle:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_derivativeexercisestyle()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->derivativeexercisestyle().data(), this->derivativeexercisestyle().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.OptionFxSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.OptionFxSnapshot)
  return false;
#undef DO_
}

void OptionFxSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fxterm().data(), this->fxterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->fxterm(), output);
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->premium(), output);
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->volatility(), output);
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->volume(), output);
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradedate(), output);
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->tradetime(), output);
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->premiumtype(), output);
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiontype().data(), this->optiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->optiontype(), output);
  }

  // optional string DateConfirmed = 9;
  if (this->dateconfirmed().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dateconfirmed().data(), this->dateconfirmed().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->dateconfirmed(), output);
  }

  // optional int32 OptionTypeEnum = 10;
  if (this->optiontypeenum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->optiontypeenum(), output);
  }

  // optional string DerivativeExerciseStyle = 11;
  if (this->derivativeexercisestyle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->derivativeexercisestyle().data(), this->derivativeexercisestyle().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->derivativeexercisestyle(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.OptionFxSnapshot)
}

::google::protobuf::uint8* OptionFxSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fxterm().data(), this->fxterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->fxterm(), target);
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->premium(), target);
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->volatility(), target);
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->volume(), target);
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradedate(), target);
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->tradetime(), target);
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->premiumtype(), target);
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiontype().data(), this->optiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->optiontype(), target);
  }

  // optional string DateConfirmed = 9;
  if (this->dateconfirmed().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dateconfirmed().data(), this->dateconfirmed().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->dateconfirmed(), target);
  }

  // optional int32 OptionTypeEnum = 10;
  if (this->optiontypeenum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->optiontypeenum(), target);
  }

  // optional string DerivativeExerciseStyle = 11;
  if (this->derivativeexercisestyle().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->derivativeexercisestyle().data(), this->derivativeexercisestyle().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->derivativeexercisestyle(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.OptionFxSnapshot)
  return target;
}

size_t OptionFxSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  size_t total_size = 0;

  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fxterm());
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premium());
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volatility());
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volume());
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->premiumtype());
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optiontype());
  }

  // optional string DateConfirmed = 9;
  if (this->dateconfirmed().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dateconfirmed());
  }

  // optional int32 OptionTypeEnum = 10;
  if (this->optiontypeenum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->optiontypeenum());
  }

  // optional string DerivativeExerciseStyle = 11;
  if (this->derivativeexercisestyle().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->derivativeexercisestyle());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OptionFxSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OptionFxSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptionFxSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.OptionFxSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.OptionFxSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void OptionFxSnapshot::MergeFrom(const OptionFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OptionFxSnapshot::UnsafeMergeFrom(const OptionFxSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.fxterm().size() > 0) {

    fxterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fxterm_);
  }
  if (from.premium() != 0) {
    set_premium(from.premium());
  }
  if (from.volatility() != 0) {
    set_volatility(from.volatility());
  }
  if (from.volume() != 0) {
    set_volume(from.volume());
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.premiumtype() != 0) {
    set_premiumtype(from.premiumtype());
  }
  if (from.optiontype().size() > 0) {

    optiontype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optiontype_);
  }
  if (from.dateconfirmed().size() > 0) {

    dateconfirmed_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dateconfirmed_);
  }
  if (from.optiontypeenum() != 0) {
    set_optiontypeenum(from.optiontypeenum());
  }
  if (from.derivativeexercisestyle().size() > 0) {

    derivativeexercisestyle_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.derivativeexercisestyle_);
  }
}

void OptionFxSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptionFxSnapshot::CopyFrom(const OptionFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.OptionFxSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OptionFxSnapshot::IsInitialized() const {

  return true;
}

void OptionFxSnapshot::Swap(OptionFxSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OptionFxSnapshot::InternalSwap(OptionFxSnapshot* other) {
  fxterm_.Swap(&other->fxterm_);
  std::swap(premium_, other->premium_);
  std::swap(volatility_, other->volatility_);
  std::swap(volume_, other->volume_);
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(premiumtype_, other->premiumtype_);
  optiontype_.Swap(&other->optiontype_);
  dateconfirmed_.Swap(&other->dateconfirmed_);
  std::swap(optiontypeenum_, other->optiontypeenum_);
  derivativeexercisestyle_.Swap(&other->derivativeexercisestyle_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OptionFxSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OptionFxSnapshot_descriptor_;
  metadata.reflection = OptionFxSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OptionFxSnapshot

// optional string FxTerm = 1;
void OptionFxSnapshot::clear_fxterm() {
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::fxterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  return fxterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_fxterm(const ::std::string& value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
void OptionFxSnapshot::set_fxterm(const char* value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
void OptionFxSnapshot::set_fxterm(const char* value, size_t size) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
::std::string* OptionFxSnapshot::mutable_fxterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  return fxterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_fxterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  
  return fxterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_fxterm(::std::string* fxterm) {
  if (fxterm != NULL) {
    
  } else {
    
  }
  fxterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fxterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}

// optional int64 Premium = 2;
void OptionFxSnapshot::clear_premium() {
  premium_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionFxSnapshot::premium() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Premium)
  return premium_;
}
void OptionFxSnapshot::set_premium(::google::protobuf::int64 value) {
  
  premium_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Premium)
}

// optional int64 Volatility = 3;
void OptionFxSnapshot::clear_volatility() {
  volatility_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionFxSnapshot::volatility() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Volatility)
  return volatility_;
}
void OptionFxSnapshot::set_volatility(::google::protobuf::int64 value) {
  
  volatility_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Volatility)
}

// optional int64 Volume = 4;
void OptionFxSnapshot::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionFxSnapshot::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Volume)
  return volume_;
}
void OptionFxSnapshot::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Volume)
}

// optional string TradeDate = 5;
void OptionFxSnapshot::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
void OptionFxSnapshot::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
void OptionFxSnapshot::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
::std::string* OptionFxSnapshot::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}

// optional string TradeTime = 6;
void OptionFxSnapshot::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
void OptionFxSnapshot::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
void OptionFxSnapshot::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
::std::string* OptionFxSnapshot::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}

// optional int32 PremiumType = 7;
void OptionFxSnapshot::clear_premiumtype() {
  premiumtype_ = 0;
}
::google::protobuf::int32 OptionFxSnapshot::premiumtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.PremiumType)
  return premiumtype_;
}
void OptionFxSnapshot::set_premiumtype(::google::protobuf::int32 value) {
  
  premiumtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.PremiumType)
}

// optional string OptionType = 8;
void OptionFxSnapshot::clear_optiontype() {
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::optiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  return optiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_optiontype(const ::std::string& value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
void OptionFxSnapshot::set_optiontype(const char* value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
void OptionFxSnapshot::set_optiontype(const char* value, size_t size) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
::std::string* OptionFxSnapshot::mutable_optiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  return optiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_optiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  
  return optiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_optiontype(::std::string* optiontype) {
  if (optiontype != NULL) {
    
  } else {
    
  }
  optiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}

// optional string DateConfirmed = 9;
void OptionFxSnapshot::clear_dateconfirmed() {
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::dateconfirmed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  return dateconfirmed_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_dateconfirmed(const ::std::string& value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
void OptionFxSnapshot::set_dateconfirmed(const char* value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
void OptionFxSnapshot::set_dateconfirmed(const char* value, size_t size) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
::std::string* OptionFxSnapshot::mutable_dateconfirmed() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  return dateconfirmed_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_dateconfirmed() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  
  return dateconfirmed_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_dateconfirmed(::std::string* dateconfirmed) {
  if (dateconfirmed != NULL) {
    
  } else {
    
  }
  dateconfirmed_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dateconfirmed);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}

// optional int32 OptionTypeEnum = 10;
void OptionFxSnapshot::clear_optiontypeenum() {
  optiontypeenum_ = 0;
}
::google::protobuf::int32 OptionFxSnapshot::optiontypeenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionTypeEnum)
  return optiontypeenum_;
}
void OptionFxSnapshot::set_optiontypeenum(::google::protobuf::int32 value) {
  
  optiontypeenum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionTypeEnum)
}

// optional string DerivativeExerciseStyle = 11;
void OptionFxSnapshot::clear_derivativeexercisestyle() {
  derivativeexercisestyle_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxSnapshot::derivativeexercisestyle() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  return derivativeexercisestyle_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_derivativeexercisestyle(const ::std::string& value) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
void OptionFxSnapshot::set_derivativeexercisestyle(const char* value) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
void OptionFxSnapshot::set_derivativeexercisestyle(const char* value, size_t size) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
::std::string* OptionFxSnapshot::mutable_derivativeexercisestyle() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  return derivativeexercisestyle_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxSnapshot::release_derivativeexercisestyle() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  
  return derivativeexercisestyle_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxSnapshot::set_allocated_derivativeexercisestyle(::std::string* derivativeexercisestyle) {
  if (derivativeexercisestyle != NULL) {
    
  } else {
    
  }
  derivativeexercisestyle_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), derivativeexercisestyle);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}

inline const OptionFxSnapshot* OptionFxSnapshot::internal_default_instance() {
  return &OptionFxSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SpotClosePriceFxSnapshot::kClosePxFieldNumber;
const int SpotClosePriceFxSnapshot::kUpdateDateFieldNumber;
const int SpotClosePriceFxSnapshot::kUpdateTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SpotClosePriceFxSnapshot::SpotClosePriceFxSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
}

void SpotClosePriceFxSnapshot::InitAsDefaultInstance() {
}

SpotClosePriceFxSnapshot::SpotClosePriceFxSnapshot(const SpotClosePriceFxSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
}

void SpotClosePriceFxSnapshot::SharedCtor() {
  updatedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  closepx_ = GOOGLE_LONGLONG(0);
  _cached_size_ = 0;
}

SpotClosePriceFxSnapshot::~SpotClosePriceFxSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  SharedDtor();
}

void SpotClosePriceFxSnapshot::SharedDtor() {
  updatedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SpotClosePriceFxSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SpotClosePriceFxSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SpotClosePriceFxSnapshot_descriptor_;
}

const SpotClosePriceFxSnapshot& SpotClosePriceFxSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SpotClosePriceFxSnapshot> SpotClosePriceFxSnapshot_default_instance_;

SpotClosePriceFxSnapshot* SpotClosePriceFxSnapshot::New(::google::protobuf::Arena* arena) const {
  SpotClosePriceFxSnapshot* n = new SpotClosePriceFxSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SpotClosePriceFxSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  closepx_ = GOOGLE_LONGLONG(0);
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool SpotClosePriceFxSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 ClosePx = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_UpdateDate;
        break;
      }

      // optional string UpdateDate = 2;
      case 2: {
        if (tag == 18) {
         parse_UpdateDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_updatedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->updatedate().data(), this->updatedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_UpdateTime;
        break;
      }

      // optional string UpdateTime = 3;
      case 3: {
        if (tag == 26) {
         parse_UpdateTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_updatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->updatetime().data(), this->updatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  return false;
#undef DO_
}

void SpotClosePriceFxSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->closepx(), output);
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatedate().data(), this->updatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->updatedate(), output);
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatetime().data(), this->updatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->updatetime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
}

::google::protobuf::uint8* SpotClosePriceFxSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->closepx(), target);
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatedate().data(), this->updatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->updatedate(), target);
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatetime().data(), this->updatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->updatetime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  return target;
}

size_t SpotClosePriceFxSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  size_t total_size = 0;

  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->updatedate());
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->updatetime());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SpotClosePriceFxSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SpotClosePriceFxSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SpotClosePriceFxSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void SpotClosePriceFxSnapshot::MergeFrom(const SpotClosePriceFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SpotClosePriceFxSnapshot::UnsafeMergeFrom(const SpotClosePriceFxSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.updatedate().size() > 0) {

    updatedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.updatedate_);
  }
  if (from.updatetime().size() > 0) {

    updatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.updatetime_);
  }
}

void SpotClosePriceFxSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SpotClosePriceFxSnapshot::CopyFrom(const SpotClosePriceFxSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SpotClosePriceFxSnapshot::IsInitialized() const {

  return true;
}

void SpotClosePriceFxSnapshot::Swap(SpotClosePriceFxSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SpotClosePriceFxSnapshot::InternalSwap(SpotClosePriceFxSnapshot* other) {
  std::swap(closepx_, other->closepx_);
  updatedate_.Swap(&other->updatedate_);
  updatetime_.Swap(&other->updatetime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SpotClosePriceFxSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SpotClosePriceFxSnapshot_descriptor_;
  metadata.reflection = SpotClosePriceFxSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SpotClosePriceFxSnapshot

// optional int64 ClosePx = 1;
void SpotClosePriceFxSnapshot::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotClosePriceFxSnapshot::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.ClosePx)
  return closepx_;
}
void SpotClosePriceFxSnapshot::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.ClosePx)
}

// optional string UpdateDate = 2;
void SpotClosePriceFxSnapshot::clear_updatedate() {
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotClosePriceFxSnapshot::updatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  return updatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceFxSnapshot::set_updatedate(const ::std::string& value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
void SpotClosePriceFxSnapshot::set_updatedate(const char* value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
void SpotClosePriceFxSnapshot::set_updatedate(const char* value, size_t size) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
::std::string* SpotClosePriceFxSnapshot::mutable_updatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  return updatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotClosePriceFxSnapshot::release_updatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  
  return updatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceFxSnapshot::set_allocated_updatedate(::std::string* updatedate) {
  if (updatedate != NULL) {
    
  } else {
    
  }
  updatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}

// optional string UpdateTime = 3;
void SpotClosePriceFxSnapshot::clear_updatetime() {
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotClosePriceFxSnapshot::updatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  return updatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceFxSnapshot::set_updatetime(const ::std::string& value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
void SpotClosePriceFxSnapshot::set_updatetime(const char* value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
void SpotClosePriceFxSnapshot::set_updatetime(const char* value, size_t size) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
::std::string* SpotClosePriceFxSnapshot::mutable_updatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  return updatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotClosePriceFxSnapshot::release_updatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  
  return updatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceFxSnapshot::set_allocated_updatetime(::std::string* updatetime) {
  if (updatetime != NULL) {
    
  } else {
    
  }
  updatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}

inline const SpotClosePriceFxSnapshot* SpotClosePriceFxSnapshot::internal_default_instance() {
  return &SpotClosePriceFxSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
