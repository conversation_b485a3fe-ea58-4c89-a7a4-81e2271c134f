// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsCurrencySnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsCurrencySnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsCurrencySnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsCurrencySnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* InterBankOfferingSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  InterBankOfferingSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* CollateralRepoSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  CollateralRepoSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* OutrightRepoSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OutrightRepoSnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto() {
  protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsCurrencySnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsCurrencySnapshot_descriptor_ = file->message_type(0);
  static const int MDCfetsCurrencySnapshot_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, currencysnapshottype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, interbankofferingsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, collateralreposnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, outrightreposnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, datamultiplepowerof10_),
  };
  MDCfetsCurrencySnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsCurrencySnapshot_descriptor_,
      MDCfetsCurrencySnapshot::internal_default_instance(),
      MDCfetsCurrencySnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsCurrencySnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsCurrencySnapshot, _internal_metadata_));
  InterBankOfferingSnapshot_descriptor_ = file->message_type(1);
  static const int InterBankOfferingSnapshot_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, precloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, preweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, weightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, averageterm_),
  };
  InterBankOfferingSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      InterBankOfferingSnapshot_descriptor_,
      InterBankOfferingSnapshot::internal_default_instance(),
      InterBankOfferingSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(InterBankOfferingSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(InterBankOfferingSnapshot, _internal_metadata_));
  CollateralRepoSnapshot_descriptor_ = file->message_type(2);
  static const int CollateralRepoSnapshot_offsets_[14] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, precloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, preweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, weightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, averageterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, irbondweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, repomethod_),
  };
  CollateralRepoSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      CollateralRepoSnapshot_descriptor_,
      CollateralRepoSnapshot::internal_default_instance(),
      CollateralRepoSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(CollateralRepoSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(CollateralRepoSnapshot, _internal_metadata_));
  OutrightRepoSnapshot_descriptor_ = file->message_type(3);
  static const int OutrightRepoSnapshot_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, precloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, preweightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, weightedrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, totalvaluetrade_),
  };
  OutrightRepoSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OutrightRepoSnapshot_descriptor_,
      OutrightRepoSnapshot::internal_default_instance(),
      OutrightRepoSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(OutrightRepoSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OutrightRepoSnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsCurrencySnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsCurrencySnapshot_descriptor_, MDCfetsCurrencySnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      InterBankOfferingSnapshot_descriptor_, InterBankOfferingSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      CollateralRepoSnapshot_descriptor_, CollateralRepoSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OutrightRepoSnapshot_descriptor_, OutrightRepoSnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto() {
  MDCfetsCurrencySnapshot_default_instance_.Shutdown();
  delete MDCfetsCurrencySnapshot_reflection_;
  InterBankOfferingSnapshot_default_instance_.Shutdown();
  delete InterBankOfferingSnapshot_reflection_;
  CollateralRepoSnapshot_default_instance_.Shutdown();
  delete CollateralRepoSnapshot_reflection_;
  OutrightRepoSnapshot_default_instance_.Shutdown();
  delete OutrightRepoSnapshot_reflection_;
}

void protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsCurrencySnapshot_default_instance_.DefaultConstruct();
  InterBankOfferingSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  CollateralRepoSnapshot_default_instance_.DefaultConstruct();
  OutrightRepoSnapshot_default_instance_.DefaultConstruct();
  MDCfetsCurrencySnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  InterBankOfferingSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  CollateralRepoSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  OutrightRepoSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_once_);
void protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\035MDCfetsCurrencySnapshot.proto\022\032com.hts"
    "c.mdc.insight.model\032\027ESecurityIDSource.p"
    "roto\032\023ESecurityType.proto\"\314\004\n\027MDCfetsCur"
    "rencySnapshot\022\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n"
    "\014SecurityType\030\002 \001(\0162!.com.htsc.mdc.model"
    ".ESecurityType\022\?\n\020SecurityIDSource\030\003 \001(\016"
    "2%.com.htsc.mdc.model.ESecurityIDSource\022"
    "\016\n\006MDDate\030\004 \001(\005\022\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataT"
    "imestamp\030\006 \001(\003\022\024\n\014TransactTime\030\007 \001(\t\022\027\n\017"
    "MarketIndicator\030\010 \001(\t\022\034\n\024CurrencySnapsho"
    "tType\030\t \001(\005\022X\n\031interBankOfferingSnapshot"
    "\030\n \001(\01325.com.htsc.mdc.insight.model.Inte"
    "rBankOfferingSnapshot\022R\n\026collateralRepoS"
    "napshot\030\013 \001(\01322.com.htsc.mdc.insight.mod"
    "el.CollateralRepoSnapshot\022N\n\024outrightRep"
    "oSnapshot\030\014 \001(\01320.com.htsc.mdc.insight.m"
    "odel.OutrightRepoSnapshot\022\035\n\025DataMultipl"
    "ePowerOf10\030\r \001(\005\"\373\001\n\031InterBankOfferingSn"
    "apshot\022\024\n\014PreCloseRate\030\001 \001(\003\022\027\n\017PreWeigh"
    "tedRate\030\002 \001(\003\022\020\n\010OpenRate\030\003 \001(\003\022\020\n\010LastR"
    "ate\030\004 \001(\003\022\020\n\010HighRate\030\005 \001(\003\022\017\n\007LowRate\030\006"
    " \001(\003\022\021\n\tCloseRate\030\007 \001(\003\022\024\n\014WeightedRate\030"
    "\010 \001(\003\022\027\n\017TotalValueTrade\030\t \001(\003\022\021\n\tNumTra"
    "des\030\n \001(\005\022\023\n\013AverageTerm\030\013 \001(\003\"\275\002\n\026Colla"
    "teralRepoSnapshot\022\023\n\013TradeMethod\030\001 \001(\005\022\024"
    "\n\014PreCloseRate\030\002 \001(\003\022\027\n\017PreWeightedRate\030"
    "\003 \001(\003\022\020\n\010OpenRate\030\004 \001(\003\022\020\n\010LastRate\030\005 \001("
    "\003\022\020\n\010HighRate\030\006 \001(\003\022\017\n\007LowRate\030\007 \001(\003\022\021\n\t"
    "CloseRate\030\010 \001(\003\022\024\n\014WeightedRate\030\t \001(\003\022\027\n"
    "\017TotalValueTrade\030\n \001(\003\022\021\n\tNumTrades\030\013 \001("
    "\005\022\023\n\013AverageTerm\030\014 \001(\003\022\032\n\022IRBondWeighted"
    "Rate\030\r \001(\003\022\022\n\nRepoMethod\030\016 \001(\t\"\316\001\n\024Outri"
    "ghtRepoSnapshot\022\024\n\014PreCloseRate\030\001 \001(\003\022\027\n"
    "\017PreWeightedRate\030\002 \001(\003\022\020\n\010OpenRate\030\003 \001(\003"
    "\022\020\n\010LastRate\030\004 \001(\003\022\020\n\010HighRate\030\005 \001(\003\022\017\n\007"
    "LowRate\030\006 \001(\003\022\021\n\tCloseRate\030\007 \001(\003\022\024\n\014Weig"
    "htedRate\030\010 \001(\003\022\027\n\017TotalValueTrade\030\t \001(\003B"
    "@\n\032com.htsc.mdc.insight.modelB\035MDCfetsCu"
    "rrencySnapshotProtosH\001\240\001\001b\006proto3", 1553);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsCurrencySnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsCurrencySnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_once_);
void protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsCurrencySnapshot_2eproto {
  StaticDescriptorInitializer_MDCfetsCurrencySnapshot_2eproto() {
    protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto();
  }
} static_descriptor_initializer_MDCfetsCurrencySnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsCurrencySnapshot::kHTSCSecurityIDFieldNumber;
const int MDCfetsCurrencySnapshot::kSecurityTypeFieldNumber;
const int MDCfetsCurrencySnapshot::kSecurityIDSourceFieldNumber;
const int MDCfetsCurrencySnapshot::kMDDateFieldNumber;
const int MDCfetsCurrencySnapshot::kMDTimeFieldNumber;
const int MDCfetsCurrencySnapshot::kDataTimestampFieldNumber;
const int MDCfetsCurrencySnapshot::kTransactTimeFieldNumber;
const int MDCfetsCurrencySnapshot::kMarketIndicatorFieldNumber;
const int MDCfetsCurrencySnapshot::kCurrencySnapshotTypeFieldNumber;
const int MDCfetsCurrencySnapshot::kInterBankOfferingSnapshotFieldNumber;
const int MDCfetsCurrencySnapshot::kCollateralRepoSnapshotFieldNumber;
const int MDCfetsCurrencySnapshot::kOutrightRepoSnapshotFieldNumber;
const int MDCfetsCurrencySnapshot::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsCurrencySnapshot::MDCfetsCurrencySnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
}

void MDCfetsCurrencySnapshot::InitAsDefaultInstance() {
  interbankofferingsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot*>(
      ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot::internal_default_instance());
  collateralreposnapshot_ = const_cast< ::com::htsc::mdc::insight::model::CollateralRepoSnapshot*>(
      ::com::htsc::mdc::insight::model::CollateralRepoSnapshot::internal_default_instance());
  outrightreposnapshot_ = const_cast< ::com::htsc::mdc::insight::model::OutrightRepoSnapshot*>(
      ::com::htsc::mdc::insight::model::OutrightRepoSnapshot::internal_default_instance());
}

MDCfetsCurrencySnapshot::MDCfetsCurrencySnapshot(const MDCfetsCurrencySnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
}

void MDCfetsCurrencySnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  interbankofferingsnapshot_ = NULL;
  collateralreposnapshot_ = NULL;
  outrightreposnapshot_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCfetsCurrencySnapshot::~MDCfetsCurrencySnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  SharedDtor();
}

void MDCfetsCurrencySnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsCurrencySnapshot_default_instance_.get()) {
    delete interbankofferingsnapshot_;
    delete collateralreposnapshot_;
    delete outrightreposnapshot_;
  }
}

void MDCfetsCurrencySnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsCurrencySnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsCurrencySnapshot_descriptor_;
}

const MDCfetsCurrencySnapshot& MDCfetsCurrencySnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsCurrencySnapshot> MDCfetsCurrencySnapshot_default_instance_;

MDCfetsCurrencySnapshot* MDCfetsCurrencySnapshot::New(::google::protobuf::Arena* arena) const {
  MDCfetsCurrencySnapshot* n = new MDCfetsCurrencySnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsCurrencySnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsCurrencySnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsCurrencySnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(currencysnapshottype_, datamultiplepowerof10_);
  if (GetArenaNoVirtual() == NULL && interbankofferingsnapshot_ != NULL) delete interbankofferingsnapshot_;
  interbankofferingsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && collateralreposnapshot_ != NULL) delete collateralreposnapshot_;
  collateralreposnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && outrightreposnapshot_ != NULL) delete outrightreposnapshot_;
  outrightreposnapshot_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsCurrencySnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_CurrencySnapshotType;
        break;
      }

      // optional int32 CurrencySnapshotType = 9;
      case 9: {
        if (tag == 72) {
         parse_CurrencySnapshotType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &currencysnapshottype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_interBankOfferingSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
      case 10: {
        if (tag == 82) {
         parse_interBankOfferingSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_interbankofferingsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_collateralRepoSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
      case 11: {
        if (tag == 90) {
         parse_collateralRepoSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_collateralreposnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_outrightRepoSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
      case 12: {
        if (tag == 98) {
         parse_outrightRepoSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_outrightreposnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 13;
      case 13: {
        if (tag == 104) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  return false;
#undef DO_
}

void MDCfetsCurrencySnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 CurrencySnapshotType = 9;
  if (this->currencysnapshottype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->currencysnapshottype(), output);
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
  if (this->has_interbankofferingsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->interbankofferingsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
  if (this->has_collateralreposnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->collateralreposnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
  if (this->has_outrightreposnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->outrightreposnapshot_, output);
  }

  // optional int32 DataMultiplePowerOf10 = 13;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
}

::google::protobuf::uint8* MDCfetsCurrencySnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 CurrencySnapshotType = 9;
  if (this->currencysnapshottype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->currencysnapshottype(), target);
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
  if (this->has_interbankofferingsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->interbankofferingsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
  if (this->has_collateralreposnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->collateralreposnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
  if (this->has_outrightreposnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->outrightreposnapshot_, false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 13;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  return target;
}

size_t MDCfetsCurrencySnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 CurrencySnapshotType = 9;
  if (this->currencysnapshottype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->currencysnapshottype());
  }

  // optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
  if (this->has_interbankofferingsnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->interbankofferingsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
  if (this->has_collateralreposnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->collateralreposnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
  if (this->has_outrightreposnapshot()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->outrightreposnapshot_);
  }

  // optional int32 DataMultiplePowerOf10 = 13;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsCurrencySnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsCurrencySnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsCurrencySnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsCurrencySnapshot::MergeFrom(const MDCfetsCurrencySnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsCurrencySnapshot::UnsafeMergeFrom(const MDCfetsCurrencySnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.currencysnapshottype() != 0) {
    set_currencysnapshottype(from.currencysnapshottype());
  }
  if (from.has_interbankofferingsnapshot()) {
    mutable_interbankofferingsnapshot()->::com::htsc::mdc::insight::model::InterBankOfferingSnapshot::MergeFrom(from.interbankofferingsnapshot());
  }
  if (from.has_collateralreposnapshot()) {
    mutable_collateralreposnapshot()->::com::htsc::mdc::insight::model::CollateralRepoSnapshot::MergeFrom(from.collateralreposnapshot());
  }
  if (from.has_outrightreposnapshot()) {
    mutable_outrightreposnapshot()->::com::htsc::mdc::insight::model::OutrightRepoSnapshot::MergeFrom(from.outrightreposnapshot());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDCfetsCurrencySnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsCurrencySnapshot::CopyFrom(const MDCfetsCurrencySnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsCurrencySnapshot::IsInitialized() const {

  return true;
}

void MDCfetsCurrencySnapshot::Swap(MDCfetsCurrencySnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsCurrencySnapshot::InternalSwap(MDCfetsCurrencySnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(currencysnapshottype_, other->currencysnapshottype_);
  std::swap(interbankofferingsnapshot_, other->interbankofferingsnapshot_);
  std::swap(collateralreposnapshot_, other->collateralreposnapshot_);
  std::swap(outrightreposnapshot_, other->outrightreposnapshot_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsCurrencySnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsCurrencySnapshot_descriptor_;
  metadata.reflection = MDCfetsCurrencySnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsCurrencySnapshot

// optional string HTSCSecurityID = 1;
void MDCfetsCurrencySnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencySnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
void MDCfetsCurrencySnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
void MDCfetsCurrencySnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}
::std::string* MDCfetsCurrencySnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencySnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsCurrencySnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsCurrencySnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsCurrencySnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsCurrencySnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsCurrencySnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsCurrencySnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsCurrencySnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencySnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDDate)
  return mddate_;
}
void MDCfetsCurrencySnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsCurrencySnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencySnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDTime)
  return mdtime_;
}
void MDCfetsCurrencySnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsCurrencySnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsCurrencySnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsCurrencySnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsCurrencySnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencySnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
void MDCfetsCurrencySnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
void MDCfetsCurrencySnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}
::std::string* MDCfetsCurrencySnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencySnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsCurrencySnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsCurrencySnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
void MDCfetsCurrencySnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
void MDCfetsCurrencySnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}
::std::string* MDCfetsCurrencySnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsCurrencySnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsCurrencySnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.MarketIndicator)
}

// optional int32 CurrencySnapshotType = 9;
void MDCfetsCurrencySnapshot::clear_currencysnapshottype() {
  currencysnapshottype_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencySnapshot::currencysnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.CurrencySnapshotType)
  return currencysnapshottype_;
}
void MDCfetsCurrencySnapshot::set_currencysnapshottype(::google::protobuf::int32 value) {
  
  currencysnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.CurrencySnapshotType)
}

// optional .com.htsc.mdc.insight.model.InterBankOfferingSnapshot interBankOfferingSnapshot = 10;
bool MDCfetsCurrencySnapshot::has_interbankofferingsnapshot() const {
  return this != internal_default_instance() && interbankofferingsnapshot_ != NULL;
}
void MDCfetsCurrencySnapshot::clear_interbankofferingsnapshot() {
  if (GetArenaNoVirtual() == NULL && interbankofferingsnapshot_ != NULL) delete interbankofferingsnapshot_;
  interbankofferingsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot& MDCfetsCurrencySnapshot::interbankofferingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  return interbankofferingsnapshot_ != NULL ? *interbankofferingsnapshot_
                         : *::com::htsc::mdc::insight::model::InterBankOfferingSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* MDCfetsCurrencySnapshot::mutable_interbankofferingsnapshot() {
  
  if (interbankofferingsnapshot_ == NULL) {
    interbankofferingsnapshot_ = new ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  return interbankofferingsnapshot_;
}
::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* MDCfetsCurrencySnapshot::release_interbankofferingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
  
  ::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* temp = interbankofferingsnapshot_;
  interbankofferingsnapshot_ = NULL;
  return temp;
}
void MDCfetsCurrencySnapshot::set_allocated_interbankofferingsnapshot(::com::htsc::mdc::insight::model::InterBankOfferingSnapshot* interbankofferingsnapshot) {
  delete interbankofferingsnapshot_;
  interbankofferingsnapshot_ = interbankofferingsnapshot;
  if (interbankofferingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.interBankOfferingSnapshot)
}

// optional .com.htsc.mdc.insight.model.CollateralRepoSnapshot collateralRepoSnapshot = 11;
bool MDCfetsCurrencySnapshot::has_collateralreposnapshot() const {
  return this != internal_default_instance() && collateralreposnapshot_ != NULL;
}
void MDCfetsCurrencySnapshot::clear_collateralreposnapshot() {
  if (GetArenaNoVirtual() == NULL && collateralreposnapshot_ != NULL) delete collateralreposnapshot_;
  collateralreposnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::CollateralRepoSnapshot& MDCfetsCurrencySnapshot::collateralreposnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  return collateralreposnapshot_ != NULL ? *collateralreposnapshot_
                         : *::com::htsc::mdc::insight::model::CollateralRepoSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::CollateralRepoSnapshot* MDCfetsCurrencySnapshot::mutable_collateralreposnapshot() {
  
  if (collateralreposnapshot_ == NULL) {
    collateralreposnapshot_ = new ::com::htsc::mdc::insight::model::CollateralRepoSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  return collateralreposnapshot_;
}
::com::htsc::mdc::insight::model::CollateralRepoSnapshot* MDCfetsCurrencySnapshot::release_collateralreposnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
  
  ::com::htsc::mdc::insight::model::CollateralRepoSnapshot* temp = collateralreposnapshot_;
  collateralreposnapshot_ = NULL;
  return temp;
}
void MDCfetsCurrencySnapshot::set_allocated_collateralreposnapshot(::com::htsc::mdc::insight::model::CollateralRepoSnapshot* collateralreposnapshot) {
  delete collateralreposnapshot_;
  collateralreposnapshot_ = collateralreposnapshot;
  if (collateralreposnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.collateralRepoSnapshot)
}

// optional .com.htsc.mdc.insight.model.OutrightRepoSnapshot outrightRepoSnapshot = 12;
bool MDCfetsCurrencySnapshot::has_outrightreposnapshot() const {
  return this != internal_default_instance() && outrightreposnapshot_ != NULL;
}
void MDCfetsCurrencySnapshot::clear_outrightreposnapshot() {
  if (GetArenaNoVirtual() == NULL && outrightreposnapshot_ != NULL) delete outrightreposnapshot_;
  outrightreposnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::OutrightRepoSnapshot& MDCfetsCurrencySnapshot::outrightreposnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  return outrightreposnapshot_ != NULL ? *outrightreposnapshot_
                         : *::com::htsc::mdc::insight::model::OutrightRepoSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::OutrightRepoSnapshot* MDCfetsCurrencySnapshot::mutable_outrightreposnapshot() {
  
  if (outrightreposnapshot_ == NULL) {
    outrightreposnapshot_ = new ::com::htsc::mdc::insight::model::OutrightRepoSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  return outrightreposnapshot_;
}
::com::htsc::mdc::insight::model::OutrightRepoSnapshot* MDCfetsCurrencySnapshot::release_outrightreposnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
  
  ::com::htsc::mdc::insight::model::OutrightRepoSnapshot* temp = outrightreposnapshot_;
  outrightreposnapshot_ = NULL;
  return temp;
}
void MDCfetsCurrencySnapshot::set_allocated_outrightreposnapshot(::com::htsc::mdc::insight::model::OutrightRepoSnapshot* outrightreposnapshot) {
  delete outrightreposnapshot_;
  outrightreposnapshot_ = outrightreposnapshot;
  if (outrightreposnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.outrightRepoSnapshot)
}

// optional int32 DataMultiplePowerOf10 = 13;
void MDCfetsCurrencySnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsCurrencySnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsCurrencySnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot.DataMultiplePowerOf10)
}

inline const MDCfetsCurrencySnapshot* MDCfetsCurrencySnapshot::internal_default_instance() {
  return &MDCfetsCurrencySnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InterBankOfferingSnapshot::kPreCloseRateFieldNumber;
const int InterBankOfferingSnapshot::kPreWeightedRateFieldNumber;
const int InterBankOfferingSnapshot::kOpenRateFieldNumber;
const int InterBankOfferingSnapshot::kLastRateFieldNumber;
const int InterBankOfferingSnapshot::kHighRateFieldNumber;
const int InterBankOfferingSnapshot::kLowRateFieldNumber;
const int InterBankOfferingSnapshot::kCloseRateFieldNumber;
const int InterBankOfferingSnapshot::kWeightedRateFieldNumber;
const int InterBankOfferingSnapshot::kTotalValueTradeFieldNumber;
const int InterBankOfferingSnapshot::kNumTradesFieldNumber;
const int InterBankOfferingSnapshot::kAverageTermFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InterBankOfferingSnapshot::InterBankOfferingSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
}

void InterBankOfferingSnapshot::InitAsDefaultInstance() {
}

InterBankOfferingSnapshot::InterBankOfferingSnapshot(const InterBankOfferingSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
}

void InterBankOfferingSnapshot::SharedCtor() {
  ::memset(&precloserate_, 0, reinterpret_cast<char*>(&numtrades_) -
    reinterpret_cast<char*>(&precloserate_) + sizeof(numtrades_));
  _cached_size_ = 0;
}

InterBankOfferingSnapshot::~InterBankOfferingSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  SharedDtor();
}

void InterBankOfferingSnapshot::SharedDtor() {
}

void InterBankOfferingSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* InterBankOfferingSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return InterBankOfferingSnapshot_descriptor_;
}

const InterBankOfferingSnapshot& InterBankOfferingSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<InterBankOfferingSnapshot> InterBankOfferingSnapshot_default_instance_;

InterBankOfferingSnapshot* InterBankOfferingSnapshot::New(::google::protobuf::Arena* arena) const {
  InterBankOfferingSnapshot* n = new InterBankOfferingSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void InterBankOfferingSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(InterBankOfferingSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<InterBankOfferingSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(precloserate_, weightedrate_);
  ZR_(totalvaluetrade_, numtrades_);

#undef ZR_HELPER_
#undef ZR_

}

bool InterBankOfferingSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 PreCloseRate = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_PreWeightedRate;
        break;
      }

      // optional int64 PreWeightedRate = 2;
      case 2: {
        if (tag == 16) {
         parse_PreWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 3;
      case 3: {
        if (tag == 24) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_LastRate;
        break;
      }

      // optional int64 LastRate = 4;
      case 4: {
        if (tag == 32) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 5;
      case 5: {
        if (tag == 40) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 6;
      case 6: {
        if (tag == 48) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 7;
      case 7: {
        if (tag == 56) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_WeightedRate;
        break;
      }

      // optional int64 WeightedRate = 8;
      case 8: {
        if (tag == 64) {
         parse_WeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 9;
      case 9: {
        if (tag == 72) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_NumTrades;
        break;
      }

      // optional int32 NumTrades = 10;
      case 10: {
        if (tag == 80) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_AverageTerm;
        break;
      }

      // optional int64 AverageTerm = 11;
      case 11: {
        if (tag == 88) {
         parse_AverageTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &averageterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  return false;
#undef DO_
}

void InterBankOfferingSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->precloserate(), output);
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->preweightedrate(), output);
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->openrate(), output);
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->lastrate(), output);
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->highrate(), output);
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->lowrate(), output);
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->closerate(), output);
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->weightedrate(), output);
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->totalvaluetrade(), output);
  }

  // optional int32 NumTrades = 10;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->numtrades(), output);
  }

  // optional int64 AverageTerm = 11;
  if (this->averageterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->averageterm(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
}

::google::protobuf::uint8* InterBankOfferingSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->precloserate(), target);
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->preweightedrate(), target);
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->openrate(), target);
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->lastrate(), target);
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->highrate(), target);
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->lowrate(), target);
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->closerate(), target);
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->weightedrate(), target);
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->totalvaluetrade(), target);
  }

  // optional int32 NumTrades = 10;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->numtrades(), target);
  }

  // optional int64 AverageTerm = 11;
  if (this->averageterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->averageterm(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  return target;
}

size_t InterBankOfferingSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  size_t total_size = 0;

  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloserate());
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedrate());
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastrate());
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedrate());
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int32 NumTrades = 10;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numtrades());
  }

  // optional int64 AverageTerm = 11;
  if (this->averageterm() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->averageterm());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void InterBankOfferingSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const InterBankOfferingSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InterBankOfferingSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void InterBankOfferingSnapshot::MergeFrom(const InterBankOfferingSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void InterBankOfferingSnapshot::UnsafeMergeFrom(const InterBankOfferingSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.precloserate() != 0) {
    set_precloserate(from.precloserate());
  }
  if (from.preweightedrate() != 0) {
    set_preweightedrate(from.preweightedrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.weightedrate() != 0) {
    set_weightedrate(from.weightedrate());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.averageterm() != 0) {
    set_averageterm(from.averageterm());
  }
}

void InterBankOfferingSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InterBankOfferingSnapshot::CopyFrom(const InterBankOfferingSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.InterBankOfferingSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool InterBankOfferingSnapshot::IsInitialized() const {

  return true;
}

void InterBankOfferingSnapshot::Swap(InterBankOfferingSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void InterBankOfferingSnapshot::InternalSwap(InterBankOfferingSnapshot* other) {
  std::swap(precloserate_, other->precloserate_);
  std::swap(preweightedrate_, other->preweightedrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(closerate_, other->closerate_);
  std::swap(weightedrate_, other->weightedrate_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(averageterm_, other->averageterm_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata InterBankOfferingSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = InterBankOfferingSnapshot_descriptor_;
  metadata.reflection = InterBankOfferingSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// InterBankOfferingSnapshot

// optional int64 PreCloseRate = 1;
void InterBankOfferingSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreCloseRate)
  return precloserate_;
}
void InterBankOfferingSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 2;
void InterBankOfferingSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreWeightedRate)
  return preweightedrate_;
}
void InterBankOfferingSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 3;
void InterBankOfferingSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.OpenRate)
  return openrate_;
}
void InterBankOfferingSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.OpenRate)
}

// optional int64 LastRate = 4;
void InterBankOfferingSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LastRate)
  return lastrate_;
}
void InterBankOfferingSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LastRate)
}

// optional int64 HighRate = 5;
void InterBankOfferingSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.HighRate)
  return highrate_;
}
void InterBankOfferingSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.HighRate)
}

// optional int64 LowRate = 6;
void InterBankOfferingSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LowRate)
  return lowrate_;
}
void InterBankOfferingSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.LowRate)
}

// optional int64 CloseRate = 7;
void InterBankOfferingSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.CloseRate)
  return closerate_;
}
void InterBankOfferingSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.CloseRate)
}

// optional int64 WeightedRate = 8;
void InterBankOfferingSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.WeightedRate)
  return weightedrate_;
}
void InterBankOfferingSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 9;
void InterBankOfferingSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
void InterBankOfferingSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.TotalValueTrade)
}

// optional int32 NumTrades = 10;
void InterBankOfferingSnapshot::clear_numtrades() {
  numtrades_ = 0;
}
::google::protobuf::int32 InterBankOfferingSnapshot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.NumTrades)
  return numtrades_;
}
void InterBankOfferingSnapshot::set_numtrades(::google::protobuf::int32 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.NumTrades)
}

// optional int64 AverageTerm = 11;
void InterBankOfferingSnapshot::clear_averageterm() {
  averageterm_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 InterBankOfferingSnapshot::averageterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.AverageTerm)
  return averageterm_;
}
void InterBankOfferingSnapshot::set_averageterm(::google::protobuf::int64 value) {
  
  averageterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingSnapshot.AverageTerm)
}

inline const InterBankOfferingSnapshot* InterBankOfferingSnapshot::internal_default_instance() {
  return &InterBankOfferingSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CollateralRepoSnapshot::kTradeMethodFieldNumber;
const int CollateralRepoSnapshot::kPreCloseRateFieldNumber;
const int CollateralRepoSnapshot::kPreWeightedRateFieldNumber;
const int CollateralRepoSnapshot::kOpenRateFieldNumber;
const int CollateralRepoSnapshot::kLastRateFieldNumber;
const int CollateralRepoSnapshot::kHighRateFieldNumber;
const int CollateralRepoSnapshot::kLowRateFieldNumber;
const int CollateralRepoSnapshot::kCloseRateFieldNumber;
const int CollateralRepoSnapshot::kWeightedRateFieldNumber;
const int CollateralRepoSnapshot::kTotalValueTradeFieldNumber;
const int CollateralRepoSnapshot::kNumTradesFieldNumber;
const int CollateralRepoSnapshot::kAverageTermFieldNumber;
const int CollateralRepoSnapshot::kIRBondWeightedRateFieldNumber;
const int CollateralRepoSnapshot::kRepoMethodFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CollateralRepoSnapshot::CollateralRepoSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
}

void CollateralRepoSnapshot::InitAsDefaultInstance() {
}

CollateralRepoSnapshot::CollateralRepoSnapshot(const CollateralRepoSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
}

void CollateralRepoSnapshot::SharedCtor() {
  repomethod_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&precloserate_, 0, reinterpret_cast<char*>(&irbondweightedrate_) -
    reinterpret_cast<char*>(&precloserate_) + sizeof(irbondweightedrate_));
  _cached_size_ = 0;
}

CollateralRepoSnapshot::~CollateralRepoSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  SharedDtor();
}

void CollateralRepoSnapshot::SharedDtor() {
  repomethod_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void CollateralRepoSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CollateralRepoSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return CollateralRepoSnapshot_descriptor_;
}

const CollateralRepoSnapshot& CollateralRepoSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<CollateralRepoSnapshot> CollateralRepoSnapshot_default_instance_;

CollateralRepoSnapshot* CollateralRepoSnapshot::New(::google::protobuf::Arena* arena) const {
  CollateralRepoSnapshot* n = new CollateralRepoSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CollateralRepoSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(CollateralRepoSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<CollateralRepoSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(precloserate_, trademethod_);
  ZR_(lowrate_, closerate_);
  ZR_(weightedrate_, irbondweightedrate_);
  numtrades_ = 0;
  repomethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool CollateralRepoSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeMethod = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_PreCloseRate;
        break;
      }

      // optional int64 PreCloseRate = 2;
      case 2: {
        if (tag == 16) {
         parse_PreCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PreWeightedRate;
        break;
      }

      // optional int64 PreWeightedRate = 3;
      case 3: {
        if (tag == 24) {
         parse_PreWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 4;
      case 4: {
        if (tag == 32) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_LastRate;
        break;
      }

      // optional int64 LastRate = 5;
      case 5: {
        if (tag == 40) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 6;
      case 6: {
        if (tag == 48) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 7;
      case 7: {
        if (tag == 56) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 8;
      case 8: {
        if (tag == 64) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_WeightedRate;
        break;
      }

      // optional int64 WeightedRate = 9;
      case 9: {
        if (tag == 72) {
         parse_WeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 10;
      case 10: {
        if (tag == 80) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int32 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_AverageTerm;
        break;
      }

      // optional int64 AverageTerm = 12;
      case 12: {
        if (tag == 96) {
         parse_AverageTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &averageterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_IRBondWeightedRate;
        break;
      }

      // optional int64 IRBondWeightedRate = 13;
      case 13: {
        if (tag == 104) {
         parse_IRBondWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &irbondweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_RepoMethod;
        break;
      }

      // optional string RepoMethod = 14;
      case 14: {
        if (tag == 114) {
         parse_RepoMethod:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_repomethod()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->repomethod().data(), this->repomethod().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  return false;
#undef DO_
}

void CollateralRepoSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->trademethod(), output);
  }

  // optional int64 PreCloseRate = 2;
  if (this->precloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->precloserate(), output);
  }

  // optional int64 PreWeightedRate = 3;
  if (this->preweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->preweightedrate(), output);
  }

  // optional int64 OpenRate = 4;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->openrate(), output);
  }

  // optional int64 LastRate = 5;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->lastrate(), output);
  }

  // optional int64 HighRate = 6;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->highrate(), output);
  }

  // optional int64 LowRate = 7;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->lowrate(), output);
  }

  // optional int64 CloseRate = 8;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->closerate(), output);
  }

  // optional int64 WeightedRate = 9;
  if (this->weightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->weightedrate(), output);
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->totalvaluetrade(), output);
  }

  // optional int32 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->numtrades(), output);
  }

  // optional int64 AverageTerm = 12;
  if (this->averageterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->averageterm(), output);
  }

  // optional int64 IRBondWeightedRate = 13;
  if (this->irbondweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->irbondweightedrate(), output);
  }

  // optional string RepoMethod = 14;
  if (this->repomethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repomethod().data(), this->repomethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->repomethod(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
}

::google::protobuf::uint8* CollateralRepoSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->trademethod(), target);
  }

  // optional int64 PreCloseRate = 2;
  if (this->precloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->precloserate(), target);
  }

  // optional int64 PreWeightedRate = 3;
  if (this->preweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->preweightedrate(), target);
  }

  // optional int64 OpenRate = 4;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->openrate(), target);
  }

  // optional int64 LastRate = 5;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->lastrate(), target);
  }

  // optional int64 HighRate = 6;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->highrate(), target);
  }

  // optional int64 LowRate = 7;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->lowrate(), target);
  }

  // optional int64 CloseRate = 8;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->closerate(), target);
  }

  // optional int64 WeightedRate = 9;
  if (this->weightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->weightedrate(), target);
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->totalvaluetrade(), target);
  }

  // optional int32 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->numtrades(), target);
  }

  // optional int64 AverageTerm = 12;
  if (this->averageterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->averageterm(), target);
  }

  // optional int64 IRBondWeightedRate = 13;
  if (this->irbondweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->irbondweightedrate(), target);
  }

  // optional string RepoMethod = 14;
  if (this->repomethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->repomethod().data(), this->repomethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->repomethod(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  return target;
}

size_t CollateralRepoSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  size_t total_size = 0;

  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional int64 PreCloseRate = 2;
  if (this->precloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloserate());
  }

  // optional int64 PreWeightedRate = 3;
  if (this->preweightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedrate());
  }

  // optional int64 OpenRate = 4;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 LastRate = 5;
  if (this->lastrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastrate());
  }

  // optional int64 HighRate = 6;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 7;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 CloseRate = 8;
  if (this->closerate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int64 WeightedRate = 9;
  if (this->weightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedrate());
  }

  // optional int64 TotalValueTrade = 10;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int32 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numtrades());
  }

  // optional int64 AverageTerm = 12;
  if (this->averageterm() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->averageterm());
  }

  // optional int64 IRBondWeightedRate = 13;
  if (this->irbondweightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->irbondweightedrate());
  }

  // optional string RepoMethod = 14;
  if (this->repomethod().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->repomethod());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CollateralRepoSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const CollateralRepoSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CollateralRepoSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void CollateralRepoSnapshot::MergeFrom(const CollateralRepoSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void CollateralRepoSnapshot::UnsafeMergeFrom(const CollateralRepoSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.precloserate() != 0) {
    set_precloserate(from.precloserate());
  }
  if (from.preweightedrate() != 0) {
    set_preweightedrate(from.preweightedrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.weightedrate() != 0) {
    set_weightedrate(from.weightedrate());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.averageterm() != 0) {
    set_averageterm(from.averageterm());
  }
  if (from.irbondweightedrate() != 0) {
    set_irbondweightedrate(from.irbondweightedrate());
  }
  if (from.repomethod().size() > 0) {

    repomethod_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.repomethod_);
  }
}

void CollateralRepoSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CollateralRepoSnapshot::CopyFrom(const CollateralRepoSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.CollateralRepoSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool CollateralRepoSnapshot::IsInitialized() const {

  return true;
}

void CollateralRepoSnapshot::Swap(CollateralRepoSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CollateralRepoSnapshot::InternalSwap(CollateralRepoSnapshot* other) {
  std::swap(trademethod_, other->trademethod_);
  std::swap(precloserate_, other->precloserate_);
  std::swap(preweightedrate_, other->preweightedrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(closerate_, other->closerate_);
  std::swap(weightedrate_, other->weightedrate_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(averageterm_, other->averageterm_);
  std::swap(irbondweightedrate_, other->irbondweightedrate_);
  repomethod_.Swap(&other->repomethod_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CollateralRepoSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = CollateralRepoSnapshot_descriptor_;
  metadata.reflection = CollateralRepoSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// CollateralRepoSnapshot

// optional int32 TradeMethod = 1;
void CollateralRepoSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 CollateralRepoSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TradeMethod)
  return trademethod_;
}
void CollateralRepoSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TradeMethod)
}

// optional int64 PreCloseRate = 2;
void CollateralRepoSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreCloseRate)
  return precloserate_;
}
void CollateralRepoSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 3;
void CollateralRepoSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreWeightedRate)
  return preweightedrate_;
}
void CollateralRepoSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 4;
void CollateralRepoSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.OpenRate)
  return openrate_;
}
void CollateralRepoSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.OpenRate)
}

// optional int64 LastRate = 5;
void CollateralRepoSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LastRate)
  return lastrate_;
}
void CollateralRepoSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LastRate)
}

// optional int64 HighRate = 6;
void CollateralRepoSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.HighRate)
  return highrate_;
}
void CollateralRepoSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.HighRate)
}

// optional int64 LowRate = 7;
void CollateralRepoSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LowRate)
  return lowrate_;
}
void CollateralRepoSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.LowRate)
}

// optional int64 CloseRate = 8;
void CollateralRepoSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.CloseRate)
  return closerate_;
}
void CollateralRepoSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.CloseRate)
}

// optional int64 WeightedRate = 9;
void CollateralRepoSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.WeightedRate)
  return weightedrate_;
}
void CollateralRepoSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 10;
void CollateralRepoSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
void CollateralRepoSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.TotalValueTrade)
}

// optional int32 NumTrades = 11;
void CollateralRepoSnapshot::clear_numtrades() {
  numtrades_ = 0;
}
::google::protobuf::int32 CollateralRepoSnapshot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.NumTrades)
  return numtrades_;
}
void CollateralRepoSnapshot::set_numtrades(::google::protobuf::int32 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.NumTrades)
}

// optional int64 AverageTerm = 12;
void CollateralRepoSnapshot::clear_averageterm() {
  averageterm_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::averageterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.AverageTerm)
  return averageterm_;
}
void CollateralRepoSnapshot::set_averageterm(::google::protobuf::int64 value) {
  
  averageterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.AverageTerm)
}

// optional int64 IRBondWeightedRate = 13;
void CollateralRepoSnapshot::clear_irbondweightedrate() {
  irbondweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 CollateralRepoSnapshot::irbondweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.IRBondWeightedRate)
  return irbondweightedrate_;
}
void CollateralRepoSnapshot::set_irbondweightedrate(::google::protobuf::int64 value) {
  
  irbondweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.IRBondWeightedRate)
}

// optional string RepoMethod = 14;
void CollateralRepoSnapshot::clear_repomethod() {
  repomethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& CollateralRepoSnapshot::repomethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  return repomethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoSnapshot::set_repomethod(const ::std::string& value) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
void CollateralRepoSnapshot::set_repomethod(const char* value) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
void CollateralRepoSnapshot::set_repomethod(const char* value, size_t size) {
  
  repomethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}
::std::string* CollateralRepoSnapshot::mutable_repomethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  return repomethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* CollateralRepoSnapshot::release_repomethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
  
  return repomethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void CollateralRepoSnapshot::set_allocated_repomethod(::std::string* repomethod) {
  if (repomethod != NULL) {
    
  } else {
    
  }
  repomethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), repomethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoSnapshot.RepoMethod)
}

inline const CollateralRepoSnapshot* CollateralRepoSnapshot::internal_default_instance() {
  return &CollateralRepoSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OutrightRepoSnapshot::kPreCloseRateFieldNumber;
const int OutrightRepoSnapshot::kPreWeightedRateFieldNumber;
const int OutrightRepoSnapshot::kOpenRateFieldNumber;
const int OutrightRepoSnapshot::kLastRateFieldNumber;
const int OutrightRepoSnapshot::kHighRateFieldNumber;
const int OutrightRepoSnapshot::kLowRateFieldNumber;
const int OutrightRepoSnapshot::kCloseRateFieldNumber;
const int OutrightRepoSnapshot::kWeightedRateFieldNumber;
const int OutrightRepoSnapshot::kTotalValueTradeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OutrightRepoSnapshot::OutrightRepoSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
}

void OutrightRepoSnapshot::InitAsDefaultInstance() {
}

OutrightRepoSnapshot::OutrightRepoSnapshot(const OutrightRepoSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
}

void OutrightRepoSnapshot::SharedCtor() {
  ::memset(&precloserate_, 0, reinterpret_cast<char*>(&totalvaluetrade_) -
    reinterpret_cast<char*>(&precloserate_) + sizeof(totalvaluetrade_));
  _cached_size_ = 0;
}

OutrightRepoSnapshot::~OutrightRepoSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  SharedDtor();
}

void OutrightRepoSnapshot::SharedDtor() {
}

void OutrightRepoSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OutrightRepoSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OutrightRepoSnapshot_descriptor_;
}

const OutrightRepoSnapshot& OutrightRepoSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OutrightRepoSnapshot> OutrightRepoSnapshot_default_instance_;

OutrightRepoSnapshot* OutrightRepoSnapshot::New(::google::protobuf::Arena* arena) const {
  OutrightRepoSnapshot* n = new OutrightRepoSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OutrightRepoSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(OutrightRepoSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<OutrightRepoSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(precloserate_, weightedrate_);
  totalvaluetrade_ = GOOGLE_LONGLONG(0);

#undef ZR_HELPER_
#undef ZR_

}

bool OutrightRepoSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 PreCloseRate = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_PreWeightedRate;
        break;
      }

      // optional int64 PreWeightedRate = 2;
      case 2: {
        if (tag == 16) {
         parse_PreWeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preweightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 3;
      case 3: {
        if (tag == 24) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_LastRate;
        break;
      }

      // optional int64 LastRate = 4;
      case 4: {
        if (tag == 32) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 5;
      case 5: {
        if (tag == 40) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 6;
      case 6: {
        if (tag == 48) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 7;
      case 7: {
        if (tag == 56) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_WeightedRate;
        break;
      }

      // optional int64 WeightedRate = 8;
      case 8: {
        if (tag == 64) {
         parse_WeightedRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 9;
      case 9: {
        if (tag == 72) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  return false;
#undef DO_
}

void OutrightRepoSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->precloserate(), output);
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->preweightedrate(), output);
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->openrate(), output);
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->lastrate(), output);
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->highrate(), output);
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->lowrate(), output);
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->closerate(), output);
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->weightedrate(), output);
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->totalvaluetrade(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
}

::google::protobuf::uint8* OutrightRepoSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->precloserate(), target);
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->preweightedrate(), target);
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->openrate(), target);
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->lastrate(), target);
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->highrate(), target);
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->lowrate(), target);
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->closerate(), target);
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->weightedrate(), target);
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->totalvaluetrade(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  return target;
}

size_t OutrightRepoSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  size_t total_size = 0;

  // optional int64 PreCloseRate = 1;
  if (this->precloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloserate());
  }

  // optional int64 PreWeightedRate = 2;
  if (this->preweightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preweightedrate());
  }

  // optional int64 OpenRate = 3;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 LastRate = 4;
  if (this->lastrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastrate());
  }

  // optional int64 HighRate = 5;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 6;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 CloseRate = 7;
  if (this->closerate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int64 WeightedRate = 8;
  if (this->weightedrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedrate());
  }

  // optional int64 TotalValueTrade = 9;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OutrightRepoSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OutrightRepoSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OutrightRepoSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void OutrightRepoSnapshot::MergeFrom(const OutrightRepoSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OutrightRepoSnapshot::UnsafeMergeFrom(const OutrightRepoSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.precloserate() != 0) {
    set_precloserate(from.precloserate());
  }
  if (from.preweightedrate() != 0) {
    set_preweightedrate(from.preweightedrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.weightedrate() != 0) {
    set_weightedrate(from.weightedrate());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
}

void OutrightRepoSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OutrightRepoSnapshot::CopyFrom(const OutrightRepoSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.OutrightRepoSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OutrightRepoSnapshot::IsInitialized() const {

  return true;
}

void OutrightRepoSnapshot::Swap(OutrightRepoSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OutrightRepoSnapshot::InternalSwap(OutrightRepoSnapshot* other) {
  std::swap(precloserate_, other->precloserate_);
  std::swap(preweightedrate_, other->preweightedrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(closerate_, other->closerate_);
  std::swap(weightedrate_, other->weightedrate_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OutrightRepoSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OutrightRepoSnapshot_descriptor_;
  metadata.reflection = OutrightRepoSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OutrightRepoSnapshot

// optional int64 PreCloseRate = 1;
void OutrightRepoSnapshot::clear_precloserate() {
  precloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreCloseRate)
  return precloserate_;
}
void OutrightRepoSnapshot::set_precloserate(::google::protobuf::int64 value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreCloseRate)
}

// optional int64 PreWeightedRate = 2;
void OutrightRepoSnapshot::clear_preweightedrate() {
  preweightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::preweightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreWeightedRate)
  return preweightedrate_;
}
void OutrightRepoSnapshot::set_preweightedrate(::google::protobuf::int64 value) {
  
  preweightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.PreWeightedRate)
}

// optional int64 OpenRate = 3;
void OutrightRepoSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.OpenRate)
  return openrate_;
}
void OutrightRepoSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.OpenRate)
}

// optional int64 LastRate = 4;
void OutrightRepoSnapshot::clear_lastrate() {
  lastrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LastRate)
  return lastrate_;
}
void OutrightRepoSnapshot::set_lastrate(::google::protobuf::int64 value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LastRate)
}

// optional int64 HighRate = 5;
void OutrightRepoSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.HighRate)
  return highrate_;
}
void OutrightRepoSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.HighRate)
}

// optional int64 LowRate = 6;
void OutrightRepoSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LowRate)
  return lowrate_;
}
void OutrightRepoSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.LowRate)
}

// optional int64 CloseRate = 7;
void OutrightRepoSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.CloseRate)
  return closerate_;
}
void OutrightRepoSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.CloseRate)
}

// optional int64 WeightedRate = 8;
void OutrightRepoSnapshot::clear_weightedrate() {
  weightedrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::weightedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.WeightedRate)
  return weightedrate_;
}
void OutrightRepoSnapshot::set_weightedrate(::google::protobuf::int64 value) {
  
  weightedrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.WeightedRate)
}

// optional int64 TotalValueTrade = 9;
void OutrightRepoSnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OutrightRepoSnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoSnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
void OutrightRepoSnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoSnapshot.TotalValueTrade)
}

inline const OutrightRepoSnapshot* OutrightRepoSnapshot::internal_default_instance() {
  return &OutrightRepoSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
