// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDETFBasicInfo.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDETFBasicInfo.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDETFBasicInfo_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDETFBasicInfo_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDETFComponentDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDETFComponentDetail_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* MDETFSubstituteFlag_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDETFBasicInfo_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDETFBasicInfo_2eproto() {
  protobuf_AddDesc_MDETFBasicInfo_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDETFBasicInfo.proto");
  GOOGLE_CHECK(file != NULL);
  MDETFBasicInfo_descriptor_ = file->message_type(0);
  static const int MDETFBasicInfo_offsets_[43] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, securityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, symbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, redemptionid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, redemptionsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationredemptioncapitalid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationredemptioncapitalsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, crosssourcecapitalid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, crosssourcecapitalsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, fundmanagementcompany_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, underlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, underlyingsecurityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationredemptionunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, estimatecashcomponent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, maxcashratio_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, ispublish_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, isallowcreation_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, isallowredemption_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, recordnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, tradingday_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, pretradingday_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, cashcomponent_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, navpercu_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, nav_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, dividendpercu_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, redemptionlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, creationlimitperuser_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, redemptionlimitperuser_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, netcreationlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, netredemptionlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, netcreationlimitperuser_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, netredemptionlimitperuser_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, etfcomponents_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, formersymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, crossmarket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, datamultiplepowerof10_),
  };
  MDETFBasicInfo_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDETFBasicInfo_descriptor_,
      MDETFBasicInfo::internal_default_instance(),
      MDETFBasicInfo_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDETFBasicInfo),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFBasicInfo, _internal_metadata_));
  MDETFComponentDetail_descriptor_ = file->message_type(1);
  static const int MDETFComponentDetail_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, securityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, symbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, componentshare_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, substituteflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, premiumratio_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, creationcashsubstitute_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, redemptioncashsubstitute_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, totalcashsubstitute_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, discountratio_),
  };
  MDETFComponentDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDETFComponentDetail_descriptor_,
      MDETFComponentDetail::internal_default_instance(),
      MDETFComponentDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDETFComponentDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDETFComponentDetail, _internal_metadata_));
  MDETFSubstituteFlag_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDETFBasicInfo_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDETFBasicInfo_descriptor_, MDETFBasicInfo::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDETFComponentDetail_descriptor_, MDETFComponentDetail::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDETFBasicInfo_2eproto() {
  MDETFBasicInfo_default_instance_.Shutdown();
  delete MDETFBasicInfo_reflection_;
  MDETFComponentDetail_default_instance_.Shutdown();
  delete MDETFComponentDetail_reflection_;
}

void protobuf_InitDefaults_MDETFBasicInfo_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDETFBasicInfo_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDETFComponentDetail_default_instance_.DefaultConstruct();
  MDETFBasicInfo_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDETFComponentDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDETFBasicInfo_2eproto_once_);
void protobuf_InitDefaults_MDETFBasicInfo_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDETFBasicInfo_2eproto_once_,
                 &protobuf_InitDefaults_MDETFBasicInfo_2eproto_impl);
}
void protobuf_AddDesc_MDETFBasicInfo_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDETFBasicInfo.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\027ESecurityIDSource.proto\032\023ESe"
    "curityType.proto\"\372\t\n\016MDETFBasicInfo\022\026\n\016H"
    "TSCSecurityID\030\001 \001(\t\022\022\n\nSecurityID\030\002 \001(\t\022"
    "\016\n\006Symbol\030\003 \001(\t\022\016\n\006MDDate\030\004 \001(\005\022\016\n\006MDTim"
    "e\030\005 \001(\005\022\?\n\020securityIDSource\030\006 \001(\0162%.com."
    "htsc.mdc.model.ESecurityIDSource\0227\n\014secu"
    "rityType\030\007 \001(\0162!.com.htsc.mdc.model.ESec"
    "urityType\022\022\n\nCreationID\030\013 \001(\t\022\026\n\016Creatio"
    "nSymbol\030\014 \001(\t\022\024\n\014RedemptionID\030\r \001(\t\022\030\n\020R"
    "edemptionSymbol\030\016 \001(\t\022#\n\033CreationRedempt"
    "ionCapitalID\030\017 \001(\t\022\'\n\037CreationRedemption"
    "CapitalSymbol\030\020 \001(\t\022\034\n\024CrossSourceCapita"
    "lID\030\021 \001(\t\022 \n\030CrossSourceCapitalSymbol\030\022 "
    "\001(\t\022\035\n\025FundManagementCompany\030\023 \001(\t\022\034\n\024Un"
    "derlyingSecurityID\030\024 \001(\t\022I\n\032UnderlyingSe"
    "curityIDSource\030\025 \001(\0162%.com.htsc.mdc.mode"
    "l.ESecurityIDSource\022\036\n\026CreationRedemptio"
    "nUnit\030\026 \001(\001\022\035\n\025EstimateCashComponent\030\027 \001"
    "(\001\022\024\n\014MaxCashRatio\030\030 \001(\001\022\021\n\tIsPublish\030\031 "
    "\001(\010\022\027\n\017IsAllowCreation\030\032 \001(\010\022\031\n\021IsAllowR"
    "edemption\030\033 \001(\010\022\021\n\tRecordNum\030\034 \001(\003\022\022\n\nTr"
    "adingDay\030\035 \001(\t\022\025\n\rPreTradingDay\030\036 \001(\t\022\025\n"
    "\rCashComponent\030\037 \001(\001\022\020\n\010NAVperCU\030  \001(\001\022\013"
    "\n\003NAV\030! \001(\001\022\025\n\rDividendPerCU\030\" \001(\001\022\025\n\rCr"
    "eationLimit\030# \001(\001\022\027\n\017RedemptionLimit\030$ \001"
    "(\001\022\034\n\024CreationLimitPerUser\030% \001(\001\022\036\n\026Rede"
    "mptionLimitPerUser\030& \001(\001\022\030\n\020NetCreationL"
    "imit\030\' \001(\001\022\032\n\022NetRedemptionLimit\030( \001(\001\022\037"
    "\n\027NetCreationLimitPerUser\030) \001(\001\022!\n\031NetRe"
    "demptionLimitPerUser\030* \001(\001\022G\n\rETFCompone"
    "nts\030+ \003(\01320.com.htsc.mdc.insight.model.M"
    "DETFComponentDetail\022\024\n\014FormerSymbol\030, \001("
    "\t\022\023\n\013CrossMarket\030- \001(\010\022\035\n\025DataMultiplePo"
    "werOf10\030. \001(\005\"\200\003\n\024MDETFComponentDetail\022\026"
    "\n\016HTSCSecurityID\030\001 \001(\t\022\022\n\nSecurityID\030\002 \001"
    "(\t\022\016\n\006Symbol\030\003 \001(\t\022\?\n\020securityIDSource\030\004"
    " \001(\0162%.com.htsc.mdc.model.ESecurityIDSou"
    "rce\022\026\n\016ComponentShare\030\005 \001(\001\022G\n\016Substitut"
    "eFlag\030\006 \001(\0162/.com.htsc.mdc.insight.model"
    ".MDETFSubstituteFlag\022\024\n\014PremiumRatio\030\007 \001"
    "(\001\022\036\n\026CreationCashSubstitute\030\010 \001(\001\022 \n\030Re"
    "demptionCashSubstitute\030\t \001(\001\022\033\n\023TotalCas"
    "hSubstitute\030\n \001(\001\022\025\n\rDiscountRatio\030\013 \001(\001"
    "*v\n\023MDETFSubstituteFlag\022\033\n\027DEFAULT_SUBST"
    "ITUTE_FLAG\020\000\022\027\n\023DISALLOW_SUBSTITUTE\020\001\022\024\n"
    "\020ALLOW_SUBSTITUTE\020\002\022\023\n\017MUST_SUBSTITUTE\020\003"
    "B7\n\032com.htsc.mdc.insight.modelB\024MDETFBas"
    "icInfoProtosH\001\240\001\001b\006proto3", 1945);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDETFBasicInfo.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDETFBasicInfo_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDETFBasicInfo_2eproto_once_);
void protobuf_AddDesc_MDETFBasicInfo_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDETFBasicInfo_2eproto_once_,
                 &protobuf_AddDesc_MDETFBasicInfo_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDETFBasicInfo_2eproto {
  StaticDescriptorInitializer_MDETFBasicInfo_2eproto() {
    protobuf_AddDesc_MDETFBasicInfo_2eproto();
  }
} static_descriptor_initializer_MDETFBasicInfo_2eproto_;
const ::google::protobuf::EnumDescriptor* MDETFSubstituteFlag_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDETFSubstituteFlag_descriptor_;
}
bool MDETFSubstituteFlag_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDETFBasicInfo::kHTSCSecurityIDFieldNumber;
const int MDETFBasicInfo::kSecurityIDFieldNumber;
const int MDETFBasicInfo::kSymbolFieldNumber;
const int MDETFBasicInfo::kMDDateFieldNumber;
const int MDETFBasicInfo::kMDTimeFieldNumber;
const int MDETFBasicInfo::kSecurityIDSourceFieldNumber;
const int MDETFBasicInfo::kSecurityTypeFieldNumber;
const int MDETFBasicInfo::kCreationIDFieldNumber;
const int MDETFBasicInfo::kCreationSymbolFieldNumber;
const int MDETFBasicInfo::kRedemptionIDFieldNumber;
const int MDETFBasicInfo::kRedemptionSymbolFieldNumber;
const int MDETFBasicInfo::kCreationRedemptionCapitalIDFieldNumber;
const int MDETFBasicInfo::kCreationRedemptionCapitalSymbolFieldNumber;
const int MDETFBasicInfo::kCrossSourceCapitalIDFieldNumber;
const int MDETFBasicInfo::kCrossSourceCapitalSymbolFieldNumber;
const int MDETFBasicInfo::kFundManagementCompanyFieldNumber;
const int MDETFBasicInfo::kUnderlyingSecurityIDFieldNumber;
const int MDETFBasicInfo::kUnderlyingSecurityIDSourceFieldNumber;
const int MDETFBasicInfo::kCreationRedemptionUnitFieldNumber;
const int MDETFBasicInfo::kEstimateCashComponentFieldNumber;
const int MDETFBasicInfo::kMaxCashRatioFieldNumber;
const int MDETFBasicInfo::kIsPublishFieldNumber;
const int MDETFBasicInfo::kIsAllowCreationFieldNumber;
const int MDETFBasicInfo::kIsAllowRedemptionFieldNumber;
const int MDETFBasicInfo::kRecordNumFieldNumber;
const int MDETFBasicInfo::kTradingDayFieldNumber;
const int MDETFBasicInfo::kPreTradingDayFieldNumber;
const int MDETFBasicInfo::kCashComponentFieldNumber;
const int MDETFBasicInfo::kNAVperCUFieldNumber;
const int MDETFBasicInfo::kNAVFieldNumber;
const int MDETFBasicInfo::kDividendPerCUFieldNumber;
const int MDETFBasicInfo::kCreationLimitFieldNumber;
const int MDETFBasicInfo::kRedemptionLimitFieldNumber;
const int MDETFBasicInfo::kCreationLimitPerUserFieldNumber;
const int MDETFBasicInfo::kRedemptionLimitPerUserFieldNumber;
const int MDETFBasicInfo::kNetCreationLimitFieldNumber;
const int MDETFBasicInfo::kNetRedemptionLimitFieldNumber;
const int MDETFBasicInfo::kNetCreationLimitPerUserFieldNumber;
const int MDETFBasicInfo::kNetRedemptionLimitPerUserFieldNumber;
const int MDETFBasicInfo::kETFComponentsFieldNumber;
const int MDETFBasicInfo::kFormerSymbolFieldNumber;
const int MDETFBasicInfo::kCrossMarketFieldNumber;
const int MDETFBasicInfo::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDETFBasicInfo::MDETFBasicInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDETFBasicInfo)
}

void MDETFBasicInfo::InitAsDefaultInstance() {
}

MDETFBasicInfo::MDETFBasicInfo(const MDETFBasicInfo& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDETFBasicInfo)
}

void MDETFBasicInfo::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fundmanagementcompany_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingday_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pretradingday_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  formersymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDETFBasicInfo::~MDETFBasicInfo() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDETFBasicInfo)
  SharedDtor();
}

void MDETFBasicInfo::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fundmanagementcompany_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  underlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingday_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pretradingday_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  formersymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDETFBasicInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDETFBasicInfo::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDETFBasicInfo_descriptor_;
}

const MDETFBasicInfo& MDETFBasicInfo::default_instance() {
  protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDETFBasicInfo> MDETFBasicInfo_default_instance_;

MDETFBasicInfo* MDETFBasicInfo::New(::google::protobuf::Arena* arena) const {
  MDETFBasicInfo* n = new MDETFBasicInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDETFBasicInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDETFBasicInfo, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDETFBasicInfo*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  redemptionsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creationredemptioncapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crosssourcecapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fundmanagementcompany_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(creationredemptionunit_, maxcashratio_);
  ZR_(underlyingsecurityidsource_, isallowredemption_);
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(cashcomponent_, creationlimit_);
  recordnum_ = GOOGLE_LONGLONG(0);
  tradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  pretradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(redemptionlimit_, netredemptionlimitperuser_);
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  crossmarket_ = false;
  datamultiplepowerof10_ = 0;

#undef ZR_HELPER_
#undef ZR_

  etfcomponents_.Clear();
}

bool MDETFBasicInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_SecurityID;
        break;
      }

      // optional string SecurityID = 2;
      case 2: {
        if (tag == 18) {
         parse_SecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securityid().data(), this->securityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Symbol;
        break;
      }

      // optional string Symbol = 3;
      case 3: {
        if (tag == 26) {
         parse_Symbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbol().data(), this->symbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_CreationID;
        break;
      }

      // optional string CreationID = 11;
      case 11: {
        if (tag == 90) {
         parse_CreationID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creationid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creationid().data(), this->creationid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_CreationSymbol;
        break;
      }

      // optional string CreationSymbol = 12;
      case 12: {
        if (tag == 98) {
         parse_CreationSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creationsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creationsymbol().data(), this->creationsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_RedemptionID;
        break;
      }

      // optional string RedemptionID = 13;
      case 13: {
        if (tag == 106) {
         parse_RedemptionID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_redemptionid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->redemptionid().data(), this->redemptionid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_RedemptionSymbol;
        break;
      }

      // optional string RedemptionSymbol = 14;
      case 14: {
        if (tag == 114) {
         parse_RedemptionSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_redemptionsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->redemptionsymbol().data(), this->redemptionsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_CreationRedemptionCapitalID;
        break;
      }

      // optional string CreationRedemptionCapitalID = 15;
      case 15: {
        if (tag == 122) {
         parse_CreationRedemptionCapitalID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creationredemptioncapitalid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creationredemptioncapitalid().data(), this->creationredemptioncapitalid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_CreationRedemptionCapitalSymbol;
        break;
      }

      // optional string CreationRedemptionCapitalSymbol = 16;
      case 16: {
        if (tag == 130) {
         parse_CreationRedemptionCapitalSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creationredemptioncapitalsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creationredemptioncapitalsymbol().data(), this->creationredemptioncapitalsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_CrossSourceCapitalID;
        break;
      }

      // optional string CrossSourceCapitalID = 17;
      case 17: {
        if (tag == 138) {
         parse_CrossSourceCapitalID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_crosssourcecapitalid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->crosssourcecapitalid().data(), this->crosssourcecapitalid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_CrossSourceCapitalSymbol;
        break;
      }

      // optional string CrossSourceCapitalSymbol = 18;
      case 18: {
        if (tag == 146) {
         parse_CrossSourceCapitalSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_crosssourcecapitalsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->crosssourcecapitalsymbol().data(), this->crosssourcecapitalsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_FundManagementCompany;
        break;
      }

      // optional string FundManagementCompany = 19;
      case 19: {
        if (tag == 154) {
         parse_FundManagementCompany:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fundmanagementcompany()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fundmanagementcompany().data(), this->fundmanagementcompany().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_UnderlyingSecurityID;
        break;
      }

      // optional string UnderlyingSecurityID = 20;
      case 20: {
        if (tag == 162) {
         parse_UnderlyingSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_underlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_UnderlyingSecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
      case 21: {
        if (tag == 168) {
         parse_UnderlyingSecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_underlyingsecurityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_CreationRedemptionUnit;
        break;
      }

      // optional double CreationRedemptionUnit = 22;
      case 22: {
        if (tag == 177) {
         parse_CreationRedemptionUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &creationredemptionunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(185)) goto parse_EstimateCashComponent;
        break;
      }

      // optional double EstimateCashComponent = 23;
      case 23: {
        if (tag == 185) {
         parse_EstimateCashComponent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &estimatecashcomponent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(193)) goto parse_MaxCashRatio;
        break;
      }

      // optional double MaxCashRatio = 24;
      case 24: {
        if (tag == 193) {
         parse_MaxCashRatio:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &maxcashratio_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_IsPublish;
        break;
      }

      // optional bool IsPublish = 25;
      case 25: {
        if (tag == 200) {
         parse_IsPublish:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &ispublish_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_IsAllowCreation;
        break;
      }

      // optional bool IsAllowCreation = 26;
      case 26: {
        if (tag == 208) {
         parse_IsAllowCreation:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isallowcreation_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_IsAllowRedemption;
        break;
      }

      // optional bool IsAllowRedemption = 27;
      case 27: {
        if (tag == 216) {
         parse_IsAllowRedemption:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isallowredemption_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_RecordNum;
        break;
      }

      // optional int64 RecordNum = 28;
      case 28: {
        if (tag == 224) {
         parse_RecordNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &recordnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_TradingDay;
        break;
      }

      // optional string TradingDay = 29;
      case 29: {
        if (tag == 234) {
         parse_TradingDay:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingday()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingday().data(), this->tradingday().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_PreTradingDay;
        break;
      }

      // optional string PreTradingDay = 30;
      case 30: {
        if (tag == 242) {
         parse_PreTradingDay:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_pretradingday()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->pretradingday().data(), this->pretradingday().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(249)) goto parse_CashComponent;
        break;
      }

      // optional double CashComponent = 31;
      case 31: {
        if (tag == 249) {
         parse_CashComponent:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &cashcomponent_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(257)) goto parse_NAVperCU;
        break;
      }

      // optional double NAVperCU = 32;
      case 32: {
        if (tag == 257) {
         parse_NAVperCU:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &navpercu_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(265)) goto parse_NAV;
        break;
      }

      // optional double NAV = 33;
      case 33: {
        if (tag == 265) {
         parse_NAV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &nav_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(273)) goto parse_DividendPerCU;
        break;
      }

      // optional double DividendPerCU = 34;
      case 34: {
        if (tag == 273) {
         parse_DividendPerCU:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &dividendpercu_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(281)) goto parse_CreationLimit;
        break;
      }

      // optional double CreationLimit = 35;
      case 35: {
        if (tag == 281) {
         parse_CreationLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &creationlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(289)) goto parse_RedemptionLimit;
        break;
      }

      // optional double RedemptionLimit = 36;
      case 36: {
        if (tag == 289) {
         parse_RedemptionLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &redemptionlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(297)) goto parse_CreationLimitPerUser;
        break;
      }

      // optional double CreationLimitPerUser = 37;
      case 37: {
        if (tag == 297) {
         parse_CreationLimitPerUser:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &creationlimitperuser_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(305)) goto parse_RedemptionLimitPerUser;
        break;
      }

      // optional double RedemptionLimitPerUser = 38;
      case 38: {
        if (tag == 305) {
         parse_RedemptionLimitPerUser:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &redemptionlimitperuser_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(313)) goto parse_NetCreationLimit;
        break;
      }

      // optional double NetCreationLimit = 39;
      case 39: {
        if (tag == 313) {
         parse_NetCreationLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &netcreationlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(321)) goto parse_NetRedemptionLimit;
        break;
      }

      // optional double NetRedemptionLimit = 40;
      case 40: {
        if (tag == 321) {
         parse_NetRedemptionLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &netredemptionlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(329)) goto parse_NetCreationLimitPerUser;
        break;
      }

      // optional double NetCreationLimitPerUser = 41;
      case 41: {
        if (tag == 329) {
         parse_NetCreationLimitPerUser:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &netcreationlimitperuser_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(337)) goto parse_NetRedemptionLimitPerUser;
        break;
      }

      // optional double NetRedemptionLimitPerUser = 42;
      case 42: {
        if (tag == 337) {
         parse_NetRedemptionLimitPerUser:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &netredemptionlimitperuser_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_ETFComponents;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
      case 43: {
        if (tag == 346) {
         parse_ETFComponents:
          DO_(input->IncrementRecursionDepth());
         parse_loop_ETFComponents:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_etfcomponents()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_loop_ETFComponents;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(354)) goto parse_FormerSymbol;
        break;
      }

      // optional string FormerSymbol = 44;
      case 44: {
        if (tag == 354) {
         parse_FormerSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_formersymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->formersymbol().data(), this->formersymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_CrossMarket;
        break;
      }

      // optional bool CrossMarket = 45;
      case 45: {
        if (tag == 360) {
         parse_CrossMarket:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &crossmarket_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 46;
      case 46: {
        if (tag == 368) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDETFBasicInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDETFBasicInfo)
  return false;
#undef DO_
}

void MDETFBasicInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->securityid(), output);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->symbol(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional string CreationID = 11;
  if (this->creationid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationid().data(), this->creationid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->creationid(), output);
  }

  // optional string CreationSymbol = 12;
  if (this->creationsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationsymbol().data(), this->creationsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->creationsymbol(), output);
  }

  // optional string RedemptionID = 13;
  if (this->redemptionid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->redemptionid().data(), this->redemptionid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->redemptionid(), output);
  }

  // optional string RedemptionSymbol = 14;
  if (this->redemptionsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->redemptionsymbol().data(), this->redemptionsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->redemptionsymbol(), output);
  }

  // optional string CreationRedemptionCapitalID = 15;
  if (this->creationredemptioncapitalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationredemptioncapitalid().data(), this->creationredemptioncapitalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->creationredemptioncapitalid(), output);
  }

  // optional string CreationRedemptionCapitalSymbol = 16;
  if (this->creationredemptioncapitalsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationredemptioncapitalsymbol().data(), this->creationredemptioncapitalsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->creationredemptioncapitalsymbol(), output);
  }

  // optional string CrossSourceCapitalID = 17;
  if (this->crosssourcecapitalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->crosssourcecapitalid().data(), this->crosssourcecapitalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->crosssourcecapitalid(), output);
  }

  // optional string CrossSourceCapitalSymbol = 18;
  if (this->crosssourcecapitalsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->crosssourcecapitalsymbol().data(), this->crosssourcecapitalsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->crosssourcecapitalsymbol(), output);
  }

  // optional string FundManagementCompany = 19;
  if (this->fundmanagementcompany().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fundmanagementcompany().data(), this->fundmanagementcompany().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->fundmanagementcompany(), output);
  }

  // optional string UnderlyingSecurityID = 20;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->underlyingsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
  if (this->underlyingsecurityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      21, this->underlyingsecurityidsource(), output);
  }

  // optional double CreationRedemptionUnit = 22;
  if (this->creationredemptionunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->creationredemptionunit(), output);
  }

  // optional double EstimateCashComponent = 23;
  if (this->estimatecashcomponent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(23, this->estimatecashcomponent(), output);
  }

  // optional double MaxCashRatio = 24;
  if (this->maxcashratio() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(24, this->maxcashratio(), output);
  }

  // optional bool IsPublish = 25;
  if (this->ispublish() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(25, this->ispublish(), output);
  }

  // optional bool IsAllowCreation = 26;
  if (this->isallowcreation() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(26, this->isallowcreation(), output);
  }

  // optional bool IsAllowRedemption = 27;
  if (this->isallowredemption() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(27, this->isallowredemption(), output);
  }

  // optional int64 RecordNum = 28;
  if (this->recordnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->recordnum(), output);
  }

  // optional string TradingDay = 29;
  if (this->tradingday().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingday().data(), this->tradingday().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      29, this->tradingday(), output);
  }

  // optional string PreTradingDay = 30;
  if (this->pretradingday().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pretradingday().data(), this->pretradingday().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      30, this->pretradingday(), output);
  }

  // optional double CashComponent = 31;
  if (this->cashcomponent() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(31, this->cashcomponent(), output);
  }

  // optional double NAVperCU = 32;
  if (this->navpercu() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(32, this->navpercu(), output);
  }

  // optional double NAV = 33;
  if (this->nav() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(33, this->nav(), output);
  }

  // optional double DividendPerCU = 34;
  if (this->dividendpercu() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(34, this->dividendpercu(), output);
  }

  // optional double CreationLimit = 35;
  if (this->creationlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(35, this->creationlimit(), output);
  }

  // optional double RedemptionLimit = 36;
  if (this->redemptionlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(36, this->redemptionlimit(), output);
  }

  // optional double CreationLimitPerUser = 37;
  if (this->creationlimitperuser() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(37, this->creationlimitperuser(), output);
  }

  // optional double RedemptionLimitPerUser = 38;
  if (this->redemptionlimitperuser() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(38, this->redemptionlimitperuser(), output);
  }

  // optional double NetCreationLimit = 39;
  if (this->netcreationlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(39, this->netcreationlimit(), output);
  }

  // optional double NetRedemptionLimit = 40;
  if (this->netredemptionlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(40, this->netredemptionlimit(), output);
  }

  // optional double NetCreationLimitPerUser = 41;
  if (this->netcreationlimitperuser() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(41, this->netcreationlimitperuser(), output);
  }

  // optional double NetRedemptionLimitPerUser = 42;
  if (this->netredemptionlimitperuser() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(42, this->netredemptionlimitperuser(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
  for (unsigned int i = 0, n = this->etfcomponents_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      43, this->etfcomponents(i), output);
  }

  // optional string FormerSymbol = 44;
  if (this->formersymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->formersymbol().data(), this->formersymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      44, this->formersymbol(), output);
  }

  // optional bool CrossMarket = 45;
  if (this->crossmarket() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(45, this->crossmarket(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(46, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDETFBasicInfo)
}

::google::protobuf::uint8* MDETFBasicInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->securityid(), target);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->symbol(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional string CreationID = 11;
  if (this->creationid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationid().data(), this->creationid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->creationid(), target);
  }

  // optional string CreationSymbol = 12;
  if (this->creationsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationsymbol().data(), this->creationsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->creationsymbol(), target);
  }

  // optional string RedemptionID = 13;
  if (this->redemptionid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->redemptionid().data(), this->redemptionid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->redemptionid(), target);
  }

  // optional string RedemptionSymbol = 14;
  if (this->redemptionsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->redemptionsymbol().data(), this->redemptionsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->redemptionsymbol(), target);
  }

  // optional string CreationRedemptionCapitalID = 15;
  if (this->creationredemptioncapitalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationredemptioncapitalid().data(), this->creationredemptioncapitalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->creationredemptioncapitalid(), target);
  }

  // optional string CreationRedemptionCapitalSymbol = 16;
  if (this->creationredemptioncapitalsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creationredemptioncapitalsymbol().data(), this->creationredemptioncapitalsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->creationredemptioncapitalsymbol(), target);
  }

  // optional string CrossSourceCapitalID = 17;
  if (this->crosssourcecapitalid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->crosssourcecapitalid().data(), this->crosssourcecapitalid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->crosssourcecapitalid(), target);
  }

  // optional string CrossSourceCapitalSymbol = 18;
  if (this->crosssourcecapitalsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->crosssourcecapitalsymbol().data(), this->crosssourcecapitalsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->crosssourcecapitalsymbol(), target);
  }

  // optional string FundManagementCompany = 19;
  if (this->fundmanagementcompany().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fundmanagementcompany().data(), this->fundmanagementcompany().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->fundmanagementcompany(), target);
  }

  // optional string UnderlyingSecurityID = 20;
  if (this->underlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->underlyingsecurityid().data(), this->underlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->underlyingsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
  if (this->underlyingsecurityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      21, this->underlyingsecurityidsource(), target);
  }

  // optional double CreationRedemptionUnit = 22;
  if (this->creationredemptionunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->creationredemptionunit(), target);
  }

  // optional double EstimateCashComponent = 23;
  if (this->estimatecashcomponent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(23, this->estimatecashcomponent(), target);
  }

  // optional double MaxCashRatio = 24;
  if (this->maxcashratio() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(24, this->maxcashratio(), target);
  }

  // optional bool IsPublish = 25;
  if (this->ispublish() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(25, this->ispublish(), target);
  }

  // optional bool IsAllowCreation = 26;
  if (this->isallowcreation() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(26, this->isallowcreation(), target);
  }

  // optional bool IsAllowRedemption = 27;
  if (this->isallowredemption() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(27, this->isallowredemption(), target);
  }

  // optional int64 RecordNum = 28;
  if (this->recordnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->recordnum(), target);
  }

  // optional string TradingDay = 29;
  if (this->tradingday().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingday().data(), this->tradingday().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        29, this->tradingday(), target);
  }

  // optional string PreTradingDay = 30;
  if (this->pretradingday().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->pretradingday().data(), this->pretradingday().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        30, this->pretradingday(), target);
  }

  // optional double CashComponent = 31;
  if (this->cashcomponent() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(31, this->cashcomponent(), target);
  }

  // optional double NAVperCU = 32;
  if (this->navpercu() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(32, this->navpercu(), target);
  }

  // optional double NAV = 33;
  if (this->nav() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(33, this->nav(), target);
  }

  // optional double DividendPerCU = 34;
  if (this->dividendpercu() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(34, this->dividendpercu(), target);
  }

  // optional double CreationLimit = 35;
  if (this->creationlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(35, this->creationlimit(), target);
  }

  // optional double RedemptionLimit = 36;
  if (this->redemptionlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(36, this->redemptionlimit(), target);
  }

  // optional double CreationLimitPerUser = 37;
  if (this->creationlimitperuser() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(37, this->creationlimitperuser(), target);
  }

  // optional double RedemptionLimitPerUser = 38;
  if (this->redemptionlimitperuser() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(38, this->redemptionlimitperuser(), target);
  }

  // optional double NetCreationLimit = 39;
  if (this->netcreationlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(39, this->netcreationlimit(), target);
  }

  // optional double NetRedemptionLimit = 40;
  if (this->netredemptionlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(40, this->netredemptionlimit(), target);
  }

  // optional double NetCreationLimitPerUser = 41;
  if (this->netcreationlimitperuser() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(41, this->netcreationlimitperuser(), target);
  }

  // optional double NetRedemptionLimitPerUser = 42;
  if (this->netredemptionlimitperuser() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(42, this->netredemptionlimitperuser(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
  for (unsigned int i = 0, n = this->etfcomponents_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        43, this->etfcomponents(i), false, target);
  }

  // optional string FormerSymbol = 44;
  if (this->formersymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->formersymbol().data(), this->formersymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        44, this->formersymbol(), target);
  }

  // optional bool CrossMarket = 45;
  if (this->crossmarket() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(45, this->crossmarket(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(46, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDETFBasicInfo)
  return target;
}

size_t MDETFBasicInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securityid());
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbol());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string CreationID = 11;
  if (this->creationid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creationid());
  }

  // optional string CreationSymbol = 12;
  if (this->creationsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creationsymbol());
  }

  // optional string RedemptionID = 13;
  if (this->redemptionid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->redemptionid());
  }

  // optional string RedemptionSymbol = 14;
  if (this->redemptionsymbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->redemptionsymbol());
  }

  // optional string CreationRedemptionCapitalID = 15;
  if (this->creationredemptioncapitalid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creationredemptioncapitalid());
  }

  // optional string CreationRedemptionCapitalSymbol = 16;
  if (this->creationredemptioncapitalsymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creationredemptioncapitalsymbol());
  }

  // optional string CrossSourceCapitalID = 17;
  if (this->crosssourcecapitalid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->crosssourcecapitalid());
  }

  // optional string CrossSourceCapitalSymbol = 18;
  if (this->crosssourcecapitalsymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->crosssourcecapitalsymbol());
  }

  // optional string FundManagementCompany = 19;
  if (this->fundmanagementcompany().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fundmanagementcompany());
  }

  // optional string UnderlyingSecurityID = 20;
  if (this->underlyingsecurityid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->underlyingsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
  if (this->underlyingsecurityidsource() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->underlyingsecurityidsource());
  }

  // optional double CreationRedemptionUnit = 22;
  if (this->creationredemptionunit() != 0) {
    total_size += 2 + 8;
  }

  // optional double EstimateCashComponent = 23;
  if (this->estimatecashcomponent() != 0) {
    total_size += 2 + 8;
  }

  // optional double MaxCashRatio = 24;
  if (this->maxcashratio() != 0) {
    total_size += 2 + 8;
  }

  // optional bool IsPublish = 25;
  if (this->ispublish() != 0) {
    total_size += 2 + 1;
  }

  // optional bool IsAllowCreation = 26;
  if (this->isallowcreation() != 0) {
    total_size += 2 + 1;
  }

  // optional bool IsAllowRedemption = 27;
  if (this->isallowredemption() != 0) {
    total_size += 2 + 1;
  }

  // optional int64 RecordNum = 28;
  if (this->recordnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->recordnum());
  }

  // optional string TradingDay = 29;
  if (this->tradingday().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingday());
  }

  // optional string PreTradingDay = 30;
  if (this->pretradingday().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->pretradingday());
  }

  // optional double CashComponent = 31;
  if (this->cashcomponent() != 0) {
    total_size += 2 + 8;
  }

  // optional double NAVperCU = 32;
  if (this->navpercu() != 0) {
    total_size += 2 + 8;
  }

  // optional double NAV = 33;
  if (this->nav() != 0) {
    total_size += 2 + 8;
  }

  // optional double DividendPerCU = 34;
  if (this->dividendpercu() != 0) {
    total_size += 2 + 8;
  }

  // optional double CreationLimit = 35;
  if (this->creationlimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double RedemptionLimit = 36;
  if (this->redemptionlimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double CreationLimitPerUser = 37;
  if (this->creationlimitperuser() != 0) {
    total_size += 2 + 8;
  }

  // optional double RedemptionLimitPerUser = 38;
  if (this->redemptionlimitperuser() != 0) {
    total_size += 2 + 8;
  }

  // optional double NetCreationLimit = 39;
  if (this->netcreationlimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double NetRedemptionLimit = 40;
  if (this->netredemptionlimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double NetCreationLimitPerUser = 41;
  if (this->netcreationlimitperuser() != 0) {
    total_size += 2 + 8;
  }

  // optional double NetRedemptionLimitPerUser = 42;
  if (this->netredemptionlimitperuser() != 0) {
    total_size += 2 + 8;
  }

  // optional string FormerSymbol = 44;
  if (this->formersymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->formersymbol());
  }

  // optional bool CrossMarket = 45;
  if (this->crossmarket() != 0) {
    total_size += 2 + 1;
  }

  // optional int32 DataMultiplePowerOf10 = 46;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
  {
    unsigned int count = this->etfcomponents_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->etfcomponents(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDETFBasicInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDETFBasicInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDETFBasicInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDETFBasicInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDETFBasicInfo)
    UnsafeMergeFrom(*source);
  }
}

void MDETFBasicInfo::MergeFrom(const MDETFBasicInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDETFBasicInfo::UnsafeMergeFrom(const MDETFBasicInfo& from) {
  GOOGLE_DCHECK(&from != this);
  etfcomponents_.MergeFrom(from.etfcomponents_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securityid().size() > 0) {

    securityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securityid_);
  }
  if (from.symbol().size() > 0) {

    symbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbol_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.creationid().size() > 0) {

    creationid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creationid_);
  }
  if (from.creationsymbol().size() > 0) {

    creationsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creationsymbol_);
  }
  if (from.redemptionid().size() > 0) {

    redemptionid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.redemptionid_);
  }
  if (from.redemptionsymbol().size() > 0) {

    redemptionsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.redemptionsymbol_);
  }
  if (from.creationredemptioncapitalid().size() > 0) {

    creationredemptioncapitalid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creationredemptioncapitalid_);
  }
  if (from.creationredemptioncapitalsymbol().size() > 0) {

    creationredemptioncapitalsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creationredemptioncapitalsymbol_);
  }
  if (from.crosssourcecapitalid().size() > 0) {

    crosssourcecapitalid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.crosssourcecapitalid_);
  }
  if (from.crosssourcecapitalsymbol().size() > 0) {

    crosssourcecapitalsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.crosssourcecapitalsymbol_);
  }
  if (from.fundmanagementcompany().size() > 0) {

    fundmanagementcompany_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fundmanagementcompany_);
  }
  if (from.underlyingsecurityid().size() > 0) {

    underlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.underlyingsecurityid_);
  }
  if (from.underlyingsecurityidsource() != 0) {
    set_underlyingsecurityidsource(from.underlyingsecurityidsource());
  }
  if (from.creationredemptionunit() != 0) {
    set_creationredemptionunit(from.creationredemptionunit());
  }
  if (from.estimatecashcomponent() != 0) {
    set_estimatecashcomponent(from.estimatecashcomponent());
  }
  if (from.maxcashratio() != 0) {
    set_maxcashratio(from.maxcashratio());
  }
  if (from.ispublish() != 0) {
    set_ispublish(from.ispublish());
  }
  if (from.isallowcreation() != 0) {
    set_isallowcreation(from.isallowcreation());
  }
  if (from.isallowredemption() != 0) {
    set_isallowredemption(from.isallowredemption());
  }
  if (from.recordnum() != 0) {
    set_recordnum(from.recordnum());
  }
  if (from.tradingday().size() > 0) {

    tradingday_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingday_);
  }
  if (from.pretradingday().size() > 0) {

    pretradingday_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.pretradingday_);
  }
  if (from.cashcomponent() != 0) {
    set_cashcomponent(from.cashcomponent());
  }
  if (from.navpercu() != 0) {
    set_navpercu(from.navpercu());
  }
  if (from.nav() != 0) {
    set_nav(from.nav());
  }
  if (from.dividendpercu() != 0) {
    set_dividendpercu(from.dividendpercu());
  }
  if (from.creationlimit() != 0) {
    set_creationlimit(from.creationlimit());
  }
  if (from.redemptionlimit() != 0) {
    set_redemptionlimit(from.redemptionlimit());
  }
  if (from.creationlimitperuser() != 0) {
    set_creationlimitperuser(from.creationlimitperuser());
  }
  if (from.redemptionlimitperuser() != 0) {
    set_redemptionlimitperuser(from.redemptionlimitperuser());
  }
  if (from.netcreationlimit() != 0) {
    set_netcreationlimit(from.netcreationlimit());
  }
  if (from.netredemptionlimit() != 0) {
    set_netredemptionlimit(from.netredemptionlimit());
  }
  if (from.netcreationlimitperuser() != 0) {
    set_netcreationlimitperuser(from.netcreationlimitperuser());
  }
  if (from.netredemptionlimitperuser() != 0) {
    set_netredemptionlimitperuser(from.netredemptionlimitperuser());
  }
  if (from.formersymbol().size() > 0) {

    formersymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.formersymbol_);
  }
  if (from.crossmarket() != 0) {
    set_crossmarket(from.crossmarket());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDETFBasicInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDETFBasicInfo::CopyFrom(const MDETFBasicInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDETFBasicInfo)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDETFBasicInfo::IsInitialized() const {

  return true;
}

void MDETFBasicInfo::Swap(MDETFBasicInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDETFBasicInfo::InternalSwap(MDETFBasicInfo* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  securityid_.Swap(&other->securityid_);
  symbol_.Swap(&other->symbol_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  creationid_.Swap(&other->creationid_);
  creationsymbol_.Swap(&other->creationsymbol_);
  redemptionid_.Swap(&other->redemptionid_);
  redemptionsymbol_.Swap(&other->redemptionsymbol_);
  creationredemptioncapitalid_.Swap(&other->creationredemptioncapitalid_);
  creationredemptioncapitalsymbol_.Swap(&other->creationredemptioncapitalsymbol_);
  crosssourcecapitalid_.Swap(&other->crosssourcecapitalid_);
  crosssourcecapitalsymbol_.Swap(&other->crosssourcecapitalsymbol_);
  fundmanagementcompany_.Swap(&other->fundmanagementcompany_);
  underlyingsecurityid_.Swap(&other->underlyingsecurityid_);
  std::swap(underlyingsecurityidsource_, other->underlyingsecurityidsource_);
  std::swap(creationredemptionunit_, other->creationredemptionunit_);
  std::swap(estimatecashcomponent_, other->estimatecashcomponent_);
  std::swap(maxcashratio_, other->maxcashratio_);
  std::swap(ispublish_, other->ispublish_);
  std::swap(isallowcreation_, other->isallowcreation_);
  std::swap(isallowredemption_, other->isallowredemption_);
  std::swap(recordnum_, other->recordnum_);
  tradingday_.Swap(&other->tradingday_);
  pretradingday_.Swap(&other->pretradingday_);
  std::swap(cashcomponent_, other->cashcomponent_);
  std::swap(navpercu_, other->navpercu_);
  std::swap(nav_, other->nav_);
  std::swap(dividendpercu_, other->dividendpercu_);
  std::swap(creationlimit_, other->creationlimit_);
  std::swap(redemptionlimit_, other->redemptionlimit_);
  std::swap(creationlimitperuser_, other->creationlimitperuser_);
  std::swap(redemptionlimitperuser_, other->redemptionlimitperuser_);
  std::swap(netcreationlimit_, other->netcreationlimit_);
  std::swap(netredemptionlimit_, other->netredemptionlimit_);
  std::swap(netcreationlimitperuser_, other->netcreationlimitperuser_);
  std::swap(netredemptionlimitperuser_, other->netredemptionlimitperuser_);
  etfcomponents_.UnsafeArenaSwap(&other->etfcomponents_);
  formersymbol_.Swap(&other->formersymbol_);
  std::swap(crossmarket_, other->crossmarket_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDETFBasicInfo::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDETFBasicInfo_descriptor_;
  metadata.reflection = MDETFBasicInfo_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDETFBasicInfo

// optional string HTSCSecurityID = 1;
void MDETFBasicInfo::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
void MDETFBasicInfo::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
void MDETFBasicInfo::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}
::std::string* MDETFBasicInfo::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.HTSCSecurityID)
}

// optional string SecurityID = 2;
void MDETFBasicInfo::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
void MDETFBasicInfo::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
void MDETFBasicInfo::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}
::std::string* MDETFBasicInfo::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.SecurityID)
}

// optional string Symbol = 3;
void MDETFBasicInfo::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
void MDETFBasicInfo::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
void MDETFBasicInfo::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}
::std::string* MDETFBasicInfo::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.Symbol)
}

// optional int32 MDDate = 4;
void MDETFBasicInfo::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDETFBasicInfo::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MDDate)
  return mddate_;
}
void MDETFBasicInfo::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MDDate)
}

// optional int32 MDTime = 5;
void MDETFBasicInfo::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDETFBasicInfo::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MDTime)
  return mdtime_;
}
void MDETFBasicInfo::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MDTime)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDETFBasicInfo::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDETFBasicInfo::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDETFBasicInfo::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDETFBasicInfo::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDETFBasicInfo::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDETFBasicInfo::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.securityType)
}

// optional string CreationID = 11;
void MDETFBasicInfo::clear_creationid() {
  creationid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::creationid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  return creationid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_creationid(const ::std::string& value) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
void MDETFBasicInfo::set_creationid(const char* value) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
void MDETFBasicInfo::set_creationid(const char* value, size_t size) {
  
  creationid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}
::std::string* MDETFBasicInfo::mutable_creationid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  return creationid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_creationid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
  
  return creationid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_creationid(::std::string* creationid) {
  if (creationid != NULL) {
    
  } else {
    
  }
  creationid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationID)
}

// optional string CreationSymbol = 12;
void MDETFBasicInfo::clear_creationsymbol() {
  creationsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::creationsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  return creationsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_creationsymbol(const ::std::string& value) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
void MDETFBasicInfo::set_creationsymbol(const char* value) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
void MDETFBasicInfo::set_creationsymbol(const char* value, size_t size) {
  
  creationsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}
::std::string* MDETFBasicInfo::mutable_creationsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  return creationsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_creationsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
  
  return creationsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_creationsymbol(::std::string* creationsymbol) {
  if (creationsymbol != NULL) {
    
  } else {
    
  }
  creationsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationSymbol)
}

// optional string RedemptionID = 13;
void MDETFBasicInfo::clear_redemptionid() {
  redemptionid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::redemptionid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  return redemptionid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_redemptionid(const ::std::string& value) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
void MDETFBasicInfo::set_redemptionid(const char* value) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
void MDETFBasicInfo::set_redemptionid(const char* value, size_t size) {
  
  redemptionid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}
::std::string* MDETFBasicInfo::mutable_redemptionid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  return redemptionid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_redemptionid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
  
  return redemptionid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_redemptionid(::std::string* redemptionid) {
  if (redemptionid != NULL) {
    
  } else {
    
  }
  redemptionid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), redemptionid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionID)
}

// optional string RedemptionSymbol = 14;
void MDETFBasicInfo::clear_redemptionsymbol() {
  redemptionsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::redemptionsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  return redemptionsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_redemptionsymbol(const ::std::string& value) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
void MDETFBasicInfo::set_redemptionsymbol(const char* value) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
void MDETFBasicInfo::set_redemptionsymbol(const char* value, size_t size) {
  
  redemptionsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}
::std::string* MDETFBasicInfo::mutable_redemptionsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  return redemptionsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_redemptionsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
  
  return redemptionsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_redemptionsymbol(::std::string* redemptionsymbol) {
  if (redemptionsymbol != NULL) {
    
  } else {
    
  }
  redemptionsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), redemptionsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionSymbol)
}

// optional string CreationRedemptionCapitalID = 15;
void MDETFBasicInfo::clear_creationredemptioncapitalid() {
  creationredemptioncapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::creationredemptioncapitalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  return creationredemptioncapitalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_creationredemptioncapitalid(const ::std::string& value) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
void MDETFBasicInfo::set_creationredemptioncapitalid(const char* value) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
void MDETFBasicInfo::set_creationredemptioncapitalid(const char* value, size_t size) {
  
  creationredemptioncapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}
::std::string* MDETFBasicInfo::mutable_creationredemptioncapitalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  return creationredemptioncapitalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_creationredemptioncapitalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
  
  return creationredemptioncapitalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_creationredemptioncapitalid(::std::string* creationredemptioncapitalid) {
  if (creationredemptioncapitalid != NULL) {
    
  } else {
    
  }
  creationredemptioncapitalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationredemptioncapitalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalID)
}

// optional string CreationRedemptionCapitalSymbol = 16;
void MDETFBasicInfo::clear_creationredemptioncapitalsymbol() {
  creationredemptioncapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::creationredemptioncapitalsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  return creationredemptioncapitalsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const ::std::string& value) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const char* value) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
void MDETFBasicInfo::set_creationredemptioncapitalsymbol(const char* value, size_t size) {
  
  creationredemptioncapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}
::std::string* MDETFBasicInfo::mutable_creationredemptioncapitalsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  return creationredemptioncapitalsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_creationredemptioncapitalsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
  
  return creationredemptioncapitalsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_creationredemptioncapitalsymbol(::std::string* creationredemptioncapitalsymbol) {
  if (creationredemptioncapitalsymbol != NULL) {
    
  } else {
    
  }
  creationredemptioncapitalsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creationredemptioncapitalsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionCapitalSymbol)
}

// optional string CrossSourceCapitalID = 17;
void MDETFBasicInfo::clear_crosssourcecapitalid() {
  crosssourcecapitalid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::crosssourcecapitalid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  return crosssourcecapitalid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_crosssourcecapitalid(const ::std::string& value) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
void MDETFBasicInfo::set_crosssourcecapitalid(const char* value) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
void MDETFBasicInfo::set_crosssourcecapitalid(const char* value, size_t size) {
  
  crosssourcecapitalid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}
::std::string* MDETFBasicInfo::mutable_crosssourcecapitalid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  return crosssourcecapitalid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_crosssourcecapitalid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
  
  return crosssourcecapitalid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_crosssourcecapitalid(::std::string* crosssourcecapitalid) {
  if (crosssourcecapitalid != NULL) {
    
  } else {
    
  }
  crosssourcecapitalid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), crosssourcecapitalid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalID)
}

// optional string CrossSourceCapitalSymbol = 18;
void MDETFBasicInfo::clear_crosssourcecapitalsymbol() {
  crosssourcecapitalsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::crosssourcecapitalsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  return crosssourcecapitalsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_crosssourcecapitalsymbol(const ::std::string& value) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
void MDETFBasicInfo::set_crosssourcecapitalsymbol(const char* value) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
void MDETFBasicInfo::set_crosssourcecapitalsymbol(const char* value, size_t size) {
  
  crosssourcecapitalsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}
::std::string* MDETFBasicInfo::mutable_crosssourcecapitalsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  return crosssourcecapitalsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_crosssourcecapitalsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
  
  return crosssourcecapitalsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_crosssourcecapitalsymbol(::std::string* crosssourcecapitalsymbol) {
  if (crosssourcecapitalsymbol != NULL) {
    
  } else {
    
  }
  crosssourcecapitalsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), crosssourcecapitalsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossSourceCapitalSymbol)
}

// optional string FundManagementCompany = 19;
void MDETFBasicInfo::clear_fundmanagementcompany() {
  fundmanagementcompany_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::fundmanagementcompany() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  return fundmanagementcompany_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_fundmanagementcompany(const ::std::string& value) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
void MDETFBasicInfo::set_fundmanagementcompany(const char* value) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
void MDETFBasicInfo::set_fundmanagementcompany(const char* value, size_t size) {
  
  fundmanagementcompany_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}
::std::string* MDETFBasicInfo::mutable_fundmanagementcompany() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  return fundmanagementcompany_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_fundmanagementcompany() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
  
  return fundmanagementcompany_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_fundmanagementcompany(::std::string* fundmanagementcompany) {
  if (fundmanagementcompany != NULL) {
    
  } else {
    
  }
  fundmanagementcompany_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fundmanagementcompany);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.FundManagementCompany)
}

// optional string UnderlyingSecurityID = 20;
void MDETFBasicInfo::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
void MDETFBasicInfo::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
void MDETFBasicInfo::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}
::std::string* MDETFBasicInfo::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityIDSource UnderlyingSecurityIDSource = 21;
void MDETFBasicInfo::clear_underlyingsecurityidsource() {
  underlyingsecurityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDETFBasicInfo::underlyingsecurityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(underlyingsecurityidsource_);
}
void MDETFBasicInfo::set_underlyingsecurityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  underlyingsecurityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.UnderlyingSecurityIDSource)
}

// optional double CreationRedemptionUnit = 22;
void MDETFBasicInfo::clear_creationredemptionunit() {
  creationredemptionunit_ = 0;
}
double MDETFBasicInfo::creationredemptionunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionUnit)
  return creationredemptionunit_;
}
void MDETFBasicInfo::set_creationredemptionunit(double value) {
  
  creationredemptionunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationRedemptionUnit)
}

// optional double EstimateCashComponent = 23;
void MDETFBasicInfo::clear_estimatecashcomponent() {
  estimatecashcomponent_ = 0;
}
double MDETFBasicInfo::estimatecashcomponent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.EstimateCashComponent)
  return estimatecashcomponent_;
}
void MDETFBasicInfo::set_estimatecashcomponent(double value) {
  
  estimatecashcomponent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.EstimateCashComponent)
}

// optional double MaxCashRatio = 24;
void MDETFBasicInfo::clear_maxcashratio() {
  maxcashratio_ = 0;
}
double MDETFBasicInfo::maxcashratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.MaxCashRatio)
  return maxcashratio_;
}
void MDETFBasicInfo::set_maxcashratio(double value) {
  
  maxcashratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.MaxCashRatio)
}

// optional bool IsPublish = 25;
void MDETFBasicInfo::clear_ispublish() {
  ispublish_ = false;
}
bool MDETFBasicInfo::ispublish() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsPublish)
  return ispublish_;
}
void MDETFBasicInfo::set_ispublish(bool value) {
  
  ispublish_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsPublish)
}

// optional bool IsAllowCreation = 26;
void MDETFBasicInfo::clear_isallowcreation() {
  isallowcreation_ = false;
}
bool MDETFBasicInfo::isallowcreation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowCreation)
  return isallowcreation_;
}
void MDETFBasicInfo::set_isallowcreation(bool value) {
  
  isallowcreation_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowCreation)
}

// optional bool IsAllowRedemption = 27;
void MDETFBasicInfo::clear_isallowredemption() {
  isallowredemption_ = false;
}
bool MDETFBasicInfo::isallowredemption() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowRedemption)
  return isallowredemption_;
}
void MDETFBasicInfo::set_isallowredemption(bool value) {
  
  isallowredemption_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.IsAllowRedemption)
}

// optional int64 RecordNum = 28;
void MDETFBasicInfo::clear_recordnum() {
  recordnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDETFBasicInfo::recordnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RecordNum)
  return recordnum_;
}
void MDETFBasicInfo::set_recordnum(::google::protobuf::int64 value) {
  
  recordnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RecordNum)
}

// optional string TradingDay = 29;
void MDETFBasicInfo::clear_tradingday() {
  tradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::tradingday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  return tradingday_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_tradingday(const ::std::string& value) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
void MDETFBasicInfo::set_tradingday(const char* value) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
void MDETFBasicInfo::set_tradingday(const char* value, size_t size) {
  
  tradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}
::std::string* MDETFBasicInfo::mutable_tradingday() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  return tradingday_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_tradingday() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
  
  return tradingday_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_tradingday(::std::string* tradingday) {
  if (tradingday != NULL) {
    
  } else {
    
  }
  tradingday_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingday);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.TradingDay)
}

// optional string PreTradingDay = 30;
void MDETFBasicInfo::clear_pretradingday() {
  pretradingday_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::pretradingday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  return pretradingday_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_pretradingday(const ::std::string& value) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
void MDETFBasicInfo::set_pretradingday(const char* value) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
void MDETFBasicInfo::set_pretradingday(const char* value, size_t size) {
  
  pretradingday_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}
::std::string* MDETFBasicInfo::mutable_pretradingday() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  return pretradingday_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_pretradingday() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
  
  return pretradingday_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_pretradingday(::std::string* pretradingday) {
  if (pretradingday != NULL) {
    
  } else {
    
  }
  pretradingday_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), pretradingday);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.PreTradingDay)
}

// optional double CashComponent = 31;
void MDETFBasicInfo::clear_cashcomponent() {
  cashcomponent_ = 0;
}
double MDETFBasicInfo::cashcomponent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CashComponent)
  return cashcomponent_;
}
void MDETFBasicInfo::set_cashcomponent(double value) {
  
  cashcomponent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CashComponent)
}

// optional double NAVperCU = 32;
void MDETFBasicInfo::clear_navpercu() {
  navpercu_ = 0;
}
double MDETFBasicInfo::navpercu() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NAVperCU)
  return navpercu_;
}
void MDETFBasicInfo::set_navpercu(double value) {
  
  navpercu_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NAVperCU)
}

// optional double NAV = 33;
void MDETFBasicInfo::clear_nav() {
  nav_ = 0;
}
double MDETFBasicInfo::nav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NAV)
  return nav_;
}
void MDETFBasicInfo::set_nav(double value) {
  
  nav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NAV)
}

// optional double DividendPerCU = 34;
void MDETFBasicInfo::clear_dividendpercu() {
  dividendpercu_ = 0;
}
double MDETFBasicInfo::dividendpercu() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.DividendPerCU)
  return dividendpercu_;
}
void MDETFBasicInfo::set_dividendpercu(double value) {
  
  dividendpercu_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.DividendPerCU)
}

// optional double CreationLimit = 35;
void MDETFBasicInfo::clear_creationlimit() {
  creationlimit_ = 0;
}
double MDETFBasicInfo::creationlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimit)
  return creationlimit_;
}
void MDETFBasicInfo::set_creationlimit(double value) {
  
  creationlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimit)
}

// optional double RedemptionLimit = 36;
void MDETFBasicInfo::clear_redemptionlimit() {
  redemptionlimit_ = 0;
}
double MDETFBasicInfo::redemptionlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimit)
  return redemptionlimit_;
}
void MDETFBasicInfo::set_redemptionlimit(double value) {
  
  redemptionlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimit)
}

// optional double CreationLimitPerUser = 37;
void MDETFBasicInfo::clear_creationlimitperuser() {
  creationlimitperuser_ = 0;
}
double MDETFBasicInfo::creationlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimitPerUser)
  return creationlimitperuser_;
}
void MDETFBasicInfo::set_creationlimitperuser(double value) {
  
  creationlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CreationLimitPerUser)
}

// optional double RedemptionLimitPerUser = 38;
void MDETFBasicInfo::clear_redemptionlimitperuser() {
  redemptionlimitperuser_ = 0;
}
double MDETFBasicInfo::redemptionlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimitPerUser)
  return redemptionlimitperuser_;
}
void MDETFBasicInfo::set_redemptionlimitperuser(double value) {
  
  redemptionlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.RedemptionLimitPerUser)
}

// optional double NetCreationLimit = 39;
void MDETFBasicInfo::clear_netcreationlimit() {
  netcreationlimit_ = 0;
}
double MDETFBasicInfo::netcreationlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimit)
  return netcreationlimit_;
}
void MDETFBasicInfo::set_netcreationlimit(double value) {
  
  netcreationlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimit)
}

// optional double NetRedemptionLimit = 40;
void MDETFBasicInfo::clear_netredemptionlimit() {
  netredemptionlimit_ = 0;
}
double MDETFBasicInfo::netredemptionlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimit)
  return netredemptionlimit_;
}
void MDETFBasicInfo::set_netredemptionlimit(double value) {
  
  netredemptionlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimit)
}

// optional double NetCreationLimitPerUser = 41;
void MDETFBasicInfo::clear_netcreationlimitperuser() {
  netcreationlimitperuser_ = 0;
}
double MDETFBasicInfo::netcreationlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimitPerUser)
  return netcreationlimitperuser_;
}
void MDETFBasicInfo::set_netcreationlimitperuser(double value) {
  
  netcreationlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetCreationLimitPerUser)
}

// optional double NetRedemptionLimitPerUser = 42;
void MDETFBasicInfo::clear_netredemptionlimitperuser() {
  netredemptionlimitperuser_ = 0;
}
double MDETFBasicInfo::netredemptionlimitperuser() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimitPerUser)
  return netredemptionlimitperuser_;
}
void MDETFBasicInfo::set_netredemptionlimitperuser(double value) {
  
  netredemptionlimitperuser_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.NetRedemptionLimitPerUser)
}

// repeated .com.htsc.mdc.insight.model.MDETFComponentDetail ETFComponents = 43;
int MDETFBasicInfo::etfcomponents_size() const {
  return etfcomponents_.size();
}
void MDETFBasicInfo::clear_etfcomponents() {
  etfcomponents_.Clear();
}
const ::com::htsc::mdc::insight::model::MDETFComponentDetail& MDETFBasicInfo::etfcomponents(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Get(index);
}
::com::htsc::mdc::insight::model::MDETFComponentDetail* MDETFBasicInfo::mutable_etfcomponents(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDETFComponentDetail* MDETFBasicInfo::add_etfcomponents() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >*
MDETFBasicInfo::mutable_etfcomponents() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return &etfcomponents_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDETFComponentDetail >&
MDETFBasicInfo::etfcomponents() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDETFBasicInfo.ETFComponents)
  return etfcomponents_;
}

// optional string FormerSymbol = 44;
void MDETFBasicInfo::clear_formersymbol() {
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFBasicInfo::formersymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  return formersymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_formersymbol(const ::std::string& value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
void MDETFBasicInfo::set_formersymbol(const char* value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
void MDETFBasicInfo::set_formersymbol(const char* value, size_t size) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}
::std::string* MDETFBasicInfo::mutable_formersymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  return formersymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFBasicInfo::release_formersymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
  
  return formersymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFBasicInfo::set_allocated_formersymbol(::std::string* formersymbol) {
  if (formersymbol != NULL) {
    
  } else {
    
  }
  formersymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), formersymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFBasicInfo.FormerSymbol)
}

// optional bool CrossMarket = 45;
void MDETFBasicInfo::clear_crossmarket() {
  crossmarket_ = false;
}
bool MDETFBasicInfo::crossmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossMarket)
  return crossmarket_;
}
void MDETFBasicInfo::set_crossmarket(bool value) {
  
  crossmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.CrossMarket)
}

// optional int32 DataMultiplePowerOf10 = 46;
void MDETFBasicInfo::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDETFBasicInfo::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFBasicInfo.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDETFBasicInfo::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFBasicInfo.DataMultiplePowerOf10)
}

inline const MDETFBasicInfo* MDETFBasicInfo::internal_default_instance() {
  return &MDETFBasicInfo_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDETFComponentDetail::kHTSCSecurityIDFieldNumber;
const int MDETFComponentDetail::kSecurityIDFieldNumber;
const int MDETFComponentDetail::kSymbolFieldNumber;
const int MDETFComponentDetail::kSecurityIDSourceFieldNumber;
const int MDETFComponentDetail::kComponentShareFieldNumber;
const int MDETFComponentDetail::kSubstituteFlagFieldNumber;
const int MDETFComponentDetail::kPremiumRatioFieldNumber;
const int MDETFComponentDetail::kCreationCashSubstituteFieldNumber;
const int MDETFComponentDetail::kRedemptionCashSubstituteFieldNumber;
const int MDETFComponentDetail::kTotalCashSubstituteFieldNumber;
const int MDETFComponentDetail::kDiscountRatioFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDETFComponentDetail::MDETFComponentDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDETFComponentDetail)
}

void MDETFComponentDetail::InitAsDefaultInstance() {
}

MDETFComponentDetail::MDETFComponentDetail(const MDETFComponentDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDETFComponentDetail)
}

void MDETFComponentDetail::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&componentshare_, 0, reinterpret_cast<char*>(&discountratio_) -
    reinterpret_cast<char*>(&componentshare_) + sizeof(discountratio_));
  _cached_size_ = 0;
}

MDETFComponentDetail::~MDETFComponentDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDETFComponentDetail)
  SharedDtor();
}

void MDETFComponentDetail::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDETFComponentDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDETFComponentDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDETFComponentDetail_descriptor_;
}

const MDETFComponentDetail& MDETFComponentDetail::default_instance() {
  protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDETFComponentDetail> MDETFComponentDetail_default_instance_;

MDETFComponentDetail* MDETFComponentDetail::New(::google::protobuf::Arena* arena) const {
  MDETFComponentDetail* n = new MDETFComponentDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDETFComponentDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDETFComponentDetail, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDETFComponentDetail*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(componentshare_, creationcashsubstitute_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(redemptioncashsubstitute_, discountratio_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDETFComponentDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_SecurityID;
        break;
      }

      // optional string SecurityID = 2;
      case 2: {
        if (tag == 18) {
         parse_SecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securityid().data(), this->securityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Symbol;
        break;
      }

      // optional string Symbol = 3;
      case 3: {
        if (tag == 26) {
         parse_Symbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbol().data(), this->symbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
      case 4: {
        if (tag == 32) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(41)) goto parse_ComponentShare;
        break;
      }

      // optional double ComponentShare = 5;
      case 5: {
        if (tag == 41) {
         parse_ComponentShare:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &componentshare_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_SubstituteFlag;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
      case 6: {
        if (tag == 48) {
         parse_SubstituteFlag:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_substituteflag(static_cast< ::com::htsc::mdc::insight::model::MDETFSubstituteFlag >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(57)) goto parse_PremiumRatio;
        break;
      }

      // optional double PremiumRatio = 7;
      case 7: {
        if (tag == 57) {
         parse_PremiumRatio:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &premiumratio_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(65)) goto parse_CreationCashSubstitute;
        break;
      }

      // optional double CreationCashSubstitute = 8;
      case 8: {
        if (tag == 65) {
         parse_CreationCashSubstitute:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &creationcashsubstitute_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(73)) goto parse_RedemptionCashSubstitute;
        break;
      }

      // optional double RedemptionCashSubstitute = 9;
      case 9: {
        if (tag == 73) {
         parse_RedemptionCashSubstitute:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &redemptioncashsubstitute_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(81)) goto parse_TotalCashSubstitute;
        break;
      }

      // optional double TotalCashSubstitute = 10;
      case 10: {
        if (tag == 81) {
         parse_TotalCashSubstitute:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalcashsubstitute_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_DiscountRatio;
        break;
      }

      // optional double DiscountRatio = 11;
      case 11: {
        if (tag == 89) {
         parse_DiscountRatio:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &discountratio_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDETFComponentDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDETFComponentDetail)
  return false;
#undef DO_
}

void MDETFComponentDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->securityid(), output);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->symbol(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      4, this->securityidsource(), output);
  }

  // optional double ComponentShare = 5;
  if (this->componentshare() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->componentshare(), output);
  }

  // optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
  if (this->substituteflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->substituteflag(), output);
  }

  // optional double PremiumRatio = 7;
  if (this->premiumratio() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->premiumratio(), output);
  }

  // optional double CreationCashSubstitute = 8;
  if (this->creationcashsubstitute() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->creationcashsubstitute(), output);
  }

  // optional double RedemptionCashSubstitute = 9;
  if (this->redemptioncashsubstitute() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->redemptioncashsubstitute(), output);
  }

  // optional double TotalCashSubstitute = 10;
  if (this->totalcashsubstitute() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(10, this->totalcashsubstitute(), output);
  }

  // optional double DiscountRatio = 11;
  if (this->discountratio() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->discountratio(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDETFComponentDetail)
}

::google::protobuf::uint8* MDETFComponentDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->securityid(), target);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->symbol(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      4, this->securityidsource(), target);
  }

  // optional double ComponentShare = 5;
  if (this->componentshare() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->componentshare(), target);
  }

  // optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
  if (this->substituteflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->substituteflag(), target);
  }

  // optional double PremiumRatio = 7;
  if (this->premiumratio() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->premiumratio(), target);
  }

  // optional double CreationCashSubstitute = 8;
  if (this->creationcashsubstitute() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->creationcashsubstitute(), target);
  }

  // optional double RedemptionCashSubstitute = 9;
  if (this->redemptioncashsubstitute() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->redemptioncashsubstitute(), target);
  }

  // optional double TotalCashSubstitute = 10;
  if (this->totalcashsubstitute() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(10, this->totalcashsubstitute(), target);
  }

  // optional double DiscountRatio = 11;
  if (this->discountratio() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->discountratio(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDETFComponentDetail)
  return target;
}

size_t MDETFComponentDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securityid());
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbol());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional double ComponentShare = 5;
  if (this->componentshare() != 0) {
    total_size += 1 + 8;
  }

  // optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
  if (this->substituteflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->substituteflag());
  }

  // optional double PremiumRatio = 7;
  if (this->premiumratio() != 0) {
    total_size += 1 + 8;
  }

  // optional double CreationCashSubstitute = 8;
  if (this->creationcashsubstitute() != 0) {
    total_size += 1 + 8;
  }

  // optional double RedemptionCashSubstitute = 9;
  if (this->redemptioncashsubstitute() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalCashSubstitute = 10;
  if (this->totalcashsubstitute() != 0) {
    total_size += 1 + 8;
  }

  // optional double DiscountRatio = 11;
  if (this->discountratio() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDETFComponentDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDETFComponentDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDETFComponentDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDETFComponentDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDETFComponentDetail)
    UnsafeMergeFrom(*source);
  }
}

void MDETFComponentDetail::MergeFrom(const MDETFComponentDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDETFComponentDetail::UnsafeMergeFrom(const MDETFComponentDetail& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securityid().size() > 0) {

    securityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securityid_);
  }
  if (from.symbol().size() > 0) {

    symbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbol_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.componentshare() != 0) {
    set_componentshare(from.componentshare());
  }
  if (from.substituteflag() != 0) {
    set_substituteflag(from.substituteflag());
  }
  if (from.premiumratio() != 0) {
    set_premiumratio(from.premiumratio());
  }
  if (from.creationcashsubstitute() != 0) {
    set_creationcashsubstitute(from.creationcashsubstitute());
  }
  if (from.redemptioncashsubstitute() != 0) {
    set_redemptioncashsubstitute(from.redemptioncashsubstitute());
  }
  if (from.totalcashsubstitute() != 0) {
    set_totalcashsubstitute(from.totalcashsubstitute());
  }
  if (from.discountratio() != 0) {
    set_discountratio(from.discountratio());
  }
}

void MDETFComponentDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDETFComponentDetail::CopyFrom(const MDETFComponentDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDETFComponentDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDETFComponentDetail::IsInitialized() const {

  return true;
}

void MDETFComponentDetail::Swap(MDETFComponentDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDETFComponentDetail::InternalSwap(MDETFComponentDetail* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  securityid_.Swap(&other->securityid_);
  symbol_.Swap(&other->symbol_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(componentshare_, other->componentshare_);
  std::swap(substituteflag_, other->substituteflag_);
  std::swap(premiumratio_, other->premiumratio_);
  std::swap(creationcashsubstitute_, other->creationcashsubstitute_);
  std::swap(redemptioncashsubstitute_, other->redemptioncashsubstitute_);
  std::swap(totalcashsubstitute_, other->totalcashsubstitute_);
  std::swap(discountratio_, other->discountratio_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDETFComponentDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDETFComponentDetail_descriptor_;
  metadata.reflection = MDETFComponentDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDETFComponentDetail

// optional string HTSCSecurityID = 1;
void MDETFComponentDetail::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFComponentDetail::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
void MDETFComponentDetail::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
void MDETFComponentDetail::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}
::std::string* MDETFComponentDetail::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFComponentDetail::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.HTSCSecurityID)
}

// optional string SecurityID = 2;
void MDETFComponentDetail::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFComponentDetail::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
void MDETFComponentDetail::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
void MDETFComponentDetail::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}
::std::string* MDETFComponentDetail::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFComponentDetail::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.SecurityID)
}

// optional string Symbol = 3;
void MDETFComponentDetail::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDETFComponentDetail::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
void MDETFComponentDetail::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
void MDETFComponentDetail::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}
::std::string* MDETFComponentDetail::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDETFComponentDetail::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDETFComponentDetail::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDETFComponentDetail.Symbol)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 4;
void MDETFComponentDetail::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDETFComponentDetail::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDETFComponentDetail::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.securityIDSource)
}

// optional double ComponentShare = 5;
void MDETFComponentDetail::clear_componentshare() {
  componentshare_ = 0;
}
double MDETFComponentDetail::componentshare() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.ComponentShare)
  return componentshare_;
}
void MDETFComponentDetail::set_componentshare(double value) {
  
  componentshare_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.ComponentShare)
}

// optional .com.htsc.mdc.insight.model.MDETFSubstituteFlag SubstituteFlag = 6;
void MDETFComponentDetail::clear_substituteflag() {
  substituteflag_ = 0;
}
::com::htsc::mdc::insight::model::MDETFSubstituteFlag MDETFComponentDetail::substituteflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.SubstituteFlag)
  return static_cast< ::com::htsc::mdc::insight::model::MDETFSubstituteFlag >(substituteflag_);
}
void MDETFComponentDetail::set_substituteflag(::com::htsc::mdc::insight::model::MDETFSubstituteFlag value) {
  
  substituteflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.SubstituteFlag)
}

// optional double PremiumRatio = 7;
void MDETFComponentDetail::clear_premiumratio() {
  premiumratio_ = 0;
}
double MDETFComponentDetail::premiumratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.PremiumRatio)
  return premiumratio_;
}
void MDETFComponentDetail::set_premiumratio(double value) {
  
  premiumratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.PremiumRatio)
}

// optional double CreationCashSubstitute = 8;
void MDETFComponentDetail::clear_creationcashsubstitute() {
  creationcashsubstitute_ = 0;
}
double MDETFComponentDetail::creationcashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.CreationCashSubstitute)
  return creationcashsubstitute_;
}
void MDETFComponentDetail::set_creationcashsubstitute(double value) {
  
  creationcashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.CreationCashSubstitute)
}

// optional double RedemptionCashSubstitute = 9;
void MDETFComponentDetail::clear_redemptioncashsubstitute() {
  redemptioncashsubstitute_ = 0;
}
double MDETFComponentDetail::redemptioncashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.RedemptionCashSubstitute)
  return redemptioncashsubstitute_;
}
void MDETFComponentDetail::set_redemptioncashsubstitute(double value) {
  
  redemptioncashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.RedemptionCashSubstitute)
}

// optional double TotalCashSubstitute = 10;
void MDETFComponentDetail::clear_totalcashsubstitute() {
  totalcashsubstitute_ = 0;
}
double MDETFComponentDetail::totalcashsubstitute() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.TotalCashSubstitute)
  return totalcashsubstitute_;
}
void MDETFComponentDetail::set_totalcashsubstitute(double value) {
  
  totalcashsubstitute_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.TotalCashSubstitute)
}

// optional double DiscountRatio = 11;
void MDETFComponentDetail::clear_discountratio() {
  discountratio_ = 0;
}
double MDETFComponentDetail::discountratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDETFComponentDetail.DiscountRatio)
  return discountratio_;
}
void MDETFComponentDetail::set_discountratio(double value) {
  
  discountratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDETFComponentDetail.DiscountRatio)
}

inline const MDETFComponentDetail* MDETFComponentDetail::internal_default_instance() {
  return &MDETFComponentDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
