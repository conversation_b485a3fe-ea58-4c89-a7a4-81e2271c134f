// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsRateSnapshot.proto

#ifndef PROTOBUF_MDCfetsRateSnapshot_2eproto__INCLUDED
#define PROTOBUF_MDCfetsRateSnapshot_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsRateSnapshot_2eproto();
void protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto();
void protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto();

class MDCfetsRateSnapshot;
class RateSwapSnapshot;
class StandardisedRateSwapSnapshot;

// ===================================================================

class MDCfetsRateSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsRateSnapshot) */ {
 public:
  MDCfetsRateSnapshot();
  virtual ~MDCfetsRateSnapshot();

  MDCfetsRateSnapshot(const MDCfetsRateSnapshot& from);

  inline MDCfetsRateSnapshot& operator=(const MDCfetsRateSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsRateSnapshot& default_instance();

  static const MDCfetsRateSnapshot* internal_default_instance();

  void Swap(MDCfetsRateSnapshot* other);

  // implements Message ----------------------------------------------

  inline MDCfetsRateSnapshot* New() const { return New(NULL); }

  MDCfetsRateSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsRateSnapshot& from);
  void MergeFrom(const MDCfetsRateSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsRateSnapshot* other);
  void UnsafeMergeFrom(const MDCfetsRateSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 DataMultiplePowerOf10 = 9;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 9;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 RateSnapshotType = 16;
  void clear_ratesnapshottype();
  static const int kRateSnapshotTypeFieldNumber = 16;
  ::google::protobuf::int32 ratesnapshottype() const;
  void set_ratesnapshottype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
  bool has_rateswapsnapshot() const;
  void clear_rateswapsnapshot();
  static const int kRateSwapSnapshotFieldNumber = 17;
  const ::com::htsc::mdc::insight::model::RateSwapSnapshot& rateswapsnapshot() const;
  ::com::htsc::mdc::insight::model::RateSwapSnapshot* mutable_rateswapsnapshot();
  ::com::htsc::mdc::insight::model::RateSwapSnapshot* release_rateswapsnapshot();
  void set_allocated_rateswapsnapshot(::com::htsc::mdc::insight::model::RateSwapSnapshot* rateswapsnapshot);

  // optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
  bool has_standardisedrateswapsnapshot() const;
  void clear_standardisedrateswapsnapshot();
  static const int kStandardisedRateSwapSnapshotFieldNumber = 18;
  const ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot& standardisedrateswapsnapshot() const;
  ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* mutable_standardisedrateswapsnapshot();
  ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* release_standardisedrateswapsnapshot();
  void set_allocated_standardisedrateswapsnapshot(::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* standardisedrateswapsnapshot);

  // optional int64 MessageNumber = 100;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 100;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::RateSwapSnapshot* rateswapsnapshot_;
  ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* standardisedrateswapsnapshot_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 ratesnapshottype_;
  ::google::protobuf::int64 messagenumber_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsRateSnapshot> MDCfetsRateSnapshot_default_instance_;

// -------------------------------------------------------------------

class RateSwapSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.RateSwapSnapshot) */ {
 public:
  RateSwapSnapshot();
  virtual ~RateSwapSnapshot();

  RateSwapSnapshot(const RateSwapSnapshot& from);

  inline RateSwapSnapshot& operator=(const RateSwapSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const RateSwapSnapshot& default_instance();

  static const RateSwapSnapshot* internal_default_instance();

  void Swap(RateSwapSnapshot* other);

  // implements Message ----------------------------------------------

  inline RateSwapSnapshot* New() const { return New(NULL); }

  RateSwapSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const RateSwapSnapshot& from);
  void MergeFrom(const RateSwapSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RateSwapSnapshot* other);
  void UnsafeMergeFrom(const RateSwapSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeMethod = 1;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 1;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional string TradeTerm = 2;
  void clear_tradeterm();
  static const int kTradeTermFieldNumber = 2;
  const ::std::string& tradeterm() const;
  void set_tradeterm(const ::std::string& value);
  void set_tradeterm(const char* value);
  void set_tradeterm(const char* value, size_t size);
  ::std::string* mutable_tradeterm();
  ::std::string* release_tradeterm();
  void set_allocated_tradeterm(::std::string* tradeterm);

  // optional double PreCloseRate = 11;
  void clear_precloserate();
  static const int kPreCloseRateFieldNumber = 11;
  double precloserate() const;
  void set_precloserate(double value);

  // optional double PreWeightedAvgRate = 12;
  void clear_preweightedavgrate();
  static const int kPreWeightedAvgRateFieldNumber = 12;
  double preweightedavgrate() const;
  void set_preweightedavgrate(double value);

  // optional double OpenRate = 13;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 13;
  double openrate() const;
  void set_openrate(double value);

  // optional double LastRate = 14;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 14;
  double lastrate() const;
  void set_lastrate(double value);

  // optional double HighRate = 15;
  void clear_highrate();
  static const int kHighRateFieldNumber = 15;
  double highrate() const;
  void set_highrate(double value);

  // optional double LowRate = 16;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 16;
  double lowrate() const;
  void set_lowrate(double value);

  // optional double CloseRate = 17;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 17;
  double closerate() const;
  void set_closerate(double value);

  // optional double WeightedAvgRate = 18;
  void clear_weightedavgrate();
  static const int kWeightedAvgRateFieldNumber = 18;
  double weightedavgrate() const;
  void set_weightedavgrate(double value);

  // optional double LastVolumeTrade = 19;
  void clear_lastvolumetrade();
  static const int kLastVolumeTradeFieldNumber = 19;
  double lastvolumetrade() const;
  void set_lastvolumetrade(double value);

  // optional double TotalVolumeTrade = 20;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 20;
  double totalvolumetrade() const;
  void set_totalvolumetrade(double value);

  // optional string BenchmarkCurveName = 21;
  void clear_benchmarkcurvename();
  static const int kBenchmarkCurveNameFieldNumber = 21;
  const ::std::string& benchmarkcurvename() const;
  void set_benchmarkcurvename(const ::std::string& value);
  void set_benchmarkcurvename(const char* value);
  void set_benchmarkcurvename(const char* value, size_t size);
  ::std::string* mutable_benchmarkcurvename();
  ::std::string* release_benchmarkcurvename();
  void set_allocated_benchmarkcurvename(::std::string* benchmarkcurvename);

  // optional double SessionReferenceRate = 22;
  void clear_sessionreferencerate();
  static const int kSessionReferenceRateFieldNumber = 22;
  double sessionreferencerate() const;
  void set_sessionreferencerate(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.RateSwapSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr tradeterm_;
  ::google::protobuf::internal::ArenaStringPtr benchmarkcurvename_;
  double precloserate_;
  double preweightedavgrate_;
  double openrate_;
  double lastrate_;
  double highrate_;
  double lowrate_;
  double closerate_;
  double weightedavgrate_;
  double lastvolumetrade_;
  double totalvolumetrade_;
  double sessionreferencerate_;
  ::google::protobuf::int32 trademethod_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<RateSwapSnapshot> RateSwapSnapshot_default_instance_;

// -------------------------------------------------------------------

class StandardisedRateSwapSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot) */ {
 public:
  StandardisedRateSwapSnapshot();
  virtual ~StandardisedRateSwapSnapshot();

  StandardisedRateSwapSnapshot(const StandardisedRateSwapSnapshot& from);

  inline StandardisedRateSwapSnapshot& operator=(const StandardisedRateSwapSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StandardisedRateSwapSnapshot& default_instance();

  static const StandardisedRateSwapSnapshot* internal_default_instance();

  void Swap(StandardisedRateSwapSnapshot* other);

  // implements Message ----------------------------------------------

  inline StandardisedRateSwapSnapshot* New() const { return New(NULL); }

  StandardisedRateSwapSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StandardisedRateSwapSnapshot& from);
  void MergeFrom(const StandardisedRateSwapSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StandardisedRateSwapSnapshot* other);
  void UnsafeMergeFrom(const StandardisedRateSwapSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeMethod = 1;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 1;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional double OpenRate = 11;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 11;
  double openrate() const;
  void set_openrate(double value);

  // optional double HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  double highrate() const;
  void set_highrate(double value);

  // optional double LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  double lowrate() const;
  void set_lowrate(double value);

  // optional double LastRate = 14;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 14;
  double lastrate() const;
  void set_lastrate(double value);

  // optional double LastVolumeTrade = 15;
  void clear_lastvolumetrade();
  static const int kLastVolumeTradeFieldNumber = 15;
  double lastvolumetrade() const;
  void set_lastvolumetrade(double value);

  // optional double TotalVolumeTrade = 16;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 16;
  double totalvolumetrade() const;
  void set_totalvolumetrade(double value);

  // optional double SettleRate = 17;
  void clear_settlerate();
  static const int kSettleRateFieldNumber = 17;
  double settlerate() const;
  void set_settlerate(double value);

  // optional string SettleRateDate = 18;
  void clear_settleratedate();
  static const int kSettleRateDateFieldNumber = 18;
  const ::std::string& settleratedate() const;
  void set_settleratedate(const ::std::string& value);
  void set_settleratedate(const char* value);
  void set_settleratedate(const char* value, size_t size);
  ::std::string* mutable_settleratedate();
  ::std::string* release_settleratedate();
  void set_allocated_settleratedate(::std::string* settleratedate);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr settleratedate_;
  double openrate_;
  double highrate_;
  double lowrate_;
  double lastrate_;
  double lastvolumetrade_;
  double totalvolumetrade_;
  double settlerate_;
  ::google::protobuf::int32 trademethod_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StandardisedRateSwapSnapshot> StandardisedRateSwapSnapshot_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsRateSnapshot

// optional string HTSCSecurityID = 1;
inline void MDCfetsRateSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsRateSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
inline void MDCfetsRateSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
inline void MDCfetsRateSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
inline ::std::string* MDCfetsRateSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsRateSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsRateSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsRateSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsRateSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsRateSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsRateSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsRateSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsRateSnapshot::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsRateSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDDate)
  return mddate_;
}
inline void MDCfetsRateSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsRateSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsRateSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDTime)
  return mdtime_;
}
inline void MDCfetsRateSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsRateSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsRateSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsRateSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsRateSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsRateSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
inline void MDCfetsRateSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
inline void MDCfetsRateSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
inline ::std::string* MDCfetsRateSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsRateSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsRateSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsRateSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
inline void MDCfetsRateSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
inline void MDCfetsRateSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
inline ::std::string* MDCfetsRateSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsRateSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsRateSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
inline void MDCfetsRateSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsRateSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsRateSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataMultiplePowerOf10)
}

// optional int32 RateSnapshotType = 16;
inline void MDCfetsRateSnapshot::clear_ratesnapshottype() {
  ratesnapshottype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsRateSnapshot::ratesnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSnapshotType)
  return ratesnapshottype_;
}
inline void MDCfetsRateSnapshot::set_ratesnapshottype(::google::protobuf::int32 value) {
  
  ratesnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSnapshotType)
}

// optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
inline bool MDCfetsRateSnapshot::has_rateswapsnapshot() const {
  return this != internal_default_instance() && rateswapsnapshot_ != NULL;
}
inline void MDCfetsRateSnapshot::clear_rateswapsnapshot() {
  if (GetArenaNoVirtual() == NULL && rateswapsnapshot_ != NULL) delete rateswapsnapshot_;
  rateswapsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::RateSwapSnapshot& MDCfetsRateSnapshot::rateswapsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  return rateswapsnapshot_ != NULL ? *rateswapsnapshot_
                         : *::com::htsc::mdc::insight::model::RateSwapSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::RateSwapSnapshot* MDCfetsRateSnapshot::mutable_rateswapsnapshot() {
  
  if (rateswapsnapshot_ == NULL) {
    rateswapsnapshot_ = new ::com::htsc::mdc::insight::model::RateSwapSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  return rateswapsnapshot_;
}
inline ::com::htsc::mdc::insight::model::RateSwapSnapshot* MDCfetsRateSnapshot::release_rateswapsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  
  ::com::htsc::mdc::insight::model::RateSwapSnapshot* temp = rateswapsnapshot_;
  rateswapsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsRateSnapshot::set_allocated_rateswapsnapshot(::com::htsc::mdc::insight::model::RateSwapSnapshot* rateswapsnapshot) {
  delete rateswapsnapshot_;
  rateswapsnapshot_ = rateswapsnapshot;
  if (rateswapsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
}

// optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
inline bool MDCfetsRateSnapshot::has_standardisedrateswapsnapshot() const {
  return this != internal_default_instance() && standardisedrateswapsnapshot_ != NULL;
}
inline void MDCfetsRateSnapshot::clear_standardisedrateswapsnapshot() {
  if (GetArenaNoVirtual() == NULL && standardisedrateswapsnapshot_ != NULL) delete standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot& MDCfetsRateSnapshot::standardisedrateswapsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  return standardisedrateswapsnapshot_ != NULL ? *standardisedrateswapsnapshot_
                         : *::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* MDCfetsRateSnapshot::mutable_standardisedrateswapsnapshot() {
  
  if (standardisedrateswapsnapshot_ == NULL) {
    standardisedrateswapsnapshot_ = new ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  return standardisedrateswapsnapshot_;
}
inline ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* MDCfetsRateSnapshot::release_standardisedrateswapsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  
  ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* temp = standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsRateSnapshot::set_allocated_standardisedrateswapsnapshot(::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* standardisedrateswapsnapshot) {
  delete standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = standardisedrateswapsnapshot;
  if (standardisedrateswapsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
}

// optional int64 MessageNumber = 100;
inline void MDCfetsRateSnapshot::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsRateSnapshot::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MessageNumber)
  return messagenumber_;
}
inline void MDCfetsRateSnapshot::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MessageNumber)
}

inline const MDCfetsRateSnapshot* MDCfetsRateSnapshot::internal_default_instance() {
  return &MDCfetsRateSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// RateSwapSnapshot

// optional int32 TradeMethod = 1;
inline void RateSwapSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 RateSwapSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeMethod)
  return trademethod_;
}
inline void RateSwapSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeMethod)
}

// optional string TradeTerm = 2;
inline void RateSwapSnapshot::clear_tradeterm() {
  tradeterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapSnapshot::tradeterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  return tradeterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapSnapshot::set_tradeterm(const ::std::string& value) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
inline void RateSwapSnapshot::set_tradeterm(const char* value) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
inline void RateSwapSnapshot::set_tradeterm(const char* value, size_t size) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
inline ::std::string* RateSwapSnapshot::mutable_tradeterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  return tradeterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapSnapshot::release_tradeterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  
  return tradeterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapSnapshot::set_allocated_tradeterm(::std::string* tradeterm) {
  if (tradeterm != NULL) {
    
  } else {
    
  }
  tradeterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}

// optional double PreCloseRate = 11;
inline void RateSwapSnapshot::clear_precloserate() {
  precloserate_ = 0;
}
inline double RateSwapSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.PreCloseRate)
  return precloserate_;
}
inline void RateSwapSnapshot::set_precloserate(double value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.PreCloseRate)
}

// optional double PreWeightedAvgRate = 12;
inline void RateSwapSnapshot::clear_preweightedavgrate() {
  preweightedavgrate_ = 0;
}
inline double RateSwapSnapshot::preweightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.PreWeightedAvgRate)
  return preweightedavgrate_;
}
inline void RateSwapSnapshot::set_preweightedavgrate(double value) {
  
  preweightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.PreWeightedAvgRate)
}

// optional double OpenRate = 13;
inline void RateSwapSnapshot::clear_openrate() {
  openrate_ = 0;
}
inline double RateSwapSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.OpenRate)
  return openrate_;
}
inline void RateSwapSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.OpenRate)
}

// optional double LastRate = 14;
inline void RateSwapSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
inline double RateSwapSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LastRate)
  return lastrate_;
}
inline void RateSwapSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LastRate)
}

// optional double HighRate = 15;
inline void RateSwapSnapshot::clear_highrate() {
  highrate_ = 0;
}
inline double RateSwapSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.HighRate)
  return highrate_;
}
inline void RateSwapSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.HighRate)
}

// optional double LowRate = 16;
inline void RateSwapSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
inline double RateSwapSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LowRate)
  return lowrate_;
}
inline void RateSwapSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LowRate)
}

// optional double CloseRate = 17;
inline void RateSwapSnapshot::clear_closerate() {
  closerate_ = 0;
}
inline double RateSwapSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.CloseRate)
  return closerate_;
}
inline void RateSwapSnapshot::set_closerate(double value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.CloseRate)
}

// optional double WeightedAvgRate = 18;
inline void RateSwapSnapshot::clear_weightedavgrate() {
  weightedavgrate_ = 0;
}
inline double RateSwapSnapshot::weightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.WeightedAvgRate)
  return weightedavgrate_;
}
inline void RateSwapSnapshot::set_weightedavgrate(double value) {
  
  weightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.WeightedAvgRate)
}

// optional double LastVolumeTrade = 19;
inline void RateSwapSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
inline double RateSwapSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
inline void RateSwapSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LastVolumeTrade)
}

// optional double TotalVolumeTrade = 20;
inline void RateSwapSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
inline double RateSwapSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void RateSwapSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TotalVolumeTrade)
}

// optional string BenchmarkCurveName = 21;
inline void RateSwapSnapshot::clear_benchmarkcurvename() {
  benchmarkcurvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RateSwapSnapshot::benchmarkcurvename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  return benchmarkcurvename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapSnapshot::set_benchmarkcurvename(const ::std::string& value) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
inline void RateSwapSnapshot::set_benchmarkcurvename(const char* value) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
inline void RateSwapSnapshot::set_benchmarkcurvename(const char* value, size_t size) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
inline ::std::string* RateSwapSnapshot::mutable_benchmarkcurvename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  return benchmarkcurvename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RateSwapSnapshot::release_benchmarkcurvename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  
  return benchmarkcurvename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RateSwapSnapshot::set_allocated_benchmarkcurvename(::std::string* benchmarkcurvename) {
  if (benchmarkcurvename != NULL) {
    
  } else {
    
  }
  benchmarkcurvename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), benchmarkcurvename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}

// optional double SessionReferenceRate = 22;
inline void RateSwapSnapshot::clear_sessionreferencerate() {
  sessionreferencerate_ = 0;
}
inline double RateSwapSnapshot::sessionreferencerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.SessionReferenceRate)
  return sessionreferencerate_;
}
inline void RateSwapSnapshot::set_sessionreferencerate(double value) {
  
  sessionreferencerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.SessionReferenceRate)
}

inline const RateSwapSnapshot* RateSwapSnapshot::internal_default_instance() {
  return &RateSwapSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// StandardisedRateSwapSnapshot

// optional int32 TradeMethod = 1;
inline void StandardisedRateSwapSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 StandardisedRateSwapSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TradeMethod)
  return trademethod_;
}
inline void StandardisedRateSwapSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TradeMethod)
}

// optional double OpenRate = 11;
inline void StandardisedRateSwapSnapshot::clear_openrate() {
  openrate_ = 0;
}
inline double StandardisedRateSwapSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.OpenRate)
  return openrate_;
}
inline void StandardisedRateSwapSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.OpenRate)
}

// optional double HighRate = 12;
inline void StandardisedRateSwapSnapshot::clear_highrate() {
  highrate_ = 0;
}
inline double StandardisedRateSwapSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.HighRate)
  return highrate_;
}
inline void StandardisedRateSwapSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.HighRate)
}

// optional double LowRate = 13;
inline void StandardisedRateSwapSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
inline double StandardisedRateSwapSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LowRate)
  return lowrate_;
}
inline void StandardisedRateSwapSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LowRate)
}

// optional double LastRate = 14;
inline void StandardisedRateSwapSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
inline double StandardisedRateSwapSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastRate)
  return lastrate_;
}
inline void StandardisedRateSwapSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastRate)
}

// optional double LastVolumeTrade = 15;
inline void StandardisedRateSwapSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
inline double StandardisedRateSwapSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
inline void StandardisedRateSwapSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastVolumeTrade)
}

// optional double TotalVolumeTrade = 16;
inline void StandardisedRateSwapSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
inline double StandardisedRateSwapSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void StandardisedRateSwapSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TotalVolumeTrade)
}

// optional double SettleRate = 17;
inline void StandardisedRateSwapSnapshot::clear_settlerate() {
  settlerate_ = 0;
}
inline double StandardisedRateSwapSnapshot::settlerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRate)
  return settlerate_;
}
inline void StandardisedRateSwapSnapshot::set_settlerate(double value) {
  
  settlerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRate)
}

// optional string SettleRateDate = 18;
inline void StandardisedRateSwapSnapshot::clear_settleratedate() {
  settleratedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& StandardisedRateSwapSnapshot::settleratedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  return settleratedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void StandardisedRateSwapSnapshot::set_settleratedate(const ::std::string& value) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
inline void StandardisedRateSwapSnapshot::set_settleratedate(const char* value) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
inline void StandardisedRateSwapSnapshot::set_settleratedate(const char* value, size_t size) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
inline ::std::string* StandardisedRateSwapSnapshot::mutable_settleratedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  return settleratedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* StandardisedRateSwapSnapshot::release_settleratedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  
  return settleratedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void StandardisedRateSwapSnapshot::set_allocated_settleratedate(::std::string* settleratedate) {
  if (settleratedate != NULL) {
    
  } else {
    
  }
  settleratedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settleratedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}

inline const StandardisedRateSwapSnapshot* StandardisedRateSwapSnapshot::internal_default_instance() {
  return &StandardisedRateSwapSnapshot_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsRateSnapshot_2eproto__INCLUDED
