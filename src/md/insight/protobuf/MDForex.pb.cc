// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDForex.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDForex.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* ForexEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ForexEntry_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDForex_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDForex_2eproto() {
  protobuf_AddDesc_MDForex_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDForex.proto");
  GOOGLE_CHECK(file != NULL);
  MDForex_descriptor_ = file->message_type(0);
  static const int MDForex_offsets_[36] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, midpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, buycurrency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, sellcurrency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, tenor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, settledate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, forexentries_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, mdreqid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, delaytype_),
  };
  MDForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDForex_descriptor_,
      MDForex::internal_default_instance(),
      MDForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDForex, _internal_metadata_));
  ForexEntry_descriptor_ = file->message_type(1);
  static const int ForexEntry_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForexEntry, mdentrytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForexEntry, mdentrypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForexEntry, mdentrysize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForexEntry, mdentryid_),
  };
  ForexEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ForexEntry_descriptor_,
      ForexEntry::internal_default_instance(),
      ForexEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ForexEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForexEntry, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDForex_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDForex_descriptor_, MDForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ForexEntry_descriptor_, ForexEntry::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDForex_2eproto() {
  MDForex_default_instance_.Shutdown();
  delete MDForex_reflection_;
  ForexEntry_default_instance_.Shutdown();
  delete ForexEntry_reflection_;
}

void protobuf_InitDefaults_MDForex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ForexEntry_default_instance_.DefaultConstruct();
  MDForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForexEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDForex_2eproto_once_);
void protobuf_InitDefaults_MDForex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDForex_2eproto_once_,
                 &protobuf_InitDefaults_MDForex_2eproto_impl);
}
void protobuf_AddDesc_MDForex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDForex_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rMDForex.proto\022\032com.htsc.mdc.insight.mo"
    "del\032\027ESecurityIDSource.proto\032\023ESecurityT"
    "ype.proto\"\225\007\n\007MDForex\022\026\n\016HTSCSecurityID\030"
    "\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n"
    "\rDataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode"
    "\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\0227\n\014secur"
    "ityType\030\007 \001(\0162!.com.htsc.mdc.model.ESecu"
    "rityType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022"
    "\n\nPreClosePx\030\n \001(\003\022\030\n\020TotalVolumeTrade\030\013"
    " \001(\003\022\027\n\017TotalValueTrade\030\014 \001(\003\022\016\n\006LastPx\030"
    "\r \001(\003\022\016\n\006OpenPx\030\016 \001(\003\022\017\n\007ClosePx\030\017 \001(\003\022\016"
    "\n\006HighPx\030\020 \001(\003\022\r\n\005LowPx\030\021 \001(\003\022\024\n\014Exchang"
    "eDate\030\022 \001(\005\022\024\n\014ExchangeTime\030\023 \001(\005\022\031\n\rBuy"
    "PriceQueue\0303 \003(\003B\002\020\001\022\034\n\020BuyOrderQtyQueue"
    "\0304 \003(\003B\002\020\001\022\032\n\016SellPriceQueue\0305 \003(\003B\002\020\001\022\035"
    "\n\021SellOrderQtyQueue\0306 \003(\003B\002\020\001\022\031\n\rBuyOrde"
    "rQueue\0307 \003(\003B\002\020\001\022\032\n\016SellOrderQueue\0308 \003(\003"
    "B\002\020\001\022\035\n\021BuyNumOrdersQueue\0309 \003(\003B\002\020\001\022\036\n\022S"
    "ellNumOrdersQueue\030: \003(\003B\002\020\001\022\035\n\025DataMulti"
    "plePowerOf10\030; \001(\005\022\r\n\005MidPx\030< \001(\003\022\023\n\013Buy"
    "Currency\030= \001(\t\022\024\n\014SellCurrency\030> \001(\t\022\r\n\005"
    "Tenor\030\? \001(\005\022\022\n\nSettleDate\030\024 \001(\t\022<\n\014Forex"
    "Entries\030\025 \003(\0132&.com.htsc.mdc.insight.mod"
    "el.ForexEntry\022\017\n\007MDReqId\030\026 \001(\t\022\021\n\tDelayT"
    "ype\030e \001(\005\"\\\n\nForexEntry\022\023\n\013MDEntryType\030\001"
    " \001(\005\022\021\n\tMDEntryPx\030\002 \001(\001\022\023\n\013MDEntrySize\030\003"
    " \001(\001\022\021\n\tMDEntryId\030\004 \001(\tB0\n\032com.htsc.mdc."
    "insight.modelB\rMDForexProtosH\001\240\001\001b\006proto"
    "3", 1161);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDForex.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDForex_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDForex_2eproto_once_);
void protobuf_AddDesc_MDForex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDForex_2eproto_once_,
                 &protobuf_AddDesc_MDForex_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDForex_2eproto {
  StaticDescriptorInitializer_MDForex_2eproto() {
    protobuf_AddDesc_MDForex_2eproto();
  }
} static_descriptor_initializer_MDForex_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDForex::kHTSCSecurityIDFieldNumber;
const int MDForex::kMDDateFieldNumber;
const int MDForex::kMDTimeFieldNumber;
const int MDForex::kDataTimestampFieldNumber;
const int MDForex::kTradingPhaseCodeFieldNumber;
const int MDForex::kSecurityIDSourceFieldNumber;
const int MDForex::kSecurityTypeFieldNumber;
const int MDForex::kMaxPxFieldNumber;
const int MDForex::kMinPxFieldNumber;
const int MDForex::kPreClosePxFieldNumber;
const int MDForex::kTotalVolumeTradeFieldNumber;
const int MDForex::kTotalValueTradeFieldNumber;
const int MDForex::kLastPxFieldNumber;
const int MDForex::kOpenPxFieldNumber;
const int MDForex::kClosePxFieldNumber;
const int MDForex::kHighPxFieldNumber;
const int MDForex::kLowPxFieldNumber;
const int MDForex::kExchangeDateFieldNumber;
const int MDForex::kExchangeTimeFieldNumber;
const int MDForex::kBuyPriceQueueFieldNumber;
const int MDForex::kBuyOrderQtyQueueFieldNumber;
const int MDForex::kSellPriceQueueFieldNumber;
const int MDForex::kSellOrderQtyQueueFieldNumber;
const int MDForex::kBuyOrderQueueFieldNumber;
const int MDForex::kSellOrderQueueFieldNumber;
const int MDForex::kBuyNumOrdersQueueFieldNumber;
const int MDForex::kSellNumOrdersQueueFieldNumber;
const int MDForex::kDataMultiplePowerOf10FieldNumber;
const int MDForex::kMidPxFieldNumber;
const int MDForex::kBuyCurrencyFieldNumber;
const int MDForex::kSellCurrencyFieldNumber;
const int MDForex::kTenorFieldNumber;
const int MDForex::kSettleDateFieldNumber;
const int MDForex::kForexEntriesFieldNumber;
const int MDForex::kMDReqIdFieldNumber;
const int MDForex::kDelayTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDForex::MDForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDForex)
}

void MDForex::InitAsDefaultInstance() {
}

MDForex::MDForex(const MDForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDForex)
}

void MDForex::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buycurrency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellcurrency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settledate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdreqid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&delaytype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(delaytype_));
  _cached_size_ = 0;
}

MDForex::~MDForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDForex)
  SharedDtor();
}

void MDForex::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buycurrency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellcurrency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settledate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdreqid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDForex_descriptor_;
}

const MDForex& MDForex::default_instance() {
  protobuf_InitDefaults_MDForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDForex> MDForex_default_instance_;

MDForex* MDForex::New(::google::protobuf::Arena* arena) const {
  MDForex* n = new MDForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, highpx_);
  ZR_(lowpx_, exchangetime_);
  ZR_(midpx_, tenor_);
  buycurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sellcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settledate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdreqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  delaytype_ = 0;

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
  forexentries_.Clear();
}

bool MDForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 11;
      case 11: {
        if (tag == 88) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 13;
      case 13: {
        if (tag == 104) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 15;
      case 15: {
        if (tag == 120) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 16;
      case 16: {
        if (tag == 128) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 17;
      case 17: {
        if (tag == 136) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 18;
      case 18: {
        if (tag == 144) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 19;
      case 19: {
        if (tag == 152) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_SettleDate;
        break;
      }

      // optional string SettleDate = 20;
      case 20: {
        if (tag == 162) {
         parse_SettleDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settledate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settledate().data(), this->settledate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.SettleDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_ForexEntries;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
      case 21: {
        if (tag == 170) {
         parse_ForexEntries:
          DO_(input->IncrementRecursionDepth());
         parse_loop_ForexEntries:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_forexentries()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_loop_ForexEntries;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(178)) goto parse_MDReqId;
        break;
      }

      // optional string MDReqId = 22;
      case 22: {
        if (tag == 178) {
         parse_MDReqId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mdreqid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->mdreqid().data(), this->mdreqid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.MDReqId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 59;
      case 59: {
        if (tag == 472) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_MidPx;
        break;
      }

      // optional int64 MidPx = 60;
      case 60: {
        if (tag == 480) {
         parse_MidPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &midpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(490)) goto parse_BuyCurrency;
        break;
      }

      // optional string BuyCurrency = 61;
      case 61: {
        if (tag == 490) {
         parse_BuyCurrency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buycurrency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buycurrency().data(), this->buycurrency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.BuyCurrency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(498)) goto parse_SellCurrency;
        break;
      }

      // optional string SellCurrency = 62;
      case 62: {
        if (tag == 498) {
         parse_SellCurrency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sellcurrency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->sellcurrency().data(), this->sellcurrency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDForex.SellCurrency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(504)) goto parse_Tenor;
        break;
      }

      // optional int32 Tenor = 63;
      case 63: {
        if (tag == 504) {
         parse_Tenor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tenor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_DelayType;
        break;
      }

      // optional int32 DelayType = 101;
      case 101: {
        if (tag == 808) {
         parse_DelayType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDForex)
  return false;
#undef DO_
}

void MDForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDForex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 TotalVolumeTrade = 11;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 12;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 13;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lastpx(), output);
  }

  // optional int64 OpenPx = 14;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openpx(), output);
  }

  // optional int64 ClosePx = 15;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->closepx(), output);
  }

  // optional int64 HighPx = 16;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->highpx(), output);
  }

  // optional int64 LowPx = 17;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->lowpx(), output);
  }

  // optional int32 ExchangeDate = 18;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 19;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->exchangetime(), output);
  }

  // optional string SettleDate = 20;
  if (this->settledate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settledate().data(), this->settledate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.SettleDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->settledate(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
  for (unsigned int i = 0, n = this->forexentries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, this->forexentries(i), output);
  }

  // optional string MDReqId = 22;
  if (this->mdreqid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdreqid().data(), this->mdreqid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.MDReqId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->mdreqid(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(59, this->datamultiplepowerof10(), output);
  }

  // optional int64 MidPx = 60;
  if (this->midpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(60, this->midpx(), output);
  }

  // optional string BuyCurrency = 61;
  if (this->buycurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buycurrency().data(), this->buycurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.BuyCurrency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      61, this->buycurrency(), output);
  }

  // optional string SellCurrency = 62;
  if (this->sellcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellcurrency().data(), this->sellcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.SellCurrency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      62, this->sellcurrency(), output);
  }

  // optional int32 Tenor = 63;
  if (this->tenor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(63, this->tenor(), output);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(101, this->delaytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDForex)
}

::google::protobuf::uint8* MDForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDForex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 TotalVolumeTrade = 11;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 12;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 13;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lastpx(), target);
  }

  // optional int64 OpenPx = 14;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openpx(), target);
  }

  // optional int64 ClosePx = 15;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->closepx(), target);
  }

  // optional int64 HighPx = 16;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->highpx(), target);
  }

  // optional int64 LowPx = 17;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->lowpx(), target);
  }

  // optional int32 ExchangeDate = 18;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 19;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->exchangetime(), target);
  }

  // optional string SettleDate = 20;
  if (this->settledate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settledate().data(), this->settledate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.SettleDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->settledate(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
  for (unsigned int i = 0, n = this->forexentries_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, this->forexentries(i), false, target);
  }

  // optional string MDReqId = 22;
  if (this->mdreqid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdreqid().data(), this->mdreqid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.MDReqId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->mdreqid(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(59, this->datamultiplepowerof10(), target);
  }

  // optional int64 MidPx = 60;
  if (this->midpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(60, this->midpx(), target);
  }

  // optional string BuyCurrency = 61;
  if (this->buycurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buycurrency().data(), this->buycurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.BuyCurrency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        61, this->buycurrency(), target);
  }

  // optional string SellCurrency = 62;
  if (this->sellcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sellcurrency().data(), this->sellcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDForex.SellCurrency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        62, this->sellcurrency(), target);
  }

  // optional int32 Tenor = 63;
  if (this->tenor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(63, this->tenor(), target);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(101, this->delaytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDForex)
  return target;
}

size_t MDForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDForex)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 TotalVolumeTrade = 11;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 12;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 13;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 14;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 15;
  if (this->closepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 16;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 17;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int32 ExchangeDate = 18;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 19;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 MidPx = 60;
  if (this->midpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->midpx());
  }

  // optional string BuyCurrency = 61;
  if (this->buycurrency().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buycurrency());
  }

  // optional string SellCurrency = 62;
  if (this->sellcurrency().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->sellcurrency());
  }

  // optional int32 Tenor = 63;
  if (this->tenor() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tenor());
  }

  // optional string SettleDate = 20;
  if (this->settledate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settledate());
  }

  // optional string MDReqId = 22;
  if (this->mdreqid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->mdreqid());
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaytype());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
  {
    unsigned int count = this->forexentries_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->forexentries(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDForex)
    UnsafeMergeFrom(*source);
  }
}

void MDForex::MergeFrom(const MDForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDForex::UnsafeMergeFrom(const MDForex& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  forexentries_.MergeFrom(from.forexentries_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.midpx() != 0) {
    set_midpx(from.midpx());
  }
  if (from.buycurrency().size() > 0) {

    buycurrency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buycurrency_);
  }
  if (from.sellcurrency().size() > 0) {

    sellcurrency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sellcurrency_);
  }
  if (from.tenor() != 0) {
    set_tenor(from.tenor());
  }
  if (from.settledate().size() > 0) {

    settledate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settledate_);
  }
  if (from.mdreqid().size() > 0) {

    mdreqid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.mdreqid_);
  }
  if (from.delaytype() != 0) {
    set_delaytype(from.delaytype());
  }
}

void MDForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDForex::CopyFrom(const MDForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDForex::IsInitialized() const {

  return true;
}

void MDForex::Swap(MDForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDForex::InternalSwap(MDForex* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(midpx_, other->midpx_);
  buycurrency_.Swap(&other->buycurrency_);
  sellcurrency_.Swap(&other->sellcurrency_);
  std::swap(tenor_, other->tenor_);
  settledate_.Swap(&other->settledate_);
  forexentries_.UnsafeArenaSwap(&other->forexentries_);
  mdreqid_.Swap(&other->mdreqid_);
  std::swap(delaytype_, other->delaytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDForex_descriptor_;
  metadata.reflection = MDForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDForex

// optional string HTSCSecurityID = 1;
void MDForex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
void MDForex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
void MDForex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}
::std::string* MDForex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDForex::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDForex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDDate)
  return mddate_;
}
void MDForex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDDate)
}

// optional int32 MDTime = 3;
void MDForex::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDForex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDTime)
  return mdtime_;
}
void MDForex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDForex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DataTimestamp)
  return datatimestamp_;
}
void MDForex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDForex::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
void MDForex::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
void MDForex::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}
::std::string* MDForex::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDForex::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDForex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDForex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDForex::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDForex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDForex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.securityType)
}

// optional int64 MaxPx = 8;
void MDForex::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MaxPx)
  return maxpx_;
}
void MDForex::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MaxPx)
}

// optional int64 MinPx = 9;
void MDForex::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MinPx)
  return minpx_;
}
void MDForex::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MinPx)
}

// optional int64 PreClosePx = 10;
void MDForex::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.PreClosePx)
  return preclosepx_;
}
void MDForex::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.PreClosePx)
}

// optional int64 TotalVolumeTrade = 11;
void MDForex::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDForex::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 12;
void MDForex::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.TotalValueTrade)
  return totalvaluetrade_;
}
void MDForex::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.TotalValueTrade)
}

// optional int64 LastPx = 13;
void MDForex::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.LastPx)
  return lastpx_;
}
void MDForex::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.LastPx)
}

// optional int64 OpenPx = 14;
void MDForex::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.OpenPx)
  return openpx_;
}
void MDForex::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.OpenPx)
}

// optional int64 ClosePx = 15;
void MDForex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ClosePx)
  return closepx_;
}
void MDForex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ClosePx)
}

// optional int64 HighPx = 16;
void MDForex::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.HighPx)
  return highpx_;
}
void MDForex::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.HighPx)
}

// optional int64 LowPx = 17;
void MDForex::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.LowPx)
  return lowpx_;
}
void MDForex::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.LowPx)
}

// optional int32 ExchangeDate = 18;
void MDForex::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDForex::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ExchangeDate)
  return exchangedate_;
}
void MDForex::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ExchangeDate)
}

// optional int32 ExchangeTime = 19;
void MDForex::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDForex::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ExchangeTime)
  return exchangetime_;
}
void MDForex::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.ExchangeTime)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDForex::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDForex::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDForex::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDForex::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
}
void MDForex::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDForex::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDForex::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDForex::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDForex::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
}
void MDForex::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDForex::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDForex::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDForex::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDForex::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
}
void MDForex::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDForex::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDForex::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDForex::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDForex::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
}
void MDForex::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDForex::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDForex::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDForex::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDForex::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
}
void MDForex::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDForex::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDForex::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDForex::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDForex::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
}
void MDForex::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDForex::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDForex::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDForex::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDForex::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
}
void MDForex::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDForex::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDForex::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDForex::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDForex::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
}
void MDForex::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDForex::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDForex::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
void MDForex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDForex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDForex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DataMultiplePowerOf10)
}

// optional int64 MidPx = 60;
void MDForex::clear_midpx() {
  midpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDForex::midpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MidPx)
  return midpx_;
}
void MDForex::set_midpx(::google::protobuf::int64 value) {
  
  midpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MidPx)
}

// optional string BuyCurrency = 61;
void MDForex::clear_buycurrency() {
  buycurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::buycurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  return buycurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_buycurrency(const ::std::string& value) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
void MDForex::set_buycurrency(const char* value) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
void MDForex::set_buycurrency(const char* value, size_t size) {
  
  buycurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}
::std::string* MDForex::mutable_buycurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  return buycurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_buycurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
  
  return buycurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_buycurrency(::std::string* buycurrency) {
  if (buycurrency != NULL) {
    
  } else {
    
  }
  buycurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buycurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.BuyCurrency)
}

// optional string SellCurrency = 62;
void MDForex::clear_sellcurrency() {
  sellcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::sellcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  return sellcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_sellcurrency(const ::std::string& value) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
void MDForex::set_sellcurrency(const char* value) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
void MDForex::set_sellcurrency(const char* value, size_t size) {
  
  sellcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}
::std::string* MDForex::mutable_sellcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  return sellcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_sellcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.SellCurrency)
  
  return sellcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_sellcurrency(::std::string* sellcurrency) {
  if (sellcurrency != NULL) {
    
  } else {
    
  }
  sellcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sellcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.SellCurrency)
}

// optional int32 Tenor = 63;
void MDForex::clear_tenor() {
  tenor_ = 0;
}
::google::protobuf::int32 MDForex::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.Tenor)
  return tenor_;
}
void MDForex::set_tenor(::google::protobuf::int32 value) {
  
  tenor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.Tenor)
}

// optional string SettleDate = 20;
void MDForex::clear_settledate() {
  settledate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::settledate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.SettleDate)
  return settledate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_settledate(const ::std::string& value) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
void MDForex::set_settledate(const char* value) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
void MDForex::set_settledate(const char* value, size_t size) {
  
  settledate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.SettleDate)
}
::std::string* MDForex::mutable_settledate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.SettleDate)
  return settledate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_settledate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.SettleDate)
  
  return settledate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_settledate(::std::string* settledate) {
  if (settledate != NULL) {
    
  } else {
    
  }
  settledate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settledate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.SettleDate)
}

// repeated .com.htsc.mdc.insight.model.ForexEntry ForexEntries = 21;
int MDForex::forexentries_size() const {
  return forexentries_.size();
}
void MDForex::clear_forexentries() {
  forexentries_.Clear();
}
const ::com::htsc::mdc::insight::model::ForexEntry& MDForex::forexentries(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Get(index);
}
::com::htsc::mdc::insight::model::ForexEntry* MDForex::mutable_forexentries(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Mutable(index);
}
::com::htsc::mdc::insight::model::ForexEntry* MDForex::add_forexentries() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >*
MDForex::mutable_forexentries() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return &forexentries_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ForexEntry >&
MDForex::forexentries() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDForex.ForexEntries)
  return forexentries_;
}

// optional string MDReqId = 22;
void MDForex::clear_mdreqid() {
  mdreqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDForex::mdreqid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.MDReqId)
  return mdreqid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_mdreqid(const ::std::string& value) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
void MDForex::set_mdreqid(const char* value) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
void MDForex::set_mdreqid(const char* value, size_t size) {
  
  mdreqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDForex.MDReqId)
}
::std::string* MDForex::mutable_mdreqid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDForex.MDReqId)
  return mdreqid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDForex::release_mdreqid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDForex.MDReqId)
  
  return mdreqid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDForex::set_allocated_mdreqid(::std::string* mdreqid) {
  if (mdreqid != NULL) {
    
  } else {
    
  }
  mdreqid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdreqid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDForex.MDReqId)
}

// optional int32 DelayType = 101;
void MDForex::clear_delaytype() {
  delaytype_ = 0;
}
::google::protobuf::int32 MDForex::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDForex.DelayType)
  return delaytype_;
}
void MDForex::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDForex.DelayType)
}

inline const MDForex* MDForex::internal_default_instance() {
  return &MDForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForexEntry::kMDEntryTypeFieldNumber;
const int ForexEntry::kMDEntryPxFieldNumber;
const int ForexEntry::kMDEntrySizeFieldNumber;
const int ForexEntry::kMDEntryIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForexEntry::ForexEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ForexEntry)
}

void ForexEntry::InitAsDefaultInstance() {
}

ForexEntry::ForexEntry(const ForexEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ForexEntry)
}

void ForexEntry::SharedCtor() {
  mdentryid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mdentrypx_, 0, reinterpret_cast<char*>(&mdentrytype_) -
    reinterpret_cast<char*>(&mdentrypx_) + sizeof(mdentrytype_));
  _cached_size_ = 0;
}

ForexEntry::~ForexEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ForexEntry)
  SharedDtor();
}

void ForexEntry::SharedDtor() {
  mdentryid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ForexEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ForexEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForexEntry_descriptor_;
}

const ForexEntry& ForexEntry::default_instance() {
  protobuf_InitDefaults_MDForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForexEntry> ForexEntry_default_instance_;

ForexEntry* ForexEntry::New(::google::protobuf::Arena* arena) const {
  ForexEntry* n = new ForexEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ForexEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ForexEntry)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ForexEntry, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ForexEntry*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mdentrypx_, mdentrytype_);
  mdentryid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ForexEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ForexEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 MDEntryType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdentrytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_MDEntryPx;
        break;
      }

      // optional double MDEntryPx = 2;
      case 2: {
        if (tag == 17) {
         parse_MDEntryPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &mdentrypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(25)) goto parse_MDEntrySize;
        break;
      }

      // optional double MDEntrySize = 3;
      case 3: {
        if (tag == 25) {
         parse_MDEntrySize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &mdentrysize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_MDEntryId;
        break;
      }

      // optional string MDEntryId = 4;
      case 4: {
        if (tag == 34) {
         parse_MDEntryId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mdentryid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->mdentryid().data(), this->mdentryid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForexEntry.MDEntryId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ForexEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ForexEntry)
  return false;
#undef DO_
}

void ForexEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ForexEntry)
  // optional int32 MDEntryType = 1;
  if (this->mdentrytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->mdentrytype(), output);
  }

  // optional double MDEntryPx = 2;
  if (this->mdentrypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->mdentrypx(), output);
  }

  // optional double MDEntrySize = 3;
  if (this->mdentrysize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->mdentrysize(), output);
  }

  // optional string MDEntryId = 4;
  if (this->mdentryid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdentryid().data(), this->mdentryid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForexEntry.MDEntryId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->mdentryid(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ForexEntry)
}

::google::protobuf::uint8* ForexEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ForexEntry)
  // optional int32 MDEntryType = 1;
  if (this->mdentrytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->mdentrytype(), target);
  }

  // optional double MDEntryPx = 2;
  if (this->mdentrypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->mdentrypx(), target);
  }

  // optional double MDEntrySize = 3;
  if (this->mdentrysize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->mdentrysize(), target);
  }

  // optional string MDEntryId = 4;
  if (this->mdentryid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdentryid().data(), this->mdentryid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForexEntry.MDEntryId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->mdentryid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ForexEntry)
  return target;
}

size_t ForexEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ForexEntry)
  size_t total_size = 0;

  // optional int32 MDEntryType = 1;
  if (this->mdentrytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdentrytype());
  }

  // optional double MDEntryPx = 2;
  if (this->mdentrypx() != 0) {
    total_size += 1 + 8;
  }

  // optional double MDEntrySize = 3;
  if (this->mdentrysize() != 0) {
    total_size += 1 + 8;
  }

  // optional string MDEntryId = 4;
  if (this->mdentryid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->mdentryid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForexEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ForexEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ForexEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ForexEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ForexEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ForexEntry)
    UnsafeMergeFrom(*source);
  }
}

void ForexEntry::MergeFrom(const ForexEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ForexEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForexEntry::UnsafeMergeFrom(const ForexEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.mdentrytype() != 0) {
    set_mdentrytype(from.mdentrytype());
  }
  if (from.mdentrypx() != 0) {
    set_mdentrypx(from.mdentrypx());
  }
  if (from.mdentrysize() != 0) {
    set_mdentrysize(from.mdentrysize());
  }
  if (from.mdentryid().size() > 0) {

    mdentryid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.mdentryid_);
  }
}

void ForexEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ForexEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ForexEntry::CopyFrom(const ForexEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ForexEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForexEntry::IsInitialized() const {

  return true;
}

void ForexEntry::Swap(ForexEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ForexEntry::InternalSwap(ForexEntry* other) {
  std::swap(mdentrytype_, other->mdentrytype_);
  std::swap(mdentrypx_, other->mdentrypx_);
  std::swap(mdentrysize_, other->mdentrysize_);
  mdentryid_.Swap(&other->mdentryid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ForexEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ForexEntry_descriptor_;
  metadata.reflection = ForexEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForexEntry

// optional int32 MDEntryType = 1;
void ForexEntry::clear_mdentrytype() {
  mdentrytype_ = 0;
}
::google::protobuf::int32 ForexEntry::mdentrytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryType)
  return mdentrytype_;
}
void ForexEntry::set_mdentrytype(::google::protobuf::int32 value) {
  
  mdentrytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryType)
}

// optional double MDEntryPx = 2;
void ForexEntry::clear_mdentrypx() {
  mdentrypx_ = 0;
}
double ForexEntry::mdentrypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryPx)
  return mdentrypx_;
}
void ForexEntry::set_mdentrypx(double value) {
  
  mdentrypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryPx)
}

// optional double MDEntrySize = 3;
void ForexEntry::clear_mdentrysize() {
  mdentrysize_ = 0;
}
double ForexEntry::mdentrysize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntrySize)
  return mdentrysize_;
}
void ForexEntry::set_mdentrysize(double value) {
  
  mdentrysize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntrySize)
}

// optional string MDEntryId = 4;
void ForexEntry::clear_mdentryid() {
  mdentryid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForexEntry::mdentryid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  return mdentryid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForexEntry::set_mdentryid(const ::std::string& value) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
void ForexEntry::set_mdentryid(const char* value) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
void ForexEntry::set_mdentryid(const char* value, size_t size) {
  
  mdentryid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}
::std::string* ForexEntry::mutable_mdentryid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  return mdentryid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForexEntry::release_mdentryid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
  
  return mdentryid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForexEntry::set_allocated_mdentryid(::std::string* mdentryid) {
  if (mdentryid != NULL) {
    
  } else {
    
  }
  mdentryid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdentryid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForexEntry.MDEntryId)
}

inline const ForexEntry* ForexEntry::internal_default_instance() {
  return &ForexEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
