// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSAQuote.proto

#ifndef PROTOBUF_MDUSAQuote_2eproto__INCLUDED
#define PROTOBUF_MDUSAQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityType.pb.h"
#include "ESecurityIDSource.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDUSAQuote_2eproto();
void protobuf_InitDefaults_MDUSAQuote_2eproto();
void protobuf_AssignDesc_MDUSAQuote_2eproto();
void protobuf_ShutdownFile_MDUSAQuote_2eproto();

class MDUSAQuote;

// ===================================================================

class MDUSAQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDUSAQuote) */ {
 public:
  MDUSAQuote();
  virtual ~MDUSAQuote();

  MDUSAQuote(const MDUSAQuote& from);

  inline MDUSAQuote& operator=(const MDUSAQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDUSAQuote& default_instance();

  static const MDUSAQuote* internal_default_instance();

  void Swap(MDUSAQuote* other);

  // implements Message ----------------------------------------------

  inline MDUSAQuote* New() const { return New(NULL); }

  MDUSAQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDUSAQuote& from);
  void MergeFrom(const MDUSAQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDUSAQuote* other);
  void UnsafeMergeFrom(const MDUSAQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 7;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 7;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 8;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 8;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 Nanosecond = 9;
  void clear_nanosecond();
  static const int kNanosecondFieldNumber = 9;
  ::google::protobuf::int32 nanosecond() const;
  void set_nanosecond(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 10;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 10;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 TimeIndex = 11;
  void clear_timeindex();
  static const int kTimeIndexFieldNumber = 11;
  ::google::protobuf::int32 timeindex() const;
  void set_timeindex(::google::protobuf::int32 value);

  // optional int64 BidPrice = 12;
  void clear_bidprice();
  static const int kBidPriceFieldNumber = 12;
  ::google::protobuf::int64 bidprice() const;
  void set_bidprice(::google::protobuf::int64 value);

  // optional int64 BidSize = 13;
  void clear_bidsize();
  static const int kBidSizeFieldNumber = 13;
  ::google::protobuf::int64 bidsize() const;
  void set_bidsize(::google::protobuf::int64 value);

  // optional int64 BidSizeNAV = 14;
  void clear_bidsizenav();
  static const int kBidSizeNAVFieldNumber = 14;
  ::google::protobuf::int64 bidsizenav() const;
  void set_bidsizenav(::google::protobuf::int64 value);

  // optional int64 AskPrice = 15;
  void clear_askprice();
  static const int kAskPriceFieldNumber = 15;
  ::google::protobuf::int64 askprice() const;
  void set_askprice(::google::protobuf::int64 value);

  // optional int64 AskSize = 16;
  void clear_asksize();
  static const int kAskSizeFieldNumber = 16;
  ::google::protobuf::int64 asksize() const;
  void set_asksize(::google::protobuf::int64 value);

  // optional int64 AskSizeNAV = 17;
  void clear_asksizenav();
  static const int kAskSizeNAVFieldNumber = 17;
  ::google::protobuf::int64 asksizenav() const;
  void set_asksizenav(::google::protobuf::int64 value);

  // optional int32 BidMarket = 18;
  void clear_bidmarket();
  static const int kBidMarketFieldNumber = 18;
  ::google::protobuf::int32 bidmarket() const;
  void set_bidmarket(::google::protobuf::int32 value);

  // optional int64 BidTime = 19;
  void clear_bidtime();
  static const int kBidTimeFieldNumber = 19;
  ::google::protobuf::int64 bidtime() const;
  void set_bidtime(::google::protobuf::int64 value);

  // optional int32 AskMarket = 20;
  void clear_askmarket();
  static const int kAskMarketFieldNumber = 20;
  ::google::protobuf::int32 askmarket() const;
  void set_askmarket(::google::protobuf::int32 value);

  // optional int64 AskTime = 21;
  void clear_asktime();
  static const int kAskTimeFieldNumber = 21;
  ::google::protobuf::int64 asktime() const;
  void set_asktime(::google::protobuf::int64 value);

  // optional int64 DataIndex = 22;
  void clear_dataindex();
  static const int kDataIndexFieldNumber = 22;
  ::google::protobuf::int64 dataindex() const;
  void set_dataindex(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDUSAQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 nanosecond_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int64 bidprice_;
  ::google::protobuf::int64 bidsize_;
  ::google::protobuf::int64 bidsizenav_;
  ::google::protobuf::int32 timeindex_;
  ::google::protobuf::int32 bidmarket_;
  ::google::protobuf::int64 askprice_;
  ::google::protobuf::int64 asksize_;
  ::google::protobuf::int64 asksizenav_;
  ::google::protobuf::int64 bidtime_;
  ::google::protobuf::int64 asktime_;
  ::google::protobuf::int64 dataindex_;
  ::google::protobuf::int32 askmarket_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDUSAQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDUSAQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDUSAQuote_2eproto();
  friend void protobuf_ShutdownFile_MDUSAQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDUSAQuote> MDUSAQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSAQuote

// optional string HTSCSecurityID = 1;
inline void MDUSAQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDUSAQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
inline void MDUSAQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
inline void MDUSAQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}
inline ::std::string* MDUSAQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDUSAQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDUSAQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDUSAQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.MDDate)
  return mddate_;
}
inline void MDUSAQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDUSAQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.MDTime)
  return mdtime_;
}
inline void MDUSAQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDUSAQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDUSAQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDUSAQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDUSAQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDUSAQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDUSAQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDUSAQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDUSAQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.securityType)
}

// optional int32 ExchangeDate = 7;
inline void MDUSAQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeDate)
  return exchangedate_;
}
inline void MDUSAQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
inline void MDUSAQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeTime)
  return exchangetime_;
}
inline void MDUSAQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.ExchangeTime)
}

// optional int32 Nanosecond = 9;
inline void MDUSAQuote::clear_nanosecond() {
  nanosecond_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.Nanosecond)
  return nanosecond_;
}
inline void MDUSAQuote::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.Nanosecond)
}

// optional int32 DataMultiplePowerOf10 = 10;
inline void MDUSAQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDUSAQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 11;
inline void MDUSAQuote::clear_timeindex() {
  timeindex_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.TimeIndex)
  return timeindex_;
}
inline void MDUSAQuote::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.TimeIndex)
}

// optional int64 BidPrice = 12;
inline void MDUSAQuote::clear_bidprice() {
  bidprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::bidprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidPrice)
  return bidprice_;
}
inline void MDUSAQuote::set_bidprice(::google::protobuf::int64 value) {
  
  bidprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidPrice)
}

// optional int64 BidSize = 13;
inline void MDUSAQuote::clear_bidsize() {
  bidsize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::bidsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidSize)
  return bidsize_;
}
inline void MDUSAQuote::set_bidsize(::google::protobuf::int64 value) {
  
  bidsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidSize)
}

// optional int64 BidSizeNAV = 14;
inline void MDUSAQuote::clear_bidsizenav() {
  bidsizenav_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::bidsizenav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidSizeNAV)
  return bidsizenav_;
}
inline void MDUSAQuote::set_bidsizenav(::google::protobuf::int64 value) {
  
  bidsizenav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidSizeNAV)
}

// optional int64 AskPrice = 15;
inline void MDUSAQuote::clear_askprice() {
  askprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::askprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskPrice)
  return askprice_;
}
inline void MDUSAQuote::set_askprice(::google::protobuf::int64 value) {
  
  askprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskPrice)
}

// optional int64 AskSize = 16;
inline void MDUSAQuote::clear_asksize() {
  asksize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::asksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskSize)
  return asksize_;
}
inline void MDUSAQuote::set_asksize(::google::protobuf::int64 value) {
  
  asksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskSize)
}

// optional int64 AskSizeNAV = 17;
inline void MDUSAQuote::clear_asksizenav() {
  asksizenav_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::asksizenav() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskSizeNAV)
  return asksizenav_;
}
inline void MDUSAQuote::set_asksizenav(::google::protobuf::int64 value) {
  
  asksizenav_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskSizeNAV)
}

// optional int32 BidMarket = 18;
inline void MDUSAQuote::clear_bidmarket() {
  bidmarket_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::bidmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidMarket)
  return bidmarket_;
}
inline void MDUSAQuote::set_bidmarket(::google::protobuf::int32 value) {
  
  bidmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidMarket)
}

// optional int64 BidTime = 19;
inline void MDUSAQuote::clear_bidtime() {
  bidtime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::bidtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.BidTime)
  return bidtime_;
}
inline void MDUSAQuote::set_bidtime(::google::protobuf::int64 value) {
  
  bidtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.BidTime)
}

// optional int32 AskMarket = 20;
inline void MDUSAQuote::clear_askmarket() {
  askmarket_ = 0;
}
inline ::google::protobuf::int32 MDUSAQuote::askmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskMarket)
  return askmarket_;
}
inline void MDUSAQuote::set_askmarket(::google::protobuf::int32 value) {
  
  askmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskMarket)
}

// optional int64 AskTime = 21;
inline void MDUSAQuote::clear_asktime() {
  asktime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::asktime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.AskTime)
  return asktime_;
}
inline void MDUSAQuote::set_asktime(::google::protobuf::int64 value) {
  
  asktime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.AskTime)
}

// optional int64 DataIndex = 22;
inline void MDUSAQuote::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDUSAQuote::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAQuote.DataIndex)
  return dataindex_;
}
inline void MDUSAQuote::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAQuote.DataIndex)
}

inline const MDUSAQuote* MDUSAQuote::internal_default_instance() {
  return &MDUSAQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDUSAQuote_2eproto__INCLUDED
