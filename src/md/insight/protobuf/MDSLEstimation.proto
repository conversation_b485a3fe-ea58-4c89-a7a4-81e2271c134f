syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDSLEstimation {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  double EstimatedLendRate = 7;
  double EstimatedBorrowRate = 8;
  int32 DataMultiplePowerOf10 = 9;
  int64 MessageNumber = 100;
}
