// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQBTransaction.proto

#ifndef PROTOBUF_MDQBTransaction_2eproto__INCLUDED
#define PROTOBUF_MDQBTransaction_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDQBTransaction_2eproto();
void protobuf_InitDefaults_MDQBTransaction_2eproto();
void protobuf_AssignDesc_MDQBTransaction_2eproto();
void protobuf_ShutdownFile_MDQBTransaction_2eproto();

class MDQBTransaction;

// ===================================================================

class MDQBTransaction : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDQBTransaction) */ {
 public:
  MDQBTransaction();
  virtual ~MDQBTransaction();

  MDQBTransaction(const MDQBTransaction& from);

  inline MDQBTransaction& operator=(const MDQBTransaction& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDQBTransaction& default_instance();

  static const MDQBTransaction* internal_default_instance();

  void Swap(MDQBTransaction* other);

  // implements Message ----------------------------------------------

  inline MDQBTransaction* New() const { return New(NULL); }

  MDQBTransaction* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDQBTransaction& from);
  void MergeFrom(const MDQBTransaction& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDQBTransaction* other);
  void UnsafeMergeFrom(const MDQBTransaction& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 8;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 8;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 9;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 9;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 10;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 10;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int64 ApplSeqNum = 11;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 11;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // optional string TradeID = 12;
  void clear_tradeid();
  static const int kTradeIDFieldNumber = 12;
  const ::std::string& tradeid() const;
  void set_tradeid(const ::std::string& value);
  void set_tradeid(const char* value);
  void set_tradeid(const char* value, size_t size);
  ::std::string* mutable_tradeid();
  ::std::string* release_tradeid();
  void set_allocated_tradeid(::std::string* tradeid);

  // optional int32 Side = 13;
  void clear_side();
  static const int kSideFieldNumber = 13;
  ::google::protobuf::int32 side() const;
  void set_side(::google::protobuf::int32 value);

  // optional int64 TradeQty = 14;
  void clear_tradeqty();
  static const int kTradeQtyFieldNumber = 14;
  ::google::protobuf::int64 tradeqty() const;
  void set_tradeqty(::google::protobuf::int64 value);

  // optional int32 SettlDate = 15;
  void clear_settldate();
  static const int kSettlDateFieldNumber = 15;
  ::google::protobuf::int32 settldate() const;
  void set_settldate(::google::protobuf::int32 value);

  // optional int64 StrikePrice = 16;
  void clear_strikeprice();
  static const int kStrikePriceFieldNumber = 16;
  ::google::protobuf::int64 strikeprice() const;
  void set_strikeprice(::google::protobuf::int64 value);

  // optional int64 Yield = 17;
  void clear_yield();
  static const int kYieldFieldNumber = 17;
  ::google::protobuf::int64 yield() const;
  void set_yield(::google::protobuf::int64 value);

  // optional int64 OriginalPrice = 18;
  void clear_originalprice();
  static const int kOriginalPriceFieldNumber = 18;
  ::google::protobuf::int64 originalprice() const;
  void set_originalprice(::google::protobuf::int64 value);

  // optional int64 FullPrice = 19;
  void clear_fullprice();
  static const int kFullPriceFieldNumber = 19;
  ::google::protobuf::int64 fullprice() const;
  void set_fullprice(::google::protobuf::int64 value);

  // optional int32 InstrmtAssignmentMethod = 20;
  void clear_instrmtassignmentmethod();
  static const int kInstrmtAssignmentMethodFieldNumber = 20;
  ::google::protobuf::int32 instrmtassignmentmethod() const;
  void set_instrmtassignmentmethod(::google::protobuf::int32 value);

  // optional int32 TradeStatus = 21;
  void clear_tradestatus();
  static const int kTradeStatusFieldNumber = 21;
  ::google::protobuf::int32 tradestatus() const;
  void set_tradestatus(::google::protobuf::int32 value);

  // optional int32 TradeType = 22;
  void clear_tradetype();
  static const int kTradeTypeFieldNumber = 22;
  ::google::protobuf::int32 tradetype() const;
  void set_tradetype(::google::protobuf::int32 value);

  // optional int32 PriceType = 23;
  void clear_pricetype();
  static const int kPriceTypeFieldNumber = 23;
  ::google::protobuf::int32 pricetype() const;
  void set_pricetype(::google::protobuf::int32 value);

  // optional int32 ClearSpeed = 24;
  void clear_clearspeed();
  static const int kClearSpeedFieldNumber = 24;
  ::google::protobuf::int32 clearspeed() const;
  void set_clearspeed(::google::protobuf::int32 value);

  // optional int32 BrokerDataType = 25;
  void clear_brokerdatatype();
  static const int kBrokerDataTypeFieldNumber = 25;
  ::google::protobuf::int32 brokerdatatype() const;
  void set_brokerdatatype(::google::protobuf::int32 value);

  // optional int32 SSDetect = 26;
  void clear_ssdetect();
  static const int kSSDetectFieldNumber = 26;
  ::google::protobuf::int32 ssdetect() const;
  void set_ssdetect(::google::protobuf::int32 value);

  // optional string TradeReqID = 27;
  void clear_tradereqid();
  static const int kTradeReqIDFieldNumber = 27;
  const ::std::string& tradereqid() const;
  void set_tradereqid(const ::std::string& value);
  void set_tradereqid(const char* value);
  void set_tradereqid(const char* value, size_t size);
  ::std::string* mutable_tradereqid();
  ::std::string* release_tradereqid();
  void set_allocated_tradereqid(::std::string* tradereqid);

  // optional int64 TransactTime = 28;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 28;
  ::google::protobuf::int64 transacttime() const;
  void set_transacttime(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 29;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 29;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string SettlType = 30;
  void clear_settltype();
  static const int kSettlTypeFieldNumber = 30;
  const ::std::string& settltype() const;
  void set_settltype(const ::std::string& value);
  void set_settltype(const char* value);
  void set_settltype(const char* value, size_t size);
  ::std::string* mutable_settltype();
  ::std::string* release_settltype();
  void set_allocated_settltype(::std::string* settltype);

  // optional int32 DealTransType = 31;
  void clear_dealtranstype();
  static const int kDealTransTypeFieldNumber = 31;
  ::google::protobuf::int32 dealtranstype() const;
  void set_dealtranstype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDQBTransaction)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr tradeid_;
  ::google::protobuf::internal::ArenaStringPtr tradereqid_;
  ::google::protobuf::internal::ArenaStringPtr settltype_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 side_;
  ::google::protobuf::int64 tradeqty_;
  ::google::protobuf::int64 strikeprice_;
  ::google::protobuf::int64 yield_;
  ::google::protobuf::int32 settldate_;
  ::google::protobuf::int32 instrmtassignmentmethod_;
  ::google::protobuf::int64 originalprice_;
  ::google::protobuf::int64 fullprice_;
  ::google::protobuf::int32 tradestatus_;
  ::google::protobuf::int32 tradetype_;
  ::google::protobuf::int32 pricetype_;
  ::google::protobuf::int32 clearspeed_;
  ::google::protobuf::int32 brokerdatatype_;
  ::google::protobuf::int32 ssdetect_;
  ::google::protobuf::int64 transacttime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 dealtranstype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQBTransaction_2eproto_impl();
  friend void  protobuf_AddDesc_MDQBTransaction_2eproto_impl();
  friend void protobuf_AssignDesc_MDQBTransaction_2eproto();
  friend void protobuf_ShutdownFile_MDQBTransaction_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDQBTransaction> MDQBTransaction_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQBTransaction

// optional string HTSCSecurityID = 1;
inline void MDQBTransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBTransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
inline void MDQBTransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
inline void MDQBTransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}
inline ::std::string* MDQBTransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBTransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDQBTransaction::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.MDDate)
  return mddate_;
}
inline void MDQBTransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.MDDate)
}

// optional int32 MDTime = 3;
inline void MDQBTransaction::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.MDTime)
  return mdtime_;
}
inline void MDQBTransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDQBTransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DataTimestamp)
  return datatimestamp_;
}
inline void MDQBTransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDQBTransaction::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBTransaction::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
inline void MDQBTransaction::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
inline void MDQBTransaction::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}
inline ::std::string* MDQBTransaction::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBTransaction::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDQBTransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDQBTransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDQBTransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDQBTransaction::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDQBTransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDQBTransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.securityType)
}

// optional int32 ExchangeDate = 8;
inline void MDQBTransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeDate)
  return exchangedate_;
}
inline void MDQBTransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
inline void MDQBTransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeTime)
  return exchangetime_;
}
inline void MDQBTransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ExchangeTime)
}

// optional int32 ChannelNo = 10;
inline void MDQBTransaction::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ChannelNo)
  return channelno_;
}
inline void MDQBTransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ChannelNo)
}

// optional int64 ApplSeqNum = 11;
inline void MDQBTransaction::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ApplSeqNum)
  return applseqnum_;
}
inline void MDQBTransaction::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ApplSeqNum)
}

// optional string TradeID = 12;
inline void MDQBTransaction::clear_tradeid() {
  tradeid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBTransaction::tradeid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  return tradeid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_tradeid(const ::std::string& value) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
inline void MDQBTransaction::set_tradeid(const char* value) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
inline void MDQBTransaction::set_tradeid(const char* value, size_t size) {
  
  tradeid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}
inline ::std::string* MDQBTransaction::mutable_tradeid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  return tradeid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBTransaction::release_tradeid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
  
  return tradeid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_allocated_tradeid(::std::string* tradeid) {
  if (tradeid != NULL) {
    
  } else {
    
  }
  tradeid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradeID)
}

// optional int32 Side = 13;
inline void MDQBTransaction::clear_side() {
  side_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.Side)
  return side_;
}
inline void MDQBTransaction::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.Side)
}

// optional int64 TradeQty = 14;
inline void MDQBTransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeQty)
  return tradeqty_;
}
inline void MDQBTransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeQty)
}

// optional int32 SettlDate = 15;
inline void MDQBTransaction::clear_settldate() {
  settldate_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SettlDate)
  return settldate_;
}
inline void MDQBTransaction::set_settldate(::google::protobuf::int32 value) {
  
  settldate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SettlDate)
}

// optional int64 StrikePrice = 16;
inline void MDQBTransaction::clear_strikeprice() {
  strikeprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::strikeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.StrikePrice)
  return strikeprice_;
}
inline void MDQBTransaction::set_strikeprice(::google::protobuf::int64 value) {
  
  strikeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.StrikePrice)
}

// optional int64 Yield = 17;
inline void MDQBTransaction::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.Yield)
  return yield_;
}
inline void MDQBTransaction::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.Yield)
}

// optional int64 OriginalPrice = 18;
inline void MDQBTransaction::clear_originalprice() {
  originalprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::originalprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.OriginalPrice)
  return originalprice_;
}
inline void MDQBTransaction::set_originalprice(::google::protobuf::int64 value) {
  
  originalprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.OriginalPrice)
}

// optional int64 FullPrice = 19;
inline void MDQBTransaction::clear_fullprice() {
  fullprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::fullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.FullPrice)
  return fullprice_;
}
inline void MDQBTransaction::set_fullprice(::google::protobuf::int64 value) {
  
  fullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.FullPrice)
}

// optional int32 InstrmtAssignmentMethod = 20;
inline void MDQBTransaction::clear_instrmtassignmentmethod() {
  instrmtassignmentmethod_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::instrmtassignmentmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.InstrmtAssignmentMethod)
  return instrmtassignmentmethod_;
}
inline void MDQBTransaction::set_instrmtassignmentmethod(::google::protobuf::int32 value) {
  
  instrmtassignmentmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.InstrmtAssignmentMethod)
}

// optional int32 TradeStatus = 21;
inline void MDQBTransaction::clear_tradestatus() {
  tradestatus_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::tradestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeStatus)
  return tradestatus_;
}
inline void MDQBTransaction::set_tradestatus(::google::protobuf::int32 value) {
  
  tradestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeStatus)
}

// optional int32 TradeType = 22;
inline void MDQBTransaction::clear_tradetype() {
  tradetype_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeType)
  return tradetype_;
}
inline void MDQBTransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeType)
}

// optional int32 PriceType = 23;
inline void MDQBTransaction::clear_pricetype() {
  pricetype_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::pricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.PriceType)
  return pricetype_;
}
inline void MDQBTransaction::set_pricetype(::google::protobuf::int32 value) {
  
  pricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.PriceType)
}

// optional int32 ClearSpeed = 24;
inline void MDQBTransaction::clear_clearspeed() {
  clearspeed_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::clearspeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.ClearSpeed)
  return clearspeed_;
}
inline void MDQBTransaction::set_clearspeed(::google::protobuf::int32 value) {
  
  clearspeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.ClearSpeed)
}

// optional int32 BrokerDataType = 25;
inline void MDQBTransaction::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.BrokerDataType)
  return brokerdatatype_;
}
inline void MDQBTransaction::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.BrokerDataType)
}

// optional int32 SSDetect = 26;
inline void MDQBTransaction::clear_ssdetect() {
  ssdetect_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::ssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SSDetect)
  return ssdetect_;
}
inline void MDQBTransaction::set_ssdetect(::google::protobuf::int32 value) {
  
  ssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SSDetect)
}

// optional string TradeReqID = 27;
inline void MDQBTransaction::clear_tradereqid() {
  tradereqid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBTransaction::tradereqid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  return tradereqid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_tradereqid(const ::std::string& value) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
inline void MDQBTransaction::set_tradereqid(const char* value) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
inline void MDQBTransaction::set_tradereqid(const char* value, size_t size) {
  
  tradereqid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}
inline ::std::string* MDQBTransaction::mutable_tradereqid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  return tradereqid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBTransaction::release_tradereqid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
  
  return tradereqid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_allocated_tradereqid(::std::string* tradereqid) {
  if (tradereqid != NULL) {
    
  } else {
    
  }
  tradereqid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradereqid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.TradeReqID)
}

// optional int64 TransactTime = 28;
inline void MDQBTransaction::clear_transacttime() {
  transacttime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQBTransaction::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.TransactTime)
  return transacttime_;
}
inline void MDQBTransaction::set_transacttime(::google::protobuf::int64 value) {
  
  transacttime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.TransactTime)
}

// optional int32 DataMultiplePowerOf10 = 29;
inline void MDQBTransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDQBTransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DataMultiplePowerOf10)
}

// optional string SettlType = 30;
inline void MDQBTransaction::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQBTransaction::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
inline void MDQBTransaction::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
inline void MDQBTransaction::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}
inline ::std::string* MDQBTransaction::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQBTransaction::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQBTransaction::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBTransaction.SettlType)
}

// optional int32 DealTransType = 31;
inline void MDQBTransaction::clear_dealtranstype() {
  dealtranstype_ = 0;
}
inline ::google::protobuf::int32 MDQBTransaction::dealtranstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBTransaction.DealTransType)
  return dealtranstype_;
}
inline void MDQBTransaction::set_dealtranstype(::google::protobuf::int32 value) {
  
  dealtranstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBTransaction.DealTransType)
}

inline const MDQBTransaction* MDQBTransaction::internal_default_instance() {
  return &MDQBTransaction_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDQBTransaction_2eproto__INCLUDED
