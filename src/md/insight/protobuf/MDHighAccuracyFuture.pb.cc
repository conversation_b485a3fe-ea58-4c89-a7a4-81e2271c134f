// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDHighAccuracyFuture.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDHighAccuracyFuture.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDHighAccuracyFuture_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDHighAccuracyFuture_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDHighAccuracyFuture_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDHighAccuracyFuture_2eproto() {
  protobuf_AddDesc_MDHighAccuracyFuture_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDHighAccuracyFuture.proto");
  GOOGLE_CHECK(file != NULL);
  MDHighAccuracyFuture_descriptor_ = file->message_type(0);
  static const int MDHighAccuracyFuture_offsets_[54] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, tradingdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, presettleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, openinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, settleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, predelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, currdelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, middlepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, impliedbuypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, impliedbuyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, impliedsellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, impliedsellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, positiontrend_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, changespeed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, changerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, changevalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, swing_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, commoditycontractnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, blockvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, eligiblevolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, strategyvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, preopeninterestdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, presettledate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, preclosedate_),
  };
  MDHighAccuracyFuture_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDHighAccuracyFuture_descriptor_,
      MDHighAccuracyFuture::internal_default_instance(),
      MDHighAccuracyFuture_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDHighAccuracyFuture),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDHighAccuracyFuture, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDHighAccuracyFuture_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDHighAccuracyFuture_descriptor_, MDHighAccuracyFuture::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDHighAccuracyFuture_2eproto() {
  MDHighAccuracyFuture_default_instance_.Shutdown();
  delete MDHighAccuracyFuture_reflection_;
}

void protobuf_InitDefaults_MDHighAccuracyFuture_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDHighAccuracyFuture_default_instance_.DefaultConstruct();
  MDHighAccuracyFuture_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDHighAccuracyFuture_2eproto_once_);
void protobuf_InitDefaults_MDHighAccuracyFuture_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDHighAccuracyFuture_2eproto_once_,
                 &protobuf_InitDefaults_MDHighAccuracyFuture_2eproto_impl);
}
void protobuf_AddDesc_MDHighAccuracyFuture_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDHighAccuracyFuture_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\032MDHighAccuracyFuture.proto\022\032com.htsc.m"
    "dc.insight.model\032\027ESecurityIDSource.prot"
    "o\032\023ESecurityType.proto\"\260\n\n\024MDHighAccurac"
    "yFuture\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDat"
    "e\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp"
    "\030\004 \001(\003\022\030\n\020TradingPhaseCode\030\005 \001(\t\022\?\n\020secu"
    "rityIDSource\030\006 \001(\0162%.com.htsc.mdc.model."
    "ESecurityIDSource\0227\n\014securityType\030\007 \001(\0162"
    "!.com.htsc.mdc.model.ESecurityType\022\r\n\005Ma"
    "xPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022\n\nPreClosePx\030\n"
    " \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022\030\n\020TotalVolumeTr"
    "ade\030\014 \001(\003\022\027\n\017TotalValueTrade\030\r \001(\003\022\016\n\006La"
    "stPx\030\016 \001(\003\022\016\n\006OpenPx\030\017 \001(\003\022\017\n\007ClosePx\030\020 "
    "\001(\003\022\016\n\006HighPx\030\021 \001(\003\022\r\n\005LowPx\030\022 \001(\003\022\023\n\013Tr"
    "adingDate\030\023 \001(\005\022\027\n\017PreOpenInterest\030\024 \001(\003"
    "\022\026\n\016PreSettlePrice\030\025 \001(\003\022\024\n\014OpenInterest"
    "\030\026 \001(\003\022\023\n\013SettlePrice\030\027 \001(\003\022\020\n\010PreDelta\030"
    "\030 \001(\003\022\021\n\tCurrDelta\030\031 \001(\003\022\020\n\010MiddlePx\030\032 \001"
    "(\003\022\024\n\014ImpliedBuyPx\030\033 \001(\003\022\025\n\rImpliedBuyQt"
    "y\030\034 \001(\003\022\025\n\rImpliedSellPx\030\035 \001(\003\022\026\n\016Implie"
    "dSellQty\030\036 \001(\003\022\025\n\rPositionTrend\030\037 \001(\003\022\023\n"
    "\013ChangeSpeed\030  \001(\003\022\022\n\nChangeRate\030! \001(\003\022\023"
    "\n\013ChangeValue\030\" \001(\003\022\r\n\005Swing\030# \001(\003\022\037\n\027Co"
    "mmodityContractNumber\030$ \001(\t\022\024\n\014ExchangeD"
    "ate\030% \001(\005\022\024\n\014ExchangeTime\030& \001(\005\022\021\n\tChann"
    "elNo\0302 \001(\005\022\031\n\rBuyPriceQueue\0303 \003(\003B\002\020\001\022\034\n"
    "\020BuyOrderQtyQueue\0304 \003(\003B\002\020\001\022\032\n\016SellPrice"
    "Queue\0305 \003(\003B\002\020\001\022\035\n\021SellOrderQtyQueue\0306 \003"
    "(\003B\002\020\001\022\031\n\rBuyOrderQueue\0307 \003(\003B\002\020\001\022\032\n\016Sel"
    "lOrderQueue\0308 \003(\003B\002\020\001\022\035\n\021BuyNumOrdersQue"
    "ue\0309 \003(\003B\002\020\001\022\036\n\022SellNumOrdersQueue\030: \003(\003"
    "B\002\020\001\022\035\n\025DataMultiplePowerOf10\030; \001(\005\022\030\n\020B"
    "lockVolumeTrade\030< \001(\003\022\033\n\023EligibleVolumeT"
    "rade\030= \001(\003\022\033\n\023StrategyVolumeTrade\030> \001(\003\022"
    "\033\n\023PreOpenInterestDate\030\? \001(\005\022\025\n\rPreSettl"
    "eDate\030@ \001(\005\022\024\n\014PreCloseDate\030A \001(\005B=\n\032com"
    ".htsc.mdc.insight.modelB\032MDHighAccuracyF"
    "utureProtosH\001\240\001\001b\006proto3", 1504);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDHighAccuracyFuture.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDHighAccuracyFuture_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDHighAccuracyFuture_2eproto_once_);
void protobuf_AddDesc_MDHighAccuracyFuture_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDHighAccuracyFuture_2eproto_once_,
                 &protobuf_AddDesc_MDHighAccuracyFuture_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDHighAccuracyFuture_2eproto {
  StaticDescriptorInitializer_MDHighAccuracyFuture_2eproto() {
    protobuf_AddDesc_MDHighAccuracyFuture_2eproto();
  }
} static_descriptor_initializer_MDHighAccuracyFuture_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDHighAccuracyFuture::kHTSCSecurityIDFieldNumber;
const int MDHighAccuracyFuture::kMDDateFieldNumber;
const int MDHighAccuracyFuture::kMDTimeFieldNumber;
const int MDHighAccuracyFuture::kDataTimestampFieldNumber;
const int MDHighAccuracyFuture::kTradingPhaseCodeFieldNumber;
const int MDHighAccuracyFuture::kSecurityIDSourceFieldNumber;
const int MDHighAccuracyFuture::kSecurityTypeFieldNumber;
const int MDHighAccuracyFuture::kMaxPxFieldNumber;
const int MDHighAccuracyFuture::kMinPxFieldNumber;
const int MDHighAccuracyFuture::kPreClosePxFieldNumber;
const int MDHighAccuracyFuture::kNumTradesFieldNumber;
const int MDHighAccuracyFuture::kTotalVolumeTradeFieldNumber;
const int MDHighAccuracyFuture::kTotalValueTradeFieldNumber;
const int MDHighAccuracyFuture::kLastPxFieldNumber;
const int MDHighAccuracyFuture::kOpenPxFieldNumber;
const int MDHighAccuracyFuture::kClosePxFieldNumber;
const int MDHighAccuracyFuture::kHighPxFieldNumber;
const int MDHighAccuracyFuture::kLowPxFieldNumber;
const int MDHighAccuracyFuture::kTradingDateFieldNumber;
const int MDHighAccuracyFuture::kPreOpenInterestFieldNumber;
const int MDHighAccuracyFuture::kPreSettlePriceFieldNumber;
const int MDHighAccuracyFuture::kOpenInterestFieldNumber;
const int MDHighAccuracyFuture::kSettlePriceFieldNumber;
const int MDHighAccuracyFuture::kPreDeltaFieldNumber;
const int MDHighAccuracyFuture::kCurrDeltaFieldNumber;
const int MDHighAccuracyFuture::kMiddlePxFieldNumber;
const int MDHighAccuracyFuture::kImpliedBuyPxFieldNumber;
const int MDHighAccuracyFuture::kImpliedBuyQtyFieldNumber;
const int MDHighAccuracyFuture::kImpliedSellPxFieldNumber;
const int MDHighAccuracyFuture::kImpliedSellQtyFieldNumber;
const int MDHighAccuracyFuture::kPositionTrendFieldNumber;
const int MDHighAccuracyFuture::kChangeSpeedFieldNumber;
const int MDHighAccuracyFuture::kChangeRateFieldNumber;
const int MDHighAccuracyFuture::kChangeValueFieldNumber;
const int MDHighAccuracyFuture::kSwingFieldNumber;
const int MDHighAccuracyFuture::kCommodityContractNumberFieldNumber;
const int MDHighAccuracyFuture::kExchangeDateFieldNumber;
const int MDHighAccuracyFuture::kExchangeTimeFieldNumber;
const int MDHighAccuracyFuture::kChannelNoFieldNumber;
const int MDHighAccuracyFuture::kBuyPriceQueueFieldNumber;
const int MDHighAccuracyFuture::kBuyOrderQtyQueueFieldNumber;
const int MDHighAccuracyFuture::kSellPriceQueueFieldNumber;
const int MDHighAccuracyFuture::kSellOrderQtyQueueFieldNumber;
const int MDHighAccuracyFuture::kBuyOrderQueueFieldNumber;
const int MDHighAccuracyFuture::kSellOrderQueueFieldNumber;
const int MDHighAccuracyFuture::kBuyNumOrdersQueueFieldNumber;
const int MDHighAccuracyFuture::kSellNumOrdersQueueFieldNumber;
const int MDHighAccuracyFuture::kDataMultiplePowerOf10FieldNumber;
const int MDHighAccuracyFuture::kBlockVolumeTradeFieldNumber;
const int MDHighAccuracyFuture::kEligibleVolumeTradeFieldNumber;
const int MDHighAccuracyFuture::kStrategyVolumeTradeFieldNumber;
const int MDHighAccuracyFuture::kPreOpenInterestDateFieldNumber;
const int MDHighAccuracyFuture::kPreSettleDateFieldNumber;
const int MDHighAccuracyFuture::kPreCloseDateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDHighAccuracyFuture::MDHighAccuracyFuture()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDHighAccuracyFuture_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
}

void MDHighAccuracyFuture::InitAsDefaultInstance() {
}

MDHighAccuracyFuture::MDHighAccuracyFuture(const MDHighAccuracyFuture& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
}

void MDHighAccuracyFuture::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&preclosedate_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(preclosedate_));
  _cached_size_ = 0;
}

MDHighAccuracyFuture::~MDHighAccuracyFuture() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  SharedDtor();
}

void MDHighAccuracyFuture::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDHighAccuracyFuture::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDHighAccuracyFuture::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDHighAccuracyFuture_descriptor_;
}

const MDHighAccuracyFuture& MDHighAccuracyFuture::default_instance() {
  protobuf_InitDefaults_MDHighAccuracyFuture_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDHighAccuracyFuture> MDHighAccuracyFuture_default_instance_;

MDHighAccuracyFuture* MDHighAccuracyFuture::New(::google::protobuf::Arena* arena) const {
  MDHighAccuracyFuture* n = new MDHighAccuracyFuture;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDHighAccuracyFuture::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDHighAccuracyFuture, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDHighAccuracyFuture*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, predelta_);
  tradingdate_ = 0;
  ZR_(currdelta_, impliedbuyqty_);
  ZR_(impliedsellpx_, changespeed_);
  ZR_(changerate_, channelno_);
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_ = 0;
  datamultiplepowerof10_ = 0;
  ZR_(blockvolumetrade_, eligiblevolumetrade_);
  ZR_(preopeninterestdate_, preclosedate_);

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
}

bool MDHighAccuracyFuture::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_TradingDate;
        break;
      }

      // optional int32 TradingDate = 19;
      case 19: {
        if (tag == 152) {
         parse_TradingDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradingdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_PreOpenInterest;
        break;
      }

      // optional int64 PreOpenInterest = 20;
      case 20: {
        if (tag == 160) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_PreSettlePrice;
        break;
      }

      // optional int64 PreSettlePrice = 21;
      case 21: {
        if (tag == 168) {
         parse_PreSettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_OpenInterest;
        break;
      }

      // optional int64 OpenInterest = 22;
      case 22: {
        if (tag == 176) {
         parse_OpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_SettlePrice;
        break;
      }

      // optional int64 SettlePrice = 23;
      case 23: {
        if (tag == 184) {
         parse_SettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_PreDelta;
        break;
      }

      // optional int64 PreDelta = 24;
      case 24: {
        if (tag == 192) {
         parse_PreDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &predelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_CurrDelta;
        break;
      }

      // optional int64 CurrDelta = 25;
      case 25: {
        if (tag == 200) {
         parse_CurrDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &currdelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_MiddlePx;
        break;
      }

      // optional int64 MiddlePx = 26;
      case 26: {
        if (tag == 208) {
         parse_MiddlePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &middlepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_ImpliedBuyPx;
        break;
      }

      // optional int64 ImpliedBuyPx = 27;
      case 27: {
        if (tag == 216) {
         parse_ImpliedBuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedbuypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_ImpliedBuyQty;
        break;
      }

      // optional int64 ImpliedBuyQty = 28;
      case 28: {
        if (tag == 224) {
         parse_ImpliedBuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedbuyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_ImpliedSellPx;
        break;
      }

      // optional int64 ImpliedSellPx = 29;
      case 29: {
        if (tag == 232) {
         parse_ImpliedSellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedsellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_ImpliedSellQty;
        break;
      }

      // optional int64 ImpliedSellQty = 30;
      case 30: {
        if (tag == 240) {
         parse_ImpliedSellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedsellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_PositionTrend;
        break;
      }

      // optional int64 PositionTrend = 31;
      case 31: {
        if (tag == 248) {
         parse_PositionTrend:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &positiontrend_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_ChangeSpeed;
        break;
      }

      // optional int64 ChangeSpeed = 32;
      case 32: {
        if (tag == 256) {
         parse_ChangeSpeed:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changespeed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_ChangeRate;
        break;
      }

      // optional int64 ChangeRate = 33;
      case 33: {
        if (tag == 264) {
         parse_ChangeRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_ChangeValue;
        break;
      }

      // optional int64 ChangeValue = 34;
      case 34: {
        if (tag == 272) {
         parse_ChangeValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changevalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_Swing;
        break;
      }

      // optional int64 Swing = 35;
      case 35: {
        if (tag == 280) {
         parse_Swing:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &swing_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_CommodityContractNumber;
        break;
      }

      // optional string CommodityContractNumber = 36;
      case 36: {
        if (tag == 290) {
         parse_CommodityContractNumber:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_commoditycontractnumber()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 37;
      case 37: {
        if (tag == 296) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 38;
      case 38: {
        if (tag == 304) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 50;
      case 50: {
        if (tag == 400) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 59;
      case 59: {
        if (tag == 472) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_BlockVolumeTrade;
        break;
      }

      // optional int64 BlockVolumeTrade = 60;
      case 60: {
        if (tag == 480) {
         parse_BlockVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &blockvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(488)) goto parse_EligibleVolumeTrade;
        break;
      }

      // optional int64 EligibleVolumeTrade = 61;
      case 61: {
        if (tag == 488) {
         parse_EligibleVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &eligiblevolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(496)) goto parse_StrategyVolumeTrade;
        break;
      }

      // optional int64 StrategyVolumeTrade = 62;
      case 62: {
        if (tag == 496) {
         parse_StrategyVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &strategyvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(504)) goto parse_PreOpenInterestDate;
        break;
      }

      // optional int32 PreOpenInterestDate = 63;
      case 63: {
        if (tag == 504) {
         parse_PreOpenInterestDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preopeninterestdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(512)) goto parse_PreSettleDate;
        break;
      }

      // optional int32 PreSettleDate = 64;
      case 64: {
        if (tag == 512) {
         parse_PreSettleDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &presettledate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(520)) goto parse_PreCloseDate;
        break;
      }

      // optional int32 PreCloseDate = 65;
      case 65: {
        if (tag == 520) {
         parse_PreCloseDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &preclosedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  return false;
#undef DO_
}

void MDHighAccuracyFuture::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->tradingdate(), output);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->preopeninterest(), output);
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->presettleprice(), output);
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->openinterest(), output);
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->settleprice(), output);
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->predelta(), output);
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->currdelta(), output);
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->middlepx(), output);
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->impliedbuypx(), output);
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->impliedbuyqty(), output);
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->impliedsellpx(), output);
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->impliedsellqty(), output);
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->positiontrend(), output);
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->changespeed(), output);
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->changerate(), output);
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->changevalue(), output);
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->swing(), output);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      36, this->commoditycontractnumber(), output);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(37, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(38, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(50, this->channelno(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(59, this->datamultiplepowerof10(), output);
  }

  // optional int64 BlockVolumeTrade = 60;
  if (this->blockvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(60, this->blockvolumetrade(), output);
  }

  // optional int64 EligibleVolumeTrade = 61;
  if (this->eligiblevolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(61, this->eligiblevolumetrade(), output);
  }

  // optional int64 StrategyVolumeTrade = 62;
  if (this->strategyvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(62, this->strategyvolumetrade(), output);
  }

  // optional int32 PreOpenInterestDate = 63;
  if (this->preopeninterestdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(63, this->preopeninterestdate(), output);
  }

  // optional int32 PreSettleDate = 64;
  if (this->presettledate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(64, this->presettledate(), output);
  }

  // optional int32 PreCloseDate = 65;
  if (this->preclosedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(65, this->preclosedate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
}

::google::protobuf::uint8* MDHighAccuracyFuture::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->tradingdate(), target);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->preopeninterest(), target);
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->presettleprice(), target);
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->openinterest(), target);
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->settleprice(), target);
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->predelta(), target);
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->currdelta(), target);
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->middlepx(), target);
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->impliedbuypx(), target);
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->impliedbuyqty(), target);
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->impliedsellpx(), target);
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->impliedsellqty(), target);
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->positiontrend(), target);
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->changespeed(), target);
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->changerate(), target);
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->changevalue(), target);
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->swing(), target);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        36, this->commoditycontractnumber(), target);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(37, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(38, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(50, this->channelno(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(59, this->datamultiplepowerof10(), target);
  }

  // optional int64 BlockVolumeTrade = 60;
  if (this->blockvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(60, this->blockvolumetrade(), target);
  }

  // optional int64 EligibleVolumeTrade = 61;
  if (this->eligiblevolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(61, this->eligiblevolumetrade(), target);
  }

  // optional int64 StrategyVolumeTrade = 62;
  if (this->strategyvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(62, this->strategyvolumetrade(), target);
  }

  // optional int32 PreOpenInterestDate = 63;
  if (this->preopeninterestdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(63, this->preopeninterestdate(), target);
  }

  // optional int32 PreSettleDate = 64;
  if (this->presettledate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(64, this->presettledate(), target);
  }

  // optional int32 PreCloseDate = 65;
  if (this->preclosedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(65, this->preclosedate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  return target;
}

size_t MDHighAccuracyFuture::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradingdate());
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preopeninterest());
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->presettleprice());
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openinterest());
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settleprice());
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->predelta());
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->currdelta());
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->middlepx());
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedbuypx());
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedbuyqty());
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedsellpx());
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedsellqty());
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->positiontrend());
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changespeed());
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changerate());
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changevalue());
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->swing());
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->commoditycontractnumber());
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 BlockVolumeTrade = 60;
  if (this->blockvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->blockvolumetrade());
  }

  // optional int64 EligibleVolumeTrade = 61;
  if (this->eligiblevolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->eligiblevolumetrade());
  }

  // optional int64 StrategyVolumeTrade = 62;
  if (this->strategyvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->strategyvolumetrade());
  }

  // optional int32 PreOpenInterestDate = 63;
  if (this->preopeninterestdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preopeninterestdate());
  }

  // optional int32 PreSettleDate = 64;
  if (this->presettledate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->presettledate());
  }

  // optional int32 PreCloseDate = 65;
  if (this->preclosedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->preclosedate());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDHighAccuracyFuture::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDHighAccuracyFuture* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDHighAccuracyFuture>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
    UnsafeMergeFrom(*source);
  }
}

void MDHighAccuracyFuture::MergeFrom(const MDHighAccuracyFuture& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDHighAccuracyFuture::UnsafeMergeFrom(const MDHighAccuracyFuture& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.tradingdate() != 0) {
    set_tradingdate(from.tradingdate());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.presettleprice() != 0) {
    set_presettleprice(from.presettleprice());
  }
  if (from.openinterest() != 0) {
    set_openinterest(from.openinterest());
  }
  if (from.settleprice() != 0) {
    set_settleprice(from.settleprice());
  }
  if (from.predelta() != 0) {
    set_predelta(from.predelta());
  }
  if (from.currdelta() != 0) {
    set_currdelta(from.currdelta());
  }
  if (from.middlepx() != 0) {
    set_middlepx(from.middlepx());
  }
  if (from.impliedbuypx() != 0) {
    set_impliedbuypx(from.impliedbuypx());
  }
  if (from.impliedbuyqty() != 0) {
    set_impliedbuyqty(from.impliedbuyqty());
  }
  if (from.impliedsellpx() != 0) {
    set_impliedsellpx(from.impliedsellpx());
  }
  if (from.impliedsellqty() != 0) {
    set_impliedsellqty(from.impliedsellqty());
  }
  if (from.positiontrend() != 0) {
    set_positiontrend(from.positiontrend());
  }
  if (from.changespeed() != 0) {
    set_changespeed(from.changespeed());
  }
  if (from.changerate() != 0) {
    set_changerate(from.changerate());
  }
  if (from.changevalue() != 0) {
    set_changevalue(from.changevalue());
  }
  if (from.swing() != 0) {
    set_swing(from.swing());
  }
  if (from.commoditycontractnumber().size() > 0) {

    commoditycontractnumber_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.commoditycontractnumber_);
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.blockvolumetrade() != 0) {
    set_blockvolumetrade(from.blockvolumetrade());
  }
  if (from.eligiblevolumetrade() != 0) {
    set_eligiblevolumetrade(from.eligiblevolumetrade());
  }
  if (from.strategyvolumetrade() != 0) {
    set_strategyvolumetrade(from.strategyvolumetrade());
  }
  if (from.preopeninterestdate() != 0) {
    set_preopeninterestdate(from.preopeninterestdate());
  }
  if (from.presettledate() != 0) {
    set_presettledate(from.presettledate());
  }
  if (from.preclosedate() != 0) {
    set_preclosedate(from.preclosedate());
  }
}

void MDHighAccuracyFuture::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDHighAccuracyFuture::CopyFrom(const MDHighAccuracyFuture& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDHighAccuracyFuture)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDHighAccuracyFuture::IsInitialized() const {

  return true;
}

void MDHighAccuracyFuture::Swap(MDHighAccuracyFuture* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDHighAccuracyFuture::InternalSwap(MDHighAccuracyFuture* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(tradingdate_, other->tradingdate_);
  std::swap(preopeninterest_, other->preopeninterest_);
  std::swap(presettleprice_, other->presettleprice_);
  std::swap(openinterest_, other->openinterest_);
  std::swap(settleprice_, other->settleprice_);
  std::swap(predelta_, other->predelta_);
  std::swap(currdelta_, other->currdelta_);
  std::swap(middlepx_, other->middlepx_);
  std::swap(impliedbuypx_, other->impliedbuypx_);
  std::swap(impliedbuyqty_, other->impliedbuyqty_);
  std::swap(impliedsellpx_, other->impliedsellpx_);
  std::swap(impliedsellqty_, other->impliedsellqty_);
  std::swap(positiontrend_, other->positiontrend_);
  std::swap(changespeed_, other->changespeed_);
  std::swap(changerate_, other->changerate_);
  std::swap(changevalue_, other->changevalue_);
  std::swap(swing_, other->swing_);
  commoditycontractnumber_.Swap(&other->commoditycontractnumber_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(blockvolumetrade_, other->blockvolumetrade_);
  std::swap(eligiblevolumetrade_, other->eligiblevolumetrade_);
  std::swap(strategyvolumetrade_, other->strategyvolumetrade_);
  std::swap(preopeninterestdate_, other->preopeninterestdate_);
  std::swap(presettledate_, other->presettledate_);
  std::swap(preclosedate_, other->preclosedate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDHighAccuracyFuture::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDHighAccuracyFuture_descriptor_;
  metadata.reflection = MDHighAccuracyFuture_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDHighAccuracyFuture

// optional string HTSCSecurityID = 1;
void MDHighAccuracyFuture::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDHighAccuracyFuture::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
}
void MDHighAccuracyFuture::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
}
void MDHighAccuracyFuture::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
}
::std::string* MDHighAccuracyFuture::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDHighAccuracyFuture::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDHighAccuracyFuture::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MDDate)
  return mddate_;
}
void MDHighAccuracyFuture::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MDDate)
}

// optional int32 MDTime = 3;
void MDHighAccuracyFuture::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MDTime)
  return mdtime_;
}
void MDHighAccuracyFuture::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDHighAccuracyFuture::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.DataTimestamp)
  return datatimestamp_;
}
void MDHighAccuracyFuture::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDHighAccuracyFuture::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDHighAccuracyFuture::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
}
void MDHighAccuracyFuture::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
}
void MDHighAccuracyFuture::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
}
::std::string* MDHighAccuracyFuture::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDHighAccuracyFuture::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDHighAccuracyFuture::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDHighAccuracyFuture::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDHighAccuracyFuture::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDHighAccuracyFuture::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDHighAccuracyFuture::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDHighAccuracyFuture::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.securityType)
}

// optional int64 MaxPx = 8;
void MDHighAccuracyFuture::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MaxPx)
  return maxpx_;
}
void MDHighAccuracyFuture::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MaxPx)
}

// optional int64 MinPx = 9;
void MDHighAccuracyFuture::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MinPx)
  return minpx_;
}
void MDHighAccuracyFuture::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MinPx)
}

// optional int64 PreClosePx = 10;
void MDHighAccuracyFuture::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreClosePx)
  return preclosepx_;
}
void MDHighAccuracyFuture::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDHighAccuracyFuture::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.NumTrades)
  return numtrades_;
}
void MDHighAccuracyFuture::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDHighAccuracyFuture::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDHighAccuracyFuture::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDHighAccuracyFuture::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TotalValueTrade)
  return totalvaluetrade_;
}
void MDHighAccuracyFuture::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDHighAccuracyFuture::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.LastPx)
  return lastpx_;
}
void MDHighAccuracyFuture::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.LastPx)
}

// optional int64 OpenPx = 15;
void MDHighAccuracyFuture::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.OpenPx)
  return openpx_;
}
void MDHighAccuracyFuture::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.OpenPx)
}

// optional int64 ClosePx = 16;
void MDHighAccuracyFuture::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ClosePx)
  return closepx_;
}
void MDHighAccuracyFuture::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ClosePx)
}

// optional int64 HighPx = 17;
void MDHighAccuracyFuture::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HighPx)
  return highpx_;
}
void MDHighAccuracyFuture::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.HighPx)
}

// optional int64 LowPx = 18;
void MDHighAccuracyFuture::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.LowPx)
  return lowpx_;
}
void MDHighAccuracyFuture::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.LowPx)
}

// optional int32 TradingDate = 19;
void MDHighAccuracyFuture::clear_tradingdate() {
  tradingdate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingDate)
  return tradingdate_;
}
void MDHighAccuracyFuture::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.TradingDate)
}

// optional int64 PreOpenInterest = 20;
void MDHighAccuracyFuture::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreOpenInterest)
  return preopeninterest_;
}
void MDHighAccuracyFuture::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreOpenInterest)
}

// optional int64 PreSettlePrice = 21;
void MDHighAccuracyFuture::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreSettlePrice)
  return presettleprice_;
}
void MDHighAccuracyFuture::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreSettlePrice)
}

// optional int64 OpenInterest = 22;
void MDHighAccuracyFuture::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.OpenInterest)
  return openinterest_;
}
void MDHighAccuracyFuture::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.OpenInterest)
}

// optional int64 SettlePrice = 23;
void MDHighAccuracyFuture::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SettlePrice)
  return settleprice_;
}
void MDHighAccuracyFuture::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SettlePrice)
}

// optional int64 PreDelta = 24;
void MDHighAccuracyFuture::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreDelta)
  return predelta_;
}
void MDHighAccuracyFuture::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreDelta)
}

// optional int64 CurrDelta = 25;
void MDHighAccuracyFuture::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CurrDelta)
  return currdelta_;
}
void MDHighAccuracyFuture::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CurrDelta)
}

// optional int64 MiddlePx = 26;
void MDHighAccuracyFuture::clear_middlepx() {
  middlepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::middlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MiddlePx)
  return middlepx_;
}
void MDHighAccuracyFuture::set_middlepx(::google::protobuf::int64 value) {
  
  middlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.MiddlePx)
}

// optional int64 ImpliedBuyPx = 27;
void MDHighAccuracyFuture::clear_impliedbuypx() {
  impliedbuypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::impliedbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedBuyPx)
  return impliedbuypx_;
}
void MDHighAccuracyFuture::set_impliedbuypx(::google::protobuf::int64 value) {
  
  impliedbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedBuyPx)
}

// optional int64 ImpliedBuyQty = 28;
void MDHighAccuracyFuture::clear_impliedbuyqty() {
  impliedbuyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::impliedbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedBuyQty)
  return impliedbuyqty_;
}
void MDHighAccuracyFuture::set_impliedbuyqty(::google::protobuf::int64 value) {
  
  impliedbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedBuyQty)
}

// optional int64 ImpliedSellPx = 29;
void MDHighAccuracyFuture::clear_impliedsellpx() {
  impliedsellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::impliedsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedSellPx)
  return impliedsellpx_;
}
void MDHighAccuracyFuture::set_impliedsellpx(::google::protobuf::int64 value) {
  
  impliedsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedSellPx)
}

// optional int64 ImpliedSellQty = 30;
void MDHighAccuracyFuture::clear_impliedsellqty() {
  impliedsellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::impliedsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedSellQty)
  return impliedsellqty_;
}
void MDHighAccuracyFuture::set_impliedsellqty(::google::protobuf::int64 value) {
  
  impliedsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ImpliedSellQty)
}

// optional int64 PositionTrend = 31;
void MDHighAccuracyFuture::clear_positiontrend() {
  positiontrend_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::positiontrend() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PositionTrend)
  return positiontrend_;
}
void MDHighAccuracyFuture::set_positiontrend(::google::protobuf::int64 value) {
  
  positiontrend_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PositionTrend)
}

// optional int64 ChangeSpeed = 32;
void MDHighAccuracyFuture::clear_changespeed() {
  changespeed_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::changespeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeSpeed)
  return changespeed_;
}
void MDHighAccuracyFuture::set_changespeed(::google::protobuf::int64 value) {
  
  changespeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeSpeed)
}

// optional int64 ChangeRate = 33;
void MDHighAccuracyFuture::clear_changerate() {
  changerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::changerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeRate)
  return changerate_;
}
void MDHighAccuracyFuture::set_changerate(::google::protobuf::int64 value) {
  
  changerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeRate)
}

// optional int64 ChangeValue = 34;
void MDHighAccuracyFuture::clear_changevalue() {
  changevalue_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::changevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeValue)
  return changevalue_;
}
void MDHighAccuracyFuture::set_changevalue(::google::protobuf::int64 value) {
  
  changevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChangeValue)
}

// optional int64 Swing = 35;
void MDHighAccuracyFuture::clear_swing() {
  swing_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::swing() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.Swing)
  return swing_;
}
void MDHighAccuracyFuture::set_swing(::google::protobuf::int64 value) {
  
  swing_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.Swing)
}

// optional string CommodityContractNumber = 36;
void MDHighAccuracyFuture::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDHighAccuracyFuture::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
}
void MDHighAccuracyFuture::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
}
void MDHighAccuracyFuture::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
}
::std::string* MDHighAccuracyFuture::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDHighAccuracyFuture::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDHighAccuracyFuture::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDHighAccuracyFuture.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
void MDHighAccuracyFuture::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ExchangeDate)
  return exchangedate_;
}
void MDHighAccuracyFuture::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
void MDHighAccuracyFuture::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ExchangeTime)
  return exchangetime_;
}
void MDHighAccuracyFuture::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ExchangeTime)
}

// optional int32 ChannelNo = 50;
void MDHighAccuracyFuture::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChannelNo)
  return channelno_;
}
void MDHighAccuracyFuture::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDHighAccuracyFuture::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDHighAccuracyFuture::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDHighAccuracyFuture::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyPriceQueue)
}
void MDHighAccuracyFuture::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDHighAccuracyFuture::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDHighAccuracyFuture::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDHighAccuracyFuture::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQtyQueue)
}
void MDHighAccuracyFuture::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDHighAccuracyFuture::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDHighAccuracyFuture::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDHighAccuracyFuture::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellPriceQueue)
}
void MDHighAccuracyFuture::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDHighAccuracyFuture::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDHighAccuracyFuture::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDHighAccuracyFuture::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQtyQueue)
}
void MDHighAccuracyFuture::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDHighAccuracyFuture::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDHighAccuracyFuture::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDHighAccuracyFuture::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQueue)
}
void MDHighAccuracyFuture::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDHighAccuracyFuture::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDHighAccuracyFuture::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDHighAccuracyFuture::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQueue)
}
void MDHighAccuracyFuture::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDHighAccuracyFuture::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDHighAccuracyFuture::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDHighAccuracyFuture::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyNumOrdersQueue)
}
void MDHighAccuracyFuture::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDHighAccuracyFuture::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDHighAccuracyFuture::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDHighAccuracyFuture::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDHighAccuracyFuture::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellNumOrdersQueue)
}
void MDHighAccuracyFuture::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDHighAccuracyFuture::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDHighAccuracyFuture::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDHighAccuracyFuture.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
void MDHighAccuracyFuture::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDHighAccuracyFuture::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.DataMultiplePowerOf10)
}

// optional int64 BlockVolumeTrade = 60;
void MDHighAccuracyFuture::clear_blockvolumetrade() {
  blockvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::blockvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BlockVolumeTrade)
  return blockvolumetrade_;
}
void MDHighAccuracyFuture::set_blockvolumetrade(::google::protobuf::int64 value) {
  
  blockvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.BlockVolumeTrade)
}

// optional int64 EligibleVolumeTrade = 61;
void MDHighAccuracyFuture::clear_eligiblevolumetrade() {
  eligiblevolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::eligiblevolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.EligibleVolumeTrade)
  return eligiblevolumetrade_;
}
void MDHighAccuracyFuture::set_eligiblevolumetrade(::google::protobuf::int64 value) {
  
  eligiblevolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.EligibleVolumeTrade)
}

// optional int64 StrategyVolumeTrade = 62;
void MDHighAccuracyFuture::clear_strategyvolumetrade() {
  strategyvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDHighAccuracyFuture::strategyvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.StrategyVolumeTrade)
  return strategyvolumetrade_;
}
void MDHighAccuracyFuture::set_strategyvolumetrade(::google::protobuf::int64 value) {
  
  strategyvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.StrategyVolumeTrade)
}

// optional int32 PreOpenInterestDate = 63;
void MDHighAccuracyFuture::clear_preopeninterestdate() {
  preopeninterestdate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::preopeninterestdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreOpenInterestDate)
  return preopeninterestdate_;
}
void MDHighAccuracyFuture::set_preopeninterestdate(::google::protobuf::int32 value) {
  
  preopeninterestdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreOpenInterestDate)
}

// optional int32 PreSettleDate = 64;
void MDHighAccuracyFuture::clear_presettledate() {
  presettledate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::presettledate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreSettleDate)
  return presettledate_;
}
void MDHighAccuracyFuture::set_presettledate(::google::protobuf::int32 value) {
  
  presettledate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreSettleDate)
}

// optional int32 PreCloseDate = 65;
void MDHighAccuracyFuture::clear_preclosedate() {
  preclosedate_ = 0;
}
::google::protobuf::int32 MDHighAccuracyFuture::preclosedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreCloseDate)
  return preclosedate_;
}
void MDHighAccuracyFuture::set_preclosedate(::google::protobuf::int32 value) {
  
  preclosedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDHighAccuracyFuture.PreCloseDate)
}

inline const MDHighAccuracyFuture* MDHighAccuracyFuture::internal_default_instance() {
  return &MDHighAccuracyFuture_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
