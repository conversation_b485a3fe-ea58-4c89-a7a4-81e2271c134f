// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLIndicativeQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSLIndicativeQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSLIndicativeQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSLIndicativeQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSLIndicativeQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSLIndicativeQuote_2eproto() {
  protobuf_AddDesc_MDSLIndicativeQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSLIndicativeQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDSLIndicativeQuote_descriptor_ = file->message_type(0);
  static const int MDSLIndicativeQuote_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htsclendamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htsclendterms_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htscbestlendrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htscborrowamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htscborrowterms_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, htscborrowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, tradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, pretradevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, pretrademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, datamultiplepowerof10_),
  };
  MDSLIndicativeQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSLIndicativeQuote_descriptor_,
      MDSLIndicativeQuote::internal_default_instance(),
      MDSLIndicativeQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSLIndicativeQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSLIndicativeQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSLIndicativeQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSLIndicativeQuote_descriptor_, MDSLIndicativeQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSLIndicativeQuote_2eproto() {
  MDSLIndicativeQuote_default_instance_.Shutdown();
  delete MDSLIndicativeQuote_reflection_;
}

void protobuf_InitDefaults_MDSLIndicativeQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSLIndicativeQuote_default_instance_.DefaultConstruct();
  MDSLIndicativeQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSLIndicativeQuote_2eproto_once_);
void protobuf_InitDefaults_MDSLIndicativeQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSLIndicativeQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDSLIndicativeQuote_2eproto_impl);
}
void protobuf_AddDesc_MDSLIndicativeQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSLIndicativeQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031MDSLIndicativeQuote.proto\022\032com.htsc.md"
    "c.insight.model\032\027ESecurityIDSource.proto"
    "\032\023ESecurityType.proto\"\247\004\n\023MDSLIndicative"
    "Quote\022\026\n\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030"
    "\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004"
    " \001(\003\022\030\n\020TradingPhaseCode\030\005 \001(\t\022\?\n\020securi"
    "tyIDSource\030\006 \001(\0162%.com.htsc.mdc.model.ES"
    "ecurityIDSource\0227\n\014securityType\030\007 \001(\0162!."
    "com.htsc.mdc.model.ESecurityType\022\016\n\006Last"
    "Px\030\010 \001(\003\022\022\n\nPreClosePx\030\t \001(\003\022\026\n\016HtscLend"
    "Amount\030\n \001(\003\022\025\n\rHtscLendTerms\030\013 \001(\t\022\030\n\020H"
    "tscBestLendRate\030\014 \001(\003\022\030\n\020HtscBorrowAmoun"
    "t\030\r \001(\003\022\027\n\017HtscBorrowTerms\030\016 \001(\t\022\026\n\016Htsc"
    "BorrowRate\030\017 \001(\003\022\023\n\013TradeVolume\030\020 \001(\003\022\022\n"
    "\nTradeMoney\030\021 \001(\003\022\026\n\016PreTradeVolume\030\022 \001("
    "\003\022\025\n\rPreTradeMoney\030\023 \001(\003\022\035\n\025DataMultiple"
    "PowerOf10\030\024 \001(\005B<\n\032com.htsc.mdc.insight."
    "modelB\031MDSLIndicativeQuoteProtosH\001\240\001\001b\006p"
    "roto3", 725);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSLIndicativeQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSLIndicativeQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSLIndicativeQuote_2eproto_once_);
void protobuf_AddDesc_MDSLIndicativeQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSLIndicativeQuote_2eproto_once_,
                 &protobuf_AddDesc_MDSLIndicativeQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSLIndicativeQuote_2eproto {
  StaticDescriptorInitializer_MDSLIndicativeQuote_2eproto() {
    protobuf_AddDesc_MDSLIndicativeQuote_2eproto();
  }
} static_descriptor_initializer_MDSLIndicativeQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSLIndicativeQuote::kHTSCSecurityIDFieldNumber;
const int MDSLIndicativeQuote::kMDDateFieldNumber;
const int MDSLIndicativeQuote::kMDTimeFieldNumber;
const int MDSLIndicativeQuote::kDataTimestampFieldNumber;
const int MDSLIndicativeQuote::kTradingPhaseCodeFieldNumber;
const int MDSLIndicativeQuote::kSecurityIDSourceFieldNumber;
const int MDSLIndicativeQuote::kSecurityTypeFieldNumber;
const int MDSLIndicativeQuote::kLastPxFieldNumber;
const int MDSLIndicativeQuote::kPreClosePxFieldNumber;
const int MDSLIndicativeQuote::kHtscLendAmountFieldNumber;
const int MDSLIndicativeQuote::kHtscLendTermsFieldNumber;
const int MDSLIndicativeQuote::kHtscBestLendRateFieldNumber;
const int MDSLIndicativeQuote::kHtscBorrowAmountFieldNumber;
const int MDSLIndicativeQuote::kHtscBorrowTermsFieldNumber;
const int MDSLIndicativeQuote::kHtscBorrowRateFieldNumber;
const int MDSLIndicativeQuote::kTradeVolumeFieldNumber;
const int MDSLIndicativeQuote::kTradeMoneyFieldNumber;
const int MDSLIndicativeQuote::kPreTradeVolumeFieldNumber;
const int MDSLIndicativeQuote::kPreTradeMoneyFieldNumber;
const int MDSLIndicativeQuote::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSLIndicativeQuote::MDSLIndicativeQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSLIndicativeQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
}

void MDSLIndicativeQuote::InitAsDefaultInstance() {
}

MDSLIndicativeQuote::MDSLIndicativeQuote(const MDSLIndicativeQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
}

void MDSLIndicativeQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htsclendterms_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscborrowterms_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSLIndicativeQuote::~MDSLIndicativeQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  SharedDtor();
}

void MDSLIndicativeQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htsclendterms_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscborrowterms_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSLIndicativeQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSLIndicativeQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSLIndicativeQuote_descriptor_;
}

const MDSLIndicativeQuote& MDSLIndicativeQuote::default_instance() {
  protobuf_InitDefaults_MDSLIndicativeQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSLIndicativeQuote> MDSLIndicativeQuote_default_instance_;

MDSLIndicativeQuote* MDSLIndicativeQuote::New(::google::protobuf::Arena* arena) const {
  MDSLIndicativeQuote* n = new MDSLIndicativeQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSLIndicativeQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSLIndicativeQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSLIndicativeQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, lastpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(preclosepx_, tradevolume_);
  htsclendterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscborrowterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(trademoney_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDSLIndicativeQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 8;
      case 8: {
        if (tag == 64) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 9;
      case 9: {
        if (tag == 72) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_HtscLendAmount;
        break;
      }

      // optional int64 HtscLendAmount = 10;
      case 10: {
        if (tag == 80) {
         parse_HtscLendAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htsclendamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_HtscLendTerms;
        break;
      }

      // optional string HtscLendTerms = 11;
      case 11: {
        if (tag == 90) {
         parse_HtscLendTerms:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htsclendterms()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htsclendterms().data(), this->htsclendterms().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HtscBestLendRate;
        break;
      }

      // optional int64 HtscBestLendRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HtscBestLendRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscbestlendrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_HtscBorrowAmount;
        break;
      }

      // optional int64 HtscBorrowAmount = 13;
      case 13: {
        if (tag == 104) {
         parse_HtscBorrowAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_HtscBorrowTerms;
        break;
      }

      // optional string HtscBorrowTerms = 14;
      case 14: {
        if (tag == 114) {
         parse_HtscBorrowTerms:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscborrowterms()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscborrowterms().data(), this->htscborrowterms().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HtscBorrowRate;
        break;
      }

      // optional int64 HtscBorrowRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HtscBorrowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &htscborrowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_TradeVolume;
        break;
      }

      // optional int64 TradeVolume = 16;
      case 16: {
        if (tag == 128) {
         parse_TradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_TradeMoney;
        break;
      }

      // optional int64 TradeMoney = 17;
      case 17: {
        if (tag == 136) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_PreTradeVolume;
        break;
      }

      // optional int64 PreTradeVolume = 18;
      case 18: {
        if (tag == 144) {
         parse_PreTradeVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &pretradevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_PreTradeMoney;
        break;
      }

      // optional int64 PreTradeMoney = 19;
      case 19: {
        if (tag == 152) {
         parse_PreTradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &pretrademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 20;
      case 20: {
        if (tag == 160) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  return false;
#undef DO_
}

void MDSLIndicativeQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 LastPx = 8;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastpx(), output);
  }

  // optional int64 PreClosePx = 9;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->preclosepx(), output);
  }

  // optional int64 HtscLendAmount = 10;
  if (this->htsclendamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->htsclendamount(), output);
  }

  // optional string HtscLendTerms = 11;
  if (this->htsclendterms().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htsclendterms().data(), this->htsclendterms().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->htsclendterms(), output);
  }

  // optional int64 HtscBestLendRate = 12;
  if (this->htscbestlendrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->htscbestlendrate(), output);
  }

  // optional int64 HtscBorrowAmount = 13;
  if (this->htscborrowamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->htscborrowamount(), output);
  }

  // optional string HtscBorrowTerms = 14;
  if (this->htscborrowterms().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscborrowterms().data(), this->htscborrowterms().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->htscborrowterms(), output);
  }

  // optional int64 HtscBorrowRate = 15;
  if (this->htscborrowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->htscborrowrate(), output);
  }

  // optional int64 TradeVolume = 16;
  if (this->tradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->tradevolume(), output);
  }

  // optional int64 TradeMoney = 17;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->trademoney(), output);
  }

  // optional int64 PreTradeVolume = 18;
  if (this->pretradevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->pretradevolume(), output);
  }

  // optional int64 PreTradeMoney = 19;
  if (this->pretrademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->pretrademoney(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
}

::google::protobuf::uint8* MDSLIndicativeQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 LastPx = 8;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastpx(), target);
  }

  // optional int64 PreClosePx = 9;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->preclosepx(), target);
  }

  // optional int64 HtscLendAmount = 10;
  if (this->htsclendamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->htsclendamount(), target);
  }

  // optional string HtscLendTerms = 11;
  if (this->htsclendterms().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htsclendterms().data(), this->htsclendterms().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->htsclendterms(), target);
  }

  // optional int64 HtscBestLendRate = 12;
  if (this->htscbestlendrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->htscbestlendrate(), target);
  }

  // optional int64 HtscBorrowAmount = 13;
  if (this->htscborrowamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->htscborrowamount(), target);
  }

  // optional string HtscBorrowTerms = 14;
  if (this->htscborrowterms().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscborrowterms().data(), this->htscborrowterms().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->htscborrowterms(), target);
  }

  // optional int64 HtscBorrowRate = 15;
  if (this->htscborrowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->htscborrowrate(), target);
  }

  // optional int64 TradeVolume = 16;
  if (this->tradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->tradevolume(), target);
  }

  // optional int64 TradeMoney = 17;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->trademoney(), target);
  }

  // optional int64 PreTradeVolume = 18;
  if (this->pretradevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->pretradevolume(), target);
  }

  // optional int64 PreTradeMoney = 19;
  if (this->pretrademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->pretrademoney(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  return target;
}

size_t MDSLIndicativeQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 LastPx = 8;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 PreClosePx = 9;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 HtscLendAmount = 10;
  if (this->htsclendamount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htsclendamount());
  }

  // optional string HtscLendTerms = 11;
  if (this->htsclendterms().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htsclendterms());
  }

  // optional int64 HtscBestLendRate = 12;
  if (this->htscbestlendrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscbestlendrate());
  }

  // optional int64 HtscBorrowAmount = 13;
  if (this->htscborrowamount() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowamount());
  }

  // optional string HtscBorrowTerms = 14;
  if (this->htscborrowterms().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscborrowterms());
  }

  // optional int64 HtscBorrowRate = 15;
  if (this->htscborrowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->htscborrowrate());
  }

  // optional int64 TradeVolume = 16;
  if (this->tradevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradevolume());
  }

  // optional int64 TradeMoney = 17;
  if (this->trademoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->trademoney());
  }

  // optional int64 PreTradeVolume = 18;
  if (this->pretradevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->pretradevolume());
  }

  // optional int64 PreTradeMoney = 19;
  if (this->pretrademoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->pretrademoney());
  }

  // optional int32 DataMultiplePowerOf10 = 20;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSLIndicativeQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSLIndicativeQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSLIndicativeQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDSLIndicativeQuote::MergeFrom(const MDSLIndicativeQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSLIndicativeQuote::UnsafeMergeFrom(const MDSLIndicativeQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.htsclendamount() != 0) {
    set_htsclendamount(from.htsclendamount());
  }
  if (from.htsclendterms().size() > 0) {

    htsclendterms_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htsclendterms_);
  }
  if (from.htscbestlendrate() != 0) {
    set_htscbestlendrate(from.htscbestlendrate());
  }
  if (from.htscborrowamount() != 0) {
    set_htscborrowamount(from.htscborrowamount());
  }
  if (from.htscborrowterms().size() > 0) {

    htscborrowterms_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscborrowterms_);
  }
  if (from.htscborrowrate() != 0) {
    set_htscborrowrate(from.htscborrowrate());
  }
  if (from.tradevolume() != 0) {
    set_tradevolume(from.tradevolume());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.pretradevolume() != 0) {
    set_pretradevolume(from.pretradevolume());
  }
  if (from.pretrademoney() != 0) {
    set_pretrademoney(from.pretrademoney());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDSLIndicativeQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSLIndicativeQuote::CopyFrom(const MDSLIndicativeQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSLIndicativeQuote::IsInitialized() const {

  return true;
}

void MDSLIndicativeQuote::Swap(MDSLIndicativeQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSLIndicativeQuote::InternalSwap(MDSLIndicativeQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(htsclendamount_, other->htsclendamount_);
  htsclendterms_.Swap(&other->htsclendterms_);
  std::swap(htscbestlendrate_, other->htscbestlendrate_);
  std::swap(htscborrowamount_, other->htscborrowamount_);
  htscborrowterms_.Swap(&other->htscborrowterms_);
  std::swap(htscborrowrate_, other->htscborrowrate_);
  std::swap(tradevolume_, other->tradevolume_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(pretradevolume_, other->pretradevolume_);
  std::swap(pretrademoney_, other->pretrademoney_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSLIndicativeQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSLIndicativeQuote_descriptor_;
  metadata.reflection = MDSLIndicativeQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLIndicativeQuote

// optional string HTSCSecurityID = 1;
void MDSLIndicativeQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLIndicativeQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
void MDSLIndicativeQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
void MDSLIndicativeQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
::std::string* MDSLIndicativeQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLIndicativeQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSLIndicativeQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSLIndicativeQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDDate)
  return mddate_;
}
void MDSLIndicativeQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDSLIndicativeQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSLIndicativeQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDTime)
  return mdtime_;
}
void MDSLIndicativeQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSLIndicativeQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataTimestamp)
  return datatimestamp_;
}
void MDSLIndicativeQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSLIndicativeQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLIndicativeQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
void MDSLIndicativeQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
void MDSLIndicativeQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
::std::string* MDSLIndicativeQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLIndicativeQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSLIndicativeQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSLIndicativeQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSLIndicativeQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSLIndicativeQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSLIndicativeQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSLIndicativeQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityType)
}

// optional int64 LastPx = 8;
void MDSLIndicativeQuote::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.LastPx)
  return lastpx_;
}
void MDSLIndicativeQuote::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.LastPx)
}

// optional int64 PreClosePx = 9;
void MDSLIndicativeQuote::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreClosePx)
  return preclosepx_;
}
void MDSLIndicativeQuote::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreClosePx)
}

// optional int64 HtscLendAmount = 10;
void MDSLIndicativeQuote::clear_htsclendamount() {
  htsclendamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::htsclendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendAmount)
  return htsclendamount_;
}
void MDSLIndicativeQuote::set_htsclendamount(::google::protobuf::int64 value) {
  
  htsclendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendAmount)
}

// optional string HtscLendTerms = 11;
void MDSLIndicativeQuote::clear_htsclendterms() {
  htsclendterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLIndicativeQuote::htsclendterms() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  return htsclendterms_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_htsclendterms(const ::std::string& value) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
void MDSLIndicativeQuote::set_htsclendterms(const char* value) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
void MDSLIndicativeQuote::set_htsclendterms(const char* value, size_t size) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
::std::string* MDSLIndicativeQuote::mutable_htsclendterms() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  return htsclendterms_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLIndicativeQuote::release_htsclendterms() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  
  return htsclendterms_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_allocated_htsclendterms(::std::string* htsclendterms) {
  if (htsclendterms != NULL) {
    
  } else {
    
  }
  htsclendterms_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htsclendterms);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}

// optional int64 HtscBestLendRate = 12;
void MDSLIndicativeQuote::clear_htscbestlendrate() {
  htscbestlendrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::htscbestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBestLendRate)
  return htscbestlendrate_;
}
void MDSLIndicativeQuote::set_htscbestlendrate(::google::protobuf::int64 value) {
  
  htscbestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBestLendRate)
}

// optional int64 HtscBorrowAmount = 13;
void MDSLIndicativeQuote::clear_htscborrowamount() {
  htscborrowamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::htscborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowAmount)
  return htscborrowamount_;
}
void MDSLIndicativeQuote::set_htscborrowamount(::google::protobuf::int64 value) {
  
  htscborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowAmount)
}

// optional string HtscBorrowTerms = 14;
void MDSLIndicativeQuote::clear_htscborrowterms() {
  htscborrowterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSLIndicativeQuote::htscborrowterms() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  return htscborrowterms_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_htscborrowterms(const ::std::string& value) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
void MDSLIndicativeQuote::set_htscborrowterms(const char* value) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
void MDSLIndicativeQuote::set_htscborrowterms(const char* value, size_t size) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
::std::string* MDSLIndicativeQuote::mutable_htscborrowterms() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  return htscborrowterms_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSLIndicativeQuote::release_htscborrowterms() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  
  return htscborrowterms_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSLIndicativeQuote::set_allocated_htscborrowterms(::std::string* htscborrowterms) {
  if (htscborrowterms != NULL) {
    
  } else {
    
  }
  htscborrowterms_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscborrowterms);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}

// optional int64 HtscBorrowRate = 15;
void MDSLIndicativeQuote::clear_htscborrowrate() {
  htscborrowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::htscborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowRate)
  return htscborrowrate_;
}
void MDSLIndicativeQuote::set_htscborrowrate(::google::protobuf::int64 value) {
  
  htscborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowRate)
}

// optional int64 TradeVolume = 16;
void MDSLIndicativeQuote::clear_tradevolume() {
  tradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeVolume)
  return tradevolume_;
}
void MDSLIndicativeQuote::set_tradevolume(::google::protobuf::int64 value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeVolume)
}

// optional int64 TradeMoney = 17;
void MDSLIndicativeQuote::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeMoney)
  return trademoney_;
}
void MDSLIndicativeQuote::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeMoney)
}

// optional int64 PreTradeVolume = 18;
void MDSLIndicativeQuote::clear_pretradevolume() {
  pretradevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::pretradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeVolume)
  return pretradevolume_;
}
void MDSLIndicativeQuote::set_pretradevolume(::google::protobuf::int64 value) {
  
  pretradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeVolume)
}

// optional int64 PreTradeMoney = 19;
void MDSLIndicativeQuote::clear_pretrademoney() {
  pretrademoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSLIndicativeQuote::pretrademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeMoney)
  return pretrademoney_;
}
void MDSLIndicativeQuote::set_pretrademoney(::google::protobuf::int64 value) {
  
  pretrademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeMoney)
}

// optional int32 DataMultiplePowerOf10 = 20;
void MDSLIndicativeQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSLIndicativeQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSLIndicativeQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataMultiplePowerOf10)
}

inline const MDSLIndicativeQuote* MDSLIndicativeQuote::internal_default_instance() {
  return &MDSLIndicativeQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
