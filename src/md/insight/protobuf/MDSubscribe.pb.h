// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSubscribe.proto

#ifndef PROTOBUF_MDSubscribe_2eproto__INCLUDED
#define PROTOBUF_MDSubscribe_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "SecuritySourceType.pb.h"
#include "EMarketDataType.pb.h"
#include "InsightErrorContext.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSubscribe_2eproto();
void protobuf_InitDefaults_MDSubscribe_2eproto();
void protobuf_AssignDesc_MDSubscribe_2eproto();
void protobuf_ShutdownFile_MDSubscribe_2eproto();

class MDSubscribeRequest;
class MDSubscribeResponse;
class SubscribeAll;
class SubscribeByID;
class SubscribeByIDDetail;
class SubscribeBySourceType;
class SubscribeBySourceTypeDetail;

enum ESubscribeActionType {
  COVERAGE = 0,
  ADD = 1,
  DECREASE = 2,
  CANCEL = 3,
  ESubscribeActionType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ESubscribeActionType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ESubscribeActionType_IsValid(int value);
const ESubscribeActionType ESubscribeActionType_MIN = COVERAGE;
const ESubscribeActionType ESubscribeActionType_MAX = CANCEL;
const int ESubscribeActionType_ARRAYSIZE = ESubscribeActionType_MAX + 1;

const ::google::protobuf::EnumDescriptor* ESubscribeActionType_descriptor();
inline const ::std::string& ESubscribeActionType_Name(ESubscribeActionType value) {
  return ::google::protobuf::internal::NameOfEnum(
    ESubscribeActionType_descriptor(), value);
}
inline bool ESubscribeActionType_Parse(
    const ::std::string& name, ESubscribeActionType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ESubscribeActionType>(
    ESubscribeActionType_descriptor(), name, value);
}
// ===================================================================

class MDSubscribeRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSubscribeRequest) */ {
 public:
  MDSubscribeRequest();
  virtual ~MDSubscribeRequest();

  MDSubscribeRequest(const MDSubscribeRequest& from);

  inline MDSubscribeRequest& operator=(const MDSubscribeRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSubscribeRequest& default_instance();

  static const MDSubscribeRequest* internal_default_instance();

  void Swap(MDSubscribeRequest* other);

  // implements Message ----------------------------------------------

  inline MDSubscribeRequest* New() const { return New(NULL); }

  MDSubscribeRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSubscribeRequest& from);
  void MergeFrom(const MDSubscribeRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSubscribeRequest* other);
  void UnsafeMergeFrom(const MDSubscribeRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
  void clear_subscribeactiontype();
  static const int kSubscribeActionTypeFieldNumber = 1;
  ::com::htsc::mdc::insight::model::ESubscribeActionType subscribeactiontype() const;
  void set_subscribeactiontype(::com::htsc::mdc::insight::model::ESubscribeActionType value);

  // optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
  bool has_subscribeall() const;
  void clear_subscribeall();
  static const int kSubscribeAllFieldNumber = 2;
  const ::com::htsc::mdc::insight::model::SubscribeAll& subscribeall() const;
  ::com::htsc::mdc::insight::model::SubscribeAll* mutable_subscribeall();
  ::com::htsc::mdc::insight::model::SubscribeAll* release_subscribeall();
  void set_allocated_subscribeall(::com::htsc::mdc::insight::model::SubscribeAll* subscribeall);

  // optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
  bool has_subscribebysourcetype() const;
  void clear_subscribebysourcetype();
  static const int kSubscribeBySourceTypeFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::SubscribeBySourceType& subscribebysourcetype() const;
  ::com::htsc::mdc::insight::model::SubscribeBySourceType* mutable_subscribebysourcetype();
  ::com::htsc::mdc::insight::model::SubscribeBySourceType* release_subscribebysourcetype();
  void set_allocated_subscribebysourcetype(::com::htsc::mdc::insight::model::SubscribeBySourceType* subscribebysourcetype);

  // optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
  bool has_subscribebyid() const;
  void clear_subscribebyid();
  static const int kSubscribeByIDFieldNumber = 4;
  const ::com::htsc::mdc::insight::model::SubscribeByID& subscribebyid() const;
  ::com::htsc::mdc::insight::model::SubscribeByID* mutable_subscribebyid();
  ::com::htsc::mdc::insight::model::SubscribeByID* release_subscribebyid();
  void set_allocated_subscribebyid(::com::htsc::mdc::insight::model::SubscribeByID* subscribebyid);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSubscribeRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::com::htsc::mdc::insight::model::SubscribeAll* subscribeall_;
  ::com::htsc::mdc::insight::model::SubscribeBySourceType* subscribebysourcetype_;
  ::com::htsc::mdc::insight::model::SubscribeByID* subscribebyid_;
  int subscribeactiontype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSubscribeRequest> MDSubscribeRequest_default_instance_;

// -------------------------------------------------------------------

class SubscribeAll : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SubscribeAll) */ {
 public:
  SubscribeAll();
  virtual ~SubscribeAll();

  SubscribeAll(const SubscribeAll& from);

  inline SubscribeAll& operator=(const SubscribeAll& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SubscribeAll& default_instance();

  static const SubscribeAll* internal_default_instance();

  void Swap(SubscribeAll* other);

  // implements Message ----------------------------------------------

  inline SubscribeAll* New() const { return New(NULL); }

  SubscribeAll* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SubscribeAll& from);
  void MergeFrom(const SubscribeAll& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SubscribeAll* other);
  void UnsafeMergeFrom(const SubscribeAll& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
  int marketdatatypes_size() const;
  void clear_marketdatatypes();
  static const int kMarketDataTypesFieldNumber = 1;
  ::com::htsc::mdc::insight::model::EMarketDataType marketdatatypes(int index) const;
  void set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value);
  void add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value);
  const ::google::protobuf::RepeatedField<int>& marketdatatypes() const;
  ::google::protobuf::RepeatedField<int>* mutable_marketdatatypes();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SubscribeAll)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField<int> marketdatatypes_;
  mutable int _marketdatatypes_cached_byte_size_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SubscribeAll> SubscribeAll_default_instance_;

// -------------------------------------------------------------------

class SubscribeByID : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SubscribeByID) */ {
 public:
  SubscribeByID();
  virtual ~SubscribeByID();

  SubscribeByID(const SubscribeByID& from);

  inline SubscribeByID& operator=(const SubscribeByID& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SubscribeByID& default_instance();

  static const SubscribeByID* internal_default_instance();

  void Swap(SubscribeByID* other);

  // implements Message ----------------------------------------------

  inline SubscribeByID* New() const { return New(NULL); }

  SubscribeByID* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SubscribeByID& from);
  void MergeFrom(const SubscribeByID& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SubscribeByID* other);
  void UnsafeMergeFrom(const SubscribeByID& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
  int subscribebyiddetails_size() const;
  void clear_subscribebyiddetails();
  static const int kSubscribeByIDDetailsFieldNumber = 1;
  const ::com::htsc::mdc::insight::model::SubscribeByIDDetail& subscribebyiddetails(int index) const;
  ::com::htsc::mdc::insight::model::SubscribeByIDDetail* mutable_subscribebyiddetails(int index);
  ::com::htsc::mdc::insight::model::SubscribeByIDDetail* add_subscribebyiddetails();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >*
      mutable_subscribebyiddetails();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >&
      subscribebyiddetails() const;

  // repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
  int globalmarketdatatypes_size() const;
  void clear_globalmarketdatatypes();
  static const int kGlobalMarketDataTypesFieldNumber = 2;
  ::com::htsc::mdc::insight::model::EMarketDataType globalmarketdatatypes(int index) const;
  void set_globalmarketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value);
  void add_globalmarketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value);
  const ::google::protobuf::RepeatedField<int>& globalmarketdatatypes() const;
  ::google::protobuf::RepeatedField<int>* mutable_globalmarketdatatypes();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SubscribeByID)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail > subscribebyiddetails_;
  ::google::protobuf::RepeatedField<int> globalmarketdatatypes_;
  mutable int _globalmarketdatatypes_cached_byte_size_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SubscribeByID> SubscribeByID_default_instance_;

// -------------------------------------------------------------------

class SubscribeByIDDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SubscribeByIDDetail) */ {
 public:
  SubscribeByIDDetail();
  virtual ~SubscribeByIDDetail();

  SubscribeByIDDetail(const SubscribeByIDDetail& from);

  inline SubscribeByIDDetail& operator=(const SubscribeByIDDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SubscribeByIDDetail& default_instance();

  static const SubscribeByIDDetail* internal_default_instance();

  void Swap(SubscribeByIDDetail* other);

  // implements Message ----------------------------------------------

  inline SubscribeByIDDetail* New() const { return New(NULL); }

  SubscribeByIDDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SubscribeByIDDetail& from);
  void MergeFrom(const SubscribeByIDDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SubscribeByIDDetail* other);
  void UnsafeMergeFrom(const SubscribeByIDDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string htscSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHtscSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  int marketdatatypes_size() const;
  void clear_marketdatatypes();
  static const int kMarketDataTypesFieldNumber = 2;
  ::com::htsc::mdc::insight::model::EMarketDataType marketdatatypes(int index) const;
  void set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value);
  void add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value);
  const ::google::protobuf::RepeatedField<int>& marketdatatypes() const;
  ::google::protobuf::RepeatedField<int>* mutable_marketdatatypes();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SubscribeByIDDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField<int> marketdatatypes_;
  mutable int _marketdatatypes_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SubscribeByIDDetail> SubscribeByIDDetail_default_instance_;

// -------------------------------------------------------------------

class SubscribeBySourceType : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SubscribeBySourceType) */ {
 public:
  SubscribeBySourceType();
  virtual ~SubscribeBySourceType();

  SubscribeBySourceType(const SubscribeBySourceType& from);

  inline SubscribeBySourceType& operator=(const SubscribeBySourceType& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SubscribeBySourceType& default_instance();

  static const SubscribeBySourceType* internal_default_instance();

  void Swap(SubscribeBySourceType* other);

  // implements Message ----------------------------------------------

  inline SubscribeBySourceType* New() const { return New(NULL); }

  SubscribeBySourceType* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SubscribeBySourceType& from);
  void MergeFrom(const SubscribeBySourceType& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SubscribeBySourceType* other);
  void UnsafeMergeFrom(const SubscribeBySourceType& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
  int subscribebysourcetypedetail_size() const;
  void clear_subscribebysourcetypedetail();
  static const int kSubscribeBySourceTypeDetailFieldNumber = 1;
  const ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail& subscribebysourcetypedetail(int index) const;
  ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* mutable_subscribebysourcetypedetail(int index);
  ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* add_subscribebysourcetypedetail();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >*
      mutable_subscribebysourcetypedetail();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >&
      subscribebysourcetypedetail() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SubscribeBySourceType)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail > subscribebysourcetypedetail_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SubscribeBySourceType> SubscribeBySourceType_default_instance_;

// -------------------------------------------------------------------

class SubscribeBySourceTypeDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail) */ {
 public:
  SubscribeBySourceTypeDetail();
  virtual ~SubscribeBySourceTypeDetail();

  SubscribeBySourceTypeDetail(const SubscribeBySourceTypeDetail& from);

  inline SubscribeBySourceTypeDetail& operator=(const SubscribeBySourceTypeDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SubscribeBySourceTypeDetail& default_instance();

  static const SubscribeBySourceTypeDetail* internal_default_instance();

  void Swap(SubscribeBySourceTypeDetail* other);

  // implements Message ----------------------------------------------

  inline SubscribeBySourceTypeDetail* New() const { return New(NULL); }

  SubscribeBySourceTypeDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SubscribeBySourceTypeDetail& from);
  void MergeFrom(const SubscribeBySourceTypeDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SubscribeBySourceTypeDetail* other);
  void UnsafeMergeFrom(const SubscribeBySourceTypeDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
  bool has_securitysourcetypes() const;
  void clear_securitysourcetypes();
  static const int kSecuritySourceTypesFieldNumber = 1;
  const ::com::htsc::mdc::insight::model::SecuritySourceType& securitysourcetypes() const;
  ::com::htsc::mdc::insight::model::SecuritySourceType* mutable_securitysourcetypes();
  ::com::htsc::mdc::insight::model::SecuritySourceType* release_securitysourcetypes();
  void set_allocated_securitysourcetypes(::com::htsc::mdc::insight::model::SecuritySourceType* securitysourcetypes);

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  int marketdatatypes_size() const;
  void clear_marketdatatypes();
  static const int kMarketDataTypesFieldNumber = 2;
  ::com::htsc::mdc::insight::model::EMarketDataType marketdatatypes(int index) const;
  void set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value);
  void add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value);
  const ::google::protobuf::RepeatedField<int>& marketdatatypes() const;
  ::google::protobuf::RepeatedField<int>* mutable_marketdatatypes();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField<int> marketdatatypes_;
  mutable int _marketdatatypes_cached_byte_size_;
  ::com::htsc::mdc::insight::model::SecuritySourceType* securitysourcetypes_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SubscribeBySourceTypeDetail> SubscribeBySourceTypeDetail_default_instance_;

// -------------------------------------------------------------------

class MDSubscribeResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSubscribeResponse) */ {
 public:
  MDSubscribeResponse();
  virtual ~MDSubscribeResponse();

  MDSubscribeResponse(const MDSubscribeResponse& from);

  inline MDSubscribeResponse& operator=(const MDSubscribeResponse& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSubscribeResponse& default_instance();

  static const MDSubscribeResponse* internal_default_instance();

  void Swap(MDSubscribeResponse* other);

  // implements Message ----------------------------------------------

  inline MDSubscribeResponse* New() const { return New(NULL); }

  MDSubscribeResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSubscribeResponse& from);
  void MergeFrom(const MDSubscribeResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSubscribeResponse* other);
  void UnsafeMergeFrom(const MDSubscribeResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool isSuccess = 1;
  void clear_issuccess();
  static const int kIsSuccessFieldNumber = 1;
  bool issuccess() const;
  void set_issuccess(bool value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  bool has_errorcontext() const;
  void clear_errorcontext();
  static const int kErrorContextFieldNumber = 2;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& errorcontext() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_errorcontext();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_errorcontext();
  void set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSubscribeResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext_;
  bool issuccess_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSubscribe_2eproto_impl();
  friend void  protobuf_AddDesc_MDSubscribe_2eproto_impl();
  friend void protobuf_AssignDesc_MDSubscribe_2eproto();
  friend void protobuf_ShutdownFile_MDSubscribe_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSubscribeResponse> MDSubscribeResponse_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSubscribeRequest

// optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
inline void MDSubscribeRequest::clear_subscribeactiontype() {
  subscribeactiontype_ = 0;
}
inline ::com::htsc::mdc::insight::model::ESubscribeActionType MDSubscribeRequest::subscribeactiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeActionType)
  return static_cast< ::com::htsc::mdc::insight::model::ESubscribeActionType >(subscribeactiontype_);
}
inline void MDSubscribeRequest::set_subscribeactiontype(::com::htsc::mdc::insight::model::ESubscribeActionType value) {
  
  subscribeactiontype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeActionType)
}

// optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
inline bool MDSubscribeRequest::has_subscribeall() const {
  return this != internal_default_instance() && subscribeall_ != NULL;
}
inline void MDSubscribeRequest::clear_subscribeall() {
  if (GetArenaNoVirtual() == NULL && subscribeall_ != NULL) delete subscribeall_;
  subscribeall_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SubscribeAll& MDSubscribeRequest::subscribeall() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  return subscribeall_ != NULL ? *subscribeall_
                         : *::com::htsc::mdc::insight::model::SubscribeAll::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SubscribeAll* MDSubscribeRequest::mutable_subscribeall() {
  
  if (subscribeall_ == NULL) {
    subscribeall_ = new ::com::htsc::mdc::insight::model::SubscribeAll;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  return subscribeall_;
}
inline ::com::htsc::mdc::insight::model::SubscribeAll* MDSubscribeRequest::release_subscribeall() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  
  ::com::htsc::mdc::insight::model::SubscribeAll* temp = subscribeall_;
  subscribeall_ = NULL;
  return temp;
}
inline void MDSubscribeRequest::set_allocated_subscribeall(::com::htsc::mdc::insight::model::SubscribeAll* subscribeall) {
  delete subscribeall_;
  subscribeall_ = subscribeall;
  if (subscribeall) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
}

// optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
inline bool MDSubscribeRequest::has_subscribebysourcetype() const {
  return this != internal_default_instance() && subscribebysourcetype_ != NULL;
}
inline void MDSubscribeRequest::clear_subscribebysourcetype() {
  if (GetArenaNoVirtual() == NULL && subscribebysourcetype_ != NULL) delete subscribebysourcetype_;
  subscribebysourcetype_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SubscribeBySourceType& MDSubscribeRequest::subscribebysourcetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  return subscribebysourcetype_ != NULL ? *subscribebysourcetype_
                         : *::com::htsc::mdc::insight::model::SubscribeBySourceType::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SubscribeBySourceType* MDSubscribeRequest::mutable_subscribebysourcetype() {
  
  if (subscribebysourcetype_ == NULL) {
    subscribebysourcetype_ = new ::com::htsc::mdc::insight::model::SubscribeBySourceType;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  return subscribebysourcetype_;
}
inline ::com::htsc::mdc::insight::model::SubscribeBySourceType* MDSubscribeRequest::release_subscribebysourcetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  
  ::com::htsc::mdc::insight::model::SubscribeBySourceType* temp = subscribebysourcetype_;
  subscribebysourcetype_ = NULL;
  return temp;
}
inline void MDSubscribeRequest::set_allocated_subscribebysourcetype(::com::htsc::mdc::insight::model::SubscribeBySourceType* subscribebysourcetype) {
  delete subscribebysourcetype_;
  subscribebysourcetype_ = subscribebysourcetype;
  if (subscribebysourcetype) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
}

// optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
inline bool MDSubscribeRequest::has_subscribebyid() const {
  return this != internal_default_instance() && subscribebyid_ != NULL;
}
inline void MDSubscribeRequest::clear_subscribebyid() {
  if (GetArenaNoVirtual() == NULL && subscribebyid_ != NULL) delete subscribebyid_;
  subscribebyid_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SubscribeByID& MDSubscribeRequest::subscribebyid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  return subscribebyid_ != NULL ? *subscribebyid_
                         : *::com::htsc::mdc::insight::model::SubscribeByID::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SubscribeByID* MDSubscribeRequest::mutable_subscribebyid() {
  
  if (subscribebyid_ == NULL) {
    subscribebyid_ = new ::com::htsc::mdc::insight::model::SubscribeByID;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  return subscribebyid_;
}
inline ::com::htsc::mdc::insight::model::SubscribeByID* MDSubscribeRequest::release_subscribebyid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  
  ::com::htsc::mdc::insight::model::SubscribeByID* temp = subscribebyid_;
  subscribebyid_ = NULL;
  return temp;
}
inline void MDSubscribeRequest::set_allocated_subscribebyid(::com::htsc::mdc::insight::model::SubscribeByID* subscribebyid) {
  delete subscribebyid_;
  subscribebyid_ = subscribebyid;
  if (subscribebyid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
}

inline const MDSubscribeRequest* MDSubscribeRequest::internal_default_instance() {
  return &MDSubscribeRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// SubscribeAll

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
inline int SubscribeAll::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
inline void SubscribeAll::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
inline ::com::htsc::mdc::insight::model::EMarketDataType SubscribeAll::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
inline void SubscribeAll::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
}
inline void SubscribeAll::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
}
inline const ::google::protobuf::RepeatedField<int>&
SubscribeAll::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return marketdatatypes_;
}
inline ::google::protobuf::RepeatedField<int>*
SubscribeAll::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeAll* SubscribeAll::internal_default_instance() {
  return &SubscribeAll_default_instance_.get();
}
// -------------------------------------------------------------------

// SubscribeByID

// repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
inline int SubscribeByID::subscribebyiddetails_size() const {
  return subscribebyiddetails_.size();
}
inline void SubscribeByID::clear_subscribebyiddetails() {
  subscribebyiddetails_.Clear();
}
inline const ::com::htsc::mdc::insight::model::SubscribeByIDDetail& SubscribeByID::subscribebyiddetails(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Get(index);
}
inline ::com::htsc::mdc::insight::model::SubscribeByIDDetail* SubscribeByID::mutable_subscribebyiddetails(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::SubscribeByIDDetail* SubscribeByID::add_subscribebyiddetails() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >*
SubscribeByID::mutable_subscribebyiddetails() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return &subscribebyiddetails_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >&
SubscribeByID::subscribebyiddetails() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_;
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
inline int SubscribeByID::globalmarketdatatypes_size() const {
  return globalmarketdatatypes_.size();
}
inline void SubscribeByID::clear_globalmarketdatatypes() {
  globalmarketdatatypes_.Clear();
}
inline ::com::htsc::mdc::insight::model::EMarketDataType SubscribeByID::globalmarketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(globalmarketdatatypes_.Get(index));
}
inline void SubscribeByID::set_globalmarketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  globalmarketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
}
inline void SubscribeByID::add_globalmarketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  globalmarketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
}
inline const ::google::protobuf::RepeatedField<int>&
SubscribeByID::globalmarketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return globalmarketdatatypes_;
}
inline ::google::protobuf::RepeatedField<int>*
SubscribeByID::mutable_globalmarketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return &globalmarketdatatypes_;
}

inline const SubscribeByID* SubscribeByID::internal_default_instance() {
  return &SubscribeByID_default_instance_.get();
}
// -------------------------------------------------------------------

// SubscribeByIDDetail

// optional string htscSecurityID = 1;
inline void SubscribeByIDDetail::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SubscribeByIDDetail::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SubscribeByIDDetail::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
inline void SubscribeByIDDetail::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
inline void SubscribeByIDDetail::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
inline ::std::string* SubscribeByIDDetail::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SubscribeByIDDetail::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SubscribeByIDDetail::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
inline int SubscribeByIDDetail::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
inline void SubscribeByIDDetail::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
inline ::com::htsc::mdc::insight::model::EMarketDataType SubscribeByIDDetail::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
inline void SubscribeByIDDetail::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
}
inline void SubscribeByIDDetail::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
}
inline const ::google::protobuf::RepeatedField<int>&
SubscribeByIDDetail::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return marketdatatypes_;
}
inline ::google::protobuf::RepeatedField<int>*
SubscribeByIDDetail::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeByIDDetail* SubscribeByIDDetail::internal_default_instance() {
  return &SubscribeByIDDetail_default_instance_.get();
}
// -------------------------------------------------------------------

// SubscribeBySourceType

// repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
inline int SubscribeBySourceType::subscribebysourcetypedetail_size() const {
  return subscribebysourcetypedetail_.size();
}
inline void SubscribeBySourceType::clear_subscribebysourcetypedetail() {
  subscribebysourcetypedetail_.Clear();
}
inline const ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail& SubscribeBySourceType::subscribebysourcetypedetail(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Get(index);
}
inline ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* SubscribeBySourceType::mutable_subscribebysourcetypedetail(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* SubscribeBySourceType::add_subscribebysourcetypedetail() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >*
SubscribeBySourceType::mutable_subscribebysourcetypedetail() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return &subscribebysourcetypedetail_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >&
SubscribeBySourceType::subscribebysourcetypedetail() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_;
}

inline const SubscribeBySourceType* SubscribeBySourceType::internal_default_instance() {
  return &SubscribeBySourceType_default_instance_.get();
}
// -------------------------------------------------------------------

// SubscribeBySourceTypeDetail

// optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
inline bool SubscribeBySourceTypeDetail::has_securitysourcetypes() const {
  return this != internal_default_instance() && securitysourcetypes_ != NULL;
}
inline void SubscribeBySourceTypeDetail::clear_securitysourcetypes() {
  if (GetArenaNoVirtual() == NULL && securitysourcetypes_ != NULL) delete securitysourcetypes_;
  securitysourcetypes_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SecuritySourceType& SubscribeBySourceTypeDetail::securitysourcetypes() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  return securitysourcetypes_ != NULL ? *securitysourcetypes_
                         : *::com::htsc::mdc::insight::model::SecuritySourceType::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* SubscribeBySourceTypeDetail::mutable_securitysourcetypes() {
  
  if (securitysourcetypes_ == NULL) {
    securitysourcetypes_ = new ::com::htsc::mdc::insight::model::SecuritySourceType;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  return securitysourcetypes_;
}
inline ::com::htsc::mdc::insight::model::SecuritySourceType* SubscribeBySourceTypeDetail::release_securitysourcetypes() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  
  ::com::htsc::mdc::insight::model::SecuritySourceType* temp = securitysourcetypes_;
  securitysourcetypes_ = NULL;
  return temp;
}
inline void SubscribeBySourceTypeDetail::set_allocated_securitysourcetypes(::com::htsc::mdc::insight::model::SecuritySourceType* securitysourcetypes) {
  delete securitysourcetypes_;
  securitysourcetypes_ = securitysourcetypes;
  if (securitysourcetypes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
inline int SubscribeBySourceTypeDetail::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
inline void SubscribeBySourceTypeDetail::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
inline ::com::htsc::mdc::insight::model::EMarketDataType SubscribeBySourceTypeDetail::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
inline void SubscribeBySourceTypeDetail::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
}
inline void SubscribeBySourceTypeDetail::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
}
inline const ::google::protobuf::RepeatedField<int>&
SubscribeBySourceTypeDetail::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return marketdatatypes_;
}
inline ::google::protobuf::RepeatedField<int>*
SubscribeBySourceTypeDetail::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeBySourceTypeDetail* SubscribeBySourceTypeDetail::internal_default_instance() {
  return &SubscribeBySourceTypeDetail_default_instance_.get();
}
// -------------------------------------------------------------------

// MDSubscribeResponse

// optional bool isSuccess = 1;
inline void MDSubscribeResponse::clear_issuccess() {
  issuccess_ = false;
}
inline bool MDSubscribeResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeResponse.isSuccess)
  return issuccess_;
}
inline void MDSubscribeResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSubscribeResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
inline bool MDSubscribeResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
inline void MDSubscribeResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& MDSubscribeResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MDSubscribeResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  return errorcontext_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MDSubscribeResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
inline void MDSubscribeResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
}

inline const MDSubscribeResponse* MDSubscribeResponse::internal_default_instance() {
  return &MDSubscribeResponse_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

#ifndef SWIG
namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::com::htsc::mdc::insight::model::ESubscribeActionType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::com::htsc::mdc::insight::model::ESubscribeActionType>() {
  return ::com::htsc::mdc::insight::model::ESubscribeActionType_descriptor();
}

}  // namespace protobuf
}  // namespace google
#endif  // SWIG

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSubscribe_2eproto__INCLUDED
