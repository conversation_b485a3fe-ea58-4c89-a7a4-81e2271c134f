// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQuote.proto

#ifndef PROTOBUF_MDQuote_2eproto__INCLUDED
#define PROTOBUF_MDQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDQuote_2eproto();
void protobuf_InitDefaults_MDQuote_2eproto();
void protobuf_AssignDesc_MDQuote_2eproto();
void protobuf_ShutdownFile_MDQuote_2eproto();

class BrokerQueue;
class MDCashBondQuote;
class MDFxBestQuote;
class MDQuote;
class NeeqMarketMakerQuote;

// ===================================================================

class MDQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDQuote) */ {
 public:
  MDQuote();
  virtual ~MDQuote();

  MDQuote(const MDQuote& from);

  inline MDQuote& operator=(const MDQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDQuote& default_instance();

  static const MDQuote* internal_default_instance();

  void Swap(MDQuote* other);

  // implements Message ----------------------------------------------

  inline MDQuote* New() const { return New(NULL); }

  MDQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDQuote& from);
  void MergeFrom(const MDQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDQuote* other);
  void UnsafeMergeFrom(const MDQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 ExchangeDate = 8;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 8;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 9;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 9;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 MaxPx = 10;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 10;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 11;
  void clear_minpx();
  static const int kMinPxFieldNumber = 11;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int32 ChannelNo = 12;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 12;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int64 ApplSeqNum = 13;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 13;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // optional int32 MDBookType = 14;
  void clear_mdbooktype();
  static const int kMDBookTypeFieldNumber = 14;
  ::google::protobuf::int32 mdbooktype() const;
  void set_mdbooktype(::google::protobuf::int32 value);

  // optional string MarketIndicator = 15;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 15;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 MarketDepth = 16;
  void clear_marketdepth();
  static const int kMarketDepthFieldNumber = 16;
  ::google::protobuf::int32 marketdepth() const;
  void set_marketdepth(::google::protobuf::int32 value);

  // optional int32 MDSubBookType = 17;
  void clear_mdsubbooktype();
  static const int kMDSubBookTypeFieldNumber = 17;
  ::google::protobuf::int32 mdsubbooktype() const;
  void set_mdsubbooktype(::google::protobuf::int32 value);

  // repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
  int mdcashbondquotes_size() const;
  void clear_mdcashbondquotes();
  static const int kMDCashBondQuotesFieldNumber = 18;
  const ::com::htsc::mdc::insight::model::MDCashBondQuote& mdcashbondquotes(int index) const;
  ::com::htsc::mdc::insight::model::MDCashBondQuote* mutable_mdcashbondquotes(int index);
  ::com::htsc::mdc::insight::model::MDCashBondQuote* add_mdcashbondquotes();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >*
      mutable_mdcashbondquotes();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >&
      mdcashbondquotes() const;

  // optional int32 DataMultiplePowerOf10 = 19;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 19;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string VolatilitySurface = 20;
  void clear_volatilitysurface();
  static const int kVolatilitySurfaceFieldNumber = 20;
  const ::std::string& volatilitysurface() const;
  void set_volatilitysurface(const ::std::string& value);
  void set_volatilitysurface(const char* value);
  void set_volatilitysurface(const char* value, size_t size);
  ::std::string* mutable_volatilitysurface();
  ::std::string* release_volatilitysurface();
  void set_allocated_volatilitysurface(::std::string* volatilitysurface);

  // repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
  int mdfxbestquotes_size() const;
  void clear_mdfxbestquotes();
  static const int kMDFxBestQuotesFieldNumber = 21;
  const ::com::htsc::mdc::insight::model::MDFxBestQuote& mdfxbestquotes(int index) const;
  ::com::htsc::mdc::insight::model::MDFxBestQuote* mutable_mdfxbestquotes(int index);
  ::com::htsc::mdc::insight::model::MDFxBestQuote* add_mdfxbestquotes();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >*
      mutable_mdfxbestquotes();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >&
      mdfxbestquotes() const;

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
  int neeqinvestorbuy_size() const;
  void clear_neeqinvestorbuy();
  static const int kNeeqInvestorBuyFieldNumber = 22;
  const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& neeqinvestorbuy(int index) const;
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* mutable_neeqinvestorbuy(int index);
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* add_neeqinvestorbuy();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
      mutable_neeqinvestorbuy();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
      neeqinvestorbuy() const;

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
  int neeqinvestorsell_size() const;
  void clear_neeqinvestorsell();
  static const int kNeeqInvestorSellFieldNumber = 23;
  const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& neeqinvestorsell(int index) const;
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* mutable_neeqinvestorsell(int index);
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* add_neeqinvestorsell();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
      mutable_neeqinvestorsell();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
      neeqinvestorsell() const;

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
  int neeqmarketmakerbuy_size() const;
  void clear_neeqmarketmakerbuy();
  static const int kNeeqMarketMakerBuyFieldNumber = 24;
  const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& neeqmarketmakerbuy(int index) const;
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* mutable_neeqmarketmakerbuy(int index);
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* add_neeqmarketmakerbuy();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
      mutable_neeqmarketmakerbuy();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
      neeqmarketmakerbuy() const;

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
  int neeqmarketmakersell_size() const;
  void clear_neeqmarketmakersell();
  static const int kNeeqMarketMakerSellFieldNumber = 25;
  const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& neeqmarketmakersell(int index) const;
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* mutable_neeqmarketmakersell(int index);
  ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* add_neeqmarketmakersell();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
      mutable_neeqmarketmakersell();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
      neeqmarketmakersell() const;

  // repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
  int buybrokerqueue_size() const;
  void clear_buybrokerqueue();
  static const int kBuyBrokerQueueFieldNumber = 26;
  const ::com::htsc::mdc::insight::model::BrokerQueue& buybrokerqueue(int index) const;
  ::com::htsc::mdc::insight::model::BrokerQueue* mutable_buybrokerqueue(int index);
  ::com::htsc::mdc::insight::model::BrokerQueue* add_buybrokerqueue();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
      mutable_buybrokerqueue();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
      buybrokerqueue() const;

  // repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
  int sellbrokerqueue_size() const;
  void clear_sellbrokerqueue();
  static const int kSellBrokerQueueFieldNumber = 27;
  const ::com::htsc::mdc::insight::model::BrokerQueue& sellbrokerqueue(int index) const;
  ::com::htsc::mdc::insight::model::BrokerQueue* mutable_sellbrokerqueue(int index);
  ::com::htsc::mdc::insight::model::BrokerQueue* add_sellbrokerqueue();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
      mutable_sellbrokerqueue();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
      sellbrokerqueue() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote > mdcashbondquotes_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote > mdfxbestquotes_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote > neeqinvestorbuy_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote > neeqinvestorsell_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote > neeqmarketmakerbuy_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote > neeqmarketmakersell_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue > buybrokerqueue_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue > sellbrokerqueue_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::google::protobuf::internal::ArenaStringPtr volatilitysurface_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 mdbooktype_;
  ::google::protobuf::int32 marketdepth_;
  ::google::protobuf::int32 mdsubbooktype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDQuote> MDQuote_default_instance_;

// -------------------------------------------------------------------

class MDCashBondQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCashBondQuote) */ {
 public:
  MDCashBondQuote();
  virtual ~MDCashBondQuote();

  MDCashBondQuote(const MDCashBondQuote& from);

  inline MDCashBondQuote& operator=(const MDCashBondQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCashBondQuote& default_instance();

  static const MDCashBondQuote* internal_default_instance();

  void Swap(MDCashBondQuote* other);

  // implements Message ----------------------------------------------

  inline MDCashBondQuote* New() const { return New(NULL); }

  MDCashBondQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCashBondQuote& from);
  void MergeFrom(const MDCashBondQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCashBondQuote* other);
  void UnsafeMergeFrom(const MDCashBondQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 QuoteType = 1;
  void clear_quotetype();
  static const int kQuoteTypeFieldNumber = 1;
  ::google::protobuf::int32 quotetype() const;
  void set_quotetype(::google::protobuf::int32 value);

  // optional int32 Side = 2;
  void clear_side();
  static const int kSideFieldNumber = 2;
  ::google::protobuf::int32 side() const;
  void set_side(::google::protobuf::int32 value);

  // optional int32 PriceLevel = 3;
  void clear_pricelevel();
  static const int kPriceLevelFieldNumber = 3;
  ::google::protobuf::int32 pricelevel() const;
  void set_pricelevel(::google::protobuf::int32 value);

  // optional string QuoteID = 4;
  void clear_quoteid();
  static const int kQuoteIDFieldNumber = 4;
  const ::std::string& quoteid() const;
  void set_quoteid(const ::std::string& value);
  void set_quoteid(const char* value);
  void set_quoteid(const char* value, size_t size);
  ::std::string* mutable_quoteid();
  ::std::string* release_quoteid();
  void set_allocated_quoteid(::std::string* quoteid);

  // optional int32 QuoteDate = 5;
  void clear_quotedate();
  static const int kQuoteDateFieldNumber = 5;
  ::google::protobuf::int32 quotedate() const;
  void set_quotedate(::google::protobuf::int32 value);

  // optional int32 QuoteTime = 6;
  void clear_quotetime();
  static const int kQuoteTimeFieldNumber = 6;
  ::google::protobuf::int32 quotetime() const;
  void set_quotetime(::google::protobuf::int32 value);

  // optional int64 CleanPrice = 7;
  void clear_cleanprice();
  static const int kCleanPriceFieldNumber = 7;
  ::google::protobuf::int64 cleanprice() const;
  void set_cleanprice(::google::protobuf::int64 value);

  // optional int64 DirtyPrice = 8;
  void clear_dirtyprice();
  static const int kDirtyPriceFieldNumber = 8;
  ::google::protobuf::int64 dirtyprice() const;
  void set_dirtyprice(::google::protobuf::int64 value);

  // optional int64 TotalFaceValue = 9;
  void clear_totalfacevalue();
  static const int kTotalFaceValueFieldNumber = 9;
  ::google::protobuf::int64 totalfacevalue() const;
  void set_totalfacevalue(::google::protobuf::int64 value);

  // optional int32 ClearingMethod = 10;
  void clear_clearingmethod();
  static const int kClearingMethodFieldNumber = 10;
  ::google::protobuf::int32 clearingmethod() const;
  void set_clearingmethod(::google::protobuf::int32 value);

  // optional string SettlType = 11;
  void clear_settltype();
  static const int kSettlTypeFieldNumber = 11;
  const ::std::string& settltype() const;
  void set_settltype(const ::std::string& value);
  void set_settltype(const char* value);
  void set_settltype(const char* value, size_t size);
  ::std::string* mutable_settltype();
  ::std::string* release_settltype();
  void set_allocated_settltype(::std::string* settltype);

  // optional int32 SettlDate = 12;
  void clear_settldate();
  static const int kSettlDateFieldNumber = 12;
  ::google::protobuf::int32 settldate() const;
  void set_settldate(::google::protobuf::int32 value);

  // optional string SettlCurrency = 13;
  void clear_settlcurrency();
  static const int kSettlCurrencyFieldNumber = 13;
  const ::std::string& settlcurrency() const;
  void set_settlcurrency(const ::std::string& value);
  void set_settlcurrency(const char* value);
  void set_settlcurrency(const char* value, size_t size);
  ::std::string* mutable_settlcurrency();
  ::std::string* release_settlcurrency();
  void set_allocated_settlcurrency(::std::string* settlcurrency);

  // optional int64 SettlCurrFxRate = 14;
  void clear_settlcurrfxrate();
  static const int kSettlCurrFxRateFieldNumber = 14;
  ::google::protobuf::int64 settlcurrfxrate() const;
  void set_settlcurrfxrate(::google::protobuf::int64 value);

  // optional int32 PartyRole = 15;
  void clear_partyrole();
  static const int kPartyRoleFieldNumber = 15;
  ::google::protobuf::int32 partyrole() const;
  void set_partyrole(::google::protobuf::int32 value);

  // optional string TraderCode = 16;
  void clear_tradercode();
  static const int kTraderCodeFieldNumber = 16;
  const ::std::string& tradercode() const;
  void set_tradercode(const ::std::string& value);
  void set_tradercode(const char* value);
  void set_tradercode(const char* value, size_t size);
  ::std::string* mutable_tradercode();
  ::std::string* release_tradercode();
  void set_allocated_tradercode(::std::string* tradercode);

  // optional int64 MaturityYield = 17;
  void clear_maturityyield();
  static const int kMaturityYieldFieldNumber = 17;
  ::google::protobuf::int64 maturityyield() const;
  void set_maturityyield(::google::protobuf::int64 value);

  // optional int32 DeliveryType = 18;
  void clear_deliverytype();
  static const int kDeliveryTypeFieldNumber = 18;
  ::google::protobuf::int32 deliverytype() const;
  void set_deliverytype(::google::protobuf::int32 value);

  // optional string TraderAccountID = 19;
  void clear_traderaccountid();
  static const int kTraderAccountIDFieldNumber = 19;
  const ::std::string& traderaccountid() const;
  void set_traderaccountid(const ::std::string& value);
  void set_traderaccountid(const char* value);
  void set_traderaccountid(const char* value, size_t size);
  ::std::string* mutable_traderaccountid();
  ::std::string* release_traderaccountid();
  void set_allocated_traderaccountid(::std::string* traderaccountid);

  // optional int32 DataType = 20;
  void clear_datatype();
  static const int kDataTypeFieldNumber = 20;
  ::google::protobuf::int32 datatype() const;
  void set_datatype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCashBondQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr quoteid_;
  ::google::protobuf::internal::ArenaStringPtr settltype_;
  ::google::protobuf::internal::ArenaStringPtr settlcurrency_;
  ::google::protobuf::internal::ArenaStringPtr tradercode_;
  ::google::protobuf::internal::ArenaStringPtr traderaccountid_;
  ::google::protobuf::int32 quotetype_;
  ::google::protobuf::int32 side_;
  ::google::protobuf::int32 pricelevel_;
  ::google::protobuf::int32 quotedate_;
  ::google::protobuf::int64 cleanprice_;
  ::google::protobuf::int64 dirtyprice_;
  ::google::protobuf::int32 quotetime_;
  ::google::protobuf::int32 clearingmethod_;
  ::google::protobuf::int64 totalfacevalue_;
  ::google::protobuf::int32 settldate_;
  ::google::protobuf::int32 partyrole_;
  ::google::protobuf::int64 settlcurrfxrate_;
  ::google::protobuf::int64 maturityyield_;
  ::google::protobuf::int32 deliverytype_;
  ::google::protobuf::int32 datatype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCashBondQuote> MDCashBondQuote_default_instance_;

// -------------------------------------------------------------------

class MDFxBestQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDFxBestQuote) */ {
 public:
  MDFxBestQuote();
  virtual ~MDFxBestQuote();

  MDFxBestQuote(const MDFxBestQuote& from);

  inline MDFxBestQuote& operator=(const MDFxBestQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDFxBestQuote& default_instance();

  static const MDFxBestQuote* internal_default_instance();

  void Swap(MDFxBestQuote* other);

  // implements Message ----------------------------------------------

  inline MDFxBestQuote* New() const { return New(NULL); }

  MDFxBestQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDFxBestQuote& from);
  void MergeFrom(const MDFxBestQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDFxBestQuote* other);
  void UnsafeMergeFrom(const MDFxBestQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Side = 1;
  void clear_side();
  static const int kSideFieldNumber = 1;
  ::google::protobuf::int32 side() const;
  void set_side(::google::protobuf::int32 value);

  // optional int64 Price = 2;
  void clear_price();
  static const int kPriceFieldNumber = 2;
  ::google::protobuf::int64 price() const;
  void set_price(::google::protobuf::int64 value);

  // optional string Tenor = 3;
  void clear_tenor();
  static const int kTenorFieldNumber = 3;
  const ::std::string& tenor() const;
  void set_tenor(const ::std::string& value);
  void set_tenor(const char* value);
  void set_tenor(const char* value, size_t size);
  ::std::string* mutable_tenor();
  ::std::string* release_tenor();
  void set_allocated_tenor(::std::string* tenor);

  // optional string Date = 4;
  void clear_date();
  static const int kDateFieldNumber = 4;
  const ::std::string& date() const;
  void set_date(const ::std::string& value);
  void set_date(const char* value);
  void set_date(const char* value, size_t size);
  ::std::string* mutable_date();
  ::std::string* release_date();
  void set_allocated_date(::std::string* date);

  // optional string Time = 5;
  void clear_time();
  static const int kTimeFieldNumber = 5;
  const ::std::string& time() const;
  void set_time(const ::std::string& value);
  void set_time(const char* value);
  void set_time(const char* value, size_t size);
  ::std::string* mutable_time();
  ::std::string* release_time();
  void set_allocated_time(::std::string* time);

  // repeated string LiquidProviders = 6;
  int liquidproviders_size() const;
  void clear_liquidproviders();
  static const int kLiquidProvidersFieldNumber = 6;
  const ::std::string& liquidproviders(int index) const;
  ::std::string* mutable_liquidproviders(int index);
  void set_liquidproviders(int index, const ::std::string& value);
  void set_liquidproviders(int index, const char* value);
  void set_liquidproviders(int index, const char* value, size_t size);
  ::std::string* add_liquidproviders();
  void add_liquidproviders(const ::std::string& value);
  void add_liquidproviders(const char* value);
  void add_liquidproviders(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& liquidproviders() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_liquidproviders();

  // optional string LegSign = 7;
  void clear_legsign();
  static const int kLegSignFieldNumber = 7;
  const ::std::string& legsign() const;
  void set_legsign(const ::std::string& value);
  void set_legsign(const char* value);
  void set_legsign(const char* value, size_t size);
  ::std::string* mutable_legsign();
  ::std::string* release_legsign();
  void set_allocated_legsign(::std::string* legsign);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDFxBestQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> liquidproviders_;
  ::google::protobuf::internal::ArenaStringPtr tenor_;
  ::google::protobuf::internal::ArenaStringPtr date_;
  ::google::protobuf::internal::ArenaStringPtr time_;
  ::google::protobuf::internal::ArenaStringPtr legsign_;
  ::google::protobuf::int64 price_;
  ::google::protobuf::int32 side_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDFxBestQuote> MDFxBestQuote_default_instance_;

// -------------------------------------------------------------------

class NeeqMarketMakerQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.NeeqMarketMakerQuote) */ {
 public:
  NeeqMarketMakerQuote();
  virtual ~NeeqMarketMakerQuote();

  NeeqMarketMakerQuote(const NeeqMarketMakerQuote& from);

  inline NeeqMarketMakerQuote& operator=(const NeeqMarketMakerQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NeeqMarketMakerQuote& default_instance();

  static const NeeqMarketMakerQuote* internal_default_instance();

  void Swap(NeeqMarketMakerQuote* other);

  // implements Message ----------------------------------------------

  inline NeeqMarketMakerQuote* New() const { return New(NULL); }

  NeeqMarketMakerQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NeeqMarketMakerQuote& from);
  void MergeFrom(const NeeqMarketMakerQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NeeqMarketMakerQuote* other);
  void UnsafeMergeFrom(const NeeqMarketMakerQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 Sequence = 1;
  void clear_sequence();
  static const int kSequenceFieldNumber = 1;
  ::google::protobuf::int32 sequence() const;
  void set_sequence(::google::protobuf::int32 value);

  // optional string QuoteType = 2;
  void clear_quotetype();
  static const int kQuoteTypeFieldNumber = 2;
  const ::std::string& quotetype() const;
  void set_quotetype(const ::std::string& value);
  void set_quotetype(const char* value);
  void set_quotetype(const char* value, size_t size);
  ::std::string* mutable_quotetype();
  ::std::string* release_quotetype();
  void set_allocated_quotetype(::std::string* quotetype);

  // optional int64 Price = 3;
  void clear_price();
  static const int kPriceFieldNumber = 3;
  ::google::protobuf::int64 price() const;
  void set_price(::google::protobuf::int64 value);

  // optional int64 OrderQty = 4;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 4;
  ::google::protobuf::int64 orderqty() const;
  void set_orderqty(::google::protobuf::int64 value);

  // optional string GenerateTime = 5;
  void clear_generatetime();
  static const int kGenerateTimeFieldNumber = 5;
  const ::std::string& generatetime() const;
  void set_generatetime(const ::std::string& value);
  void set_generatetime(const char* value);
  void set_generatetime(const char* value, size_t size);
  ::std::string* mutable_generatetime();
  ::std::string* release_generatetime();
  void set_allocated_generatetime(::std::string* generatetime);

  // optional string BackupField = 6;
  void clear_backupfield();
  static const int kBackupFieldFieldNumber = 6;
  const ::std::string& backupfield() const;
  void set_backupfield(const ::std::string& value);
  void set_backupfield(const char* value);
  void set_backupfield(const char* value, size_t size);
  ::std::string* mutable_backupfield();
  ::std::string* release_backupfield();
  void set_allocated_backupfield(::std::string* backupfield);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr quotetype_;
  ::google::protobuf::internal::ArenaStringPtr generatetime_;
  ::google::protobuf::internal::ArenaStringPtr backupfield_;
  ::google::protobuf::int64 price_;
  ::google::protobuf::int64 orderqty_;
  ::google::protobuf::int32 sequence_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NeeqMarketMakerQuote> NeeqMarketMakerQuote_default_instance_;

// -------------------------------------------------------------------

class BrokerQueue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BrokerQueue) */ {
 public:
  BrokerQueue();
  virtual ~BrokerQueue();

  BrokerQueue(const BrokerQueue& from);

  inline BrokerQueue& operator=(const BrokerQueue& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BrokerQueue& default_instance();

  static const BrokerQueue* internal_default_instance();

  void Swap(BrokerQueue* other);

  // implements Message ----------------------------------------------

  inline BrokerQueue* New() const { return New(NULL); }

  BrokerQueue* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BrokerQueue& from);
  void MergeFrom(const BrokerQueue& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BrokerQueue* other);
  void UnsafeMergeFrom(const BrokerQueue& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 Price = 1;
  void clear_price();
  static const int kPriceFieldNumber = 1;
  ::google::protobuf::int64 price() const;
  void set_price(::google::protobuf::int64 value);

  // optional int64 OrderQty = 2;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 2;
  ::google::protobuf::int64 orderqty() const;
  void set_orderqty(::google::protobuf::int64 value);

  // optional int64 NumOrders = 3;
  void clear_numorders();
  static const int kNumOrdersFieldNumber = 3;
  ::google::protobuf::int64 numorders() const;
  void set_numorders(::google::protobuf::int64 value);

  // repeated int32 BrokerID = 4;
  int brokerid_size() const;
  void clear_brokerid();
  static const int kBrokerIDFieldNumber = 4;
  ::google::protobuf::int32 brokerid(int index) const;
  void set_brokerid(int index, ::google::protobuf::int32 value);
  void add_brokerid(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      brokerid() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_brokerid();

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BrokerQueue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > brokerid_;
  mutable int _brokerid_cached_byte_size_;
  ::google::protobuf::int64 price_;
  ::google::protobuf::int64 orderqty_;
  ::google::protobuf::int64 numorders_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDQuote_2eproto();
  friend void protobuf_ShutdownFile_MDQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BrokerQueue> BrokerQueue_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQuote

// optional string HTSCSecurityID = 1;
inline void MDQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
inline void MDQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
inline void MDQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
inline ::std::string* MDQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDDate)
  return mddate_;
}
inline void MDQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDTime)
  return mdtime_;
}
inline void MDQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
inline void MDQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
inline void MDQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
inline ::std::string* MDQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.securityType)
}

// optional int32 ExchangeDate = 8;
inline void MDQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ExchangeDate)
  return exchangedate_;
}
inline void MDQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
inline void MDQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ExchangeTime)
  return exchangetime_;
}
inline void MDQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ExchangeTime)
}

// optional int64 MaxPx = 10;
inline void MDQuote::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQuote::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MaxPx)
  return maxpx_;
}
inline void MDQuote::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MaxPx)
}

// optional int64 MinPx = 11;
inline void MDQuote::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQuote::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MinPx)
  return minpx_;
}
inline void MDQuote::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MinPx)
}

// optional int32 ChannelNo = 12;
inline void MDQuote::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDQuote::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ChannelNo)
  return channelno_;
}
inline void MDQuote::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ChannelNo)
}

// optional int64 ApplSeqNum = 13;
inline void MDQuote::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDQuote::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ApplSeqNum)
  return applseqnum_;
}
inline void MDQuote::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ApplSeqNum)
}

// optional int32 MDBookType = 14;
inline void MDQuote::clear_mdbooktype() {
  mdbooktype_ = 0;
}
inline ::google::protobuf::int32 MDQuote::mdbooktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDBookType)
  return mdbooktype_;
}
inline void MDQuote::set_mdbooktype(::google::protobuf::int32 value) {
  
  mdbooktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDBookType)
}

// optional string MarketIndicator = 15;
inline void MDQuote::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQuote::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
inline void MDQuote::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
inline void MDQuote::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
inline ::std::string* MDQuote::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQuote::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}

// optional int32 MarketDepth = 16;
inline void MDQuote::clear_marketdepth() {
  marketdepth_ = 0;
}
inline ::google::protobuf::int32 MDQuote::marketdepth() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MarketDepth)
  return marketdepth_;
}
inline void MDQuote::set_marketdepth(::google::protobuf::int32 value) {
  
  marketdepth_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MarketDepth)
}

// optional int32 MDSubBookType = 17;
inline void MDQuote::clear_mdsubbooktype() {
  mdsubbooktype_ = 0;
}
inline ::google::protobuf::int32 MDQuote::mdsubbooktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDSubBookType)
  return mdsubbooktype_;
}
inline void MDQuote::set_mdsubbooktype(::google::protobuf::int32 value) {
  
  mdsubbooktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDSubBookType)
}

// repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
inline int MDQuote::mdcashbondquotes_size() const {
  return mdcashbondquotes_.size();
}
inline void MDQuote::clear_mdcashbondquotes() {
  mdcashbondquotes_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDCashBondQuote& MDQuote::mdcashbondquotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDCashBondQuote* MDQuote::mutable_mdcashbondquotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDCashBondQuote* MDQuote::add_mdcashbondquotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >*
MDQuote::mutable_mdcashbondquotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return &mdcashbondquotes_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >&
MDQuote::mdcashbondquotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_;
}

// optional int32 DataMultiplePowerOf10 = 19;
inline void MDQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.DataMultiplePowerOf10)
}

// optional string VolatilitySurface = 20;
inline void MDQuote::clear_volatilitysurface() {
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDQuote::volatilitysurface() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  return volatilitysurface_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_volatilitysurface(const ::std::string& value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
inline void MDQuote::set_volatilitysurface(const char* value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
inline void MDQuote::set_volatilitysurface(const char* value, size_t size) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
inline ::std::string* MDQuote::mutable_volatilitysurface() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  return volatilitysurface_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDQuote::release_volatilitysurface() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  
  return volatilitysurface_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDQuote::set_allocated_volatilitysurface(::std::string* volatilitysurface) {
  if (volatilitysurface != NULL) {
    
  } else {
    
  }
  volatilitysurface_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitysurface);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}

// repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
inline int MDQuote::mdfxbestquotes_size() const {
  return mdfxbestquotes_.size();
}
inline void MDQuote::clear_mdfxbestquotes() {
  mdfxbestquotes_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MDFxBestQuote& MDQuote::mdfxbestquotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MDFxBestQuote* MDQuote::mutable_mdfxbestquotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MDFxBestQuote* MDQuote::add_mdfxbestquotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >*
MDQuote::mutable_mdfxbestquotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return &mdfxbestquotes_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >&
MDQuote::mdfxbestquotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
inline int MDQuote::neeqinvestorbuy_size() const {
  return neeqinvestorbuy_.size();
}
inline void MDQuote::clear_neeqinvestorbuy() {
  neeqinvestorbuy_.Clear();
}
inline const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqinvestorbuy(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Get(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqinvestorbuy(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqinvestorbuy() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqinvestorbuy() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return &neeqinvestorbuy_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqinvestorbuy() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
inline int MDQuote::neeqinvestorsell_size() const {
  return neeqinvestorsell_.size();
}
inline void MDQuote::clear_neeqinvestorsell() {
  neeqinvestorsell_.Clear();
}
inline const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqinvestorsell(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Get(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqinvestorsell(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqinvestorsell() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqinvestorsell() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return &neeqinvestorsell_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqinvestorsell() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
inline int MDQuote::neeqmarketmakerbuy_size() const {
  return neeqmarketmakerbuy_.size();
}
inline void MDQuote::clear_neeqmarketmakerbuy() {
  neeqmarketmakerbuy_.Clear();
}
inline const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqmarketmakerbuy(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Get(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqmarketmakerbuy(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqmarketmakerbuy() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqmarketmakerbuy() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return &neeqmarketmakerbuy_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqmarketmakerbuy() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
inline int MDQuote::neeqmarketmakersell_size() const {
  return neeqmarketmakersell_.size();
}
inline void MDQuote::clear_neeqmarketmakersell() {
  neeqmarketmakersell_.Clear();
}
inline const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqmarketmakersell(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Get(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqmarketmakersell(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqmarketmakersell() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqmarketmakersell() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return &neeqmarketmakersell_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqmarketmakersell() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_;
}

// repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
inline int MDQuote::buybrokerqueue_size() const {
  return buybrokerqueue_.size();
}
inline void MDQuote::clear_buybrokerqueue() {
  buybrokerqueue_.Clear();
}
inline const ::com::htsc::mdc::insight::model::BrokerQueue& MDQuote::buybrokerqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Get(index);
}
inline ::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::mutable_buybrokerqueue(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::add_buybrokerqueue() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
MDQuote::mutable_buybrokerqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return &buybrokerqueue_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
MDQuote::buybrokerqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_;
}

// repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
inline int MDQuote::sellbrokerqueue_size() const {
  return sellbrokerqueue_.size();
}
inline void MDQuote::clear_sellbrokerqueue() {
  sellbrokerqueue_.Clear();
}
inline const ::com::htsc::mdc::insight::model::BrokerQueue& MDQuote::sellbrokerqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Get(index);
}
inline ::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::mutable_sellbrokerqueue(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::add_sellbrokerqueue() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
MDQuote::mutable_sellbrokerqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return &sellbrokerqueue_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
MDQuote::sellbrokerqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_;
}

inline const MDQuote* MDQuote::internal_default_instance() {
  return &MDQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// MDCashBondQuote

// optional int32 QuoteType = 1;
inline void MDCashBondQuote::clear_quotetype() {
  quotetype_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteType)
  return quotetype_;
}
inline void MDCashBondQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteType)
}

// optional int32 Side = 2;
inline void MDCashBondQuote::clear_side() {
  side_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.Side)
  return side_;
}
inline void MDCashBondQuote::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.Side)
}

// optional int32 PriceLevel = 3;
inline void MDCashBondQuote::clear_pricelevel() {
  pricelevel_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::pricelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.PriceLevel)
  return pricelevel_;
}
inline void MDCashBondQuote::set_pricelevel(::google::protobuf::int32 value) {
  
  pricelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.PriceLevel)
}

// optional string QuoteID = 4;
inline void MDCashBondQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCashBondQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
inline void MDCashBondQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
inline void MDCashBondQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
inline ::std::string* MDCashBondQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCashBondQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}

// optional int32 QuoteDate = 5;
inline void MDCashBondQuote::clear_quotedate() {
  quotedate_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteDate)
  return quotedate_;
}
inline void MDCashBondQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteDate)
}

// optional int32 QuoteTime = 6;
inline void MDCashBondQuote::clear_quotetime() {
  quotetime_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteTime)
  return quotetime_;
}
inline void MDCashBondQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteTime)
}

// optional int64 CleanPrice = 7;
inline void MDCashBondQuote::clear_cleanprice() {
  cleanprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCashBondQuote::cleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.CleanPrice)
  return cleanprice_;
}
inline void MDCashBondQuote::set_cleanprice(::google::protobuf::int64 value) {
  
  cleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.CleanPrice)
}

// optional int64 DirtyPrice = 8;
inline void MDCashBondQuote::clear_dirtyprice() {
  dirtyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCashBondQuote::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DirtyPrice)
  return dirtyprice_;
}
inline void MDCashBondQuote::set_dirtyprice(::google::protobuf::int64 value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DirtyPrice)
}

// optional int64 TotalFaceValue = 9;
inline void MDCashBondQuote::clear_totalfacevalue() {
  totalfacevalue_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCashBondQuote::totalfacevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TotalFaceValue)
  return totalfacevalue_;
}
inline void MDCashBondQuote::set_totalfacevalue(::google::protobuf::int64 value) {
  
  totalfacevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TotalFaceValue)
}

// optional int32 ClearingMethod = 10;
inline void MDCashBondQuote::clear_clearingmethod() {
  clearingmethod_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::clearingmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.ClearingMethod)
  return clearingmethod_;
}
inline void MDCashBondQuote::set_clearingmethod(::google::protobuf::int32 value) {
  
  clearingmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.ClearingMethod)
}

// optional string SettlType = 11;
inline void MDCashBondQuote::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCashBondQuote::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
inline void MDCashBondQuote::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
inline void MDCashBondQuote::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
inline ::std::string* MDCashBondQuote::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCashBondQuote::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}

// optional int32 SettlDate = 12;
inline void MDCashBondQuote::clear_settldate() {
  settldate_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlDate)
  return settldate_;
}
inline void MDCashBondQuote::set_settldate(::google::protobuf::int32 value) {
  
  settldate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlDate)
}

// optional string SettlCurrency = 13;
inline void MDCashBondQuote::clear_settlcurrency() {
  settlcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCashBondQuote::settlcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  return settlcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_settlcurrency(const ::std::string& value) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
inline void MDCashBondQuote::set_settlcurrency(const char* value) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
inline void MDCashBondQuote::set_settlcurrency(const char* value, size_t size) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
inline ::std::string* MDCashBondQuote::mutable_settlcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  return settlcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCashBondQuote::release_settlcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  
  return settlcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_allocated_settlcurrency(::std::string* settlcurrency) {
  if (settlcurrency != NULL) {
    
  } else {
    
  }
  settlcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settlcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}

// optional int64 SettlCurrFxRate = 14;
inline void MDCashBondQuote::clear_settlcurrfxrate() {
  settlcurrfxrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCashBondQuote::settlcurrfxrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrFxRate)
  return settlcurrfxrate_;
}
inline void MDCashBondQuote::set_settlcurrfxrate(::google::protobuf::int64 value) {
  
  settlcurrfxrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrFxRate)
}

// optional int32 PartyRole = 15;
inline void MDCashBondQuote::clear_partyrole() {
  partyrole_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::partyrole() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.PartyRole)
  return partyrole_;
}
inline void MDCashBondQuote::set_partyrole(::google::protobuf::int32 value) {
  
  partyrole_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.PartyRole)
}

// optional string TraderCode = 16;
inline void MDCashBondQuote::clear_tradercode() {
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCashBondQuote::tradercode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  return tradercode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_tradercode(const ::std::string& value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
inline void MDCashBondQuote::set_tradercode(const char* value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
inline void MDCashBondQuote::set_tradercode(const char* value, size_t size) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
inline ::std::string* MDCashBondQuote::mutable_tradercode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  return tradercode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCashBondQuote::release_tradercode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  
  return tradercode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_allocated_tradercode(::std::string* tradercode) {
  if (tradercode != NULL) {
    
  } else {
    
  }
  tradercode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradercode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}

// optional int64 MaturityYield = 17;
inline void MDCashBondQuote::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCashBondQuote::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.MaturityYield)
  return maturityyield_;
}
inline void MDCashBondQuote::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.MaturityYield)
}

// optional int32 DeliveryType = 18;
inline void MDCashBondQuote::clear_deliverytype() {
  deliverytype_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::deliverytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DeliveryType)
  return deliverytype_;
}
inline void MDCashBondQuote::set_deliverytype(::google::protobuf::int32 value) {
  
  deliverytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DeliveryType)
}

// optional string TraderAccountID = 19;
inline void MDCashBondQuote::clear_traderaccountid() {
  traderaccountid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCashBondQuote::traderaccountid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  return traderaccountid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_traderaccountid(const ::std::string& value) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
inline void MDCashBondQuote::set_traderaccountid(const char* value) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
inline void MDCashBondQuote::set_traderaccountid(const char* value, size_t size) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
inline ::std::string* MDCashBondQuote::mutable_traderaccountid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  return traderaccountid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCashBondQuote::release_traderaccountid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  
  return traderaccountid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCashBondQuote::set_allocated_traderaccountid(::std::string* traderaccountid) {
  if (traderaccountid != NULL) {
    
  } else {
    
  }
  traderaccountid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), traderaccountid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}

// optional int32 DataType = 20;
inline void MDCashBondQuote::clear_datatype() {
  datatype_ = 0;
}
inline ::google::protobuf::int32 MDCashBondQuote::datatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DataType)
  return datatype_;
}
inline void MDCashBondQuote::set_datatype(::google::protobuf::int32 value) {
  
  datatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DataType)
}

inline const MDCashBondQuote* MDCashBondQuote::internal_default_instance() {
  return &MDCashBondQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// MDFxBestQuote

// optional int32 Side = 1;
inline void MDFxBestQuote::clear_side() {
  side_ = 0;
}
inline ::google::protobuf::int32 MDFxBestQuote::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Side)
  return side_;
}
inline void MDFxBestQuote::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Side)
}

// optional int64 Price = 2;
inline void MDFxBestQuote::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFxBestQuote::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Price)
  return price_;
}
inline void MDFxBestQuote::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Price)
}

// optional string Tenor = 3;
inline void MDFxBestQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFxBestQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
inline void MDFxBestQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
inline void MDFxBestQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
inline ::std::string* MDFxBestQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFxBestQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}

// optional string Date = 4;
inline void MDFxBestQuote::clear_date() {
  date_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFxBestQuote::date() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  return date_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_date(const ::std::string& value) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
inline void MDFxBestQuote::set_date(const char* value) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
inline void MDFxBestQuote::set_date(const char* value, size_t size) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
inline ::std::string* MDFxBestQuote::mutable_date() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  return date_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFxBestQuote::release_date() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  
  return date_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_allocated_date(::std::string* date) {
  if (date != NULL) {
    
  } else {
    
  }
  date_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), date);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}

// optional string Time = 5;
inline void MDFxBestQuote::clear_time() {
  time_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFxBestQuote::time() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  return time_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_time(const ::std::string& value) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
inline void MDFxBestQuote::set_time(const char* value) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
inline void MDFxBestQuote::set_time(const char* value, size_t size) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
inline ::std::string* MDFxBestQuote::mutable_time() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  return time_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFxBestQuote::release_time() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  
  return time_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_allocated_time(::std::string* time) {
  if (time != NULL) {
    
  } else {
    
  }
  time_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), time);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}

// repeated string LiquidProviders = 6;
inline int MDFxBestQuote::liquidproviders_size() const {
  return liquidproviders_.size();
}
inline void MDFxBestQuote::clear_liquidproviders() {
  liquidproviders_.Clear();
}
inline const ::std::string& MDFxBestQuote::liquidproviders(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Get(index);
}
inline ::std::string* MDFxBestQuote::mutable_liquidproviders(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Mutable(index);
}
inline void MDFxBestQuote::set_liquidproviders(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  liquidproviders_.Mutable(index)->assign(value);
}
inline void MDFxBestQuote::set_liquidproviders(int index, const char* value) {
  liquidproviders_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
inline void MDFxBestQuote::set_liquidproviders(int index, const char* value, size_t size) {
  liquidproviders_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
inline ::std::string* MDFxBestQuote::add_liquidproviders() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Add();
}
inline void MDFxBestQuote::add_liquidproviders(const ::std::string& value) {
  liquidproviders_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
inline void MDFxBestQuote::add_liquidproviders(const char* value) {
  liquidproviders_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
inline void MDFxBestQuote::add_liquidproviders(const char* value, size_t size) {
  liquidproviders_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
MDFxBestQuote::liquidproviders() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
MDFxBestQuote::mutable_liquidproviders() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return &liquidproviders_;
}

// optional string LegSign = 7;
inline void MDFxBestQuote::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFxBestQuote::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
inline void MDFxBestQuote::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
inline void MDFxBestQuote::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
inline ::std::string* MDFxBestQuote::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFxBestQuote::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFxBestQuote::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}

inline const MDFxBestQuote* MDFxBestQuote::internal_default_instance() {
  return &MDFxBestQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// NeeqMarketMakerQuote

// optional int32 Sequence = 1;
inline void NeeqMarketMakerQuote::clear_sequence() {
  sequence_ = 0;
}
inline ::google::protobuf::int32 NeeqMarketMakerQuote::sequence() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Sequence)
  return sequence_;
}
inline void NeeqMarketMakerQuote::set_sequence(::google::protobuf::int32 value) {
  
  sequence_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Sequence)
}

// optional string QuoteType = 2;
inline void NeeqMarketMakerQuote::clear_quotetype() {
  quotetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NeeqMarketMakerQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  return quotetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_quotetype(const ::std::string& value) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
inline void NeeqMarketMakerQuote::set_quotetype(const char* value) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
inline void NeeqMarketMakerQuote::set_quotetype(const char* value, size_t size) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
inline ::std::string* NeeqMarketMakerQuote::mutable_quotetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  return quotetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NeeqMarketMakerQuote::release_quotetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  
  return quotetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_allocated_quotetype(::std::string* quotetype) {
  if (quotetype != NULL) {
    
  } else {
    
  }
  quotetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}

// optional int64 Price = 3;
inline void NeeqMarketMakerQuote::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NeeqMarketMakerQuote::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Price)
  return price_;
}
inline void NeeqMarketMakerQuote::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Price)
}

// optional int64 OrderQty = 4;
inline void NeeqMarketMakerQuote::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NeeqMarketMakerQuote::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.OrderQty)
  return orderqty_;
}
inline void NeeqMarketMakerQuote::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.OrderQty)
}

// optional string GenerateTime = 5;
inline void NeeqMarketMakerQuote::clear_generatetime() {
  generatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NeeqMarketMakerQuote::generatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  return generatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_generatetime(const ::std::string& value) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
inline void NeeqMarketMakerQuote::set_generatetime(const char* value) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
inline void NeeqMarketMakerQuote::set_generatetime(const char* value, size_t size) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
inline ::std::string* NeeqMarketMakerQuote::mutable_generatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  return generatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NeeqMarketMakerQuote::release_generatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  
  return generatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_allocated_generatetime(::std::string* generatetime) {
  if (generatetime != NULL) {
    
  } else {
    
  }
  generatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), generatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}

// optional string BackupField = 6;
inline void NeeqMarketMakerQuote::clear_backupfield() {
  backupfield_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NeeqMarketMakerQuote::backupfield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  return backupfield_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_backupfield(const ::std::string& value) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
inline void NeeqMarketMakerQuote::set_backupfield(const char* value) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
inline void NeeqMarketMakerQuote::set_backupfield(const char* value, size_t size) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
inline ::std::string* NeeqMarketMakerQuote::mutable_backupfield() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  return backupfield_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NeeqMarketMakerQuote::release_backupfield() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  
  return backupfield_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NeeqMarketMakerQuote::set_allocated_backupfield(::std::string* backupfield) {
  if (backupfield != NULL) {
    
  } else {
    
  }
  backupfield_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), backupfield);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}

inline const NeeqMarketMakerQuote* NeeqMarketMakerQuote::internal_default_instance() {
  return &NeeqMarketMakerQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// BrokerQueue

// optional int64 Price = 1;
inline void BrokerQueue::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 BrokerQueue::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.Price)
  return price_;
}
inline void BrokerQueue::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.Price)
}

// optional int64 OrderQty = 2;
inline void BrokerQueue::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 BrokerQueue::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.OrderQty)
  return orderqty_;
}
inline void BrokerQueue::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.OrderQty)
}

// optional int64 NumOrders = 3;
inline void BrokerQueue::clear_numorders() {
  numorders_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 BrokerQueue::numorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.NumOrders)
  return numorders_;
}
inline void BrokerQueue::set_numorders(::google::protobuf::int64 value) {
  
  numorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.NumOrders)
}

// repeated int32 BrokerID = 4;
inline int BrokerQueue::brokerid_size() const {
  return brokerid_.size();
}
inline void BrokerQueue::clear_brokerid() {
  brokerid_.Clear();
}
inline ::google::protobuf::int32 BrokerQueue::brokerid(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return brokerid_.Get(index);
}
inline void BrokerQueue::set_brokerid(int index, ::google::protobuf::int32 value) {
  brokerid_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
}
inline void BrokerQueue::add_brokerid(::google::protobuf::int32 value) {
  brokerid_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
BrokerQueue::brokerid() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return brokerid_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
BrokerQueue::mutable_brokerid() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return &brokerid_;
}

inline const BrokerQueue* BrokerQueue::internal_default_instance() {
  return &BrokerQueue_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDQuote_2eproto__INCLUDED
