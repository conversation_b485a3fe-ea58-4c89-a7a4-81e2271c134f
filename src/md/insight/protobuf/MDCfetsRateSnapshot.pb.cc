// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsRateSnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsRateSnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsRateSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsRateSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* RateSwapSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  RateSwapSnapshot_reflection_ = NULL;
const ::google::protobuf::Descriptor* StandardisedRateSwapSnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  StandardisedRateSwapSnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto() {
  protobuf_AddDesc_MDCfetsRateSnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsRateSnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsRateSnapshot_descriptor_ = file->message_type(0);
  static const int MDCfetsRateSnapshot_offsets_[13] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, ratesnapshottype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, rateswapsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, standardisedrateswapsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, messagenumber_),
  };
  MDCfetsRateSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsRateSnapshot_descriptor_,
      MDCfetsRateSnapshot::internal_default_instance(),
      MDCfetsRateSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsRateSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsRateSnapshot, _internal_metadata_));
  RateSwapSnapshot_descriptor_ = file->message_type(1);
  static const int RateSwapSnapshot_offsets_[14] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, tradeterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, precloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, preweightedavgrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, weightedavgrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, lastvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, benchmarkcurvename_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, sessionreferencerate_),
  };
  RateSwapSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      RateSwapSnapshot_descriptor_,
      RateSwapSnapshot::internal_default_instance(),
      RateSwapSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(RateSwapSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(RateSwapSnapshot, _internal_metadata_));
  StandardisedRateSwapSnapshot_descriptor_ = file->message_type(2);
  static const int StandardisedRateSwapSnapshot_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, trademethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, lastrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, lastvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, settlerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, settleratedate_),
  };
  StandardisedRateSwapSnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      StandardisedRateSwapSnapshot_descriptor_,
      StandardisedRateSwapSnapshot::internal_default_instance(),
      StandardisedRateSwapSnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(StandardisedRateSwapSnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StandardisedRateSwapSnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsRateSnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsRateSnapshot_descriptor_, MDCfetsRateSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      RateSwapSnapshot_descriptor_, RateSwapSnapshot::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      StandardisedRateSwapSnapshot_descriptor_, StandardisedRateSwapSnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto() {
  MDCfetsRateSnapshot_default_instance_.Shutdown();
  delete MDCfetsRateSnapshot_reflection_;
  RateSwapSnapshot_default_instance_.Shutdown();
  delete RateSwapSnapshot_reflection_;
  StandardisedRateSwapSnapshot_default_instance_.Shutdown();
  delete StandardisedRateSwapSnapshot_reflection_;
}

void protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsRateSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  RateSwapSnapshot_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  StandardisedRateSwapSnapshot_default_instance_.DefaultConstruct();
  MDCfetsRateSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  RateSwapSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
  StandardisedRateSwapSnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_once_);
void protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\031MDCfetsRateSnapshot.proto\022\032com.htsc.md"
    "c.insight.model\032\027ESecurityIDSource.proto"
    "\032\023ESecurityType.proto\"\205\004\n\023MDCfetsRateSna"
    "pshot\022\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n\014Securit"
    "yType\030\002 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\?\n\020SecurityIDSource\030\003 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\022\016\n\006MDDat"
    "e\030\004 \001(\005\022\016\n\006MDTime\030\005 \001(\005\022\025\n\rDataTimestamp"
    "\030\006 \001(\003\022\024\n\014TransactTime\030\007 \001(\t\022\027\n\017MarketIn"
    "dicator\030\010 \001(\t\022\035\n\025DataMultiplePowerOf10\030\t"
    " \001(\005\022\030\n\020RateSnapshotType\030\020 \001(\005\022F\n\020RateSw"
    "apSnapshot\030\021 \001(\0132,.com.htsc.mdc.insight."
    "model.RateSwapSnapshot\022^\n\034StandardisedRa"
    "teSwapSnapshot\030\022 \001(\01328.com.htsc.mdc.insi"
    "ght.model.StandardisedRateSwapSnapshot\022\025"
    "\n\rMessageNumber\030d \001(\003\"\314\002\n\020RateSwapSnapsh"
    "ot\022\023\n\013TradeMethod\030\001 \001(\005\022\021\n\tTradeTerm\030\002 \001"
    "(\t\022\024\n\014PreCloseRate\030\013 \001(\001\022\032\n\022PreWeightedA"
    "vgRate\030\014 \001(\001\022\020\n\010OpenRate\030\r \001(\001\022\020\n\010LastRa"
    "te\030\016 \001(\001\022\020\n\010HighRate\030\017 \001(\001\022\017\n\007LowRate\030\020 "
    "\001(\001\022\021\n\tCloseRate\030\021 \001(\001\022\027\n\017WeightedAvgRat"
    "e\030\022 \001(\001\022\027\n\017LastVolumeTrade\030\023 \001(\001\022\030\n\020Tota"
    "lVolumeTrade\030\024 \001(\001\022\032\n\022BenchmarkCurveName"
    "\030\025 \001(\t\022\034\n\024SessionReferenceRate\030\026 \001(\001\"\331\001\n"
    "\034StandardisedRateSwapSnapshot\022\023\n\013TradeMe"
    "thod\030\001 \001(\005\022\020\n\010OpenRate\030\013 \001(\001\022\020\n\010HighRate"
    "\030\014 \001(\001\022\017\n\007LowRate\030\r \001(\001\022\020\n\010LastRate\030\016 \001("
    "\001\022\027\n\017LastVolumeTrade\030\017 \001(\001\022\030\n\020TotalVolum"
    "eTrade\030\020 \001(\001\022\022\n\nSettleRate\030\021 \001(\001\022\026\n\016Sett"
    "leRateDate\030\022 \001(\tB<\n\032com.htsc.mdc.insight"
    ".modelB\031MDCfetsRateSnapshotProtosH\001\240\001\001b\006"
    "proto3", 1246);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsRateSnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsRateSnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_once_);
void protobuf_AddDesc_MDCfetsRateSnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsRateSnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsRateSnapshot_2eproto {
  StaticDescriptorInitializer_MDCfetsRateSnapshot_2eproto() {
    protobuf_AddDesc_MDCfetsRateSnapshot_2eproto();
  }
} static_descriptor_initializer_MDCfetsRateSnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsRateSnapshot::kHTSCSecurityIDFieldNumber;
const int MDCfetsRateSnapshot::kSecurityTypeFieldNumber;
const int MDCfetsRateSnapshot::kSecurityIDSourceFieldNumber;
const int MDCfetsRateSnapshot::kMDDateFieldNumber;
const int MDCfetsRateSnapshot::kMDTimeFieldNumber;
const int MDCfetsRateSnapshot::kDataTimestampFieldNumber;
const int MDCfetsRateSnapshot::kTransactTimeFieldNumber;
const int MDCfetsRateSnapshot::kMarketIndicatorFieldNumber;
const int MDCfetsRateSnapshot::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsRateSnapshot::kRateSnapshotTypeFieldNumber;
const int MDCfetsRateSnapshot::kRateSwapSnapshotFieldNumber;
const int MDCfetsRateSnapshot::kStandardisedRateSwapSnapshotFieldNumber;
const int MDCfetsRateSnapshot::kMessageNumberFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsRateSnapshot::MDCfetsRateSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
}

void MDCfetsRateSnapshot::InitAsDefaultInstance() {
  rateswapsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::RateSwapSnapshot*>(
      ::com::htsc::mdc::insight::model::RateSwapSnapshot::internal_default_instance());
  standardisedrateswapsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot*>(
      ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot::internal_default_instance());
}

MDCfetsRateSnapshot::MDCfetsRateSnapshot(const MDCfetsRateSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
}

void MDCfetsRateSnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateswapsnapshot_ = NULL;
  standardisedrateswapsnapshot_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&messagenumber_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(messagenumber_));
  _cached_size_ = 0;
}

MDCfetsRateSnapshot::~MDCfetsRateSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  SharedDtor();
}

void MDCfetsRateSnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsRateSnapshot_default_instance_.get()) {
    delete rateswapsnapshot_;
    delete standardisedrateswapsnapshot_;
  }
}

void MDCfetsRateSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsRateSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsRateSnapshot_descriptor_;
}

const MDCfetsRateSnapshot& MDCfetsRateSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsRateSnapshot> MDCfetsRateSnapshot_default_instance_;

MDCfetsRateSnapshot* MDCfetsRateSnapshot::New(::google::protobuf::Arena* arena) const {
  MDCfetsRateSnapshot* n = new MDCfetsRateSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsRateSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsRateSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsRateSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, datatimestamp_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(datamultiplepowerof10_, messagenumber_);
  if (GetArenaNoVirtual() == NULL && rateswapsnapshot_ != NULL) delete rateswapsnapshot_;
  rateswapsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && standardisedrateswapsnapshot_ != NULL) delete standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsRateSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 6;
      case 6: {
        if (tag == 48) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 7;
      case 7: {
        if (tag == 58) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 8;
      case 8: {
        if (tag == 66) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 9;
      case 9: {
        if (tag == 72) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_RateSnapshotType;
        break;
      }

      // optional int32 RateSnapshotType = 16;
      case 16: {
        if (tag == 128) {
         parse_RateSnapshotType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ratesnapshottype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_RateSwapSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
      case 17: {
        if (tag == 138) {
         parse_RateSwapSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_rateswapsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_StandardisedRateSwapSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
      case 18: {
        if (tag == 146) {
         parse_StandardisedRateSwapSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_standardisedrateswapsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(800)) goto parse_MessageNumber;
        break;
      }

      // optional int64 MessageNumber = 100;
      case 100: {
        if (tag == 800) {
         parse_MessageNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  return false;
#undef DO_
}

void MDCfetsRateSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->datatimestamp(), output);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->transacttime(), output);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->marketindicator(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->datamultiplepowerof10(), output);
  }

  // optional int32 RateSnapshotType = 16;
  if (this->ratesnapshottype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->ratesnapshottype(), output);
  }

  // optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
  if (this->has_rateswapsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->rateswapsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
  if (this->has_standardisedrateswapsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->standardisedrateswapsnapshot_, output);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(100, this->messagenumber(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
}

::google::protobuf::uint8* MDCfetsRateSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->datatimestamp(), target);
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->transacttime(), target);
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->marketindicator(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->datamultiplepowerof10(), target);
  }

  // optional int32 RateSnapshotType = 16;
  if (this->ratesnapshottype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->ratesnapshottype(), target);
  }

  // optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
  if (this->has_rateswapsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->rateswapsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
  if (this->has_standardisedrateswapsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->standardisedrateswapsnapshot_, false, target);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(100, this->messagenumber(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  return target;
}

size_t MDCfetsRateSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 6;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TransactTime = 7;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 8;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 DataMultiplePowerOf10 = 9;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 RateSnapshotType = 16;
  if (this->ratesnapshottype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ratesnapshottype());
  }

  // optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
  if (this->has_rateswapsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->rateswapsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
  if (this->has_standardisedrateswapsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->standardisedrateswapsnapshot_);
  }

  // optional int64 MessageNumber = 100;
  if (this->messagenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagenumber());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsRateSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsRateSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsRateSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsRateSnapshot::MergeFrom(const MDCfetsRateSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsRateSnapshot::UnsafeMergeFrom(const MDCfetsRateSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.ratesnapshottype() != 0) {
    set_ratesnapshottype(from.ratesnapshottype());
  }
  if (from.has_rateswapsnapshot()) {
    mutable_rateswapsnapshot()->::com::htsc::mdc::insight::model::RateSwapSnapshot::MergeFrom(from.rateswapsnapshot());
  }
  if (from.has_standardisedrateswapsnapshot()) {
    mutable_standardisedrateswapsnapshot()->::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot::MergeFrom(from.standardisedrateswapsnapshot());
  }
  if (from.messagenumber() != 0) {
    set_messagenumber(from.messagenumber());
  }
}

void MDCfetsRateSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsRateSnapshot::CopyFrom(const MDCfetsRateSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsRateSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsRateSnapshot::IsInitialized() const {

  return true;
}

void MDCfetsRateSnapshot::Swap(MDCfetsRateSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsRateSnapshot::InternalSwap(MDCfetsRateSnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(ratesnapshottype_, other->ratesnapshottype_);
  std::swap(rateswapsnapshot_, other->rateswapsnapshot_);
  std::swap(standardisedrateswapsnapshot_, other->standardisedrateswapsnapshot_);
  std::swap(messagenumber_, other->messagenumber_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsRateSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsRateSnapshot_descriptor_;
  metadata.reflection = MDCfetsRateSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsRateSnapshot

// optional string HTSCSecurityID = 1;
void MDCfetsRateSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsRateSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
void MDCfetsRateSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
void MDCfetsRateSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}
::std::string* MDCfetsRateSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsRateSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDCfetsRateSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsRateSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsRateSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDCfetsRateSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsRateSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsRateSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDCfetsRateSnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsRateSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDDate)
  return mddate_;
}
void MDCfetsRateSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDDate)
}

// optional int32 MDTime = 5;
void MDCfetsRateSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsRateSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDTime)
  return mdtime_;
}
void MDCfetsRateSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
void MDCfetsRateSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsRateSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsRateSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
void MDCfetsRateSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsRateSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
void MDCfetsRateSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
void MDCfetsRateSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}
::std::string* MDCfetsRateSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsRateSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
void MDCfetsRateSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsRateSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
void MDCfetsRateSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
void MDCfetsRateSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}
::std::string* MDCfetsRateSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsRateSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsRateSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
void MDCfetsRateSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsRateSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsRateSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.DataMultiplePowerOf10)
}

// optional int32 RateSnapshotType = 16;
void MDCfetsRateSnapshot::clear_ratesnapshottype() {
  ratesnapshottype_ = 0;
}
::google::protobuf::int32 MDCfetsRateSnapshot::ratesnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSnapshotType)
  return ratesnapshottype_;
}
void MDCfetsRateSnapshot::set_ratesnapshottype(::google::protobuf::int32 value) {
  
  ratesnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSnapshotType)
}

// optional .com.htsc.mdc.insight.model.RateSwapSnapshot RateSwapSnapshot = 17;
bool MDCfetsRateSnapshot::has_rateswapsnapshot() const {
  return this != internal_default_instance() && rateswapsnapshot_ != NULL;
}
void MDCfetsRateSnapshot::clear_rateswapsnapshot() {
  if (GetArenaNoVirtual() == NULL && rateswapsnapshot_ != NULL) delete rateswapsnapshot_;
  rateswapsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::RateSwapSnapshot& MDCfetsRateSnapshot::rateswapsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  return rateswapsnapshot_ != NULL ? *rateswapsnapshot_
                         : *::com::htsc::mdc::insight::model::RateSwapSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::RateSwapSnapshot* MDCfetsRateSnapshot::mutable_rateswapsnapshot() {
  
  if (rateswapsnapshot_ == NULL) {
    rateswapsnapshot_ = new ::com::htsc::mdc::insight::model::RateSwapSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  return rateswapsnapshot_;
}
::com::htsc::mdc::insight::model::RateSwapSnapshot* MDCfetsRateSnapshot::release_rateswapsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
  
  ::com::htsc::mdc::insight::model::RateSwapSnapshot* temp = rateswapsnapshot_;
  rateswapsnapshot_ = NULL;
  return temp;
}
void MDCfetsRateSnapshot::set_allocated_rateswapsnapshot(::com::htsc::mdc::insight::model::RateSwapSnapshot* rateswapsnapshot) {
  delete rateswapsnapshot_;
  rateswapsnapshot_ = rateswapsnapshot;
  if (rateswapsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.RateSwapSnapshot)
}

// optional .com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot StandardisedRateSwapSnapshot = 18;
bool MDCfetsRateSnapshot::has_standardisedrateswapsnapshot() const {
  return this != internal_default_instance() && standardisedrateswapsnapshot_ != NULL;
}
void MDCfetsRateSnapshot::clear_standardisedrateswapsnapshot() {
  if (GetArenaNoVirtual() == NULL && standardisedrateswapsnapshot_ != NULL) delete standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot& MDCfetsRateSnapshot::standardisedrateswapsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  return standardisedrateswapsnapshot_ != NULL ? *standardisedrateswapsnapshot_
                         : *::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* MDCfetsRateSnapshot::mutable_standardisedrateswapsnapshot() {
  
  if (standardisedrateswapsnapshot_ == NULL) {
    standardisedrateswapsnapshot_ = new ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  return standardisedrateswapsnapshot_;
}
::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* MDCfetsRateSnapshot::release_standardisedrateswapsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
  
  ::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* temp = standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = NULL;
  return temp;
}
void MDCfetsRateSnapshot::set_allocated_standardisedrateswapsnapshot(::com::htsc::mdc::insight::model::StandardisedRateSwapSnapshot* standardisedrateswapsnapshot) {
  delete standardisedrateswapsnapshot_;
  standardisedrateswapsnapshot_ = standardisedrateswapsnapshot;
  if (standardisedrateswapsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.StandardisedRateSwapSnapshot)
}

// optional int64 MessageNumber = 100;
void MDCfetsRateSnapshot::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsRateSnapshot::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MessageNumber)
  return messagenumber_;
}
void MDCfetsRateSnapshot::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsRateSnapshot.MessageNumber)
}

inline const MDCfetsRateSnapshot* MDCfetsRateSnapshot::internal_default_instance() {
  return &MDCfetsRateSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RateSwapSnapshot::kTradeMethodFieldNumber;
const int RateSwapSnapshot::kTradeTermFieldNumber;
const int RateSwapSnapshot::kPreCloseRateFieldNumber;
const int RateSwapSnapshot::kPreWeightedAvgRateFieldNumber;
const int RateSwapSnapshot::kOpenRateFieldNumber;
const int RateSwapSnapshot::kLastRateFieldNumber;
const int RateSwapSnapshot::kHighRateFieldNumber;
const int RateSwapSnapshot::kLowRateFieldNumber;
const int RateSwapSnapshot::kCloseRateFieldNumber;
const int RateSwapSnapshot::kWeightedAvgRateFieldNumber;
const int RateSwapSnapshot::kLastVolumeTradeFieldNumber;
const int RateSwapSnapshot::kTotalVolumeTradeFieldNumber;
const int RateSwapSnapshot::kBenchmarkCurveNameFieldNumber;
const int RateSwapSnapshot::kSessionReferenceRateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RateSwapSnapshot::RateSwapSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.RateSwapSnapshot)
}

void RateSwapSnapshot::InitAsDefaultInstance() {
}

RateSwapSnapshot::RateSwapSnapshot(const RateSwapSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.RateSwapSnapshot)
}

void RateSwapSnapshot::SharedCtor() {
  tradeterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  benchmarkcurvename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&precloserate_, 0, reinterpret_cast<char*>(&trademethod_) -
    reinterpret_cast<char*>(&precloserate_) + sizeof(trademethod_));
  _cached_size_ = 0;
}

RateSwapSnapshot::~RateSwapSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.RateSwapSnapshot)
  SharedDtor();
}

void RateSwapSnapshot::SharedDtor() {
  tradeterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  benchmarkcurvename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void RateSwapSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RateSwapSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return RateSwapSnapshot_descriptor_;
}

const RateSwapSnapshot& RateSwapSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<RateSwapSnapshot> RateSwapSnapshot_default_instance_;

RateSwapSnapshot* RateSwapSnapshot::New(::google::protobuf::Arena* arena) const {
  RateSwapSnapshot* n = new RateSwapSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RateSwapSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(RateSwapSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<RateSwapSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(precloserate_, lowrate_);
  trademethod_ = 0;
  tradeterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(closerate_, sessionreferencerate_);
  benchmarkcurvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool RateSwapSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeMethod = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TradeTerm;
        break;
      }

      // optional string TradeTerm = 2;
      case 2: {
        if (tag == 18) {
         parse_TradeTerm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradeterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradeterm().data(), this->tradeterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_PreCloseRate;
        break;
      }

      // optional double PreCloseRate = 11;
      case 11: {
        if (tag == 89) {
         parse_PreCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &precloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_PreWeightedAvgRate;
        break;
      }

      // optional double PreWeightedAvgRate = 12;
      case 12: {
        if (tag == 97) {
         parse_PreWeightedAvgRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preweightedavgrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_OpenRate;
        break;
      }

      // optional double OpenRate = 13;
      case 13: {
        if (tag == 105) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_LastRate;
        break;
      }

      // optional double LastRate = 14;
      case 14: {
        if (tag == 113) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_HighRate;
        break;
      }

      // optional double HighRate = 15;
      case 15: {
        if (tag == 121) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_LowRate;
        break;
      }

      // optional double LowRate = 16;
      case 16: {
        if (tag == 129) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_CloseRate;
        break;
      }

      // optional double CloseRate = 17;
      case 17: {
        if (tag == 137) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_WeightedAvgRate;
        break;
      }

      // optional double WeightedAvgRate = 18;
      case 18: {
        if (tag == 145) {
         parse_WeightedAvgRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &weightedavgrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_LastVolumeTrade;
        break;
      }

      // optional double LastVolumeTrade = 19;
      case 19: {
        if (tag == 153) {
         parse_LastVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(161)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional double TotalVolumeTrade = 20;
      case 20: {
        if (tag == 161) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_BenchmarkCurveName;
        break;
      }

      // optional string BenchmarkCurveName = 21;
      case 21: {
        if (tag == 170) {
         parse_BenchmarkCurveName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_benchmarkcurvename()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->benchmarkcurvename().data(), this->benchmarkcurvename().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_SessionReferenceRate;
        break;
      }

      // optional double SessionReferenceRate = 22;
      case 22: {
        if (tag == 177) {
         parse_SessionReferenceRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sessionreferencerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.RateSwapSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.RateSwapSnapshot)
  return false;
#undef DO_
}

void RateSwapSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->trademethod(), output);
  }

  // optional string TradeTerm = 2;
  if (this->tradeterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradeterm().data(), this->tradeterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->tradeterm(), output);
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->precloserate(), output);
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->preweightedavgrate(), output);
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->openrate(), output);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->lastrate(), output);
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->highrate(), output);
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->lowrate(), output);
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->closerate(), output);
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->weightedavgrate(), output);
  }

  // optional double LastVolumeTrade = 19;
  if (this->lastvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->lastvolumetrade(), output);
  }

  // optional double TotalVolumeTrade = 20;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(20, this->totalvolumetrade(), output);
  }

  // optional string BenchmarkCurveName = 21;
  if (this->benchmarkcurvename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->benchmarkcurvename().data(), this->benchmarkcurvename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->benchmarkcurvename(), output);
  }

  // optional double SessionReferenceRate = 22;
  if (this->sessionreferencerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->sessionreferencerate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.RateSwapSnapshot)
}

::google::protobuf::uint8* RateSwapSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->trademethod(), target);
  }

  // optional string TradeTerm = 2;
  if (this->tradeterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradeterm().data(), this->tradeterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->tradeterm(), target);
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->precloserate(), target);
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->preweightedavgrate(), target);
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->openrate(), target);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->lastrate(), target);
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->highrate(), target);
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->lowrate(), target);
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->closerate(), target);
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->weightedavgrate(), target);
  }

  // optional double LastVolumeTrade = 19;
  if (this->lastvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->lastvolumetrade(), target);
  }

  // optional double TotalVolumeTrade = 20;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(20, this->totalvolumetrade(), target);
  }

  // optional string BenchmarkCurveName = 21;
  if (this->benchmarkcurvename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->benchmarkcurvename().data(), this->benchmarkcurvename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->benchmarkcurvename(), target);
  }

  // optional double SessionReferenceRate = 22;
  if (this->sessionreferencerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->sessionreferencerate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.RateSwapSnapshot)
  return target;
}

size_t RateSwapSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  size_t total_size = 0;

  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional string TradeTerm = 2;
  if (this->tradeterm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradeterm());
  }

  // optional double PreCloseRate = 11;
  if (this->precloserate() != 0) {
    total_size += 1 + 8;
  }

  // optional double PreWeightedAvgRate = 12;
  if (this->preweightedavgrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double OpenRate = 13;
  if (this->openrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighRate = 15;
  if (this->highrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LowRate = 16;
  if (this->lowrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double CloseRate = 17;
  if (this->closerate() != 0) {
    total_size += 2 + 8;
  }

  // optional double WeightedAvgRate = 18;
  if (this->weightedavgrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double LastVolumeTrade = 19;
  if (this->lastvolumetrade() != 0) {
    total_size += 2 + 8;
  }

  // optional double TotalVolumeTrade = 20;
  if (this->totalvolumetrade() != 0) {
    total_size += 2 + 8;
  }

  // optional string BenchmarkCurveName = 21;
  if (this->benchmarkcurvename().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->benchmarkcurvename());
  }

  // optional double SessionReferenceRate = 22;
  if (this->sessionreferencerate() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RateSwapSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const RateSwapSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RateSwapSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.RateSwapSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.RateSwapSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void RateSwapSnapshot::MergeFrom(const RateSwapSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void RateSwapSnapshot::UnsafeMergeFrom(const RateSwapSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.tradeterm().size() > 0) {

    tradeterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradeterm_);
  }
  if (from.precloserate() != 0) {
    set_precloserate(from.precloserate());
  }
  if (from.preweightedavgrate() != 0) {
    set_preweightedavgrate(from.preweightedavgrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.weightedavgrate() != 0) {
    set_weightedavgrate(from.weightedavgrate());
  }
  if (from.lastvolumetrade() != 0) {
    set_lastvolumetrade(from.lastvolumetrade());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.benchmarkcurvename().size() > 0) {

    benchmarkcurvename_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.benchmarkcurvename_);
  }
  if (from.sessionreferencerate() != 0) {
    set_sessionreferencerate(from.sessionreferencerate());
  }
}

void RateSwapSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RateSwapSnapshot::CopyFrom(const RateSwapSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.RateSwapSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool RateSwapSnapshot::IsInitialized() const {

  return true;
}

void RateSwapSnapshot::Swap(RateSwapSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RateSwapSnapshot::InternalSwap(RateSwapSnapshot* other) {
  std::swap(trademethod_, other->trademethod_);
  tradeterm_.Swap(&other->tradeterm_);
  std::swap(precloserate_, other->precloserate_);
  std::swap(preweightedavgrate_, other->preweightedavgrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(closerate_, other->closerate_);
  std::swap(weightedavgrate_, other->weightedavgrate_);
  std::swap(lastvolumetrade_, other->lastvolumetrade_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  benchmarkcurvename_.Swap(&other->benchmarkcurvename_);
  std::swap(sessionreferencerate_, other->sessionreferencerate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RateSwapSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = RateSwapSnapshot_descriptor_;
  metadata.reflection = RateSwapSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// RateSwapSnapshot

// optional int32 TradeMethod = 1;
void RateSwapSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 RateSwapSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeMethod)
  return trademethod_;
}
void RateSwapSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeMethod)
}

// optional string TradeTerm = 2;
void RateSwapSnapshot::clear_tradeterm() {
  tradeterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapSnapshot::tradeterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  return tradeterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapSnapshot::set_tradeterm(const ::std::string& value) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
void RateSwapSnapshot::set_tradeterm(const char* value) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
void RateSwapSnapshot::set_tradeterm(const char* value, size_t size) {
  
  tradeterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}
::std::string* RateSwapSnapshot::mutable_tradeterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  return tradeterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapSnapshot::release_tradeterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
  
  return tradeterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapSnapshot::set_allocated_tradeterm(::std::string* tradeterm) {
  if (tradeterm != NULL) {
    
  } else {
    
  }
  tradeterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradeterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapSnapshot.TradeTerm)
}

// optional double PreCloseRate = 11;
void RateSwapSnapshot::clear_precloserate() {
  precloserate_ = 0;
}
double RateSwapSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.PreCloseRate)
  return precloserate_;
}
void RateSwapSnapshot::set_precloserate(double value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.PreCloseRate)
}

// optional double PreWeightedAvgRate = 12;
void RateSwapSnapshot::clear_preweightedavgrate() {
  preweightedavgrate_ = 0;
}
double RateSwapSnapshot::preweightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.PreWeightedAvgRate)
  return preweightedavgrate_;
}
void RateSwapSnapshot::set_preweightedavgrate(double value) {
  
  preweightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.PreWeightedAvgRate)
}

// optional double OpenRate = 13;
void RateSwapSnapshot::clear_openrate() {
  openrate_ = 0;
}
double RateSwapSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.OpenRate)
  return openrate_;
}
void RateSwapSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.OpenRate)
}

// optional double LastRate = 14;
void RateSwapSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
double RateSwapSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LastRate)
  return lastrate_;
}
void RateSwapSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LastRate)
}

// optional double HighRate = 15;
void RateSwapSnapshot::clear_highrate() {
  highrate_ = 0;
}
double RateSwapSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.HighRate)
  return highrate_;
}
void RateSwapSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.HighRate)
}

// optional double LowRate = 16;
void RateSwapSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
double RateSwapSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LowRate)
  return lowrate_;
}
void RateSwapSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LowRate)
}

// optional double CloseRate = 17;
void RateSwapSnapshot::clear_closerate() {
  closerate_ = 0;
}
double RateSwapSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.CloseRate)
  return closerate_;
}
void RateSwapSnapshot::set_closerate(double value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.CloseRate)
}

// optional double WeightedAvgRate = 18;
void RateSwapSnapshot::clear_weightedavgrate() {
  weightedavgrate_ = 0;
}
double RateSwapSnapshot::weightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.WeightedAvgRate)
  return weightedavgrate_;
}
void RateSwapSnapshot::set_weightedavgrate(double value) {
  
  weightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.WeightedAvgRate)
}

// optional double LastVolumeTrade = 19;
void RateSwapSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
double RateSwapSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
void RateSwapSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.LastVolumeTrade)
}

// optional double TotalVolumeTrade = 20;
void RateSwapSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
double RateSwapSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
void RateSwapSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.TotalVolumeTrade)
}

// optional string BenchmarkCurveName = 21;
void RateSwapSnapshot::clear_benchmarkcurvename() {
  benchmarkcurvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& RateSwapSnapshot::benchmarkcurvename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  return benchmarkcurvename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapSnapshot::set_benchmarkcurvename(const ::std::string& value) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
void RateSwapSnapshot::set_benchmarkcurvename(const char* value) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
void RateSwapSnapshot::set_benchmarkcurvename(const char* value, size_t size) {
  
  benchmarkcurvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}
::std::string* RateSwapSnapshot::mutable_benchmarkcurvename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  return benchmarkcurvename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* RateSwapSnapshot::release_benchmarkcurvename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
  
  return benchmarkcurvename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void RateSwapSnapshot::set_allocated_benchmarkcurvename(::std::string* benchmarkcurvename) {
  if (benchmarkcurvename != NULL) {
    
  } else {
    
  }
  benchmarkcurvename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), benchmarkcurvename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.RateSwapSnapshot.BenchmarkCurveName)
}

// optional double SessionReferenceRate = 22;
void RateSwapSnapshot::clear_sessionreferencerate() {
  sessionreferencerate_ = 0;
}
double RateSwapSnapshot::sessionreferencerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.RateSwapSnapshot.SessionReferenceRate)
  return sessionreferencerate_;
}
void RateSwapSnapshot::set_sessionreferencerate(double value) {
  
  sessionreferencerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.RateSwapSnapshot.SessionReferenceRate)
}

inline const RateSwapSnapshot* RateSwapSnapshot::internal_default_instance() {
  return &RateSwapSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StandardisedRateSwapSnapshot::kTradeMethodFieldNumber;
const int StandardisedRateSwapSnapshot::kOpenRateFieldNumber;
const int StandardisedRateSwapSnapshot::kHighRateFieldNumber;
const int StandardisedRateSwapSnapshot::kLowRateFieldNumber;
const int StandardisedRateSwapSnapshot::kLastRateFieldNumber;
const int StandardisedRateSwapSnapshot::kLastVolumeTradeFieldNumber;
const int StandardisedRateSwapSnapshot::kTotalVolumeTradeFieldNumber;
const int StandardisedRateSwapSnapshot::kSettleRateFieldNumber;
const int StandardisedRateSwapSnapshot::kSettleRateDateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StandardisedRateSwapSnapshot::StandardisedRateSwapSnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
}

void StandardisedRateSwapSnapshot::InitAsDefaultInstance() {
}

StandardisedRateSwapSnapshot::StandardisedRateSwapSnapshot(const StandardisedRateSwapSnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
}

void StandardisedRateSwapSnapshot::SharedCtor() {
  settleratedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&openrate_, 0, reinterpret_cast<char*>(&trademethod_) -
    reinterpret_cast<char*>(&openrate_) + sizeof(trademethod_));
  _cached_size_ = 0;
}

StandardisedRateSwapSnapshot::~StandardisedRateSwapSnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  SharedDtor();
}

void StandardisedRateSwapSnapshot::SharedDtor() {
  settleratedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void StandardisedRateSwapSnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StandardisedRateSwapSnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return StandardisedRateSwapSnapshot_descriptor_;
}

const StandardisedRateSwapSnapshot& StandardisedRateSwapSnapshot::default_instance() {
  protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<StandardisedRateSwapSnapshot> StandardisedRateSwapSnapshot_default_instance_;

StandardisedRateSwapSnapshot* StandardisedRateSwapSnapshot::New(::google::protobuf::Arena* arena) const {
  StandardisedRateSwapSnapshot* n = new StandardisedRateSwapSnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StandardisedRateSwapSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(StandardisedRateSwapSnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<StandardisedRateSwapSnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(openrate_, trademethod_);
  settleratedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool StandardisedRateSwapSnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 TradeMethod = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trademethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_OpenRate;
        break;
      }

      // optional double OpenRate = 11;
      case 11: {
        if (tag == 89) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_HighRate;
        break;
      }

      // optional double HighRate = 12;
      case 12: {
        if (tag == 97) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_LowRate;
        break;
      }

      // optional double LowRate = 13;
      case 13: {
        if (tag == 105) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_LastRate;
        break;
      }

      // optional double LastRate = 14;
      case 14: {
        if (tag == 113) {
         parse_LastRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_LastVolumeTrade;
        break;
      }

      // optional double LastVolumeTrade = 15;
      case 15: {
        if (tag == 121) {
         parse_LastVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &lastvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional double TotalVolumeTrade = 16;
      case 16: {
        if (tag == 129) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_SettleRate;
        break;
      }

      // optional double SettleRate = 17;
      case 17: {
        if (tag == 137) {
         parse_SettleRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &settlerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_SettleRateDate;
        break;
      }

      // optional string SettleRateDate = 18;
      case 18: {
        if (tag == 146) {
         parse_SettleRateDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settleratedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settleratedate().data(), this->settleratedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  return false;
#undef DO_
}

void StandardisedRateSwapSnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->trademethod(), output);
  }

  // optional double OpenRate = 11;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->openrate(), output);
  }

  // optional double HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->highrate(), output);
  }

  // optional double LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->lowrate(), output);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->lastrate(), output);
  }

  // optional double LastVolumeTrade = 15;
  if (this->lastvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->lastvolumetrade(), output);
  }

  // optional double TotalVolumeTrade = 16;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->totalvolumetrade(), output);
  }

  // optional double SettleRate = 17;
  if (this->settlerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->settlerate(), output);
  }

  // optional string SettleRateDate = 18;
  if (this->settleratedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settleratedate().data(), this->settleratedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->settleratedate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
}

::google::protobuf::uint8* StandardisedRateSwapSnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->trademethod(), target);
  }

  // optional double OpenRate = 11;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->openrate(), target);
  }

  // optional double HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->highrate(), target);
  }

  // optional double LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->lowrate(), target);
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->lastrate(), target);
  }

  // optional double LastVolumeTrade = 15;
  if (this->lastvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->lastvolumetrade(), target);
  }

  // optional double TotalVolumeTrade = 16;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->totalvolumetrade(), target);
  }

  // optional double SettleRate = 17;
  if (this->settlerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->settlerate(), target);
  }

  // optional string SettleRateDate = 18;
  if (this->settleratedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settleratedate().data(), this->settleratedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->settleratedate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  return target;
}

size_t StandardisedRateSwapSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  size_t total_size = 0;

  // optional int32 TradeMethod = 1;
  if (this->trademethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trademethod());
  }

  // optional double OpenRate = 11;
  if (this->openrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastRate = 14;
  if (this->lastrate() != 0) {
    total_size += 1 + 8;
  }

  // optional double LastVolumeTrade = 15;
  if (this->lastvolumetrade() != 0) {
    total_size += 1 + 8;
  }

  // optional double TotalVolumeTrade = 16;
  if (this->totalvolumetrade() != 0) {
    total_size += 2 + 8;
  }

  // optional double SettleRate = 17;
  if (this->settlerate() != 0) {
    total_size += 2 + 8;
  }

  // optional string SettleRateDate = 18;
  if (this->settleratedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settleratedate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StandardisedRateSwapSnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const StandardisedRateSwapSnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StandardisedRateSwapSnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
    UnsafeMergeFrom(*source);
  }
}

void StandardisedRateSwapSnapshot::MergeFrom(const StandardisedRateSwapSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void StandardisedRateSwapSnapshot::UnsafeMergeFrom(const StandardisedRateSwapSnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.trademethod() != 0) {
    set_trademethod(from.trademethod());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.lastrate() != 0) {
    set_lastrate(from.lastrate());
  }
  if (from.lastvolumetrade() != 0) {
    set_lastvolumetrade(from.lastvolumetrade());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.settlerate() != 0) {
    set_settlerate(from.settlerate());
  }
  if (from.settleratedate().size() > 0) {

    settleratedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settleratedate_);
  }
}

void StandardisedRateSwapSnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StandardisedRateSwapSnapshot::CopyFrom(const StandardisedRateSwapSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool StandardisedRateSwapSnapshot::IsInitialized() const {

  return true;
}

void StandardisedRateSwapSnapshot::Swap(StandardisedRateSwapSnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StandardisedRateSwapSnapshot::InternalSwap(StandardisedRateSwapSnapshot* other) {
  std::swap(trademethod_, other->trademethod_);
  std::swap(openrate_, other->openrate_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(lastrate_, other->lastrate_);
  std::swap(lastvolumetrade_, other->lastvolumetrade_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(settlerate_, other->settlerate_);
  settleratedate_.Swap(&other->settleratedate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StandardisedRateSwapSnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = StandardisedRateSwapSnapshot_descriptor_;
  metadata.reflection = StandardisedRateSwapSnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StandardisedRateSwapSnapshot

// optional int32 TradeMethod = 1;
void StandardisedRateSwapSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
::google::protobuf::int32 StandardisedRateSwapSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TradeMethod)
  return trademethod_;
}
void StandardisedRateSwapSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TradeMethod)
}

// optional double OpenRate = 11;
void StandardisedRateSwapSnapshot::clear_openrate() {
  openrate_ = 0;
}
double StandardisedRateSwapSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.OpenRate)
  return openrate_;
}
void StandardisedRateSwapSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.OpenRate)
}

// optional double HighRate = 12;
void StandardisedRateSwapSnapshot::clear_highrate() {
  highrate_ = 0;
}
double StandardisedRateSwapSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.HighRate)
  return highrate_;
}
void StandardisedRateSwapSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.HighRate)
}

// optional double LowRate = 13;
void StandardisedRateSwapSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
double StandardisedRateSwapSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LowRate)
  return lowrate_;
}
void StandardisedRateSwapSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LowRate)
}

// optional double LastRate = 14;
void StandardisedRateSwapSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
double StandardisedRateSwapSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastRate)
  return lastrate_;
}
void StandardisedRateSwapSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastRate)
}

// optional double LastVolumeTrade = 15;
void StandardisedRateSwapSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
double StandardisedRateSwapSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
void StandardisedRateSwapSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.LastVolumeTrade)
}

// optional double TotalVolumeTrade = 16;
void StandardisedRateSwapSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
double StandardisedRateSwapSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
void StandardisedRateSwapSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.TotalVolumeTrade)
}

// optional double SettleRate = 17;
void StandardisedRateSwapSnapshot::clear_settlerate() {
  settlerate_ = 0;
}
double StandardisedRateSwapSnapshot::settlerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRate)
  return settlerate_;
}
void StandardisedRateSwapSnapshot::set_settlerate(double value) {
  
  settlerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRate)
}

// optional string SettleRateDate = 18;
void StandardisedRateSwapSnapshot::clear_settleratedate() {
  settleratedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& StandardisedRateSwapSnapshot::settleratedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  return settleratedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void StandardisedRateSwapSnapshot::set_settleratedate(const ::std::string& value) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
void StandardisedRateSwapSnapshot::set_settleratedate(const char* value) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
void StandardisedRateSwapSnapshot::set_settleratedate(const char* value, size_t size) {
  
  settleratedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}
::std::string* StandardisedRateSwapSnapshot::mutable_settleratedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  return settleratedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* StandardisedRateSwapSnapshot::release_settleratedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
  
  return settleratedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void StandardisedRateSwapSnapshot::set_allocated_settleratedate(::std::string* settleratedate) {
  if (settleratedate != NULL) {
    
  } else {
    
  }
  settleratedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settleratedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.StandardisedRateSwapSnapshot.SettleRateDate)
}

inline const StandardisedRateSwapSnapshot* StandardisedRateSwapSnapshot::internal_default_instance() {
  return &StandardisedRateSwapSnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
