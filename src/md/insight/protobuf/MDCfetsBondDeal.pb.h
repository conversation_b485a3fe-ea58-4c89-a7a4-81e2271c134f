// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBondDeal.proto

#ifndef PROTOBUF_MDCfetsBondDeal_2eproto__INCLUDED
#define PROTOBUF_MDCfetsBondDeal_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsBondDeal_2eproto();
void protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
void protobuf_AssignDesc_MDCfetsBondDeal_2eproto();
void protobuf_ShutdownFile_MDCfetsBondDeal_2eproto();

class BondLendingDeal;
class CashBondTradingDeal;
class MDCfetsBondDeal;

// ===================================================================

class MDCfetsBondDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsBondDeal) */ {
 public:
  MDCfetsBondDeal();
  virtual ~MDCfetsBondDeal();

  MDCfetsBondDeal(const MDCfetsBondDeal& from);

  inline MDCfetsBondDeal& operator=(const MDCfetsBondDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsBondDeal& default_instance();

  static const MDCfetsBondDeal* internal_default_instance();

  void Swap(MDCfetsBondDeal* other);

  // implements Message ----------------------------------------------

  inline MDCfetsBondDeal* New() const { return New(NULL); }

  MDCfetsBondDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsBondDeal& from);
  void MergeFrom(const MDCfetsBondDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsBondDeal* other);
  void UnsafeMergeFrom(const MDCfetsBondDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 DataMultiplePowerOf10 = 9;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 9;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 MessageNumber = 16;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 16;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // optional int32 BondDealType = 21;
  void clear_bonddealtype();
  static const int kBondDealTypeFieldNumber = 21;
  ::google::protobuf::int32 bonddealtype() const;
  void set_bonddealtype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
  bool has_cashbondtradingdeal() const;
  void clear_cashbondtradingdeal();
  static const int kCashBondTradingDealFieldNumber = 22;
  const ::com::htsc::mdc::insight::model::CashBondTradingDeal& cashbondtradingdeal() const;
  ::com::htsc::mdc::insight::model::CashBondTradingDeal* mutable_cashbondtradingdeal();
  ::com::htsc::mdc::insight::model::CashBondTradingDeal* release_cashbondtradingdeal();
  void set_allocated_cashbondtradingdeal(::com::htsc::mdc::insight::model::CashBondTradingDeal* cashbondtradingdeal);

  // optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
  bool has_bondlendingdeal() const;
  void clear_bondlendingdeal();
  static const int kBondLendingDealFieldNumber = 23;
  const ::com::htsc::mdc::insight::model::BondLendingDeal& bondlendingdeal() const;
  ::com::htsc::mdc::insight::model::BondLendingDeal* mutable_bondlendingdeal();
  ::com::htsc::mdc::insight::model::BondLendingDeal* release_bondlendingdeal();
  void set_allocated_bondlendingdeal(::com::htsc::mdc::insight::model::BondLendingDeal* bondlendingdeal);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsBondDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::CashBondTradingDeal* cashbondtradingdeal_;
  ::com::htsc::mdc::insight::model::BondLendingDeal* bondlendingdeal_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int64 messagenumber_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 bonddealtype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBondDeal> MDCfetsBondDeal_default_instance_;

// -------------------------------------------------------------------

class CashBondTradingDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.CashBondTradingDeal) */ {
 public:
  CashBondTradingDeal();
  virtual ~CashBondTradingDeal();

  CashBondTradingDeal(const CashBondTradingDeal& from);

  inline CashBondTradingDeal& operator=(const CashBondTradingDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CashBondTradingDeal& default_instance();

  static const CashBondTradingDeal* internal_default_instance();

  void Swap(CashBondTradingDeal* other);

  // implements Message ----------------------------------------------

  inline CashBondTradingDeal* New() const { return New(NULL); }

  CashBondTradingDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CashBondTradingDeal& from);
  void MergeFrom(const CashBondTradingDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CashBondTradingDeal* other);
  void UnsafeMergeFrom(const CashBondTradingDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TradeDate = 1;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 1;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 2;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 2;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional double Price = 3;
  void clear_price();
  static const int kPriceFieldNumber = 3;
  double price() const;
  void set_price(double value);

  // optional double Yield = 4;
  void clear_yield();
  static const int kYieldFieldNumber = 4;
  double yield() const;
  void set_yield(double value);

  // optional int64 LastQty = 5;
  void clear_lastqty();
  static const int kLastQtyFieldNumber = 5;
  ::google::protobuf::int64 lastqty() const;
  void set_lastqty(::google::protobuf::int64 value);

  // optional bool PreMarketBondIndicator = 6;
  void clear_premarketbondindicator();
  static const int kPreMarketBondIndicatorFieldNumber = 6;
  bool premarketbondindicator() const;
  void set_premarketbondindicator(bool value);

  // optional int32 TradeMethod = 7;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 7;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional string TransactionMethod = 8;
  void clear_transactionmethod();
  static const int kTransactionMethodFieldNumber = 8;
  const ::std::string& transactionmethod() const;
  void set_transactionmethod(const ::std::string& value);
  void set_transactionmethod(const char* value);
  void set_transactionmethod(const char* value, size_t size);
  ::std::string* mutable_transactionmethod();
  ::std::string* release_transactionmethod();
  void set_allocated_transactionmethod(::std::string* transactionmethod);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.CashBondTradingDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  ::google::protobuf::internal::ArenaStringPtr transactionmethod_;
  double price_;
  double yield_;
  ::google::protobuf::int64 lastqty_;
  bool premarketbondindicator_;
  ::google::protobuf::int32 trademethod_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CashBondTradingDeal> CashBondTradingDeal_default_instance_;

// -------------------------------------------------------------------

class BondLendingDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BondLendingDeal) */ {
 public:
  BondLendingDeal();
  virtual ~BondLendingDeal();

  BondLendingDeal(const BondLendingDeal& from);

  inline BondLendingDeal& operator=(const BondLendingDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BondLendingDeal& default_instance();

  static const BondLendingDeal* internal_default_instance();

  void Swap(BondLendingDeal* other);

  // implements Message ----------------------------------------------

  inline BondLendingDeal* New() const { return New(NULL); }

  BondLendingDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BondLendingDeal& from);
  void MergeFrom(const BondLendingDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BondLendingDeal* other);
  void UnsafeMergeFrom(const BondLendingDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TradeDate = 1;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 1;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 2;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 2;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional double Price = 3;
  void clear_price();
  static const int kPriceFieldNumber = 3;
  double price() const;
  void set_price(double value);

  // optional string UnderlyingSymbol = 4;
  void clear_underlyingsymbol();
  static const int kUnderlyingSymbolFieldNumber = 4;
  const ::std::string& underlyingsymbol() const;
  void set_underlyingsymbol(const ::std::string& value);
  void set_underlyingsymbol(const char* value);
  void set_underlyingsymbol(const char* value, size_t size);
  ::std::string* mutable_underlyingsymbol();
  ::std::string* release_underlyingsymbol();
  void set_allocated_underlyingsymbol(::std::string* underlyingsymbol);

  // optional string UnderlyingSecurityID = 5;
  void clear_underlyingsecurityid();
  static const int kUnderlyingSecurityIDFieldNumber = 5;
  const ::std::string& underlyingsecurityid() const;
  void set_underlyingsecurityid(const ::std::string& value);
  void set_underlyingsecurityid(const char* value);
  void set_underlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_underlyingsecurityid();
  ::std::string* release_underlyingsecurityid();
  void set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid);

  // optional double UnderlyingQty = 6;
  void clear_underlyingqty();
  static const int kUnderlyingQtyFieldNumber = 6;
  double underlyingqty() const;
  void set_underlyingqty(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BondLendingDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsymbol_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsecurityid_;
  double price_;
  double underlyingqty_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BondLendingDeal> BondLendingDeal_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBondDeal

// optional string HTSCSecurityID = 1;
inline void MDCfetsBondDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
inline void MDCfetsBondDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
inline void MDCfetsBondDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}
inline ::std::string* MDCfetsBondDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsBondDeal::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsBondDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsBondDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsBondDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsBondDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsBondDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsBondDeal::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDDate)
  return mddate_;
}
inline void MDCfetsBondDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsBondDeal::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDTime)
  return mdtime_;
}
inline void MDCfetsBondDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsBondDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBondDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsBondDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsBondDeal::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondDeal::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
inline void MDCfetsBondDeal::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
inline void MDCfetsBondDeal::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}
inline ::std::string* MDCfetsBondDeal::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondDeal::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsBondDeal::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondDeal::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
inline void MDCfetsBondDeal::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
inline void MDCfetsBondDeal::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}
inline ::std::string* MDCfetsBondDeal::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondDeal::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondDeal::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
inline void MDCfetsBondDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsBondDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 16;
inline void MDCfetsBondDeal::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBondDeal::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.MessageNumber)
  return messagenumber_;
}
inline void MDCfetsBondDeal::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.MessageNumber)
}

// optional int32 BondDealType = 21;
inline void MDCfetsBondDeal::clear_bonddealtype() {
  bonddealtype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondDeal::bonddealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondDealType)
  return bonddealtype_;
}
inline void MDCfetsBondDeal::set_bonddealtype(::google::protobuf::int32 value) {
  
  bonddealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondDealType)
}

// optional .com.htsc.mdc.insight.model.CashBondTradingDeal CashBondTradingDeal = 22;
inline bool MDCfetsBondDeal::has_cashbondtradingdeal() const {
  return this != internal_default_instance() && cashbondtradingdeal_ != NULL;
}
inline void MDCfetsBondDeal::clear_cashbondtradingdeal() {
  if (GetArenaNoVirtual() == NULL && cashbondtradingdeal_ != NULL) delete cashbondtradingdeal_;
  cashbondtradingdeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::CashBondTradingDeal& MDCfetsBondDeal::cashbondtradingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  return cashbondtradingdeal_ != NULL ? *cashbondtradingdeal_
                         : *::com::htsc::mdc::insight::model::CashBondTradingDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::CashBondTradingDeal* MDCfetsBondDeal::mutable_cashbondtradingdeal() {
  
  if (cashbondtradingdeal_ == NULL) {
    cashbondtradingdeal_ = new ::com::htsc::mdc::insight::model::CashBondTradingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  return cashbondtradingdeal_;
}
inline ::com::htsc::mdc::insight::model::CashBondTradingDeal* MDCfetsBondDeal::release_cashbondtradingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
  
  ::com::htsc::mdc::insight::model::CashBondTradingDeal* temp = cashbondtradingdeal_;
  cashbondtradingdeal_ = NULL;
  return temp;
}
inline void MDCfetsBondDeal::set_allocated_cashbondtradingdeal(::com::htsc::mdc::insight::model::CashBondTradingDeal* cashbondtradingdeal) {
  delete cashbondtradingdeal_;
  cashbondtradingdeal_ = cashbondtradingdeal;
  if (cashbondtradingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.CashBondTradingDeal)
}

// optional .com.htsc.mdc.insight.model.BondLendingDeal BondLendingDeal = 23;
inline bool MDCfetsBondDeal::has_bondlendingdeal() const {
  return this != internal_default_instance() && bondlendingdeal_ != NULL;
}
inline void MDCfetsBondDeal::clear_bondlendingdeal() {
  if (GetArenaNoVirtual() == NULL && bondlendingdeal_ != NULL) delete bondlendingdeal_;
  bondlendingdeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::BondLendingDeal& MDCfetsBondDeal::bondlendingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  return bondlendingdeal_ != NULL ? *bondlendingdeal_
                         : *::com::htsc::mdc::insight::model::BondLendingDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::BondLendingDeal* MDCfetsBondDeal::mutable_bondlendingdeal() {
  
  if (bondlendingdeal_ == NULL) {
    bondlendingdeal_ = new ::com::htsc::mdc::insight::model::BondLendingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  return bondlendingdeal_;
}
inline ::com::htsc::mdc::insight::model::BondLendingDeal* MDCfetsBondDeal::release_bondlendingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
  
  ::com::htsc::mdc::insight::model::BondLendingDeal* temp = bondlendingdeal_;
  bondlendingdeal_ = NULL;
  return temp;
}
inline void MDCfetsBondDeal::set_allocated_bondlendingdeal(::com::htsc::mdc::insight::model::BondLendingDeal* bondlendingdeal) {
  delete bondlendingdeal_;
  bondlendingdeal_ = bondlendingdeal;
  if (bondlendingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondDeal.BondLendingDeal)
}

inline const MDCfetsBondDeal* MDCfetsBondDeal::internal_default_instance() {
  return &MDCfetsBondDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// CashBondTradingDeal

// optional string TradeDate = 1;
inline void CashBondTradingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CashBondTradingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
inline void CashBondTradingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
inline void CashBondTradingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}
inline ::std::string* CashBondTradingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CashBondTradingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeDate)
}

// optional string TradeTime = 2;
inline void CashBondTradingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CashBondTradingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
inline void CashBondTradingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
inline void CashBondTradingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}
inline ::std::string* CashBondTradingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CashBondTradingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeTime)
}

// optional double Price = 3;
inline void CashBondTradingDeal::clear_price() {
  price_ = 0;
}
inline double CashBondTradingDeal::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.Price)
  return price_;
}
inline void CashBondTradingDeal::set_price(double value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.Price)
}

// optional double Yield = 4;
inline void CashBondTradingDeal::clear_yield() {
  yield_ = 0;
}
inline double CashBondTradingDeal::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.Yield)
  return yield_;
}
inline void CashBondTradingDeal::set_yield(double value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.Yield)
}

// optional int64 LastQty = 5;
inline void CashBondTradingDeal::clear_lastqty() {
  lastqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 CashBondTradingDeal::lastqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.LastQty)
  return lastqty_;
}
inline void CashBondTradingDeal::set_lastqty(::google::protobuf::int64 value) {
  
  lastqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.LastQty)
}

// optional bool PreMarketBondIndicator = 6;
inline void CashBondTradingDeal::clear_premarketbondindicator() {
  premarketbondindicator_ = false;
}
inline bool CashBondTradingDeal::premarketbondindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.PreMarketBondIndicator)
  return premarketbondindicator_;
}
inline void CashBondTradingDeal::set_premarketbondindicator(bool value) {
  
  premarketbondindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.PreMarketBondIndicator)
}

// optional int32 TradeMethod = 7;
inline void CashBondTradingDeal::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 CashBondTradingDeal::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeMethod)
  return trademethod_;
}
inline void CashBondTradingDeal::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TradeMethod)
}

// optional string TransactionMethod = 8;
inline void CashBondTradingDeal::clear_transactionmethod() {
  transactionmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CashBondTradingDeal::transactionmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  return transactionmethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_transactionmethod(const ::std::string& value) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
inline void CashBondTradingDeal::set_transactionmethod(const char* value) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
inline void CashBondTradingDeal::set_transactionmethod(const char* value, size_t size) {
  
  transactionmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}
inline ::std::string* CashBondTradingDeal::mutable_transactionmethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  return transactionmethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CashBondTradingDeal::release_transactionmethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
  
  return transactionmethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingDeal::set_allocated_transactionmethod(::std::string* transactionmethod) {
  if (transactionmethod != NULL) {
    
  } else {
    
  }
  transactionmethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactionmethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingDeal.TransactionMethod)
}

inline const CashBondTradingDeal* CashBondTradingDeal::internal_default_instance() {
  return &CashBondTradingDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// BondLendingDeal

// optional string TradeDate = 1;
inline void BondLendingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
inline void BondLendingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
inline void BondLendingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}
inline ::std::string* BondLendingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.TradeDate)
}

// optional string TradeTime = 2;
inline void BondLendingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
inline void BondLendingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
inline void BondLendingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}
inline ::std::string* BondLendingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.TradeTime)
}

// optional double Price = 3;
inline void BondLendingDeal::clear_price() {
  price_ = 0;
}
inline double BondLendingDeal::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.Price)
  return price_;
}
inline void BondLendingDeal::set_price(double value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.Price)
}

// optional string UnderlyingSymbol = 4;
inline void BondLendingDeal::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingDeal::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
inline void BondLendingDeal::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
inline void BondLendingDeal::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}
inline ::std::string* BondLendingDeal::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingDeal::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 5;
inline void BondLendingDeal::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingDeal::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
inline void BondLendingDeal::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
inline void BondLendingDeal::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}
inline ::std::string* BondLendingDeal::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingDeal::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingDeal::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingSecurityID)
}

// optional double UnderlyingQty = 6;
inline void BondLendingDeal::clear_underlyingqty() {
  underlyingqty_ = 0;
}
inline double BondLendingDeal::underlyingqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingQty)
  return underlyingqty_;
}
inline void BondLendingDeal::set_underlyingqty(double value) {
  
  underlyingqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingDeal.UnderlyingQty)
}

inline const BondLendingDeal* BondLendingDeal::internal_default_instance() {
  return &BondLendingDeal_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsBondDeal_2eproto__INCLUDED
