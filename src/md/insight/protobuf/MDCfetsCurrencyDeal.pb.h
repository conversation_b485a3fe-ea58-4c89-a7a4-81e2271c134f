// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsCurrencyDeal.proto

#ifndef PROTOBUF_MDCfetsCurrencyDeal_2eproto__INCLUDED
#define PROTOBUF_MDCfetsCurrencyDeal_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto();
void protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

class CollateralRepoDeal;
class InterBankOfferingDeal;
class MDCfetsCurrencyDeal;
class OutrightRepoDeal;
class UnderlyingSecurityDetail;

// ===================================================================

class MDCfetsCurrencyDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal) */ {
 public:
  MDCfetsCurrencyDeal();
  virtual ~MDCfetsCurrencyDeal();

  MDCfetsCurrencyDeal(const MDCfetsCurrencyDeal& from);

  inline MDCfetsCurrencyDeal& operator=(const MDCfetsCurrencyDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsCurrencyDeal& default_instance();

  static const MDCfetsCurrencyDeal* internal_default_instance();

  void Swap(MDCfetsCurrencyDeal* other);

  // implements Message ----------------------------------------------

  inline MDCfetsCurrencyDeal* New() const { return New(NULL); }

  MDCfetsCurrencyDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsCurrencyDeal& from);
  void MergeFrom(const MDCfetsCurrencyDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsCurrencyDeal* other);
  void UnsafeMergeFrom(const MDCfetsCurrencyDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 DataMultiplePowerOf10 = 9;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 9;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 MessageNumber = 16;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 16;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // optional int32 CurrencyDealType = 21;
  void clear_currencydealtype();
  static const int kCurrencyDealTypeFieldNumber = 21;
  ::google::protobuf::int32 currencydealtype() const;
  void set_currencydealtype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
  bool has_interbankofferingdeal() const;
  void clear_interbankofferingdeal();
  static const int kInterBankOfferingDealFieldNumber = 22;
  const ::com::htsc::mdc::insight::model::InterBankOfferingDeal& interbankofferingdeal() const;
  ::com::htsc::mdc::insight::model::InterBankOfferingDeal* mutable_interbankofferingdeal();
  ::com::htsc::mdc::insight::model::InterBankOfferingDeal* release_interbankofferingdeal();
  void set_allocated_interbankofferingdeal(::com::htsc::mdc::insight::model::InterBankOfferingDeal* interbankofferingdeal);

  // optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
  bool has_collateralrepodeal() const;
  void clear_collateralrepodeal();
  static const int kCollateralRepoDealFieldNumber = 23;
  const ::com::htsc::mdc::insight::model::CollateralRepoDeal& collateralrepodeal() const;
  ::com::htsc::mdc::insight::model::CollateralRepoDeal* mutable_collateralrepodeal();
  ::com::htsc::mdc::insight::model::CollateralRepoDeal* release_collateralrepodeal();
  void set_allocated_collateralrepodeal(::com::htsc::mdc::insight::model::CollateralRepoDeal* collateralrepodeal);

  // optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
  bool has_outrightrepodeal() const;
  void clear_outrightrepodeal();
  static const int kOutrightRepoDealFieldNumber = 24;
  const ::com::htsc::mdc::insight::model::OutrightRepoDeal& outrightrepodeal() const;
  ::com::htsc::mdc::insight::model::OutrightRepoDeal* mutable_outrightrepodeal();
  ::com::htsc::mdc::insight::model::OutrightRepoDeal* release_outrightrepodeal();
  void set_allocated_outrightrepodeal(::com::htsc::mdc::insight::model::OutrightRepoDeal* outrightrepodeal);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::InterBankOfferingDeal* interbankofferingdeal_;
  ::com::htsc::mdc::insight::model::CollateralRepoDeal* collateralrepodeal_;
  ::com::htsc::mdc::insight::model::OutrightRepoDeal* outrightrepodeal_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int64 messagenumber_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 currencydealtype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsCurrencyDeal> MDCfetsCurrencyDeal_default_instance_;

// -------------------------------------------------------------------

class InterBankOfferingDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.InterBankOfferingDeal) */ {
 public:
  InterBankOfferingDeal();
  virtual ~InterBankOfferingDeal();

  InterBankOfferingDeal(const InterBankOfferingDeal& from);

  inline InterBankOfferingDeal& operator=(const InterBankOfferingDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const InterBankOfferingDeal& default_instance();

  static const InterBankOfferingDeal* internal_default_instance();

  void Swap(InterBankOfferingDeal* other);

  // implements Message ----------------------------------------------

  inline InterBankOfferingDeal* New() const { return New(NULL); }

  InterBankOfferingDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const InterBankOfferingDeal& from);
  void MergeFrom(const InterBankOfferingDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(InterBankOfferingDeal* other);
  void UnsafeMergeFrom(const InterBankOfferingDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TradeDate = 1;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 1;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 2;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 2;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional double TradeYield = 3;
  void clear_tradeyield();
  static const int kTradeYieldFieldNumber = 3;
  double tradeyield() const;
  void set_tradeyield(double value);

  // optional double TradeMoney = 4;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 4;
  double trademoney() const;
  void set_trademoney(double value);

  // optional double Term = 5;
  void clear_term();
  static const int kTermFieldNumber = 5;
  double term() const;
  void set_term(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.InterBankOfferingDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  double tradeyield_;
  double trademoney_;
  double term_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<InterBankOfferingDeal> InterBankOfferingDeal_default_instance_;

// -------------------------------------------------------------------

class CollateralRepoDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.CollateralRepoDeal) */ {
 public:
  CollateralRepoDeal();
  virtual ~CollateralRepoDeal();

  CollateralRepoDeal(const CollateralRepoDeal& from);

  inline CollateralRepoDeal& operator=(const CollateralRepoDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CollateralRepoDeal& default_instance();

  static const CollateralRepoDeal* internal_default_instance();

  void Swap(CollateralRepoDeal* other);

  // implements Message ----------------------------------------------

  inline CollateralRepoDeal* New() const { return New(NULL); }

  CollateralRepoDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CollateralRepoDeal& from);
  void MergeFrom(const CollateralRepoDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CollateralRepoDeal* other);
  void UnsafeMergeFrom(const CollateralRepoDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TradeDate = 1;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 1;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 2;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 2;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional double TradeYield = 3;
  void clear_tradeyield();
  static const int kTradeYieldFieldNumber = 3;
  double tradeyield() const;
  void set_tradeyield(double value);

  // optional double TradeMoney = 4;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 4;
  double trademoney() const;
  void set_trademoney(double value);

  // optional double Term = 5;
  void clear_term();
  static const int kTermFieldNumber = 5;
  double term() const;
  void set_term(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.CollateralRepoDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  double tradeyield_;
  double trademoney_;
  double term_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CollateralRepoDeal> CollateralRepoDeal_default_instance_;

// -------------------------------------------------------------------

class OutrightRepoDeal : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.OutrightRepoDeal) */ {
 public:
  OutrightRepoDeal();
  virtual ~OutrightRepoDeal();

  OutrightRepoDeal(const OutrightRepoDeal& from);

  inline OutrightRepoDeal& operator=(const OutrightRepoDeal& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OutrightRepoDeal& default_instance();

  static const OutrightRepoDeal* internal_default_instance();

  void Swap(OutrightRepoDeal* other);

  // implements Message ----------------------------------------------

  inline OutrightRepoDeal* New() const { return New(NULL); }

  OutrightRepoDeal* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OutrightRepoDeal& from);
  void MergeFrom(const OutrightRepoDeal& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OutrightRepoDeal* other);
  void UnsafeMergeFrom(const OutrightRepoDeal& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string TradeDate = 1;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 1;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 2;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 2;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional double TradeYield = 3;
  void clear_tradeyield();
  static const int kTradeYieldFieldNumber = 3;
  double tradeyield() const;
  void set_tradeyield(double value);

  // optional double Term = 4;
  void clear_term();
  static const int kTermFieldNumber = 4;
  double term() const;
  void set_term(double value);

  // optional double TotalTradeMoney = 5;
  void clear_totaltrademoney();
  static const int kTotalTradeMoneyFieldNumber = 5;
  double totaltrademoney() const;
  void set_totaltrademoney(double value);

  // optional double TotalSettlCurrAmt = 6;
  void clear_totalsettlcurramt();
  static const int kTotalSettlCurrAmtFieldNumber = 6;
  double totalsettlcurramt() const;
  void set_totalsettlcurramt(double value);

  // optional double TotalSettlCurrAmt2 = 7;
  void clear_totalsettlcurramt2();
  static const int kTotalSettlCurrAmt2FieldNumber = 7;
  double totalsettlcurramt2() const;
  void set_totalsettlcurramt2(double value);

  // repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
  int securitydetaillist_size() const;
  void clear_securitydetaillist();
  static const int kSecurityDetailListFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail& securitydetaillist(int index) const;
  ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* mutable_securitydetaillist(int index);
  ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* add_securitydetaillist();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >*
      mutable_securitydetaillist();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >&
      securitydetaillist() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.OutrightRepoDeal)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail > securitydetaillist_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  double tradeyield_;
  double term_;
  double totaltrademoney_;
  double totalsettlcurramt_;
  double totalsettlcurramt2_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OutrightRepoDeal> OutrightRepoDeal_default_instance_;

// -------------------------------------------------------------------

class UnderlyingSecurityDetail : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.UnderlyingSecurityDetail) */ {
 public:
  UnderlyingSecurityDetail();
  virtual ~UnderlyingSecurityDetail();

  UnderlyingSecurityDetail(const UnderlyingSecurityDetail& from);

  inline UnderlyingSecurityDetail& operator=(const UnderlyingSecurityDetail& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const UnderlyingSecurityDetail& default_instance();

  static const UnderlyingSecurityDetail* internal_default_instance();

  void Swap(UnderlyingSecurityDetail* other);

  // implements Message ----------------------------------------------

  inline UnderlyingSecurityDetail* New() const { return New(NULL); }

  UnderlyingSecurityDetail* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const UnderlyingSecurityDetail& from);
  void MergeFrom(const UnderlyingSecurityDetail& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(UnderlyingSecurityDetail* other);
  void UnsafeMergeFrom(const UnderlyingSecurityDetail& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string UnderlyingSecurityID = 1;
  void clear_underlyingsecurityid();
  static const int kUnderlyingSecurityIDFieldNumber = 1;
  const ::std::string& underlyingsecurityid() const;
  void set_underlyingsecurityid(const ::std::string& value);
  void set_underlyingsecurityid(const char* value);
  void set_underlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_underlyingsecurityid();
  ::std::string* release_underlyingsecurityid();
  void set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid);

  // optional string UnderlyingSymbol = 2;
  void clear_underlyingsymbol();
  static const int kUnderlyingSymbolFieldNumber = 2;
  const ::std::string& underlyingsymbol() const;
  void set_underlyingsymbol(const ::std::string& value);
  void set_underlyingsymbol(const char* value);
  void set_underlyingsymbol(const char* value, size_t size);
  ::std::string* mutable_underlyingsymbol();
  ::std::string* release_underlyingsymbol();
  void set_allocated_underlyingsymbol(::std::string* underlyingsymbol);

  // optional double UnderlyingSettlCurrAmt = 3;
  void clear_underlyingsettlcurramt();
  static const int kUnderlyingSettlCurrAmtFieldNumber = 3;
  double underlyingsettlcurramt() const;
  void set_underlyingsettlcurramt(double value);

  // optional double UnderlyingSettlCurrAmt2 = 4;
  void clear_underlyingsettlcurramt2();
  static const int kUnderlyingSettlCurrAmt2FieldNumber = 4;
  double underlyingsettlcurramt2() const;
  void set_underlyingsettlcurramt2(double value);

  // optional double UnderlyingQty = 5;
  void clear_underlyingqty();
  static const int kUnderlyingQtyFieldNumber = 5;
  double underlyingqty() const;
  void set_underlyingqty(double value);

  // optional double UnderlyingPx = 6;
  void clear_underlyingpx();
  static const int kUnderlyingPxFieldNumber = 6;
  double underlyingpx() const;
  void set_underlyingpx(double value);

  // optional double UnderlyingPx2 = 7;
  void clear_underlyingpx2();
  static const int kUnderlyingPx2FieldNumber = 7;
  double underlyingpx2() const;
  void set_underlyingpx2(double value);

  // optional double UnderlyingYield = 8;
  void clear_underlyingyield();
  static const int kUnderlyingYieldFieldNumber = 8;
  double underlyingyield() const;
  void set_underlyingyield(double value);

  // optional double UnderlyingYield2 = 9;
  void clear_underlyingyield2();
  static const int kUnderlyingYield2FieldNumber = 9;
  double underlyingyield2() const;
  void set_underlyingyield2(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.UnderlyingSecurityDetail)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsymbol_;
  double underlyingsettlcurramt_;
  double underlyingsettlcurramt2_;
  double underlyingqty_;
  double underlyingpx_;
  double underlyingpx2_;
  double underlyingyield_;
  double underlyingyield2_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsCurrencyDeal_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsCurrencyDeal_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<UnderlyingSecurityDetail> UnderlyingSecurityDetail_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsCurrencyDeal

// optional string HTSCSecurityID = 1;
inline void MDCfetsCurrencyDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencyDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
inline void MDCfetsCurrencyDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
inline void MDCfetsCurrencyDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}
inline ::std::string* MDCfetsCurrencyDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencyDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsCurrencyDeal::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsCurrencyDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsCurrencyDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsCurrencyDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsCurrencyDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsCurrencyDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsCurrencyDeal::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencyDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDDate)
  return mddate_;
}
inline void MDCfetsCurrencyDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsCurrencyDeal::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencyDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDTime)
  return mdtime_;
}
inline void MDCfetsCurrencyDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsCurrencyDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsCurrencyDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsCurrencyDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsCurrencyDeal::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencyDeal::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
inline void MDCfetsCurrencyDeal::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
inline void MDCfetsCurrencyDeal::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}
inline ::std::string* MDCfetsCurrencyDeal::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencyDeal::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsCurrencyDeal::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsCurrencyDeal::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
inline void MDCfetsCurrencyDeal::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
inline void MDCfetsCurrencyDeal::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}
inline ::std::string* MDCfetsCurrencyDeal::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsCurrencyDeal::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsCurrencyDeal::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
inline void MDCfetsCurrencyDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencyDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsCurrencyDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.DataMultiplePowerOf10)
}

// optional int64 MessageNumber = 16;
inline void MDCfetsCurrencyDeal::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsCurrencyDeal::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MessageNumber)
  return messagenumber_;
}
inline void MDCfetsCurrencyDeal::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.MessageNumber)
}

// optional int32 CurrencyDealType = 21;
inline void MDCfetsCurrencyDeal::clear_currencydealtype() {
  currencydealtype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsCurrencyDeal::currencydealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CurrencyDealType)
  return currencydealtype_;
}
inline void MDCfetsCurrencyDeal::set_currencydealtype(::google::protobuf::int32 value) {
  
  currencydealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CurrencyDealType)
}

// optional .com.htsc.mdc.insight.model.InterBankOfferingDeal InterBankOfferingDeal = 22;
inline bool MDCfetsCurrencyDeal::has_interbankofferingdeal() const {
  return this != internal_default_instance() && interbankofferingdeal_ != NULL;
}
inline void MDCfetsCurrencyDeal::clear_interbankofferingdeal() {
  if (GetArenaNoVirtual() == NULL && interbankofferingdeal_ != NULL) delete interbankofferingdeal_;
  interbankofferingdeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InterBankOfferingDeal& MDCfetsCurrencyDeal::interbankofferingdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  return interbankofferingdeal_ != NULL ? *interbankofferingdeal_
                         : *::com::htsc::mdc::insight::model::InterBankOfferingDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InterBankOfferingDeal* MDCfetsCurrencyDeal::mutable_interbankofferingdeal() {
  
  if (interbankofferingdeal_ == NULL) {
    interbankofferingdeal_ = new ::com::htsc::mdc::insight::model::InterBankOfferingDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  return interbankofferingdeal_;
}
inline ::com::htsc::mdc::insight::model::InterBankOfferingDeal* MDCfetsCurrencyDeal::release_interbankofferingdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
  
  ::com::htsc::mdc::insight::model::InterBankOfferingDeal* temp = interbankofferingdeal_;
  interbankofferingdeal_ = NULL;
  return temp;
}
inline void MDCfetsCurrencyDeal::set_allocated_interbankofferingdeal(::com::htsc::mdc::insight::model::InterBankOfferingDeal* interbankofferingdeal) {
  delete interbankofferingdeal_;
  interbankofferingdeal_ = interbankofferingdeal;
  if (interbankofferingdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.InterBankOfferingDeal)
}

// optional .com.htsc.mdc.insight.model.CollateralRepoDeal CollateralRepoDeal = 23;
inline bool MDCfetsCurrencyDeal::has_collateralrepodeal() const {
  return this != internal_default_instance() && collateralrepodeal_ != NULL;
}
inline void MDCfetsCurrencyDeal::clear_collateralrepodeal() {
  if (GetArenaNoVirtual() == NULL && collateralrepodeal_ != NULL) delete collateralrepodeal_;
  collateralrepodeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::CollateralRepoDeal& MDCfetsCurrencyDeal::collateralrepodeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  return collateralrepodeal_ != NULL ? *collateralrepodeal_
                         : *::com::htsc::mdc::insight::model::CollateralRepoDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::CollateralRepoDeal* MDCfetsCurrencyDeal::mutable_collateralrepodeal() {
  
  if (collateralrepodeal_ == NULL) {
    collateralrepodeal_ = new ::com::htsc::mdc::insight::model::CollateralRepoDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  return collateralrepodeal_;
}
inline ::com::htsc::mdc::insight::model::CollateralRepoDeal* MDCfetsCurrencyDeal::release_collateralrepodeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
  
  ::com::htsc::mdc::insight::model::CollateralRepoDeal* temp = collateralrepodeal_;
  collateralrepodeal_ = NULL;
  return temp;
}
inline void MDCfetsCurrencyDeal::set_allocated_collateralrepodeal(::com::htsc::mdc::insight::model::CollateralRepoDeal* collateralrepodeal) {
  delete collateralrepodeal_;
  collateralrepodeal_ = collateralrepodeal;
  if (collateralrepodeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.CollateralRepoDeal)
}

// optional .com.htsc.mdc.insight.model.OutrightRepoDeal OutrightRepoDeal = 24;
inline bool MDCfetsCurrencyDeal::has_outrightrepodeal() const {
  return this != internal_default_instance() && outrightrepodeal_ != NULL;
}
inline void MDCfetsCurrencyDeal::clear_outrightrepodeal() {
  if (GetArenaNoVirtual() == NULL && outrightrepodeal_ != NULL) delete outrightrepodeal_;
  outrightrepodeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::OutrightRepoDeal& MDCfetsCurrencyDeal::outrightrepodeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  return outrightrepodeal_ != NULL ? *outrightrepodeal_
                         : *::com::htsc::mdc::insight::model::OutrightRepoDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::OutrightRepoDeal* MDCfetsCurrencyDeal::mutable_outrightrepodeal() {
  
  if (outrightrepodeal_ == NULL) {
    outrightrepodeal_ = new ::com::htsc::mdc::insight::model::OutrightRepoDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  return outrightrepodeal_;
}
inline ::com::htsc::mdc::insight::model::OutrightRepoDeal* MDCfetsCurrencyDeal::release_outrightrepodeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
  
  ::com::htsc::mdc::insight::model::OutrightRepoDeal* temp = outrightrepodeal_;
  outrightrepodeal_ = NULL;
  return temp;
}
inline void MDCfetsCurrencyDeal::set_allocated_outrightrepodeal(::com::htsc::mdc::insight::model::OutrightRepoDeal* outrightrepodeal) {
  delete outrightrepodeal_;
  outrightrepodeal_ = outrightrepodeal;
  if (outrightrepodeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsCurrencyDeal.OutrightRepoDeal)
}

inline const MDCfetsCurrencyDeal* MDCfetsCurrencyDeal::internal_default_instance() {
  return &MDCfetsCurrencyDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// InterBankOfferingDeal

// optional string TradeDate = 1;
inline void InterBankOfferingDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& InterBankOfferingDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InterBankOfferingDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
inline void InterBankOfferingDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
inline void InterBankOfferingDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}
inline ::std::string* InterBankOfferingDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* InterBankOfferingDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InterBankOfferingDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeDate)
}

// optional string TradeTime = 2;
inline void InterBankOfferingDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& InterBankOfferingDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InterBankOfferingDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
inline void InterBankOfferingDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
inline void InterBankOfferingDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}
inline ::std::string* InterBankOfferingDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* InterBankOfferingDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InterBankOfferingDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeTime)
}

// optional double TradeYield = 3;
inline void InterBankOfferingDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
inline double InterBankOfferingDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeYield)
  return tradeyield_;
}
inline void InterBankOfferingDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeYield)
}

// optional double TradeMoney = 4;
inline void InterBankOfferingDeal::clear_trademoney() {
  trademoney_ = 0;
}
inline double InterBankOfferingDeal::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeMoney)
  return trademoney_;
}
inline void InterBankOfferingDeal::set_trademoney(double value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.TradeMoney)
}

// optional double Term = 5;
inline void InterBankOfferingDeal::clear_term() {
  term_ = 0;
}
inline double InterBankOfferingDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.InterBankOfferingDeal.Term)
  return term_;
}
inline void InterBankOfferingDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.InterBankOfferingDeal.Term)
}

inline const InterBankOfferingDeal* InterBankOfferingDeal::internal_default_instance() {
  return &InterBankOfferingDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// CollateralRepoDeal

// optional string TradeDate = 1;
inline void CollateralRepoDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CollateralRepoDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
inline void CollateralRepoDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
inline void CollateralRepoDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}
inline ::std::string* CollateralRepoDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CollateralRepoDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeDate)
}

// optional string TradeTime = 2;
inline void CollateralRepoDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CollateralRepoDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
inline void CollateralRepoDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
inline void CollateralRepoDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}
inline ::std::string* CollateralRepoDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CollateralRepoDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CollateralRepoDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeTime)
}

// optional double TradeYield = 3;
inline void CollateralRepoDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
inline double CollateralRepoDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeYield)
  return tradeyield_;
}
inline void CollateralRepoDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeYield)
}

// optional double TradeMoney = 4;
inline void CollateralRepoDeal::clear_trademoney() {
  trademoney_ = 0;
}
inline double CollateralRepoDeal::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeMoney)
  return trademoney_;
}
inline void CollateralRepoDeal::set_trademoney(double value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.TradeMoney)
}

// optional double Term = 5;
inline void CollateralRepoDeal::clear_term() {
  term_ = 0;
}
inline double CollateralRepoDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CollateralRepoDeal.Term)
  return term_;
}
inline void CollateralRepoDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CollateralRepoDeal.Term)
}

inline const CollateralRepoDeal* CollateralRepoDeal::internal_default_instance() {
  return &CollateralRepoDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// OutrightRepoDeal

// optional string TradeDate = 1;
inline void OutrightRepoDeal::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OutrightRepoDeal::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OutrightRepoDeal::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
inline void OutrightRepoDeal::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
inline void OutrightRepoDeal::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}
inline ::std::string* OutrightRepoDeal::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OutrightRepoDeal::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OutrightRepoDeal::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeDate)
}

// optional string TradeTime = 2;
inline void OutrightRepoDeal::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OutrightRepoDeal::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OutrightRepoDeal::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
inline void OutrightRepoDeal::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
inline void OutrightRepoDeal::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}
inline ::std::string* OutrightRepoDeal::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OutrightRepoDeal::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OutrightRepoDeal::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeTime)
}

// optional double TradeYield = 3;
inline void OutrightRepoDeal::clear_tradeyield() {
  tradeyield_ = 0;
}
inline double OutrightRepoDeal::tradeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeYield)
  return tradeyield_;
}
inline void OutrightRepoDeal::set_tradeyield(double value) {
  
  tradeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TradeYield)
}

// optional double Term = 4;
inline void OutrightRepoDeal::clear_term() {
  term_ = 0;
}
inline double OutrightRepoDeal::term() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.Term)
  return term_;
}
inline void OutrightRepoDeal::set_term(double value) {
  
  term_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.Term)
}

// optional double TotalTradeMoney = 5;
inline void OutrightRepoDeal::clear_totaltrademoney() {
  totaltrademoney_ = 0;
}
inline double OutrightRepoDeal::totaltrademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalTradeMoney)
  return totaltrademoney_;
}
inline void OutrightRepoDeal::set_totaltrademoney(double value) {
  
  totaltrademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalTradeMoney)
}

// optional double TotalSettlCurrAmt = 6;
inline void OutrightRepoDeal::clear_totalsettlcurramt() {
  totalsettlcurramt_ = 0;
}
inline double OutrightRepoDeal::totalsettlcurramt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt)
  return totalsettlcurramt_;
}
inline void OutrightRepoDeal::set_totalsettlcurramt(double value) {
  
  totalsettlcurramt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt)
}

// optional double TotalSettlCurrAmt2 = 7;
inline void OutrightRepoDeal::clear_totalsettlcurramt2() {
  totalsettlcurramt2_ = 0;
}
inline double OutrightRepoDeal::totalsettlcurramt2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt2)
  return totalsettlcurramt2_;
}
inline void OutrightRepoDeal::set_totalsettlcurramt2(double value) {
  
  totalsettlcurramt2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OutrightRepoDeal.TotalSettlCurrAmt2)
}

// repeated .com.htsc.mdc.insight.model.UnderlyingSecurityDetail SecurityDetailList = 10;
inline int OutrightRepoDeal::securitydetaillist_size() const {
  return securitydetaillist_.size();
}
inline void OutrightRepoDeal::clear_securitydetaillist() {
  securitydetaillist_.Clear();
}
inline const ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail& OutrightRepoDeal::securitydetaillist(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Get(index);
}
inline ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* OutrightRepoDeal::mutable_securitydetaillist(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail* OutrightRepoDeal::add_securitydetaillist() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >*
OutrightRepoDeal::mutable_securitydetaillist() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return &securitydetaillist_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::UnderlyingSecurityDetail >&
OutrightRepoDeal::securitydetaillist() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.OutrightRepoDeal.SecurityDetailList)
  return securitydetaillist_;
}

inline const OutrightRepoDeal* OutrightRepoDeal::internal_default_instance() {
  return &OutrightRepoDeal_default_instance_.get();
}
// -------------------------------------------------------------------

// UnderlyingSecurityDetail

// optional string UnderlyingSecurityID = 1;
inline void UnderlyingSecurityDetail::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UnderlyingSecurityDetail::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UnderlyingSecurityDetail::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
inline void UnderlyingSecurityDetail::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
inline void UnderlyingSecurityDetail::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}
inline ::std::string* UnderlyingSecurityDetail::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UnderlyingSecurityDetail::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UnderlyingSecurityDetail::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSecurityID)
}

// optional string UnderlyingSymbol = 2;
inline void UnderlyingSecurityDetail::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& UnderlyingSecurityDetail::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UnderlyingSecurityDetail::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
inline void UnderlyingSecurityDetail::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
inline void UnderlyingSecurityDetail::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}
inline ::std::string* UnderlyingSecurityDetail::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* UnderlyingSecurityDetail::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void UnderlyingSecurityDetail::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSymbol)
}

// optional double UnderlyingSettlCurrAmt = 3;
inline void UnderlyingSecurityDetail::clear_underlyingsettlcurramt() {
  underlyingsettlcurramt_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingsettlcurramt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt)
  return underlyingsettlcurramt_;
}
inline void UnderlyingSecurityDetail::set_underlyingsettlcurramt(double value) {
  
  underlyingsettlcurramt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt)
}

// optional double UnderlyingSettlCurrAmt2 = 4;
inline void UnderlyingSecurityDetail::clear_underlyingsettlcurramt2() {
  underlyingsettlcurramt2_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingsettlcurramt2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt2)
  return underlyingsettlcurramt2_;
}
inline void UnderlyingSecurityDetail::set_underlyingsettlcurramt2(double value) {
  
  underlyingsettlcurramt2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingSettlCurrAmt2)
}

// optional double UnderlyingQty = 5;
inline void UnderlyingSecurityDetail::clear_underlyingqty() {
  underlyingqty_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingQty)
  return underlyingqty_;
}
inline void UnderlyingSecurityDetail::set_underlyingqty(double value) {
  
  underlyingqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingQty)
}

// optional double UnderlyingPx = 6;
inline void UnderlyingSecurityDetail::clear_underlyingpx() {
  underlyingpx_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx)
  return underlyingpx_;
}
inline void UnderlyingSecurityDetail::set_underlyingpx(double value) {
  
  underlyingpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx)
}

// optional double UnderlyingPx2 = 7;
inline void UnderlyingSecurityDetail::clear_underlyingpx2() {
  underlyingpx2_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx2)
  return underlyingpx2_;
}
inline void UnderlyingSecurityDetail::set_underlyingpx2(double value) {
  
  underlyingpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingPx2)
}

// optional double UnderlyingYield = 8;
inline void UnderlyingSecurityDetail::clear_underlyingyield() {
  underlyingyield_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield)
  return underlyingyield_;
}
inline void UnderlyingSecurityDetail::set_underlyingyield(double value) {
  
  underlyingyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield)
}

// optional double UnderlyingYield2 = 9;
inline void UnderlyingSecurityDetail::clear_underlyingyield2() {
  underlyingyield2_ = 0;
}
inline double UnderlyingSecurityDetail::underlyingyield2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield2)
  return underlyingyield2_;
}
inline void UnderlyingSecurityDetail::set_underlyingyield2(double value) {
  
  underlyingyield2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.UnderlyingSecurityDetail.UnderlyingYield2)
}

inline const UnderlyingSecurityDetail* UnderlyingSecurityDetail::internal_default_instance() {
  return &UnderlyingSecurityDetail_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsCurrencyDeal_2eproto__INCLUDED
