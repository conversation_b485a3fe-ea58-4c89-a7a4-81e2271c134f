syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDHKGreyMarket {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  double IndicativePrice = 10;
  double IndicativeVolume = 11;
  double BidPrice = 12;
  double AskPrice = 13;
  double BidSize = 14;
  double AskSize = 15;
  string GreyMarketStatus = 16;
  int64 MessageNumber = 100;
}
