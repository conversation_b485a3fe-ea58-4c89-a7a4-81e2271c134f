// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDOrder.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDOrder.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDOrder_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDOrder_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDOrder_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDOrder_2eproto() {
  protobuf_AddDesc_MDOrder_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDOrder.proto");
  GOOGLE_CHECK(file != NULL);
  MDOrder_descriptor_ = file->message_type(0);
  static const int MDOrder_offsets_[35] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, orderindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, ordertype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, orderprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, orderbsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, orderno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, securitystatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, quoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, memberid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, investortype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, investorid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, investorname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, tradercode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, settlperiod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, settltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, memo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, secondaryorderid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, bidtranstype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, bidexecinsttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, lowlimitprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, highlimitprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, minqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, tradedqty_),
  };
  MDOrder_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDOrder_descriptor_,
      MDOrder::internal_default_instance(),
      MDOrder_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDOrder),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOrder, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDOrder_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDOrder_descriptor_, MDOrder::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDOrder_2eproto() {
  MDOrder_default_instance_.Shutdown();
  delete MDOrder_reflection_;
}

void protobuf_InitDefaults_MDOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDOrder_default_instance_.DefaultConstruct();
  MDOrder_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDOrder_2eproto_once_);
void protobuf_InitDefaults_MDOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDOrder_2eproto_once_,
                 &protobuf_InitDefaults_MDOrder_2eproto_impl);
}
void protobuf_AddDesc_MDOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDOrder_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rMDOrder.proto\022\032com.htsc.mdc.insight.mo"
    "del\032\027ESecurityIDSource.proto\032\023ESecurityT"
    "ype.proto\"\252\006\n\007MDOrder\022\026\n\016HTSCSecurityID\030"
    "\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n"
    "\rDataTimestamp\030\004 \001(\003\022\?\n\020securityIDSource"
    "\030\005 \001(\0162%.com.htsc.mdc.model.ESecurityIDS"
    "ource\0227\n\014securityType\030\006 \001(\0162!.com.htsc.m"
    "dc.model.ESecurityType\022\022\n\nOrderIndex\030\007 \001"
    "(\003\022\021\n\tOrderType\030\010 \001(\005\022\022\n\nOrderPrice\030\t \001("
    "\003\022\020\n\010OrderQty\030\n \001(\003\022\023\n\013OrderBSFlag\030\013 \001(\005"
    "\022\021\n\tChannelNo\030\014 \001(\005\022\024\n\014ExchangeDate\030\r \001("
    "\005\022\024\n\014ExchangeTime\030\016 \001(\005\022\017\n\007OrderNO\030\017 \001(\003"
    "\022\022\n\nApplSeqNum\030\020 \001(\003\022\026\n\016SecurityStatus\030\021"
    " \001(\t\022\017\n\007QuoteID\030\022 \001(\t\022\020\n\010MemberID\030\023 \001(\t\022"
    "\024\n\014InvestorType\030\024 \001(\t\022\022\n\nInvestorID\030\025 \001("
    "\t\022\024\n\014InvestorName\030\026 \001(\t\022\022\n\nTraderCode\030\027 "
    "\001(\t\022\023\n\013SettlPeriod\030\030 \001(\005\022\021\n\tSettlType\030\031 "
    "\001(\005\022\014\n\004Memo\030\032 \001(\t\022\035\n\025DataMultiplePowerOf"
    "10\030\033 \001(\005\022\030\n\020SecondaryOrderID\030\034 \001(\t\022\024\n\014Bi"
    "dTransType\030\035 \001(\005\022\027\n\017BidExecInstType\030\036 \001("
    "\005\022\025\n\rLowLimitPrice\030\037 \001(\003\022\026\n\016HighLimitPri"
    "ce\030  \001(\003\022\016\n\006MinQty\030! \001(\003\022\021\n\tTradeDate\030\" "
    "\001(\t\022\021\n\tTradedQty\030# \001(\003B0\n\032com.htsc.mdc.i"
    "nsight.modelB\rMDOrderProtosH\001\240\001\001b\006proto3", 960);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDOrder.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDOrder_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDOrder_2eproto_once_);
void protobuf_AddDesc_MDOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDOrder_2eproto_once_,
                 &protobuf_AddDesc_MDOrder_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDOrder_2eproto {
  StaticDescriptorInitializer_MDOrder_2eproto() {
    protobuf_AddDesc_MDOrder_2eproto();
  }
} static_descriptor_initializer_MDOrder_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDOrder::kHTSCSecurityIDFieldNumber;
const int MDOrder::kMDDateFieldNumber;
const int MDOrder::kMDTimeFieldNumber;
const int MDOrder::kDataTimestampFieldNumber;
const int MDOrder::kSecurityIDSourceFieldNumber;
const int MDOrder::kSecurityTypeFieldNumber;
const int MDOrder::kOrderIndexFieldNumber;
const int MDOrder::kOrderTypeFieldNumber;
const int MDOrder::kOrderPriceFieldNumber;
const int MDOrder::kOrderQtyFieldNumber;
const int MDOrder::kOrderBSFlagFieldNumber;
const int MDOrder::kChannelNoFieldNumber;
const int MDOrder::kExchangeDateFieldNumber;
const int MDOrder::kExchangeTimeFieldNumber;
const int MDOrder::kOrderNOFieldNumber;
const int MDOrder::kApplSeqNumFieldNumber;
const int MDOrder::kSecurityStatusFieldNumber;
const int MDOrder::kQuoteIDFieldNumber;
const int MDOrder::kMemberIDFieldNumber;
const int MDOrder::kInvestorTypeFieldNumber;
const int MDOrder::kInvestorIDFieldNumber;
const int MDOrder::kInvestorNameFieldNumber;
const int MDOrder::kTraderCodeFieldNumber;
const int MDOrder::kSettlPeriodFieldNumber;
const int MDOrder::kSettlTypeFieldNumber;
const int MDOrder::kMemoFieldNumber;
const int MDOrder::kDataMultiplePowerOf10FieldNumber;
const int MDOrder::kSecondaryOrderIDFieldNumber;
const int MDOrder::kBidTransTypeFieldNumber;
const int MDOrder::kBidExecInstTypeFieldNumber;
const int MDOrder::kLowLimitPriceFieldNumber;
const int MDOrder::kHighLimitPriceFieldNumber;
const int MDOrder::kMinQtyFieldNumber;
const int MDOrder::kTradeDateFieldNumber;
const int MDOrder::kTradedQtyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDOrder::MDOrder()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDOrder_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDOrder)
}

void MDOrder::InitAsDefaultInstance() {
}

MDOrder::MDOrder(const MDOrder& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDOrder)
}

void MDOrder::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitystatus_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memberid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investortype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memo_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secondaryorderid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&tradedqty_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(tradedqty_));
  _cached_size_ = 0;
}

MDOrder::~MDOrder() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDOrder)
  SharedDtor();
}

void MDOrder::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitystatus_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memberid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investortype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memo_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secondaryorderid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDOrder::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDOrder::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDOrder_descriptor_;
}

const MDOrder& MDOrder::default_instance() {
  protobuf_InitDefaults_MDOrder_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDOrder> MDOrder_default_instance_;

MDOrder* MDOrder::New(::google::protobuf::Arena* arena) const {
  MDOrder* n = new MDOrder;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDOrder::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDOrder)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDOrder, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDOrder*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, orderindex_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ordertype_ = 0;
  ZR_(orderbsflag_, exchangetime_);
  orderprice_ = GOOGLE_LONGLONG(0);
  securitystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  memberid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investortype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  investorname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settlperiod_ = 0;
  ZR_(settltype_, highlimitprice_);
  memo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minqty_, tradedqty_);
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDOrder::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDOrder)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_OrderIndex;
        break;
      }

      // optional int64 OrderIndex = 7;
      case 7: {
        if (tag == 56) {
         parse_OrderIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_OrderType;
        break;
      }

      // optional int32 OrderType = 8;
      case 8: {
        if (tag == 64) {
         parse_OrderType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ordertype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_OrderPrice;
        break;
      }

      // optional int64 OrderPrice = 9;
      case 9: {
        if (tag == 72) {
         parse_OrderPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_OrderQty;
        break;
      }

      // optional int64 OrderQty = 10;
      case 10: {
        if (tag == 80) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_OrderBSFlag;
        break;
      }

      // optional int32 OrderBSFlag = 11;
      case 11: {
        if (tag == 88) {
         parse_OrderBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &orderbsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 12;
      case 12: {
        if (tag == 96) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 13;
      case 13: {
        if (tag == 104) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 14;
      case 14: {
        if (tag == 112) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OrderNO;
        break;
      }

      // optional int64 OrderNO = 15;
      case 15: {
        if (tag == 120) {
         parse_OrderNO:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 16;
      case 16: {
        if (tag == 128) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_SecurityStatus;
        break;
      }

      // optional string SecurityStatus = 17;
      case 17: {
        if (tag == 138) {
         parse_SecurityStatus:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitystatus()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitystatus().data(), this->securitystatus().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.SecurityStatus"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_QuoteID;
        break;
      }

      // optional string QuoteID = 18;
      case 18: {
        if (tag == 146) {
         parse_QuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoteid().data(), this->quoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.QuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_MemberID;
        break;
      }

      // optional string MemberID = 19;
      case 19: {
        if (tag == 154) {
         parse_MemberID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_memberid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->memberid().data(), this->memberid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.MemberID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_InvestorType;
        break;
      }

      // optional string InvestorType = 20;
      case 20: {
        if (tag == 162) {
         parse_InvestorType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_investortype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->investortype().data(), this->investortype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.InvestorType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_InvestorID;
        break;
      }

      // optional string InvestorID = 21;
      case 21: {
        if (tag == 170) {
         parse_InvestorID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_investorid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->investorid().data(), this->investorid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.InvestorID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_InvestorName;
        break;
      }

      // optional string InvestorName = 22;
      case 22: {
        if (tag == 178) {
         parse_InvestorName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_investorname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->investorname().data(), this->investorname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.InvestorName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_TraderCode;
        break;
      }

      // optional string TraderCode = 23;
      case 23: {
        if (tag == 186) {
         parse_TraderCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradercode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradercode().data(), this->tradercode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.TraderCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_SettlPeriod;
        break;
      }

      // optional int32 SettlPeriod = 24;
      case 24: {
        if (tag == 192) {
         parse_SettlPeriod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settlperiod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_SettlType;
        break;
      }

      // optional int32 SettlType = 25;
      case 25: {
        if (tag == 200) {
         parse_SettlType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settltype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_Memo;
        break;
      }

      // optional string Memo = 26;
      case 26: {
        if (tag == 210) {
         parse_Memo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_memo()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->memo().data(), this->memo().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.Memo"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 27;
      case 27: {
        if (tag == 216) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_SecondaryOrderID;
        break;
      }

      // optional string SecondaryOrderID = 28;
      case 28: {
        if (tag == 226) {
         parse_SecondaryOrderID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_secondaryorderid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->secondaryorderid().data(), this->secondaryorderid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_BidTransType;
        break;
      }

      // optional int32 BidTransType = 29;
      case 29: {
        if (tag == 232) {
         parse_BidTransType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidtranstype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_BidExecInstType;
        break;
      }

      // optional int32 BidExecInstType = 30;
      case 30: {
        if (tag == 240) {
         parse_BidExecInstType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidexecinsttype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_LowLimitPrice;
        break;
      }

      // optional int64 LowLimitPrice = 31;
      case 31: {
        if (tag == 248) {
         parse_LowLimitPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowlimitprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_HighLimitPrice;
        break;
      }

      // optional int64 HighLimitPrice = 32;
      case 32: {
        if (tag == 256) {
         parse_HighLimitPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highlimitprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_MinQty;
        break;
      }

      // optional int64 MinQty = 33;
      case 33: {
        if (tag == 264) {
         parse_MinQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 34;
      case 34: {
        if (tag == 274) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOrder.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_TradedQty;
        break;
      }

      // optional int64 TradedQty = 35;
      case 35: {
        if (tag == 280) {
         parse_TradedQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradedqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDOrder)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDOrder)
  return false;
#undef DO_
}

void MDOrder::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int64 OrderIndex = 7;
  if (this->orderindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->orderindex(), output);
  }

  // optional int32 OrderType = 8;
  if (this->ordertype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->ordertype(), output);
  }

  // optional int64 OrderPrice = 9;
  if (this->orderprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->orderprice(), output);
  }

  // optional int64 OrderQty = 10;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->orderqty(), output);
  }

  // optional int32 OrderBSFlag = 11;
  if (this->orderbsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->orderbsflag(), output);
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->channelno(), output);
  }

  // optional int32 ExchangeDate = 13;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 14;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->exchangetime(), output);
  }

  // optional int64 OrderNO = 15;
  if (this->orderno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->orderno(), output);
  }

  // optional int64 ApplSeqNum = 16;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->applseqnum(), output);
  }

  // optional string SecurityStatus = 17;
  if (this->securitystatus().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitystatus().data(), this->securitystatus().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.SecurityStatus");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->securitystatus(), output);
  }

  // optional string QuoteID = 18;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.QuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->quoteid(), output);
  }

  // optional string MemberID = 19;
  if (this->memberid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memberid().data(), this->memberid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.MemberID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->memberid(), output);
  }

  // optional string InvestorType = 20;
  if (this->investortype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investortype().data(), this->investortype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->investortype(), output);
  }

  // optional string InvestorID = 21;
  if (this->investorid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investorid().data(), this->investorid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->investorid(), output);
  }

  // optional string InvestorName = 22;
  if (this->investorname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investorname().data(), this->investorname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      22, this->investorname(), output);
  }

  // optional string TraderCode = 23;
  if (this->tradercode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradercode().data(), this->tradercode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.TraderCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->tradercode(), output);
  }

  // optional int32 SettlPeriod = 24;
  if (this->settlperiod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(24, this->settlperiod(), output);
  }

  // optional int32 SettlType = 25;
  if (this->settltype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->settltype(), output);
  }

  // optional string Memo = 26;
  if (this->memo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memo().data(), this->memo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.Memo");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      26, this->memo(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(27, this->datamultiplepowerof10(), output);
  }

  // optional string SecondaryOrderID = 28;
  if (this->secondaryorderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secondaryorderid().data(), this->secondaryorderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      28, this->secondaryorderid(), output);
  }

  // optional int32 BidTransType = 29;
  if (this->bidtranstype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(29, this->bidtranstype(), output);
  }

  // optional int32 BidExecInstType = 30;
  if (this->bidexecinsttype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->bidexecinsttype(), output);
  }

  // optional int64 LowLimitPrice = 31;
  if (this->lowlimitprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->lowlimitprice(), output);
  }

  // optional int64 HighLimitPrice = 32;
  if (this->highlimitprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->highlimitprice(), output);
  }

  // optional int64 MinQty = 33;
  if (this->minqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->minqty(), output);
  }

  // optional string TradeDate = 34;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      34, this->tradedate(), output);
  }

  // optional int64 TradedQty = 35;
  if (this->tradedqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->tradedqty(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDOrder)
}

::google::protobuf::uint8* MDOrder::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int64 OrderIndex = 7;
  if (this->orderindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->orderindex(), target);
  }

  // optional int32 OrderType = 8;
  if (this->ordertype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->ordertype(), target);
  }

  // optional int64 OrderPrice = 9;
  if (this->orderprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->orderprice(), target);
  }

  // optional int64 OrderQty = 10;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->orderqty(), target);
  }

  // optional int32 OrderBSFlag = 11;
  if (this->orderbsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->orderbsflag(), target);
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->channelno(), target);
  }

  // optional int32 ExchangeDate = 13;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 14;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->exchangetime(), target);
  }

  // optional int64 OrderNO = 15;
  if (this->orderno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->orderno(), target);
  }

  // optional int64 ApplSeqNum = 16;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->applseqnum(), target);
  }

  // optional string SecurityStatus = 17;
  if (this->securitystatus().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitystatus().data(), this->securitystatus().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.SecurityStatus");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->securitystatus(), target);
  }

  // optional string QuoteID = 18;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.QuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->quoteid(), target);
  }

  // optional string MemberID = 19;
  if (this->memberid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memberid().data(), this->memberid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.MemberID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->memberid(), target);
  }

  // optional string InvestorType = 20;
  if (this->investortype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investortype().data(), this->investortype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->investortype(), target);
  }

  // optional string InvestorID = 21;
  if (this->investorid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investorid().data(), this->investorid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->investorid(), target);
  }

  // optional string InvestorName = 22;
  if (this->investorname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->investorname().data(), this->investorname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.InvestorName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        22, this->investorname(), target);
  }

  // optional string TraderCode = 23;
  if (this->tradercode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradercode().data(), this->tradercode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.TraderCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->tradercode(), target);
  }

  // optional int32 SettlPeriod = 24;
  if (this->settlperiod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(24, this->settlperiod(), target);
  }

  // optional int32 SettlType = 25;
  if (this->settltype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->settltype(), target);
  }

  // optional string Memo = 26;
  if (this->memo().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->memo().data(), this->memo().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.Memo");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        26, this->memo(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(27, this->datamultiplepowerof10(), target);
  }

  // optional string SecondaryOrderID = 28;
  if (this->secondaryorderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secondaryorderid().data(), this->secondaryorderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        28, this->secondaryorderid(), target);
  }

  // optional int32 BidTransType = 29;
  if (this->bidtranstype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(29, this->bidtranstype(), target);
  }

  // optional int32 BidExecInstType = 30;
  if (this->bidexecinsttype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->bidexecinsttype(), target);
  }

  // optional int64 LowLimitPrice = 31;
  if (this->lowlimitprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->lowlimitprice(), target);
  }

  // optional int64 HighLimitPrice = 32;
  if (this->highlimitprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->highlimitprice(), target);
  }

  // optional int64 MinQty = 33;
  if (this->minqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->minqty(), target);
  }

  // optional string TradeDate = 34;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOrder.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        34, this->tradedate(), target);
  }

  // optional int64 TradedQty = 35;
  if (this->tradedqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->tradedqty(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDOrder)
  return target;
}

size_t MDOrder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDOrder)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 OrderIndex = 7;
  if (this->orderindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderindex());
  }

  // optional int32 OrderType = 8;
  if (this->ordertype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ordertype());
  }

  // optional int64 OrderPrice = 9;
  if (this->orderprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderprice());
  }

  // optional int64 OrderQty = 10;
  if (this->orderqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderqty());
  }

  // optional int32 OrderBSFlag = 11;
  if (this->orderbsflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->orderbsflag());
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 ExchangeDate = 13;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 14;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 OrderNO = 15;
  if (this->orderno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderno());
  }

  // optional int64 ApplSeqNum = 16;
  if (this->applseqnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional string SecurityStatus = 17;
  if (this->securitystatus().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitystatus());
  }

  // optional string QuoteID = 18;
  if (this->quoteid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoteid());
  }

  // optional string MemberID = 19;
  if (this->memberid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->memberid());
  }

  // optional string InvestorType = 20;
  if (this->investortype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->investortype());
  }

  // optional string InvestorID = 21;
  if (this->investorid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->investorid());
  }

  // optional string InvestorName = 22;
  if (this->investorname().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->investorname());
  }

  // optional string TraderCode = 23;
  if (this->tradercode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradercode());
  }

  // optional int32 SettlPeriod = 24;
  if (this->settlperiod() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settlperiod());
  }

  // optional int32 SettlType = 25;
  if (this->settltype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settltype());
  }

  // optional string Memo = 26;
  if (this->memo().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->memo());
  }

  // optional int32 DataMultiplePowerOf10 = 27;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string SecondaryOrderID = 28;
  if (this->secondaryorderid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->secondaryorderid());
  }

  // optional int32 BidTransType = 29;
  if (this->bidtranstype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidtranstype());
  }

  // optional int32 BidExecInstType = 30;
  if (this->bidexecinsttype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidexecinsttype());
  }

  // optional int64 LowLimitPrice = 31;
  if (this->lowlimitprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowlimitprice());
  }

  // optional int64 HighLimitPrice = 32;
  if (this->highlimitprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highlimitprice());
  }

  // optional int64 MinQty = 33;
  if (this->minqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minqty());
  }

  // optional string TradeDate = 34;
  if (this->tradedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional int64 TradedQty = 35;
  if (this->tradedqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradedqty());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDOrder::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDOrder)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDOrder* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDOrder>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDOrder)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDOrder)
    UnsafeMergeFrom(*source);
  }
}

void MDOrder::MergeFrom(const MDOrder& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDOrder)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDOrder::UnsafeMergeFrom(const MDOrder& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.orderindex() != 0) {
    set_orderindex(from.orderindex());
  }
  if (from.ordertype() != 0) {
    set_ordertype(from.ordertype());
  }
  if (from.orderprice() != 0) {
    set_orderprice(from.orderprice());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.orderbsflag() != 0) {
    set_orderbsflag(from.orderbsflag());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.orderno() != 0) {
    set_orderno(from.orderno());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.securitystatus().size() > 0) {

    securitystatus_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitystatus_);
  }
  if (from.quoteid().size() > 0) {

    quoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoteid_);
  }
  if (from.memberid().size() > 0) {

    memberid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.memberid_);
  }
  if (from.investortype().size() > 0) {

    investortype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.investortype_);
  }
  if (from.investorid().size() > 0) {

    investorid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.investorid_);
  }
  if (from.investorname().size() > 0) {

    investorname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.investorname_);
  }
  if (from.tradercode().size() > 0) {

    tradercode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradercode_);
  }
  if (from.settlperiod() != 0) {
    set_settlperiod(from.settlperiod());
  }
  if (from.settltype() != 0) {
    set_settltype(from.settltype());
  }
  if (from.memo().size() > 0) {

    memo_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.memo_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.secondaryorderid().size() > 0) {

    secondaryorderid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.secondaryorderid_);
  }
  if (from.bidtranstype() != 0) {
    set_bidtranstype(from.bidtranstype());
  }
  if (from.bidexecinsttype() != 0) {
    set_bidexecinsttype(from.bidexecinsttype());
  }
  if (from.lowlimitprice() != 0) {
    set_lowlimitprice(from.lowlimitprice());
  }
  if (from.highlimitprice() != 0) {
    set_highlimitprice(from.highlimitprice());
  }
  if (from.minqty() != 0) {
    set_minqty(from.minqty());
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradedqty() != 0) {
    set_tradedqty(from.tradedqty());
  }
}

void MDOrder::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDOrder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDOrder::CopyFrom(const MDOrder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDOrder)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDOrder::IsInitialized() const {

  return true;
}

void MDOrder::Swap(MDOrder* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDOrder::InternalSwap(MDOrder* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(orderindex_, other->orderindex_);
  std::swap(ordertype_, other->ordertype_);
  std::swap(orderprice_, other->orderprice_);
  std::swap(orderqty_, other->orderqty_);
  std::swap(orderbsflag_, other->orderbsflag_);
  std::swap(channelno_, other->channelno_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(orderno_, other->orderno_);
  std::swap(applseqnum_, other->applseqnum_);
  securitystatus_.Swap(&other->securitystatus_);
  quoteid_.Swap(&other->quoteid_);
  memberid_.Swap(&other->memberid_);
  investortype_.Swap(&other->investortype_);
  investorid_.Swap(&other->investorid_);
  investorname_.Swap(&other->investorname_);
  tradercode_.Swap(&other->tradercode_);
  std::swap(settlperiod_, other->settlperiod_);
  std::swap(settltype_, other->settltype_);
  memo_.Swap(&other->memo_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  secondaryorderid_.Swap(&other->secondaryorderid_);
  std::swap(bidtranstype_, other->bidtranstype_);
  std::swap(bidexecinsttype_, other->bidexecinsttype_);
  std::swap(lowlimitprice_, other->lowlimitprice_);
  std::swap(highlimitprice_, other->highlimitprice_);
  std::swap(minqty_, other->minqty_);
  tradedate_.Swap(&other->tradedate_);
  std::swap(tradedqty_, other->tradedqty_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDOrder::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDOrder_descriptor_;
  metadata.reflection = MDOrder_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDOrder

// optional string HTSCSecurityID = 1;
void MDOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
void MDOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
void MDOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
::std::string* MDOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDOrder::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MDDate)
  return mddate_;
}
void MDOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MDDate)
}

// optional int32 MDTime = 3;
void MDOrder::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MDTime)
  return mdtime_;
}
void MDOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.DataTimestamp)
  return datatimestamp_;
}
void MDOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDOrder::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.securityType)
}

// optional int64 OrderIndex = 7;
void MDOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderIndex)
  return orderindex_;
}
void MDOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderIndex)
}

// optional int32 OrderType = 8;
void MDOrder::clear_ordertype() {
  ordertype_ = 0;
}
::google::protobuf::int32 MDOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderType)
  return ordertype_;
}
void MDOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderType)
}

// optional int64 OrderPrice = 9;
void MDOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderPrice)
  return orderprice_;
}
void MDOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderPrice)
}

// optional int64 OrderQty = 10;
void MDOrder::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderQty)
  return orderqty_;
}
void MDOrder::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderQty)
}

// optional int32 OrderBSFlag = 11;
void MDOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
::google::protobuf::int32 MDOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderBSFlag)
  return orderbsflag_;
}
void MDOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderBSFlag)
}

// optional int32 ChannelNo = 12;
void MDOrder::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDOrder::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ChannelNo)
  return channelno_;
}
void MDOrder::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ChannelNo)
}

// optional int32 ExchangeDate = 13;
void MDOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ExchangeDate)
  return exchangedate_;
}
void MDOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 14;
void MDOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ExchangeTime)
  return exchangetime_;
}
void MDOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ExchangeTime)
}

// optional int64 OrderNO = 15;
void MDOrder::clear_orderno() {
  orderno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::orderno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderNO)
  return orderno_;
}
void MDOrder::set_orderno(::google::protobuf::int64 value) {
  
  orderno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderNO)
}

// optional int64 ApplSeqNum = 16;
void MDOrder::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ApplSeqNum)
  return applseqnum_;
}
void MDOrder::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ApplSeqNum)
}

// optional string SecurityStatus = 17;
void MDOrder::clear_securitystatus() {
  securitystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::securitystatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  return securitystatus_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_securitystatus(const ::std::string& value) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
void MDOrder::set_securitystatus(const char* value) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
void MDOrder::set_securitystatus(const char* value, size_t size) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
::std::string* MDOrder::mutable_securitystatus() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  return securitystatus_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_securitystatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  
  return securitystatus_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_securitystatus(::std::string* securitystatus) {
  if (securitystatus != NULL) {
    
  } else {
    
  }
  securitystatus_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitystatus);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}

// optional string QuoteID = 18;
void MDOrder::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
void MDOrder::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
void MDOrder::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
::std::string* MDOrder::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}

// optional string MemberID = 19;
void MDOrder::clear_memberid() {
  memberid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::memberid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MemberID)
  return memberid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_memberid(const ::std::string& value) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
void MDOrder::set_memberid(const char* value) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
void MDOrder::set_memberid(const char* value, size_t size) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
::std::string* MDOrder::mutable_memberid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.MemberID)
  return memberid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_memberid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.MemberID)
  
  return memberid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_memberid(::std::string* memberid) {
  if (memberid != NULL) {
    
  } else {
    
  }
  memberid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), memberid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.MemberID)
}

// optional string InvestorType = 20;
void MDOrder::clear_investortype() {
  investortype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::investortype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  return investortype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_investortype(const ::std::string& value) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
void MDOrder::set_investortype(const char* value) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
void MDOrder::set_investortype(const char* value, size_t size) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
::std::string* MDOrder::mutable_investortype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  return investortype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_investortype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  
  return investortype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_investortype(::std::string* investortype) {
  if (investortype != NULL) {
    
  } else {
    
  }
  investortype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investortype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}

// optional string InvestorID = 21;
void MDOrder::clear_investorid() {
  investorid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::investorid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  return investorid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_investorid(const ::std::string& value) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
void MDOrder::set_investorid(const char* value) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
void MDOrder::set_investorid(const char* value, size_t size) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
::std::string* MDOrder::mutable_investorid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  return investorid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_investorid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  
  return investorid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_investorid(::std::string* investorid) {
  if (investorid != NULL) {
    
  } else {
    
  }
  investorid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investorid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}

// optional string InvestorName = 22;
void MDOrder::clear_investorname() {
  investorname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::investorname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  return investorname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_investorname(const ::std::string& value) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
void MDOrder::set_investorname(const char* value) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
void MDOrder::set_investorname(const char* value, size_t size) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
::std::string* MDOrder::mutable_investorname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  return investorname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_investorname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  
  return investorname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_investorname(::std::string* investorname) {
  if (investorname != NULL) {
    
  } else {
    
  }
  investorname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investorname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}

// optional string TraderCode = 23;
void MDOrder::clear_tradercode() {
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::tradercode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  return tradercode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_tradercode(const ::std::string& value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
void MDOrder::set_tradercode(const char* value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
void MDOrder::set_tradercode(const char* value, size_t size) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
::std::string* MDOrder::mutable_tradercode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  return tradercode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_tradercode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  
  return tradercode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_tradercode(::std::string* tradercode) {
  if (tradercode != NULL) {
    
  } else {
    
  }
  tradercode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradercode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}

// optional int32 SettlPeriod = 24;
void MDOrder::clear_settlperiod() {
  settlperiod_ = 0;
}
::google::protobuf::int32 MDOrder::settlperiod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SettlPeriod)
  return settlperiod_;
}
void MDOrder::set_settlperiod(::google::protobuf::int32 value) {
  
  settlperiod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SettlPeriod)
}

// optional int32 SettlType = 25;
void MDOrder::clear_settltype() {
  settltype_ = 0;
}
::google::protobuf::int32 MDOrder::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SettlType)
  return settltype_;
}
void MDOrder::set_settltype(::google::protobuf::int32 value) {
  
  settltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SettlType)
}

// optional string Memo = 26;
void MDOrder::clear_memo() {
  memo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::memo() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.Memo)
  return memo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_memo(const ::std::string& value) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.Memo)
}
void MDOrder::set_memo(const char* value) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.Memo)
}
void MDOrder::set_memo(const char* value, size_t size) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.Memo)
}
::std::string* MDOrder::mutable_memo() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.Memo)
  return memo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_memo() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.Memo)
  
  return memo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_memo(::std::string* memo) {
  if (memo != NULL) {
    
  } else {
    
  }
  memo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), memo);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.Memo)
}

// optional int32 DataMultiplePowerOf10 = 27;
void MDOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.DataMultiplePowerOf10)
}

// optional string SecondaryOrderID = 28;
void MDOrder::clear_secondaryorderid() {
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::secondaryorderid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  return secondaryorderid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_secondaryorderid(const ::std::string& value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
void MDOrder::set_secondaryorderid(const char* value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
void MDOrder::set_secondaryorderid(const char* value, size_t size) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
::std::string* MDOrder::mutable_secondaryorderid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  return secondaryorderid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_secondaryorderid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  
  return secondaryorderid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_secondaryorderid(::std::string* secondaryorderid) {
  if (secondaryorderid != NULL) {
    
  } else {
    
  }
  secondaryorderid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secondaryorderid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}

// optional int32 BidTransType = 29;
void MDOrder::clear_bidtranstype() {
  bidtranstype_ = 0;
}
::google::protobuf::int32 MDOrder::bidtranstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.BidTransType)
  return bidtranstype_;
}
void MDOrder::set_bidtranstype(::google::protobuf::int32 value) {
  
  bidtranstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.BidTransType)
}

// optional int32 BidExecInstType = 30;
void MDOrder::clear_bidexecinsttype() {
  bidexecinsttype_ = 0;
}
::google::protobuf::int32 MDOrder::bidexecinsttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.BidExecInstType)
  return bidexecinsttype_;
}
void MDOrder::set_bidexecinsttype(::google::protobuf::int32 value) {
  
  bidexecinsttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.BidExecInstType)
}

// optional int64 LowLimitPrice = 31;
void MDOrder::clear_lowlimitprice() {
  lowlimitprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::lowlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.LowLimitPrice)
  return lowlimitprice_;
}
void MDOrder::set_lowlimitprice(::google::protobuf::int64 value) {
  
  lowlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.LowLimitPrice)
}

// optional int64 HighLimitPrice = 32;
void MDOrder::clear_highlimitprice() {
  highlimitprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::highlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.HighLimitPrice)
  return highlimitprice_;
}
void MDOrder::set_highlimitprice(::google::protobuf::int64 value) {
  
  highlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.HighLimitPrice)
}

// optional int64 MinQty = 33;
void MDOrder::clear_minqty() {
  minqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::minqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MinQty)
  return minqty_;
}
void MDOrder::set_minqty(::google::protobuf::int64 value) {
  
  minqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MinQty)
}

// optional string TradeDate = 34;
void MDOrder::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOrder::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
void MDOrder::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
void MDOrder::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
::std::string* MDOrder::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOrder::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOrder::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}

// optional int64 TradedQty = 35;
void MDOrder::clear_tradedqty() {
  tradedqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOrder::tradedqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TradedQty)
  return tradedqty_;
}
void MDOrder::set_tradedqty(::google::protobuf::int64 value) {
  
  tradedqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TradedQty)
}

inline const MDOrder* MDOrder::internal_default_instance() {
  return &MDOrder_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
