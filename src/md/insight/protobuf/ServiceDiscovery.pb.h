// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ServiceDiscovery.proto

#ifndef PROTOBUF_ServiceDiscovery_2eproto__INCLUDED
#define PROTOBUF_ServiceDiscovery_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "InsightErrorContext.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_ServiceDiscovery_2eproto();
void protobuf_InitDefaults_ServiceDiscovery_2eproto();
void protobuf_AssignDesc_ServiceDiscovery_2eproto();
void protobuf_ShutdownFile_ServiceDiscovery_2eproto();

class ServerInfo;
class ServiceDiscoveryRequest;
class ServiceDiscoveryResponse;

// ===================================================================

class ServiceDiscoveryRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ServiceDiscoveryRequest) */ {
 public:
  ServiceDiscoveryRequest();
  virtual ~ServiceDiscoveryRequest();

  ServiceDiscoveryRequest(const ServiceDiscoveryRequest& from);

  inline ServiceDiscoveryRequest& operator=(const ServiceDiscoveryRequest& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ServiceDiscoveryRequest& default_instance();

  static const ServiceDiscoveryRequest* internal_default_instance();

  void Swap(ServiceDiscoveryRequest* other);

  // implements Message ----------------------------------------------

  inline ServiceDiscoveryRequest* New() const { return New(NULL); }

  ServiceDiscoveryRequest* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ServiceDiscoveryRequest& from);
  void MergeFrom(const ServiceDiscoveryRequest& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ServiceDiscoveryRequest* other);
  void UnsafeMergeFrom(const ServiceDiscoveryRequest& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 appType = 1;
  void clear_apptype();
  static const int kAppTypeFieldNumber = 1;
  ::google::protobuf::int32 apptype() const;
  void set_apptype(::google::protobuf::int32 value);

  // optional string appVersion = 2;
  void clear_appversion();
  static const int kAppVersionFieldNumber = 2;
  const ::std::string& appversion() const;
  void set_appversion(const ::std::string& value);
  void set_appversion(const char* value);
  void set_appversion(const char* value, size_t size);
  ::std::string* mutable_appversion();
  ::std::string* release_appversion();
  void set_allocated_appversion(::std::string* appversion);

  // optional string userName = 3;
  void clear_username();
  static const int kUserNameFieldNumber = 3;
  const ::std::string& username() const;
  void set_username(const ::std::string& value);
  void set_username(const char* value);
  void set_username(const char* value, size_t size);
  ::std::string* mutable_username();
  ::std::string* release_username();
  void set_allocated_username(::std::string* username);

  // optional string deviceId = 4;
  void clear_deviceid();
  static const int kDeviceIdFieldNumber = 4;
  const ::std::string& deviceid() const;
  void set_deviceid(const ::std::string& value);
  void set_deviceid(const char* value);
  void set_deviceid(const char* value, size_t size);
  ::std::string* mutable_deviceid();
  ::std::string* release_deviceid();
  void set_allocated_deviceid(::std::string* deviceid);

  // optional bool isSupportCompressed = 5;
  void clear_issupportcompressed();
  static const int kIsSupportCompressedFieldNumber = 5;
  bool issupportcompressed() const;
  void set_issupportcompressed(bool value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr appversion_;
  ::google::protobuf::internal::ArenaStringPtr username_;
  ::google::protobuf::internal::ArenaStringPtr deviceid_;
  ::google::protobuf::int32 apptype_;
  bool issupportcompressed_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ServiceDiscovery_2eproto_impl();
  friend void  protobuf_AddDesc_ServiceDiscovery_2eproto_impl();
  friend void protobuf_AssignDesc_ServiceDiscovery_2eproto();
  friend void protobuf_ShutdownFile_ServiceDiscovery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ServiceDiscoveryRequest> ServiceDiscoveryRequest_default_instance_;

// -------------------------------------------------------------------

class ServiceDiscoveryResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ServiceDiscoveryResponse) */ {
 public:
  ServiceDiscoveryResponse();
  virtual ~ServiceDiscoveryResponse();

  ServiceDiscoveryResponse(const ServiceDiscoveryResponse& from);

  inline ServiceDiscoveryResponse& operator=(const ServiceDiscoveryResponse& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ServiceDiscoveryResponse& default_instance();

  static const ServiceDiscoveryResponse* internal_default_instance();

  void Swap(ServiceDiscoveryResponse* other);

  // implements Message ----------------------------------------------

  inline ServiceDiscoveryResponse* New() const { return New(NULL); }

  ServiceDiscoveryResponse* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ServiceDiscoveryResponse& from);
  void MergeFrom(const ServiceDiscoveryResponse& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ServiceDiscoveryResponse* other);
  void UnsafeMergeFrom(const ServiceDiscoveryResponse& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool isSuccess = 1;
  void clear_issuccess();
  static const int kIsSuccessFieldNumber = 1;
  bool issuccess() const;
  void set_issuccess(bool value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  bool has_errorcontext() const;
  void clear_errorcontext();
  static const int kErrorContextFieldNumber = 2;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& errorcontext() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_errorcontext();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_errorcontext();
  void set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext);

  // repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
  int servers_size() const;
  void clear_servers();
  static const int kServersFieldNumber = 3;
  const ::com::htsc::mdc::insight::model::ServerInfo& servers(int index) const;
  ::com::htsc::mdc::insight::model::ServerInfo* mutable_servers(int index);
  ::com::htsc::mdc::insight::model::ServerInfo* add_servers();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >*
      mutable_servers();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >&
      servers() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo > servers_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext_;
  bool issuccess_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ServiceDiscovery_2eproto_impl();
  friend void  protobuf_AddDesc_ServiceDiscovery_2eproto_impl();
  friend void protobuf_AssignDesc_ServiceDiscovery_2eproto();
  friend void protobuf_ShutdownFile_ServiceDiscovery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ServiceDiscoveryResponse> ServiceDiscoveryResponse_default_instance_;

// -------------------------------------------------------------------

class ServerInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ServerInfo) */ {
 public:
  ServerInfo();
  virtual ~ServerInfo();

  ServerInfo(const ServerInfo& from);

  inline ServerInfo& operator=(const ServerInfo& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerInfo& default_instance();

  static const ServerInfo* internal_default_instance();

  void Swap(ServerInfo* other);

  // implements Message ----------------------------------------------

  inline ServerInfo* New() const { return New(NULL); }

  ServerInfo* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ServerInfo& from);
  void MergeFrom(const ServerInfo& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ServerInfo* other);
  void UnsafeMergeFrom(const ServerInfo& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ip = 1;
  void clear_ip();
  static const int kIpFieldNumber = 1;
  const ::std::string& ip() const;
  void set_ip(const ::std::string& value);
  void set_ip(const char* value);
  void set_ip(const char* value, size_t size);
  ::std::string* mutable_ip();
  ::std::string* release_ip();
  void set_allocated_ip(::std::string* ip);

  // optional int32 port = 2;
  void clear_port();
  static const int kPortFieldNumber = 2;
  ::google::protobuf::int32 port() const;
  void set_port(::google::protobuf::int32 value);

  // optional int32 ipType = 3;
  void clear_iptype();
  static const int kIpTypeFieldNumber = 3;
  ::google::protobuf::int32 iptype() const;
  void set_iptype(::google::protobuf::int32 value);

  // optional int32 siteType = 4;
  void clear_sitetype();
  static const int kSiteTypeFieldNumber = 4;
  ::google::protobuf::int32 sitetype() const;
  void set_sitetype(::google::protobuf::int32 value);

  // optional string siteName = 5;
  void clear_sitename();
  static const int kSiteNameFieldNumber = 5;
  const ::std::string& sitename() const;
  void set_sitename(const ::std::string& value);
  void set_sitename(const char* value);
  void set_sitename(const char* value, size_t size);
  ::std::string* mutable_sitename();
  ::std::string* release_sitename();
  void set_allocated_sitename(::std::string* sitename);

  // optional int32 ipVersion = 6;
  void clear_ipversion();
  static const int kIpVersionFieldNumber = 6;
  ::google::protobuf::int32 ipversion() const;
  void set_ipversion(::google::protobuf::int32 value);

  // optional bool isSsl = 7;
  void clear_isssl();
  static const int kIsSslFieldNumber = 7;
  bool isssl() const;
  void set_isssl(bool value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ServerInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr ip_;
  ::google::protobuf::internal::ArenaStringPtr sitename_;
  ::google::protobuf::int32 port_;
  ::google::protobuf::int32 iptype_;
  ::google::protobuf::int32 sitetype_;
  ::google::protobuf::int32 ipversion_;
  bool isssl_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_ServiceDiscovery_2eproto_impl();
  friend void  protobuf_AddDesc_ServiceDiscovery_2eproto_impl();
  friend void protobuf_AssignDesc_ServiceDiscovery_2eproto();
  friend void protobuf_ShutdownFile_ServiceDiscovery_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ServerInfo> ServerInfo_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// ServiceDiscoveryRequest

// optional int32 appType = 1;
inline void ServiceDiscoveryRequest::clear_apptype() {
  apptype_ = 0;
}
inline ::google::protobuf::int32 ServiceDiscoveryRequest::apptype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appType)
  return apptype_;
}
inline void ServiceDiscoveryRequest::set_apptype(::google::protobuf::int32 value) {
  
  apptype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appType)
}

// optional string appVersion = 2;
inline void ServiceDiscoveryRequest::clear_appversion() {
  appversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServiceDiscoveryRequest::appversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  return appversion_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_appversion(const ::std::string& value) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
inline void ServiceDiscoveryRequest::set_appversion(const char* value) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
inline void ServiceDiscoveryRequest::set_appversion(const char* value, size_t size) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
inline ::std::string* ServiceDiscoveryRequest::mutable_appversion() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  return appversion_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServiceDiscoveryRequest::release_appversion() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  
  return appversion_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_allocated_appversion(::std::string* appversion) {
  if (appversion != NULL) {
    
  } else {
    
  }
  appversion_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), appversion);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}

// optional string userName = 3;
inline void ServiceDiscoveryRequest::clear_username() {
  username_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServiceDiscoveryRequest::username() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  return username_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_username(const ::std::string& value) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
inline void ServiceDiscoveryRequest::set_username(const char* value) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
inline void ServiceDiscoveryRequest::set_username(const char* value, size_t size) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
inline ::std::string* ServiceDiscoveryRequest::mutable_username() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  return username_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServiceDiscoveryRequest::release_username() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  
  return username_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_allocated_username(::std::string* username) {
  if (username != NULL) {
    
  } else {
    
  }
  username_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), username);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}

// optional string deviceId = 4;
inline void ServiceDiscoveryRequest::clear_deviceid() {
  deviceid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServiceDiscoveryRequest::deviceid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  return deviceid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_deviceid(const ::std::string& value) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
inline void ServiceDiscoveryRequest::set_deviceid(const char* value) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
inline void ServiceDiscoveryRequest::set_deviceid(const char* value, size_t size) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
inline ::std::string* ServiceDiscoveryRequest::mutable_deviceid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  return deviceid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServiceDiscoveryRequest::release_deviceid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  
  return deviceid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceDiscoveryRequest::set_allocated_deviceid(::std::string* deviceid) {
  if (deviceid != NULL) {
    
  } else {
    
  }
  deviceid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deviceid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}

// optional bool isSupportCompressed = 5;
inline void ServiceDiscoveryRequest::clear_issupportcompressed() {
  issupportcompressed_ = false;
}
inline bool ServiceDiscoveryRequest::issupportcompressed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.isSupportCompressed)
  return issupportcompressed_;
}
inline void ServiceDiscoveryRequest::set_issupportcompressed(bool value) {
  
  issupportcompressed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.isSupportCompressed)
}

inline const ServiceDiscoveryRequest* ServiceDiscoveryRequest::internal_default_instance() {
  return &ServiceDiscoveryRequest_default_instance_.get();
}
// -------------------------------------------------------------------

// ServiceDiscoveryResponse

// optional bool isSuccess = 1;
inline void ServiceDiscoveryResponse::clear_issuccess() {
  issuccess_ = false;
}
inline bool ServiceDiscoveryResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.isSuccess)
  return issuccess_;
}
inline void ServiceDiscoveryResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
inline bool ServiceDiscoveryResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
inline void ServiceDiscoveryResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& ServiceDiscoveryResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* ServiceDiscoveryResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  return errorcontext_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* ServiceDiscoveryResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
inline void ServiceDiscoveryResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
}

// repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
inline int ServiceDiscoveryResponse::servers_size() const {
  return servers_.size();
}
inline void ServiceDiscoveryResponse::clear_servers() {
  servers_.Clear();
}
inline const ::com::htsc::mdc::insight::model::ServerInfo& ServiceDiscoveryResponse::servers(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Get(index);
}
inline ::com::htsc::mdc::insight::model::ServerInfo* ServiceDiscoveryResponse::mutable_servers(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::ServerInfo* ServiceDiscoveryResponse::add_servers() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >*
ServiceDiscoveryResponse::mutable_servers() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return &servers_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >&
ServiceDiscoveryResponse::servers() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_;
}

inline const ServiceDiscoveryResponse* ServiceDiscoveryResponse::internal_default_instance() {
  return &ServiceDiscoveryResponse_default_instance_.get();
}
// -------------------------------------------------------------------

// ServerInfo

// optional string ip = 1;
inline void ServerInfo::clear_ip() {
  ip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerInfo::ip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ip)
  return ip_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerInfo::set_ip(const ::std::string& value) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ip)
}
inline void ServerInfo::set_ip(const char* value) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServerInfo.ip)
}
inline void ServerInfo::set_ip(const char* value, size_t size) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServerInfo.ip)
}
inline ::std::string* ServerInfo::mutable_ip() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServerInfo.ip)
  return ip_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerInfo::release_ip() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServerInfo.ip)
  
  return ip_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerInfo::set_allocated_ip(::std::string* ip) {
  if (ip != NULL) {
    
  } else {
    
  }
  ip_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ip);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServerInfo.ip)
}

// optional int32 port = 2;
inline void ServerInfo::clear_port() {
  port_ = 0;
}
inline ::google::protobuf::int32 ServerInfo::port() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.port)
  return port_;
}
inline void ServerInfo::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.port)
}

// optional int32 ipType = 3;
inline void ServerInfo::clear_iptype() {
  iptype_ = 0;
}
inline ::google::protobuf::int32 ServerInfo::iptype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ipType)
  return iptype_;
}
inline void ServerInfo::set_iptype(::google::protobuf::int32 value) {
  
  iptype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ipType)
}

// optional int32 siteType = 4;
inline void ServerInfo::clear_sitetype() {
  sitetype_ = 0;
}
inline ::google::protobuf::int32 ServerInfo::sitetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.siteType)
  return sitetype_;
}
inline void ServerInfo::set_sitetype(::google::protobuf::int32 value) {
  
  sitetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.siteType)
}

// optional string siteName = 5;
inline void ServerInfo::clear_sitename() {
  sitename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerInfo::sitename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.siteName)
  return sitename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerInfo::set_sitename(const ::std::string& value) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
inline void ServerInfo::set_sitename(const char* value) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
inline void ServerInfo::set_sitename(const char* value, size_t size) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
inline ::std::string* ServerInfo::mutable_sitename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServerInfo.siteName)
  return sitename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerInfo::release_sitename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServerInfo.siteName)
  
  return sitename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerInfo::set_allocated_sitename(::std::string* sitename) {
  if (sitename != NULL) {
    
  } else {
    
  }
  sitename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sitename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServerInfo.siteName)
}

// optional int32 ipVersion = 6;
inline void ServerInfo::clear_ipversion() {
  ipversion_ = 0;
}
inline ::google::protobuf::int32 ServerInfo::ipversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ipVersion)
  return ipversion_;
}
inline void ServerInfo::set_ipversion(::google::protobuf::int32 value) {
  
  ipversion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ipVersion)
}

// optional bool isSsl = 7;
inline void ServerInfo::clear_isssl() {
  isssl_ = false;
}
inline bool ServerInfo::isssl() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.isSsl)
  return isssl_;
}
inline void ServerInfo::set_isssl(bool value) {
  
  isssl_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.isSsl)
}

inline const ServerInfo* ServerInfo::internal_default_instance() {
  return &ServerInfo_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_ServiceDiscovery_2eproto__INCLUDED
