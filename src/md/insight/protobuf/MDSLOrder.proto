syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDSLOrder {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  repeated SLOrderBookEntry BidEntries = 7;  // 融出方（lender）
  repeated SLOrderBookEntry AskEntries = 8;  // 融入方（borrower）
  int32 DataMultiplePowerOf10 = 9;
  int64 MessageNumber = 100;
}

message SLOrderBookEntry {
  double Rate = 1;      // 年化利率
  double Quantity = 2;  // 数量
}
