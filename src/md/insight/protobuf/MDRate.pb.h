// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDRate.proto

#ifndef PROTOBUF_MDRate_2eproto__INCLUDED
#define PROTOBUF_MDRate_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDRate_2eproto();
void protobuf_InitDefaults_MDRate_2eproto();
void protobuf_AssignDesc_MDRate_2eproto();
void protobuf_ShutdownFile_MDRate_2eproto();

class MDRate;

// ===================================================================

class MDRate : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDRate) */ {
 public:
  MDRate();
  virtual ~MDRate();

  MDRate(const MDRate& from);

  inline MDRate& operator=(const MDRate& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDRate& default_instance();

  static const MDRate* internal_default_instance();

  void Swap(MDRate* other);

  // implements Message ----------------------------------------------

  inline MDRate* New() const { return New(NULL); }

  MDRate* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDRate& from);
  void MergeFrom(const MDRate& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDRate* other);
  void UnsafeMergeFrom(const MDRate& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 11;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 11;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 12;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 13;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 13;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 14;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 14;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 15;
  void clear_closepx();
  static const int kClosePxFieldNumber = 15;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 16;
  void clear_highpx();
  static const int kHighPxFieldNumber = 16;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 17;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 17;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 18;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 18;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 19;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 19;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 21 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 21;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 22 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 22;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 23 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 23;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 24 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 24;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 25 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 25;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 26 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 26;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 27 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 27;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 28 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 28;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 29;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 29;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDRate)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 delaytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDRate_2eproto_impl();
  friend void  protobuf_AddDesc_MDRate_2eproto_impl();
  friend void protobuf_AssignDesc_MDRate_2eproto();
  friend void protobuf_ShutdownFile_MDRate_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDRate> MDRate_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDRate

// optional string HTSCSecurityID = 1;
inline void MDRate::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDRate::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDRate::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
}
inline void MDRate::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
}
inline void MDRate::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
}
inline ::std::string* MDRate::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDRate::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDRate::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDRate.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDRate::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDRate::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.MDDate)
  return mddate_;
}
inline void MDRate::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.MDDate)
}

// optional int32 MDTime = 3;
inline void MDRate::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDRate::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.MDTime)
  return mdtime_;
}
inline void MDRate::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDRate::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.DataTimestamp)
  return datatimestamp_;
}
inline void MDRate::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDRate::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDRate::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDRate::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
}
inline void MDRate::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
}
inline void MDRate::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
}
inline ::std::string* MDRate::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDRate::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDRate::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDRate.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDRate::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDRate::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDRate::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDRate::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDRate::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDRate::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.securityType)
}

// optional int64 MaxPx = 8;
inline void MDRate::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.MaxPx)
  return maxpx_;
}
inline void MDRate::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDRate::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.MinPx)
  return minpx_;
}
inline void MDRate::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDRate::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.PreClosePx)
  return preclosepx_;
}
inline void MDRate::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.PreClosePx)
}

// optional int64 TotalVolumeTrade = 11;
inline void MDRate::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDRate::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 12;
inline void MDRate::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDRate::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.TotalValueTrade)
}

// optional int64 LastPx = 13;
inline void MDRate::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.LastPx)
  return lastpx_;
}
inline void MDRate::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.LastPx)
}

// optional int64 OpenPx = 14;
inline void MDRate::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.OpenPx)
  return openpx_;
}
inline void MDRate::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.OpenPx)
}

// optional int64 ClosePx = 15;
inline void MDRate::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.ClosePx)
  return closepx_;
}
inline void MDRate::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.ClosePx)
}

// optional int64 HighPx = 16;
inline void MDRate::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.HighPx)
  return highpx_;
}
inline void MDRate::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.HighPx)
}

// optional int64 LowPx = 17;
inline void MDRate::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDRate::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.LowPx)
  return lowpx_;
}
inline void MDRate::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.LowPx)
}

// optional int32 ExchangeDate = 18;
inline void MDRate::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDRate::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.ExchangeDate)
  return exchangedate_;
}
inline void MDRate::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.ExchangeDate)
}

// optional int32 ExchangeTime = 19;
inline void MDRate::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDRate::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.ExchangeTime)
  return exchangetime_;
}
inline void MDRate::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.ExchangeTime)
}

// repeated int64 BuyPriceQueue = 21 [packed = true];
inline int MDRate::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDRate::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDRate::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDRate::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.BuyPriceQueue)
}
inline void MDRate::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 22 [packed = true];
inline int MDRate::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDRate::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDRate::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.BuyOrderQtyQueue)
}
inline void MDRate::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 23 [packed = true];
inline int MDRate::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDRate::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDRate::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDRate::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.SellPriceQueue)
}
inline void MDRate::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 24 [packed = true];
inline int MDRate::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDRate::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDRate::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.SellOrderQtyQueue)
}
inline void MDRate::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 25 [packed = true];
inline int MDRate::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDRate::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDRate::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.BuyOrderQueue)
}
inline void MDRate::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 26 [packed = true];
inline int MDRate::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDRate::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDRate::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.SellOrderQueue)
}
inline void MDRate::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 27 [packed = true];
inline int MDRate::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDRate::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDRate::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.BuyNumOrdersQueue)
}
inline void MDRate::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 28 [packed = true];
inline int MDRate::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDRate::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDRate::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDRate::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.SellNumOrdersQueue)
}
inline void MDRate::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDRate.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDRate::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDRate.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDRate::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDRate.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 29;
inline void MDRate::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDRate::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDRate::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.DataMultiplePowerOf10)
}

// optional int32 DelayType = 101;
inline void MDRate::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDRate::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDRate.DelayType)
  return delaytype_;
}
inline void MDRate::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDRate.DelayType)
}

inline const MDRate* MDRate::internal_default_instance() {
  return &MDRate_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDRate_2eproto__INCLUDED
