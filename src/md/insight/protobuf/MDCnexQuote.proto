syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCnexQuote {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  string InstrumentID = 10;
  double BidPrice = 11;
  double AskPrice = 12;
  double BidSize = 13;
  double AskSize = 14;
  string QuoteTime = 15;
  int64 MessageNumber = 100;
}
