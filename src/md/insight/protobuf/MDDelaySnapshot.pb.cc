// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDDelaySnapshot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDDelaySnapshot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDDelaySnapshot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDDelaySnapshot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDDelaySnapshot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDDelaySnapshot_2eproto() {
  protobuf_AddDesc_MDDelaySnapshot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDDelaySnapshot.proto");
  GOOGLE_CHECK(file != NULL);
  MDDelaySnapshot_descriptor_ = file->message_type(0);
  static const int MDDelaySnapshot_offsets_[49] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, tradingdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, presettleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, openinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, settleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, predelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, currdelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, middlepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, impliedbuypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, impliedbuyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, impliedsellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, impliedsellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, positiontrend_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, changespeed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, changerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, changevalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, swing_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, commoditycontractnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, delaysnapshottype_),
  };
  MDDelaySnapshot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDDelaySnapshot_descriptor_,
      MDDelaySnapshot::internal_default_instance(),
      MDDelaySnapshot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDDelaySnapshot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDDelaySnapshot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDDelaySnapshot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDDelaySnapshot_descriptor_, MDDelaySnapshot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDDelaySnapshot_2eproto() {
  MDDelaySnapshot_default_instance_.Shutdown();
  delete MDDelaySnapshot_reflection_;
}

void protobuf_InitDefaults_MDDelaySnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDDelaySnapshot_default_instance_.DefaultConstruct();
  MDDelaySnapshot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDDelaySnapshot_2eproto_once_);
void protobuf_InitDefaults_MDDelaySnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDDelaySnapshot_2eproto_once_,
                 &protobuf_InitDefaults_MDDelaySnapshot_2eproto_impl);
}
void protobuf_AddDesc_MDDelaySnapshot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDDelaySnapshot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025MDDelaySnapshot.proto\022\032com.htsc.mdc.in"
    "sight.model\032\027ESecurityIDSource.proto\032\023ES"
    "ecurityType.proto\"\250\t\n\017MDDelaySnapshot\022\026\n"
    "\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n"
    "\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020"
    "TradingPhaseCode\030\005 \001(\t\022\?\n\020securityIDSour"
    "ce\030\006 \001(\0162%.com.htsc.mdc.model.ESecurityI"
    "DSource\0227\n\014securityType\030\007 \001(\0162!.com.htsc"
    ".mdc.model.ESecurityType\022\r\n\005MaxPx\030\010 \001(\003\022"
    "\r\n\005MinPx\030\t \001(\003\022\022\n\nPreClosePx\030\n \001(\003\022\021\n\tNu"
    "mTrades\030\013 \001(\003\022\030\n\020TotalVolumeTrade\030\014 \001(\003\022"
    "\027\n\017TotalValueTrade\030\r \001(\003\022\016\n\006LastPx\030\016 \001(\003"
    "\022\016\n\006OpenPx\030\017 \001(\003\022\017\n\007ClosePx\030\020 \001(\003\022\016\n\006Hig"
    "hPx\030\021 \001(\003\022\r\n\005LowPx\030\022 \001(\003\022\023\n\013TradingDate\030"
    "\023 \001(\005\022\027\n\017PreOpenInterest\030\024 \001(\003\022\026\n\016PreSet"
    "tlePrice\030\025 \001(\003\022\024\n\014OpenInterest\030\026 \001(\003\022\023\n\013"
    "SettlePrice\030\027 \001(\003\022\020\n\010PreDelta\030\030 \001(\003\022\021\n\tC"
    "urrDelta\030\031 \001(\003\022\020\n\010MiddlePx\030\032 \001(\003\022\024\n\014Impl"
    "iedBuyPx\030\033 \001(\003\022\025\n\rImpliedBuyQty\030\034 \001(\003\022\025\n"
    "\rImpliedSellPx\030\035 \001(\003\022\026\n\016ImpliedSellQty\030\036"
    " \001(\003\022\025\n\rPositionTrend\030\037 \001(\003\022\023\n\013ChangeSpe"
    "ed\030  \001(\003\022\022\n\nChangeRate\030! \001(\003\022\023\n\013ChangeVa"
    "lue\030\" \001(\003\022\r\n\005Swing\030# \001(\003\022\037\n\027CommodityCon"
    "tractNumber\030$ \001(\t\022\024\n\014ExchangeDate\030% \001(\005\022"
    "\024\n\014ExchangeTime\030& \001(\005\022\021\n\tChannelNo\0302 \001(\005"
    "\022\031\n\rBuyPriceQueue\0303 \003(\003B\002\020\001\022\034\n\020BuyOrderQ"
    "tyQueue\0304 \003(\003B\002\020\001\022\032\n\016SellPriceQueue\0305 \003("
    "\003B\002\020\001\022\035\n\021SellOrderQtyQueue\0306 \003(\003B\002\020\001\022\031\n\r"
    "BuyOrderQueue\0307 \003(\003B\002\020\001\022\032\n\016SellOrderQueu"
    "e\0308 \003(\003B\002\020\001\022\035\n\021BuyNumOrdersQueue\0309 \003(\003B\002"
    "\020\001\022\036\n\022SellNumOrdersQueue\030: \003(\003B\002\020\001\022\035\n\025Da"
    "taMultiplePowerOf10\030; \001(\005\022\031\n\021DelaySnapsh"
    "otType\030< \001(\005B8\n\032com.htsc.mdc.insight.mod"
    "elB\025MDDelaySnapshotProtosH\001\240\001\001b\006proto3", 1358);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDDelaySnapshot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDDelaySnapshot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDDelaySnapshot_2eproto_once_);
void protobuf_AddDesc_MDDelaySnapshot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDDelaySnapshot_2eproto_once_,
                 &protobuf_AddDesc_MDDelaySnapshot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDDelaySnapshot_2eproto {
  StaticDescriptorInitializer_MDDelaySnapshot_2eproto() {
    protobuf_AddDesc_MDDelaySnapshot_2eproto();
  }
} static_descriptor_initializer_MDDelaySnapshot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDDelaySnapshot::kHTSCSecurityIDFieldNumber;
const int MDDelaySnapshot::kMDDateFieldNumber;
const int MDDelaySnapshot::kMDTimeFieldNumber;
const int MDDelaySnapshot::kDataTimestampFieldNumber;
const int MDDelaySnapshot::kTradingPhaseCodeFieldNumber;
const int MDDelaySnapshot::kSecurityIDSourceFieldNumber;
const int MDDelaySnapshot::kSecurityTypeFieldNumber;
const int MDDelaySnapshot::kMaxPxFieldNumber;
const int MDDelaySnapshot::kMinPxFieldNumber;
const int MDDelaySnapshot::kPreClosePxFieldNumber;
const int MDDelaySnapshot::kNumTradesFieldNumber;
const int MDDelaySnapshot::kTotalVolumeTradeFieldNumber;
const int MDDelaySnapshot::kTotalValueTradeFieldNumber;
const int MDDelaySnapshot::kLastPxFieldNumber;
const int MDDelaySnapshot::kOpenPxFieldNumber;
const int MDDelaySnapshot::kClosePxFieldNumber;
const int MDDelaySnapshot::kHighPxFieldNumber;
const int MDDelaySnapshot::kLowPxFieldNumber;
const int MDDelaySnapshot::kTradingDateFieldNumber;
const int MDDelaySnapshot::kPreOpenInterestFieldNumber;
const int MDDelaySnapshot::kPreSettlePriceFieldNumber;
const int MDDelaySnapshot::kOpenInterestFieldNumber;
const int MDDelaySnapshot::kSettlePriceFieldNumber;
const int MDDelaySnapshot::kPreDeltaFieldNumber;
const int MDDelaySnapshot::kCurrDeltaFieldNumber;
const int MDDelaySnapshot::kMiddlePxFieldNumber;
const int MDDelaySnapshot::kImpliedBuyPxFieldNumber;
const int MDDelaySnapshot::kImpliedBuyQtyFieldNumber;
const int MDDelaySnapshot::kImpliedSellPxFieldNumber;
const int MDDelaySnapshot::kImpliedSellQtyFieldNumber;
const int MDDelaySnapshot::kPositionTrendFieldNumber;
const int MDDelaySnapshot::kChangeSpeedFieldNumber;
const int MDDelaySnapshot::kChangeRateFieldNumber;
const int MDDelaySnapshot::kChangeValueFieldNumber;
const int MDDelaySnapshot::kSwingFieldNumber;
const int MDDelaySnapshot::kCommodityContractNumberFieldNumber;
const int MDDelaySnapshot::kExchangeDateFieldNumber;
const int MDDelaySnapshot::kExchangeTimeFieldNumber;
const int MDDelaySnapshot::kChannelNoFieldNumber;
const int MDDelaySnapshot::kBuyPriceQueueFieldNumber;
const int MDDelaySnapshot::kBuyOrderQtyQueueFieldNumber;
const int MDDelaySnapshot::kSellPriceQueueFieldNumber;
const int MDDelaySnapshot::kSellOrderQtyQueueFieldNumber;
const int MDDelaySnapshot::kBuyOrderQueueFieldNumber;
const int MDDelaySnapshot::kSellOrderQueueFieldNumber;
const int MDDelaySnapshot::kBuyNumOrdersQueueFieldNumber;
const int MDDelaySnapshot::kSellNumOrdersQueueFieldNumber;
const int MDDelaySnapshot::kDataMultiplePowerOf10FieldNumber;
const int MDDelaySnapshot::kDelaySnapshotTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDDelaySnapshot::MDDelaySnapshot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDDelaySnapshot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDDelaySnapshot)
}

void MDDelaySnapshot::InitAsDefaultInstance() {
}

MDDelaySnapshot::MDDelaySnapshot(const MDDelaySnapshot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDDelaySnapshot)
}

void MDDelaySnapshot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&delaysnapshottype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(delaysnapshottype_));
  _cached_size_ = 0;
}

MDDelaySnapshot::~MDDelaySnapshot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDDelaySnapshot)
  SharedDtor();
}

void MDDelaySnapshot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDDelaySnapshot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDDelaySnapshot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDDelaySnapshot_descriptor_;
}

const MDDelaySnapshot& MDDelaySnapshot::default_instance() {
  protobuf_InitDefaults_MDDelaySnapshot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDDelaySnapshot> MDDelaySnapshot_default_instance_;

MDDelaySnapshot* MDDelaySnapshot::New(::google::protobuf::Arena* arena) const {
  MDDelaySnapshot* n = new MDDelaySnapshot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDDelaySnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDDelaySnapshot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDDelaySnapshot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, predelta_);
  tradingdate_ = 0;
  ZR_(currdelta_, impliedbuyqty_);
  ZR_(impliedsellpx_, changespeed_);
  ZR_(changerate_, channelno_);
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_ = 0;
  datamultiplepowerof10_ = 0;
  delaysnapshottype_ = 0;

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
}

bool MDDelaySnapshot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_TradingDate;
        break;
      }

      // optional int32 TradingDate = 19;
      case 19: {
        if (tag == 152) {
         parse_TradingDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradingdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_PreOpenInterest;
        break;
      }

      // optional int64 PreOpenInterest = 20;
      case 20: {
        if (tag == 160) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_PreSettlePrice;
        break;
      }

      // optional int64 PreSettlePrice = 21;
      case 21: {
        if (tag == 168) {
         parse_PreSettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_OpenInterest;
        break;
      }

      // optional int64 OpenInterest = 22;
      case 22: {
        if (tag == 176) {
         parse_OpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_SettlePrice;
        break;
      }

      // optional int64 SettlePrice = 23;
      case 23: {
        if (tag == 184) {
         parse_SettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_PreDelta;
        break;
      }

      // optional int64 PreDelta = 24;
      case 24: {
        if (tag == 192) {
         parse_PreDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &predelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_CurrDelta;
        break;
      }

      // optional int64 CurrDelta = 25;
      case 25: {
        if (tag == 200) {
         parse_CurrDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &currdelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_MiddlePx;
        break;
      }

      // optional int64 MiddlePx = 26;
      case 26: {
        if (tag == 208) {
         parse_MiddlePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &middlepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_ImpliedBuyPx;
        break;
      }

      // optional int64 ImpliedBuyPx = 27;
      case 27: {
        if (tag == 216) {
         parse_ImpliedBuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedbuypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_ImpliedBuyQty;
        break;
      }

      // optional int64 ImpliedBuyQty = 28;
      case 28: {
        if (tag == 224) {
         parse_ImpliedBuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedbuyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_ImpliedSellPx;
        break;
      }

      // optional int64 ImpliedSellPx = 29;
      case 29: {
        if (tag == 232) {
         parse_ImpliedSellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedsellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_ImpliedSellQty;
        break;
      }

      // optional int64 ImpliedSellQty = 30;
      case 30: {
        if (tag == 240) {
         parse_ImpliedSellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &impliedsellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_PositionTrend;
        break;
      }

      // optional int64 PositionTrend = 31;
      case 31: {
        if (tag == 248) {
         parse_PositionTrend:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &positiontrend_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_ChangeSpeed;
        break;
      }

      // optional int64 ChangeSpeed = 32;
      case 32: {
        if (tag == 256) {
         parse_ChangeSpeed:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changespeed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_ChangeRate;
        break;
      }

      // optional int64 ChangeRate = 33;
      case 33: {
        if (tag == 264) {
         parse_ChangeRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_ChangeValue;
        break;
      }

      // optional int64 ChangeValue = 34;
      case 34: {
        if (tag == 272) {
         parse_ChangeValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &changevalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_Swing;
        break;
      }

      // optional int64 Swing = 35;
      case 35: {
        if (tag == 280) {
         parse_Swing:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &swing_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_CommodityContractNumber;
        break;
      }

      // optional string CommodityContractNumber = 36;
      case 36: {
        if (tag == 290) {
         parse_CommodityContractNumber:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_commoditycontractnumber()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 37;
      case 37: {
        if (tag == 296) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 38;
      case 38: {
        if (tag == 304) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 50;
      case 50: {
        if (tag == 400) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 59;
      case 59: {
        if (tag == 472) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_DelaySnapshotType;
        break;
      }

      // optional int32 DelaySnapshotType = 60;
      case 60: {
        if (tag == 480) {
         parse_DelaySnapshotType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaysnapshottype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDDelaySnapshot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDDelaySnapshot)
  return false;
#undef DO_
}

void MDDelaySnapshot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->tradingdate(), output);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->preopeninterest(), output);
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->presettleprice(), output);
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->openinterest(), output);
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->settleprice(), output);
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->predelta(), output);
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->currdelta(), output);
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->middlepx(), output);
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->impliedbuypx(), output);
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->impliedbuyqty(), output);
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->impliedsellpx(), output);
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->impliedsellqty(), output);
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->positiontrend(), output);
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->changespeed(), output);
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->changerate(), output);
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->changevalue(), output);
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->swing(), output);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      36, this->commoditycontractnumber(), output);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(37, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(38, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(50, this->channelno(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(59, this->datamultiplepowerof10(), output);
  }

  // optional int32 DelaySnapshotType = 60;
  if (this->delaysnapshottype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(60, this->delaysnapshottype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDDelaySnapshot)
}

::google::protobuf::uint8* MDDelaySnapshot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->tradingdate(), target);
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->preopeninterest(), target);
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->presettleprice(), target);
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->openinterest(), target);
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->settleprice(), target);
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->predelta(), target);
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->currdelta(), target);
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->middlepx(), target);
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->impliedbuypx(), target);
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->impliedbuyqty(), target);
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->impliedsellpx(), target);
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->impliedsellqty(), target);
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->positiontrend(), target);
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->changespeed(), target);
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->changerate(), target);
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->changevalue(), target);
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->swing(), target);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        36, this->commoditycontractnumber(), target);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(37, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(38, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(50, this->channelno(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(59, this->datamultiplepowerof10(), target);
  }

  // optional int32 DelaySnapshotType = 60;
  if (this->delaysnapshottype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(60, this->delaysnapshottype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDDelaySnapshot)
  return target;
}

size_t MDDelaySnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int32 TradingDate = 19;
  if (this->tradingdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradingdate());
  }

  // optional int64 PreOpenInterest = 20;
  if (this->preopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preopeninterest());
  }

  // optional int64 PreSettlePrice = 21;
  if (this->presettleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->presettleprice());
  }

  // optional int64 OpenInterest = 22;
  if (this->openinterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openinterest());
  }

  // optional int64 SettlePrice = 23;
  if (this->settleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settleprice());
  }

  // optional int64 PreDelta = 24;
  if (this->predelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->predelta());
  }

  // optional int64 CurrDelta = 25;
  if (this->currdelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->currdelta());
  }

  // optional int64 MiddlePx = 26;
  if (this->middlepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->middlepx());
  }

  // optional int64 ImpliedBuyPx = 27;
  if (this->impliedbuypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedbuypx());
  }

  // optional int64 ImpliedBuyQty = 28;
  if (this->impliedbuyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedbuyqty());
  }

  // optional int64 ImpliedSellPx = 29;
  if (this->impliedsellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedsellpx());
  }

  // optional int64 ImpliedSellQty = 30;
  if (this->impliedsellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->impliedsellqty());
  }

  // optional int64 PositionTrend = 31;
  if (this->positiontrend() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->positiontrend());
  }

  // optional int64 ChangeSpeed = 32;
  if (this->changespeed() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changespeed());
  }

  // optional int64 ChangeRate = 33;
  if (this->changerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changerate());
  }

  // optional int64 ChangeValue = 34;
  if (this->changevalue() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->changevalue());
  }

  // optional int64 Swing = 35;
  if (this->swing() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->swing());
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->commoditycontractnumber());
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 DelaySnapshotType = 60;
  if (this->delaysnapshottype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaysnapshottype());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDDelaySnapshot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDDelaySnapshot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDDelaySnapshot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDDelaySnapshot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDDelaySnapshot)
    UnsafeMergeFrom(*source);
  }
}

void MDDelaySnapshot::MergeFrom(const MDDelaySnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDDelaySnapshot::UnsafeMergeFrom(const MDDelaySnapshot& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.tradingdate() != 0) {
    set_tradingdate(from.tradingdate());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.presettleprice() != 0) {
    set_presettleprice(from.presettleprice());
  }
  if (from.openinterest() != 0) {
    set_openinterest(from.openinterest());
  }
  if (from.settleprice() != 0) {
    set_settleprice(from.settleprice());
  }
  if (from.predelta() != 0) {
    set_predelta(from.predelta());
  }
  if (from.currdelta() != 0) {
    set_currdelta(from.currdelta());
  }
  if (from.middlepx() != 0) {
    set_middlepx(from.middlepx());
  }
  if (from.impliedbuypx() != 0) {
    set_impliedbuypx(from.impliedbuypx());
  }
  if (from.impliedbuyqty() != 0) {
    set_impliedbuyqty(from.impliedbuyqty());
  }
  if (from.impliedsellpx() != 0) {
    set_impliedsellpx(from.impliedsellpx());
  }
  if (from.impliedsellqty() != 0) {
    set_impliedsellqty(from.impliedsellqty());
  }
  if (from.positiontrend() != 0) {
    set_positiontrend(from.positiontrend());
  }
  if (from.changespeed() != 0) {
    set_changespeed(from.changespeed());
  }
  if (from.changerate() != 0) {
    set_changerate(from.changerate());
  }
  if (from.changevalue() != 0) {
    set_changevalue(from.changevalue());
  }
  if (from.swing() != 0) {
    set_swing(from.swing());
  }
  if (from.commoditycontractnumber().size() > 0) {

    commoditycontractnumber_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.commoditycontractnumber_);
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.delaysnapshottype() != 0) {
    set_delaysnapshottype(from.delaysnapshottype());
  }
}

void MDDelaySnapshot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDDelaySnapshot::CopyFrom(const MDDelaySnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDDelaySnapshot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDDelaySnapshot::IsInitialized() const {

  return true;
}

void MDDelaySnapshot::Swap(MDDelaySnapshot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDDelaySnapshot::InternalSwap(MDDelaySnapshot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(tradingdate_, other->tradingdate_);
  std::swap(preopeninterest_, other->preopeninterest_);
  std::swap(presettleprice_, other->presettleprice_);
  std::swap(openinterest_, other->openinterest_);
  std::swap(settleprice_, other->settleprice_);
  std::swap(predelta_, other->predelta_);
  std::swap(currdelta_, other->currdelta_);
  std::swap(middlepx_, other->middlepx_);
  std::swap(impliedbuypx_, other->impliedbuypx_);
  std::swap(impliedbuyqty_, other->impliedbuyqty_);
  std::swap(impliedsellpx_, other->impliedsellpx_);
  std::swap(impliedsellqty_, other->impliedsellqty_);
  std::swap(positiontrend_, other->positiontrend_);
  std::swap(changespeed_, other->changespeed_);
  std::swap(changerate_, other->changerate_);
  std::swap(changevalue_, other->changevalue_);
  std::swap(swing_, other->swing_);
  commoditycontractnumber_.Swap(&other->commoditycontractnumber_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(delaysnapshottype_, other->delaysnapshottype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDDelaySnapshot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDDelaySnapshot_descriptor_;
  metadata.reflection = MDDelaySnapshot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDDelaySnapshot

// optional string HTSCSecurityID = 1;
void MDDelaySnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDDelaySnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
}
void MDDelaySnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
}
void MDDelaySnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
}
::std::string* MDDelaySnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDDelaySnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDDelaySnapshot.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDDelaySnapshot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.MDDate)
  return mddate_;
}
void MDDelaySnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.MDDate)
}

// optional int32 MDTime = 3;
void MDDelaySnapshot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.MDTime)
  return mdtime_;
}
void MDDelaySnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDDelaySnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.DataTimestamp)
  return datatimestamp_;
}
void MDDelaySnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDDelaySnapshot::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDDelaySnapshot::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
}
void MDDelaySnapshot::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
}
void MDDelaySnapshot::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
}
::std::string* MDDelaySnapshot::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDDelaySnapshot::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDDelaySnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDDelaySnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDDelaySnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDDelaySnapshot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDDelaySnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDDelaySnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.securityType)
}

// optional int64 MaxPx = 8;
void MDDelaySnapshot::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.MaxPx)
  return maxpx_;
}
void MDDelaySnapshot::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.MaxPx)
}

// optional int64 MinPx = 9;
void MDDelaySnapshot::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.MinPx)
  return minpx_;
}
void MDDelaySnapshot::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.MinPx)
}

// optional int64 PreClosePx = 10;
void MDDelaySnapshot::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.PreClosePx)
  return preclosepx_;
}
void MDDelaySnapshot::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDDelaySnapshot::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.NumTrades)
  return numtrades_;
}
void MDDelaySnapshot::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDDelaySnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDDelaySnapshot::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDDelaySnapshot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.TotalValueTrade)
  return totalvaluetrade_;
}
void MDDelaySnapshot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDDelaySnapshot::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.LastPx)
  return lastpx_;
}
void MDDelaySnapshot::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.LastPx)
}

// optional int64 OpenPx = 15;
void MDDelaySnapshot::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.OpenPx)
  return openpx_;
}
void MDDelaySnapshot::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.OpenPx)
}

// optional int64 ClosePx = 16;
void MDDelaySnapshot::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ClosePx)
  return closepx_;
}
void MDDelaySnapshot::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ClosePx)
}

// optional int64 HighPx = 17;
void MDDelaySnapshot::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.HighPx)
  return highpx_;
}
void MDDelaySnapshot::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.HighPx)
}

// optional int64 LowPx = 18;
void MDDelaySnapshot::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.LowPx)
  return lowpx_;
}
void MDDelaySnapshot::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.LowPx)
}

// optional int32 TradingDate = 19;
void MDDelaySnapshot::clear_tradingdate() {
  tradingdate_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingDate)
  return tradingdate_;
}
void MDDelaySnapshot::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.TradingDate)
}

// optional int64 PreOpenInterest = 20;
void MDDelaySnapshot::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.PreOpenInterest)
  return preopeninterest_;
}
void MDDelaySnapshot::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.PreOpenInterest)
}

// optional int64 PreSettlePrice = 21;
void MDDelaySnapshot::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.PreSettlePrice)
  return presettleprice_;
}
void MDDelaySnapshot::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.PreSettlePrice)
}

// optional int64 OpenInterest = 22;
void MDDelaySnapshot::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.OpenInterest)
  return openinterest_;
}
void MDDelaySnapshot::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.OpenInterest)
}

// optional int64 SettlePrice = 23;
void MDDelaySnapshot::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.SettlePrice)
  return settleprice_;
}
void MDDelaySnapshot::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.SettlePrice)
}

// optional int64 PreDelta = 24;
void MDDelaySnapshot::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.PreDelta)
  return predelta_;
}
void MDDelaySnapshot::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.PreDelta)
}

// optional int64 CurrDelta = 25;
void MDDelaySnapshot::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.CurrDelta)
  return currdelta_;
}
void MDDelaySnapshot::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.CurrDelta)
}

// optional int64 MiddlePx = 26;
void MDDelaySnapshot::clear_middlepx() {
  middlepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::middlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.MiddlePx)
  return middlepx_;
}
void MDDelaySnapshot::set_middlepx(::google::protobuf::int64 value) {
  
  middlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.MiddlePx)
}

// optional int64 ImpliedBuyPx = 27;
void MDDelaySnapshot::clear_impliedbuypx() {
  impliedbuypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::impliedbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedBuyPx)
  return impliedbuypx_;
}
void MDDelaySnapshot::set_impliedbuypx(::google::protobuf::int64 value) {
  
  impliedbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedBuyPx)
}

// optional int64 ImpliedBuyQty = 28;
void MDDelaySnapshot::clear_impliedbuyqty() {
  impliedbuyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::impliedbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedBuyQty)
  return impliedbuyqty_;
}
void MDDelaySnapshot::set_impliedbuyqty(::google::protobuf::int64 value) {
  
  impliedbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedBuyQty)
}

// optional int64 ImpliedSellPx = 29;
void MDDelaySnapshot::clear_impliedsellpx() {
  impliedsellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::impliedsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedSellPx)
  return impliedsellpx_;
}
void MDDelaySnapshot::set_impliedsellpx(::google::protobuf::int64 value) {
  
  impliedsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedSellPx)
}

// optional int64 ImpliedSellQty = 30;
void MDDelaySnapshot::clear_impliedsellqty() {
  impliedsellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::impliedsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedSellQty)
  return impliedsellqty_;
}
void MDDelaySnapshot::set_impliedsellqty(::google::protobuf::int64 value) {
  
  impliedsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ImpliedSellQty)
}

// optional int64 PositionTrend = 31;
void MDDelaySnapshot::clear_positiontrend() {
  positiontrend_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::positiontrend() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.PositionTrend)
  return positiontrend_;
}
void MDDelaySnapshot::set_positiontrend(::google::protobuf::int64 value) {
  
  positiontrend_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.PositionTrend)
}

// optional int64 ChangeSpeed = 32;
void MDDelaySnapshot::clear_changespeed() {
  changespeed_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::changespeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeSpeed)
  return changespeed_;
}
void MDDelaySnapshot::set_changespeed(::google::protobuf::int64 value) {
  
  changespeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeSpeed)
}

// optional int64 ChangeRate = 33;
void MDDelaySnapshot::clear_changerate() {
  changerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::changerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeRate)
  return changerate_;
}
void MDDelaySnapshot::set_changerate(::google::protobuf::int64 value) {
  
  changerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeRate)
}

// optional int64 ChangeValue = 34;
void MDDelaySnapshot::clear_changevalue() {
  changevalue_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::changevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeValue)
  return changevalue_;
}
void MDDelaySnapshot::set_changevalue(::google::protobuf::int64 value) {
  
  changevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ChangeValue)
}

// optional int64 Swing = 35;
void MDDelaySnapshot::clear_swing() {
  swing_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDDelaySnapshot::swing() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.Swing)
  return swing_;
}
void MDDelaySnapshot::set_swing(::google::protobuf::int64 value) {
  
  swing_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.Swing)
}

// optional string CommodityContractNumber = 36;
void MDDelaySnapshot::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDDelaySnapshot::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
}
void MDDelaySnapshot::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
}
void MDDelaySnapshot::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
}
::std::string* MDDelaySnapshot::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDDelaySnapshot::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDDelaySnapshot::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDDelaySnapshot.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
void MDDelaySnapshot::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ExchangeDate)
  return exchangedate_;
}
void MDDelaySnapshot::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
void MDDelaySnapshot::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ExchangeTime)
  return exchangetime_;
}
void MDDelaySnapshot::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ExchangeTime)
}

// optional int32 ChannelNo = 50;
void MDDelaySnapshot::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.ChannelNo)
  return channelno_;
}
void MDDelaySnapshot::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDDelaySnapshot::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDDelaySnapshot::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDDelaySnapshot::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyPriceQueue)
}
void MDDelaySnapshot::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDDelaySnapshot::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDDelaySnapshot::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDDelaySnapshot::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQtyQueue)
}
void MDDelaySnapshot::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDDelaySnapshot::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDDelaySnapshot::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDDelaySnapshot::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.SellPriceQueue)
}
void MDDelaySnapshot::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDDelaySnapshot::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDDelaySnapshot::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDDelaySnapshot::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQtyQueue)
}
void MDDelaySnapshot::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDDelaySnapshot::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDDelaySnapshot::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDDelaySnapshot::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQueue)
}
void MDDelaySnapshot::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDDelaySnapshot::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDDelaySnapshot::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDDelaySnapshot::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQueue)
}
void MDDelaySnapshot::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDDelaySnapshot::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDDelaySnapshot::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDDelaySnapshot::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyNumOrdersQueue)
}
void MDDelaySnapshot::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDDelaySnapshot::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDDelaySnapshot::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDDelaySnapshot::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDDelaySnapshot::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.SellNumOrdersQueue)
}
void MDDelaySnapshot::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDDelaySnapshot.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDDelaySnapshot::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDDelaySnapshot::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDDelaySnapshot.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
void MDDelaySnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDDelaySnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.DataMultiplePowerOf10)
}

// optional int32 DelaySnapshotType = 60;
void MDDelaySnapshot::clear_delaysnapshottype() {
  delaysnapshottype_ = 0;
}
::google::protobuf::int32 MDDelaySnapshot::delaysnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDDelaySnapshot.DelaySnapshotType)
  return delaysnapshottype_;
}
void MDDelaySnapshot::set_delaysnapshottype(::google::protobuf::int32 value) {
  
  delaysnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDDelaySnapshot.DelaySnapshotType)
}

inline const MDDelaySnapshot* MDDelaySnapshot::internal_default_instance() {
  return &MDDelaySnapshot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
