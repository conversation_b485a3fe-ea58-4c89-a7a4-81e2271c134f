// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCnexQuote.proto

#ifndef PROTOBUF_MDCnexQuote_2eproto__INCLUDED
#define PROTOBUF_MDCnexQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCnexQuote_2eproto();
void protobuf_InitDefaults_MDCnexQuote_2eproto();
void protobuf_AssignDesc_MDCnexQuote_2eproto();
void protobuf_ShutdownFile_MDCnexQuote_2eproto();

class MDBondQuote;
class MDCnexQuote;
class MDCnyRepoQuote;

// ===================================================================

class MDCnexQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCnexQuote) */ {
 public:
  MDCnexQuote();
  virtual ~MDCnexQuote();

  MDCnexQuote(const MDCnexQuote& from);

  inline MDCnexQuote& operator=(const MDCnexQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCnexQuote& default_instance();

  static const MDCnexQuote* internal_default_instance();

  void Swap(MDCnexQuote* other);

  // implements Message ----------------------------------------------

  inline MDCnexQuote* New() const { return New(NULL); }

  MDCnexQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCnexQuote& from);
  void MergeFrom(const MDCnexQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCnexQuote* other);
  void UnsafeMergeFrom(const MDCnexQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int32 QuoteCategory = 7;
  void clear_quotecategory();
  static const int kQuoteCategoryFieldNumber = 7;
  ::google::protobuf::int32 quotecategory() const;
  void set_quotecategory(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
  bool has_bondquote() const;
  void clear_bondquote();
  static const int kBondQuoteFieldNumber = 8;
  const ::com::htsc::mdc::insight::model::MDBondQuote& bondquote() const;
  ::com::htsc::mdc::insight::model::MDBondQuote* mutable_bondquote();
  ::com::htsc::mdc::insight::model::MDBondQuote* release_bondquote();
  void set_allocated_bondquote(::com::htsc::mdc::insight::model::MDBondQuote* bondquote);

  // optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
  bool has_cnyrepoquote() const;
  void clear_cnyrepoquote();
  static const int kCnyRepoQuoteFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::MDCnyRepoQuote& cnyrepoquote() const;
  ::com::htsc::mdc::insight::model::MDCnyRepoQuote* mutable_cnyrepoquote();
  ::com::htsc::mdc::insight::model::MDCnyRepoQuote* release_cnyrepoquote();
  void set_allocated_cnyrepoquote(::com::htsc::mdc::insight::model::MDCnyRepoQuote* cnyrepoquote);

  // optional int32 DataMultiplePowerOf10 = 10;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 10;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCnexQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::com::htsc::mdc::insight::model::MDBondQuote* bondquote_;
  ::com::htsc::mdc::insight::model::MDCnyRepoQuote* cnyrepoquote_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 quotecategory_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCnexQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCnexQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCnexQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCnexQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCnexQuote> MDCnexQuote_default_instance_;

// -------------------------------------------------------------------

class MDBondQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDBondQuote) */ {
 public:
  MDBondQuote();
  virtual ~MDBondQuote();

  MDBondQuote(const MDBondQuote& from);

  inline MDBondQuote& operator=(const MDBondQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDBondQuote& default_instance();

  static const MDBondQuote* internal_default_instance();

  void Swap(MDBondQuote* other);

  // implements Message ----------------------------------------------

  inline MDBondQuote* New() const { return New(NULL); }

  MDBondQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDBondQuote& from);
  void MergeFrom(const MDBondQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDBondQuote* other);
  void UnsafeMergeFrom(const MDBondQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 CnexDataType = 1;
  void clear_cnexdatatype();
  static const int kCnexDataTypeFieldNumber = 1;
  ::google::protobuf::int32 cnexdatatype() const;
  void set_cnexdatatype(::google::protobuf::int32 value);

  // optional int64 IssueDataTime = 2;
  void clear_issuedatatime();
  static const int kIssueDataTimeFieldNumber = 2;
  ::google::protobuf::int64 issuedatatime() const;
  void set_issuedatatime(::google::protobuf::int64 value);

  // optional string QuoteID = 3;
  void clear_quoteid();
  static const int kQuoteIDFieldNumber = 3;
  const ::std::string& quoteid() const;
  void set_quoteid(const ::std::string& value);
  void set_quoteid(const char* value);
  void set_quoteid(const char* value, size_t size);
  ::std::string* mutable_quoteid();
  ::std::string* release_quoteid();
  void set_allocated_quoteid(::std::string* quoteid);

  // optional int32 QuoteType = 4;
  void clear_quotetype();
  static const int kQuoteTypeFieldNumber = 4;
  ::google::protobuf::int32 quotetype() const;
  void set_quotetype(::google::protobuf::int32 value);

  // optional int64 QuotePrice = 5;
  void clear_quoteprice();
  static const int kQuotePriceFieldNumber = 5;
  ::google::protobuf::int64 quoteprice() const;
  void set_quoteprice(::google::protobuf::int64 value);

  // optional int64 QuoteSize = 6;
  void clear_quotesize();
  static const int kQuoteSizeFieldNumber = 6;
  ::google::protobuf::int64 quotesize() const;
  void set_quotesize(::google::protobuf::int64 value);

  // optional int64 Yield = 7;
  void clear_yield();
  static const int kYieldFieldNumber = 7;
  ::google::protobuf::int64 yield() const;
  void set_yield(::google::protobuf::int64 value);

  // optional int32 QuoteDate = 8;
  void clear_quotedate();
  static const int kQuoteDateFieldNumber = 8;
  ::google::protobuf::int32 quotedate() const;
  void set_quotedate(::google::protobuf::int32 value);

  // optional int32 QuoteTime = 9;
  void clear_quotetime();
  static const int kQuoteTimeFieldNumber = 9;
  ::google::protobuf::int32 quotetime() const;
  void set_quotetime(::google::protobuf::int32 value);

  // optional int32 QuoteStatus = 10;
  void clear_quotestatus();
  static const int kQuoteStatusFieldNumber = 10;
  ::google::protobuf::int32 quotestatus() const;
  void set_quotestatus(::google::protobuf::int32 value);

  // optional int32 QuotePriceType = 11;
  void clear_quotepricetype();
  static const int kQuotePriceTypeFieldNumber = 11;
  ::google::protobuf::int32 quotepricetype() const;
  void set_quotepricetype(::google::protobuf::int32 value);

  // optional int32 MaturityDate = 12;
  void clear_maturitydate();
  static const int kMaturityDateFieldNumber = 12;
  ::google::protobuf::int32 maturitydate() const;
  void set_maturitydate(::google::protobuf::int32 value);

  // optional string CnexSecurityType = 13;
  void clear_cnexsecuritytype();
  static const int kCnexSecurityTypeFieldNumber = 13;
  const ::std::string& cnexsecuritytype() const;
  void set_cnexsecuritytype(const ::std::string& value);
  void set_cnexsecuritytype(const char* value);
  void set_cnexsecuritytype(const char* value, size_t size);
  ::std::string* mutable_cnexsecuritytype();
  ::std::string* release_cnexsecuritytype();
  void set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype);

  // optional string CreditRating = 14;
  void clear_creditrating();
  static const int kCreditRatingFieldNumber = 14;
  const ::std::string& creditrating() const;
  void set_creditrating(const ::std::string& value);
  void set_creditrating(const char* value);
  void set_creditrating(const char* value, size_t size);
  ::std::string* mutable_creditrating();
  ::std::string* release_creditrating();
  void set_allocated_creditrating(::std::string* creditrating);

  // optional string Text = 15;
  void clear_text();
  static const int kTextFieldNumber = 15;
  const ::std::string& text() const;
  void set_text(const ::std::string& value);
  void set_text(const char* value);
  void set_text(const char* value, size_t size);
  ::std::string* mutable_text();
  ::std::string* release_text();
  void set_allocated_text(::std::string* text);

  // optional string MaturityMonthYear = 16;
  void clear_maturitymonthyear();
  static const int kMaturityMonthYearFieldNumber = 16;
  const ::std::string& maturitymonthyear() const;
  void set_maturitymonthyear(const ::std::string& value);
  void set_maturitymonthyear(const char* value);
  void set_maturitymonthyear(const char* value, size_t size);
  ::std::string* mutable_maturitymonthyear();
  ::std::string* release_maturitymonthyear();
  void set_allocated_maturitymonthyear(::std::string* maturitymonthyear);

  // optional int32 UnderlyingPrice = 17;
  void clear_underlyingprice();
  static const int kUnderlyingPriceFieldNumber = 17;
  ::google::protobuf::int32 underlyingprice() const;
  void set_underlyingprice(::google::protobuf::int32 value);

  // optional string MatchId = 18;
  void clear_matchid();
  static const int kMatchIdFieldNumber = 18;
  const ::std::string& matchid() const;
  void set_matchid(const ::std::string& value);
  void set_matchid(const char* value);
  void set_matchid(const char* value, size_t size);
  ::std::string* mutable_matchid();
  ::std::string* release_matchid();
  void set_allocated_matchid(::std::string* matchid);

  // optional int32 WorkBench = 19;
  void clear_workbench();
  static const int kWorkBenchFieldNumber = 19;
  ::google::protobuf::int32 workbench() const;
  void set_workbench(::google::protobuf::int32 value);

  // optional string Tenor = 20;
  void clear_tenor();
  static const int kTenorFieldNumber = 20;
  const ::std::string& tenor() const;
  void set_tenor(const ::std::string& value);
  void set_tenor(const char* value);
  void set_tenor(const char* value, size_t size);
  ::std::string* mutable_tenor();
  ::std::string* release_tenor();
  void set_allocated_tenor(::std::string* tenor);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDBondQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr quoteid_;
  ::google::protobuf::internal::ArenaStringPtr cnexsecuritytype_;
  ::google::protobuf::internal::ArenaStringPtr creditrating_;
  ::google::protobuf::internal::ArenaStringPtr text_;
  ::google::protobuf::internal::ArenaStringPtr maturitymonthyear_;
  ::google::protobuf::internal::ArenaStringPtr matchid_;
  ::google::protobuf::internal::ArenaStringPtr tenor_;
  ::google::protobuf::int64 issuedatatime_;
  ::google::protobuf::int32 cnexdatatype_;
  ::google::protobuf::int32 quotetype_;
  ::google::protobuf::int64 quoteprice_;
  ::google::protobuf::int64 quotesize_;
  ::google::protobuf::int64 yield_;
  ::google::protobuf::int32 quotedate_;
  ::google::protobuf::int32 quotetime_;
  ::google::protobuf::int32 quotestatus_;
  ::google::protobuf::int32 quotepricetype_;
  ::google::protobuf::int32 maturitydate_;
  ::google::protobuf::int32 underlyingprice_;
  ::google::protobuf::int32 workbench_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCnexQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCnexQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCnexQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCnexQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDBondQuote> MDBondQuote_default_instance_;

// -------------------------------------------------------------------

class MDCnyRepoQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCnyRepoQuote) */ {
 public:
  MDCnyRepoQuote();
  virtual ~MDCnyRepoQuote();

  MDCnyRepoQuote(const MDCnyRepoQuote& from);

  inline MDCnyRepoQuote& operator=(const MDCnyRepoQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCnyRepoQuote& default_instance();

  static const MDCnyRepoQuote* internal_default_instance();

  void Swap(MDCnyRepoQuote* other);

  // implements Message ----------------------------------------------

  inline MDCnyRepoQuote* New() const { return New(NULL); }

  MDCnyRepoQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCnyRepoQuote& from);
  void MergeFrom(const MDCnyRepoQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCnyRepoQuote* other);
  void UnsafeMergeFrom(const MDCnyRepoQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 CnexDataType = 1;
  void clear_cnexdatatype();
  static const int kCnexDataTypeFieldNumber = 1;
  ::google::protobuf::int32 cnexdatatype() const;
  void set_cnexdatatype(::google::protobuf::int32 value);

  // optional int64 IssueDataTime = 2;
  void clear_issuedatatime();
  static const int kIssueDataTimeFieldNumber = 2;
  ::google::protobuf::int64 issuedatatime() const;
  void set_issuedatatime(::google::protobuf::int64 value);

  // optional string QuoteID = 3;
  void clear_quoteid();
  static const int kQuoteIDFieldNumber = 3;
  const ::std::string& quoteid() const;
  void set_quoteid(const ::std::string& value);
  void set_quoteid(const char* value);
  void set_quoteid(const char* value, size_t size);
  ::std::string* mutable_quoteid();
  ::std::string* release_quoteid();
  void set_allocated_quoteid(::std::string* quoteid);

  // optional int32 QuoteType = 4;
  void clear_quotetype();
  static const int kQuoteTypeFieldNumber = 4;
  ::google::protobuf::int32 quotetype() const;
  void set_quotetype(::google::protobuf::int32 value);

  // optional int32 QuoteDate = 5;
  void clear_quotedate();
  static const int kQuoteDateFieldNumber = 5;
  ::google::protobuf::int32 quotedate() const;
  void set_quotedate(::google::protobuf::int32 value);

  // optional int32 QuoteTime = 6;
  void clear_quotetime();
  static const int kQuoteTimeFieldNumber = 6;
  ::google::protobuf::int32 quotetime() const;
  void set_quotetime(::google::protobuf::int32 value);

  // optional int32 QuoteStatus = 7;
  void clear_quotestatus();
  static const int kQuoteStatusFieldNumber = 7;
  ::google::protobuf::int32 quotestatus() const;
  void set_quotestatus(::google::protobuf::int32 value);

  // optional int32 QuotePriceType = 8;
  void clear_quotepricetype();
  static const int kQuotePriceTypeFieldNumber = 8;
  ::google::protobuf::int32 quotepricetype() const;
  void set_quotepricetype(::google::protobuf::int32 value);

  // optional int64 CreateDate = 9;
  void clear_createdate();
  static const int kCreateDateFieldNumber = 9;
  ::google::protobuf::int64 createdate() const;
  void set_createdate(::google::protobuf::int64 value);

  // optional int64 ModifyDate = 10;
  void clear_modifydate();
  static const int kModifyDateFieldNumber = 10;
  ::google::protobuf::int64 modifydate() const;
  void set_modifydate(::google::protobuf::int64 value);

  // optional int32 BaseTerm = 11;
  void clear_baseterm();
  static const int kBaseTermFieldNumber = 11;
  ::google::protobuf::int32 baseterm() const;
  void set_baseterm(::google::protobuf::int32 value);

  // optional int32 FloatType = 12;
  void clear_floattype();
  static const int kFloatTypeFieldNumber = 12;
  ::google::protobuf::int32 floattype() const;
  void set_floattype(::google::protobuf::int32 value);

  // optional string FloatPrice = 13;
  void clear_floatprice();
  static const int kFloatPriceFieldNumber = 13;
  const ::std::string& floatprice() const;
  void set_floatprice(const ::std::string& value);
  void set_floatprice(const char* value);
  void set_floatprice(const char* value, size_t size);
  ::std::string* mutable_floatprice();
  ::std::string* release_floatprice();
  void set_allocated_floatprice(::std::string* floatprice);

  // optional string FixedRate = 14;
  void clear_fixedrate();
  static const int kFixedRateFieldNumber = 14;
  const ::std::string& fixedrate() const;
  void set_fixedrate(const ::std::string& value);
  void set_fixedrate(const char* value);
  void set_fixedrate(const char* value, size_t size);
  ::std::string* mutable_fixedrate();
  ::std::string* release_fixedrate();
  void set_allocated_fixedrate(::std::string* fixedrate);

  // optional string Volume = 15;
  void clear_volume();
  static const int kVolumeFieldNumber = 15;
  const ::std::string& volume() const;
  void set_volume(const ::std::string& value);
  void set_volume(const char* value);
  void set_volume(const char* value, size_t size);
  ::std::string* mutable_volume();
  ::std::string* release_volume();
  void set_allocated_volume(::std::string* volume);

  // optional int32 VolumeAboveOrBelow = 16;
  void clear_volumeaboveorbelow();
  static const int kVolumeAboveOrBelowFieldNumber = 16;
  ::google::protobuf::int32 volumeaboveorbelow() const;
  void set_volumeaboveorbelow(::google::protobuf::int32 value);

  // optional int32 VolCanSplit = 17;
  void clear_volcansplit();
  static const int kVolCanSplitFieldNumber = 17;
  ::google::protobuf::int32 volcansplit() const;
  void set_volcansplit(::google::protobuf::int32 value);

  // optional string SpecialTerm = 18;
  void clear_specialterm();
  static const int kSpecialTermFieldNumber = 18;
  const ::std::string& specialterm() const;
  void set_specialterm(const ::std::string& value);
  void set_specialterm(const char* value);
  void set_specialterm(const char* value, size_t size);
  ::std::string* mutable_specialterm();
  ::std::string* release_specialterm();
  void set_allocated_specialterm(::std::string* specialterm);

  // optional int32 At1 = 19;
  void clear_at1();
  static const int kAt1FieldNumber = 19;
  ::google::protobuf::int32 at1() const;
  void set_at1(::google::protobuf::int32 value);

  // optional int32 At2 = 20;
  void clear_at2();
  static const int kAt2FieldNumber = 20;
  ::google::protobuf::int32 at2() const;
  void set_at2(::google::protobuf::int32 value);

  // optional int32 ATCreditRating = 21;
  void clear_atcreditrating();
  static const int kATCreditRatingFieldNumber = 21;
  ::google::protobuf::int32 atcreditrating() const;
  void set_atcreditrating(::google::protobuf::int32 value);

  // optional int32 ATBankLimited = 22;
  void clear_atbanklimited();
  static const int kATBankLimitedFieldNumber = 22;
  ::google::protobuf::int32 atbanklimited() const;
  void set_atbanklimited(::google::protobuf::int32 value);

  // optional string ZhiLian = 23;
  void clear_zhilian();
  static const int kZhiLianFieldNumber = 23;
  const ::std::string& zhilian() const;
  void set_zhilian(const ::std::string& value);
  void set_zhilian(const char* value);
  void set_zhilian(const char* value, size_t size);
  ::std::string* mutable_zhilian();
  ::std::string* release_zhilian();
  void set_allocated_zhilian(::std::string* zhilian);

  // optional string UnderwriterLevel1 = 24;
  void clear_underwriterlevel1();
  static const int kUnderwriterLevel1FieldNumber = 24;
  const ::std::string& underwriterlevel1() const;
  void set_underwriterlevel1(const ::std::string& value);
  void set_underwriterlevel1(const char* value);
  void set_underwriterlevel1(const char* value, size_t size);
  ::std::string* mutable_underwriterlevel1();
  ::std::string* release_underwriterlevel1();
  void set_allocated_underwriterlevel1(::std::string* underwriterlevel1);

  // optional string ATAdd = 25;
  void clear_atadd();
  static const int kATAddFieldNumber = 25;
  const ::std::string& atadd() const;
  void set_atadd(const ::std::string& value);
  void set_atadd(const char* value);
  void set_atadd(const char* value, size_t size);
  ::std::string* mutable_atadd();
  ::std::string* release_atadd();
  void set_allocated_atadd(::std::string* atadd);

  // optional string Dealtype = 26;
  void clear_dealtype();
  static const int kDealtypeFieldNumber = 26;
  const ::std::string& dealtype() const;
  void set_dealtype(const ::std::string& value);
  void set_dealtype(const char* value);
  void set_dealtype(const char* value, size_t size);
  ::std::string* mutable_dealtype();
  ::std::string* release_dealtype();
  void set_allocated_dealtype(::std::string* dealtype);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCnyRepoQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr quoteid_;
  ::google::protobuf::internal::ArenaStringPtr floatprice_;
  ::google::protobuf::internal::ArenaStringPtr fixedrate_;
  ::google::protobuf::internal::ArenaStringPtr volume_;
  ::google::protobuf::internal::ArenaStringPtr specialterm_;
  ::google::protobuf::internal::ArenaStringPtr zhilian_;
  ::google::protobuf::internal::ArenaStringPtr underwriterlevel1_;
  ::google::protobuf::internal::ArenaStringPtr atadd_;
  ::google::protobuf::internal::ArenaStringPtr dealtype_;
  ::google::protobuf::int64 issuedatatime_;
  ::google::protobuf::int32 cnexdatatype_;
  ::google::protobuf::int32 quotetype_;
  ::google::protobuf::int32 quotedate_;
  ::google::protobuf::int32 quotetime_;
  ::google::protobuf::int32 quotestatus_;
  ::google::protobuf::int32 quotepricetype_;
  ::google::protobuf::int64 createdate_;
  ::google::protobuf::int64 modifydate_;
  ::google::protobuf::int32 baseterm_;
  ::google::protobuf::int32 floattype_;
  ::google::protobuf::int32 volumeaboveorbelow_;
  ::google::protobuf::int32 volcansplit_;
  ::google::protobuf::int32 at1_;
  ::google::protobuf::int32 at2_;
  ::google::protobuf::int32 atcreditrating_;
  ::google::protobuf::int32 atbanklimited_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCnexQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCnexQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCnexQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCnexQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCnyRepoQuote> MDCnyRepoQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCnexQuote

// optional string HTSCSecurityID = 1;
inline void MDCnexQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnexQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
inline void MDCnexQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
inline void MDCnexQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}
inline ::std::string* MDCnexQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnexQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnexQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCnexQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCnexQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.MDDate)
  return mddate_;
}
inline void MDCnexQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCnexQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCnexQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.MDTime)
  return mdtime_;
}
inline void MDCnexQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCnexQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnexQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDCnexQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCnexQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCnexQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCnexQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCnexQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCnexQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCnexQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.securityType)
}

// optional int32 QuoteCategory = 7;
inline void MDCnexQuote::clear_quotecategory() {
  quotecategory_ = 0;
}
inline ::google::protobuf::int32 MDCnexQuote::quotecategory() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.QuoteCategory)
  return quotecategory_;
}
inline void MDCnexQuote::set_quotecategory(::google::protobuf::int32 value) {
  
  quotecategory_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.QuoteCategory)
}

// optional .com.htsc.mdc.insight.model.MDBondQuote BondQuote = 8;
inline bool MDCnexQuote::has_bondquote() const {
  return this != internal_default_instance() && bondquote_ != NULL;
}
inline void MDCnexQuote::clear_bondquote() {
  if (GetArenaNoVirtual() == NULL && bondquote_ != NULL) delete bondquote_;
  bondquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDBondQuote& MDCnexQuote::bondquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  return bondquote_ != NULL ? *bondquote_
                         : *::com::htsc::mdc::insight::model::MDBondQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDBondQuote* MDCnexQuote::mutable_bondquote() {
  
  if (bondquote_ == NULL) {
    bondquote_ = new ::com::htsc::mdc::insight::model::MDBondQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  return bondquote_;
}
inline ::com::htsc::mdc::insight::model::MDBondQuote* MDCnexQuote::release_bondquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
  
  ::com::htsc::mdc::insight::model::MDBondQuote* temp = bondquote_;
  bondquote_ = NULL;
  return temp;
}
inline void MDCnexQuote::set_allocated_bondquote(::com::htsc::mdc::insight::model::MDBondQuote* bondquote) {
  delete bondquote_;
  bondquote_ = bondquote;
  if (bondquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.BondQuote)
}

// optional .com.htsc.mdc.insight.model.MDCnyRepoQuote CnyRepoQuote = 9;
inline bool MDCnexQuote::has_cnyrepoquote() const {
  return this != internal_default_instance() && cnyrepoquote_ != NULL;
}
inline void MDCnexQuote::clear_cnyrepoquote() {
  if (GetArenaNoVirtual() == NULL && cnyrepoquote_ != NULL) delete cnyrepoquote_;
  cnyrepoquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCnyRepoQuote& MDCnexQuote::cnyrepoquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  return cnyrepoquote_ != NULL ? *cnyrepoquote_
                         : *::com::htsc::mdc::insight::model::MDCnyRepoQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCnyRepoQuote* MDCnexQuote::mutable_cnyrepoquote() {
  
  if (cnyrepoquote_ == NULL) {
    cnyrepoquote_ = new ::com::htsc::mdc::insight::model::MDCnyRepoQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  return cnyrepoquote_;
}
inline ::com::htsc::mdc::insight::model::MDCnyRepoQuote* MDCnexQuote::release_cnyrepoquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
  
  ::com::htsc::mdc::insight::model::MDCnyRepoQuote* temp = cnyrepoquote_;
  cnyrepoquote_ = NULL;
  return temp;
}
inline void MDCnexQuote::set_allocated_cnyrepoquote(::com::htsc::mdc::insight::model::MDCnyRepoQuote* cnyrepoquote) {
  delete cnyrepoquote_;
  cnyrepoquote_ = cnyrepoquote;
  if (cnyrepoquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexQuote.CnyRepoQuote)
}

// optional int32 DataMultiplePowerOf10 = 10;
inline void MDCnexQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCnexQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCnexQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexQuote.DataMultiplePowerOf10)
}

inline const MDCnexQuote* MDCnexQuote::internal_default_instance() {
  return &MDCnexQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// MDBondQuote

// optional int32 CnexDataType = 1;
inline void MDBondQuote::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CnexDataType)
  return cnexdatatype_;
}
inline void MDBondQuote::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CnexDataType)
}

// optional int64 IssueDataTime = 2;
inline void MDBondQuote::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBondQuote::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.IssueDataTime)
  return issuedatatime_;
}
inline void MDBondQuote::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.IssueDataTime)
}

// optional string QuoteID = 3;
inline void MDBondQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
inline void MDBondQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
inline void MDBondQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}
inline ::std::string* MDBondQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.QuoteID)
}

// optional int32 QuoteType = 4;
inline void MDBondQuote::clear_quotetype() {
  quotetype_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteType)
  return quotetype_;
}
inline void MDBondQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteType)
}

// optional int64 QuotePrice = 5;
inline void MDBondQuote::clear_quoteprice() {
  quoteprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBondQuote::quoteprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuotePrice)
  return quoteprice_;
}
inline void MDBondQuote::set_quoteprice(::google::protobuf::int64 value) {
  
  quoteprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuotePrice)
}

// optional int64 QuoteSize = 6;
inline void MDBondQuote::clear_quotesize() {
  quotesize_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBondQuote::quotesize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteSize)
  return quotesize_;
}
inline void MDBondQuote::set_quotesize(::google::protobuf::int64 value) {
  
  quotesize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteSize)
}

// optional int64 Yield = 7;
inline void MDBondQuote::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBondQuote::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Yield)
  return yield_;
}
inline void MDBondQuote::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Yield)
}

// optional int32 QuoteDate = 8;
inline void MDBondQuote::clear_quotedate() {
  quotedate_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteDate)
  return quotedate_;
}
inline void MDBondQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteDate)
}

// optional int32 QuoteTime = 9;
inline void MDBondQuote::clear_quotetime() {
  quotetime_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteTime)
  return quotetime_;
}
inline void MDBondQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteTime)
}

// optional int32 QuoteStatus = 10;
inline void MDBondQuote::clear_quotestatus() {
  quotestatus_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuoteStatus)
  return quotestatus_;
}
inline void MDBondQuote::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuoteStatus)
}

// optional int32 QuotePriceType = 11;
inline void MDBondQuote::clear_quotepricetype() {
  quotepricetype_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.QuotePriceType)
  return quotepricetype_;
}
inline void MDBondQuote::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.QuotePriceType)
}

// optional int32 MaturityDate = 12;
inline void MDBondQuote::clear_maturitydate() {
  maturitydate_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MaturityDate)
  return maturitydate_;
}
inline void MDBondQuote::set_maturitydate(::google::protobuf::int32 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MaturityDate)
}

// optional string CnexSecurityType = 13;
inline void MDBondQuote::clear_cnexsecuritytype() {
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::cnexsecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  return cnexsecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_cnexsecuritytype(const ::std::string& value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
inline void MDBondQuote::set_cnexsecuritytype(const char* value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
inline void MDBondQuote::set_cnexsecuritytype(const char* value, size_t size) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}
inline ::std::string* MDBondQuote::mutable_cnexsecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  return cnexsecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_cnexsecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
  
  return cnexsecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype) {
  if (cnexsecuritytype != NULL) {
    
  } else {
    
  }
  cnexsecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cnexsecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.CnexSecurityType)
}

// optional string CreditRating = 14;
inline void MDBondQuote::clear_creditrating() {
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::creditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  return creditrating_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_creditrating(const ::std::string& value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
inline void MDBondQuote::set_creditrating(const char* value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
inline void MDBondQuote::set_creditrating(const char* value, size_t size) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}
inline ::std::string* MDBondQuote::mutable_creditrating() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  return creditrating_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_creditrating() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
  
  return creditrating_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_creditrating(::std::string* creditrating) {
  if (creditrating != NULL) {
    
  } else {
    
  }
  creditrating_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creditrating);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.CreditRating)
}

// optional string Text = 15;
inline void MDBondQuote::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::text() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
inline void MDBondQuote::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
inline void MDBondQuote::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.Text)
}
inline ::std::string* MDBondQuote::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.Text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_text() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.Text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.Text)
}

// optional string MaturityMonthYear = 16;
inline void MDBondQuote::clear_maturitymonthyear() {
  maturitymonthyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::maturitymonthyear() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  return maturitymonthyear_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_maturitymonthyear(const ::std::string& value) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
inline void MDBondQuote::set_maturitymonthyear(const char* value) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
inline void MDBondQuote::set_maturitymonthyear(const char* value, size_t size) {
  
  maturitymonthyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}
inline ::std::string* MDBondQuote::mutable_maturitymonthyear() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  return maturitymonthyear_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_maturitymonthyear() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
  
  return maturitymonthyear_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_maturitymonthyear(::std::string* maturitymonthyear) {
  if (maturitymonthyear != NULL) {
    
  } else {
    
  }
  maturitymonthyear_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), maturitymonthyear);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.MaturityMonthYear)
}

// optional int32 UnderlyingPrice = 17;
inline void MDBondQuote::clear_underlyingprice() {
  underlyingprice_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::underlyingprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.UnderlyingPrice)
  return underlyingprice_;
}
inline void MDBondQuote::set_underlyingprice(::google::protobuf::int32 value) {
  
  underlyingprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.UnderlyingPrice)
}

// optional string MatchId = 18;
inline void MDBondQuote::clear_matchid() {
  matchid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::matchid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  return matchid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_matchid(const ::std::string& value) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
inline void MDBondQuote::set_matchid(const char* value) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
inline void MDBondQuote::set_matchid(const char* value, size_t size) {
  
  matchid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}
inline ::std::string* MDBondQuote::mutable_matchid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  return matchid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_matchid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
  
  return matchid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_matchid(::std::string* matchid) {
  if (matchid != NULL) {
    
  } else {
    
  }
  matchid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), matchid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.MatchId)
}

// optional int32 WorkBench = 19;
inline void MDBondQuote::clear_workbench() {
  workbench_ = 0;
}
inline ::google::protobuf::int32 MDBondQuote::workbench() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.WorkBench)
  return workbench_;
}
inline void MDBondQuote::set_workbench(::google::protobuf::int32 value) {
  
  workbench_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.WorkBench)
}

// optional string Tenor = 20;
inline void MDBondQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBondQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
inline void MDBondQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
inline void MDBondQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}
inline ::std::string* MDBondQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBondQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBondQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBondQuote.Tenor)
}

inline const MDBondQuote* MDBondQuote::internal_default_instance() {
  return &MDBondQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// MDCnyRepoQuote

// optional int32 CnexDataType = 1;
inline void MDCnyRepoQuote::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.CnexDataType)
  return cnexdatatype_;
}
inline void MDCnyRepoQuote::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.CnexDataType)
}

// optional int64 IssueDataTime = 2;
inline void MDCnyRepoQuote::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnyRepoQuote::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.IssueDataTime)
  return issuedatatime_;
}
inline void MDCnyRepoQuote::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.IssueDataTime)
}

// optional string QuoteID = 3;
inline void MDCnyRepoQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
inline void MDCnyRepoQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
inline void MDCnyRepoQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}
inline ::std::string* MDCnyRepoQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteID)
}

// optional int32 QuoteType = 4;
inline void MDCnyRepoQuote::clear_quotetype() {
  quotetype_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteType)
  return quotetype_;
}
inline void MDCnyRepoQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteType)
}

// optional int32 QuoteDate = 5;
inline void MDCnyRepoQuote::clear_quotedate() {
  quotedate_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteDate)
  return quotedate_;
}
inline void MDCnyRepoQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteDate)
}

// optional int32 QuoteTime = 6;
inline void MDCnyRepoQuote::clear_quotetime() {
  quotetime_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteTime)
  return quotetime_;
}
inline void MDCnyRepoQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteTime)
}

// optional int32 QuoteStatus = 7;
inline void MDCnyRepoQuote::clear_quotestatus() {
  quotestatus_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteStatus)
  return quotestatus_;
}
inline void MDCnyRepoQuote::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuoteStatus)
}

// optional int32 QuotePriceType = 8;
inline void MDCnyRepoQuote::clear_quotepricetype() {
  quotepricetype_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuotePriceType)
  return quotepricetype_;
}
inline void MDCnyRepoQuote::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.QuotePriceType)
}

// optional int64 CreateDate = 9;
inline void MDCnyRepoQuote::clear_createdate() {
  createdate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnyRepoQuote::createdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.CreateDate)
  return createdate_;
}
inline void MDCnyRepoQuote::set_createdate(::google::protobuf::int64 value) {
  
  createdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.CreateDate)
}

// optional int64 ModifyDate = 10;
inline void MDCnyRepoQuote::clear_modifydate() {
  modifydate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCnyRepoQuote::modifydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ModifyDate)
  return modifydate_;
}
inline void MDCnyRepoQuote::set_modifydate(::google::protobuf::int64 value) {
  
  modifydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ModifyDate)
}

// optional int32 BaseTerm = 11;
inline void MDCnyRepoQuote::clear_baseterm() {
  baseterm_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::baseterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.BaseTerm)
  return baseterm_;
}
inline void MDCnyRepoQuote::set_baseterm(::google::protobuf::int32 value) {
  
  baseterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.BaseTerm)
}

// optional int32 FloatType = 12;
inline void MDCnyRepoQuote::clear_floattype() {
  floattype_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::floattype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatType)
  return floattype_;
}
inline void MDCnyRepoQuote::set_floattype(::google::protobuf::int32 value) {
  
  floattype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatType)
}

// optional string FloatPrice = 13;
inline void MDCnyRepoQuote::clear_floatprice() {
  floatprice_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::floatprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  return floatprice_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_floatprice(const ::std::string& value) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
inline void MDCnyRepoQuote::set_floatprice(const char* value) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
inline void MDCnyRepoQuote::set_floatprice(const char* value, size_t size) {
  
  floatprice_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}
inline ::std::string* MDCnyRepoQuote::mutable_floatprice() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  return floatprice_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_floatprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
  
  return floatprice_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_floatprice(::std::string* floatprice) {
  if (floatprice != NULL) {
    
  } else {
    
  }
  floatprice_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), floatprice);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.FloatPrice)
}

// optional string FixedRate = 14;
inline void MDCnyRepoQuote::clear_fixedrate() {
  fixedrate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::fixedrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  return fixedrate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_fixedrate(const ::std::string& value) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
inline void MDCnyRepoQuote::set_fixedrate(const char* value) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
inline void MDCnyRepoQuote::set_fixedrate(const char* value, size_t size) {
  
  fixedrate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}
inline ::std::string* MDCnyRepoQuote::mutable_fixedrate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  return fixedrate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_fixedrate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
  
  return fixedrate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_fixedrate(::std::string* fixedrate) {
  if (fixedrate != NULL) {
    
  } else {
    
  }
  fixedrate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fixedrate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.FixedRate)
}

// optional string Volume = 15;
inline void MDCnyRepoQuote::clear_volume() {
  volume_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  return volume_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_volume(const ::std::string& value) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
inline void MDCnyRepoQuote::set_volume(const char* value) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
inline void MDCnyRepoQuote::set_volume(const char* value, size_t size) {
  
  volume_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}
inline ::std::string* MDCnyRepoQuote::mutable_volume() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  return volume_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_volume() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
  
  return volume_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_volume(::std::string* volume) {
  if (volume != NULL) {
    
  } else {
    
  }
  volume_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volume);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.Volume)
}

// optional int32 VolumeAboveOrBelow = 16;
inline void MDCnyRepoQuote::clear_volumeaboveorbelow() {
  volumeaboveorbelow_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::volumeaboveorbelow() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolumeAboveOrBelow)
  return volumeaboveorbelow_;
}
inline void MDCnyRepoQuote::set_volumeaboveorbelow(::google::protobuf::int32 value) {
  
  volumeaboveorbelow_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolumeAboveOrBelow)
}

// optional int32 VolCanSplit = 17;
inline void MDCnyRepoQuote::clear_volcansplit() {
  volcansplit_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::volcansplit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolCanSplit)
  return volcansplit_;
}
inline void MDCnyRepoQuote::set_volcansplit(::google::protobuf::int32 value) {
  
  volcansplit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.VolCanSplit)
}

// optional string SpecialTerm = 18;
inline void MDCnyRepoQuote::clear_specialterm() {
  specialterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::specialterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  return specialterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_specialterm(const ::std::string& value) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
inline void MDCnyRepoQuote::set_specialterm(const char* value) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
inline void MDCnyRepoQuote::set_specialterm(const char* value, size_t size) {
  
  specialterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}
inline ::std::string* MDCnyRepoQuote::mutable_specialterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  return specialterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_specialterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
  
  return specialterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_specialterm(::std::string* specialterm) {
  if (specialterm != NULL) {
    
  } else {
    
  }
  specialterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), specialterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.SpecialTerm)
}

// optional int32 At1 = 19;
inline void MDCnyRepoQuote::clear_at1() {
  at1_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::at1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.At1)
  return at1_;
}
inline void MDCnyRepoQuote::set_at1(::google::protobuf::int32 value) {
  
  at1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.At1)
}

// optional int32 At2 = 20;
inline void MDCnyRepoQuote::clear_at2() {
  at2_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::at2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.At2)
  return at2_;
}
inline void MDCnyRepoQuote::set_at2(::google::protobuf::int32 value) {
  
  at2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.At2)
}

// optional int32 ATCreditRating = 21;
inline void MDCnyRepoQuote::clear_atcreditrating() {
  atcreditrating_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::atcreditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATCreditRating)
  return atcreditrating_;
}
inline void MDCnyRepoQuote::set_atcreditrating(::google::protobuf::int32 value) {
  
  atcreditrating_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATCreditRating)
}

// optional int32 ATBankLimited = 22;
inline void MDCnyRepoQuote::clear_atbanklimited() {
  atbanklimited_ = 0;
}
inline ::google::protobuf::int32 MDCnyRepoQuote::atbanklimited() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATBankLimited)
  return atbanklimited_;
}
inline void MDCnyRepoQuote::set_atbanklimited(::google::protobuf::int32 value) {
  
  atbanklimited_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATBankLimited)
}

// optional string ZhiLian = 23;
inline void MDCnyRepoQuote::clear_zhilian() {
  zhilian_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::zhilian() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  return zhilian_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_zhilian(const ::std::string& value) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
inline void MDCnyRepoQuote::set_zhilian(const char* value) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
inline void MDCnyRepoQuote::set_zhilian(const char* value, size_t size) {
  
  zhilian_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}
inline ::std::string* MDCnyRepoQuote::mutable_zhilian() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  return zhilian_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_zhilian() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
  
  return zhilian_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_zhilian(::std::string* zhilian) {
  if (zhilian != NULL) {
    
  } else {
    
  }
  zhilian_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), zhilian);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.ZhiLian)
}

// optional string UnderwriterLevel1 = 24;
inline void MDCnyRepoQuote::clear_underwriterlevel1() {
  underwriterlevel1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::underwriterlevel1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  return underwriterlevel1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_underwriterlevel1(const ::std::string& value) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
inline void MDCnyRepoQuote::set_underwriterlevel1(const char* value) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
inline void MDCnyRepoQuote::set_underwriterlevel1(const char* value, size_t size) {
  
  underwriterlevel1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}
inline ::std::string* MDCnyRepoQuote::mutable_underwriterlevel1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  return underwriterlevel1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_underwriterlevel1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
  
  return underwriterlevel1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_underwriterlevel1(::std::string* underwriterlevel1) {
  if (underwriterlevel1 != NULL) {
    
  } else {
    
  }
  underwriterlevel1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underwriterlevel1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.UnderwriterLevel1)
}

// optional string ATAdd = 25;
inline void MDCnyRepoQuote::clear_atadd() {
  atadd_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::atadd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  return atadd_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_atadd(const ::std::string& value) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
inline void MDCnyRepoQuote::set_atadd(const char* value) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
inline void MDCnyRepoQuote::set_atadd(const char* value, size_t size) {
  
  atadd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}
inline ::std::string* MDCnyRepoQuote::mutable_atadd() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  return atadd_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_atadd() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
  
  return atadd_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_atadd(::std::string* atadd) {
  if (atadd != NULL) {
    
  } else {
    
  }
  atadd_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), atadd);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.ATAdd)
}

// optional string Dealtype = 26;
inline void MDCnyRepoQuote::clear_dealtype() {
  dealtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCnyRepoQuote::dealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  return dealtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_dealtype(const ::std::string& value) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
inline void MDCnyRepoQuote::set_dealtype(const char* value) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
inline void MDCnyRepoQuote::set_dealtype(const char* value, size_t size) {
  
  dealtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}
inline ::std::string* MDCnyRepoQuote::mutable_dealtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  return dealtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCnyRepoQuote::release_dealtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
  
  return dealtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCnyRepoQuote::set_allocated_dealtype(::std::string* dealtype) {
  if (dealtype != NULL) {
    
  } else {
    
  }
  dealtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnyRepoQuote.Dealtype)
}

inline const MDCnyRepoQuote* MDCnyRepoQuote::internal_default_instance() {
  return &MDCnyRepoQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCnexQuote_2eproto__INCLUDED
