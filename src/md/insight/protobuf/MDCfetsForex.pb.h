// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsForex.proto

#ifndef PROTOBUF_MDCfetsForex_2eproto__INCLUDED
#define PROTOBUF_MDCfetsForex_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsForex_2eproto();
void protobuf_InitDefaults_MDCfetsForex_2eproto();
void protobuf_AssignDesc_MDCfetsForex_2eproto();
void protobuf_ShutdownFile_MDCfetsForex_2eproto();

class ForwardForex;
class MDCfetsForex;
class NonDeliverableForwardsForex;
class OptionForex;
class SpotClosePriceForex;
class SpotForex;
class SwapForex;

// ===================================================================

class MDCfetsForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsForex) */ {
 public:
  MDCfetsForex();
  virtual ~MDCfetsForex();

  MDCfetsForex(const MDCfetsForex& from);

  inline MDCfetsForex& operator=(const MDCfetsForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsForex& default_instance();

  static const MDCfetsForex* internal_default_instance();

  void Swap(MDCfetsForex* other);

  // implements Message ----------------------------------------------

  inline MDCfetsForex* New() const { return New(NULL); }

  MDCfetsForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsForex& from);
  void MergeFrom(const MDCfetsForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsForex* other);
  void UnsafeMergeFrom(const MDCfetsForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string SecuritySubType = 7;
  void clear_securitysubtype();
  static const int kSecuritySubTypeFieldNumber = 7;
  const ::std::string& securitysubtype() const;
  void set_securitysubtype(const ::std::string& value);
  void set_securitysubtype(const char* value);
  void set_securitysubtype(const char* value, size_t size);
  ::std::string* mutable_securitysubtype();
  ::std::string* release_securitysubtype();
  void set_allocated_securitysubtype(::std::string* securitysubtype);

  // optional int32 ForexType = 8;
  void clear_forextype();
  static const int kForexTypeFieldNumber = 8;
  ::google::protobuf::int32 forextype() const;
  void set_forextype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
  bool has_spotforex() const;
  void clear_spotforex();
  static const int kSpotForexFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::SpotForex& spotforex() const;
  ::com::htsc::mdc::insight::model::SpotForex* mutable_spotforex();
  ::com::htsc::mdc::insight::model::SpotForex* release_spotforex();
  void set_allocated_spotforex(::com::htsc::mdc::insight::model::SpotForex* spotforex);

  // optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
  bool has_forwardforex() const;
  void clear_forwardforex();
  static const int kForwardForexFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::ForwardForex& forwardforex() const;
  ::com::htsc::mdc::insight::model::ForwardForex* mutable_forwardforex();
  ::com::htsc::mdc::insight::model::ForwardForex* release_forwardforex();
  void set_allocated_forwardforex(::com::htsc::mdc::insight::model::ForwardForex* forwardforex);

  // optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
  bool has_nondeliverableforwardsforex() const;
  void clear_nondeliverableforwardsforex();
  static const int kNonDeliverableForwardsForexFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex& nondeliverableforwardsforex() const;
  ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* mutable_nondeliverableforwardsforex();
  ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* release_nondeliverableforwardsforex();
  void set_allocated_nondeliverableforwardsforex(::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* nondeliverableforwardsforex);

  // optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
  bool has_swapforex() const;
  void clear_swapforex();
  static const int kSwapForexFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::SwapForex& swapforex() const;
  ::com::htsc::mdc::insight::model::SwapForex* mutable_swapforex();
  ::com::htsc::mdc::insight::model::SwapForex* release_swapforex();
  void set_allocated_swapforex(::com::htsc::mdc::insight::model::SwapForex* swapforex);

  // optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
  bool has_optionforex() const;
  void clear_optionforex();
  static const int kOptionForexFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::OptionForex& optionforex() const;
  ::com::htsc::mdc::insight::model::OptionForex* mutable_optionforex();
  ::com::htsc::mdc::insight::model::OptionForex* release_optionforex();
  void set_allocated_optionforex(::com::htsc::mdc::insight::model::OptionForex* optionforex);

  // optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
  bool has_spotclosepriceforex() const;
  void clear_spotclosepriceforex();
  static const int kSpotClosePriceForexFieldNumber = 14;
  const ::com::htsc::mdc::insight::model::SpotClosePriceForex& spotclosepriceforex() const;
  ::com::htsc::mdc::insight::model::SpotClosePriceForex* mutable_spotclosepriceforex();
  ::com::htsc::mdc::insight::model::SpotClosePriceForex* release_spotclosepriceforex();
  void set_allocated_spotclosepriceforex(::com::htsc::mdc::insight::model::SpotClosePriceForex* spotclosepriceforex);

  // optional int32 DataMultiplePowerOf10 = 15;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 15;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string TransactTime = 16;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 16;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securitysubtype_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::com::htsc::mdc::insight::model::SpotForex* spotforex_;
  ::com::htsc::mdc::insight::model::ForwardForex* forwardforex_;
  ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* nondeliverableforwardsforex_;
  ::com::htsc::mdc::insight::model::SwapForex* swapforex_;
  ::com::htsc::mdc::insight::model::OptionForex* optionforex_;
  ::com::htsc::mdc::insight::model::SpotClosePriceForex* spotclosepriceforex_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 forextype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsForex> MDCfetsForex_default_instance_;

// -------------------------------------------------------------------

class SpotForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SpotForex) */ {
 public:
  SpotForex();
  virtual ~SpotForex();

  SpotForex(const SpotForex& from);

  inline SpotForex& operator=(const SpotForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SpotForex& default_instance();

  static const SpotForex* internal_default_instance();

  void Swap(SpotForex* other);

  // implements Message ----------------------------------------------

  inline SpotForex* New() const { return New(NULL); }

  SpotForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SpotForex& from);
  void MergeFrom(const SpotForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SpotForex* other);
  void UnsafeMergeFrom(const SpotForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ValueDate = 1;
  void clear_valuedate();
  static const int kValueDateFieldNumber = 1;
  const ::std::string& valuedate() const;
  void set_valuedate(const ::std::string& value);
  void set_valuedate(const char* value);
  void set_valuedate(const char* value, size_t size);
  ::std::string* mutable_valuedate();
  ::std::string* release_valuedate();
  void set_allocated_valuedate(::std::string* valuedate);

  // optional int64 NetBasisChange = 2;
  void clear_netbasischange();
  static const int kNetBasisChangeFieldNumber = 2;
  ::google::protobuf::int64 netbasischange() const;
  void set_netbasischange(::google::protobuf::int64 value);

  // optional int64 PercentageChange = 3;
  void clear_percentagechange();
  static const int kPercentageChangeFieldNumber = 3;
  ::google::protobuf::int64 percentagechange() const;
  void set_percentagechange(::google::protobuf::int64 value);

  // optional string BuyDate = 4;
  void clear_buydate();
  static const int kBuyDateFieldNumber = 4;
  const ::std::string& buydate() const;
  void set_buydate(const ::std::string& value);
  void set_buydate(const char* value);
  void set_buydate(const char* value, size_t size);
  ::std::string* mutable_buydate();
  ::std::string* release_buydate();
  void set_allocated_buydate(::std::string* buydate);

  // optional string BuyTime = 5;
  void clear_buytime();
  static const int kBuyTimeFieldNumber = 5;
  const ::std::string& buytime() const;
  void set_buytime(const ::std::string& value);
  void set_buytime(const char* value);
  void set_buytime(const char* value, size_t size);
  ::std::string* mutable_buytime();
  ::std::string* release_buytime();
  void set_allocated_buytime(::std::string* buytime);

  // optional string SellDate = 6;
  void clear_selldate();
  static const int kSellDateFieldNumber = 6;
  const ::std::string& selldate() const;
  void set_selldate(const ::std::string& value);
  void set_selldate(const char* value);
  void set_selldate(const char* value, size_t size);
  ::std::string* mutable_selldate();
  ::std::string* release_selldate();
  void set_allocated_selldate(::std::string* selldate);

  // optional string SellTime = 7;
  void clear_selltime();
  static const int kSellTimeFieldNumber = 7;
  const ::std::string& selltime() const;
  void set_selltime(const ::std::string& value);
  void set_selltime(const char* value);
  void set_selltime(const char* value, size_t size);
  ::std::string* mutable_selltime();
  ::std::string* release_selltime();
  void set_allocated_selltime(::std::string* selltime);

  // optional int64 LastBuyRate = 8;
  void clear_lastbuyrate();
  static const int kLastBuyRateFieldNumber = 8;
  ::google::protobuf::int64 lastbuyrate() const;
  void set_lastbuyrate(::google::protobuf::int64 value);

  // optional int64 LastSellRate = 9;
  void clear_lastsellrate();
  static const int kLastSellRateFieldNumber = 9;
  ::google::protobuf::int64 lastsellrate() const;
  void set_lastsellrate(::google::protobuf::int64 value);

  // optional int64 LastBuyAllin = 10;
  void clear_lastbuyallin();
  static const int kLastBuyAllinFieldNumber = 10;
  ::google::protobuf::int64 lastbuyallin() const;
  void set_lastbuyallin(::google::protobuf::int64 value);

  // optional int64 LastSellAllin = 11;
  void clear_lastsellallin();
  static const int kLastSellAllinFieldNumber = 11;
  ::google::protobuf::int64 lastsellallin() const;
  void set_lastsellallin(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 14;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 14;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 HistoryCloseRate = 15;
  void clear_historycloserate();
  static const int kHistoryCloseRateFieldNumber = 15;
  ::google::protobuf::int64 historycloserate() const;
  void set_historycloserate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 16;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 16;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int32 AmountLevelRate = 17;
  void clear_amountlevelrate();
  static const int kAmountLevelRateFieldNumber = 17;
  ::google::protobuf::int32 amountlevelrate() const;
  void set_amountlevelrate(::google::protobuf::int32 value);

  // optional int32 AmountLevelAllin = 18;
  void clear_amountlevelallin();
  static const int kAmountLevelAllinFieldNumber = 18;
  ::google::protobuf::int32 amountlevelallin() const;
  void set_amountlevelallin(::google::protobuf::int32 value);

  // optional int32 RateSide = 19;
  void clear_rateside();
  static const int kRateSideFieldNumber = 19;
  ::google::protobuf::int32 rateside() const;
  void set_rateside(::google::protobuf::int32 value);

  // optional int32 AllinSide = 20;
  void clear_allinside();
  static const int kAllinSideFieldNumber = 20;
  ::google::protobuf::int32 allinside() const;
  void set_allinside(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SpotForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valuedate_;
  ::google::protobuf::internal::ArenaStringPtr buydate_;
  ::google::protobuf::internal::ArenaStringPtr buytime_;
  ::google::protobuf::internal::ArenaStringPtr selldate_;
  ::google::protobuf::internal::ArenaStringPtr selltime_;
  ::google::protobuf::int64 netbasischange_;
  ::google::protobuf::int64 percentagechange_;
  ::google::protobuf::int64 lastbuyrate_;
  ::google::protobuf::int64 lastsellrate_;
  ::google::protobuf::int64 lastbuyallin_;
  ::google::protobuf::int64 lastsellallin_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 historycloserate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int32 amountlevelrate_;
  ::google::protobuf::int32 amountlevelallin_;
  ::google::protobuf::int32 rateside_;
  ::google::protobuf::int32 allinside_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SpotForex> SpotForex_default_instance_;

// -------------------------------------------------------------------

class ForwardForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.ForwardForex) */ {
 public:
  ForwardForex();
  virtual ~ForwardForex();

  ForwardForex(const ForwardForex& from);

  inline ForwardForex& operator=(const ForwardForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const ForwardForex& default_instance();

  static const ForwardForex* internal_default_instance();

  void Swap(ForwardForex* other);

  // implements Message ----------------------------------------------

  inline ForwardForex* New() const { return New(NULL); }

  ForwardForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const ForwardForex& from);
  void MergeFrom(const ForwardForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ForwardForex* other);
  void UnsafeMergeFrom(const ForwardForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ValueDate = 1;
  void clear_valuedate();
  static const int kValueDateFieldNumber = 1;
  const ::std::string& valuedate() const;
  void set_valuedate(const ::std::string& value);
  void set_valuedate(const char* value);
  void set_valuedate(const char* value, size_t size);
  ::std::string* mutable_valuedate();
  ::std::string* release_valuedate();
  void set_allocated_valuedate(::std::string* valuedate);

  // optional int64 NetBasisChange = 2;
  void clear_netbasischange();
  static const int kNetBasisChangeFieldNumber = 2;
  ::google::protobuf::int64 netbasischange() const;
  void set_netbasischange(::google::protobuf::int64 value);

  // optional int64 PercentageChange = 3;
  void clear_percentagechange();
  static const int kPercentageChangeFieldNumber = 3;
  ::google::protobuf::int64 percentagechange() const;
  void set_percentagechange(::google::protobuf::int64 value);

  // optional string BuyDate = 4;
  void clear_buydate();
  static const int kBuyDateFieldNumber = 4;
  const ::std::string& buydate() const;
  void set_buydate(const ::std::string& value);
  void set_buydate(const char* value);
  void set_buydate(const char* value, size_t size);
  ::std::string* mutable_buydate();
  ::std::string* release_buydate();
  void set_allocated_buydate(::std::string* buydate);

  // optional string BuyTime = 5;
  void clear_buytime();
  static const int kBuyTimeFieldNumber = 5;
  const ::std::string& buytime() const;
  void set_buytime(const ::std::string& value);
  void set_buytime(const char* value);
  void set_buytime(const char* value, size_t size);
  ::std::string* mutable_buytime();
  ::std::string* release_buytime();
  void set_allocated_buytime(::std::string* buytime);

  // optional string SellDate = 6;
  void clear_selldate();
  static const int kSellDateFieldNumber = 6;
  const ::std::string& selldate() const;
  void set_selldate(const ::std::string& value);
  void set_selldate(const char* value);
  void set_selldate(const char* value, size_t size);
  ::std::string* mutable_selldate();
  ::std::string* release_selldate();
  void set_allocated_selldate(::std::string* selldate);

  // optional string SellTime = 7;
  void clear_selltime();
  static const int kSellTimeFieldNumber = 7;
  const ::std::string& selltime() const;
  void set_selltime(const ::std::string& value);
  void set_selltime(const char* value);
  void set_selltime(const char* value, size_t size);
  ::std::string* mutable_selltime();
  ::std::string* release_selltime();
  void set_allocated_selltime(::std::string* selltime);

  // optional int64 LastBuyRate = 8;
  void clear_lastbuyrate();
  static const int kLastBuyRateFieldNumber = 8;
  ::google::protobuf::int64 lastbuyrate() const;
  void set_lastbuyrate(::google::protobuf::int64 value);

  // optional int64 LastSellRate = 9;
  void clear_lastsellrate();
  static const int kLastSellRateFieldNumber = 9;
  ::google::protobuf::int64 lastsellrate() const;
  void set_lastsellrate(::google::protobuf::int64 value);

  // optional int64 LastBuyAllin = 10;
  void clear_lastbuyallin();
  static const int kLastBuyAllinFieldNumber = 10;
  ::google::protobuf::int64 lastbuyallin() const;
  void set_lastbuyallin(::google::protobuf::int64 value);

  // optional int64 LastSellAllin = 11;
  void clear_lastsellallin();
  static const int kLastSellAllinFieldNumber = 11;
  ::google::protobuf::int64 lastsellallin() const;
  void set_lastsellallin(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 14;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 14;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 HistoryCloseRate = 15;
  void clear_historycloserate();
  static const int kHistoryCloseRateFieldNumber = 15;
  ::google::protobuf::int64 historycloserate() const;
  void set_historycloserate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 16;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 16;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int32 AmountLevelRate = 17;
  void clear_amountlevelrate();
  static const int kAmountLevelRateFieldNumber = 17;
  ::google::protobuf::int32 amountlevelrate() const;
  void set_amountlevelrate(::google::protobuf::int32 value);

  // optional int32 AmountLevelAllin = 18;
  void clear_amountlevelallin();
  static const int kAmountLevelAllinFieldNumber = 18;
  ::google::protobuf::int32 amountlevelallin() const;
  void set_amountlevelallin(::google::protobuf::int32 value);

  // optional int32 RateSide = 19;
  void clear_rateside();
  static const int kRateSideFieldNumber = 19;
  ::google::protobuf::int32 rateside() const;
  void set_rateside(::google::protobuf::int32 value);

  // optional int32 AllinSide = 20;
  void clear_allinside();
  static const int kAllinSideFieldNumber = 20;
  ::google::protobuf::int32 allinside() const;
  void set_allinside(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.ForwardForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valuedate_;
  ::google::protobuf::internal::ArenaStringPtr buydate_;
  ::google::protobuf::internal::ArenaStringPtr buytime_;
  ::google::protobuf::internal::ArenaStringPtr selldate_;
  ::google::protobuf::internal::ArenaStringPtr selltime_;
  ::google::protobuf::int64 netbasischange_;
  ::google::protobuf::int64 percentagechange_;
  ::google::protobuf::int64 lastbuyrate_;
  ::google::protobuf::int64 lastsellrate_;
  ::google::protobuf::int64 lastbuyallin_;
  ::google::protobuf::int64 lastsellallin_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 historycloserate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int32 amountlevelrate_;
  ::google::protobuf::int32 amountlevelallin_;
  ::google::protobuf::int32 rateside_;
  ::google::protobuf::int32 allinside_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<ForwardForex> ForwardForex_default_instance_;

// -------------------------------------------------------------------

class NonDeliverableForwardsForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.NonDeliverableForwardsForex) */ {
 public:
  NonDeliverableForwardsForex();
  virtual ~NonDeliverableForwardsForex();

  NonDeliverableForwardsForex(const NonDeliverableForwardsForex& from);

  inline NonDeliverableForwardsForex& operator=(const NonDeliverableForwardsForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const NonDeliverableForwardsForex& default_instance();

  static const NonDeliverableForwardsForex* internal_default_instance();

  void Swap(NonDeliverableForwardsForex* other);

  // implements Message ----------------------------------------------

  inline NonDeliverableForwardsForex* New() const { return New(NULL); }

  NonDeliverableForwardsForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const NonDeliverableForwardsForex& from);
  void MergeFrom(const NonDeliverableForwardsForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(NonDeliverableForwardsForex* other);
  void UnsafeMergeFrom(const NonDeliverableForwardsForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ValueDate = 1;
  void clear_valuedate();
  static const int kValueDateFieldNumber = 1;
  const ::std::string& valuedate() const;
  void set_valuedate(const ::std::string& value);
  void set_valuedate(const char* value);
  void set_valuedate(const char* value, size_t size);
  ::std::string* mutable_valuedate();
  ::std::string* release_valuedate();
  void set_allocated_valuedate(::std::string* valuedate);

  // optional int64 NetBasisChange = 2;
  void clear_netbasischange();
  static const int kNetBasisChangeFieldNumber = 2;
  ::google::protobuf::int64 netbasischange() const;
  void set_netbasischange(::google::protobuf::int64 value);

  // optional int64 PercentageChange = 3;
  void clear_percentagechange();
  static const int kPercentageChangeFieldNumber = 3;
  ::google::protobuf::int64 percentagechange() const;
  void set_percentagechange(::google::protobuf::int64 value);

  // optional string BuyDate = 4;
  void clear_buydate();
  static const int kBuyDateFieldNumber = 4;
  const ::std::string& buydate() const;
  void set_buydate(const ::std::string& value);
  void set_buydate(const char* value);
  void set_buydate(const char* value, size_t size);
  ::std::string* mutable_buydate();
  ::std::string* release_buydate();
  void set_allocated_buydate(::std::string* buydate);

  // optional string BuyTime = 5;
  void clear_buytime();
  static const int kBuyTimeFieldNumber = 5;
  const ::std::string& buytime() const;
  void set_buytime(const ::std::string& value);
  void set_buytime(const char* value);
  void set_buytime(const char* value, size_t size);
  ::std::string* mutable_buytime();
  ::std::string* release_buytime();
  void set_allocated_buytime(::std::string* buytime);

  // optional string SellDate = 6;
  void clear_selldate();
  static const int kSellDateFieldNumber = 6;
  const ::std::string& selldate() const;
  void set_selldate(const ::std::string& value);
  void set_selldate(const char* value);
  void set_selldate(const char* value, size_t size);
  ::std::string* mutable_selldate();
  ::std::string* release_selldate();
  void set_allocated_selldate(::std::string* selldate);

  // optional string SellTime = 7;
  void clear_selltime();
  static const int kSellTimeFieldNumber = 7;
  const ::std::string& selltime() const;
  void set_selltime(const ::std::string& value);
  void set_selltime(const char* value);
  void set_selltime(const char* value, size_t size);
  ::std::string* mutable_selltime();
  ::std::string* release_selltime();
  void set_allocated_selltime(::std::string* selltime);

  // optional int64 LastBuyRate = 8;
  void clear_lastbuyrate();
  static const int kLastBuyRateFieldNumber = 8;
  ::google::protobuf::int64 lastbuyrate() const;
  void set_lastbuyrate(::google::protobuf::int64 value);

  // optional int64 LastSellRate = 9;
  void clear_lastsellrate();
  static const int kLastSellRateFieldNumber = 9;
  ::google::protobuf::int64 lastsellrate() const;
  void set_lastsellrate(::google::protobuf::int64 value);

  // optional int64 LastBuyAllin = 10;
  void clear_lastbuyallin();
  static const int kLastBuyAllinFieldNumber = 10;
  ::google::protobuf::int64 lastbuyallin() const;
  void set_lastbuyallin(::google::protobuf::int64 value);

  // optional int64 LastSellAllin = 11;
  void clear_lastsellallin();
  static const int kLastSellAllinFieldNumber = 11;
  ::google::protobuf::int64 lastsellallin() const;
  void set_lastsellallin(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 14;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 14;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 HistoryCloseRate = 15;
  void clear_historycloserate();
  static const int kHistoryCloseRateFieldNumber = 15;
  ::google::protobuf::int64 historycloserate() const;
  void set_historycloserate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 16;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 16;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int32 AmountLevelRate = 17;
  void clear_amountlevelrate();
  static const int kAmountLevelRateFieldNumber = 17;
  ::google::protobuf::int32 amountlevelrate() const;
  void set_amountlevelrate(::google::protobuf::int32 value);

  // optional int32 AmountLevelAllin = 18;
  void clear_amountlevelallin();
  static const int kAmountLevelAllinFieldNumber = 18;
  ::google::protobuf::int32 amountlevelallin() const;
  void set_amountlevelallin(::google::protobuf::int32 value);

  // optional int32 RateSide = 19;
  void clear_rateside();
  static const int kRateSideFieldNumber = 19;
  ::google::protobuf::int32 rateside() const;
  void set_rateside(::google::protobuf::int32 value);

  // optional int32 AllinSide = 20;
  void clear_allinside();
  static const int kAllinSideFieldNumber = 20;
  ::google::protobuf::int32 allinside() const;
  void set_allinside(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valuedate_;
  ::google::protobuf::internal::ArenaStringPtr buydate_;
  ::google::protobuf::internal::ArenaStringPtr buytime_;
  ::google::protobuf::internal::ArenaStringPtr selldate_;
  ::google::protobuf::internal::ArenaStringPtr selltime_;
  ::google::protobuf::int64 netbasischange_;
  ::google::protobuf::int64 percentagechange_;
  ::google::protobuf::int64 lastbuyrate_;
  ::google::protobuf::int64 lastsellrate_;
  ::google::protobuf::int64 lastbuyallin_;
  ::google::protobuf::int64 lastsellallin_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 historycloserate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int32 amountlevelrate_;
  ::google::protobuf::int32 amountlevelallin_;
  ::google::protobuf::int32 rateside_;
  ::google::protobuf::int32 allinside_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<NonDeliverableForwardsForex> NonDeliverableForwardsForex_default_instance_;

// -------------------------------------------------------------------

class SwapForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SwapForex) */ {
 public:
  SwapForex();
  virtual ~SwapForex();

  SwapForex(const SwapForex& from);

  inline SwapForex& operator=(const SwapForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SwapForex& default_instance();

  static const SwapForex* internal_default_instance();

  void Swap(SwapForex* other);

  // implements Message ----------------------------------------------

  inline SwapForex* New() const { return New(NULL); }

  SwapForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SwapForex& from);
  void MergeFrom(const SwapForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SwapForex* other);
  void UnsafeMergeFrom(const SwapForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ValueDate = 1;
  void clear_valuedate();
  static const int kValueDateFieldNumber = 1;
  const ::std::string& valuedate() const;
  void set_valuedate(const ::std::string& value);
  void set_valuedate(const char* value);
  void set_valuedate(const char* value, size_t size);
  ::std::string* mutable_valuedate();
  ::std::string* release_valuedate();
  void set_allocated_valuedate(::std::string* valuedate);

  // optional int64 NetBasisChange = 2;
  void clear_netbasischange();
  static const int kNetBasisChangeFieldNumber = 2;
  ::google::protobuf::int64 netbasischange() const;
  void set_netbasischange(::google::protobuf::int64 value);

  // optional int64 PercentageChange = 3;
  void clear_percentagechange();
  static const int kPercentageChangeFieldNumber = 3;
  ::google::protobuf::int64 percentagechange() const;
  void set_percentagechange(::google::protobuf::int64 value);

  // optional string BuyDate = 4;
  void clear_buydate();
  static const int kBuyDateFieldNumber = 4;
  const ::std::string& buydate() const;
  void set_buydate(const ::std::string& value);
  void set_buydate(const char* value);
  void set_buydate(const char* value, size_t size);
  ::std::string* mutable_buydate();
  ::std::string* release_buydate();
  void set_allocated_buydate(::std::string* buydate);

  // optional string BuyTime = 5;
  void clear_buytime();
  static const int kBuyTimeFieldNumber = 5;
  const ::std::string& buytime() const;
  void set_buytime(const ::std::string& value);
  void set_buytime(const char* value);
  void set_buytime(const char* value, size_t size);
  ::std::string* mutable_buytime();
  ::std::string* release_buytime();
  void set_allocated_buytime(::std::string* buytime);

  // optional string SellDate = 6;
  void clear_selldate();
  static const int kSellDateFieldNumber = 6;
  const ::std::string& selldate() const;
  void set_selldate(const ::std::string& value);
  void set_selldate(const char* value);
  void set_selldate(const char* value, size_t size);
  ::std::string* mutable_selldate();
  ::std::string* release_selldate();
  void set_allocated_selldate(::std::string* selldate);

  // optional string SellTime = 7;
  void clear_selltime();
  static const int kSellTimeFieldNumber = 7;
  const ::std::string& selltime() const;
  void set_selltime(const ::std::string& value);
  void set_selltime(const char* value);
  void set_selltime(const char* value, size_t size);
  ::std::string* mutable_selltime();
  ::std::string* release_selltime();
  void set_allocated_selltime(::std::string* selltime);

  // optional int64 LastBuyRate = 8;
  void clear_lastbuyrate();
  static const int kLastBuyRateFieldNumber = 8;
  ::google::protobuf::int64 lastbuyrate() const;
  void set_lastbuyrate(::google::protobuf::int64 value);

  // optional int64 LastSellRate = 9;
  void clear_lastsellrate();
  static const int kLastSellRateFieldNumber = 9;
  ::google::protobuf::int64 lastsellrate() const;
  void set_lastsellrate(::google::protobuf::int64 value);

  // optional int64 LastBuyAllin = 10;
  void clear_lastbuyallin();
  static const int kLastBuyAllinFieldNumber = 10;
  ::google::protobuf::int64 lastbuyallin() const;
  void set_lastbuyallin(::google::protobuf::int64 value);

  // optional int64 LastSellAllin = 11;
  void clear_lastsellallin();
  static const int kLastSellAllinFieldNumber = 11;
  ::google::protobuf::int64 lastsellallin() const;
  void set_lastsellallin(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 14;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 14;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 HistoryCloseRate = 15;
  void clear_historycloserate();
  static const int kHistoryCloseRateFieldNumber = 15;
  ::google::protobuf::int64 historycloserate() const;
  void set_historycloserate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 16;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 16;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int32 AmountLevelRate = 17;
  void clear_amountlevelrate();
  static const int kAmountLevelRateFieldNumber = 17;
  ::google::protobuf::int32 amountlevelrate() const;
  void set_amountlevelrate(::google::protobuf::int32 value);

  // optional int32 AmountLevelAllin = 18;
  void clear_amountlevelallin();
  static const int kAmountLevelAllinFieldNumber = 18;
  ::google::protobuf::int32 amountlevelallin() const;
  void set_amountlevelallin(::google::protobuf::int32 value);

  // optional int32 RateSide = 19;
  void clear_rateside();
  static const int kRateSideFieldNumber = 19;
  ::google::protobuf::int32 rateside() const;
  void set_rateside(::google::protobuf::int32 value);

  // optional int32 AllinSide = 20;
  void clear_allinside();
  static const int kAllinSideFieldNumber = 20;
  ::google::protobuf::int32 allinside() const;
  void set_allinside(::google::protobuf::int32 value);

  // optional string LegSign = 21;
  void clear_legsign();
  static const int kLegSignFieldNumber = 21;
  const ::std::string& legsign() const;
  void set_legsign(const ::std::string& value);
  void set_legsign(const char* value);
  void set_legsign(const char* value, size_t size);
  ::std::string* mutable_legsign();
  ::std::string* release_legsign();
  void set_allocated_legsign(::std::string* legsign);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SwapForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valuedate_;
  ::google::protobuf::internal::ArenaStringPtr buydate_;
  ::google::protobuf::internal::ArenaStringPtr buytime_;
  ::google::protobuf::internal::ArenaStringPtr selldate_;
  ::google::protobuf::internal::ArenaStringPtr selltime_;
  ::google::protobuf::internal::ArenaStringPtr legsign_;
  ::google::protobuf::int64 netbasischange_;
  ::google::protobuf::int64 percentagechange_;
  ::google::protobuf::int64 lastbuyrate_;
  ::google::protobuf::int64 lastsellrate_;
  ::google::protobuf::int64 lastbuyallin_;
  ::google::protobuf::int64 lastsellallin_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 historycloserate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int32 amountlevelrate_;
  ::google::protobuf::int32 amountlevelallin_;
  ::google::protobuf::int32 rateside_;
  ::google::protobuf::int32 allinside_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SwapForex> SwapForex_default_instance_;

// -------------------------------------------------------------------

class OptionForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.OptionForex) */ {
 public:
  OptionForex();
  virtual ~OptionForex();

  OptionForex(const OptionForex& from);

  inline OptionForex& operator=(const OptionForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OptionForex& default_instance();

  static const OptionForex* internal_default_instance();

  void Swap(OptionForex* other);

  // implements Message ----------------------------------------------

  inline OptionForex* New() const { return New(NULL); }

  OptionForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OptionForex& from);
  void MergeFrom(const OptionForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OptionForex* other);
  void UnsafeMergeFrom(const OptionForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string FxTerm = 1;
  void clear_fxterm();
  static const int kFxTermFieldNumber = 1;
  const ::std::string& fxterm() const;
  void set_fxterm(const ::std::string& value);
  void set_fxterm(const char* value);
  void set_fxterm(const char* value, size_t size);
  ::std::string* mutable_fxterm();
  ::std::string* release_fxterm();
  void set_allocated_fxterm(::std::string* fxterm);

  // optional int64 Premium = 2;
  void clear_premium();
  static const int kPremiumFieldNumber = 2;
  ::google::protobuf::int64 premium() const;
  void set_premium(::google::protobuf::int64 value);

  // optional int64 Volatility = 3;
  void clear_volatility();
  static const int kVolatilityFieldNumber = 3;
  ::google::protobuf::int64 volatility() const;
  void set_volatility(::google::protobuf::int64 value);

  // optional int64 Volume = 4;
  void clear_volume();
  static const int kVolumeFieldNumber = 4;
  ::google::protobuf::int64 volume() const;
  void set_volume(::google::protobuf::int64 value);

  // optional string TradeDate = 5;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 5;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 6;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 6;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional int32 PremiumType = 7;
  void clear_premiumtype();
  static const int kPremiumTypeFieldNumber = 7;
  ::google::protobuf::int32 premiumtype() const;
  void set_premiumtype(::google::protobuf::int32 value);

  // optional string OptionType = 8;
  void clear_optiontype();
  static const int kOptionTypeFieldNumber = 8;
  const ::std::string& optiontype() const;
  void set_optiontype(const ::std::string& value);
  void set_optiontype(const char* value);
  void set_optiontype(const char* value, size_t size);
  ::std::string* mutable_optiontype();
  ::std::string* release_optiontype();
  void set_allocated_optiontype(::std::string* optiontype);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.OptionForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr fxterm_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  ::google::protobuf::internal::ArenaStringPtr optiontype_;
  ::google::protobuf::int64 premium_;
  ::google::protobuf::int64 volatility_;
  ::google::protobuf::int64 volume_;
  ::google::protobuf::int32 premiumtype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OptionForex> OptionForex_default_instance_;

// -------------------------------------------------------------------

class SpotClosePriceForex : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SpotClosePriceForex) */ {
 public:
  SpotClosePriceForex();
  virtual ~SpotClosePriceForex();

  SpotClosePriceForex(const SpotClosePriceForex& from);

  inline SpotClosePriceForex& operator=(const SpotClosePriceForex& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SpotClosePriceForex& default_instance();

  static const SpotClosePriceForex* internal_default_instance();

  void Swap(SpotClosePriceForex* other);

  // implements Message ----------------------------------------------

  inline SpotClosePriceForex* New() const { return New(NULL); }

  SpotClosePriceForex* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SpotClosePriceForex& from);
  void MergeFrom(const SpotClosePriceForex& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SpotClosePriceForex* other);
  void UnsafeMergeFrom(const SpotClosePriceForex& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 ClosePx = 1;
  void clear_closepx();
  static const int kClosePxFieldNumber = 1;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional string UpdateDate = 2;
  void clear_updatedate();
  static const int kUpdateDateFieldNumber = 2;
  const ::std::string& updatedate() const;
  void set_updatedate(const ::std::string& value);
  void set_updatedate(const char* value);
  void set_updatedate(const char* value, size_t size);
  ::std::string* mutable_updatedate();
  ::std::string* release_updatedate();
  void set_allocated_updatedate(::std::string* updatedate);

  // optional string UpdateTime = 3;
  void clear_updatetime();
  static const int kUpdateTimeFieldNumber = 3;
  const ::std::string& updatetime() const;
  void set_updatetime(const ::std::string& value);
  void set_updatetime(const char* value);
  void set_updatetime(const char* value, size_t size);
  ::std::string* mutable_updatetime();
  ::std::string* release_updatetime();
  void set_allocated_updatetime(::std::string* updatetime);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SpotClosePriceForex)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr updatedate_;
  ::google::protobuf::internal::ArenaStringPtr updatetime_;
  ::google::protobuf::int64 closepx_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsForex_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsForex_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsForex_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsForex_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SpotClosePriceForex> SpotClosePriceForex_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsForex

// optional string HTSCSecurityID = 1;
inline void MDCfetsForex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsForex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
inline void MDCfetsForex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
inline void MDCfetsForex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
inline ::std::string* MDCfetsForex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsForex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCfetsForex::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsForex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.MDDate)
  return mddate_;
}
inline void MDCfetsForex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCfetsForex::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsForex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.MDTime)
  return mdtime_;
}
inline void MDCfetsForex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCfetsForex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsForex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsForex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCfetsForex::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsForex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsForex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCfetsForex::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsForex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsForex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.securityType)
}

// optional string SecuritySubType = 7;
inline void MDCfetsForex::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsForex::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
inline void MDCfetsForex::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
inline void MDCfetsForex::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
inline ::std::string* MDCfetsForex::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsForex::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}

// optional int32 ForexType = 8;
inline void MDCfetsForex::clear_forextype() {
  forextype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsForex::forextype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.ForexType)
  return forextype_;
}
inline void MDCfetsForex::set_forextype(::google::protobuf::int32 value) {
  
  forextype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.ForexType)
}

// optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
inline bool MDCfetsForex::has_spotforex() const {
  return this != internal_default_instance() && spotforex_ != NULL;
}
inline void MDCfetsForex::clear_spotforex() {
  if (GetArenaNoVirtual() == NULL && spotforex_ != NULL) delete spotforex_;
  spotforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SpotForex& MDCfetsForex::spotforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  return spotforex_ != NULL ? *spotforex_
                         : *::com::htsc::mdc::insight::model::SpotForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SpotForex* MDCfetsForex::mutable_spotforex() {
  
  if (spotforex_ == NULL) {
    spotforex_ = new ::com::htsc::mdc::insight::model::SpotForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  return spotforex_;
}
inline ::com::htsc::mdc::insight::model::SpotForex* MDCfetsForex::release_spotforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  
  ::com::htsc::mdc::insight::model::SpotForex* temp = spotforex_;
  spotforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_spotforex(::com::htsc::mdc::insight::model::SpotForex* spotforex) {
  delete spotforex_;
  spotforex_ = spotforex;
  if (spotforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
}

// optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
inline bool MDCfetsForex::has_forwardforex() const {
  return this != internal_default_instance() && forwardforex_ != NULL;
}
inline void MDCfetsForex::clear_forwardforex() {
  if (GetArenaNoVirtual() == NULL && forwardforex_ != NULL) delete forwardforex_;
  forwardforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ForwardForex& MDCfetsForex::forwardforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  return forwardforex_ != NULL ? *forwardforex_
                         : *::com::htsc::mdc::insight::model::ForwardForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ForwardForex* MDCfetsForex::mutable_forwardforex() {
  
  if (forwardforex_ == NULL) {
    forwardforex_ = new ::com::htsc::mdc::insight::model::ForwardForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  return forwardforex_;
}
inline ::com::htsc::mdc::insight::model::ForwardForex* MDCfetsForex::release_forwardforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  
  ::com::htsc::mdc::insight::model::ForwardForex* temp = forwardforex_;
  forwardforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_forwardforex(::com::htsc::mdc::insight::model::ForwardForex* forwardforex) {
  delete forwardforex_;
  forwardforex_ = forwardforex;
  if (forwardforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
}

// optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
inline bool MDCfetsForex::has_nondeliverableforwardsforex() const {
  return this != internal_default_instance() && nondeliverableforwardsforex_ != NULL;
}
inline void MDCfetsForex::clear_nondeliverableforwardsforex() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsforex_ != NULL) delete nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex& MDCfetsForex::nondeliverableforwardsforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  return nondeliverableforwardsforex_ != NULL ? *nondeliverableforwardsforex_
                         : *::com::htsc::mdc::insight::model::NonDeliverableForwardsForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* MDCfetsForex::mutable_nondeliverableforwardsforex() {
  
  if (nondeliverableforwardsforex_ == NULL) {
    nondeliverableforwardsforex_ = new ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  return nondeliverableforwardsforex_;
}
inline ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* MDCfetsForex::release_nondeliverableforwardsforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  
  ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* temp = nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_nondeliverableforwardsforex(::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* nondeliverableforwardsforex) {
  delete nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = nondeliverableforwardsforex;
  if (nondeliverableforwardsforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
}

// optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
inline bool MDCfetsForex::has_swapforex() const {
  return this != internal_default_instance() && swapforex_ != NULL;
}
inline void MDCfetsForex::clear_swapforex() {
  if (GetArenaNoVirtual() == NULL && swapforex_ != NULL) delete swapforex_;
  swapforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwapForex& MDCfetsForex::swapforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  return swapforex_ != NULL ? *swapforex_
                         : *::com::htsc::mdc::insight::model::SwapForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwapForex* MDCfetsForex::mutable_swapforex() {
  
  if (swapforex_ == NULL) {
    swapforex_ = new ::com::htsc::mdc::insight::model::SwapForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  return swapforex_;
}
inline ::com::htsc::mdc::insight::model::SwapForex* MDCfetsForex::release_swapforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  
  ::com::htsc::mdc::insight::model::SwapForex* temp = swapforex_;
  swapforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_swapforex(::com::htsc::mdc::insight::model::SwapForex* swapforex) {
  delete swapforex_;
  swapforex_ = swapforex;
  if (swapforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
}

// optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
inline bool MDCfetsForex::has_optionforex() const {
  return this != internal_default_instance() && optionforex_ != NULL;
}
inline void MDCfetsForex::clear_optionforex() {
  if (GetArenaNoVirtual() == NULL && optionforex_ != NULL) delete optionforex_;
  optionforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::OptionForex& MDCfetsForex::optionforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  return optionforex_ != NULL ? *optionforex_
                         : *::com::htsc::mdc::insight::model::OptionForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::OptionForex* MDCfetsForex::mutable_optionforex() {
  
  if (optionforex_ == NULL) {
    optionforex_ = new ::com::htsc::mdc::insight::model::OptionForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  return optionforex_;
}
inline ::com::htsc::mdc::insight::model::OptionForex* MDCfetsForex::release_optionforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  
  ::com::htsc::mdc::insight::model::OptionForex* temp = optionforex_;
  optionforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_optionforex(::com::htsc::mdc::insight::model::OptionForex* optionforex) {
  delete optionforex_;
  optionforex_ = optionforex;
  if (optionforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
}

// optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
inline bool MDCfetsForex::has_spotclosepriceforex() const {
  return this != internal_default_instance() && spotclosepriceforex_ != NULL;
}
inline void MDCfetsForex::clear_spotclosepriceforex() {
  if (GetArenaNoVirtual() == NULL && spotclosepriceforex_ != NULL) delete spotclosepriceforex_;
  spotclosepriceforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SpotClosePriceForex& MDCfetsForex::spotclosepriceforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  return spotclosepriceforex_ != NULL ? *spotclosepriceforex_
                         : *::com::htsc::mdc::insight::model::SpotClosePriceForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SpotClosePriceForex* MDCfetsForex::mutable_spotclosepriceforex() {
  
  if (spotclosepriceforex_ == NULL) {
    spotclosepriceforex_ = new ::com::htsc::mdc::insight::model::SpotClosePriceForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  return spotclosepriceforex_;
}
inline ::com::htsc::mdc::insight::model::SpotClosePriceForex* MDCfetsForex::release_spotclosepriceforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  
  ::com::htsc::mdc::insight::model::SpotClosePriceForex* temp = spotclosepriceforex_;
  spotclosepriceforex_ = NULL;
  return temp;
}
inline void MDCfetsForex::set_allocated_spotclosepriceforex(::com::htsc::mdc::insight::model::SpotClosePriceForex* spotclosepriceforex) {
  delete spotclosepriceforex_;
  spotclosepriceforex_ = spotclosepriceforex;
  if (spotclosepriceforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
}

// optional int32 DataMultiplePowerOf10 = 15;
inline void MDCfetsForex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsForex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsForex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.DataMultiplePowerOf10)
}

// optional string TransactTime = 16;
inline void MDCfetsForex::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsForex::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
inline void MDCfetsForex::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
inline void MDCfetsForex::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
inline ::std::string* MDCfetsForex::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsForex::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsForex::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}

inline const MDCfetsForex* MDCfetsForex::internal_default_instance() {
  return &MDCfetsForex_default_instance_.get();
}
// -------------------------------------------------------------------

// SpotForex

// optional string ValueDate = 1;
inline void SpotForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
inline void SpotForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
inline void SpotForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
inline ::std::string* SpotForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
inline void SpotForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.NetBasisChange)
  return netbasischange_;
}
inline void SpotForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
inline void SpotForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.PercentageChange)
  return percentagechange_;
}
inline void SpotForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.PercentageChange)
}

// optional string BuyDate = 4;
inline void SpotForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
inline void SpotForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
inline void SpotForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
inline ::std::string* SpotForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}

// optional string BuyTime = 5;
inline void SpotForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
inline void SpotForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
inline void SpotForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
inline ::std::string* SpotForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}

// optional string SellDate = 6;
inline void SpotForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
inline void SpotForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
inline void SpotForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
inline ::std::string* SpotForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.SellDate)
}

// optional string SellTime = 7;
inline void SpotForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
inline void SpotForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
inline void SpotForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
inline ::std::string* SpotForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.SellTime)
}

// optional int64 LastBuyRate = 8;
inline void SpotForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastBuyRate)
  return lastbuyrate_;
}
inline void SpotForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
inline void SpotForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastSellRate)
  return lastsellrate_;
}
inline void SpotForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
inline void SpotForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastBuyAllin)
  return lastbuyallin_;
}
inline void SpotForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
inline void SpotForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastSellAllin)
  return lastsellallin_;
}
inline void SpotForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastSellAllin)
}

// optional int64 HighRate = 12;
inline void SpotForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.HighRate)
  return highrate_;
}
inline void SpotForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.HighRate)
}

// optional int64 LowRate = 13;
inline void SpotForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LowRate)
  return lowrate_;
}
inline void SpotForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LowRate)
}

// optional int64 OpenRate = 14;
inline void SpotForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.OpenRate)
  return openrate_;
}
inline void SpotForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
inline void SpotForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.HistoryCloseRate)
  return historycloserate_;
}
inline void SpotForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
inline void SpotForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.CloseRate)
  return closerate_;
}
inline void SpotForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
inline void SpotForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
inline ::google::protobuf::int32 SpotForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AmountLevelRate)
  return amountlevelrate_;
}
inline void SpotForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
inline void SpotForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
inline ::google::protobuf::int32 SpotForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AmountLevelAllin)
  return amountlevelallin_;
}
inline void SpotForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
inline void SpotForex::clear_rateside() {
  rateside_ = 0;
}
inline ::google::protobuf::int32 SpotForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.RateSide)
  return rateside_;
}
inline void SpotForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.RateSide)
}

// optional int32 AllinSide = 20;
inline void SpotForex::clear_allinside() {
  allinside_ = 0;
}
inline ::google::protobuf::int32 SpotForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AllinSide)
  return allinside_;
}
inline void SpotForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AllinSide)
}

inline const SpotForex* SpotForex::internal_default_instance() {
  return &SpotForex_default_instance_.get();
}
// -------------------------------------------------------------------

// ForwardForex

// optional string ValueDate = 1;
inline void ForwardForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForwardForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
inline void ForwardForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
inline void ForwardForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
inline ::std::string* ForwardForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForwardForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
inline void ForwardForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.NetBasisChange)
  return netbasischange_;
}
inline void ForwardForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
inline void ForwardForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.PercentageChange)
  return percentagechange_;
}
inline void ForwardForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.PercentageChange)
}

// optional string BuyDate = 4;
inline void ForwardForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForwardForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
inline void ForwardForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
inline void ForwardForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
inline ::std::string* ForwardForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForwardForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}

// optional string BuyTime = 5;
inline void ForwardForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForwardForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
inline void ForwardForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
inline void ForwardForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
inline ::std::string* ForwardForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForwardForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}

// optional string SellDate = 6;
inline void ForwardForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForwardForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
inline void ForwardForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
inline void ForwardForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
inline ::std::string* ForwardForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForwardForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}

// optional string SellTime = 7;
inline void ForwardForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ForwardForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
inline void ForwardForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
inline void ForwardForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
inline ::std::string* ForwardForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ForwardForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ForwardForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}

// optional int64 LastBuyRate = 8;
inline void ForwardForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastBuyRate)
  return lastbuyrate_;
}
inline void ForwardForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
inline void ForwardForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastSellRate)
  return lastsellrate_;
}
inline void ForwardForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
inline void ForwardForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastBuyAllin)
  return lastbuyallin_;
}
inline void ForwardForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
inline void ForwardForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastSellAllin)
  return lastsellallin_;
}
inline void ForwardForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastSellAllin)
}

// optional int64 HighRate = 12;
inline void ForwardForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.HighRate)
  return highrate_;
}
inline void ForwardForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.HighRate)
}

// optional int64 LowRate = 13;
inline void ForwardForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LowRate)
  return lowrate_;
}
inline void ForwardForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LowRate)
}

// optional int64 OpenRate = 14;
inline void ForwardForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.OpenRate)
  return openrate_;
}
inline void ForwardForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
inline void ForwardForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.HistoryCloseRate)
  return historycloserate_;
}
inline void ForwardForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
inline void ForwardForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ForwardForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.CloseRate)
  return closerate_;
}
inline void ForwardForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
inline void ForwardForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
inline ::google::protobuf::int32 ForwardForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AmountLevelRate)
  return amountlevelrate_;
}
inline void ForwardForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
inline void ForwardForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
inline ::google::protobuf::int32 ForwardForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AmountLevelAllin)
  return amountlevelallin_;
}
inline void ForwardForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
inline void ForwardForex::clear_rateside() {
  rateside_ = 0;
}
inline ::google::protobuf::int32 ForwardForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.RateSide)
  return rateside_;
}
inline void ForwardForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.RateSide)
}

// optional int32 AllinSide = 20;
inline void ForwardForex::clear_allinside() {
  allinside_ = 0;
}
inline ::google::protobuf::int32 ForwardForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AllinSide)
  return allinside_;
}
inline void ForwardForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AllinSide)
}

inline const ForwardForex* ForwardForex::internal_default_instance() {
  return &ForwardForex_default_instance_.get();
}
// -------------------------------------------------------------------

// NonDeliverableForwardsForex

// optional string ValueDate = 1;
inline void NonDeliverableForwardsForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NonDeliverableForwardsForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
inline void NonDeliverableForwardsForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
inline void NonDeliverableForwardsForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
inline ::std::string* NonDeliverableForwardsForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NonDeliverableForwardsForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
inline void NonDeliverableForwardsForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.NetBasisChange)
  return netbasischange_;
}
inline void NonDeliverableForwardsForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
inline void NonDeliverableForwardsForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.PercentageChange)
  return percentagechange_;
}
inline void NonDeliverableForwardsForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.PercentageChange)
}

// optional string BuyDate = 4;
inline void NonDeliverableForwardsForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NonDeliverableForwardsForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
inline void NonDeliverableForwardsForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
inline void NonDeliverableForwardsForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
inline ::std::string* NonDeliverableForwardsForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NonDeliverableForwardsForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}

// optional string BuyTime = 5;
inline void NonDeliverableForwardsForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NonDeliverableForwardsForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
inline void NonDeliverableForwardsForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
inline void NonDeliverableForwardsForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
inline ::std::string* NonDeliverableForwardsForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NonDeliverableForwardsForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}

// optional string SellDate = 6;
inline void NonDeliverableForwardsForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NonDeliverableForwardsForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
inline void NonDeliverableForwardsForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
inline void NonDeliverableForwardsForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
inline ::std::string* NonDeliverableForwardsForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NonDeliverableForwardsForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}

// optional string SellTime = 7;
inline void NonDeliverableForwardsForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& NonDeliverableForwardsForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
inline void NonDeliverableForwardsForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
inline void NonDeliverableForwardsForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
inline ::std::string* NonDeliverableForwardsForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* NonDeliverableForwardsForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void NonDeliverableForwardsForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}

// optional int64 LastBuyRate = 8;
inline void NonDeliverableForwardsForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyRate)
  return lastbuyrate_;
}
inline void NonDeliverableForwardsForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
inline void NonDeliverableForwardsForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellRate)
  return lastsellrate_;
}
inline void NonDeliverableForwardsForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
inline void NonDeliverableForwardsForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyAllin)
  return lastbuyallin_;
}
inline void NonDeliverableForwardsForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
inline void NonDeliverableForwardsForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellAllin)
  return lastsellallin_;
}
inline void NonDeliverableForwardsForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellAllin)
}

// optional int64 HighRate = 12;
inline void NonDeliverableForwardsForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HighRate)
  return highrate_;
}
inline void NonDeliverableForwardsForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HighRate)
}

// optional int64 LowRate = 13;
inline void NonDeliverableForwardsForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LowRate)
  return lowrate_;
}
inline void NonDeliverableForwardsForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LowRate)
}

// optional int64 OpenRate = 14;
inline void NonDeliverableForwardsForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.OpenRate)
  return openrate_;
}
inline void NonDeliverableForwardsForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
inline void NonDeliverableForwardsForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HistoryCloseRate)
  return historycloserate_;
}
inline void NonDeliverableForwardsForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
inline void NonDeliverableForwardsForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 NonDeliverableForwardsForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.CloseRate)
  return closerate_;
}
inline void NonDeliverableForwardsForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
inline void NonDeliverableForwardsForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
inline ::google::protobuf::int32 NonDeliverableForwardsForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelRate)
  return amountlevelrate_;
}
inline void NonDeliverableForwardsForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
inline void NonDeliverableForwardsForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
inline ::google::protobuf::int32 NonDeliverableForwardsForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelAllin)
  return amountlevelallin_;
}
inline void NonDeliverableForwardsForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
inline void NonDeliverableForwardsForex::clear_rateside() {
  rateside_ = 0;
}
inline ::google::protobuf::int32 NonDeliverableForwardsForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.RateSide)
  return rateside_;
}
inline void NonDeliverableForwardsForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.RateSide)
}

// optional int32 AllinSide = 20;
inline void NonDeliverableForwardsForex::clear_allinside() {
  allinside_ = 0;
}
inline ::google::protobuf::int32 NonDeliverableForwardsForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AllinSide)
  return allinside_;
}
inline void NonDeliverableForwardsForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AllinSide)
}

inline const NonDeliverableForwardsForex* NonDeliverableForwardsForex::internal_default_instance() {
  return &NonDeliverableForwardsForex_default_instance_.get();
}
// -------------------------------------------------------------------

// SwapForex

// optional string ValueDate = 1;
inline void SwapForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
inline void SwapForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
inline void SwapForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
inline ::std::string* SwapForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
inline void SwapForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.NetBasisChange)
  return netbasischange_;
}
inline void SwapForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
inline void SwapForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.PercentageChange)
  return percentagechange_;
}
inline void SwapForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.PercentageChange)
}

// optional string BuyDate = 4;
inline void SwapForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
inline void SwapForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
inline void SwapForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
inline ::std::string* SwapForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}

// optional string BuyTime = 5;
inline void SwapForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
inline void SwapForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
inline void SwapForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
inline ::std::string* SwapForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}

// optional string SellDate = 6;
inline void SwapForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
inline void SwapForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
inline void SwapForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
inline ::std::string* SwapForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.SellDate)
}

// optional string SellTime = 7;
inline void SwapForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
inline void SwapForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
inline void SwapForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
inline ::std::string* SwapForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.SellTime)
}

// optional int64 LastBuyRate = 8;
inline void SwapForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastBuyRate)
  return lastbuyrate_;
}
inline void SwapForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
inline void SwapForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastSellRate)
  return lastsellrate_;
}
inline void SwapForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
inline void SwapForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastBuyAllin)
  return lastbuyallin_;
}
inline void SwapForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
inline void SwapForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastSellAllin)
  return lastsellallin_;
}
inline void SwapForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastSellAllin)
}

// optional int64 HighRate = 12;
inline void SwapForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.HighRate)
  return highrate_;
}
inline void SwapForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.HighRate)
}

// optional int64 LowRate = 13;
inline void SwapForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LowRate)
  return lowrate_;
}
inline void SwapForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LowRate)
}

// optional int64 OpenRate = 14;
inline void SwapForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.OpenRate)
  return openrate_;
}
inline void SwapForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
inline void SwapForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.HistoryCloseRate)
  return historycloserate_;
}
inline void SwapForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
inline void SwapForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwapForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.CloseRate)
  return closerate_;
}
inline void SwapForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
inline void SwapForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
inline ::google::protobuf::int32 SwapForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AmountLevelRate)
  return amountlevelrate_;
}
inline void SwapForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
inline void SwapForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
inline ::google::protobuf::int32 SwapForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AmountLevelAllin)
  return amountlevelallin_;
}
inline void SwapForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
inline void SwapForex::clear_rateside() {
  rateside_ = 0;
}
inline ::google::protobuf::int32 SwapForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.RateSide)
  return rateside_;
}
inline void SwapForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.RateSide)
}

// optional int32 AllinSide = 20;
inline void SwapForex::clear_allinside() {
  allinside_ = 0;
}
inline ::google::protobuf::int32 SwapForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AllinSide)
  return allinside_;
}
inline void SwapForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AllinSide)
}

// optional string LegSign = 21;
inline void SwapForex::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwapForex::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
inline void SwapForex::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
inline void SwapForex::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
inline ::std::string* SwapForex::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwapForex::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwapForex::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.LegSign)
}

inline const SwapForex* SwapForex::internal_default_instance() {
  return &SwapForex_default_instance_.get();
}
// -------------------------------------------------------------------

// OptionForex

// optional string FxTerm = 1;
inline void OptionForex::clear_fxterm() {
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionForex::fxterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  return fxterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_fxterm(const ::std::string& value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
inline void OptionForex::set_fxterm(const char* value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
inline void OptionForex::set_fxterm(const char* value, size_t size) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
inline ::std::string* OptionForex::mutable_fxterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  return fxterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionForex::release_fxterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  
  return fxterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_allocated_fxterm(::std::string* fxterm) {
  if (fxterm != NULL) {
    
  } else {
    
  }
  fxterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fxterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}

// optional int64 Premium = 2;
inline void OptionForex::clear_premium() {
  premium_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionForex::premium() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Premium)
  return premium_;
}
inline void OptionForex::set_premium(::google::protobuf::int64 value) {
  
  premium_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Premium)
}

// optional int64 Volatility = 3;
inline void OptionForex::clear_volatility() {
  volatility_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionForex::volatility() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Volatility)
  return volatility_;
}
inline void OptionForex::set_volatility(::google::protobuf::int64 value) {
  
  volatility_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Volatility)
}

// optional int64 Volume = 4;
inline void OptionForex::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionForex::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Volume)
  return volume_;
}
inline void OptionForex::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Volume)
}

// optional string TradeDate = 5;
inline void OptionForex::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionForex::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
inline void OptionForex::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
inline void OptionForex::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
inline ::std::string* OptionForex::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionForex::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}

// optional string TradeTime = 6;
inline void OptionForex::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionForex::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
inline void OptionForex::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
inline void OptionForex::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
inline ::std::string* OptionForex::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionForex::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}

// optional int32 PremiumType = 7;
inline void OptionForex::clear_premiumtype() {
  premiumtype_ = 0;
}
inline ::google::protobuf::int32 OptionForex::premiumtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.PremiumType)
  return premiumtype_;
}
inline void OptionForex::set_premiumtype(::google::protobuf::int32 value) {
  
  premiumtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.PremiumType)
}

// optional string OptionType = 8;
inline void OptionForex::clear_optiontype() {
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionForex::optiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.OptionType)
  return optiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_optiontype(const ::std::string& value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
inline void OptionForex::set_optiontype(const char* value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
inline void OptionForex::set_optiontype(const char* value, size_t size) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
inline ::std::string* OptionForex::mutable_optiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.OptionType)
  return optiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionForex::release_optiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.OptionType)
  
  return optiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionForex::set_allocated_optiontype(::std::string* optiontype) {
  if (optiontype != NULL) {
    
  } else {
    
  }
  optiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.OptionType)
}

inline const OptionForex* OptionForex::internal_default_instance() {
  return &OptionForex_default_instance_.get();
}
// -------------------------------------------------------------------

// SpotClosePriceForex

// optional int64 ClosePx = 1;
inline void SpotClosePriceForex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotClosePriceForex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.ClosePx)
  return closepx_;
}
inline void SpotClosePriceForex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.ClosePx)
}

// optional string UpdateDate = 2;
inline void SpotClosePriceForex::clear_updatedate() {
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotClosePriceForex::updatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  return updatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceForex::set_updatedate(const ::std::string& value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
inline void SpotClosePriceForex::set_updatedate(const char* value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
inline void SpotClosePriceForex::set_updatedate(const char* value, size_t size) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
inline ::std::string* SpotClosePriceForex::mutable_updatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  return updatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotClosePriceForex::release_updatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  
  return updatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceForex::set_allocated_updatedate(::std::string* updatedate) {
  if (updatedate != NULL) {
    
  } else {
    
  }
  updatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}

// optional string UpdateTime = 3;
inline void SpotClosePriceForex::clear_updatetime() {
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotClosePriceForex::updatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  return updatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceForex::set_updatetime(const ::std::string& value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
inline void SpotClosePriceForex::set_updatetime(const char* value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
inline void SpotClosePriceForex::set_updatetime(const char* value, size_t size) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
inline ::std::string* SpotClosePriceForex::mutable_updatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  return updatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotClosePriceForex::release_updatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  
  return updatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceForex::set_allocated_updatetime(::std::string* updatetime) {
  if (updatetime != NULL) {
    
  } else {
    
  }
  updatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}

inline const SpotClosePriceForex* SpotClosePriceForex::internal_default_instance() {
  return &SpotClosePriceForex_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsForex_2eproto__INCLUDED
