syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDQBQuote {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  string QuoteID = 7;
  double BidPrice = 8;
  double AskPrice = 9;
  double BidSize = 10;
  double AskSize = 11;
  string QuoteTime = 12;
  int32 DataMultiplePowerOf10 = 13;
  int64 MessageNumber = 100;
}
