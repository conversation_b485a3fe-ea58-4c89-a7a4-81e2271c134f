// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxSnapshot.proto

#ifndef PROTOBUF_MDCfetsFxSnapshot_2eproto__INCLUDED
#define PROTOBUF_MDCfetsFxSnapshot_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsFxSnapshot_2eproto();
void protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto();
void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto();

class MDCfetsFxSnapshot;
class OptionFxSnapshot;
class SpotClosePriceFxSnapshot;
class SwpSptNdfFowFxSnapshot;

// ===================================================================

class MDCfetsFxSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsFxSnapshot) */ {
 public:
  MDCfetsFxSnapshot();
  virtual ~MDCfetsFxSnapshot();

  MDCfetsFxSnapshot(const MDCfetsFxSnapshot& from);

  inline MDCfetsFxSnapshot& operator=(const MDCfetsFxSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsFxSnapshot& default_instance();

  static const MDCfetsFxSnapshot* internal_default_instance();

  void Swap(MDCfetsFxSnapshot* other);

  // implements Message ----------------------------------------------

  inline MDCfetsFxSnapshot* New() const { return New(NULL); }

  MDCfetsFxSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsFxSnapshot& from);
  void MergeFrom(const MDCfetsFxSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsFxSnapshot* other);
  void UnsafeMergeFrom(const MDCfetsFxSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string SecuritySubType = 7;
  void clear_securitysubtype();
  static const int kSecuritySubTypeFieldNumber = 7;
  const ::std::string& securitysubtype() const;
  void set_securitysubtype(const ::std::string& value);
  void set_securitysubtype(const char* value);
  void set_securitysubtype(const char* value, size_t size);
  ::std::string* mutable_securitysubtype();
  ::std::string* release_securitysubtype();
  void set_allocated_securitysubtype(::std::string* securitysubtype);

  // optional int32 ForexSnapshotType = 8;
  void clear_forexsnapshottype();
  static const int kForexSnapshotTypeFieldNumber = 8;
  ::google::protobuf::int32 forexsnapshottype() const;
  void set_forexsnapshottype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
  bool has_spotfxsnapshot() const;
  void clear_spotfxsnapshot();
  static const int kSpotFxSnapshotFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& spotfxsnapshot() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* mutable_spotfxsnapshot();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* release_spotfxsnapshot();
  void set_allocated_spotfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* spotfxsnapshot);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
  bool has_forwardfxsnapshot() const;
  void clear_forwardfxsnapshot();
  static const int kForwardFxSnapshotFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& forwardfxsnapshot() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* mutable_forwardfxsnapshot();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* release_forwardfxsnapshot();
  void set_allocated_forwardfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* forwardfxsnapshot);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
  bool has_nondeliverableforwardsfxsnapshot() const;
  void clear_nondeliverableforwardsfxsnapshot();
  static const int kNonDeliverableForwardsFxSnapshotFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& nondeliverableforwardsfxsnapshot() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* mutable_nondeliverableforwardsfxsnapshot();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* release_nondeliverableforwardsfxsnapshot();
  void set_allocated_nondeliverableforwardsfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* nondeliverableforwardsfxsnapshot);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
  bool has_swapfxsnapshot() const;
  void clear_swapfxsnapshot();
  static const int kSwapFxSnapshotFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& swapfxsnapshot() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* mutable_swapfxsnapshot();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* release_swapfxsnapshot();
  void set_allocated_swapfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* swapfxsnapshot);

  // optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
  bool has_optionfxsnapshot() const;
  void clear_optionfxsnapshot();
  static const int kOptionFxSnapshotFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::OptionFxSnapshot& optionfxsnapshot() const;
  ::com::htsc::mdc::insight::model::OptionFxSnapshot* mutable_optionfxsnapshot();
  ::com::htsc::mdc::insight::model::OptionFxSnapshot* release_optionfxsnapshot();
  void set_allocated_optionfxsnapshot(::com::htsc::mdc::insight::model::OptionFxSnapshot* optionfxsnapshot);

  // optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
  bool has_spotclosepricefxsnapshot() const;
  void clear_spotclosepricefxsnapshot();
  static const int kSpotClosePriceFxSnapshotFieldNumber = 14;
  const ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot& spotclosepricefxsnapshot() const;
  ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* mutable_spotclosepricefxsnapshot();
  ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* release_spotclosepricefxsnapshot();
  void set_allocated_spotclosepricefxsnapshot(::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* spotclosepricefxsnapshot);

  // optional int32 DataMultiplePowerOf10 = 15;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 15;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string TransactTime = 16;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 16;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 20;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 20;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsFxSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securitysubtype_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* spotfxsnapshot_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* forwardfxsnapshot_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* nondeliverableforwardsfxsnapshot_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* swapfxsnapshot_;
  ::com::htsc::mdc::insight::model::OptionFxSnapshot* optionfxsnapshot_;
  ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* spotclosepricefxsnapshot_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 forexsnapshottype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxSnapshot> MDCfetsFxSnapshot_default_instance_;

// -------------------------------------------------------------------

class SwpSptNdfFowFxSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot) */ {
 public:
  SwpSptNdfFowFxSnapshot();
  virtual ~SwpSptNdfFowFxSnapshot();

  SwpSptNdfFowFxSnapshot(const SwpSptNdfFowFxSnapshot& from);

  inline SwpSptNdfFowFxSnapshot& operator=(const SwpSptNdfFowFxSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SwpSptNdfFowFxSnapshot& default_instance();

  static const SwpSptNdfFowFxSnapshot* internal_default_instance();

  void Swap(SwpSptNdfFowFxSnapshot* other);

  // implements Message ----------------------------------------------

  inline SwpSptNdfFowFxSnapshot* New() const { return New(NULL); }

  SwpSptNdfFowFxSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SwpSptNdfFowFxSnapshot& from);
  void MergeFrom(const SwpSptNdfFowFxSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SwpSptNdfFowFxSnapshot* other);
  void UnsafeMergeFrom(const SwpSptNdfFowFxSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string ValueDate = 1;
  void clear_valuedate();
  static const int kValueDateFieldNumber = 1;
  const ::std::string& valuedate() const;
  void set_valuedate(const ::std::string& value);
  void set_valuedate(const char* value);
  void set_valuedate(const char* value, size_t size);
  ::std::string* mutable_valuedate();
  ::std::string* release_valuedate();
  void set_allocated_valuedate(::std::string* valuedate);

  // optional int64 NetBasisChange = 2;
  void clear_netbasischange();
  static const int kNetBasisChangeFieldNumber = 2;
  ::google::protobuf::int64 netbasischange() const;
  void set_netbasischange(::google::protobuf::int64 value);

  // optional int64 PercentageChange = 3;
  void clear_percentagechange();
  static const int kPercentageChangeFieldNumber = 3;
  ::google::protobuf::int64 percentagechange() const;
  void set_percentagechange(::google::protobuf::int64 value);

  // optional string DateBuy = 4;
  void clear_datebuy();
  static const int kDateBuyFieldNumber = 4;
  const ::std::string& datebuy() const;
  void set_datebuy(const ::std::string& value);
  void set_datebuy(const char* value);
  void set_datebuy(const char* value, size_t size);
  ::std::string* mutable_datebuy();
  ::std::string* release_datebuy();
  void set_allocated_datebuy(::std::string* datebuy);

  // optional string TimeBuy = 5;
  void clear_timebuy();
  static const int kTimeBuyFieldNumber = 5;
  const ::std::string& timebuy() const;
  void set_timebuy(const ::std::string& value);
  void set_timebuy(const char* value);
  void set_timebuy(const char* value, size_t size);
  ::std::string* mutable_timebuy();
  ::std::string* release_timebuy();
  void set_allocated_timebuy(::std::string* timebuy);

  // optional string DateSell = 6;
  void clear_datesell();
  static const int kDateSellFieldNumber = 6;
  const ::std::string& datesell() const;
  void set_datesell(const ::std::string& value);
  void set_datesell(const char* value);
  void set_datesell(const char* value, size_t size);
  ::std::string* mutable_datesell();
  ::std::string* release_datesell();
  void set_allocated_datesell(::std::string* datesell);

  // optional string TimeSell = 7;
  void clear_timesell();
  static const int kTimeSellFieldNumber = 7;
  const ::std::string& timesell() const;
  void set_timesell(const ::std::string& value);
  void set_timesell(const char* value);
  void set_timesell(const char* value, size_t size);
  ::std::string* mutable_timesell();
  ::std::string* release_timesell();
  void set_allocated_timesell(::std::string* timesell);

  // optional int64 LastRateBuy = 8;
  void clear_lastratebuy();
  static const int kLastRateBuyFieldNumber = 8;
  ::google::protobuf::int64 lastratebuy() const;
  void set_lastratebuy(::google::protobuf::int64 value);

  // optional int64 LastRateSell = 9;
  void clear_lastratesell();
  static const int kLastRateSellFieldNumber = 9;
  ::google::protobuf::int64 lastratesell() const;
  void set_lastratesell(::google::protobuf::int64 value);

  // optional int64 LastAllinBuy = 10;
  void clear_lastallinbuy();
  static const int kLastAllinBuyFieldNumber = 10;
  ::google::protobuf::int64 lastallinbuy() const;
  void set_lastallinbuy(::google::protobuf::int64 value);

  // optional int64 LastAllinSell = 11;
  void clear_lastallinsell();
  static const int kLastAllinSellFieldNumber = 11;
  ::google::protobuf::int64 lastallinsell() const;
  void set_lastallinsell(::google::protobuf::int64 value);

  // optional int64 HighRate = 12;
  void clear_highrate();
  static const int kHighRateFieldNumber = 12;
  ::google::protobuf::int64 highrate() const;
  void set_highrate(::google::protobuf::int64 value);

  // optional int64 LowRate = 13;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 13;
  ::google::protobuf::int64 lowrate() const;
  void set_lowrate(::google::protobuf::int64 value);

  // optional int64 OpenRate = 14;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 14;
  ::google::protobuf::int64 openrate() const;
  void set_openrate(::google::protobuf::int64 value);

  // optional int64 HistoryCloseRate = 15;
  void clear_historycloserate();
  static const int kHistoryCloseRateFieldNumber = 15;
  ::google::protobuf::int64 historycloserate() const;
  void set_historycloserate(::google::protobuf::int64 value);

  // optional int64 CloseRate = 16;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 16;
  ::google::protobuf::int64 closerate() const;
  void set_closerate(::google::protobuf::int64 value);

  // optional int32 AmountLevelRate = 17;
  void clear_amountlevelrate();
  static const int kAmountLevelRateFieldNumber = 17;
  ::google::protobuf::int32 amountlevelrate() const;
  void set_amountlevelrate(::google::protobuf::int32 value);

  // optional int32 AmountLevelAllin = 18;
  void clear_amountlevelallin();
  static const int kAmountLevelAllinFieldNumber = 18;
  ::google::protobuf::int32 amountlevelallin() const;
  void set_amountlevelallin(::google::protobuf::int32 value);

  // optional int32 RateSide = 19;
  void clear_rateside();
  static const int kRateSideFieldNumber = 19;
  ::google::protobuf::int32 rateside() const;
  void set_rateside(::google::protobuf::int32 value);

  // optional int32 AllinSide = 20;
  void clear_allinside();
  static const int kAllinSideFieldNumber = 20;
  ::google::protobuf::int32 allinside() const;
  void set_allinside(::google::protobuf::int32 value);

  // optional string LegSign = 21;
  void clear_legsign();
  static const int kLegSignFieldNumber = 21;
  const ::std::string& legsign() const;
  void set_legsign(const ::std::string& value);
  void set_legsign(const char* value);
  void set_legsign(const char* value, size_t size);
  ::std::string* mutable_legsign();
  ::std::string* release_legsign();
  void set_allocated_legsign(::std::string* legsign);

  // optional string FillSide = 22;
  void clear_fillside();
  static const int kFillSideFieldNumber = 22;
  const ::std::string& fillside() const;
  void set_fillside(const ::std::string& value);
  void set_fillside(const char* value);
  void set_fillside(const char* value, size_t size);
  ::std::string* mutable_fillside();
  ::std::string* release_fillside();
  void set_allocated_fillside(::std::string* fillside);

  // optional string DateConfirmed = 23;
  void clear_dateconfirmed();
  static const int kDateConfirmedFieldNumber = 23;
  const ::std::string& dateconfirmed() const;
  void set_dateconfirmed(const ::std::string& value);
  void set_dateconfirmed(const char* value);
  void set_dateconfirmed(const char* value, size_t size);
  ::std::string* mutable_dateconfirmed();
  ::std::string* release_dateconfirmed();
  void set_allocated_dateconfirmed(::std::string* dateconfirmed);

  // optional bool ContingencyWithdraw = 24;
  void clear_contingencywithdraw();
  static const int kContingencyWithdrawFieldNumber = 24;
  bool contingencywithdraw() const;
  void set_contingencywithdraw(bool value);

  // optional string ContingencyTradeDate = 25;
  void clear_contingencytradedate();
  static const int kContingencyTradeDateFieldNumber = 25;
  const ::std::string& contingencytradedate() const;
  void set_contingencytradedate(const ::std::string& value);
  void set_contingencytradedate(const char* value);
  void set_contingencytradedate(const char* value, size_t size);
  ::std::string* mutable_contingencytradedate();
  ::std::string* release_contingencytradedate();
  void set_allocated_contingencytradedate(::std::string* contingencytradedate);

  // optional string ContingencyTradeTime = 26;
  void clear_contingencytradetime();
  static const int kContingencyTradeTimeFieldNumber = 26;
  const ::std::string& contingencytradetime() const;
  void set_contingencytradetime(const ::std::string& value);
  void set_contingencytradetime(const char* value);
  void set_contingencytradetime(const char* value, size_t size);
  ::std::string* mutable_contingencytradetime();
  ::std::string* release_contingencytradetime();
  void set_allocated_contingencytradetime(::std::string* contingencytradetime);

  // optional int64 ContingencyLastPx = 27;
  void clear_contingencylastpx();
  static const int kContingencyLastPxFieldNumber = 27;
  ::google::protobuf::int64 contingencylastpx() const;
  void set_contingencylastpx(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valuedate_;
  ::google::protobuf::internal::ArenaStringPtr datebuy_;
  ::google::protobuf::internal::ArenaStringPtr timebuy_;
  ::google::protobuf::internal::ArenaStringPtr datesell_;
  ::google::protobuf::internal::ArenaStringPtr timesell_;
  ::google::protobuf::internal::ArenaStringPtr legsign_;
  ::google::protobuf::internal::ArenaStringPtr fillside_;
  ::google::protobuf::internal::ArenaStringPtr dateconfirmed_;
  ::google::protobuf::internal::ArenaStringPtr contingencytradedate_;
  ::google::protobuf::internal::ArenaStringPtr contingencytradetime_;
  ::google::protobuf::int64 netbasischange_;
  ::google::protobuf::int64 percentagechange_;
  ::google::protobuf::int64 lastratebuy_;
  ::google::protobuf::int64 lastratesell_;
  ::google::protobuf::int64 lastallinbuy_;
  ::google::protobuf::int64 lastallinsell_;
  ::google::protobuf::int64 highrate_;
  ::google::protobuf::int64 lowrate_;
  ::google::protobuf::int64 openrate_;
  ::google::protobuf::int64 historycloserate_;
  ::google::protobuf::int64 closerate_;
  ::google::protobuf::int32 amountlevelrate_;
  ::google::protobuf::int32 amountlevelallin_;
  ::google::protobuf::int32 rateside_;
  ::google::protobuf::int32 allinside_;
  ::google::protobuf::int64 contingencylastpx_;
  bool contingencywithdraw_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SwpSptNdfFowFxSnapshot> SwpSptNdfFowFxSnapshot_default_instance_;

// -------------------------------------------------------------------

class OptionFxSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.OptionFxSnapshot) */ {
 public:
  OptionFxSnapshot();
  virtual ~OptionFxSnapshot();

  OptionFxSnapshot(const OptionFxSnapshot& from);

  inline OptionFxSnapshot& operator=(const OptionFxSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OptionFxSnapshot& default_instance();

  static const OptionFxSnapshot* internal_default_instance();

  void Swap(OptionFxSnapshot* other);

  // implements Message ----------------------------------------------

  inline OptionFxSnapshot* New() const { return New(NULL); }

  OptionFxSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OptionFxSnapshot& from);
  void MergeFrom(const OptionFxSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OptionFxSnapshot* other);
  void UnsafeMergeFrom(const OptionFxSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string FxTerm = 1;
  void clear_fxterm();
  static const int kFxTermFieldNumber = 1;
  const ::std::string& fxterm() const;
  void set_fxterm(const ::std::string& value);
  void set_fxterm(const char* value);
  void set_fxterm(const char* value, size_t size);
  ::std::string* mutable_fxterm();
  ::std::string* release_fxterm();
  void set_allocated_fxterm(::std::string* fxterm);

  // optional int64 Premium = 2;
  void clear_premium();
  static const int kPremiumFieldNumber = 2;
  ::google::protobuf::int64 premium() const;
  void set_premium(::google::protobuf::int64 value);

  // optional int64 Volatility = 3;
  void clear_volatility();
  static const int kVolatilityFieldNumber = 3;
  ::google::protobuf::int64 volatility() const;
  void set_volatility(::google::protobuf::int64 value);

  // optional int64 Volume = 4;
  void clear_volume();
  static const int kVolumeFieldNumber = 4;
  ::google::protobuf::int64 volume() const;
  void set_volume(::google::protobuf::int64 value);

  // optional string TradeDate = 5;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 5;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string TradeTime = 6;
  void clear_tradetime();
  static const int kTradeTimeFieldNumber = 6;
  const ::std::string& tradetime() const;
  void set_tradetime(const ::std::string& value);
  void set_tradetime(const char* value);
  void set_tradetime(const char* value, size_t size);
  ::std::string* mutable_tradetime();
  ::std::string* release_tradetime();
  void set_allocated_tradetime(::std::string* tradetime);

  // optional int32 PremiumType = 7;
  void clear_premiumtype();
  static const int kPremiumTypeFieldNumber = 7;
  ::google::protobuf::int32 premiumtype() const;
  void set_premiumtype(::google::protobuf::int32 value);

  // optional string OptionType = 8;
  void clear_optiontype();
  static const int kOptionTypeFieldNumber = 8;
  const ::std::string& optiontype() const;
  void set_optiontype(const ::std::string& value);
  void set_optiontype(const char* value);
  void set_optiontype(const char* value, size_t size);
  ::std::string* mutable_optiontype();
  ::std::string* release_optiontype();
  void set_allocated_optiontype(::std::string* optiontype);

  // optional string DateConfirmed = 9;
  void clear_dateconfirmed();
  static const int kDateConfirmedFieldNumber = 9;
  const ::std::string& dateconfirmed() const;
  void set_dateconfirmed(const ::std::string& value);
  void set_dateconfirmed(const char* value);
  void set_dateconfirmed(const char* value, size_t size);
  ::std::string* mutable_dateconfirmed();
  ::std::string* release_dateconfirmed();
  void set_allocated_dateconfirmed(::std::string* dateconfirmed);

  // optional int32 OptionTypeEnum = 10;
  void clear_optiontypeenum();
  static const int kOptionTypeEnumFieldNumber = 10;
  ::google::protobuf::int32 optiontypeenum() const;
  void set_optiontypeenum(::google::protobuf::int32 value);

  // optional string DerivativeExerciseStyle = 11;
  void clear_derivativeexercisestyle();
  static const int kDerivativeExerciseStyleFieldNumber = 11;
  const ::std::string& derivativeexercisestyle() const;
  void set_derivativeexercisestyle(const ::std::string& value);
  void set_derivativeexercisestyle(const char* value);
  void set_derivativeexercisestyle(const char* value, size_t size);
  ::std::string* mutable_derivativeexercisestyle();
  ::std::string* release_derivativeexercisestyle();
  void set_allocated_derivativeexercisestyle(::std::string* derivativeexercisestyle);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.OptionFxSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr fxterm_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr tradetime_;
  ::google::protobuf::internal::ArenaStringPtr optiontype_;
  ::google::protobuf::internal::ArenaStringPtr dateconfirmed_;
  ::google::protobuf::internal::ArenaStringPtr derivativeexercisestyle_;
  ::google::protobuf::int64 premium_;
  ::google::protobuf::int64 volatility_;
  ::google::protobuf::int64 volume_;
  ::google::protobuf::int32 premiumtype_;
  ::google::protobuf::int32 optiontypeenum_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OptionFxSnapshot> OptionFxSnapshot_default_instance_;

// -------------------------------------------------------------------

class SpotClosePriceFxSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot) */ {
 public:
  SpotClosePriceFxSnapshot();
  virtual ~SpotClosePriceFxSnapshot();

  SpotClosePriceFxSnapshot(const SpotClosePriceFxSnapshot& from);

  inline SpotClosePriceFxSnapshot& operator=(const SpotClosePriceFxSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SpotClosePriceFxSnapshot& default_instance();

  static const SpotClosePriceFxSnapshot* internal_default_instance();

  void Swap(SpotClosePriceFxSnapshot* other);

  // implements Message ----------------------------------------------

  inline SpotClosePriceFxSnapshot* New() const { return New(NULL); }

  SpotClosePriceFxSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SpotClosePriceFxSnapshot& from);
  void MergeFrom(const SpotClosePriceFxSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SpotClosePriceFxSnapshot* other);
  void UnsafeMergeFrom(const SpotClosePriceFxSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int64 ClosePx = 1;
  void clear_closepx();
  static const int kClosePxFieldNumber = 1;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional string UpdateDate = 2;
  void clear_updatedate();
  static const int kUpdateDateFieldNumber = 2;
  const ::std::string& updatedate() const;
  void set_updatedate(const ::std::string& value);
  void set_updatedate(const char* value);
  void set_updatedate(const char* value, size_t size);
  ::std::string* mutable_updatedate();
  ::std::string* release_updatedate();
  void set_allocated_updatedate(::std::string* updatedate);

  // optional string UpdateTime = 3;
  void clear_updatetime();
  static const int kUpdateTimeFieldNumber = 3;
  const ::std::string& updatetime() const;
  void set_updatetime(const ::std::string& value);
  void set_updatetime(const char* value);
  void set_updatetime(const char* value, size_t size);
  ::std::string* mutable_updatetime();
  ::std::string* release_updatetime();
  void set_allocated_updatetime(::std::string* updatetime);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr updatedate_;
  ::google::protobuf::internal::ArenaStringPtr updatetime_;
  ::google::protobuf::int64 closepx_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SpotClosePriceFxSnapshot> SpotClosePriceFxSnapshot_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxSnapshot

// optional string HTSCSecurityID = 1;
inline void MDCfetsFxSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
inline void MDCfetsFxSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
inline void MDCfetsFxSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}
inline ::std::string* MDCfetsFxSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCfetsFxSnapshot::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDDate)
  return mddate_;
}
inline void MDCfetsFxSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCfetsFxSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDTime)
  return mdtime_;
}
inline void MDCfetsFxSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCfetsFxSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsFxSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCfetsFxSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsFxSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCfetsFxSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsFxSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsFxSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.securityType)
}

// optional string SecuritySubType = 7;
inline void MDCfetsFxSnapshot::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxSnapshot::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
inline void MDCfetsFxSnapshot::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
inline void MDCfetsFxSnapshot::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}
inline ::std::string* MDCfetsFxSnapshot::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxSnapshot::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.SecuritySubType)
}

// optional int32 ForexSnapshotType = 8;
inline void MDCfetsFxSnapshot::clear_forexsnapshottype() {
  forexsnapshottype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxSnapshot::forexsnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.ForexSnapshotType)
  return forexsnapshottype_;
}
inline void MDCfetsFxSnapshot::set_forexsnapshottype(::google::protobuf::int32 value) {
  
  forexsnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.ForexSnapshotType)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot spotFxSnapshot = 9;
inline bool MDCfetsFxSnapshot::has_spotfxsnapshot() const {
  return this != internal_default_instance() && spotfxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_spotfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && spotfxsnapshot_ != NULL) delete spotfxsnapshot_;
  spotfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::spotfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  return spotfxsnapshot_ != NULL ? *spotfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_spotfxsnapshot() {
  
  if (spotfxsnapshot_ == NULL) {
    spotfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  return spotfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_spotfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = spotfxsnapshot_;
  spotfxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_spotfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* spotfxsnapshot) {
  delete spotfxsnapshot_;
  spotfxsnapshot_ = spotfxsnapshot;
  if (spotfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot forwardFxSnapshot = 10;
inline bool MDCfetsFxSnapshot::has_forwardfxsnapshot() const {
  return this != internal_default_instance() && forwardfxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_forwardfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && forwardfxsnapshot_ != NULL) delete forwardfxsnapshot_;
  forwardfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::forwardfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  return forwardfxsnapshot_ != NULL ? *forwardfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_forwardfxsnapshot() {
  
  if (forwardfxsnapshot_ == NULL) {
    forwardfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  return forwardfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_forwardfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = forwardfxsnapshot_;
  forwardfxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_forwardfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* forwardfxsnapshot) {
  delete forwardfxsnapshot_;
  forwardfxsnapshot_ = forwardfxsnapshot;
  if (forwardfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.forwardFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot nonDeliverableForwardsFxSnapshot = 11;
inline bool MDCfetsFxSnapshot::has_nondeliverableforwardsfxsnapshot() const {
  return this != internal_default_instance() && nondeliverableforwardsfxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_nondeliverableforwardsfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxsnapshot_ != NULL) delete nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::nondeliverableforwardsfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  return nondeliverableforwardsfxsnapshot_ != NULL ? *nondeliverableforwardsfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_nondeliverableforwardsfxsnapshot() {
  
  if (nondeliverableforwardsfxsnapshot_ == NULL) {
    nondeliverableforwardsfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  return nondeliverableforwardsfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_nondeliverableforwardsfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_nondeliverableforwardsfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* nondeliverableforwardsfxsnapshot) {
  delete nondeliverableforwardsfxsnapshot_;
  nondeliverableforwardsfxsnapshot_ = nondeliverableforwardsfxsnapshot;
  if (nondeliverableforwardsfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.nonDeliverableForwardsFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot swapFxSnapshot = 12;
inline bool MDCfetsFxSnapshot::has_swapfxsnapshot() const {
  return this != internal_default_instance() && swapfxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_swapfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && swapfxsnapshot_ != NULL) delete swapfxsnapshot_;
  swapfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot& MDCfetsFxSnapshot::swapfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  return swapfxsnapshot_ != NULL ? *swapfxsnapshot_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::mutable_swapfxsnapshot() {
  
  if (swapfxsnapshot_ == NULL) {
    swapfxsnapshot_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  return swapfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* MDCfetsFxSnapshot::release_swapfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* temp = swapfxsnapshot_;
  swapfxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_swapfxsnapshot(::com::htsc::mdc::insight::model::SwpSptNdfFowFxSnapshot* swapfxsnapshot) {
  delete swapfxsnapshot_;
  swapfxsnapshot_ = swapfxsnapshot;
  if (swapfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.swapFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.OptionFxSnapshot optionFxSnapshot = 13;
inline bool MDCfetsFxSnapshot::has_optionfxsnapshot() const {
  return this != internal_default_instance() && optionfxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_optionfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && optionfxsnapshot_ != NULL) delete optionfxsnapshot_;
  optionfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::OptionFxSnapshot& MDCfetsFxSnapshot::optionfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  return optionfxsnapshot_ != NULL ? *optionfxsnapshot_
                         : *::com::htsc::mdc::insight::model::OptionFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::OptionFxSnapshot* MDCfetsFxSnapshot::mutable_optionfxsnapshot() {
  
  if (optionfxsnapshot_ == NULL) {
    optionfxsnapshot_ = new ::com::htsc::mdc::insight::model::OptionFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  return optionfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::OptionFxSnapshot* MDCfetsFxSnapshot::release_optionfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
  
  ::com::htsc::mdc::insight::model::OptionFxSnapshot* temp = optionfxsnapshot_;
  optionfxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_optionfxsnapshot(::com::htsc::mdc::insight::model::OptionFxSnapshot* optionfxsnapshot) {
  delete optionfxsnapshot_;
  optionfxsnapshot_ = optionfxsnapshot;
  if (optionfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.optionFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot spotClosePriceFxSnapshot = 14;
inline bool MDCfetsFxSnapshot::has_spotclosepricefxsnapshot() const {
  return this != internal_default_instance() && spotclosepricefxsnapshot_ != NULL;
}
inline void MDCfetsFxSnapshot::clear_spotclosepricefxsnapshot() {
  if (GetArenaNoVirtual() == NULL && spotclosepricefxsnapshot_ != NULL) delete spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot& MDCfetsFxSnapshot::spotclosepricefxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  return spotclosepricefxsnapshot_ != NULL ? *spotclosepricefxsnapshot_
                         : *::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* MDCfetsFxSnapshot::mutable_spotclosepricefxsnapshot() {
  
  if (spotclosepricefxsnapshot_ == NULL) {
    spotclosepricefxsnapshot_ = new ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  return spotclosepricefxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* MDCfetsFxSnapshot::release_spotclosepricefxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
  
  ::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* temp = spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsFxSnapshot::set_allocated_spotclosepricefxsnapshot(::com::htsc::mdc::insight::model::SpotClosePriceFxSnapshot* spotclosepricefxsnapshot) {
  delete spotclosepricefxsnapshot_;
  spotclosepricefxsnapshot_ = spotclosepricefxsnapshot;
  if (spotclosepricefxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.spotClosePriceFxSnapshot)
}

// optional int32 DataMultiplePowerOf10 = 15;
inline void MDCfetsFxSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsFxSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.DataMultiplePowerOf10)
}

// optional string TransactTime = 16;
inline void MDCfetsFxSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
inline void MDCfetsFxSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
inline void MDCfetsFxSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}
inline ::std::string* MDCfetsFxSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.TransactTime)
}

// optional string MarketIndicator = 20;
inline void MDCfetsFxSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
inline void MDCfetsFxSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
inline void MDCfetsFxSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}
inline ::std::string* MDCfetsFxSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxSnapshot.MarketIndicator)
}

inline const MDCfetsFxSnapshot* MDCfetsFxSnapshot::internal_default_instance() {
  return &MDCfetsFxSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// SwpSptNdfFowFxSnapshot

// optional string ValueDate = 1;
inline void SwpSptNdfFowFxSnapshot::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
inline void SwpSptNdfFowFxSnapshot::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
inline void SwpSptNdfFowFxSnapshot::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ValueDate)
}

// optional int64 NetBasisChange = 2;
inline void SwpSptNdfFowFxSnapshot::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.NetBasisChange)
  return netbasischange_;
}
inline void SwpSptNdfFowFxSnapshot::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.NetBasisChange)
}

// optional int64 PercentageChange = 3;
inline void SwpSptNdfFowFxSnapshot::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.PercentageChange)
  return percentagechange_;
}
inline void SwpSptNdfFowFxSnapshot::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.PercentageChange)
}

// optional string DateBuy = 4;
inline void SwpSptNdfFowFxSnapshot::clear_datebuy() {
  datebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::datebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  return datebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_datebuy(const ::std::string& value) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
inline void SwpSptNdfFowFxSnapshot::set_datebuy(const char* value) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
inline void SwpSptNdfFowFxSnapshot::set_datebuy(const char* value, size_t size) {
  
  datebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_datebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  return datebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_datebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
  
  return datebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_datebuy(::std::string* datebuy) {
  if (datebuy != NULL) {
    
  } else {
    
  }
  datebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateBuy)
}

// optional string TimeBuy = 5;
inline void SwpSptNdfFowFxSnapshot::clear_timebuy() {
  timebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::timebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  return timebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_timebuy(const ::std::string& value) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
inline void SwpSptNdfFowFxSnapshot::set_timebuy(const char* value) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
inline void SwpSptNdfFowFxSnapshot::set_timebuy(const char* value, size_t size) {
  
  timebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_timebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  return timebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_timebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
  
  return timebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_timebuy(::std::string* timebuy) {
  if (timebuy != NULL) {
    
  } else {
    
  }
  timebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), timebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeBuy)
}

// optional string DateSell = 6;
inline void SwpSptNdfFowFxSnapshot::clear_datesell() {
  datesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::datesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  return datesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_datesell(const ::std::string& value) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
inline void SwpSptNdfFowFxSnapshot::set_datesell(const char* value) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
inline void SwpSptNdfFowFxSnapshot::set_datesell(const char* value, size_t size) {
  
  datesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_datesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  return datesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_datesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
  
  return datesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_datesell(::std::string* datesell) {
  if (datesell != NULL) {
    
  } else {
    
  }
  datesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), datesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateSell)
}

// optional string TimeSell = 7;
inline void SwpSptNdfFowFxSnapshot::clear_timesell() {
  timesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::timesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  return timesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_timesell(const ::std::string& value) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
inline void SwpSptNdfFowFxSnapshot::set_timesell(const char* value) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
inline void SwpSptNdfFowFxSnapshot::set_timesell(const char* value, size_t size) {
  
  timesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_timesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  return timesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_timesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
  
  return timesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_timesell(::std::string* timesell) {
  if (timesell != NULL) {
    
  } else {
    
  }
  timesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), timesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.TimeSell)
}

// optional int64 LastRateBuy = 8;
inline void SwpSptNdfFowFxSnapshot::clear_lastratebuy() {
  lastratebuy_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateBuy)
  return lastratebuy_;
}
inline void SwpSptNdfFowFxSnapshot::set_lastratebuy(::google::protobuf::int64 value) {
  
  lastratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateBuy)
}

// optional int64 LastRateSell = 9;
inline void SwpSptNdfFowFxSnapshot::clear_lastratesell() {
  lastratesell_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateSell)
  return lastratesell_;
}
inline void SwpSptNdfFowFxSnapshot::set_lastratesell(::google::protobuf::int64 value) {
  
  lastratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastRateSell)
}

// optional int64 LastAllinBuy = 10;
inline void SwpSptNdfFowFxSnapshot::clear_lastallinbuy() {
  lastallinbuy_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastallinbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinBuy)
  return lastallinbuy_;
}
inline void SwpSptNdfFowFxSnapshot::set_lastallinbuy(::google::protobuf::int64 value) {
  
  lastallinbuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinBuy)
}

// optional int64 LastAllinSell = 11;
inline void SwpSptNdfFowFxSnapshot::clear_lastallinsell() {
  lastallinsell_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lastallinsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinSell)
  return lastallinsell_;
}
inline void SwpSptNdfFowFxSnapshot::set_lastallinsell(::google::protobuf::int64 value) {
  
  lastallinsell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LastAllinSell)
}

// optional int64 HighRate = 12;
inline void SwpSptNdfFowFxSnapshot::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HighRate)
  return highrate_;
}
inline void SwpSptNdfFowFxSnapshot::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HighRate)
}

// optional int64 LowRate = 13;
inline void SwpSptNdfFowFxSnapshot::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LowRate)
  return lowrate_;
}
inline void SwpSptNdfFowFxSnapshot::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LowRate)
}

// optional int64 OpenRate = 14;
inline void SwpSptNdfFowFxSnapshot::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.OpenRate)
  return openrate_;
}
inline void SwpSptNdfFowFxSnapshot::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
inline void SwpSptNdfFowFxSnapshot::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HistoryCloseRate)
  return historycloserate_;
}
inline void SwpSptNdfFowFxSnapshot::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
inline void SwpSptNdfFowFxSnapshot::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.CloseRate)
  return closerate_;
}
inline void SwpSptNdfFowFxSnapshot::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.CloseRate)
}

// optional int32 AmountLevelRate = 17;
inline void SwpSptNdfFowFxSnapshot::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
inline ::google::protobuf::int32 SwpSptNdfFowFxSnapshot::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelRate)
  return amountlevelrate_;
}
inline void SwpSptNdfFowFxSnapshot::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
inline void SwpSptNdfFowFxSnapshot::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
inline ::google::protobuf::int32 SwpSptNdfFowFxSnapshot::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelAllin)
  return amountlevelallin_;
}
inline void SwpSptNdfFowFxSnapshot::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AmountLevelAllin)
}

// optional int32 RateSide = 19;
inline void SwpSptNdfFowFxSnapshot::clear_rateside() {
  rateside_ = 0;
}
inline ::google::protobuf::int32 SwpSptNdfFowFxSnapshot::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.RateSide)
  return rateside_;
}
inline void SwpSptNdfFowFxSnapshot::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.RateSide)
}

// optional int32 AllinSide = 20;
inline void SwpSptNdfFowFxSnapshot::clear_allinside() {
  allinside_ = 0;
}
inline ::google::protobuf::int32 SwpSptNdfFowFxSnapshot::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AllinSide)
  return allinside_;
}
inline void SwpSptNdfFowFxSnapshot::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.AllinSide)
}

// optional string LegSign = 21;
inline void SwpSptNdfFowFxSnapshot::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
inline void SwpSptNdfFowFxSnapshot::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
inline void SwpSptNdfFowFxSnapshot::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.LegSign)
}

// optional string FillSide = 22;
inline void SwpSptNdfFowFxSnapshot::clear_fillside() {
  fillside_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::fillside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  return fillside_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_fillside(const ::std::string& value) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
inline void SwpSptNdfFowFxSnapshot::set_fillside(const char* value) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
inline void SwpSptNdfFowFxSnapshot::set_fillside(const char* value, size_t size) {
  
  fillside_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_fillside() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  return fillside_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_fillside() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
  
  return fillside_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_fillside(::std::string* fillside) {
  if (fillside != NULL) {
    
  } else {
    
  }
  fillside_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fillside);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.FillSide)
}

// optional string DateConfirmed = 23;
inline void SwpSptNdfFowFxSnapshot::clear_dateconfirmed() {
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::dateconfirmed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  return dateconfirmed_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const ::std::string& value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
inline void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const char* value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
inline void SwpSptNdfFowFxSnapshot::set_dateconfirmed(const char* value, size_t size) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_dateconfirmed() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  return dateconfirmed_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_dateconfirmed() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
  
  return dateconfirmed_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_dateconfirmed(::std::string* dateconfirmed) {
  if (dateconfirmed != NULL) {
    
  } else {
    
  }
  dateconfirmed_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dateconfirmed);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.DateConfirmed)
}

// optional bool ContingencyWithdraw = 24;
inline void SwpSptNdfFowFxSnapshot::clear_contingencywithdraw() {
  contingencywithdraw_ = false;
}
inline bool SwpSptNdfFowFxSnapshot::contingencywithdraw() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyWithdraw)
  return contingencywithdraw_;
}
inline void SwpSptNdfFowFxSnapshot::set_contingencywithdraw(bool value) {
  
  contingencywithdraw_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyWithdraw)
}

// optional string ContingencyTradeDate = 25;
inline void SwpSptNdfFowFxSnapshot::clear_contingencytradedate() {
  contingencytradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::contingencytradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  return contingencytradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const ::std::string& value) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const char* value) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradedate(const char* value, size_t size) {
  
  contingencytradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_contingencytradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  return contingencytradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_contingencytradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
  
  return contingencytradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_contingencytradedate(::std::string* contingencytradedate) {
  if (contingencytradedate != NULL) {
    
  } else {
    
  }
  contingencytradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), contingencytradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeDate)
}

// optional string ContingencyTradeTime = 26;
inline void SwpSptNdfFowFxSnapshot::clear_contingencytradetime() {
  contingencytradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxSnapshot::contingencytradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  return contingencytradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const ::std::string& value) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const char* value) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
inline void SwpSptNdfFowFxSnapshot::set_contingencytradetime(const char* value, size_t size) {
  
  contingencytradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}
inline ::std::string* SwpSptNdfFowFxSnapshot::mutable_contingencytradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  return contingencytradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxSnapshot::release_contingencytradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
  
  return contingencytradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxSnapshot::set_allocated_contingencytradetime(::std::string* contingencytradetime) {
  if (contingencytradetime != NULL) {
    
  } else {
    
  }
  contingencytradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), contingencytradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyTradeTime)
}

// optional int64 ContingencyLastPx = 27;
inline void SwpSptNdfFowFxSnapshot::clear_contingencylastpx() {
  contingencylastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxSnapshot::contingencylastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyLastPx)
  return contingencylastpx_;
}
inline void SwpSptNdfFowFxSnapshot::set_contingencylastpx(::google::protobuf::int64 value) {
  
  contingencylastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxSnapshot.ContingencyLastPx)
}

inline const SwpSptNdfFowFxSnapshot* SwpSptNdfFowFxSnapshot::internal_default_instance() {
  return &SwpSptNdfFowFxSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// OptionFxSnapshot

// optional string FxTerm = 1;
inline void OptionFxSnapshot::clear_fxterm() {
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::fxterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  return fxterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_fxterm(const ::std::string& value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
inline void OptionFxSnapshot::set_fxterm(const char* value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
inline void OptionFxSnapshot::set_fxterm(const char* value, size_t size) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}
inline ::std::string* OptionFxSnapshot::mutable_fxterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  return fxterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_fxterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
  
  return fxterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_fxterm(::std::string* fxterm) {
  if (fxterm != NULL) {
    
  } else {
    
  }
  fxterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fxterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.FxTerm)
}

// optional int64 Premium = 2;
inline void OptionFxSnapshot::clear_premium() {
  premium_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionFxSnapshot::premium() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Premium)
  return premium_;
}
inline void OptionFxSnapshot::set_premium(::google::protobuf::int64 value) {
  
  premium_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Premium)
}

// optional int64 Volatility = 3;
inline void OptionFxSnapshot::clear_volatility() {
  volatility_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionFxSnapshot::volatility() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Volatility)
  return volatility_;
}
inline void OptionFxSnapshot::set_volatility(::google::protobuf::int64 value) {
  
  volatility_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Volatility)
}

// optional int64 Volume = 4;
inline void OptionFxSnapshot::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionFxSnapshot::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.Volume)
  return volume_;
}
inline void OptionFxSnapshot::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.Volume)
}

// optional string TradeDate = 5;
inline void OptionFxSnapshot::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
inline void OptionFxSnapshot::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
inline void OptionFxSnapshot::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}
inline ::std::string* OptionFxSnapshot::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeDate)
}

// optional string TradeTime = 6;
inline void OptionFxSnapshot::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
inline void OptionFxSnapshot::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
inline void OptionFxSnapshot::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}
inline ::std::string* OptionFxSnapshot::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.TradeTime)
}

// optional int32 PremiumType = 7;
inline void OptionFxSnapshot::clear_premiumtype() {
  premiumtype_ = 0;
}
inline ::google::protobuf::int32 OptionFxSnapshot::premiumtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.PremiumType)
  return premiumtype_;
}
inline void OptionFxSnapshot::set_premiumtype(::google::protobuf::int32 value) {
  
  premiumtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.PremiumType)
}

// optional string OptionType = 8;
inline void OptionFxSnapshot::clear_optiontype() {
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::optiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  return optiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_optiontype(const ::std::string& value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
inline void OptionFxSnapshot::set_optiontype(const char* value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
inline void OptionFxSnapshot::set_optiontype(const char* value, size_t size) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}
inline ::std::string* OptionFxSnapshot::mutable_optiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  return optiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_optiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
  
  return optiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_optiontype(::std::string* optiontype) {
  if (optiontype != NULL) {
    
  } else {
    
  }
  optiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionType)
}

// optional string DateConfirmed = 9;
inline void OptionFxSnapshot::clear_dateconfirmed() {
  dateconfirmed_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::dateconfirmed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  return dateconfirmed_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_dateconfirmed(const ::std::string& value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
inline void OptionFxSnapshot::set_dateconfirmed(const char* value) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
inline void OptionFxSnapshot::set_dateconfirmed(const char* value, size_t size) {
  
  dateconfirmed_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}
inline ::std::string* OptionFxSnapshot::mutable_dateconfirmed() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  return dateconfirmed_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_dateconfirmed() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
  
  return dateconfirmed_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_dateconfirmed(::std::string* dateconfirmed) {
  if (dateconfirmed != NULL) {
    
  } else {
    
  }
  dateconfirmed_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dateconfirmed);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.DateConfirmed)
}

// optional int32 OptionTypeEnum = 10;
inline void OptionFxSnapshot::clear_optiontypeenum() {
  optiontypeenum_ = 0;
}
inline ::google::protobuf::int32 OptionFxSnapshot::optiontypeenum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionTypeEnum)
  return optiontypeenum_;
}
inline void OptionFxSnapshot::set_optiontypeenum(::google::protobuf::int32 value) {
  
  optiontypeenum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.OptionTypeEnum)
}

// optional string DerivativeExerciseStyle = 11;
inline void OptionFxSnapshot::clear_derivativeexercisestyle() {
  derivativeexercisestyle_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxSnapshot::derivativeexercisestyle() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  return derivativeexercisestyle_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_derivativeexercisestyle(const ::std::string& value) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
inline void OptionFxSnapshot::set_derivativeexercisestyle(const char* value) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
inline void OptionFxSnapshot::set_derivativeexercisestyle(const char* value, size_t size) {
  
  derivativeexercisestyle_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}
inline ::std::string* OptionFxSnapshot::mutable_derivativeexercisestyle() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  return derivativeexercisestyle_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxSnapshot::release_derivativeexercisestyle() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
  
  return derivativeexercisestyle_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxSnapshot::set_allocated_derivativeexercisestyle(::std::string* derivativeexercisestyle) {
  if (derivativeexercisestyle != NULL) {
    
  } else {
    
  }
  derivativeexercisestyle_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), derivativeexercisestyle);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxSnapshot.DerivativeExerciseStyle)
}

inline const OptionFxSnapshot* OptionFxSnapshot::internal_default_instance() {
  return &OptionFxSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// SpotClosePriceFxSnapshot

// optional int64 ClosePx = 1;
inline void SpotClosePriceFxSnapshot::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SpotClosePriceFxSnapshot::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.ClosePx)
  return closepx_;
}
inline void SpotClosePriceFxSnapshot::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.ClosePx)
}

// optional string UpdateDate = 2;
inline void SpotClosePriceFxSnapshot::clear_updatedate() {
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotClosePriceFxSnapshot::updatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  return updatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceFxSnapshot::set_updatedate(const ::std::string& value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
inline void SpotClosePriceFxSnapshot::set_updatedate(const char* value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
inline void SpotClosePriceFxSnapshot::set_updatedate(const char* value, size_t size) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}
inline ::std::string* SpotClosePriceFxSnapshot::mutable_updatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  return updatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotClosePriceFxSnapshot::release_updatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
  
  return updatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceFxSnapshot::set_allocated_updatedate(::std::string* updatedate) {
  if (updatedate != NULL) {
    
  } else {
    
  }
  updatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateDate)
}

// optional string UpdateTime = 3;
inline void SpotClosePriceFxSnapshot::clear_updatetime() {
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SpotClosePriceFxSnapshot::updatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  return updatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceFxSnapshot::set_updatetime(const ::std::string& value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
inline void SpotClosePriceFxSnapshot::set_updatetime(const char* value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
inline void SpotClosePriceFxSnapshot::set_updatetime(const char* value, size_t size) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}
inline ::std::string* SpotClosePriceFxSnapshot::mutable_updatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  return updatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SpotClosePriceFxSnapshot::release_updatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
  
  return updatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SpotClosePriceFxSnapshot::set_allocated_updatetime(::std::string* updatetime) {
  if (updatetime != NULL) {
    
  } else {
    
  }
  updatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceFxSnapshot.UpdateTime)
}

inline const SpotClosePriceFxSnapshot* SpotClosePriceFxSnapshot::internal_default_instance() {
  return &SpotClosePriceFxSnapshot_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsFxSnapshot_2eproto__INCLUDED
