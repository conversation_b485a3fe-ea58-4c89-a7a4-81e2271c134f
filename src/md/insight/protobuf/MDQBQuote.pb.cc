// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQBQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDQBQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDQBQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDQBQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDQBQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDQBQuote_2eproto() {
  protobuf_AddDesc_MDQBQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDQBQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDQBQuote_descriptor_ = file->message_type(0);
  static const int MDQBQuote_offsets_[39] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidnetprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidbargainflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidrelationflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidcomment_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidssdetect_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offerpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offersize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offeryield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offernetprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offerbargainflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offerrelationflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offercomment_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offerid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, offerssdetect_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, brokerdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidexerciseflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, ofrexerciseflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidfullprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, ofrfullprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidpricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, ofrpricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, bidsettltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, ofrsettltype_),
  };
  MDQBQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDQBQuote_descriptor_,
      MDQBQuote::internal_default_instance(),
      MDQBQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDQBQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQBQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDQBQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDQBQuote_descriptor_, MDQBQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDQBQuote_2eproto() {
  MDQBQuote_default_instance_.Shutdown();
  delete MDQBQuote_reflection_;
}

void protobuf_InitDefaults_MDQBQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDQBQuote_default_instance_.DefaultConstruct();
  MDQBQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDQBQuote_2eproto_once_);
void protobuf_InitDefaults_MDQBQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDQBQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDQBQuote_2eproto_impl);
}
void protobuf_AddDesc_MDQBQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDQBQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\017MDQBQuote.proto\022\032com.htsc.mdc.insight."
    "model\032\027ESecurityIDSource.proto\032\023ESecurit"
    "yType.proto\"\240\007\n\tMDQBQuote\022\026\n\016HTSCSecurit"
    "yID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001("
    "\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020TradingPhase"
    "Code\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.c"
    "om.htsc.mdc.model.ESecurityIDSource\0227\n\014s"
    "ecurityType\030\007 \001(\0162!.com.htsc.mdc.model.E"
    "SecurityType\022\024\n\014ExchangeDate\030\010 \001(\005\022\024\n\014Ex"
    "changeTime\030\t \001(\005\022\021\n\tChannelNo\030\n \001(\005\022\022\n\nA"
    "pplSeqNum\030\013 \001(\003\022\r\n\005BidPx\030\014 \001(\003\022\017\n\007BidSiz"
    "e\030\r \001(\003\022\020\n\010BidYield\030\016 \001(\003\022\023\n\013BidNetPrice"
    "\030\017 \001(\003\022\026\n\016BidBargainFlag\030\020 \001(\005\022\027\n\017BidRel"
    "ationFlag\030\021 \001(\005\022\022\n\nBidComment\030\022 \001(\t\022\r\n\005B"
    "idID\030\023 \001(\t\022\023\n\013BidSsDetect\030\024 \001(\005\022\017\n\007Offer"
    "Px\030\025 \001(\003\022\021\n\tOfferSize\030\026 \001(\003\022\022\n\nOfferYiel"
    "d\030\027 \001(\003\022\025\n\rOfferNetPrice\030\030 \001(\003\022\030\n\020OfferB"
    "argainFlag\030\031 \001(\005\022\031\n\021OfferRelationFlag\030\032 "
    "\001(\005\022\024\n\014OfferComment\030\033 \001(\t\022\017\n\007OfferID\030\034 \001"
    "(\t\022\025\n\rOfferSsDetect\030\035 \001(\005\022\026\n\016BrokerDataT"
    "ype\030\036 \001(\005\022\027\n\017BidExerciseFlag\030\037 \001(\005\022\027\n\017Of"
    "rExerciseFlag\030  \001(\005\022\035\n\025DataMultiplePower"
    "Of10\030! \001(\005\022\024\n\014BidFullPrice\030\" \001(\003\022\024\n\014OfrF"
    "ullPrice\030# \001(\003\022\024\n\014BidPriceType\030$ \001(\005\022\024\n\014"
    "OfrPriceType\030% \001(\005\022\024\n\014BidSettlType\030& \001(\t"
    "\022\024\n\014OfrSettlType\030\' \001(\tB2\n\032com.htsc.mdc.i"
    "nsight.modelB\017MDQBQuoteProtosH\001\240\001\001b\006prot"
    "o3", 1082);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDQBQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDQBQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDQBQuote_2eproto_once_);
void protobuf_AddDesc_MDQBQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDQBQuote_2eproto_once_,
                 &protobuf_AddDesc_MDQBQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDQBQuote_2eproto {
  StaticDescriptorInitializer_MDQBQuote_2eproto() {
    protobuf_AddDesc_MDQBQuote_2eproto();
  }
} static_descriptor_initializer_MDQBQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDQBQuote::kHTSCSecurityIDFieldNumber;
const int MDQBQuote::kMDDateFieldNumber;
const int MDQBQuote::kMDTimeFieldNumber;
const int MDQBQuote::kDataTimestampFieldNumber;
const int MDQBQuote::kTradingPhaseCodeFieldNumber;
const int MDQBQuote::kSecurityIDSourceFieldNumber;
const int MDQBQuote::kSecurityTypeFieldNumber;
const int MDQBQuote::kExchangeDateFieldNumber;
const int MDQBQuote::kExchangeTimeFieldNumber;
const int MDQBQuote::kChannelNoFieldNumber;
const int MDQBQuote::kApplSeqNumFieldNumber;
const int MDQBQuote::kBidPxFieldNumber;
const int MDQBQuote::kBidSizeFieldNumber;
const int MDQBQuote::kBidYieldFieldNumber;
const int MDQBQuote::kBidNetPriceFieldNumber;
const int MDQBQuote::kBidBargainFlagFieldNumber;
const int MDQBQuote::kBidRelationFlagFieldNumber;
const int MDQBQuote::kBidCommentFieldNumber;
const int MDQBQuote::kBidIDFieldNumber;
const int MDQBQuote::kBidSsDetectFieldNumber;
const int MDQBQuote::kOfferPxFieldNumber;
const int MDQBQuote::kOfferSizeFieldNumber;
const int MDQBQuote::kOfferYieldFieldNumber;
const int MDQBQuote::kOfferNetPriceFieldNumber;
const int MDQBQuote::kOfferBargainFlagFieldNumber;
const int MDQBQuote::kOfferRelationFlagFieldNumber;
const int MDQBQuote::kOfferCommentFieldNumber;
const int MDQBQuote::kOfferIDFieldNumber;
const int MDQBQuote::kOfferSsDetectFieldNumber;
const int MDQBQuote::kBrokerDataTypeFieldNumber;
const int MDQBQuote::kBidExerciseFlagFieldNumber;
const int MDQBQuote::kOfrExerciseFlagFieldNumber;
const int MDQBQuote::kDataMultiplePowerOf10FieldNumber;
const int MDQBQuote::kBidFullPriceFieldNumber;
const int MDQBQuote::kOfrFullPriceFieldNumber;
const int MDQBQuote::kBidPriceTypeFieldNumber;
const int MDQBQuote::kOfrPriceTypeFieldNumber;
const int MDQBQuote::kBidSettlTypeFieldNumber;
const int MDQBQuote::kOfrSettlTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDQBQuote::MDQBQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQBQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDQBQuote)
}

void MDQBQuote::InitAsDefaultInstance() {
}

MDQBQuote::MDQBQuote(const MDQBQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDQBQuote)
}

void MDQBQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidcomment_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offercomment_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offerid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidsettltype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ofrsettltype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&ofrpricetype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(ofrpricetype_));
  _cached_size_ = 0;
}

MDQBQuote::~MDQBQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDQBQuote)
  SharedDtor();
}

void MDQBQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidcomment_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offercomment_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offerid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidsettltype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ofrsettltype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDQBQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDQBQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDQBQuote_descriptor_;
}

const MDQBQuote& MDQBQuote::default_instance() {
  protobuf_InitDefaults_MDQBQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDQBQuote> MDQBQuote_default_instance_;

MDQBQuote* MDQBQuote::New(::google::protobuf::Arena* arena) const {
  MDQBQuote* n = new MDQBQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDQBQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDQBQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDQBQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDQBQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangedate_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(exchangetime_, bidnetprice_);
  ZR_(bidrelationflag_, offernetprice_);
  bidcomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bidid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(offerbargainflag_, ofrexerciseflag_);
  offercomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  offerid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(bidfullprice_, ofrpricetype_);
  bidsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ofrsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDQBQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDQBQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 9;
      case 9: {
        if (tag == 72) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 10;
      case 10: {
        if (tag == 80) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 11;
      case 11: {
        if (tag == 88) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_BidPx;
        break;
      }

      // optional int64 BidPx = 12;
      case 12: {
        if (tag == 96) {
         parse_BidPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_BidSize;
        break;
      }

      // optional int64 BidSize = 13;
      case 13: {
        if (tag == 104) {
         parse_BidSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_BidYield;
        break;
      }

      // optional int64 BidYield = 14;
      case 14: {
        if (tag == 112) {
         parse_BidYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_BidNetPrice;
        break;
      }

      // optional int64 BidNetPrice = 15;
      case 15: {
        if (tag == 120) {
         parse_BidNetPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidnetprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_BidBargainFlag;
        break;
      }

      // optional int32 BidBargainFlag = 16;
      case 16: {
        if (tag == 128) {
         parse_BidBargainFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidbargainflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_BidRelationFlag;
        break;
      }

      // optional int32 BidRelationFlag = 17;
      case 17: {
        if (tag == 136) {
         parse_BidRelationFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidrelationflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_BidComment;
        break;
      }

      // optional string BidComment = 18;
      case 18: {
        if (tag == 146) {
         parse_BidComment:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bidcomment()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bidcomment().data(), this->bidcomment().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.BidComment"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_BidID;
        break;
      }

      // optional string BidID = 19;
      case 19: {
        if (tag == 154) {
         parse_BidID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bidid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bidid().data(), this->bidid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.BidID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_BidSsDetect;
        break;
      }

      // optional int32 BidSsDetect = 20;
      case 20: {
        if (tag == 160) {
         parse_BidSsDetect:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidssdetect_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_OfferPx;
        break;
      }

      // optional int64 OfferPx = 21;
      case 21: {
        if (tag == 168) {
         parse_OfferPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &offerpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_OfferSize;
        break;
      }

      // optional int64 OfferSize = 22;
      case 22: {
        if (tag == 176) {
         parse_OfferSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &offersize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_OfferYield;
        break;
      }

      // optional int64 OfferYield = 23;
      case 23: {
        if (tag == 184) {
         parse_OfferYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &offeryield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_OfferNetPrice;
        break;
      }

      // optional int64 OfferNetPrice = 24;
      case 24: {
        if (tag == 192) {
         parse_OfferNetPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &offernetprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_OfferBargainFlag;
        break;
      }

      // optional int32 OfferBargainFlag = 25;
      case 25: {
        if (tag == 200) {
         parse_OfferBargainFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &offerbargainflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_OfferRelationFlag;
        break;
      }

      // optional int32 OfferRelationFlag = 26;
      case 26: {
        if (tag == 208) {
         parse_OfferRelationFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &offerrelationflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_OfferComment;
        break;
      }

      // optional string OfferComment = 27;
      case 27: {
        if (tag == 218) {
         parse_OfferComment:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_offercomment()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->offercomment().data(), this->offercomment().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.OfferComment"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_OfferID;
        break;
      }

      // optional string OfferID = 28;
      case 28: {
        if (tag == 226) {
         parse_OfferID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_offerid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->offerid().data(), this->offerid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.OfferID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_OfferSsDetect;
        break;
      }

      // optional int32 OfferSsDetect = 29;
      case 29: {
        if (tag == 232) {
         parse_OfferSsDetect:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &offerssdetect_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_BrokerDataType;
        break;
      }

      // optional int32 BrokerDataType = 30;
      case 30: {
        if (tag == 240) {
         parse_BrokerDataType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &brokerdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_BidExerciseFlag;
        break;
      }

      // optional int32 BidExerciseFlag = 31;
      case 31: {
        if (tag == 248) {
         parse_BidExerciseFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidexerciseflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_OfrExerciseFlag;
        break;
      }

      // optional int32 OfrExerciseFlag = 32;
      case 32: {
        if (tag == 256) {
         parse_OfrExerciseFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ofrexerciseflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 33;
      case 33: {
        if (tag == 264) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_BidFullPrice;
        break;
      }

      // optional int64 BidFullPrice = 34;
      case 34: {
        if (tag == 272) {
         parse_BidFullPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bidfullprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_OfrFullPrice;
        break;
      }

      // optional int64 OfrFullPrice = 35;
      case 35: {
        if (tag == 280) {
         parse_OfrFullPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &ofrfullprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_BidPriceType;
        break;
      }

      // optional int32 BidPriceType = 36;
      case 36: {
        if (tag == 288) {
         parse_BidPriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidpricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_OfrPriceType;
        break;
      }

      // optional int32 OfrPriceType = 37;
      case 37: {
        if (tag == 296) {
         parse_OfrPriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ofrpricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_BidSettlType;
        break;
      }

      // optional string BidSettlType = 38;
      case 38: {
        if (tag == 306) {
         parse_BidSettlType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bidsettltype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bidsettltype().data(), this->bidsettltype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.BidSettlType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(314)) goto parse_OfrSettlType;
        break;
      }

      // optional string OfrSettlType = 39;
      case 39: {
        if (tag == 314) {
         parse_OfrSettlType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ofrsettltype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->ofrsettltype().data(), this->ofrsettltype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDQBQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDQBQuote)
  return false;
#undef DO_
}

void MDQBQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDQBQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->channelno(), output);
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->applseqnum(), output);
  }

  // optional int64 BidPx = 12;
  if (this->bidpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->bidpx(), output);
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->bidsize(), output);
  }

  // optional int64 BidYield = 14;
  if (this->bidyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->bidyield(), output);
  }

  // optional int64 BidNetPrice = 15;
  if (this->bidnetprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->bidnetprice(), output);
  }

  // optional int32 BidBargainFlag = 16;
  if (this->bidbargainflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->bidbargainflag(), output);
  }

  // optional int32 BidRelationFlag = 17;
  if (this->bidrelationflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->bidrelationflag(), output);
  }

  // optional string BidComment = 18;
  if (this->bidcomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidcomment().data(), this->bidcomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidComment");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->bidcomment(), output);
  }

  // optional string BidID = 19;
  if (this->bidid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidid().data(), this->bidid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->bidid(), output);
  }

  // optional int32 BidSsDetect = 20;
  if (this->bidssdetect() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->bidssdetect(), output);
  }

  // optional int64 OfferPx = 21;
  if (this->offerpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->offerpx(), output);
  }

  // optional int64 OfferSize = 22;
  if (this->offersize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->offersize(), output);
  }

  // optional int64 OfferYield = 23;
  if (this->offeryield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->offeryield(), output);
  }

  // optional int64 OfferNetPrice = 24;
  if (this->offernetprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->offernetprice(), output);
  }

  // optional int32 OfferBargainFlag = 25;
  if (this->offerbargainflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->offerbargainflag(), output);
  }

  // optional int32 OfferRelationFlag = 26;
  if (this->offerrelationflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->offerrelationflag(), output);
  }

  // optional string OfferComment = 27;
  if (this->offercomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->offercomment().data(), this->offercomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfferComment");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      27, this->offercomment(), output);
  }

  // optional string OfferID = 28;
  if (this->offerid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->offerid().data(), this->offerid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfferID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      28, this->offerid(), output);
  }

  // optional int32 OfferSsDetect = 29;
  if (this->offerssdetect() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(29, this->offerssdetect(), output);
  }

  // optional int32 BrokerDataType = 30;
  if (this->brokerdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->brokerdatatype(), output);
  }

  // optional int32 BidExerciseFlag = 31;
  if (this->bidexerciseflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(31, this->bidexerciseflag(), output);
  }

  // optional int32 OfrExerciseFlag = 32;
  if (this->ofrexerciseflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(32, this->ofrexerciseflag(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 33;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(33, this->datamultiplepowerof10(), output);
  }

  // optional int64 BidFullPrice = 34;
  if (this->bidfullprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->bidfullprice(), output);
  }

  // optional int64 OfrFullPrice = 35;
  if (this->ofrfullprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->ofrfullprice(), output);
  }

  // optional int32 BidPriceType = 36;
  if (this->bidpricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(36, this->bidpricetype(), output);
  }

  // optional int32 OfrPriceType = 37;
  if (this->ofrpricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(37, this->ofrpricetype(), output);
  }

  // optional string BidSettlType = 38;
  if (this->bidsettltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidsettltype().data(), this->bidsettltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidSettlType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      38, this->bidsettltype(), output);
  }

  // optional string OfrSettlType = 39;
  if (this->ofrsettltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ofrsettltype().data(), this->ofrsettltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      39, this->ofrsettltype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDQBQuote)
}

::google::protobuf::uint8* MDQBQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDQBQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->channelno(), target);
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->applseqnum(), target);
  }

  // optional int64 BidPx = 12;
  if (this->bidpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->bidpx(), target);
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->bidsize(), target);
  }

  // optional int64 BidYield = 14;
  if (this->bidyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->bidyield(), target);
  }

  // optional int64 BidNetPrice = 15;
  if (this->bidnetprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->bidnetprice(), target);
  }

  // optional int32 BidBargainFlag = 16;
  if (this->bidbargainflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->bidbargainflag(), target);
  }

  // optional int32 BidRelationFlag = 17;
  if (this->bidrelationflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->bidrelationflag(), target);
  }

  // optional string BidComment = 18;
  if (this->bidcomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidcomment().data(), this->bidcomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidComment");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->bidcomment(), target);
  }

  // optional string BidID = 19;
  if (this->bidid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidid().data(), this->bidid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->bidid(), target);
  }

  // optional int32 BidSsDetect = 20;
  if (this->bidssdetect() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->bidssdetect(), target);
  }

  // optional int64 OfferPx = 21;
  if (this->offerpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->offerpx(), target);
  }

  // optional int64 OfferSize = 22;
  if (this->offersize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->offersize(), target);
  }

  // optional int64 OfferYield = 23;
  if (this->offeryield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->offeryield(), target);
  }

  // optional int64 OfferNetPrice = 24;
  if (this->offernetprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->offernetprice(), target);
  }

  // optional int32 OfferBargainFlag = 25;
  if (this->offerbargainflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->offerbargainflag(), target);
  }

  // optional int32 OfferRelationFlag = 26;
  if (this->offerrelationflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->offerrelationflag(), target);
  }

  // optional string OfferComment = 27;
  if (this->offercomment().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->offercomment().data(), this->offercomment().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfferComment");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        27, this->offercomment(), target);
  }

  // optional string OfferID = 28;
  if (this->offerid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->offerid().data(), this->offerid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfferID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        28, this->offerid(), target);
  }

  // optional int32 OfferSsDetect = 29;
  if (this->offerssdetect() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(29, this->offerssdetect(), target);
  }

  // optional int32 BrokerDataType = 30;
  if (this->brokerdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->brokerdatatype(), target);
  }

  // optional int32 BidExerciseFlag = 31;
  if (this->bidexerciseflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(31, this->bidexerciseflag(), target);
  }

  // optional int32 OfrExerciseFlag = 32;
  if (this->ofrexerciseflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(32, this->ofrexerciseflag(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 33;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(33, this->datamultiplepowerof10(), target);
  }

  // optional int64 BidFullPrice = 34;
  if (this->bidfullprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->bidfullprice(), target);
  }

  // optional int64 OfrFullPrice = 35;
  if (this->ofrfullprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->ofrfullprice(), target);
  }

  // optional int32 BidPriceType = 36;
  if (this->bidpricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(36, this->bidpricetype(), target);
  }

  // optional int32 OfrPriceType = 37;
  if (this->ofrpricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(37, this->ofrpricetype(), target);
  }

  // optional string BidSettlType = 38;
  if (this->bidsettltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bidsettltype().data(), this->bidsettltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.BidSettlType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        38, this->bidsettltype(), target);
  }

  // optional string OfrSettlType = 39;
  if (this->ofrsettltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ofrsettltype().data(), this->ofrsettltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        39, this->ofrsettltype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDQBQuote)
  return target;
}

size_t MDQBQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDQBQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 ApplSeqNum = 11;
  if (this->applseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional int64 BidPx = 12;
  if (this->bidpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidpx());
  }

  // optional int64 BidSize = 13;
  if (this->bidsize() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidsize());
  }

  // optional int64 BidYield = 14;
  if (this->bidyield() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidyield());
  }

  // optional int64 BidNetPrice = 15;
  if (this->bidnetprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidnetprice());
  }

  // optional int32 BidBargainFlag = 16;
  if (this->bidbargainflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidbargainflag());
  }

  // optional int32 BidRelationFlag = 17;
  if (this->bidrelationflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidrelationflag());
  }

  // optional string BidComment = 18;
  if (this->bidcomment().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bidcomment());
  }

  // optional string BidID = 19;
  if (this->bidid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bidid());
  }

  // optional int32 BidSsDetect = 20;
  if (this->bidssdetect() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidssdetect());
  }

  // optional int64 OfferPx = 21;
  if (this->offerpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->offerpx());
  }

  // optional int64 OfferSize = 22;
  if (this->offersize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->offersize());
  }

  // optional int64 OfferYield = 23;
  if (this->offeryield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->offeryield());
  }

  // optional int64 OfferNetPrice = 24;
  if (this->offernetprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->offernetprice());
  }

  // optional int32 OfferBargainFlag = 25;
  if (this->offerbargainflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->offerbargainflag());
  }

  // optional int32 OfferRelationFlag = 26;
  if (this->offerrelationflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->offerrelationflag());
  }

  // optional string OfferComment = 27;
  if (this->offercomment().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->offercomment());
  }

  // optional string OfferID = 28;
  if (this->offerid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->offerid());
  }

  // optional int32 OfferSsDetect = 29;
  if (this->offerssdetect() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->offerssdetect());
  }

  // optional int32 BrokerDataType = 30;
  if (this->brokerdatatype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->brokerdatatype());
  }

  // optional int32 BidExerciseFlag = 31;
  if (this->bidexerciseflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidexerciseflag());
  }

  // optional int32 OfrExerciseFlag = 32;
  if (this->ofrexerciseflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ofrexerciseflag());
  }

  // optional int32 DataMultiplePowerOf10 = 33;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 BidFullPrice = 34;
  if (this->bidfullprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bidfullprice());
  }

  // optional int64 OfrFullPrice = 35;
  if (this->ofrfullprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->ofrfullprice());
  }

  // optional int32 BidPriceType = 36;
  if (this->bidpricetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidpricetype());
  }

  // optional int32 OfrPriceType = 37;
  if (this->ofrpricetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ofrpricetype());
  }

  // optional string BidSettlType = 38;
  if (this->bidsettltype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bidsettltype());
  }

  // optional string OfrSettlType = 39;
  if (this->ofrsettltype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->ofrsettltype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDQBQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDQBQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDQBQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDQBQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDQBQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDQBQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDQBQuote::MergeFrom(const MDQBQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDQBQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDQBQuote::UnsafeMergeFrom(const MDQBQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.bidpx() != 0) {
    set_bidpx(from.bidpx());
  }
  if (from.bidsize() != 0) {
    set_bidsize(from.bidsize());
  }
  if (from.bidyield() != 0) {
    set_bidyield(from.bidyield());
  }
  if (from.bidnetprice() != 0) {
    set_bidnetprice(from.bidnetprice());
  }
  if (from.bidbargainflag() != 0) {
    set_bidbargainflag(from.bidbargainflag());
  }
  if (from.bidrelationflag() != 0) {
    set_bidrelationflag(from.bidrelationflag());
  }
  if (from.bidcomment().size() > 0) {

    bidcomment_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bidcomment_);
  }
  if (from.bidid().size() > 0) {

    bidid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bidid_);
  }
  if (from.bidssdetect() != 0) {
    set_bidssdetect(from.bidssdetect());
  }
  if (from.offerpx() != 0) {
    set_offerpx(from.offerpx());
  }
  if (from.offersize() != 0) {
    set_offersize(from.offersize());
  }
  if (from.offeryield() != 0) {
    set_offeryield(from.offeryield());
  }
  if (from.offernetprice() != 0) {
    set_offernetprice(from.offernetprice());
  }
  if (from.offerbargainflag() != 0) {
    set_offerbargainflag(from.offerbargainflag());
  }
  if (from.offerrelationflag() != 0) {
    set_offerrelationflag(from.offerrelationflag());
  }
  if (from.offercomment().size() > 0) {

    offercomment_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.offercomment_);
  }
  if (from.offerid().size() > 0) {

    offerid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.offerid_);
  }
  if (from.offerssdetect() != 0) {
    set_offerssdetect(from.offerssdetect());
  }
  if (from.brokerdatatype() != 0) {
    set_brokerdatatype(from.brokerdatatype());
  }
  if (from.bidexerciseflag() != 0) {
    set_bidexerciseflag(from.bidexerciseflag());
  }
  if (from.ofrexerciseflag() != 0) {
    set_ofrexerciseflag(from.ofrexerciseflag());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.bidfullprice() != 0) {
    set_bidfullprice(from.bidfullprice());
  }
  if (from.ofrfullprice() != 0) {
    set_ofrfullprice(from.ofrfullprice());
  }
  if (from.bidpricetype() != 0) {
    set_bidpricetype(from.bidpricetype());
  }
  if (from.ofrpricetype() != 0) {
    set_ofrpricetype(from.ofrpricetype());
  }
  if (from.bidsettltype().size() > 0) {

    bidsettltype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bidsettltype_);
  }
  if (from.ofrsettltype().size() > 0) {

    ofrsettltype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ofrsettltype_);
  }
}

void MDQBQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDQBQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDQBQuote::CopyFrom(const MDQBQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDQBQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDQBQuote::IsInitialized() const {

  return true;
}

void MDQBQuote::Swap(MDQBQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDQBQuote::InternalSwap(MDQBQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  std::swap(applseqnum_, other->applseqnum_);
  std::swap(bidpx_, other->bidpx_);
  std::swap(bidsize_, other->bidsize_);
  std::swap(bidyield_, other->bidyield_);
  std::swap(bidnetprice_, other->bidnetprice_);
  std::swap(bidbargainflag_, other->bidbargainflag_);
  std::swap(bidrelationflag_, other->bidrelationflag_);
  bidcomment_.Swap(&other->bidcomment_);
  bidid_.Swap(&other->bidid_);
  std::swap(bidssdetect_, other->bidssdetect_);
  std::swap(offerpx_, other->offerpx_);
  std::swap(offersize_, other->offersize_);
  std::swap(offeryield_, other->offeryield_);
  std::swap(offernetprice_, other->offernetprice_);
  std::swap(offerbargainflag_, other->offerbargainflag_);
  std::swap(offerrelationflag_, other->offerrelationflag_);
  offercomment_.Swap(&other->offercomment_);
  offerid_.Swap(&other->offerid_);
  std::swap(offerssdetect_, other->offerssdetect_);
  std::swap(brokerdatatype_, other->brokerdatatype_);
  std::swap(bidexerciseflag_, other->bidexerciseflag_);
  std::swap(ofrexerciseflag_, other->ofrexerciseflag_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(bidfullprice_, other->bidfullprice_);
  std::swap(ofrfullprice_, other->ofrfullprice_);
  std::swap(bidpricetype_, other->bidpricetype_);
  std::swap(ofrpricetype_, other->ofrpricetype_);
  bidsettltype_.Swap(&other->bidsettltype_);
  ofrsettltype_.Swap(&other->ofrsettltype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDQBQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDQBQuote_descriptor_;
  metadata.reflection = MDQBQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQBQuote

// optional string HTSCSecurityID = 1;
void MDQBQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
void MDQBQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
void MDQBQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}
::std::string* MDQBQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDQBQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDQBQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.MDDate)
  return mddate_;
}
void MDQBQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDQBQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDQBQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.MDTime)
  return mdtime_;
}
void MDQBQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDQBQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.DataTimestamp)
  return datatimestamp_;
}
void MDQBQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDQBQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
void MDQBQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
void MDQBQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}
::std::string* MDQBQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDQBQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDQBQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDQBQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDQBQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDQBQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDQBQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.securityType)
}

// optional int32 ExchangeDate = 8;
void MDQBQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDQBQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ExchangeDate)
  return exchangedate_;
}
void MDQBQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
void MDQBQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDQBQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ExchangeTime)
  return exchangetime_;
}
void MDQBQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ExchangeTime)
}

// optional int32 ChannelNo = 10;
void MDQBQuote::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDQBQuote::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ChannelNo)
  return channelno_;
}
void MDQBQuote::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ChannelNo)
}

// optional int64 ApplSeqNum = 11;
void MDQBQuote::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.ApplSeqNum)
  return applseqnum_;
}
void MDQBQuote::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.ApplSeqNum)
}

// optional int64 BidPx = 12;
void MDQBQuote::clear_bidpx() {
  bidpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::bidpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidPx)
  return bidpx_;
}
void MDQBQuote::set_bidpx(::google::protobuf::int64 value) {
  
  bidpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidPx)
}

// optional int64 BidSize = 13;
void MDQBQuote::clear_bidsize() {
  bidsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::bidsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSize)
  return bidsize_;
}
void MDQBQuote::set_bidsize(::google::protobuf::int64 value) {
  
  bidsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSize)
}

// optional int64 BidYield = 14;
void MDQBQuote::clear_bidyield() {
  bidyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::bidyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidYield)
  return bidyield_;
}
void MDQBQuote::set_bidyield(::google::protobuf::int64 value) {
  
  bidyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidYield)
}

// optional int64 BidNetPrice = 15;
void MDQBQuote::clear_bidnetprice() {
  bidnetprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::bidnetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidNetPrice)
  return bidnetprice_;
}
void MDQBQuote::set_bidnetprice(::google::protobuf::int64 value) {
  
  bidnetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidNetPrice)
}

// optional int32 BidBargainFlag = 16;
void MDQBQuote::clear_bidbargainflag() {
  bidbargainflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::bidbargainflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidBargainFlag)
  return bidbargainflag_;
}
void MDQBQuote::set_bidbargainflag(::google::protobuf::int32 value) {
  
  bidbargainflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidBargainFlag)
}

// optional int32 BidRelationFlag = 17;
void MDQBQuote::clear_bidrelationflag() {
  bidrelationflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::bidrelationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidRelationFlag)
  return bidrelationflag_;
}
void MDQBQuote::set_bidrelationflag(::google::protobuf::int32 value) {
  
  bidrelationflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidRelationFlag)
}

// optional string BidComment = 18;
void MDQBQuote::clear_bidcomment() {
  bidcomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::bidcomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  return bidcomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_bidcomment(const ::std::string& value) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
void MDQBQuote::set_bidcomment(const char* value) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
void MDQBQuote::set_bidcomment(const char* value, size_t size) {
  
  bidcomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}
::std::string* MDQBQuote::mutable_bidcomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  return bidcomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_bidcomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
  
  return bidcomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_bidcomment(::std::string* bidcomment) {
  if (bidcomment != NULL) {
    
  } else {
    
  }
  bidcomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidcomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidComment)
}

// optional string BidID = 19;
void MDQBQuote::clear_bidid() {
  bidid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::bidid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  return bidid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_bidid(const ::std::string& value) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
void MDQBQuote::set_bidid(const char* value) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
void MDQBQuote::set_bidid(const char* value, size_t size) {
  
  bidid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}
::std::string* MDQBQuote::mutable_bidid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  return bidid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_bidid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidID)
  
  return bidid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_bidid(::std::string* bidid) {
  if (bidid != NULL) {
    
  } else {
    
  }
  bidid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidID)
}

// optional int32 BidSsDetect = 20;
void MDQBQuote::clear_bidssdetect() {
  bidssdetect_ = 0;
}
::google::protobuf::int32 MDQBQuote::bidssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSsDetect)
  return bidssdetect_;
}
void MDQBQuote::set_bidssdetect(::google::protobuf::int32 value) {
  
  bidssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSsDetect)
}

// optional int64 OfferPx = 21;
void MDQBQuote::clear_offerpx() {
  offerpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::offerpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferPx)
  return offerpx_;
}
void MDQBQuote::set_offerpx(::google::protobuf::int64 value) {
  
  offerpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferPx)
}

// optional int64 OfferSize = 22;
void MDQBQuote::clear_offersize() {
  offersize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::offersize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferSize)
  return offersize_;
}
void MDQBQuote::set_offersize(::google::protobuf::int64 value) {
  
  offersize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferSize)
}

// optional int64 OfferYield = 23;
void MDQBQuote::clear_offeryield() {
  offeryield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::offeryield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferYield)
  return offeryield_;
}
void MDQBQuote::set_offeryield(::google::protobuf::int64 value) {
  
  offeryield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferYield)
}

// optional int64 OfferNetPrice = 24;
void MDQBQuote::clear_offernetprice() {
  offernetprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::offernetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferNetPrice)
  return offernetprice_;
}
void MDQBQuote::set_offernetprice(::google::protobuf::int64 value) {
  
  offernetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferNetPrice)
}

// optional int32 OfferBargainFlag = 25;
void MDQBQuote::clear_offerbargainflag() {
  offerbargainflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::offerbargainflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferBargainFlag)
  return offerbargainflag_;
}
void MDQBQuote::set_offerbargainflag(::google::protobuf::int32 value) {
  
  offerbargainflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferBargainFlag)
}

// optional int32 OfferRelationFlag = 26;
void MDQBQuote::clear_offerrelationflag() {
  offerrelationflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::offerrelationflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferRelationFlag)
  return offerrelationflag_;
}
void MDQBQuote::set_offerrelationflag(::google::protobuf::int32 value) {
  
  offerrelationflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferRelationFlag)
}

// optional string OfferComment = 27;
void MDQBQuote::clear_offercomment() {
  offercomment_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::offercomment() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  return offercomment_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_offercomment(const ::std::string& value) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
void MDQBQuote::set_offercomment(const char* value) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
void MDQBQuote::set_offercomment(const char* value, size_t size) {
  
  offercomment_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}
::std::string* MDQBQuote::mutable_offercomment() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  return offercomment_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_offercomment() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
  
  return offercomment_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_offercomment(::std::string* offercomment) {
  if (offercomment != NULL) {
    
  } else {
    
  }
  offercomment_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offercomment);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfferComment)
}

// optional string OfferID = 28;
void MDQBQuote::clear_offerid() {
  offerid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::offerid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  return offerid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_offerid(const ::std::string& value) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
void MDQBQuote::set_offerid(const char* value) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
void MDQBQuote::set_offerid(const char* value, size_t size) {
  
  offerid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}
::std::string* MDQBQuote::mutable_offerid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  return offerid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_offerid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
  
  return offerid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_offerid(::std::string* offerid) {
  if (offerid != NULL) {
    
  } else {
    
  }
  offerid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), offerid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfferID)
}

// optional int32 OfferSsDetect = 29;
void MDQBQuote::clear_offerssdetect() {
  offerssdetect_ = 0;
}
::google::protobuf::int32 MDQBQuote::offerssdetect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfferSsDetect)
  return offerssdetect_;
}
void MDQBQuote::set_offerssdetect(::google::protobuf::int32 value) {
  
  offerssdetect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfferSsDetect)
}

// optional int32 BrokerDataType = 30;
void MDQBQuote::clear_brokerdatatype() {
  brokerdatatype_ = 0;
}
::google::protobuf::int32 MDQBQuote::brokerdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BrokerDataType)
  return brokerdatatype_;
}
void MDQBQuote::set_brokerdatatype(::google::protobuf::int32 value) {
  
  brokerdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BrokerDataType)
}

// optional int32 BidExerciseFlag = 31;
void MDQBQuote::clear_bidexerciseflag() {
  bidexerciseflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::bidexerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidExerciseFlag)
  return bidexerciseflag_;
}
void MDQBQuote::set_bidexerciseflag(::google::protobuf::int32 value) {
  
  bidexerciseflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidExerciseFlag)
}

// optional int32 OfrExerciseFlag = 32;
void MDQBQuote::clear_ofrexerciseflag() {
  ofrexerciseflag_ = 0;
}
::google::protobuf::int32 MDQBQuote::ofrexerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrExerciseFlag)
  return ofrexerciseflag_;
}
void MDQBQuote::set_ofrexerciseflag(::google::protobuf::int32 value) {
  
  ofrexerciseflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrExerciseFlag)
}

// optional int32 DataMultiplePowerOf10 = 33;
void MDQBQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDQBQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDQBQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.DataMultiplePowerOf10)
}

// optional int64 BidFullPrice = 34;
void MDQBQuote::clear_bidfullprice() {
  bidfullprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::bidfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidFullPrice)
  return bidfullprice_;
}
void MDQBQuote::set_bidfullprice(::google::protobuf::int64 value) {
  
  bidfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidFullPrice)
}

// optional int64 OfrFullPrice = 35;
void MDQBQuote::clear_ofrfullprice() {
  ofrfullprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQBQuote::ofrfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrFullPrice)
  return ofrfullprice_;
}
void MDQBQuote::set_ofrfullprice(::google::protobuf::int64 value) {
  
  ofrfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrFullPrice)
}

// optional int32 BidPriceType = 36;
void MDQBQuote::clear_bidpricetype() {
  bidpricetype_ = 0;
}
::google::protobuf::int32 MDQBQuote::bidpricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidPriceType)
  return bidpricetype_;
}
void MDQBQuote::set_bidpricetype(::google::protobuf::int32 value) {
  
  bidpricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidPriceType)
}

// optional int32 OfrPriceType = 37;
void MDQBQuote::clear_ofrpricetype() {
  ofrpricetype_ = 0;
}
::google::protobuf::int32 MDQBQuote::ofrpricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrPriceType)
  return ofrpricetype_;
}
void MDQBQuote::set_ofrpricetype(::google::protobuf::int32 value) {
  
  ofrpricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrPriceType)
}

// optional string BidSettlType = 38;
void MDQBQuote::clear_bidsettltype() {
  bidsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::bidsettltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  return bidsettltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_bidsettltype(const ::std::string& value) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
void MDQBQuote::set_bidsettltype(const char* value) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
void MDQBQuote::set_bidsettltype(const char* value, size_t size) {
  
  bidsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}
::std::string* MDQBQuote::mutable_bidsettltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  return bidsettltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_bidsettltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
  
  return bidsettltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_bidsettltype(::std::string* bidsettltype) {
  if (bidsettltype != NULL) {
    
  } else {
    
  }
  bidsettltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bidsettltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.BidSettlType)
}

// optional string OfrSettlType = 39;
void MDQBQuote::clear_ofrsettltype() {
  ofrsettltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQBQuote::ofrsettltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  return ofrsettltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_ofrsettltype(const ::std::string& value) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
void MDQBQuote::set_ofrsettltype(const char* value) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
void MDQBQuote::set_ofrsettltype(const char* value, size_t size) {
  
  ofrsettltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}
::std::string* MDQBQuote::mutable_ofrsettltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  return ofrsettltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQBQuote::release_ofrsettltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
  
  return ofrsettltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQBQuote::set_allocated_ofrsettltype(::std::string* ofrsettltype) {
  if (ofrsettltype != NULL) {
    
  } else {
    
  }
  ofrsettltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ofrsettltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQBQuote.OfrSettlType)
}

inline const MDQBQuote* MDQBQuote::internal_default_instance() {
  return &MDQBQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
