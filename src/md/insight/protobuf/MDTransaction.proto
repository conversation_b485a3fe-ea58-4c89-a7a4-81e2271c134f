syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDTransaction {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  int64 TradeIndex = 7;
  int64 TradeBuyNo = 8;
  int64 TradeSellNo = 9;
  int32 TradeType = 10;
  int32 TradeBSFlag = 11;
  int64 TradePrice = 12;
  int64 TradeQty = 13;
  int64 TradeMoney = 14;
  int64 ApplSeqNum = 15;
  int32 ChannelNo = 16;
  int32 ExchangeDate = 17;
  int32 ExchangeTime = 18;
  int64 TradeCleanPrice = 19;
  int64 AccruedInterestAmt = 20;
  int64 TradeDirtyPrice = 21;
  int64 MaturityYield = 22;
  string FITradingMethod = 23;
  int64 AccruedInterestOtd = 24;
  int64 Duration = 25;
  int64 ModifiedDuration = 26;
  int64 Convexity = 27;
  int32 SettlPeriod = 28;
  int32 SettlType = 29;
  int32 HKTradeType = 30;
  int32 DataMultiplePowerOf10 = 31;
  string SecondaryOrderID = 32;
  int32 BidExecInstType = 33;
  int64 MarginPrice = 34;
  string DealDate = 35;
  string DealTime = 36;
  string DealNumber = 37;
  int32 MarketIndicator = 38;
  int32 RepoTerm = 39;
  int64 LegSettlementAmount1st = 40;
  int64 LegSettlementAmount2nd = 41;
  string BondCode = 42;
  string BondName = 43;
  int64 TotalFacevalue = 44;
  int64 LegCleanPrice1st = 45;
  int64 LegCleanPrice2nd = 46;
  int64 LegYield1st = 47;
  int64 LegYield2nd = 48;
}
