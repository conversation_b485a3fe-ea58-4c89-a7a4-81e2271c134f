// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQuery.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDQuery.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDQueryRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDQueryRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* QueryParam_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  QueryParam_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDQueryResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDQueryResponse_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDQuery_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDQuery_2eproto() {
  protobuf_AddDesc_MDQuery_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDQuery.proto");
  GOOGLE_CHECK(file != NULL);
  MDQueryRequest_descriptor_ = file->message_type(0);
  static const int MDQueryRequest_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryRequest, querytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryRequest, securitysourcetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryRequest, htscsecurityids_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryRequest, queryparams_),
  };
  MDQueryRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDQueryRequest_descriptor_,
      MDQueryRequest::internal_default_instance(),
      MDQueryRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDQueryRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryRequest, _internal_metadata_));
  QueryParam_descriptor_ = file->message_type(1);
  static const int QueryParam_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(QueryParam, paramtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(QueryParam, paramvalue_),
  };
  QueryParam_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      QueryParam_descriptor_,
      QueryParam::internal_default_instance(),
      QueryParam_offsets_,
      -1,
      -1,
      -1,
      sizeof(QueryParam),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(QueryParam, _internal_metadata_));
  MDQueryResponse_descriptor_ = file->message_type(2);
  static const int MDQueryResponse_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryResponse, querytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryResponse, issuccess_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryResponse, errorcontext_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryResponse, marketdatastream_),
  };
  MDQueryResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDQueryResponse_descriptor_,
      MDQueryResponse::internal_default_instance(),
      MDQueryResponse_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDQueryResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQueryResponse, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDQuery_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDQueryRequest_descriptor_, MDQueryRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      QueryParam_descriptor_, QueryParam::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDQueryResponse_descriptor_, MDQueryResponse::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDQuery_2eproto() {
  MDQueryRequest_default_instance_.Shutdown();
  delete MDQueryRequest_reflection_;
  QueryParam_default_instance_.Shutdown();
  delete QueryParam_reflection_;
  MDQueryResponse_default_instance_.Shutdown();
  delete MDQueryResponse_reflection_;
}

void protobuf_InitDefaults_MDQuery_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_SecuritySourceType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MarketData_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDQueryRequest_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  QueryParam_default_instance_.DefaultConstruct();
  MDQueryResponse_default_instance_.DefaultConstruct();
  MDQueryRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  QueryParam_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDQueryResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDQuery_2eproto_once_);
void protobuf_InitDefaults_MDQuery_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDQuery_2eproto_once_,
                 &protobuf_InitDefaults_MDQuery_2eproto_impl);
}
void protobuf_AddDesc_MDQuery_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDQuery_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rMDQuery.proto\022\032com.htsc.mdc.insight.mo"
    "del\032\030SecuritySourceType.proto\032\031InsightEr"
    "rorContext.proto\032\020MarketData.proto\"\305\001\n\016M"
    "DQueryRequest\022\021\n\tqueryType\030\001 \001(\005\022J\n\022secu"
    "ritySourceType\030\002 \003(\0132..com.htsc.mdc.insi"
    "ght.model.SecuritySourceType\022\027\n\017htscSecu"
    "rityIDs\030\003 \003(\t\022;\n\013queryParams\030\004 \003(\0132&.com"
    ".htsc.mdc.insight.model.QueryParam\"3\n\nQu"
    "eryParam\022\021\n\tparamType\030\001 \001(\t\022\022\n\nparamValu"
    "e\030\002 \001(\t\"\306\001\n\017MDQueryResponse\022\021\n\tqueryType"
    "\030\001 \001(\005\022\021\n\tisSuccess\030\002 \001(\010\022E\n\014errorContex"
    "t\030\003 \001(\0132/.com.htsc.mdc.insight.model.Ins"
    "ightErrorContext\022F\n\020marketDataStream\030\004 \001"
    "(\0132,.com.htsc.mdc.insight.model.MarketDa"
    "taStreamB/\n\032com.htsc.mdc.insight.modelB\014"
    "MDQueryProtoH\001\240\001\001b\006proto3", 625);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDQuery.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_SecuritySourceType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_InsightErrorContext_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MarketData_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDQuery_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDQuery_2eproto_once_);
void protobuf_AddDesc_MDQuery_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDQuery_2eproto_once_,
                 &protobuf_AddDesc_MDQuery_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDQuery_2eproto {
  StaticDescriptorInitializer_MDQuery_2eproto() {
    protobuf_AddDesc_MDQuery_2eproto();
  }
} static_descriptor_initializer_MDQuery_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDQueryRequest::kQueryTypeFieldNumber;
const int MDQueryRequest::kSecuritySourceTypeFieldNumber;
const int MDQueryRequest::kHtscSecurityIDsFieldNumber;
const int MDQueryRequest::kQueryParamsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDQueryRequest::MDQueryRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDQueryRequest)
}

void MDQueryRequest::InitAsDefaultInstance() {
}

MDQueryRequest::MDQueryRequest(const MDQueryRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDQueryRequest)
}

void MDQueryRequest::SharedCtor() {
  querytype_ = 0;
  _cached_size_ = 0;
}

MDQueryRequest::~MDQueryRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDQueryRequest)
  SharedDtor();
}

void MDQueryRequest::SharedDtor() {
}

void MDQueryRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDQueryRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDQueryRequest_descriptor_;
}

const MDQueryRequest& MDQueryRequest::default_instance() {
  protobuf_InitDefaults_MDQuery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDQueryRequest> MDQueryRequest_default_instance_;

MDQueryRequest* MDQueryRequest::New(::google::protobuf::Arena* arena) const {
  MDQueryRequest* n = new MDQueryRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDQueryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDQueryRequest)
  querytype_ = 0;
  securitysourcetype_.Clear();
  htscsecurityids_.Clear();
  queryparams_.Clear();
}

bool MDQueryRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDQueryRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 queryType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &querytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_securitySourceType;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
      case 2: {
        if (tag == 18) {
         parse_securitySourceType:
          DO_(input->IncrementRecursionDepth());
         parse_loop_securitySourceType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_securitysourcetype()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_loop_securitySourceType;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(26)) goto parse_htscSecurityIDs;
        break;
      }

      // repeated string htscSecurityIDs = 3;
      case 3: {
        if (tag == 26) {
         parse_htscSecurityIDs:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_htscsecurityids()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityids(this->htscsecurityids_size() - 1).data(),
            this->htscsecurityids(this->htscsecurityids_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_htscSecurityIDs;
        if (input->ExpectTag(34)) goto parse_queryParams;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
      case 4: {
        if (tag == 34) {
         parse_queryParams:
          DO_(input->IncrementRecursionDepth());
         parse_loop_queryParams:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_queryparams()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_loop_queryParams;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDQueryRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDQueryRequest)
  return false;
#undef DO_
}

void MDQueryRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDQueryRequest)
  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->querytype(), output);
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
  for (unsigned int i = 0, n = this->securitysourcetype_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->securitysourcetype(i), output);
  }

  // repeated string htscSecurityIDs = 3;
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityids(i).data(), this->htscsecurityids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      3, this->htscsecurityids(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
  for (unsigned int i = 0, n = this->queryparams_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->queryparams(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDQueryRequest)
}

::google::protobuf::uint8* MDQueryRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDQueryRequest)
  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->querytype(), target);
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
  for (unsigned int i = 0, n = this->securitysourcetype_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, this->securitysourcetype(i), false, target);
  }

  // repeated string htscSecurityIDs = 3;
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityids(i).data(), this->htscsecurityids(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(3, this->htscsecurityids(i), target);
  }

  // repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
  for (unsigned int i = 0, n = this->queryparams_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, this->queryparams(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDQueryRequest)
  return target;
}

size_t MDQueryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDQueryRequest)
  size_t total_size = 0;

  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->querytype());
  }

  // repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
  {
    unsigned int count = this->securitysourcetype_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->securitysourcetype(i));
    }
  }

  // repeated string htscSecurityIDs = 3;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->htscsecurityids_size());
  for (int i = 0; i < this->htscsecurityids_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->htscsecurityids(i));
  }

  // repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
  {
    unsigned int count = this->queryparams_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->queryparams(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDQueryRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDQueryRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDQueryRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDQueryRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDQueryRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDQueryRequest)
    UnsafeMergeFrom(*source);
  }
}

void MDQueryRequest::MergeFrom(const MDQueryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDQueryRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDQueryRequest::UnsafeMergeFrom(const MDQueryRequest& from) {
  GOOGLE_DCHECK(&from != this);
  securitysourcetype_.MergeFrom(from.securitysourcetype_);
  htscsecurityids_.UnsafeMergeFrom(from.htscsecurityids_);
  queryparams_.MergeFrom(from.queryparams_);
  if (from.querytype() != 0) {
    set_querytype(from.querytype());
  }
}

void MDQueryRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDQueryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDQueryRequest::CopyFrom(const MDQueryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDQueryRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDQueryRequest::IsInitialized() const {

  return true;
}

void MDQueryRequest::Swap(MDQueryRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDQueryRequest::InternalSwap(MDQueryRequest* other) {
  std::swap(querytype_, other->querytype_);
  securitysourcetype_.UnsafeArenaSwap(&other->securitysourcetype_);
  htscsecurityids_.UnsafeArenaSwap(&other->htscsecurityids_);
  queryparams_.UnsafeArenaSwap(&other->queryparams_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDQueryRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDQueryRequest_descriptor_;
  metadata.reflection = MDQueryRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQueryRequest

// optional int32 queryType = 1;
void MDQueryRequest::clear_querytype() {
  querytype_ = 0;
}
::google::protobuf::int32 MDQueryRequest::querytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.queryType)
  return querytype_;
}
void MDQueryRequest::set_querytype(::google::protobuf::int32 value) {
  
  querytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryRequest.queryType)
}

// repeated .com.htsc.mdc.insight.model.SecuritySourceType securitySourceType = 2;
int MDQueryRequest::securitysourcetype_size() const {
  return securitysourcetype_.size();
}
void MDQueryRequest::clear_securitysourcetype() {
  securitysourcetype_.Clear();
}
const ::com::htsc::mdc::insight::model::SecuritySourceType& MDQueryRequest::securitysourcetype(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Get(index);
}
::com::htsc::mdc::insight::model::SecuritySourceType* MDQueryRequest::mutable_securitysourcetype(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Mutable(index);
}
::com::htsc::mdc::insight::model::SecuritySourceType* MDQueryRequest::add_securitysourcetype() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >*
MDQueryRequest::mutable_securitysourcetype() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return &securitysourcetype_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SecuritySourceType >&
MDQueryRequest::securitysourcetype() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.securitySourceType)
  return securitysourcetype_;
}

// repeated string htscSecurityIDs = 3;
int MDQueryRequest::htscsecurityids_size() const {
  return htscsecurityids_.size();
}
void MDQueryRequest::clear_htscsecurityids() {
  htscsecurityids_.Clear();
}
const ::std::string& MDQueryRequest::htscsecurityids(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Get(index);
}
::std::string* MDQueryRequest::mutable_htscsecurityids(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Mutable(index);
}
void MDQueryRequest::set_htscsecurityids(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  htscsecurityids_.Mutable(index)->assign(value);
}
void MDQueryRequest::set_htscsecurityids(int index, const char* value) {
  htscsecurityids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
void MDQueryRequest::set_htscsecurityids(int index, const char* value, size_t size) {
  htscsecurityids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
::std::string* MDQueryRequest::add_htscsecurityids() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_.Add();
}
void MDQueryRequest::add_htscsecurityids(const ::std::string& value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
void MDQueryRequest::add_htscsecurityids(const char* value) {
  htscsecurityids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
void MDQueryRequest::add_htscsecurityids(const char* value, size_t size) {
  htscsecurityids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
MDQueryRequest::htscsecurityids() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return htscsecurityids_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
MDQueryRequest::mutable_htscsecurityids() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.htscSecurityIDs)
  return &htscsecurityids_;
}

// repeated .com.htsc.mdc.insight.model.QueryParam queryParams = 4;
int MDQueryRequest::queryparams_size() const {
  return queryparams_.size();
}
void MDQueryRequest::clear_queryparams() {
  queryparams_.Clear();
}
const ::com::htsc::mdc::insight::model::QueryParam& MDQueryRequest::queryparams(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Get(index);
}
::com::htsc::mdc::insight::model::QueryParam* MDQueryRequest::mutable_queryparams(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Mutable(index);
}
::com::htsc::mdc::insight::model::QueryParam* MDQueryRequest::add_queryparams() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >*
MDQueryRequest::mutable_queryparams() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return &queryparams_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::QueryParam >&
MDQueryRequest::queryparams() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQueryRequest.queryParams)
  return queryparams_;
}

inline const MDQueryRequest* MDQueryRequest::internal_default_instance() {
  return &MDQueryRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int QueryParam::kParamTypeFieldNumber;
const int QueryParam::kParamValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

QueryParam::QueryParam()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.QueryParam)
}

void QueryParam::InitAsDefaultInstance() {
}

QueryParam::QueryParam(const QueryParam& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.QueryParam)
}

void QueryParam::SharedCtor() {
  paramtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

QueryParam::~QueryParam() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.QueryParam)
  SharedDtor();
}

void QueryParam::SharedDtor() {
  paramtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void QueryParam::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* QueryParam::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return QueryParam_descriptor_;
}

const QueryParam& QueryParam::default_instance() {
  protobuf_InitDefaults_MDQuery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<QueryParam> QueryParam_default_instance_;

QueryParam* QueryParam::New(::google::protobuf::Arena* arena) const {
  QueryParam* n = new QueryParam;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void QueryParam::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.QueryParam)
  paramtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool QueryParam::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.QueryParam)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string paramType = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_paramtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->paramtype().data(), this->paramtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.QueryParam.paramType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_paramValue;
        break;
      }

      // optional string paramValue = 2;
      case 2: {
        if (tag == 18) {
         parse_paramValue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_paramvalue()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->paramvalue().data(), this->paramvalue().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.QueryParam.paramValue"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.QueryParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.QueryParam)
  return false;
#undef DO_
}

void QueryParam::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.QueryParam)
  // optional string paramType = 1;
  if (this->paramtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramtype().data(), this->paramtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.QueryParam.paramType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->paramtype(), output);
  }

  // optional string paramValue = 2;
  if (this->paramvalue().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramvalue().data(), this->paramvalue().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.QueryParam.paramValue");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->paramvalue(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.QueryParam)
}

::google::protobuf::uint8* QueryParam::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.QueryParam)
  // optional string paramType = 1;
  if (this->paramtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramtype().data(), this->paramtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.QueryParam.paramType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->paramtype(), target);
  }

  // optional string paramValue = 2;
  if (this->paramvalue().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramvalue().data(), this->paramvalue().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.QueryParam.paramValue");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->paramvalue(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.QueryParam)
  return target;
}

size_t QueryParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.QueryParam)
  size_t total_size = 0;

  // optional string paramType = 1;
  if (this->paramtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->paramtype());
  }

  // optional string paramValue = 2;
  if (this->paramvalue().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->paramvalue());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void QueryParam::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.QueryParam)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const QueryParam* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const QueryParam>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.QueryParam)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.QueryParam)
    UnsafeMergeFrom(*source);
  }
}

void QueryParam::MergeFrom(const QueryParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.QueryParam)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void QueryParam::UnsafeMergeFrom(const QueryParam& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.paramtype().size() > 0) {

    paramtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.paramtype_);
  }
  if (from.paramvalue().size() > 0) {

    paramvalue_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.paramvalue_);
  }
}

void QueryParam::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.QueryParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void QueryParam::CopyFrom(const QueryParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.QueryParam)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool QueryParam::IsInitialized() const {

  return true;
}

void QueryParam::Swap(QueryParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void QueryParam::InternalSwap(QueryParam* other) {
  paramtype_.Swap(&other->paramtype_);
  paramvalue_.Swap(&other->paramvalue_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata QueryParam::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = QueryParam_descriptor_;
  metadata.reflection = QueryParam_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// QueryParam

// optional string paramType = 1;
void QueryParam::clear_paramtype() {
  paramtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& QueryParam::paramtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.QueryParam.paramType)
  return paramtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void QueryParam::set_paramtype(const ::std::string& value) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.QueryParam.paramType)
}
void QueryParam::set_paramtype(const char* value) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.QueryParam.paramType)
}
void QueryParam::set_paramtype(const char* value, size_t size) {
  
  paramtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.QueryParam.paramType)
}
::std::string* QueryParam::mutable_paramtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.QueryParam.paramType)
  return paramtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* QueryParam::release_paramtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.QueryParam.paramType)
  
  return paramtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void QueryParam::set_allocated_paramtype(::std::string* paramtype) {
  if (paramtype != NULL) {
    
  } else {
    
  }
  paramtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.QueryParam.paramType)
}

// optional string paramValue = 2;
void QueryParam::clear_paramvalue() {
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& QueryParam::paramvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.QueryParam.paramValue)
  return paramvalue_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void QueryParam::set_paramvalue(const ::std::string& value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
void QueryParam::set_paramvalue(const char* value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
void QueryParam::set_paramvalue(const char* value, size_t size) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.QueryParam.paramValue)
}
::std::string* QueryParam::mutable_paramvalue() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.QueryParam.paramValue)
  return paramvalue_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* QueryParam::release_paramvalue() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.QueryParam.paramValue)
  
  return paramvalue_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void QueryParam::set_allocated_paramvalue(::std::string* paramvalue) {
  if (paramvalue != NULL) {
    
  } else {
    
  }
  paramvalue_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramvalue);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.QueryParam.paramValue)
}

inline const QueryParam* QueryParam::internal_default_instance() {
  return &QueryParam_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDQueryResponse::kQueryTypeFieldNumber;
const int MDQueryResponse::kIsSuccessFieldNumber;
const int MDQueryResponse::kErrorContextFieldNumber;
const int MDQueryResponse::kMarketDataStreamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDQueryResponse::MDQueryResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDQueryResponse)
}

void MDQueryResponse::InitAsDefaultInstance() {
  errorcontext_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
  marketdatastream_ = const_cast< ::com::htsc::mdc::insight::model::MarketDataStream*>(
      ::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance());
}

MDQueryResponse::MDQueryResponse(const MDQueryResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDQueryResponse)
}

void MDQueryResponse::SharedCtor() {
  errorcontext_ = NULL;
  marketdatastream_ = NULL;
  ::memset(&querytype_, 0, reinterpret_cast<char*>(&issuccess_) -
    reinterpret_cast<char*>(&querytype_) + sizeof(issuccess_));
  _cached_size_ = 0;
}

MDQueryResponse::~MDQueryResponse() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDQueryResponse)
  SharedDtor();
}

void MDQueryResponse::SharedDtor() {
  if (this != &MDQueryResponse_default_instance_.get()) {
    delete errorcontext_;
    delete marketdatastream_;
  }
}

void MDQueryResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDQueryResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDQueryResponse_descriptor_;
}

const MDQueryResponse& MDQueryResponse::default_instance() {
  protobuf_InitDefaults_MDQuery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDQueryResponse> MDQueryResponse_default_instance_;

MDQueryResponse* MDQueryResponse::New(::google::protobuf::Arena* arena) const {
  MDQueryResponse* n = new MDQueryResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDQueryResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDQueryResponse)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDQueryResponse, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDQueryResponse*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(querytype_, issuccess_);
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDQueryResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDQueryResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 queryType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &querytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_isSuccess;
        break;
      }

      // optional bool isSuccess = 2;
      case 2: {
        if (tag == 16) {
         parse_isSuccess:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issuccess_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_errorContext;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
      case 3: {
        if (tag == 26) {
         parse_errorContext:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_errorcontext()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_marketDataStream;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
      case 4: {
        if (tag == 34) {
         parse_marketDataStream:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_marketdatastream()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDQueryResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDQueryResponse)
  return false;
#undef DO_
}

void MDQueryResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDQueryResponse)
  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->querytype(), output);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(2, this->issuccess(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->errorcontext_, output);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
  if (this->has_marketdatastream()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->marketdatastream_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDQueryResponse)
}

::google::protobuf::uint8* MDQueryResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDQueryResponse)
  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->querytype(), target);
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(2, this->issuccess(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->errorcontext_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
  if (this->has_marketdatastream()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->marketdatastream_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDQueryResponse)
  return target;
}

size_t MDQueryResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDQueryResponse)
  size_t total_size = 0;

  // optional int32 queryType = 1;
  if (this->querytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->querytype());
  }

  // optional bool isSuccess = 2;
  if (this->issuccess() != 0) {
    total_size += 1 + 1;
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
  if (this->has_errorcontext()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->errorcontext_);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
  if (this->has_marketdatastream()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->marketdatastream_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDQueryResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDQueryResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDQueryResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDQueryResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDQueryResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDQueryResponse)
    UnsafeMergeFrom(*source);
  }
}

void MDQueryResponse::MergeFrom(const MDQueryResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDQueryResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDQueryResponse::UnsafeMergeFrom(const MDQueryResponse& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.querytype() != 0) {
    set_querytype(from.querytype());
  }
  if (from.issuccess() != 0) {
    set_issuccess(from.issuccess());
  }
  if (from.has_errorcontext()) {
    mutable_errorcontext()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.errorcontext());
  }
  if (from.has_marketdatastream()) {
    mutable_marketdatastream()->::com::htsc::mdc::insight::model::MarketDataStream::MergeFrom(from.marketdatastream());
  }
}

void MDQueryResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDQueryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDQueryResponse::CopyFrom(const MDQueryResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDQueryResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDQueryResponse::IsInitialized() const {

  return true;
}

void MDQueryResponse::Swap(MDQueryResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDQueryResponse::InternalSwap(MDQueryResponse* other) {
  std::swap(querytype_, other->querytype_);
  std::swap(issuccess_, other->issuccess_);
  std::swap(errorcontext_, other->errorcontext_);
  std::swap(marketdatastream_, other->marketdatastream_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDQueryResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDQueryResponse_descriptor_;
  metadata.reflection = MDQueryResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQueryResponse

// optional int32 queryType = 1;
void MDQueryResponse::clear_querytype() {
  querytype_ = 0;
}
::google::protobuf::int32 MDQueryResponse::querytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.queryType)
  return querytype_;
}
void MDQueryResponse::set_querytype(::google::protobuf::int32 value) {
  
  querytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryResponse.queryType)
}

// optional bool isSuccess = 2;
void MDQueryResponse::clear_issuccess() {
  issuccess_ = false;
}
bool MDQueryResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.isSuccess)
  return issuccess_;
}
void MDQueryResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQueryResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 3;
bool MDQueryResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
void MDQueryResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& MDQueryResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* MDQueryResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  return errorcontext_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* MDQueryResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
void MDQueryResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQueryResponse.errorContext)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream marketDataStream = 4;
bool MDQueryResponse::has_marketdatastream() const {
  return this != internal_default_instance() && marketdatastream_ != NULL;
}
void MDQueryResponse::clear_marketdatastream() {
  if (GetArenaNoVirtual() == NULL && marketdatastream_ != NULL) delete marketdatastream_;
  marketdatastream_ = NULL;
}
const ::com::htsc::mdc::insight::model::MarketDataStream& MDQueryResponse::marketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  return marketdatastream_ != NULL ? *marketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
::com::htsc::mdc::insight::model::MarketDataStream* MDQueryResponse::mutable_marketdatastream() {
  
  if (marketdatastream_ == NULL) {
    marketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  return marketdatastream_;
}
::com::htsc::mdc::insight::model::MarketDataStream* MDQueryResponse::release_marketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = marketdatastream_;
  marketdatastream_ = NULL;
  return temp;
}
void MDQueryResponse::set_allocated_marketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* marketdatastream) {
  delete marketdatastream_;
  marketdatastream_ = marketdatastream;
  if (marketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQueryResponse.marketDataStream)
}

inline const MDQueryResponse* MDQueryResponse::internal_default_instance() {
  return &MDQueryResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
