// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxQuote.proto

#ifndef PROTOBUF_MDCfetsFxQuote_2eproto__INCLUDED
#define PROTOBUF_MDCfetsFxQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsFxQuote_2eproto();
void protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
void protobuf_AssignDesc_MDCfetsFxQuote_2eproto();
void protobuf_ShutdownFile_MDCfetsFxQuote_2eproto();

class MDCfetsFxQuote;
class OptionFxQuote;
class SwpSptNdfFowFxQuote;

// ===================================================================

class MDCfetsFxQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsFxQuote) */ {
 public:
  MDCfetsFxQuote();
  virtual ~MDCfetsFxQuote();

  MDCfetsFxQuote(const MDCfetsFxQuote& from);

  inline MDCfetsFxQuote& operator=(const MDCfetsFxQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsFxQuote& default_instance();

  static const MDCfetsFxQuote* internal_default_instance();

  void Swap(MDCfetsFxQuote* other);

  // implements Message ----------------------------------------------

  inline MDCfetsFxQuote* New() const { return New(NULL); }

  MDCfetsFxQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsFxQuote& from);
  void MergeFrom(const MDCfetsFxQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsFxQuote* other);
  void UnsafeMergeFrom(const MDCfetsFxQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional string SecuritySubType = 7;
  void clear_securitysubtype();
  static const int kSecuritySubTypeFieldNumber = 7;
  const ::std::string& securitysubtype() const;
  void set_securitysubtype(const ::std::string& value);
  void set_securitysubtype(const char* value);
  void set_securitysubtype(const char* value, size_t size);
  ::std::string* mutable_securitysubtype();
  ::std::string* release_securitysubtype();
  void set_allocated_securitysubtype(::std::string* securitysubtype);

  // optional int32 ForexQuoteType = 8;
  void clear_forexquotetype();
  static const int kForexQuoteTypeFieldNumber = 8;
  ::google::protobuf::int32 forexquotetype() const;
  void set_forexquotetype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
  bool has_spotfxquote() const;
  void clear_spotfxquote();
  static const int kSpotFxQuoteFieldNumber = 9;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& spotfxquote() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* mutable_spotfxquote();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* release_spotfxquote();
  void set_allocated_spotfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* spotfxquote);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
  bool has_forwardfxquote() const;
  void clear_forwardfxquote();
  static const int kForwardFxQuoteFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& forwardfxquote() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* mutable_forwardfxquote();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* release_forwardfxquote();
  void set_allocated_forwardfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* forwardfxquote);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
  bool has_nondeliverableforwardsfxquote() const;
  void clear_nondeliverableforwardsfxquote();
  static const int kNonDeliverableForwardsFxQuoteFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& nondeliverableforwardsfxquote() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* mutable_nondeliverableforwardsfxquote();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* release_nondeliverableforwardsfxquote();
  void set_allocated_nondeliverableforwardsfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* nondeliverableforwardsfxquote);

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
  bool has_swapfxquote() const;
  void clear_swapfxquote();
  static const int kSwapFxQuoteFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& swapfxquote() const;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* mutable_swapfxquote();
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* release_swapfxquote();
  void set_allocated_swapfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* swapfxquote);

  // optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
  bool has_optionfxquote() const;
  void clear_optionfxquote();
  static const int kOptionFxQuoteFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::OptionFxQuote& optionfxquote() const;
  ::com::htsc::mdc::insight::model::OptionFxQuote* mutable_optionfxquote();
  ::com::htsc::mdc::insight::model::OptionFxQuote* release_optionfxquote();
  void set_allocated_optionfxquote(::com::htsc::mdc::insight::model::OptionFxQuote* optionfxquote);

  // optional int32 DataMultiplePowerOf10 = 14;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 14;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string TransactTime = 15;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 15;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 16;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 16;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsFxQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securitysubtype_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* spotfxquote_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* forwardfxquote_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* nondeliverableforwardsfxquote_;
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* swapfxquote_;
  ::com::htsc::mdc::insight::model::OptionFxQuote* optionfxquote_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int32 forexquotetype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxQuote> MDCfetsFxQuote_default_instance_;

// -------------------------------------------------------------------

class SwpSptNdfFowFxQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote) */ {
 public:
  SwpSptNdfFowFxQuote();
  virtual ~SwpSptNdfFowFxQuote();

  SwpSptNdfFowFxQuote(const SwpSptNdfFowFxQuote& from);

  inline SwpSptNdfFowFxQuote& operator=(const SwpSptNdfFowFxQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const SwpSptNdfFowFxQuote& default_instance();

  static const SwpSptNdfFowFxQuote* internal_default_instance();

  void Swap(SwpSptNdfFowFxQuote* other);

  // implements Message ----------------------------------------------

  inline SwpSptNdfFowFxQuote* New() const { return New(NULL); }

  SwpSptNdfFowFxQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const SwpSptNdfFowFxQuote& from);
  void MergeFrom(const SwpSptNdfFowFxQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SwpSptNdfFowFxQuote* other);
  void UnsafeMergeFrom(const SwpSptNdfFowFxQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string BestRateDateBuy = 1;
  void clear_bestratedatebuy();
  static const int kBestRateDateBuyFieldNumber = 1;
  const ::std::string& bestratedatebuy() const;
  void set_bestratedatebuy(const ::std::string& value);
  void set_bestratedatebuy(const char* value);
  void set_bestratedatebuy(const char* value, size_t size);
  ::std::string* mutable_bestratedatebuy();
  ::std::string* release_bestratedatebuy();
  void set_allocated_bestratedatebuy(::std::string* bestratedatebuy);

  // optional string BestRateTimeBuy = 2;
  void clear_bestratetimebuy();
  static const int kBestRateTimeBuyFieldNumber = 2;
  const ::std::string& bestratetimebuy() const;
  void set_bestratetimebuy(const ::std::string& value);
  void set_bestratetimebuy(const char* value);
  void set_bestratetimebuy(const char* value, size_t size);
  ::std::string* mutable_bestratetimebuy();
  ::std::string* release_bestratetimebuy();
  void set_allocated_bestratetimebuy(::std::string* bestratetimebuy);

  // optional string BestRateDateSell = 3;
  void clear_bestratedatesell();
  static const int kBestRateDateSellFieldNumber = 3;
  const ::std::string& bestratedatesell() const;
  void set_bestratedatesell(const ::std::string& value);
  void set_bestratedatesell(const char* value);
  void set_bestratedatesell(const char* value, size_t size);
  ::std::string* mutable_bestratedatesell();
  ::std::string* release_bestratedatesell();
  void set_allocated_bestratedatesell(::std::string* bestratedatesell);

  // optional string BestRateTimeSell = 4;
  void clear_bestratetimesell();
  static const int kBestRateTimeSellFieldNumber = 4;
  const ::std::string& bestratetimesell() const;
  void set_bestratetimesell(const ::std::string& value);
  void set_bestratetimesell(const char* value);
  void set_bestratetimesell(const char* value, size_t size);
  ::std::string* mutable_bestratetimesell();
  ::std::string* release_bestratetimesell();
  void set_allocated_bestratetimesell(::std::string* bestratetimesell);

  // optional int64 BestRateBuy = 5;
  void clear_bestratebuy();
  static const int kBestRateBuyFieldNumber = 5;
  ::google::protobuf::int64 bestratebuy() const;
  void set_bestratebuy(::google::protobuf::int64 value);

  // optional int64 BestRateSell = 6;
  void clear_bestratesell();
  static const int kBestRateSellFieldNumber = 6;
  ::google::protobuf::int64 bestratesell() const;
  void set_bestratesell(::google::protobuf::int64 value);

  // optional string RateLiquidProviderBuy1 = 7;
  void clear_rateliquidproviderbuy1();
  static const int kRateLiquidProviderBuy1FieldNumber = 7;
  const ::std::string& rateliquidproviderbuy1() const;
  void set_rateliquidproviderbuy1(const ::std::string& value);
  void set_rateliquidproviderbuy1(const char* value);
  void set_rateliquidproviderbuy1(const char* value, size_t size);
  ::std::string* mutable_rateliquidproviderbuy1();
  ::std::string* release_rateliquidproviderbuy1();
  void set_allocated_rateliquidproviderbuy1(::std::string* rateliquidproviderbuy1);

  // optional string RateLiquidProviderBuy2 = 8;
  void clear_rateliquidproviderbuy2();
  static const int kRateLiquidProviderBuy2FieldNumber = 8;
  const ::std::string& rateliquidproviderbuy2() const;
  void set_rateliquidproviderbuy2(const ::std::string& value);
  void set_rateliquidproviderbuy2(const char* value);
  void set_rateliquidproviderbuy2(const char* value, size_t size);
  ::std::string* mutable_rateliquidproviderbuy2();
  ::std::string* release_rateliquidproviderbuy2();
  void set_allocated_rateliquidproviderbuy2(::std::string* rateliquidproviderbuy2);

  // optional string RateLiquidProviderBuy3 = 9;
  void clear_rateliquidproviderbuy3();
  static const int kRateLiquidProviderBuy3FieldNumber = 9;
  const ::std::string& rateliquidproviderbuy3() const;
  void set_rateliquidproviderbuy3(const ::std::string& value);
  void set_rateliquidproviderbuy3(const char* value);
  void set_rateliquidproviderbuy3(const char* value, size_t size);
  ::std::string* mutable_rateliquidproviderbuy3();
  ::std::string* release_rateliquidproviderbuy3();
  void set_allocated_rateliquidproviderbuy3(::std::string* rateliquidproviderbuy3);

  // optional string RateLiquidProviderBuy4 = 10;
  void clear_rateliquidproviderbuy4();
  static const int kRateLiquidProviderBuy4FieldNumber = 10;
  const ::std::string& rateliquidproviderbuy4() const;
  void set_rateliquidproviderbuy4(const ::std::string& value);
  void set_rateliquidproviderbuy4(const char* value);
  void set_rateliquidproviderbuy4(const char* value, size_t size);
  ::std::string* mutable_rateliquidproviderbuy4();
  ::std::string* release_rateliquidproviderbuy4();
  void set_allocated_rateliquidproviderbuy4(::std::string* rateliquidproviderbuy4);

  // optional string RateLiquidProviderBuy5 = 11;
  void clear_rateliquidproviderbuy5();
  static const int kRateLiquidProviderBuy5FieldNumber = 11;
  const ::std::string& rateliquidproviderbuy5() const;
  void set_rateliquidproviderbuy5(const ::std::string& value);
  void set_rateliquidproviderbuy5(const char* value);
  void set_rateliquidproviderbuy5(const char* value, size_t size);
  ::std::string* mutable_rateliquidproviderbuy5();
  ::std::string* release_rateliquidproviderbuy5();
  void set_allocated_rateliquidproviderbuy5(::std::string* rateliquidproviderbuy5);

  // optional string RateLiquidProviderSell1 = 12;
  void clear_rateliquidprovidersell1();
  static const int kRateLiquidProviderSell1FieldNumber = 12;
  const ::std::string& rateliquidprovidersell1() const;
  void set_rateliquidprovidersell1(const ::std::string& value);
  void set_rateliquidprovidersell1(const char* value);
  void set_rateliquidprovidersell1(const char* value, size_t size);
  ::std::string* mutable_rateliquidprovidersell1();
  ::std::string* release_rateliquidprovidersell1();
  void set_allocated_rateliquidprovidersell1(::std::string* rateliquidprovidersell1);

  // optional string RateLiquidProviderSell2 = 13;
  void clear_rateliquidprovidersell2();
  static const int kRateLiquidProviderSell2FieldNumber = 13;
  const ::std::string& rateliquidprovidersell2() const;
  void set_rateliquidprovidersell2(const ::std::string& value);
  void set_rateliquidprovidersell2(const char* value);
  void set_rateliquidprovidersell2(const char* value, size_t size);
  ::std::string* mutable_rateliquidprovidersell2();
  ::std::string* release_rateliquidprovidersell2();
  void set_allocated_rateliquidprovidersell2(::std::string* rateliquidprovidersell2);

  // optional string RateLiquidProviderSell3 = 14;
  void clear_rateliquidprovidersell3();
  static const int kRateLiquidProviderSell3FieldNumber = 14;
  const ::std::string& rateliquidprovidersell3() const;
  void set_rateliquidprovidersell3(const ::std::string& value);
  void set_rateliquidprovidersell3(const char* value);
  void set_rateliquidprovidersell3(const char* value, size_t size);
  ::std::string* mutable_rateliquidprovidersell3();
  ::std::string* release_rateliquidprovidersell3();
  void set_allocated_rateliquidprovidersell3(::std::string* rateliquidprovidersell3);

  // optional string RateLiquidProviderSell4 = 15;
  void clear_rateliquidprovidersell4();
  static const int kRateLiquidProviderSell4FieldNumber = 15;
  const ::std::string& rateliquidprovidersell4() const;
  void set_rateliquidprovidersell4(const ::std::string& value);
  void set_rateliquidprovidersell4(const char* value);
  void set_rateliquidprovidersell4(const char* value, size_t size);
  ::std::string* mutable_rateliquidprovidersell4();
  ::std::string* release_rateliquidprovidersell4();
  void set_allocated_rateliquidprovidersell4(::std::string* rateliquidprovidersell4);

  // optional string RateLiquidProviderSell5 = 16;
  void clear_rateliquidprovidersell5();
  static const int kRateLiquidProviderSell5FieldNumber = 16;
  const ::std::string& rateliquidprovidersell5() const;
  void set_rateliquidprovidersell5(const ::std::string& value);
  void set_rateliquidprovidersell5(const char* value);
  void set_rateliquidprovidersell5(const char* value, size_t size);
  ::std::string* mutable_rateliquidprovidersell5();
  ::std::string* release_rateliquidprovidersell5();
  void set_allocated_rateliquidprovidersell5(::std::string* rateliquidprovidersell5);

  // optional string LegSign = 17;
  void clear_legsign();
  static const int kLegSignFieldNumber = 17;
  const ::std::string& legsign() const;
  void set_legsign(const ::std::string& value);
  void set_legsign(const char* value);
  void set_legsign(const char* value, size_t size);
  ::std::string* mutable_legsign();
  ::std::string* release_legsign();
  void set_allocated_legsign(::std::string* legsign);

  // optional string TradeDate = 18;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 18;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr bestratedatebuy_;
  ::google::protobuf::internal::ArenaStringPtr bestratetimebuy_;
  ::google::protobuf::internal::ArenaStringPtr bestratedatesell_;
  ::google::protobuf::internal::ArenaStringPtr bestratetimesell_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidproviderbuy1_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidproviderbuy2_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidproviderbuy3_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidproviderbuy4_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidproviderbuy5_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidprovidersell1_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidprovidersell2_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidprovidersell3_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidprovidersell4_;
  ::google::protobuf::internal::ArenaStringPtr rateliquidprovidersell5_;
  ::google::protobuf::internal::ArenaStringPtr legsign_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::int64 bestratebuy_;
  ::google::protobuf::int64 bestratesell_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<SwpSptNdfFowFxQuote> SwpSptNdfFowFxQuote_default_instance_;

// -------------------------------------------------------------------

class OptionFxQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.OptionFxQuote) */ {
 public:
  OptionFxQuote();
  virtual ~OptionFxQuote();

  OptionFxQuote(const OptionFxQuote& from);

  inline OptionFxQuote& operator=(const OptionFxQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const OptionFxQuote& default_instance();

  static const OptionFxQuote* internal_default_instance();

  void Swap(OptionFxQuote* other);

  // implements Message ----------------------------------------------

  inline OptionFxQuote* New() const { return New(NULL); }

  OptionFxQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const OptionFxQuote& from);
  void MergeFrom(const OptionFxQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(OptionFxQuote* other);
  void UnsafeMergeFrom(const OptionFxQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string BestRateDateBuy = 1;
  void clear_bestratedatebuy();
  static const int kBestRateDateBuyFieldNumber = 1;
  const ::std::string& bestratedatebuy() const;
  void set_bestratedatebuy(const ::std::string& value);
  void set_bestratedatebuy(const char* value);
  void set_bestratedatebuy(const char* value, size_t size);
  ::std::string* mutable_bestratedatebuy();
  ::std::string* release_bestratedatebuy();
  void set_allocated_bestratedatebuy(::std::string* bestratedatebuy);

  // optional string BestRateTimeBuy = 2;
  void clear_bestratetimebuy();
  static const int kBestRateTimeBuyFieldNumber = 2;
  const ::std::string& bestratetimebuy() const;
  void set_bestratetimebuy(const ::std::string& value);
  void set_bestratetimebuy(const char* value);
  void set_bestratetimebuy(const char* value, size_t size);
  ::std::string* mutable_bestratetimebuy();
  ::std::string* release_bestratetimebuy();
  void set_allocated_bestratetimebuy(::std::string* bestratetimebuy);

  // optional string BestRateDateSell = 3;
  void clear_bestratedatesell();
  static const int kBestRateDateSellFieldNumber = 3;
  const ::std::string& bestratedatesell() const;
  void set_bestratedatesell(const ::std::string& value);
  void set_bestratedatesell(const char* value);
  void set_bestratedatesell(const char* value, size_t size);
  ::std::string* mutable_bestratedatesell();
  ::std::string* release_bestratedatesell();
  void set_allocated_bestratedatesell(::std::string* bestratedatesell);

  // optional string BestRateTimeSell = 4;
  void clear_bestratetimesell();
  static const int kBestRateTimeSellFieldNumber = 4;
  const ::std::string& bestratetimesell() const;
  void set_bestratetimesell(const ::std::string& value);
  void set_bestratetimesell(const char* value);
  void set_bestratetimesell(const char* value, size_t size);
  ::std::string* mutable_bestratetimesell();
  ::std::string* release_bestratetimesell();
  void set_allocated_bestratetimesell(::std::string* bestratetimesell);

  // optional int64 BestRateBuy = 5;
  void clear_bestratebuy();
  static const int kBestRateBuyFieldNumber = 5;
  ::google::protobuf::int64 bestratebuy() const;
  void set_bestratebuy(::google::protobuf::int64 value);

  // optional int64 BestRateSell = 6;
  void clear_bestratesell();
  static const int kBestRateSellFieldNumber = 6;
  ::google::protobuf::int64 bestratesell() const;
  void set_bestratesell(::google::protobuf::int64 value);

  // optional string VolatilitySurface = 7;
  void clear_volatilitysurface();
  static const int kVolatilitySurfaceFieldNumber = 7;
  const ::std::string& volatilitysurface() const;
  void set_volatilitysurface(const ::std::string& value);
  void set_volatilitysurface(const char* value);
  void set_volatilitysurface(const char* value, size_t size);
  ::std::string* mutable_volatilitysurface();
  ::std::string* release_volatilitysurface();
  void set_allocated_volatilitysurface(::std::string* volatilitysurface);

  // optional string TenorBuy = 8;
  void clear_tenorbuy();
  static const int kTenorBuyFieldNumber = 8;
  const ::std::string& tenorbuy() const;
  void set_tenorbuy(const ::std::string& value);
  void set_tenorbuy(const char* value);
  void set_tenorbuy(const char* value, size_t size);
  ::std::string* mutable_tenorbuy();
  ::std::string* release_tenorbuy();
  void set_allocated_tenorbuy(::std::string* tenorbuy);

  // optional string TenorSell = 9;
  void clear_tenorsell();
  static const int kTenorSellFieldNumber = 9;
  const ::std::string& tenorsell() const;
  void set_tenorsell(const ::std::string& value);
  void set_tenorsell(const char* value);
  void set_tenorsell(const char* value, size_t size);
  ::std::string* mutable_tenorsell();
  ::std::string* release_tenorsell();
  void set_allocated_tenorsell(::std::string* tenorsell);

  // optional string MakerInstitutionBuy = 10;
  void clear_makerinstitutionbuy();
  static const int kMakerInstitutionBuyFieldNumber = 10;
  const ::std::string& makerinstitutionbuy() const;
  void set_makerinstitutionbuy(const ::std::string& value);
  void set_makerinstitutionbuy(const char* value);
  void set_makerinstitutionbuy(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionbuy();
  ::std::string* release_makerinstitutionbuy();
  void set_allocated_makerinstitutionbuy(::std::string* makerinstitutionbuy);

  // optional string MakerInstitutionSell = 11;
  void clear_makerinstitutionsell();
  static const int kMakerInstitutionSellFieldNumber = 11;
  const ::std::string& makerinstitutionsell() const;
  void set_makerinstitutionsell(const ::std::string& value);
  void set_makerinstitutionsell(const char* value);
  void set_makerinstitutionsell(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionsell();
  ::std::string* release_makerinstitutionsell();
  void set_allocated_makerinstitutionsell(::std::string* makerinstitutionsell);

  // optional string Tenor = 12;
  void clear_tenor();
  static const int kTenorFieldNumber = 12;
  const ::std::string& tenor() const;
  void set_tenor(const ::std::string& value);
  void set_tenor(const char* value);
  void set_tenor(const char* value, size_t size);
  ::std::string* mutable_tenor();
  ::std::string* release_tenor();
  void set_allocated_tenor(::std::string* tenor);

  // optional string TradeDate = 13;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 13;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional string MakerInstitutionBuy2 = 14;
  void clear_makerinstitutionbuy2();
  static const int kMakerInstitutionBuy2FieldNumber = 14;
  const ::std::string& makerinstitutionbuy2() const;
  void set_makerinstitutionbuy2(const ::std::string& value);
  void set_makerinstitutionbuy2(const char* value);
  void set_makerinstitutionbuy2(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionbuy2();
  ::std::string* release_makerinstitutionbuy2();
  void set_allocated_makerinstitutionbuy2(::std::string* makerinstitutionbuy2);

  // optional string MakerInstitutionSell2 = 15;
  void clear_makerinstitutionsell2();
  static const int kMakerInstitutionSell2FieldNumber = 15;
  const ::std::string& makerinstitutionsell2() const;
  void set_makerinstitutionsell2(const ::std::string& value);
  void set_makerinstitutionsell2(const char* value);
  void set_makerinstitutionsell2(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionsell2();
  ::std::string* release_makerinstitutionsell2();
  void set_allocated_makerinstitutionsell2(::std::string* makerinstitutionsell2);

  // optional string MakerInstitutionBuy3 = 16;
  void clear_makerinstitutionbuy3();
  static const int kMakerInstitutionBuy3FieldNumber = 16;
  const ::std::string& makerinstitutionbuy3() const;
  void set_makerinstitutionbuy3(const ::std::string& value);
  void set_makerinstitutionbuy3(const char* value);
  void set_makerinstitutionbuy3(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionbuy3();
  ::std::string* release_makerinstitutionbuy3();
  void set_allocated_makerinstitutionbuy3(::std::string* makerinstitutionbuy3);

  // optional string MakerInstitutionSell3 = 17;
  void clear_makerinstitutionsell3();
  static const int kMakerInstitutionSell3FieldNumber = 17;
  const ::std::string& makerinstitutionsell3() const;
  void set_makerinstitutionsell3(const ::std::string& value);
  void set_makerinstitutionsell3(const char* value);
  void set_makerinstitutionsell3(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionsell3();
  ::std::string* release_makerinstitutionsell3();
  void set_allocated_makerinstitutionsell3(::std::string* makerinstitutionsell3);

  // optional string MakerInstitutionBuy4 = 18;
  void clear_makerinstitutionbuy4();
  static const int kMakerInstitutionBuy4FieldNumber = 18;
  const ::std::string& makerinstitutionbuy4() const;
  void set_makerinstitutionbuy4(const ::std::string& value);
  void set_makerinstitutionbuy4(const char* value);
  void set_makerinstitutionbuy4(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionbuy4();
  ::std::string* release_makerinstitutionbuy4();
  void set_allocated_makerinstitutionbuy4(::std::string* makerinstitutionbuy4);

  // optional string MakerInstitutionSell4 = 19;
  void clear_makerinstitutionsell4();
  static const int kMakerInstitutionSell4FieldNumber = 19;
  const ::std::string& makerinstitutionsell4() const;
  void set_makerinstitutionsell4(const ::std::string& value);
  void set_makerinstitutionsell4(const char* value);
  void set_makerinstitutionsell4(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionsell4();
  ::std::string* release_makerinstitutionsell4();
  void set_allocated_makerinstitutionsell4(::std::string* makerinstitutionsell4);

  // optional string MakerInstitutionBuy5 = 20;
  void clear_makerinstitutionbuy5();
  static const int kMakerInstitutionBuy5FieldNumber = 20;
  const ::std::string& makerinstitutionbuy5() const;
  void set_makerinstitutionbuy5(const ::std::string& value);
  void set_makerinstitutionbuy5(const char* value);
  void set_makerinstitutionbuy5(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionbuy5();
  ::std::string* release_makerinstitutionbuy5();
  void set_allocated_makerinstitutionbuy5(::std::string* makerinstitutionbuy5);

  // optional string MakerInstitutionSell5 = 21;
  void clear_makerinstitutionsell5();
  static const int kMakerInstitutionSell5FieldNumber = 21;
  const ::std::string& makerinstitutionsell5() const;
  void set_makerinstitutionsell5(const ::std::string& value);
  void set_makerinstitutionsell5(const char* value);
  void set_makerinstitutionsell5(const char* value, size_t size);
  ::std::string* mutable_makerinstitutionsell5();
  ::std::string* release_makerinstitutionsell5();
  void set_allocated_makerinstitutionsell5(::std::string* makerinstitutionsell5);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.OptionFxQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr bestratedatebuy_;
  ::google::protobuf::internal::ArenaStringPtr bestratetimebuy_;
  ::google::protobuf::internal::ArenaStringPtr bestratedatesell_;
  ::google::protobuf::internal::ArenaStringPtr bestratetimesell_;
  ::google::protobuf::internal::ArenaStringPtr volatilitysurface_;
  ::google::protobuf::internal::ArenaStringPtr tenorbuy_;
  ::google::protobuf::internal::ArenaStringPtr tenorsell_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionbuy_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionsell_;
  ::google::protobuf::internal::ArenaStringPtr tenor_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionbuy2_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionsell2_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionbuy3_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionsell3_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionbuy4_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionsell4_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionbuy5_;
  ::google::protobuf::internal::ArenaStringPtr makerinstitutionsell5_;
  ::google::protobuf::int64 bestratebuy_;
  ::google::protobuf::int64 bestratesell_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsFxQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsFxQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsFxQuote_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsFxQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<OptionFxQuote> OptionFxQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxQuote

// optional string HTSCSecurityID = 1;
inline void MDCfetsFxQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
inline void MDCfetsFxQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
inline void MDCfetsFxQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
inline ::std::string* MDCfetsFxQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDCfetsFxQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDDate)
  return mddate_;
}
inline void MDCfetsFxQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDCfetsFxQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDTime)
  return mdtime_;
}
inline void MDCfetsFxQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDCfetsFxQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsFxQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsFxQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDCfetsFxQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsFxQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDCfetsFxQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsFxQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsFxQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityType)
}

// optional string SecuritySubType = 7;
inline void MDCfetsFxQuote::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxQuote::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
inline void MDCfetsFxQuote::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
inline void MDCfetsFxQuote::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
inline ::std::string* MDCfetsFxQuote::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxQuote::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}

// optional int32 ForexQuoteType = 8;
inline void MDCfetsFxQuote::clear_forexquotetype() {
  forexquotetype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxQuote::forexquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.ForexQuoteType)
  return forexquotetype_;
}
inline void MDCfetsFxQuote::set_forexquotetype(::google::protobuf::int32 value) {
  
  forexquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.ForexQuoteType)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
inline bool MDCfetsFxQuote::has_spotfxquote() const {
  return this != internal_default_instance() && spotfxquote_ != NULL;
}
inline void MDCfetsFxQuote::clear_spotfxquote() {
  if (GetArenaNoVirtual() == NULL && spotfxquote_ != NULL) delete spotfxquote_;
  spotfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::spotfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  return spotfxquote_ != NULL ? *spotfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_spotfxquote() {
  
  if (spotfxquote_ == NULL) {
    spotfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  return spotfxquote_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_spotfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = spotfxquote_;
  spotfxquote_ = NULL;
  return temp;
}
inline void MDCfetsFxQuote::set_allocated_spotfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* spotfxquote) {
  delete spotfxquote_;
  spotfxquote_ = spotfxquote;
  if (spotfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
inline bool MDCfetsFxQuote::has_forwardfxquote() const {
  return this != internal_default_instance() && forwardfxquote_ != NULL;
}
inline void MDCfetsFxQuote::clear_forwardfxquote() {
  if (GetArenaNoVirtual() == NULL && forwardfxquote_ != NULL) delete forwardfxquote_;
  forwardfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::forwardfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  return forwardfxquote_ != NULL ? *forwardfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_forwardfxquote() {
  
  if (forwardfxquote_ == NULL) {
    forwardfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  return forwardfxquote_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_forwardfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = forwardfxquote_;
  forwardfxquote_ = NULL;
  return temp;
}
inline void MDCfetsFxQuote::set_allocated_forwardfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* forwardfxquote) {
  delete forwardfxquote_;
  forwardfxquote_ = forwardfxquote;
  if (forwardfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
inline bool MDCfetsFxQuote::has_nondeliverableforwardsfxquote() const {
  return this != internal_default_instance() && nondeliverableforwardsfxquote_ != NULL;
}
inline void MDCfetsFxQuote::clear_nondeliverableforwardsfxquote() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxquote_ != NULL) delete nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::nondeliverableforwardsfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  return nondeliverableforwardsfxquote_ != NULL ? *nondeliverableforwardsfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_nondeliverableforwardsfxquote() {
  
  if (nondeliverableforwardsfxquote_ == NULL) {
    nondeliverableforwardsfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  return nondeliverableforwardsfxquote_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_nondeliverableforwardsfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = NULL;
  return temp;
}
inline void MDCfetsFxQuote::set_allocated_nondeliverableforwardsfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* nondeliverableforwardsfxquote) {
  delete nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = nondeliverableforwardsfxquote;
  if (nondeliverableforwardsfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
inline bool MDCfetsFxQuote::has_swapfxquote() const {
  return this != internal_default_instance() && swapfxquote_ != NULL;
}
inline void MDCfetsFxQuote::clear_swapfxquote() {
  if (GetArenaNoVirtual() == NULL && swapfxquote_ != NULL) delete swapfxquote_;
  swapfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::swapfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  return swapfxquote_ != NULL ? *swapfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_swapfxquote() {
  
  if (swapfxquote_ == NULL) {
    swapfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  return swapfxquote_;
}
inline ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_swapfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = swapfxquote_;
  swapfxquote_ = NULL;
  return temp;
}
inline void MDCfetsFxQuote::set_allocated_swapfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* swapfxquote) {
  delete swapfxquote_;
  swapfxquote_ = swapfxquote;
  if (swapfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
}

// optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
inline bool MDCfetsFxQuote::has_optionfxquote() const {
  return this != internal_default_instance() && optionfxquote_ != NULL;
}
inline void MDCfetsFxQuote::clear_optionfxquote() {
  if (GetArenaNoVirtual() == NULL && optionfxquote_ != NULL) delete optionfxquote_;
  optionfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::OptionFxQuote& MDCfetsFxQuote::optionfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  return optionfxquote_ != NULL ? *optionfxquote_
                         : *::com::htsc::mdc::insight::model::OptionFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::OptionFxQuote* MDCfetsFxQuote::mutable_optionfxquote() {
  
  if (optionfxquote_ == NULL) {
    optionfxquote_ = new ::com::htsc::mdc::insight::model::OptionFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  return optionfxquote_;
}
inline ::com::htsc::mdc::insight::model::OptionFxQuote* MDCfetsFxQuote::release_optionfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  
  ::com::htsc::mdc::insight::model::OptionFxQuote* temp = optionfxquote_;
  optionfxquote_ = NULL;
  return temp;
}
inline void MDCfetsFxQuote::set_allocated_optionfxquote(::com::htsc::mdc::insight::model::OptionFxQuote* optionfxquote) {
  delete optionfxquote_;
  optionfxquote_ = optionfxquote;
  if (optionfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
}

// optional int32 DataMultiplePowerOf10 = 14;
inline void MDCfetsFxQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsFxQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsFxQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataMultiplePowerOf10)
}

// optional string TransactTime = 15;
inline void MDCfetsFxQuote::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxQuote::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
inline void MDCfetsFxQuote::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
inline void MDCfetsFxQuote::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
inline ::std::string* MDCfetsFxQuote::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxQuote::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}

// optional string MarketIndicator = 16;
inline void MDCfetsFxQuote::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsFxQuote::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
inline void MDCfetsFxQuote::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
inline void MDCfetsFxQuote::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
inline ::std::string* MDCfetsFxQuote::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsFxQuote::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsFxQuote::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}

inline const MDCfetsFxQuote* MDCfetsFxQuote::internal_default_instance() {
  return &MDCfetsFxQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// SwpSptNdfFowFxQuote

// optional string BestRateDateBuy = 1;
inline void SwpSptNdfFowFxQuote::clear_bestratedatebuy() {
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::bestratedatebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  return bestratedatebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_bestratedatebuy(const ::std::string& value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
inline void SwpSptNdfFowFxQuote::set_bestratedatebuy(const char* value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
inline void SwpSptNdfFowFxQuote::set_bestratedatebuy(const char* value, size_t size) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_bestratedatebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  return bestratedatebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_bestratedatebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  
  return bestratedatebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_bestratedatebuy(::std::string* bestratedatebuy) {
  if (bestratedatebuy != NULL) {
    
  } else {
    
  }
  bestratedatebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}

// optional string BestRateTimeBuy = 2;
inline void SwpSptNdfFowFxQuote::clear_bestratetimebuy() {
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::bestratetimebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_bestratetimebuy(const ::std::string& value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
inline void SwpSptNdfFowFxQuote::set_bestratetimebuy(const char* value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
inline void SwpSptNdfFowFxQuote::set_bestratetimebuy(const char* value, size_t size) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_bestratetimebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_bestratetimebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  
  return bestratetimebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_bestratetimebuy(::std::string* bestratetimebuy) {
  if (bestratetimebuy != NULL) {
    
  } else {
    
  }
  bestratetimebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}

// optional string BestRateDateSell = 3;
inline void SwpSptNdfFowFxQuote::clear_bestratedatesell() {
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::bestratedatesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  return bestratedatesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_bestratedatesell(const ::std::string& value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
inline void SwpSptNdfFowFxQuote::set_bestratedatesell(const char* value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
inline void SwpSptNdfFowFxQuote::set_bestratedatesell(const char* value, size_t size) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_bestratedatesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  return bestratedatesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_bestratedatesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  
  return bestratedatesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_bestratedatesell(::std::string* bestratedatesell) {
  if (bestratedatesell != NULL) {
    
  } else {
    
  }
  bestratedatesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}

// optional string BestRateTimeSell = 4;
inline void SwpSptNdfFowFxQuote::clear_bestratetimesell() {
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::bestratetimesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  return bestratetimesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_bestratetimesell(const ::std::string& value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
inline void SwpSptNdfFowFxQuote::set_bestratetimesell(const char* value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
inline void SwpSptNdfFowFxQuote::set_bestratetimesell(const char* value, size_t size) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_bestratetimesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  return bestratetimesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_bestratetimesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  
  return bestratetimesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_bestratetimesell(::std::string* bestratetimesell) {
  if (bestratetimesell != NULL) {
    
  } else {
    
  }
  bestratetimesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}

// optional int64 BestRateBuy = 5;
inline void SwpSptNdfFowFxQuote::clear_bestratebuy() {
  bestratebuy_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxQuote::bestratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateBuy)
  return bestratebuy_;
}
inline void SwpSptNdfFowFxQuote::set_bestratebuy(::google::protobuf::int64 value) {
  
  bestratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateBuy)
}

// optional int64 BestRateSell = 6;
inline void SwpSptNdfFowFxQuote::clear_bestratesell() {
  bestratesell_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SwpSptNdfFowFxQuote::bestratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateSell)
  return bestratesell_;
}
inline void SwpSptNdfFowFxQuote::set_bestratesell(::google::protobuf::int64 value) {
  
  bestratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateSell)
}

// optional string RateLiquidProviderBuy1 = 7;
inline void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy1() {
  rateliquidproviderbuy1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  return rateliquidproviderbuy1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const ::std::string& value) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const char* value) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const char* value, size_t size) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  return rateliquidproviderbuy1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  
  return rateliquidproviderbuy1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy1(::std::string* rateliquidproviderbuy1) {
  if (rateliquidproviderbuy1 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}

// optional string RateLiquidProviderBuy2 = 8;
inline void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy2() {
  rateliquidproviderbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  return rateliquidproviderbuy2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const ::std::string& value) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const char* value) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const char* value, size_t size) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  return rateliquidproviderbuy2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  
  return rateliquidproviderbuy2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy2(::std::string* rateliquidproviderbuy2) {
  if (rateliquidproviderbuy2 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}

// optional string RateLiquidProviderBuy3 = 9;
inline void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy3() {
  rateliquidproviderbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  return rateliquidproviderbuy3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const ::std::string& value) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const char* value) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const char* value, size_t size) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  return rateliquidproviderbuy3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  
  return rateliquidproviderbuy3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy3(::std::string* rateliquidproviderbuy3) {
  if (rateliquidproviderbuy3 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}

// optional string RateLiquidProviderBuy4 = 10;
inline void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy4() {
  rateliquidproviderbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  return rateliquidproviderbuy4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const ::std::string& value) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const char* value) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const char* value, size_t size) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  return rateliquidproviderbuy4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  
  return rateliquidproviderbuy4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy4(::std::string* rateliquidproviderbuy4) {
  if (rateliquidproviderbuy4 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}

// optional string RateLiquidProviderBuy5 = 11;
inline void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy5() {
  rateliquidproviderbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  return rateliquidproviderbuy5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const ::std::string& value) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const char* value) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const char* value, size_t size) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  return rateliquidproviderbuy5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  
  return rateliquidproviderbuy5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy5(::std::string* rateliquidproviderbuy5) {
  if (rateliquidproviderbuy5 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}

// optional string RateLiquidProviderSell1 = 12;
inline void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell1() {
  rateliquidprovidersell1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  return rateliquidprovidersell1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const ::std::string& value) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const char* value) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const char* value, size_t size) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  return rateliquidprovidersell1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  
  return rateliquidprovidersell1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell1(::std::string* rateliquidprovidersell1) {
  if (rateliquidprovidersell1 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}

// optional string RateLiquidProviderSell2 = 13;
inline void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell2() {
  rateliquidprovidersell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  return rateliquidprovidersell2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const ::std::string& value) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const char* value) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const char* value, size_t size) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  return rateliquidprovidersell2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  
  return rateliquidprovidersell2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell2(::std::string* rateliquidprovidersell2) {
  if (rateliquidprovidersell2 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}

// optional string RateLiquidProviderSell3 = 14;
inline void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell3() {
  rateliquidprovidersell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  return rateliquidprovidersell3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const ::std::string& value) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const char* value) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const char* value, size_t size) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  return rateliquidprovidersell3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  
  return rateliquidprovidersell3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell3(::std::string* rateliquidprovidersell3) {
  if (rateliquidprovidersell3 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}

// optional string RateLiquidProviderSell4 = 15;
inline void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell4() {
  rateliquidprovidersell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  return rateliquidprovidersell4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const ::std::string& value) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const char* value) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const char* value, size_t size) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  return rateliquidprovidersell4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  
  return rateliquidprovidersell4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell4(::std::string* rateliquidprovidersell4) {
  if (rateliquidprovidersell4 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}

// optional string RateLiquidProviderSell5 = 16;
inline void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell5() {
  rateliquidprovidersell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  return rateliquidprovidersell5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const ::std::string& value) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const char* value) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
inline void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const char* value, size_t size) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  return rateliquidprovidersell5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  
  return rateliquidprovidersell5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell5(::std::string* rateliquidprovidersell5) {
  if (rateliquidprovidersell5 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}

// optional string LegSign = 17;
inline void SwpSptNdfFowFxQuote::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
inline void SwpSptNdfFowFxQuote::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
inline void SwpSptNdfFowFxQuote::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}

// optional string TradeDate = 18;
inline void SwpSptNdfFowFxQuote::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SwpSptNdfFowFxQuote::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
inline void SwpSptNdfFowFxQuote::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
inline void SwpSptNdfFowFxQuote::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
inline ::std::string* SwpSptNdfFowFxQuote::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SwpSptNdfFowFxQuote::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SwpSptNdfFowFxQuote::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}

inline const SwpSptNdfFowFxQuote* SwpSptNdfFowFxQuote::internal_default_instance() {
  return &SwpSptNdfFowFxQuote_default_instance_.get();
}
// -------------------------------------------------------------------

// OptionFxQuote

// optional string BestRateDateBuy = 1;
inline void OptionFxQuote::clear_bestratedatebuy() {
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::bestratedatebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  return bestratedatebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_bestratedatebuy(const ::std::string& value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
inline void OptionFxQuote::set_bestratedatebuy(const char* value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
inline void OptionFxQuote::set_bestratedatebuy(const char* value, size_t size) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
inline ::std::string* OptionFxQuote::mutable_bestratedatebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  return bestratedatebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_bestratedatebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  
  return bestratedatebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_bestratedatebuy(::std::string* bestratedatebuy) {
  if (bestratedatebuy != NULL) {
    
  } else {
    
  }
  bestratedatebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}

// optional string BestRateTimeBuy = 2;
inline void OptionFxQuote::clear_bestratetimebuy() {
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::bestratetimebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_bestratetimebuy(const ::std::string& value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
inline void OptionFxQuote::set_bestratetimebuy(const char* value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
inline void OptionFxQuote::set_bestratetimebuy(const char* value, size_t size) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
inline ::std::string* OptionFxQuote::mutable_bestratetimebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_bestratetimebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  
  return bestratetimebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_bestratetimebuy(::std::string* bestratetimebuy) {
  if (bestratetimebuy != NULL) {
    
  } else {
    
  }
  bestratetimebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}

// optional string BestRateDateSell = 3;
inline void OptionFxQuote::clear_bestratedatesell() {
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::bestratedatesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  return bestratedatesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_bestratedatesell(const ::std::string& value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
inline void OptionFxQuote::set_bestratedatesell(const char* value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
inline void OptionFxQuote::set_bestratedatesell(const char* value, size_t size) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
inline ::std::string* OptionFxQuote::mutable_bestratedatesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  return bestratedatesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_bestratedatesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  
  return bestratedatesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_bestratedatesell(::std::string* bestratedatesell) {
  if (bestratedatesell != NULL) {
    
  } else {
    
  }
  bestratedatesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}

// optional string BestRateTimeSell = 4;
inline void OptionFxQuote::clear_bestratetimesell() {
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::bestratetimesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  return bestratetimesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_bestratetimesell(const ::std::string& value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
inline void OptionFxQuote::set_bestratetimesell(const char* value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
inline void OptionFxQuote::set_bestratetimesell(const char* value, size_t size) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
inline ::std::string* OptionFxQuote::mutable_bestratetimesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  return bestratetimesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_bestratetimesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  
  return bestratetimesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_bestratetimesell(::std::string* bestratetimesell) {
  if (bestratetimesell != NULL) {
    
  } else {
    
  }
  bestratetimesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}

// optional int64 BestRateBuy = 5;
inline void OptionFxQuote::clear_bestratebuy() {
  bestratebuy_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionFxQuote::bestratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateBuy)
  return bestratebuy_;
}
inline void OptionFxQuote::set_bestratebuy(::google::protobuf::int64 value) {
  
  bestratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateBuy)
}

// optional int64 BestRateSell = 6;
inline void OptionFxQuote::clear_bestratesell() {
  bestratesell_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 OptionFxQuote::bestratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateSell)
  return bestratesell_;
}
inline void OptionFxQuote::set_bestratesell(::google::protobuf::int64 value) {
  
  bestratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateSell)
}

// optional string VolatilitySurface = 7;
inline void OptionFxQuote::clear_volatilitysurface() {
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::volatilitysurface() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  return volatilitysurface_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_volatilitysurface(const ::std::string& value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
inline void OptionFxQuote::set_volatilitysurface(const char* value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
inline void OptionFxQuote::set_volatilitysurface(const char* value, size_t size) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
inline ::std::string* OptionFxQuote::mutable_volatilitysurface() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  return volatilitysurface_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_volatilitysurface() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  
  return volatilitysurface_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_volatilitysurface(::std::string* volatilitysurface) {
  if (volatilitysurface != NULL) {
    
  } else {
    
  }
  volatilitysurface_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitysurface);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}

// optional string TenorBuy = 8;
inline void OptionFxQuote::clear_tenorbuy() {
  tenorbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::tenorbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  return tenorbuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_tenorbuy(const ::std::string& value) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
inline void OptionFxQuote::set_tenorbuy(const char* value) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
inline void OptionFxQuote::set_tenorbuy(const char* value, size_t size) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
inline ::std::string* OptionFxQuote::mutable_tenorbuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  return tenorbuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_tenorbuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  
  return tenorbuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_tenorbuy(::std::string* tenorbuy) {
  if (tenorbuy != NULL) {
    
  } else {
    
  }
  tenorbuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenorbuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}

// optional string TenorSell = 9;
inline void OptionFxQuote::clear_tenorsell() {
  tenorsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::tenorsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  return tenorsell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_tenorsell(const ::std::string& value) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
inline void OptionFxQuote::set_tenorsell(const char* value) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
inline void OptionFxQuote::set_tenorsell(const char* value, size_t size) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
inline ::std::string* OptionFxQuote::mutable_tenorsell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  return tenorsell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_tenorsell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  
  return tenorsell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_tenorsell(::std::string* tenorsell) {
  if (tenorsell != NULL) {
    
  } else {
    
  }
  tenorsell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenorsell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}

// optional string MakerInstitutionBuy = 10;
inline void OptionFxQuote::clear_makerinstitutionbuy() {
  makerinstitutionbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  return makerinstitutionbuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionbuy(const ::std::string& value) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
inline void OptionFxQuote::set_makerinstitutionbuy(const char* value) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
inline void OptionFxQuote::set_makerinstitutionbuy(const char* value, size_t size) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionbuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  return makerinstitutionbuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionbuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  
  return makerinstitutionbuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionbuy(::std::string* makerinstitutionbuy) {
  if (makerinstitutionbuy != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}

// optional string MakerInstitutionSell = 11;
inline void OptionFxQuote::clear_makerinstitutionsell() {
  makerinstitutionsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  return makerinstitutionsell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionsell(const ::std::string& value) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
inline void OptionFxQuote::set_makerinstitutionsell(const char* value) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
inline void OptionFxQuote::set_makerinstitutionsell(const char* value, size_t size) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionsell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  return makerinstitutionsell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionsell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  
  return makerinstitutionsell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionsell(::std::string* makerinstitutionsell) {
  if (makerinstitutionsell != NULL) {
    
  } else {
    
  }
  makerinstitutionsell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}

// optional string Tenor = 12;
inline void OptionFxQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
inline void OptionFxQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
inline void OptionFxQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
inline ::std::string* OptionFxQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}

// optional string TradeDate = 13;
inline void OptionFxQuote::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
inline void OptionFxQuote::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
inline void OptionFxQuote::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
inline ::std::string* OptionFxQuote::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}

// optional string MakerInstitutionBuy2 = 14;
inline void OptionFxQuote::clear_makerinstitutionbuy2() {
  makerinstitutionbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionbuy2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  return makerinstitutionbuy2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionbuy2(const ::std::string& value) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
inline void OptionFxQuote::set_makerinstitutionbuy2(const char* value) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
inline void OptionFxQuote::set_makerinstitutionbuy2(const char* value, size_t size) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionbuy2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  return makerinstitutionbuy2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionbuy2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  
  return makerinstitutionbuy2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionbuy2(::std::string* makerinstitutionbuy2) {
  if (makerinstitutionbuy2 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}

// optional string MakerInstitutionSell2 = 15;
inline void OptionFxQuote::clear_makerinstitutionsell2() {
  makerinstitutionsell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionsell2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  return makerinstitutionsell2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionsell2(const ::std::string& value) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
inline void OptionFxQuote::set_makerinstitutionsell2(const char* value) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
inline void OptionFxQuote::set_makerinstitutionsell2(const char* value, size_t size) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionsell2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  return makerinstitutionsell2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionsell2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  
  return makerinstitutionsell2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionsell2(::std::string* makerinstitutionsell2) {
  if (makerinstitutionsell2 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}

// optional string MakerInstitutionBuy3 = 16;
inline void OptionFxQuote::clear_makerinstitutionbuy3() {
  makerinstitutionbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionbuy3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  return makerinstitutionbuy3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionbuy3(const ::std::string& value) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
inline void OptionFxQuote::set_makerinstitutionbuy3(const char* value) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
inline void OptionFxQuote::set_makerinstitutionbuy3(const char* value, size_t size) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionbuy3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  return makerinstitutionbuy3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionbuy3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  
  return makerinstitutionbuy3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionbuy3(::std::string* makerinstitutionbuy3) {
  if (makerinstitutionbuy3 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}

// optional string MakerInstitutionSell3 = 17;
inline void OptionFxQuote::clear_makerinstitutionsell3() {
  makerinstitutionsell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionsell3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  return makerinstitutionsell3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionsell3(const ::std::string& value) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
inline void OptionFxQuote::set_makerinstitutionsell3(const char* value) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
inline void OptionFxQuote::set_makerinstitutionsell3(const char* value, size_t size) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionsell3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  return makerinstitutionsell3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionsell3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  
  return makerinstitutionsell3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionsell3(::std::string* makerinstitutionsell3) {
  if (makerinstitutionsell3 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}

// optional string MakerInstitutionBuy4 = 18;
inline void OptionFxQuote::clear_makerinstitutionbuy4() {
  makerinstitutionbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionbuy4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  return makerinstitutionbuy4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionbuy4(const ::std::string& value) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
inline void OptionFxQuote::set_makerinstitutionbuy4(const char* value) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
inline void OptionFxQuote::set_makerinstitutionbuy4(const char* value, size_t size) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionbuy4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  return makerinstitutionbuy4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionbuy4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  
  return makerinstitutionbuy4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionbuy4(::std::string* makerinstitutionbuy4) {
  if (makerinstitutionbuy4 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}

// optional string MakerInstitutionSell4 = 19;
inline void OptionFxQuote::clear_makerinstitutionsell4() {
  makerinstitutionsell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionsell4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  return makerinstitutionsell4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionsell4(const ::std::string& value) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
inline void OptionFxQuote::set_makerinstitutionsell4(const char* value) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
inline void OptionFxQuote::set_makerinstitutionsell4(const char* value, size_t size) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionsell4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  return makerinstitutionsell4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionsell4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  
  return makerinstitutionsell4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionsell4(::std::string* makerinstitutionsell4) {
  if (makerinstitutionsell4 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}

// optional string MakerInstitutionBuy5 = 20;
inline void OptionFxQuote::clear_makerinstitutionbuy5() {
  makerinstitutionbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionbuy5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  return makerinstitutionbuy5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionbuy5(const ::std::string& value) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
inline void OptionFxQuote::set_makerinstitutionbuy5(const char* value) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
inline void OptionFxQuote::set_makerinstitutionbuy5(const char* value, size_t size) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionbuy5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  return makerinstitutionbuy5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionbuy5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  
  return makerinstitutionbuy5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionbuy5(::std::string* makerinstitutionbuy5) {
  if (makerinstitutionbuy5 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}

// optional string MakerInstitutionSell5 = 21;
inline void OptionFxQuote::clear_makerinstitutionsell5() {
  makerinstitutionsell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& OptionFxQuote::makerinstitutionsell5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  return makerinstitutionsell5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_makerinstitutionsell5(const ::std::string& value) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
inline void OptionFxQuote::set_makerinstitutionsell5(const char* value) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
inline void OptionFxQuote::set_makerinstitutionsell5(const char* value, size_t size) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
inline ::std::string* OptionFxQuote::mutable_makerinstitutionsell5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  return makerinstitutionsell5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* OptionFxQuote::release_makerinstitutionsell5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  
  return makerinstitutionsell5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void OptionFxQuote::set_allocated_makerinstitutionsell5(::std::string* makerinstitutionsell5) {
  if (makerinstitutionsell5 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}

inline const OptionFxQuote* OptionFxQuote::internal_default_instance() {
  return &OptionFxQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsFxQuote_2eproto__INCLUDED
