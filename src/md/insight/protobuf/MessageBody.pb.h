// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MessageBody.proto

#ifndef PROTOBUF_MessageBody_2eproto__INCLUDED
#define PROTOBUF_MessageBody_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "EMessageType.pb.h"
#include "Login.pb.h"
#include "ServiceDiscovery.pb.h"
#include "MDSubscribe.pb.h"
#include "MarketData.pb.h"
#include "InsightErrorContext.pb.h"
#include "MDQuery.pb.h"
#include "MDPlayback.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MessageBody_2eproto();
void protobuf_InitDefaults_MessageBody_2eproto();
void protobuf_AssignDesc_MessageBody_2eproto();
void protobuf_ShutdownFile_MessageBody_2eproto();

class MessageBody;

// ===================================================================

class MessageBody : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MessageBody) */ {
 public:
  MessageBody();
  virtual ~MessageBody();

  MessageBody(const MessageBody& from);

  inline MessageBody& operator=(const MessageBody& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MessageBody& default_instance();

  static const MessageBody* internal_default_instance();

  void Swap(MessageBody* other);

  // implements Message ----------------------------------------------

  inline MessageBody* New() const { return New(NULL); }

  MessageBody* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MessageBody& from);
  void MergeFrom(const MessageBody& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MessageBody* other);
  void UnsafeMergeFrom(const MessageBody& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .com.htsc.mdc.insight.model.EMessageType type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  ::com::htsc::mdc::insight::model::EMessageType type() const;
  void set_type(::com::htsc::mdc::insight::model::EMessageType value);

  // optional int64 interactionId = 2;
  void clear_interactionid();
  static const int kInteractionIdFieldNumber = 2;
  ::google::protobuf::int64 interactionid() const;
  void set_interactionid(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
  bool has_generalerrormessage() const;
  void clear_generalerrormessage();
  static const int kGeneralErrorMessageFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::InsightErrorContext& generalerrormessage() const;
  ::com::htsc::mdc::insight::model::InsightErrorContext* mutable_generalerrormessage();
  ::com::htsc::mdc::insight::model::InsightErrorContext* release_generalerrormessage();
  void set_allocated_generalerrormessage(::com::htsc::mdc::insight::model::InsightErrorContext* generalerrormessage);

  // optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
  bool has_loginrequest() const;
  void clear_loginrequest();
  static const int kLoginRequestFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::LoginRequest& loginrequest() const;
  ::com::htsc::mdc::insight::model::LoginRequest* mutable_loginrequest();
  ::com::htsc::mdc::insight::model::LoginRequest* release_loginrequest();
  void set_allocated_loginrequest(::com::htsc::mdc::insight::model::LoginRequest* loginrequest);

  // optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
  bool has_loginresponse() const;
  void clear_loginresponse();
  static const int kLoginResponseFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::LoginResponse& loginresponse() const;
  ::com::htsc::mdc::insight::model::LoginResponse* mutable_loginresponse();
  ::com::htsc::mdc::insight::model::LoginResponse* release_loginresponse();
  void set_allocated_loginresponse(::com::htsc::mdc::insight::model::LoginResponse* loginresponse);

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
  bool has_servicediscoveryrequest() const;
  void clear_servicediscoveryrequest();
  static const int kServiceDiscoveryRequestFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest& servicediscoveryrequest() const;
  ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* mutable_servicediscoveryrequest();
  ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* release_servicediscoveryrequest();
  void set_allocated_servicediscoveryrequest(::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* servicediscoveryrequest);

  // optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
  bool has_servicediscoveryresponse() const;
  void clear_servicediscoveryresponse();
  static const int kServiceDiscoveryResponseFieldNumber = 14;
  const ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse& servicediscoveryresponse() const;
  ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* mutable_servicediscoveryresponse();
  ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* release_servicediscoveryresponse();
  void set_allocated_servicediscoveryresponse(::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* servicediscoveryresponse);

  // optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
  bool has_mdsubscriberequest() const;
  void clear_mdsubscriberequest();
  static const int kMdSubscribeRequestFieldNumber = 15;
  const ::com::htsc::mdc::insight::model::MDSubscribeRequest& mdsubscriberequest() const;
  ::com::htsc::mdc::insight::model::MDSubscribeRequest* mutable_mdsubscriberequest();
  ::com::htsc::mdc::insight::model::MDSubscribeRequest* release_mdsubscriberequest();
  void set_allocated_mdsubscriberequest(::com::htsc::mdc::insight::model::MDSubscribeRequest* mdsubscriberequest);

  // optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
  bool has_mdsubscriberesponse() const;
  void clear_mdsubscriberesponse();
  static const int kMdSubscribeResponseFieldNumber = 16;
  const ::com::htsc::mdc::insight::model::MDSubscribeResponse& mdsubscriberesponse() const;
  ::com::htsc::mdc::insight::model::MDSubscribeResponse* mutable_mdsubscriberesponse();
  ::com::htsc::mdc::insight::model::MDSubscribeResponse* release_mdsubscriberesponse();
  void set_allocated_mdsubscriberesponse(::com::htsc::mdc::insight::model::MDSubscribeResponse* mdsubscriberesponse);

  // optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
  bool has_pushmarketdata() const;
  void clear_pushmarketdata();
  static const int kPushMarketDataFieldNumber = 20;
  const ::com::htsc::mdc::insight::model::MarketData& pushmarketdata() const;
  ::com::htsc::mdc::insight::model::MarketData* mutable_pushmarketdata();
  ::com::htsc::mdc::insight::model::MarketData* release_pushmarketdata();
  void set_allocated_pushmarketdata(::com::htsc::mdc::insight::model::MarketData* pushmarketdata);

  // optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
  bool has_pushmarketdatastream() const;
  void clear_pushmarketdatastream();
  static const int kPushMarketDataStreamFieldNumber = 21;
  const ::com::htsc::mdc::insight::model::MarketDataStream& pushmarketdatastream() const;
  ::com::htsc::mdc::insight::model::MarketDataStream* mutable_pushmarketdatastream();
  ::com::htsc::mdc::insight::model::MarketDataStream* release_pushmarketdatastream();
  void set_allocated_pushmarketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* pushmarketdatastream);

  // optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
  bool has_mdqueryrequest() const;
  void clear_mdqueryrequest();
  static const int kMdQueryRequestFieldNumber = 30;
  const ::com::htsc::mdc::insight::model::MDQueryRequest& mdqueryrequest() const;
  ::com::htsc::mdc::insight::model::MDQueryRequest* mutable_mdqueryrequest();
  ::com::htsc::mdc::insight::model::MDQueryRequest* release_mdqueryrequest();
  void set_allocated_mdqueryrequest(::com::htsc::mdc::insight::model::MDQueryRequest* mdqueryrequest);

  // optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
  bool has_mdqueryresponse() const;
  void clear_mdqueryresponse();
  static const int kMdQueryResponseFieldNumber = 31;
  const ::com::htsc::mdc::insight::model::MDQueryResponse& mdqueryresponse() const;
  ::com::htsc::mdc::insight::model::MDQueryResponse* mutable_mdqueryresponse();
  ::com::htsc::mdc::insight::model::MDQueryResponse* release_mdqueryresponse();
  void set_allocated_mdqueryresponse(::com::htsc::mdc::insight::model::MDQueryResponse* mdqueryresponse);

  // optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
  bool has_playbackrequest() const;
  void clear_playbackrequest();
  static const int kPlaybackRequestFieldNumber = 32;
  const ::com::htsc::mdc::insight::model::PlaybackRequest& playbackrequest() const;
  ::com::htsc::mdc::insight::model::PlaybackRequest* mutable_playbackrequest();
  ::com::htsc::mdc::insight::model::PlaybackRequest* release_playbackrequest();
  void set_allocated_playbackrequest(::com::htsc::mdc::insight::model::PlaybackRequest* playbackrequest);

  // optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
  bool has_playbackresponse() const;
  void clear_playbackresponse();
  static const int kPlaybackResponseFieldNumber = 33;
  const ::com::htsc::mdc::insight::model::PlaybackResponse& playbackresponse() const;
  ::com::htsc::mdc::insight::model::PlaybackResponse* mutable_playbackresponse();
  ::com::htsc::mdc::insight::model::PlaybackResponse* release_playbackresponse();
  void set_allocated_playbackresponse(::com::htsc::mdc::insight::model::PlaybackResponse* playbackresponse);

  // optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
  bool has_playbackcontrolrequest() const;
  void clear_playbackcontrolrequest();
  static const int kPlaybackControlRequestFieldNumber = 34;
  const ::com::htsc::mdc::insight::model::PlaybackControlRequest& playbackcontrolrequest() const;
  ::com::htsc::mdc::insight::model::PlaybackControlRequest* mutable_playbackcontrolrequest();
  ::com::htsc::mdc::insight::model::PlaybackControlRequest* release_playbackcontrolrequest();
  void set_allocated_playbackcontrolrequest(::com::htsc::mdc::insight::model::PlaybackControlRequest* playbackcontrolrequest);

  // optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
  bool has_playbackcontrolresponse() const;
  void clear_playbackcontrolresponse();
  static const int kPlaybackControlResponseFieldNumber = 35;
  const ::com::htsc::mdc::insight::model::PlaybackControlResponse& playbackcontrolresponse() const;
  ::com::htsc::mdc::insight::model::PlaybackControlResponse* mutable_playbackcontrolresponse();
  ::com::htsc::mdc::insight::model::PlaybackControlResponse* release_playbackcontrolresponse();
  void set_allocated_playbackcontrolresponse(::com::htsc::mdc::insight::model::PlaybackControlResponse* playbackcontrolresponse);

  // optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
  bool has_playbackstatusrequest() const;
  void clear_playbackstatusrequest();
  static const int kPlaybackStatusRequestFieldNumber = 36;
  const ::com::htsc::mdc::insight::model::PlaybackStatusRequest& playbackstatusrequest() const;
  ::com::htsc::mdc::insight::model::PlaybackStatusRequest* mutable_playbackstatusrequest();
  ::com::htsc::mdc::insight::model::PlaybackStatusRequest* release_playbackstatusrequest();
  void set_allocated_playbackstatusrequest(::com::htsc::mdc::insight::model::PlaybackStatusRequest* playbackstatusrequest);

  // optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
  bool has_playbackstatus() const;
  void clear_playbackstatus();
  static const int kPlaybackStatusFieldNumber = 37;
  const ::com::htsc::mdc::insight::model::PlaybackStatus& playbackstatus() const;
  ::com::htsc::mdc::insight::model::PlaybackStatus* mutable_playbackstatus();
  ::com::htsc::mdc::insight::model::PlaybackStatus* release_playbackstatus();
  void set_allocated_playbackstatus(::com::htsc::mdc::insight::model::PlaybackStatus* playbackstatus);

  // optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
  bool has_playbackpayload() const;
  void clear_playbackpayload();
  static const int kPlaybackPayloadFieldNumber = 38;
  const ::com::htsc::mdc::insight::model::PlaybackPayload& playbackpayload() const;
  ::com::htsc::mdc::insight::model::PlaybackPayload* mutable_playbackpayload();
  ::com::htsc::mdc::insight::model::PlaybackPayload* release_playbackpayload();
  void set_allocated_playbackpayload(::com::htsc::mdc::insight::model::PlaybackPayload* playbackpayload);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MessageBody)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::com::htsc::mdc::insight::model::InsightErrorContext* generalerrormessage_;
  ::com::htsc::mdc::insight::model::LoginRequest* loginrequest_;
  ::com::htsc::mdc::insight::model::LoginResponse* loginresponse_;
  ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* servicediscoveryrequest_;
  ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* servicediscoveryresponse_;
  ::com::htsc::mdc::insight::model::MDSubscribeRequest* mdsubscriberequest_;
  ::com::htsc::mdc::insight::model::MDSubscribeResponse* mdsubscriberesponse_;
  ::com::htsc::mdc::insight::model::MarketData* pushmarketdata_;
  ::com::htsc::mdc::insight::model::MarketDataStream* pushmarketdatastream_;
  ::com::htsc::mdc::insight::model::MDQueryRequest* mdqueryrequest_;
  ::com::htsc::mdc::insight::model::MDQueryResponse* mdqueryresponse_;
  ::com::htsc::mdc::insight::model::PlaybackRequest* playbackrequest_;
  ::com::htsc::mdc::insight::model::PlaybackResponse* playbackresponse_;
  ::com::htsc::mdc::insight::model::PlaybackControlRequest* playbackcontrolrequest_;
  ::com::htsc::mdc::insight::model::PlaybackControlResponse* playbackcontrolresponse_;
  ::com::htsc::mdc::insight::model::PlaybackStatusRequest* playbackstatusrequest_;
  ::com::htsc::mdc::insight::model::PlaybackStatus* playbackstatus_;
  ::com::htsc::mdc::insight::model::PlaybackPayload* playbackpayload_;
  ::google::protobuf::int64 interactionid_;
  int type_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MessageBody_2eproto_impl();
  friend void  protobuf_AddDesc_MessageBody_2eproto_impl();
  friend void protobuf_AssignDesc_MessageBody_2eproto();
  friend void protobuf_ShutdownFile_MessageBody_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MessageBody> MessageBody_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MessageBody

// optional .com.htsc.mdc.insight.model.EMessageType type = 1;
inline void MessageBody::clear_type() {
  type_ = 0;
}
inline ::com::htsc::mdc::insight::model::EMessageType MessageBody::type() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.type)
  return static_cast< ::com::htsc::mdc::insight::model::EMessageType >(type_);
}
inline void MessageBody::set_type(::com::htsc::mdc::insight::model::EMessageType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageBody.type)
}

// optional int64 interactionId = 2;
inline void MessageBody::clear_interactionid() {
  interactionid_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MessageBody::interactionid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.interactionId)
  return interactionid_;
}
inline void MessageBody::set_interactionid(::google::protobuf::int64 value) {
  
  interactionid_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MessageBody.interactionId)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext generalErrorMessage = 10;
inline bool MessageBody::has_generalerrormessage() const {
  return this != internal_default_instance() && generalerrormessage_ != NULL;
}
inline void MessageBody::clear_generalerrormessage() {
  if (GetArenaNoVirtual() == NULL && generalerrormessage_ != NULL) delete generalerrormessage_;
  generalerrormessage_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::InsightErrorContext& MessageBody::generalerrormessage() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  return generalerrormessage_ != NULL ? *generalerrormessage_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MessageBody::mutable_generalerrormessage() {
  
  if (generalerrormessage_ == NULL) {
    generalerrormessage_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  return generalerrormessage_;
}
inline ::com::htsc::mdc::insight::model::InsightErrorContext* MessageBody::release_generalerrormessage() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = generalerrormessage_;
  generalerrormessage_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_generalerrormessage(::com::htsc::mdc::insight::model::InsightErrorContext* generalerrormessage) {
  delete generalerrormessage_;
  generalerrormessage_ = generalerrormessage;
  if (generalerrormessage) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.generalErrorMessage)
}

// optional .com.htsc.mdc.insight.model.LoginRequest loginRequest = 11;
inline bool MessageBody::has_loginrequest() const {
  return this != internal_default_instance() && loginrequest_ != NULL;
}
inline void MessageBody::clear_loginrequest() {
  if (GetArenaNoVirtual() == NULL && loginrequest_ != NULL) delete loginrequest_;
  loginrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::LoginRequest& MessageBody::loginrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  return loginrequest_ != NULL ? *loginrequest_
                         : *::com::htsc::mdc::insight::model::LoginRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::LoginRequest* MessageBody::mutable_loginrequest() {
  
  if (loginrequest_ == NULL) {
    loginrequest_ = new ::com::htsc::mdc::insight::model::LoginRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  return loginrequest_;
}
inline ::com::htsc::mdc::insight::model::LoginRequest* MessageBody::release_loginrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.loginRequest)
  
  ::com::htsc::mdc::insight::model::LoginRequest* temp = loginrequest_;
  loginrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_loginrequest(::com::htsc::mdc::insight::model::LoginRequest* loginrequest) {
  delete loginrequest_;
  loginrequest_ = loginrequest;
  if (loginrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.loginRequest)
}

// optional .com.htsc.mdc.insight.model.LoginResponse loginResponse = 12;
inline bool MessageBody::has_loginresponse() const {
  return this != internal_default_instance() && loginresponse_ != NULL;
}
inline void MessageBody::clear_loginresponse() {
  if (GetArenaNoVirtual() == NULL && loginresponse_ != NULL) delete loginresponse_;
  loginresponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::LoginResponse& MessageBody::loginresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  return loginresponse_ != NULL ? *loginresponse_
                         : *::com::htsc::mdc::insight::model::LoginResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::LoginResponse* MessageBody::mutable_loginresponse() {
  
  if (loginresponse_ == NULL) {
    loginresponse_ = new ::com::htsc::mdc::insight::model::LoginResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  return loginresponse_;
}
inline ::com::htsc::mdc::insight::model::LoginResponse* MessageBody::release_loginresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.loginResponse)
  
  ::com::htsc::mdc::insight::model::LoginResponse* temp = loginresponse_;
  loginresponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_loginresponse(::com::htsc::mdc::insight::model::LoginResponse* loginresponse) {
  delete loginresponse_;
  loginresponse_ = loginresponse;
  if (loginresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.loginResponse)
}

// optional .com.htsc.mdc.insight.model.ServiceDiscoveryRequest serviceDiscoveryRequest = 13;
inline bool MessageBody::has_servicediscoveryrequest() const {
  return this != internal_default_instance() && servicediscoveryrequest_ != NULL;
}
inline void MessageBody::clear_servicediscoveryrequest() {
  if (GetArenaNoVirtual() == NULL && servicediscoveryrequest_ != NULL) delete servicediscoveryrequest_;
  servicediscoveryrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest& MessageBody::servicediscoveryrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  return servicediscoveryrequest_ != NULL ? *servicediscoveryrequest_
                         : *::com::htsc::mdc::insight::model::ServiceDiscoveryRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* MessageBody::mutable_servicediscoveryrequest() {
  
  if (servicediscoveryrequest_ == NULL) {
    servicediscoveryrequest_ = new ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  return servicediscoveryrequest_;
}
inline ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* MessageBody::release_servicediscoveryrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
  
  ::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* temp = servicediscoveryrequest_;
  servicediscoveryrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_servicediscoveryrequest(::com::htsc::mdc::insight::model::ServiceDiscoveryRequest* servicediscoveryrequest) {
  delete servicediscoveryrequest_;
  servicediscoveryrequest_ = servicediscoveryrequest;
  if (servicediscoveryrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryRequest)
}

// optional .com.htsc.mdc.insight.model.ServiceDiscoveryResponse serviceDiscoveryResponse = 14;
inline bool MessageBody::has_servicediscoveryresponse() const {
  return this != internal_default_instance() && servicediscoveryresponse_ != NULL;
}
inline void MessageBody::clear_servicediscoveryresponse() {
  if (GetArenaNoVirtual() == NULL && servicediscoveryresponse_ != NULL) delete servicediscoveryresponse_;
  servicediscoveryresponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse& MessageBody::servicediscoveryresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  return servicediscoveryresponse_ != NULL ? *servicediscoveryresponse_
                         : *::com::htsc::mdc::insight::model::ServiceDiscoveryResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* MessageBody::mutable_servicediscoveryresponse() {
  
  if (servicediscoveryresponse_ == NULL) {
    servicediscoveryresponse_ = new ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  return servicediscoveryresponse_;
}
inline ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* MessageBody::release_servicediscoveryresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
  
  ::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* temp = servicediscoveryresponse_;
  servicediscoveryresponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_servicediscoveryresponse(::com::htsc::mdc::insight::model::ServiceDiscoveryResponse* servicediscoveryresponse) {
  delete servicediscoveryresponse_;
  servicediscoveryresponse_ = servicediscoveryresponse;
  if (servicediscoveryresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.serviceDiscoveryResponse)
}

// optional .com.htsc.mdc.insight.model.MDSubscribeRequest mdSubscribeRequest = 15;
inline bool MessageBody::has_mdsubscriberequest() const {
  return this != internal_default_instance() && mdsubscriberequest_ != NULL;
}
inline void MessageBody::clear_mdsubscriberequest() {
  if (GetArenaNoVirtual() == NULL && mdsubscriberequest_ != NULL) delete mdsubscriberequest_;
  mdsubscriberequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSubscribeRequest& MessageBody::mdsubscriberequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  return mdsubscriberequest_ != NULL ? *mdsubscriberequest_
                         : *::com::htsc::mdc::insight::model::MDSubscribeRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSubscribeRequest* MessageBody::mutable_mdsubscriberequest() {
  
  if (mdsubscriberequest_ == NULL) {
    mdsubscriberequest_ = new ::com::htsc::mdc::insight::model::MDSubscribeRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  return mdsubscriberequest_;
}
inline ::com::htsc::mdc::insight::model::MDSubscribeRequest* MessageBody::release_mdsubscriberequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
  
  ::com::htsc::mdc::insight::model::MDSubscribeRequest* temp = mdsubscriberequest_;
  mdsubscriberequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_mdsubscriberequest(::com::htsc::mdc::insight::model::MDSubscribeRequest* mdsubscriberequest) {
  delete mdsubscriberequest_;
  mdsubscriberequest_ = mdsubscriberequest;
  if (mdsubscriberequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdSubscribeRequest)
}

// optional .com.htsc.mdc.insight.model.MDSubscribeResponse mdSubscribeResponse = 16;
inline bool MessageBody::has_mdsubscriberesponse() const {
  return this != internal_default_instance() && mdsubscriberesponse_ != NULL;
}
inline void MessageBody::clear_mdsubscriberesponse() {
  if (GetArenaNoVirtual() == NULL && mdsubscriberesponse_ != NULL) delete mdsubscriberesponse_;
  mdsubscriberesponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSubscribeResponse& MessageBody::mdsubscriberesponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  return mdsubscriberesponse_ != NULL ? *mdsubscriberesponse_
                         : *::com::htsc::mdc::insight::model::MDSubscribeResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSubscribeResponse* MessageBody::mutable_mdsubscriberesponse() {
  
  if (mdsubscriberesponse_ == NULL) {
    mdsubscriberesponse_ = new ::com::htsc::mdc::insight::model::MDSubscribeResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  return mdsubscriberesponse_;
}
inline ::com::htsc::mdc::insight::model::MDSubscribeResponse* MessageBody::release_mdsubscriberesponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
  
  ::com::htsc::mdc::insight::model::MDSubscribeResponse* temp = mdsubscriberesponse_;
  mdsubscriberesponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_mdsubscriberesponse(::com::htsc::mdc::insight::model::MDSubscribeResponse* mdsubscriberesponse) {
  delete mdsubscriberesponse_;
  mdsubscriberesponse_ = mdsubscriberesponse;
  if (mdsubscriberesponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdSubscribeResponse)
}

// optional .com.htsc.mdc.insight.model.MarketData pushMarketData = 20;
inline bool MessageBody::has_pushmarketdata() const {
  return this != internal_default_instance() && pushmarketdata_ != NULL;
}
inline void MessageBody::clear_pushmarketdata() {
  if (GetArenaNoVirtual() == NULL && pushmarketdata_ != NULL) delete pushmarketdata_;
  pushmarketdata_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MarketData& MessageBody::pushmarketdata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  return pushmarketdata_ != NULL ? *pushmarketdata_
                         : *::com::htsc::mdc::insight::model::MarketData::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MarketData* MessageBody::mutable_pushmarketdata() {
  
  if (pushmarketdata_ == NULL) {
    pushmarketdata_ = new ::com::htsc::mdc::insight::model::MarketData;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  return pushmarketdata_;
}
inline ::com::htsc::mdc::insight::model::MarketData* MessageBody::release_pushmarketdata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
  
  ::com::htsc::mdc::insight::model::MarketData* temp = pushmarketdata_;
  pushmarketdata_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_pushmarketdata(::com::htsc::mdc::insight::model::MarketData* pushmarketdata) {
  delete pushmarketdata_;
  pushmarketdata_ = pushmarketdata;
  if (pushmarketdata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.pushMarketData)
}

// optional .com.htsc.mdc.insight.model.MarketDataStream pushMarketDataStream = 21;
inline bool MessageBody::has_pushmarketdatastream() const {
  return this != internal_default_instance() && pushmarketdatastream_ != NULL;
}
inline void MessageBody::clear_pushmarketdatastream() {
  if (GetArenaNoVirtual() == NULL && pushmarketdatastream_ != NULL) delete pushmarketdatastream_;
  pushmarketdatastream_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MarketDataStream& MessageBody::pushmarketdatastream() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  return pushmarketdatastream_ != NULL ? *pushmarketdatastream_
                         : *::com::htsc::mdc::insight::model::MarketDataStream::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* MessageBody::mutable_pushmarketdatastream() {
  
  if (pushmarketdatastream_ == NULL) {
    pushmarketdatastream_ = new ::com::htsc::mdc::insight::model::MarketDataStream;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  return pushmarketdatastream_;
}
inline ::com::htsc::mdc::insight::model::MarketDataStream* MessageBody::release_pushmarketdatastream() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
  
  ::com::htsc::mdc::insight::model::MarketDataStream* temp = pushmarketdatastream_;
  pushmarketdatastream_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_pushmarketdatastream(::com::htsc::mdc::insight::model::MarketDataStream* pushmarketdatastream) {
  delete pushmarketdatastream_;
  pushmarketdatastream_ = pushmarketdatastream;
  if (pushmarketdatastream) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.pushMarketDataStream)
}

// optional .com.htsc.mdc.insight.model.MDQueryRequest mdQueryRequest = 30;
inline bool MessageBody::has_mdqueryrequest() const {
  return this != internal_default_instance() && mdqueryrequest_ != NULL;
}
inline void MessageBody::clear_mdqueryrequest() {
  if (GetArenaNoVirtual() == NULL && mdqueryrequest_ != NULL) delete mdqueryrequest_;
  mdqueryrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDQueryRequest& MessageBody::mdqueryrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  return mdqueryrequest_ != NULL ? *mdqueryrequest_
                         : *::com::htsc::mdc::insight::model::MDQueryRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDQueryRequest* MessageBody::mutable_mdqueryrequest() {
  
  if (mdqueryrequest_ == NULL) {
    mdqueryrequest_ = new ::com::htsc::mdc::insight::model::MDQueryRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  return mdqueryrequest_;
}
inline ::com::htsc::mdc::insight::model::MDQueryRequest* MessageBody::release_mdqueryrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
  
  ::com::htsc::mdc::insight::model::MDQueryRequest* temp = mdqueryrequest_;
  mdqueryrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_mdqueryrequest(::com::htsc::mdc::insight::model::MDQueryRequest* mdqueryrequest) {
  delete mdqueryrequest_;
  mdqueryrequest_ = mdqueryrequest;
  if (mdqueryrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdQueryRequest)
}

// optional .com.htsc.mdc.insight.model.MDQueryResponse mdQueryResponse = 31;
inline bool MessageBody::has_mdqueryresponse() const {
  return this != internal_default_instance() && mdqueryresponse_ != NULL;
}
inline void MessageBody::clear_mdqueryresponse() {
  if (GetArenaNoVirtual() == NULL && mdqueryresponse_ != NULL) delete mdqueryresponse_;
  mdqueryresponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDQueryResponse& MessageBody::mdqueryresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  return mdqueryresponse_ != NULL ? *mdqueryresponse_
                         : *::com::htsc::mdc::insight::model::MDQueryResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDQueryResponse* MessageBody::mutable_mdqueryresponse() {
  
  if (mdqueryresponse_ == NULL) {
    mdqueryresponse_ = new ::com::htsc::mdc::insight::model::MDQueryResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  return mdqueryresponse_;
}
inline ::com::htsc::mdc::insight::model::MDQueryResponse* MessageBody::release_mdqueryresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
  
  ::com::htsc::mdc::insight::model::MDQueryResponse* temp = mdqueryresponse_;
  mdqueryresponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_mdqueryresponse(::com::htsc::mdc::insight::model::MDQueryResponse* mdqueryresponse) {
  delete mdqueryresponse_;
  mdqueryresponse_ = mdqueryresponse;
  if (mdqueryresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.mdQueryResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackRequest playbackRequest = 32;
inline bool MessageBody::has_playbackrequest() const {
  return this != internal_default_instance() && playbackrequest_ != NULL;
}
inline void MessageBody::clear_playbackrequest() {
  if (GetArenaNoVirtual() == NULL && playbackrequest_ != NULL) delete playbackrequest_;
  playbackrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackRequest& MessageBody::playbackrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  return playbackrequest_ != NULL ? *playbackrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackRequest* MessageBody::mutable_playbackrequest() {
  
  if (playbackrequest_ == NULL) {
    playbackrequest_ = new ::com::htsc::mdc::insight::model::PlaybackRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  return playbackrequest_;
}
inline ::com::htsc::mdc::insight::model::PlaybackRequest* MessageBody::release_playbackrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackRequest* temp = playbackrequest_;
  playbackrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackrequest(::com::htsc::mdc::insight::model::PlaybackRequest* playbackrequest) {
  delete playbackrequest_;
  playbackrequest_ = playbackrequest;
  if (playbackrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackResponse playbackResponse = 33;
inline bool MessageBody::has_playbackresponse() const {
  return this != internal_default_instance() && playbackresponse_ != NULL;
}
inline void MessageBody::clear_playbackresponse() {
  if (GetArenaNoVirtual() == NULL && playbackresponse_ != NULL) delete playbackresponse_;
  playbackresponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackResponse& MessageBody::playbackresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  return playbackresponse_ != NULL ? *playbackresponse_
                         : *::com::htsc::mdc::insight::model::PlaybackResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackResponse* MessageBody::mutable_playbackresponse() {
  
  if (playbackresponse_ == NULL) {
    playbackresponse_ = new ::com::htsc::mdc::insight::model::PlaybackResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  return playbackresponse_;
}
inline ::com::htsc::mdc::insight::model::PlaybackResponse* MessageBody::release_playbackresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
  
  ::com::htsc::mdc::insight::model::PlaybackResponse* temp = playbackresponse_;
  playbackresponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackresponse(::com::htsc::mdc::insight::model::PlaybackResponse* playbackresponse) {
  delete playbackresponse_;
  playbackresponse_ = playbackresponse;
  if (playbackresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackControlRequest playbackControlRequest = 34;
inline bool MessageBody::has_playbackcontrolrequest() const {
  return this != internal_default_instance() && playbackcontrolrequest_ != NULL;
}
inline void MessageBody::clear_playbackcontrolrequest() {
  if (GetArenaNoVirtual() == NULL && playbackcontrolrequest_ != NULL) delete playbackcontrolrequest_;
  playbackcontrolrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackControlRequest& MessageBody::playbackcontrolrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  return playbackcontrolrequest_ != NULL ? *playbackcontrolrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackControlRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackControlRequest* MessageBody::mutable_playbackcontrolrequest() {
  
  if (playbackcontrolrequest_ == NULL) {
    playbackcontrolrequest_ = new ::com::htsc::mdc::insight::model::PlaybackControlRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  return playbackcontrolrequest_;
}
inline ::com::htsc::mdc::insight::model::PlaybackControlRequest* MessageBody::release_playbackcontrolrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackControlRequest* temp = playbackcontrolrequest_;
  playbackcontrolrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackcontrolrequest(::com::htsc::mdc::insight::model::PlaybackControlRequest* playbackcontrolrequest) {
  delete playbackcontrolrequest_;
  playbackcontrolrequest_ = playbackcontrolrequest;
  if (playbackcontrolrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackControlRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackControlResponse playbackControlResponse = 35;
inline bool MessageBody::has_playbackcontrolresponse() const {
  return this != internal_default_instance() && playbackcontrolresponse_ != NULL;
}
inline void MessageBody::clear_playbackcontrolresponse() {
  if (GetArenaNoVirtual() == NULL && playbackcontrolresponse_ != NULL) delete playbackcontrolresponse_;
  playbackcontrolresponse_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackControlResponse& MessageBody::playbackcontrolresponse() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  return playbackcontrolresponse_ != NULL ? *playbackcontrolresponse_
                         : *::com::htsc::mdc::insight::model::PlaybackControlResponse::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackControlResponse* MessageBody::mutable_playbackcontrolresponse() {
  
  if (playbackcontrolresponse_ == NULL) {
    playbackcontrolresponse_ = new ::com::htsc::mdc::insight::model::PlaybackControlResponse;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  return playbackcontrolresponse_;
}
inline ::com::htsc::mdc::insight::model::PlaybackControlResponse* MessageBody::release_playbackcontrolresponse() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
  
  ::com::htsc::mdc::insight::model::PlaybackControlResponse* temp = playbackcontrolresponse_;
  playbackcontrolresponse_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackcontrolresponse(::com::htsc::mdc::insight::model::PlaybackControlResponse* playbackcontrolresponse) {
  delete playbackcontrolresponse_;
  playbackcontrolresponse_ = playbackcontrolresponse;
  if (playbackcontrolresponse) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackControlResponse)
}

// optional .com.htsc.mdc.insight.model.PlaybackStatusRequest playbackStatusRequest = 36;
inline bool MessageBody::has_playbackstatusrequest() const {
  return this != internal_default_instance() && playbackstatusrequest_ != NULL;
}
inline void MessageBody::clear_playbackstatusrequest() {
  if (GetArenaNoVirtual() == NULL && playbackstatusrequest_ != NULL) delete playbackstatusrequest_;
  playbackstatusrequest_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackStatusRequest& MessageBody::playbackstatusrequest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  return playbackstatusrequest_ != NULL ? *playbackstatusrequest_
                         : *::com::htsc::mdc::insight::model::PlaybackStatusRequest::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackStatusRequest* MessageBody::mutable_playbackstatusrequest() {
  
  if (playbackstatusrequest_ == NULL) {
    playbackstatusrequest_ = new ::com::htsc::mdc::insight::model::PlaybackStatusRequest;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  return playbackstatusrequest_;
}
inline ::com::htsc::mdc::insight::model::PlaybackStatusRequest* MessageBody::release_playbackstatusrequest() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
  
  ::com::htsc::mdc::insight::model::PlaybackStatusRequest* temp = playbackstatusrequest_;
  playbackstatusrequest_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackstatusrequest(::com::htsc::mdc::insight::model::PlaybackStatusRequest* playbackstatusrequest) {
  delete playbackstatusrequest_;
  playbackstatusrequest_ = playbackstatusrequest;
  if (playbackstatusrequest) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackStatusRequest)
}

// optional .com.htsc.mdc.insight.model.PlaybackStatus playbackStatus = 37;
inline bool MessageBody::has_playbackstatus() const {
  return this != internal_default_instance() && playbackstatus_ != NULL;
}
inline void MessageBody::clear_playbackstatus() {
  if (GetArenaNoVirtual() == NULL && playbackstatus_ != NULL) delete playbackstatus_;
  playbackstatus_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackStatus& MessageBody::playbackstatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  return playbackstatus_ != NULL ? *playbackstatus_
                         : *::com::htsc::mdc::insight::model::PlaybackStatus::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackStatus* MessageBody::mutable_playbackstatus() {
  
  if (playbackstatus_ == NULL) {
    playbackstatus_ = new ::com::htsc::mdc::insight::model::PlaybackStatus;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  return playbackstatus_;
}
inline ::com::htsc::mdc::insight::model::PlaybackStatus* MessageBody::release_playbackstatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
  
  ::com::htsc::mdc::insight::model::PlaybackStatus* temp = playbackstatus_;
  playbackstatus_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackstatus(::com::htsc::mdc::insight::model::PlaybackStatus* playbackstatus) {
  delete playbackstatus_;
  playbackstatus_ = playbackstatus;
  if (playbackstatus) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackStatus)
}

// optional .com.htsc.mdc.insight.model.PlaybackPayload playbackPayload = 38;
inline bool MessageBody::has_playbackpayload() const {
  return this != internal_default_instance() && playbackpayload_ != NULL;
}
inline void MessageBody::clear_playbackpayload() {
  if (GetArenaNoVirtual() == NULL && playbackpayload_ != NULL) delete playbackpayload_;
  playbackpayload_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::PlaybackPayload& MessageBody::playbackpayload() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  return playbackpayload_ != NULL ? *playbackpayload_
                         : *::com::htsc::mdc::insight::model::PlaybackPayload::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::PlaybackPayload* MessageBody::mutable_playbackpayload() {
  
  if (playbackpayload_ == NULL) {
    playbackpayload_ = new ::com::htsc::mdc::insight::model::PlaybackPayload;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  return playbackpayload_;
}
inline ::com::htsc::mdc::insight::model::PlaybackPayload* MessageBody::release_playbackpayload() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
  
  ::com::htsc::mdc::insight::model::PlaybackPayload* temp = playbackpayload_;
  playbackpayload_ = NULL;
  return temp;
}
inline void MessageBody::set_allocated_playbackpayload(::com::htsc::mdc::insight::model::PlaybackPayload* playbackpayload) {
  delete playbackpayload_;
  playbackpayload_ = playbackpayload;
  if (playbackpayload) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MessageBody.playbackPayload)
}

inline const MessageBody* MessageBody::internal_default_instance() {
  return &MessageBody_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MessageBody_2eproto__INCLUDED
