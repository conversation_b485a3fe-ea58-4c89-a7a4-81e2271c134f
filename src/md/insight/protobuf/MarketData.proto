syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";
import "EMarketDataType.proto"; // 需要你之前提供的 EMarketDataType.proto

message MarketData {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  com.htsc.mdc.insight.model.EMarketDataType MarketDataType = 16;
  oneof payload {
    MDCfetsRateSnapshot rateSnapshot = 17;
    MDCfetsRateDeal rateDeal = 18;
    MDCfetsQDMQuote qdmQuote = 19;
    MDCfetsODMSnapshot odmSnapshot = 20;
    MDCfetsFxSnapshot fxSnapshot = 21;
    MDCfetsFxQuote fxQuote = 22;
    MDCfetsFxCnyMiddlePrice fxCnyMiddlePrice = 23;
    MDCfetsForex forex = 24;
    MDCfetsCurrencySnapshot currencySnapshot = 25;
    MDCfetsCurrencyDeal currencyDeal = 26;
    MDCfetsBondSnapshot bondSnapshot = 27;
    MDCfetsBondDeal bondDeal = 28;
    MDCfetsBenchmark benchmark = 29;
    MDBond bond = 30;
    MDBasicInfo basicInfo = 31;
  }
  int64 MessageNumber = 100;
}
