syntax = "proto3";

package com.htsc.mdc.insight.model;

enum EMarketDataType {
  UNKNOWN_DATA_TYPE = 0;
  MD_TICK = 1;
  MD_TRANSACTION = 2;
  MD_ORDER = 3;
  MD_CONSTANT = 4;
  DYNAMIC_PACKET = 5;
  MD_ETF_BASICINFO = 6;
  MD_IOPV_SNAPSHOT = 7;
  MD_KLINE_1MIN = 20;
  MD_KLINE_5MIN = 21;
  MD_KLINE_15MIN = 22;
  MD_KLINE_30MIN = 23;
  MD_KLINE_60MIN = 24;
  MD_KLINE_1D = 25;
  MD_KLINE_15S = 26;
  MD_TWAP_1MIN = 30;
  MD_TWAP_1S = 31;
  MD_VWAP_1MIN = 40;
  MD_VWAP_1S = 41;
  MD_SIMPLE_TICK = 50;
  AD_UPSDOWNS_ANALYSIS = 51;
  AD_INDICATORS_RANKING = 52;
  AD_VOLUME_BYPRICE = 53;
  AD_FUND_FLOW_ANALYSIS = 54;
  AD_ORDERBOOK_SNAPSHOT = 55;
  AD_ORDERBOOK_SNAPSHOT_WITH_TICK = 56;
  AD_CHIP_DISTRIBUTION = 57;
  MD_WARRANT = 58;
  MD_SECURITY_LENDING = 59;
  AD_NEWS = 60;
  AD_STARING_RESULT = 61;
  AD_DERIVED_ANALYSIS = 62;
  MD_FI_QUOTE = 70;
  MD_QUOTE = 71;
  MD_QB_QUOTE = 72;
  MD_QB_TRANSACTION = 73;
  MD_SL_ORDER = 74;
  MD_SL_TRANSACTION = 75;
  MD_USA_ORDER = 76;
  MD_USA_TRANSACTION = 77;
  MD_HK_GREY_MARKET = 78;
  MD_SL_INDICATIVE_QUOTE = 79;
  MD_SL_STATISTICS = 80;
  MD_USA_QUOTE = 81;
  MD_SL_ESTIMATION = 82;
  MD_CNEX_QUOTE = 83;
  MD_CNEX_DEAL = 84;
  MD_DELAY_SNAPSHOT = 85;
  MD_HIGH_ACCURACY_TICK = 86;
  MD_CFETS_FOREX = 87;
  MD_CFETS_FOREX_SNAPSHOT = 88;
  MD_CFETS_FOREX_QUOTE = 89;
  MD_CFETS_CNY_CURRENCY_DEAL = 90;
  MD_CFETS_CNY_CURRENCY_SNAPSHOT = 91;
  MD_CFETS_CNY_BOND_DEAL = 92;
  MD_CFETS_CNY_BOND_SNAPSHOT = 93;
  MD_CFETS_CNY_RATE_DEAL = 94;
  MD_CFETS_CNY_RATE_SNAPSHOT = 95;
  MD_CFETS_BENCHMARK = 96;
  MD_CFETS_QDM_QUOTE = 97;
  MD_CFETS_ODM_SNAPSHOT = 98;
  MD_CFETS_FOREX_CNY_MIDDLE_PRICE = 99;
  REPLAY_MD_TICK_WITH_TRANSACTION = 101;
  REPLAY_MD_TICK_WITH_ORDER = 102;
  REPLAY_MD_TICK_WITH_TRANSACTION_AND_ORDER = 103;
  REPLAY_MD_TICK = 104;
  REPLAY_MD_TRANSACTION = 105;
  REPLAY_MD_ORDER = 106;
  REPLAY_MD_TRANSACTION_AND_ORDER = 107;
  MD_CHINABOND_BENCHMARK = 120;
  MD_ICE_TRACE = 121;
}
