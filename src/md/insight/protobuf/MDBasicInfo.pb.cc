// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDBasicInfo.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDBasicInfo.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDBasicInfo_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDBasicInfo_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDBasicInfo_ConstantParam_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDBasicInfo_ConstantParam_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDBasicInfo_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDBasicInfo_2eproto() {
  protobuf_AddDesc_MDBasicInfo_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDBasicInfo.proto");
  GOOGLE_CHECK(file != NULL);
  MDBasicInfo_descriptor_ = file->message_type(0);
  static const int MDBasicInfo_offsets_[148] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, securityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, symbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, chispelling_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, englishname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, securitysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, listdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, currency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, outstandingshare_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, publicfloatsharequantity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, lotsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, shortsellflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, exchangesymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, ticksize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, loanmarginindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, pxaccuracy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, ipoprofitable_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, diffrightsindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, hkspreadtablecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, presettlepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, preiopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, shhkconnect_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, szhkconnect_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncontractid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncontractsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionunderlyingsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionunderlyingsymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionunderlyingtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionoptiontype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncallorput_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncontractmultiplierunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionexerciseprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionstartdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionenddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionexercisedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optiondeliverydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionexpiredate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionupdateversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optiontotallongposition_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsecurityclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsettlprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionunderlyingclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionpricelimittype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optiondailypriceuplimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optiondailypricedownlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarginunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarginratioparam1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarginratioparam2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionroundlot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionlmtordminfloor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionlmtordmaxfloor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmktordminfloor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmktordmaxfloor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionticksize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsecuritystatusflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncarryinterestdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionearlyexpiredate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionstrategysecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fitradeproducttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fisecurityproperty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fisecuritystatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fipledgedsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiopentime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, ficlosetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiissuemode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fifaceamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiissueprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiinteresttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiinterestfrequency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, figuaranteedinterestrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fibaseinterestrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiquotedmargin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fitimelimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fitotalissuance_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiissuestartdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiissueenddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, filistdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiexpiredate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, finationaldebttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fiissuemethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, ficrossmarket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fishortsellflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fitotalshortsellquota_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fidealershortsellquota_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fipreclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, fipreweightedpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionlisttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optiondeliverytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionadjusttimes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncontractposition_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionbuyqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsellqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarketorderbuyqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarketordersellqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionquoteorderbuyqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionquoteordersellqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionbuyqtyunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsellqtyunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionlastsellmargin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionsellmargin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optionmarketmakerflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, optioncombinationstrategy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, deliveryyear_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, deliverymonth_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, instrumentid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, instrumentname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, exchangeinstid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, productid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, maxmarketordervolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, minmarketordervolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, maxlimitordervolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, minlimitordervolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, volumemultiple_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, createdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, expiredate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, startdelivdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, enddelivdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, positiontype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, longmarginratio_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, shortmarginratio_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, maxmarginsidealgorithm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, strikeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, formersymbol_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, delistdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, buyqtyunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, sellqtyunit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, buyqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, sellqtyupperlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, buyqtylowerlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, sellqtylowerlimit_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, vcmflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, casflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, posflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, posupperlimitpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, poslowerlimitpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, basecontractid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, constantparams_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, interestaccrualdate_),
  };
  MDBasicInfo_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDBasicInfo_descriptor_,
      MDBasicInfo::internal_default_instance(),
      MDBasicInfo_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDBasicInfo),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo, _internal_metadata_));
  MDBasicInfo_ConstantParam_descriptor_ = MDBasicInfo_descriptor_->nested_type(0);
  static const int MDBasicInfo_ConstantParam_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo_ConstantParam, paramname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo_ConstantParam, paramvalue_),
  };
  MDBasicInfo_ConstantParam_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDBasicInfo_ConstantParam_descriptor_,
      MDBasicInfo_ConstantParam::internal_default_instance(),
      MDBasicInfo_ConstantParam_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDBasicInfo_ConstantParam),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDBasicInfo_ConstantParam, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDBasicInfo_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDBasicInfo_descriptor_, MDBasicInfo::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDBasicInfo_ConstantParam_descriptor_, MDBasicInfo_ConstantParam::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDBasicInfo_2eproto() {
  MDBasicInfo_default_instance_.Shutdown();
  delete MDBasicInfo_reflection_;
  MDBasicInfo_ConstantParam_default_instance_.Shutdown();
  delete MDBasicInfo_ConstantParam_reflection_;
}

void protobuf_InitDefaults_MDBasicInfo_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDBasicInfo_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDBasicInfo_ConstantParam_default_instance_.DefaultConstruct();
  MDBasicInfo_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDBasicInfo_ConstantParam_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDBasicInfo_2eproto_once_);
void protobuf_InitDefaults_MDBasicInfo_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDBasicInfo_2eproto_once_,
                 &protobuf_InitDefaults_MDBasicInfo_2eproto_impl);
}
void protobuf_AddDesc_MDBasicInfo_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDBasicInfo_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\021MDBasicInfo.proto\022\032com.htsc.mdc.insigh"
    "t.model\032\027ESecurityIDSource.proto\032\023ESecur"
    "ityType.proto\"\372\037\n\013MDBasicInfo\022\026\n\016HTSCSec"
    "urityID\030\001 \001(\t\022\022\n\nSecurityID\030\002 \001(\t\022\016\n\006Sym"
    "bol\030\003 \001(\t\022\023\n\013ChiSpelling\030\004 \001(\t\022\023\n\013Englis"
    "hName\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%."
    "com.htsc.mdc.model.ESecurityIDSource\0227\n\014"
    "securityType\030\007 \001(\0162!.com.htsc.mdc.model."
    "ESecurityType\022\027\n\017SecuritySubType\030\010 \001(\t\022\020"
    "\n\010ListDate\030\t \001(\t\022\020\n\010Currency\030\n \001(\t\022\030\n\020Ou"
    "tstandingShare\030\013 \001(\003\022 \n\030PublicFloatShare"
    "Quantity\030\014 \001(\003\022\016\n\006MDDate\030\r \001(\005\022\030\n\020Tradin"
    "gPhaseCode\030\016 \001(\t\022\022\n\nPreClosePx\030\017 \001(\003\022\r\n\005"
    "MaxPx\030\020 \001(\003\022\r\n\005MinPx\030\021 \001(\003\022\017\n\007LotSize\030\022 "
    "\001(\003\022\025\n\rShortSellFlag\030\023 \001(\010\022\024\n\014ExchangeDa"
    "te\030\024 \001(\t\022\026\n\016ExchangeSymbol\030\025 \001(\t\022\020\n\010Tick"
    "Size\030\026 \001(\001\022\033\n\023LoanMarginIndicator\030\027 \001(\005\022"
    "\022\n\nPxAccuracy\030\030 \001(\005\022\025\n\rIPOProfitable\030\031 \001"
    "(\005\022\033\n\023DiffRightsIndicator\030\032 \001(\005\022\031\n\021HKSpr"
    "eadTableCode\030\033 \001(\t\022\023\n\013PreSettlePx\030\034 \001(\001\022"
    "\017\n\007PreIOPV\030\035 \001(\001\022\023\n\013ShHkConnect\030\036 \001(\005\022\023\n"
    "\013SzHkConnect\030\037 \001(\005\022\030\n\020OptionContractID\030("
    " \001(\t\022\034\n\024OptionContractSymbol\030) \001(\t\022\"\n\032Op"
    "tionUnderlyingSecurityID\030* \001(\t\022\036\n\026Option"
    "UnderlyingSymbol\030+ \001(\t\022\034\n\024OptionUnderlyi"
    "ngType\030, \001(\t\022\030\n\020OptionOptionType\030- \001(\t\022\027"
    "\n\017OptionCallOrPut\030. \001(\t\022$\n\034OptionContrac"
    "tMultiplierUnit\030/ \001(\003\022\033\n\023OptionExerciseP"
    "rice\0300 \001(\001\022\027\n\017OptionStartDate\0301 \001(\t\022\025\n\rO"
    "ptionEndDate\0302 \001(\t\022\032\n\022OptionExerciseDate"
    "\0303 \001(\t\022\032\n\022OptionDeliveryDate\0304 \001(\t\022\030\n\020Op"
    "tionExpireDate\0305 \001(\t\022\033\n\023OptionUpdateVers"
    "ion\0306 \001(\t\022\037\n\027OptionTotalLongPosition\0307 \001"
    "(\003\022\035\n\025OptionSecurityClosePx\0308 \001(\001\022\030\n\020Opt"
    "ionSettlPrice\0309 \001(\001\022\037\n\027OptionUnderlyingC"
    "losePx\030: \001(\001\022\034\n\024OptionPriceLimitType\030; \001"
    "(\t\022\037\n\027OptionDailyPriceUpLimit\030< \001(\001\022!\n\031O"
    "ptionDailyPriceDownLimit\030= \001(\001\022\030\n\020Option"
    "MarginUnit\030> \001(\001\022\037\n\027OptionMarginRatioPar"
    "am1\030\? \001(\001\022\037\n\027OptionMarginRatioParam2\030@ \001"
    "(\001\022\026\n\016OptionRoundLot\030A \001(\003\022\034\n\024OptionLmtO"
    "rdMinFloor\030B \001(\003\022\034\n\024OptionLmtOrdMaxFloor"
    "\030C \001(\003\022\034\n\024OptionMktOrdMinFloor\030D \001(\003\022\034\n\024"
    "OptionMktOrdMaxFloor\030E \001(\003\022\026\n\016OptionTick"
    "Size\030F \001(\001\022 \n\030OptionSecurityStatusFlag\030G"
    " \001(\t\022\037\n\027OptionCarryInterestDate\030H \001(\t\022\035\n"
    "\025OptionEarlyExpireDate\030I \001(\t\022 \n\030OptionSt"
    "rategySecurityID\030J \001(\t\022\032\n\022FITradeProduct"
    "Type\030P \001(\t\022\032\n\022FISecurityProperty\030Q \001(\t\022\030"
    "\n\020FISecurityStatus\030R \001(\t\022\033\n\023FIPledgedSec"
    "urityID\030S \001(\t\022\022\n\nFIOpenTime\030T \001(\t\022\023\n\013FIC"
    "loseTime\030U \001(\t\022\023\n\013FIIssueMode\030V \001(\t\022\024\n\014F"
    "IFaceAmount\030W \001(\001\022\024\n\014FIIssuePrice\030X \001(\001\022"
    "\026\n\016FIInterestType\030Y \001(\t\022\033\n\023FIInterestFre"
    "quency\030Z \001(\t\022 \n\030FIGuaranteedInterestRate"
    "\030[ \001(\001\022\032\n\022FIBaseInterestRate\030\\ \001(\001\022\026\n\016FI"
    "QuotedMargin\030] \001(\001\022\023\n\013FITimeLimit\030^ \001(\005\022"
    "\027\n\017FITotalIssuance\030_ \001(\001\022\030\n\020FIIssueStart"
    "Date\030` \001(\t\022\026\n\016FIIssueEndDate\030a \001(\t\022\022\n\nFI"
    "ListDate\030b \001(\t\022\024\n\014FIExpireDate\030c \001(\t\022\032\n\022"
    "FINationalDebtType\030d \001(\t\022\025\n\rFIIssueMetho"
    "d\030e \001(\t\022\025\n\rFICrossMarket\030f \001(\010\022\027\n\017FIShor"
    "tSellFlag\030g \001(\010\022\035\n\025FITotalShortSellQuota"
    "\030h \001(\001\022\036\n\026FIDealerShortSellQuota\030i \001(\001\022\024"
    "\n\014FIPreClosePx\030j \001(\001\022\027\n\017FIPreWeightedPx\030"
    "k \001(\001\022\026\n\016OptionListType\030n \001(\t\022\032\n\022OptionD"
    "eliveryType\030o \001(\t\022\031\n\021OptionAdjustTimes\030p"
    " \001(\005\022\036\n\026OptionContractPosition\030q \001(\003\022\036\n\026"
    "OptionBuyQtyUpperLimit\030r \001(\003\022\037\n\027OptionSe"
    "llQtyUpperLimit\030s \001(\003\022)\n!OptionMarketOrd"
    "erBuyQtyUpperLimit\030t \001(\003\022*\n\"OptionMarket"
    "OrderSellQtyUpperLimit\030u \001(\003\022(\n OptionQu"
    "oteOrderBuyQtyUpperLimit\030v \001(\003\022)\n!Option"
    "QuoteOrderSellQtyUpperLimit\030w \001(\003\022\030\n\020Opt"
    "ionBuyQtyUnit\030x \001(\003\022\031\n\021OptionSellQtyUnit"
    "\030y \001(\003\022\034\n\024OptionLastSellMargin\030z \001(\001\022\030\n\020"
    "OptionSellMargin\030{ \001(\001\022\035\n\025OptionMarketMa"
    "kerFlag\030| \001(\t\022!\n\031OptionCombinationStrate"
    "gy\030} \001(\t\022\024\n\014DeliveryYear\030~ \001(\t\022\025\n\rDelive"
    "ryMonth\030\177 \001(\t\022\025\n\014InstrumentID\030\200\001 \001(\t\022\027\n\016"
    "InstrumentName\030\201\001 \001(\t\022\027\n\016ExchangeInstID\030"
    "\202\001 \001(\t\022\022\n\tProductID\030\203\001 \001(\t\022\035\n\024MaxMarketO"
    "rderVolume\030\204\001 \001(\003\022\035\n\024MinMarketOrderVolum"
    "e\030\205\001 \001(\003\022\034\n\023MaxLimitOrderVolume\030\206\001 \001(\003\022\034"
    "\n\023MinLimitOrderVolume\030\207\001 \001(\003\022\027\n\016VolumeMu"
    "ltiple\030\210\001 \001(\003\022\023\n\nCreateDate\030\211\001 \001(\t\022\023\n\nEx"
    "pireDate\030\212\001 \001(\t\022\027\n\016StartDelivDate\030\213\001 \001(\t"
    "\022\025\n\014EndDelivDate\030\214\001 \001(\t\022\025\n\014PositionType\030"
    "\215\001 \001(\t\022\030\n\017LongMarginRatio\030\216\001 \001(\001\022\031\n\020Shor"
    "tMarginRatio\030\217\001 \001(\001\022\037\n\026MaxMarginSideAlgo"
    "rithm\030\220\001 \001(\t\022\024\n\013StrikePrice\030\221\001 \001(\001\022\030\n\017Pr"
    "eOpenInterest\030\222\001 \001(\001\022\025\n\014FormerSymbol\030\223\001 "
    "\001(\t\022\023\n\nDelistDate\030\224\001 \001(\t\022\023\n\nBuyQtyUnit\030\225"
    "\001 \001(\003\022\024\n\013SellQtyUnit\030\226\001 \001(\003\022\031\n\020BuyQtyUpp"
    "erLimit\030\241\001 \001(\003\022\032\n\021SellQtyUpperLimit\030\242\001 \001"
    "(\003\022\031\n\020BuyQtyLowerLimit\030\243\001 \001(\003\022\032\n\021SellQty"
    "LowerLimit\030\244\001 \001(\003\022\020\n\007VCMFlag\030\245\001 \001(\005\022\020\n\007C"
    "ASFlag\030\246\001 \001(\005\022\020\n\007POSFlag\030\247\001 \001(\005\022\030\n\017POSUp"
    "perLimitPx\030\250\001 \001(\001\022\030\n\017POSLowerLimitPx\030\251\001 "
    "\001(\001\022\027\n\016BaseContractID\030\252\001 \001(\t\022N\n\016constant"
    "Params\030\253\001 \003(\01325.com.htsc.mdc.insight.mod"
    "el.MDBasicInfo.ConstantParam\022\036\n\025DataMult"
    "iplePowerOf10\030\254\001 \001(\005\022\034\n\023InterestAccrualD"
    "ate\030\255\001 \001(\t\0326\n\rConstantParam\022\021\n\tParamName"
    "\030\001 \001(\t\022\022\n\nParamValue\030\002 \001(\tB4\n\032com.htsc.m"
    "dc.insight.modelB\021MDBasicInfoProtosH\001\240\001\001"
    "b\006proto3", 4248);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDBasicInfo.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDBasicInfo_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDBasicInfo_2eproto_once_);
void protobuf_AddDesc_MDBasicInfo_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDBasicInfo_2eproto_once_,
                 &protobuf_AddDesc_MDBasicInfo_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDBasicInfo_2eproto {
  StaticDescriptorInitializer_MDBasicInfo_2eproto() {
    protobuf_AddDesc_MDBasicInfo_2eproto();
  }
} static_descriptor_initializer_MDBasicInfo_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDBasicInfo_ConstantParam::kParamNameFieldNumber;
const int MDBasicInfo_ConstantParam::kParamValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDBasicInfo_ConstantParam::MDBasicInfo_ConstantParam()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDBasicInfo_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
}

void MDBasicInfo_ConstantParam::InitAsDefaultInstance() {
}

MDBasicInfo_ConstantParam::MDBasicInfo_ConstantParam(const MDBasicInfo_ConstantParam& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
}

void MDBasicInfo_ConstantParam::SharedCtor() {
  paramname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

MDBasicInfo_ConstantParam::~MDBasicInfo_ConstantParam() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  SharedDtor();
}

void MDBasicInfo_ConstantParam::SharedDtor() {
  paramname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDBasicInfo_ConstantParam::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDBasicInfo_ConstantParam::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDBasicInfo_ConstantParam_descriptor_;
}

const MDBasicInfo_ConstantParam& MDBasicInfo_ConstantParam::default_instance() {
  protobuf_InitDefaults_MDBasicInfo_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDBasicInfo_ConstantParam> MDBasicInfo_ConstantParam_default_instance_;

MDBasicInfo_ConstantParam* MDBasicInfo_ConstantParam::New(::google::protobuf::Arena* arena) const {
  MDBasicInfo_ConstantParam* n = new MDBasicInfo_ConstantParam;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDBasicInfo_ConstantParam::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  paramname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool MDBasicInfo_ConstantParam::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ParamName = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_paramname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->paramname().data(), this->paramname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_ParamValue;
        break;
      }

      // optional string ParamValue = 2;
      case 2: {
        if (tag == 18) {
         parse_ParamValue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_paramvalue()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->paramvalue().data(), this->paramvalue().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  return false;
#undef DO_
}

void MDBasicInfo_ConstantParam::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  // optional string ParamName = 1;
  if (this->paramname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramname().data(), this->paramname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->paramname(), output);
  }

  // optional string ParamValue = 2;
  if (this->paramvalue().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramvalue().data(), this->paramvalue().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->paramvalue(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
}

::google::protobuf::uint8* MDBasicInfo_ConstantParam::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  // optional string ParamName = 1;
  if (this->paramname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramname().data(), this->paramname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->paramname(), target);
  }

  // optional string ParamValue = 2;
  if (this->paramvalue().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->paramvalue().data(), this->paramvalue().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->paramvalue(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  return target;
}

size_t MDBasicInfo_ConstantParam::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  size_t total_size = 0;

  // optional string ParamName = 1;
  if (this->paramname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->paramname());
  }

  // optional string ParamValue = 2;
  if (this->paramvalue().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->paramvalue());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDBasicInfo_ConstantParam::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDBasicInfo_ConstantParam* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDBasicInfo_ConstantParam>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
    UnsafeMergeFrom(*source);
  }
}

void MDBasicInfo_ConstantParam::MergeFrom(const MDBasicInfo_ConstantParam& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDBasicInfo_ConstantParam::UnsafeMergeFrom(const MDBasicInfo_ConstantParam& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.paramname().size() > 0) {

    paramname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.paramname_);
  }
  if (from.paramvalue().size() > 0) {

    paramvalue_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.paramvalue_);
  }
}

void MDBasicInfo_ConstantParam::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDBasicInfo_ConstantParam::CopyFrom(const MDBasicInfo_ConstantParam& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDBasicInfo_ConstantParam::IsInitialized() const {

  return true;
}

void MDBasicInfo_ConstantParam::Swap(MDBasicInfo_ConstantParam* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDBasicInfo_ConstantParam::InternalSwap(MDBasicInfo_ConstantParam* other) {
  paramname_.Swap(&other->paramname_);
  paramvalue_.Swap(&other->paramvalue_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDBasicInfo_ConstantParam::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDBasicInfo_ConstantParam_descriptor_;
  metadata.reflection = MDBasicInfo_ConstantParam_reflection_;
  return metadata;
}


// -------------------------------------------------------------------

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDBasicInfo::kHTSCSecurityIDFieldNumber;
const int MDBasicInfo::kSecurityIDFieldNumber;
const int MDBasicInfo::kSymbolFieldNumber;
const int MDBasicInfo::kChiSpellingFieldNumber;
const int MDBasicInfo::kEnglishNameFieldNumber;
const int MDBasicInfo::kSecurityIDSourceFieldNumber;
const int MDBasicInfo::kSecurityTypeFieldNumber;
const int MDBasicInfo::kSecuritySubTypeFieldNumber;
const int MDBasicInfo::kListDateFieldNumber;
const int MDBasicInfo::kCurrencyFieldNumber;
const int MDBasicInfo::kOutstandingShareFieldNumber;
const int MDBasicInfo::kPublicFloatShareQuantityFieldNumber;
const int MDBasicInfo::kMDDateFieldNumber;
const int MDBasicInfo::kTradingPhaseCodeFieldNumber;
const int MDBasicInfo::kPreClosePxFieldNumber;
const int MDBasicInfo::kMaxPxFieldNumber;
const int MDBasicInfo::kMinPxFieldNumber;
const int MDBasicInfo::kLotSizeFieldNumber;
const int MDBasicInfo::kShortSellFlagFieldNumber;
const int MDBasicInfo::kExchangeDateFieldNumber;
const int MDBasicInfo::kExchangeSymbolFieldNumber;
const int MDBasicInfo::kTickSizeFieldNumber;
const int MDBasicInfo::kLoanMarginIndicatorFieldNumber;
const int MDBasicInfo::kPxAccuracyFieldNumber;
const int MDBasicInfo::kIPOProfitableFieldNumber;
const int MDBasicInfo::kDiffRightsIndicatorFieldNumber;
const int MDBasicInfo::kHKSpreadTableCodeFieldNumber;
const int MDBasicInfo::kPreSettlePxFieldNumber;
const int MDBasicInfo::kPreIOPVFieldNumber;
const int MDBasicInfo::kShHkConnectFieldNumber;
const int MDBasicInfo::kSzHkConnectFieldNumber;
const int MDBasicInfo::kOptionContractIDFieldNumber;
const int MDBasicInfo::kOptionContractSymbolFieldNumber;
const int MDBasicInfo::kOptionUnderlyingSecurityIDFieldNumber;
const int MDBasicInfo::kOptionUnderlyingSymbolFieldNumber;
const int MDBasicInfo::kOptionUnderlyingTypeFieldNumber;
const int MDBasicInfo::kOptionOptionTypeFieldNumber;
const int MDBasicInfo::kOptionCallOrPutFieldNumber;
const int MDBasicInfo::kOptionContractMultiplierUnitFieldNumber;
const int MDBasicInfo::kOptionExercisePriceFieldNumber;
const int MDBasicInfo::kOptionStartDateFieldNumber;
const int MDBasicInfo::kOptionEndDateFieldNumber;
const int MDBasicInfo::kOptionExerciseDateFieldNumber;
const int MDBasicInfo::kOptionDeliveryDateFieldNumber;
const int MDBasicInfo::kOptionExpireDateFieldNumber;
const int MDBasicInfo::kOptionUpdateVersionFieldNumber;
const int MDBasicInfo::kOptionTotalLongPositionFieldNumber;
const int MDBasicInfo::kOptionSecurityClosePxFieldNumber;
const int MDBasicInfo::kOptionSettlPriceFieldNumber;
const int MDBasicInfo::kOptionUnderlyingClosePxFieldNumber;
const int MDBasicInfo::kOptionPriceLimitTypeFieldNumber;
const int MDBasicInfo::kOptionDailyPriceUpLimitFieldNumber;
const int MDBasicInfo::kOptionDailyPriceDownLimitFieldNumber;
const int MDBasicInfo::kOptionMarginUnitFieldNumber;
const int MDBasicInfo::kOptionMarginRatioParam1FieldNumber;
const int MDBasicInfo::kOptionMarginRatioParam2FieldNumber;
const int MDBasicInfo::kOptionRoundLotFieldNumber;
const int MDBasicInfo::kOptionLmtOrdMinFloorFieldNumber;
const int MDBasicInfo::kOptionLmtOrdMaxFloorFieldNumber;
const int MDBasicInfo::kOptionMktOrdMinFloorFieldNumber;
const int MDBasicInfo::kOptionMktOrdMaxFloorFieldNumber;
const int MDBasicInfo::kOptionTickSizeFieldNumber;
const int MDBasicInfo::kOptionSecurityStatusFlagFieldNumber;
const int MDBasicInfo::kOptionCarryInterestDateFieldNumber;
const int MDBasicInfo::kOptionEarlyExpireDateFieldNumber;
const int MDBasicInfo::kOptionStrategySecurityIDFieldNumber;
const int MDBasicInfo::kFITradeProductTypeFieldNumber;
const int MDBasicInfo::kFISecurityPropertyFieldNumber;
const int MDBasicInfo::kFISecurityStatusFieldNumber;
const int MDBasicInfo::kFIPledgedSecurityIDFieldNumber;
const int MDBasicInfo::kFIOpenTimeFieldNumber;
const int MDBasicInfo::kFICloseTimeFieldNumber;
const int MDBasicInfo::kFIIssueModeFieldNumber;
const int MDBasicInfo::kFIFaceAmountFieldNumber;
const int MDBasicInfo::kFIIssuePriceFieldNumber;
const int MDBasicInfo::kFIInterestTypeFieldNumber;
const int MDBasicInfo::kFIInterestFrequencyFieldNumber;
const int MDBasicInfo::kFIGuaranteedInterestRateFieldNumber;
const int MDBasicInfo::kFIBaseInterestRateFieldNumber;
const int MDBasicInfo::kFIQuotedMarginFieldNumber;
const int MDBasicInfo::kFITimeLimitFieldNumber;
const int MDBasicInfo::kFITotalIssuanceFieldNumber;
const int MDBasicInfo::kFIIssueStartDateFieldNumber;
const int MDBasicInfo::kFIIssueEndDateFieldNumber;
const int MDBasicInfo::kFIListDateFieldNumber;
const int MDBasicInfo::kFIExpireDateFieldNumber;
const int MDBasicInfo::kFINationalDebtTypeFieldNumber;
const int MDBasicInfo::kFIIssueMethodFieldNumber;
const int MDBasicInfo::kFICrossMarketFieldNumber;
const int MDBasicInfo::kFIShortSellFlagFieldNumber;
const int MDBasicInfo::kFITotalShortSellQuotaFieldNumber;
const int MDBasicInfo::kFIDealerShortSellQuotaFieldNumber;
const int MDBasicInfo::kFIPreClosePxFieldNumber;
const int MDBasicInfo::kFIPreWeightedPxFieldNumber;
const int MDBasicInfo::kOptionListTypeFieldNumber;
const int MDBasicInfo::kOptionDeliveryTypeFieldNumber;
const int MDBasicInfo::kOptionAdjustTimesFieldNumber;
const int MDBasicInfo::kOptionContractPositionFieldNumber;
const int MDBasicInfo::kOptionBuyQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionSellQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionMarketOrderBuyQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionMarketOrderSellQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionQuoteOrderBuyQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionQuoteOrderSellQtyUpperLimitFieldNumber;
const int MDBasicInfo::kOptionBuyQtyUnitFieldNumber;
const int MDBasicInfo::kOptionSellQtyUnitFieldNumber;
const int MDBasicInfo::kOptionLastSellMarginFieldNumber;
const int MDBasicInfo::kOptionSellMarginFieldNumber;
const int MDBasicInfo::kOptionMarketMakerFlagFieldNumber;
const int MDBasicInfo::kOptionCombinationStrategyFieldNumber;
const int MDBasicInfo::kDeliveryYearFieldNumber;
const int MDBasicInfo::kDeliveryMonthFieldNumber;
const int MDBasicInfo::kInstrumentIDFieldNumber;
const int MDBasicInfo::kInstrumentNameFieldNumber;
const int MDBasicInfo::kExchangeInstIDFieldNumber;
const int MDBasicInfo::kProductIDFieldNumber;
const int MDBasicInfo::kMaxMarketOrderVolumeFieldNumber;
const int MDBasicInfo::kMinMarketOrderVolumeFieldNumber;
const int MDBasicInfo::kMaxLimitOrderVolumeFieldNumber;
const int MDBasicInfo::kMinLimitOrderVolumeFieldNumber;
const int MDBasicInfo::kVolumeMultipleFieldNumber;
const int MDBasicInfo::kCreateDateFieldNumber;
const int MDBasicInfo::kExpireDateFieldNumber;
const int MDBasicInfo::kStartDelivDateFieldNumber;
const int MDBasicInfo::kEndDelivDateFieldNumber;
const int MDBasicInfo::kPositionTypeFieldNumber;
const int MDBasicInfo::kLongMarginRatioFieldNumber;
const int MDBasicInfo::kShortMarginRatioFieldNumber;
const int MDBasicInfo::kMaxMarginSideAlgorithmFieldNumber;
const int MDBasicInfo::kStrikePriceFieldNumber;
const int MDBasicInfo::kPreOpenInterestFieldNumber;
const int MDBasicInfo::kFormerSymbolFieldNumber;
const int MDBasicInfo::kDelistDateFieldNumber;
const int MDBasicInfo::kBuyQtyUnitFieldNumber;
const int MDBasicInfo::kSellQtyUnitFieldNumber;
const int MDBasicInfo::kBuyQtyUpperLimitFieldNumber;
const int MDBasicInfo::kSellQtyUpperLimitFieldNumber;
const int MDBasicInfo::kBuyQtyLowerLimitFieldNumber;
const int MDBasicInfo::kSellQtyLowerLimitFieldNumber;
const int MDBasicInfo::kVCMFlagFieldNumber;
const int MDBasicInfo::kCASFlagFieldNumber;
const int MDBasicInfo::kPOSFlagFieldNumber;
const int MDBasicInfo::kPOSUpperLimitPxFieldNumber;
const int MDBasicInfo::kPOSLowerLimitPxFieldNumber;
const int MDBasicInfo::kBaseContractIDFieldNumber;
const int MDBasicInfo::kConstantParamsFieldNumber;
const int MDBasicInfo::kDataMultiplePowerOf10FieldNumber;
const int MDBasicInfo::kInterestAccrualDateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDBasicInfo::MDBasicInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDBasicInfo_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDBasicInfo)
}

void MDBasicInfo::InitAsDefaultInstance() {
}

MDBasicInfo::MDBasicInfo(const MDBasicInfo& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDBasicInfo)
}

void MDBasicInfo::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  chispelling_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  englishname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  listdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangesymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  hkspreadtablecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncontractid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncontractsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionoptiontype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncallorput_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionstartdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionenddate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexercisedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverydate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexpiredate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionupdateversion_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionpricelimittype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionsecuritystatusflag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncarryinterestdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionearlyexpiredate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionstrategysecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fitradeproducttype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecurityproperty_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecuritystatus_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fipledgedsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiopentime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ficlosetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuemode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinteresttype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinterestfrequency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuestartdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissueenddate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  filistdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiexpiredate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  finationaldebttype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuemethod_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionlisttype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverytype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionmarketmakerflag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncombinationstrategy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliveryyear_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliverymonth_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangeinstid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  productid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  createdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expiredate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  startdelivdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enddelivdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  positiontype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maxmarginsidealgorithm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  formersymbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  delistdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  basecontractid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  interestaccrualdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&securityidsource_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&securityidsource_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDBasicInfo::~MDBasicInfo() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDBasicInfo)
  SharedDtor();
}

void MDBasicInfo::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  chispelling_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  englishname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  listdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangesymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  hkspreadtablecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncontractid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncontractsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionoptiontype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncallorput_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionstartdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionenddate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexercisedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverydate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexpiredate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionupdateversion_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionpricelimittype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionsecuritystatusflag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncarryinterestdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionearlyexpiredate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionstrategysecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fitradeproducttype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecurityproperty_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecuritystatus_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fipledgedsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiopentime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ficlosetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuemode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinteresttype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinterestfrequency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuestartdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissueenddate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  filistdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiexpiredate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  finationaldebttype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuemethod_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionlisttype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverytype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionmarketmakerflag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncombinationstrategy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliveryyear_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliverymonth_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangeinstid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  productid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  createdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expiredate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  startdelivdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enddelivdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  positiontype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  maxmarginsidealgorithm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  formersymbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  delistdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  basecontractid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  interestaccrualdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDBasicInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDBasicInfo::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDBasicInfo_descriptor_;
}

const MDBasicInfo& MDBasicInfo::default_instance() {
  protobuf_InitDefaults_MDBasicInfo_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDBasicInfo> MDBasicInfo_default_instance_;

MDBasicInfo* MDBasicInfo::New(::google::protobuf::Arena* arena) const {
  MDBasicInfo* n = new MDBasicInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDBasicInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDBasicInfo)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDBasicInfo, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDBasicInfo*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securityidsource_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  chispelling_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  englishname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(outstandingshare_, maxpx_);
  listdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mddate_ = 0;
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, lotsize_);
  ZR_(loanmarginindicator_, pxaccuracy_);
  shortsellflag_ = false;
  exchangedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(ipoprofitable_, preiopv_);
  hkspreadtablecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  szhkconnect_ = 0;
  optioncontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(optioncontractmultiplierunit_, optionexerciseprice_);
  optioncontractsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionunderlyingtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionoptiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncallorput_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(optiontotallongposition_, optionsecurityclosepx_);
  optionstartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexercisedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionupdateversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(optionsettlprice_, optionmarginratioparam2_);
  optionpricelimittype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(optionlmtordminfloor_, optionticksize_);
  optionroundlot_ = GOOGLE_LONGLONG(0);
  optionsecuritystatusflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncarryinterestdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionearlyexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optionstrategysecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fitradeproducttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecurityproperty_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fisecuritystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fipledgedsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiopentime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ficlosetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(fifaceamount_, fiquotedmargin_);
  fiissuemode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinteresttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiinterestfrequency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(fitotalissuance_, fitimelimit_);
  fiissuestartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissueenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  filistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  finationaldebttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fiissuemethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(ficrossmarket_, fishortsellflag_);
  ZR_(fitotalshortsellquota_, fipreweightedpx_);
  optionlisttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiondeliverytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(optioncontractposition_, optionquoteordersellqtyupperlimit_);
  optionadjusttimes_ = 0;
  ZR_(optionbuyqtyunit_, optionsellmargin_);
  optionmarketmakerflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optioncombinationstrategy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliveryyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deliverymonth_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(maxmarketordervolume_, minlimitordervolume_);
  instrumentid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangeinstid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  productid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(volumemultiple_, shortmarginratio_);
  createdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  startdelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  enddelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  positiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(strikeprice_, buyqtyupperlimit_);
  maxmarginsidealgorithm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  delistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(sellqtyupperlimit_, posflag_);
  basecontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  datamultiplepowerof10_ = 0;
  interestaccrualdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

  constantparams_.Clear();
}

bool MDBasicInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDBasicInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_SecurityID;
        break;
      }

      // optional string SecurityID = 2;
      case 2: {
        if (tag == 18) {
         parse_SecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securityid().data(), this->securityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.SecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Symbol;
        break;
      }

      // optional string Symbol = 3;
      case 3: {
        if (tag == 26) {
         parse_Symbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_symbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->symbol().data(), this->symbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.Symbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_ChiSpelling;
        break;
      }

      // optional string ChiSpelling = 4;
      case 4: {
        if (tag == 34) {
         parse_ChiSpelling:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_chispelling()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->chispelling().data(), this->chispelling().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_EnglishName;
        break;
      }

      // optional string EnglishName = 5;
      case 5: {
        if (tag == 42) {
         parse_EnglishName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_englishname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->englishname().data(), this->englishname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.EnglishName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_SecuritySubType;
        break;
      }

      // optional string SecuritySubType = 8;
      case 8: {
        if (tag == 66) {
         parse_SecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitysubtype().data(), this->securitysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_ListDate;
        break;
      }

      // optional string ListDate = 9;
      case 9: {
        if (tag == 74) {
         parse_ListDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_listdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->listdate().data(), this->listdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ListDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_Currency;
        break;
      }

      // optional string Currency = 10;
      case 10: {
        if (tag == 82) {
         parse_Currency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_currency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->currency().data(), this->currency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.Currency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_OutstandingShare;
        break;
      }

      // optional int64 OutstandingShare = 11;
      case 11: {
        if (tag == 88) {
         parse_OutstandingShare:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &outstandingshare_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_PublicFloatShareQuantity;
        break;
      }

      // optional int64 PublicFloatShareQuantity = 12;
      case 12: {
        if (tag == 96) {
         parse_PublicFloatShareQuantity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &publicfloatsharequantity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 13;
      case 13: {
        if (tag == 104) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 14;
      case 14: {
        if (tag == 114) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 15;
      case 15: {
        if (tag == 120) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 16;
      case 16: {
        if (tag == 128) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 17;
      case 17: {
        if (tag == 136) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LotSize;
        break;
      }

      // optional int64 LotSize = 18;
      case 18: {
        if (tag == 144) {
         parse_LotSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lotsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_ShortSellFlag;
        break;
      }

      // optional bool ShortSellFlag = 19;
      case 19: {
        if (tag == 152) {
         parse_ShortSellFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &shortsellflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_ExchangeDate;
        break;
      }

      // optional string ExchangeDate = 20;
      case 20: {
        if (tag == 162) {
         parse_ExchangeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchangedate().data(), this->exchangedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_ExchangeSymbol;
        break;
      }

      // optional string ExchangeSymbol = 21;
      case 21: {
        if (tag == 170) {
         parse_ExchangeSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangesymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchangesymbol().data(), this->exchangesymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_TickSize;
        break;
      }

      // optional double TickSize = 22;
      case 22: {
        if (tag == 177) {
         parse_TickSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &ticksize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_LoanMarginIndicator;
        break;
      }

      // optional int32 LoanMarginIndicator = 23;
      case 23: {
        if (tag == 184) {
         parse_LoanMarginIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &loanmarginindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_PxAccuracy;
        break;
      }

      // optional int32 PxAccuracy = 24;
      case 24: {
        if (tag == 192) {
         parse_PxAccuracy:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &pxaccuracy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_IPOProfitable;
        break;
      }

      // optional int32 IPOProfitable = 25;
      case 25: {
        if (tag == 200) {
         parse_IPOProfitable:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ipoprofitable_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_DiffRightsIndicator;
        break;
      }

      // optional int32 DiffRightsIndicator = 26;
      case 26: {
        if (tag == 208) {
         parse_DiffRightsIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &diffrightsindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_HKSpreadTableCode;
        break;
      }

      // optional string HKSpreadTableCode = 27;
      case 27: {
        if (tag == 218) {
         parse_HKSpreadTableCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_hkspreadtablecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hkspreadtablecode().data(), this->hkspreadtablecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(225)) goto parse_PreSettlePx;
        break;
      }

      // optional double PreSettlePx = 28;
      case 28: {
        if (tag == 225) {
         parse_PreSettlePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &presettlepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(233)) goto parse_PreIOPV;
        break;
      }

      // optional double PreIOPV = 29;
      case 29: {
        if (tag == 233) {
         parse_PreIOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preiopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_ShHkConnect;
        break;
      }

      // optional int32 ShHkConnect = 30;
      case 30: {
        if (tag == 240) {
         parse_ShHkConnect:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &shhkconnect_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_SzHkConnect;
        break;
      }

      // optional int32 SzHkConnect = 31;
      case 31: {
        if (tag == 248) {
         parse_SzHkConnect:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &szhkconnect_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_OptionContractID;
        break;
      }

      // optional string OptionContractID = 40;
      case 40: {
        if (tag == 322) {
         parse_OptionContractID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optioncontractid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optioncontractid().data(), this->optioncontractid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(330)) goto parse_OptionContractSymbol;
        break;
      }

      // optional string OptionContractSymbol = 41;
      case 41: {
        if (tag == 330) {
         parse_OptionContractSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optioncontractsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optioncontractsymbol().data(), this->optioncontractsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(338)) goto parse_OptionUnderlyingSecurityID;
        break;
      }

      // optional string OptionUnderlyingSecurityID = 42;
      case 42: {
        if (tag == 338) {
         parse_OptionUnderlyingSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionunderlyingsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionunderlyingsecurityid().data(), this->optionunderlyingsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_OptionUnderlyingSymbol;
        break;
      }

      // optional string OptionUnderlyingSymbol = 43;
      case 43: {
        if (tag == 346) {
         parse_OptionUnderlyingSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionunderlyingsymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionunderlyingsymbol().data(), this->optionunderlyingsymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_OptionUnderlyingType;
        break;
      }

      // optional string OptionUnderlyingType = 44;
      case 44: {
        if (tag == 354) {
         parse_OptionUnderlyingType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionunderlyingtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionunderlyingtype().data(), this->optionunderlyingtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(362)) goto parse_OptionOptionType;
        break;
      }

      // optional string OptionOptionType = 45;
      case 45: {
        if (tag == 362) {
         parse_OptionOptionType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionoptiontype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionoptiontype().data(), this->optionoptiontype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(370)) goto parse_OptionCallOrPut;
        break;
      }

      // optional string OptionCallOrPut = 46;
      case 46: {
        if (tag == 370) {
         parse_OptionCallOrPut:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optioncallorput()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optioncallorput().data(), this->optioncallorput().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(376)) goto parse_OptionContractMultiplierUnit;
        break;
      }

      // optional int64 OptionContractMultiplierUnit = 47;
      case 47: {
        if (tag == 376) {
         parse_OptionContractMultiplierUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optioncontractmultiplierunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(385)) goto parse_OptionExercisePrice;
        break;
      }

      // optional double OptionExercisePrice = 48;
      case 48: {
        if (tag == 385) {
         parse_OptionExercisePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionexerciseprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(394)) goto parse_OptionStartDate;
        break;
      }

      // optional string OptionStartDate = 49;
      case 49: {
        if (tag == 394) {
         parse_OptionStartDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionstartdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionstartdate().data(), this->optionstartdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_OptionEndDate;
        break;
      }

      // optional string OptionEndDate = 50;
      case 50: {
        if (tag == 402) {
         parse_OptionEndDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionenddate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionenddate().data(), this->optionenddate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_OptionExerciseDate;
        break;
      }

      // optional string OptionExerciseDate = 51;
      case 51: {
        if (tag == 410) {
         parse_OptionExerciseDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionexercisedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionexercisedate().data(), this->optionexercisedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_OptionDeliveryDate;
        break;
      }

      // optional string OptionDeliveryDate = 52;
      case 52: {
        if (tag == 418) {
         parse_OptionDeliveryDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optiondeliverydate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optiondeliverydate().data(), this->optiondeliverydate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_OptionExpireDate;
        break;
      }

      // optional string OptionExpireDate = 53;
      case 53: {
        if (tag == 426) {
         parse_OptionExpireDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionexpiredate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionexpiredate().data(), this->optionexpiredate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_OptionUpdateVersion;
        break;
      }

      // optional string OptionUpdateVersion = 54;
      case 54: {
        if (tag == 434) {
         parse_OptionUpdateVersion:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionupdateversion()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionupdateversion().data(), this->optionupdateversion().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(440)) goto parse_OptionTotalLongPosition;
        break;
      }

      // optional int64 OptionTotalLongPosition = 55;
      case 55: {
        if (tag == 440) {
         parse_OptionTotalLongPosition:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optiontotallongposition_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(449)) goto parse_OptionSecurityClosePx;
        break;
      }

      // optional double OptionSecurityClosePx = 56;
      case 56: {
        if (tag == 449) {
         parse_OptionSecurityClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionsecurityclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(457)) goto parse_OptionSettlPrice;
        break;
      }

      // optional double OptionSettlPrice = 57;
      case 57: {
        if (tag == 457) {
         parse_OptionSettlPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionsettlprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(465)) goto parse_OptionUnderlyingClosePx;
        break;
      }

      // optional double OptionUnderlyingClosePx = 58;
      case 58: {
        if (tag == 465) {
         parse_OptionUnderlyingClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionunderlyingclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(474)) goto parse_OptionPriceLimitType;
        break;
      }

      // optional string OptionPriceLimitType = 59;
      case 59: {
        if (tag == 474) {
         parse_OptionPriceLimitType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionpricelimittype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionpricelimittype().data(), this->optionpricelimittype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(481)) goto parse_OptionDailyPriceUpLimit;
        break;
      }

      // optional double OptionDailyPriceUpLimit = 60;
      case 60: {
        if (tag == 481) {
         parse_OptionDailyPriceUpLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optiondailypriceuplimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(489)) goto parse_OptionDailyPriceDownLimit;
        break;
      }

      // optional double OptionDailyPriceDownLimit = 61;
      case 61: {
        if (tag == 489) {
         parse_OptionDailyPriceDownLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optiondailypricedownlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(497)) goto parse_OptionMarginUnit;
        break;
      }

      // optional double OptionMarginUnit = 62;
      case 62: {
        if (tag == 497) {
         parse_OptionMarginUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionmarginunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(505)) goto parse_OptionMarginRatioParam1;
        break;
      }

      // optional double OptionMarginRatioParam1 = 63;
      case 63: {
        if (tag == 505) {
         parse_OptionMarginRatioParam1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionmarginratioparam1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(513)) goto parse_OptionMarginRatioParam2;
        break;
      }

      // optional double OptionMarginRatioParam2 = 64;
      case 64: {
        if (tag == 513) {
         parse_OptionMarginRatioParam2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionmarginratioparam2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(520)) goto parse_OptionRoundLot;
        break;
      }

      // optional int64 OptionRoundLot = 65;
      case 65: {
        if (tag == 520) {
         parse_OptionRoundLot:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionroundlot_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(528)) goto parse_OptionLmtOrdMinFloor;
        break;
      }

      // optional int64 OptionLmtOrdMinFloor = 66;
      case 66: {
        if (tag == 528) {
         parse_OptionLmtOrdMinFloor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionlmtordminfloor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(536)) goto parse_OptionLmtOrdMaxFloor;
        break;
      }

      // optional int64 OptionLmtOrdMaxFloor = 67;
      case 67: {
        if (tag == 536) {
         parse_OptionLmtOrdMaxFloor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionlmtordmaxfloor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(544)) goto parse_OptionMktOrdMinFloor;
        break;
      }

      // optional int64 OptionMktOrdMinFloor = 68;
      case 68: {
        if (tag == 544) {
         parse_OptionMktOrdMinFloor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionmktordminfloor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(552)) goto parse_OptionMktOrdMaxFloor;
        break;
      }

      // optional int64 OptionMktOrdMaxFloor = 69;
      case 69: {
        if (tag == 552) {
         parse_OptionMktOrdMaxFloor:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionmktordmaxfloor_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(561)) goto parse_OptionTickSize;
        break;
      }

      // optional double OptionTickSize = 70;
      case 70: {
        if (tag == 561) {
         parse_OptionTickSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionticksize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(570)) goto parse_OptionSecurityStatusFlag;
        break;
      }

      // optional string OptionSecurityStatusFlag = 71;
      case 71: {
        if (tag == 570) {
         parse_OptionSecurityStatusFlag:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionsecuritystatusflag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionsecuritystatusflag().data(), this->optionsecuritystatusflag().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(578)) goto parse_OptionCarryInterestDate;
        break;
      }

      // optional string OptionCarryInterestDate = 72;
      case 72: {
        if (tag == 578) {
         parse_OptionCarryInterestDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optioncarryinterestdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optioncarryinterestdate().data(), this->optioncarryinterestdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(586)) goto parse_OptionEarlyExpireDate;
        break;
      }

      // optional string OptionEarlyExpireDate = 73;
      case 73: {
        if (tag == 586) {
         parse_OptionEarlyExpireDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionearlyexpiredate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionearlyexpiredate().data(), this->optionearlyexpiredate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(594)) goto parse_OptionStrategySecurityID;
        break;
      }

      // optional string OptionStrategySecurityID = 74;
      case 74: {
        if (tag == 594) {
         parse_OptionStrategySecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionstrategysecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionstrategysecurityid().data(), this->optionstrategysecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(642)) goto parse_FITradeProductType;
        break;
      }

      // optional string FITradeProductType = 80;
      case 80: {
        if (tag == 642) {
         parse_FITradeProductType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fitradeproducttype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fitradeproducttype().data(), this->fitradeproducttype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(650)) goto parse_FISecurityProperty;
        break;
      }

      // optional string FISecurityProperty = 81;
      case 81: {
        if (tag == 650) {
         parse_FISecurityProperty:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fisecurityproperty()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fisecurityproperty().data(), this->fisecurityproperty().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(658)) goto parse_FISecurityStatus;
        break;
      }

      // optional string FISecurityStatus = 82;
      case 82: {
        if (tag == 658) {
         parse_FISecurityStatus:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fisecuritystatus()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fisecuritystatus().data(), this->fisecuritystatus().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(666)) goto parse_FIPledgedSecurityID;
        break;
      }

      // optional string FIPledgedSecurityID = 83;
      case 83: {
        if (tag == 666) {
         parse_FIPledgedSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fipledgedsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fipledgedsecurityid().data(), this->fipledgedsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(674)) goto parse_FIOpenTime;
        break;
      }

      // optional string FIOpenTime = 84;
      case 84: {
        if (tag == 674) {
         parse_FIOpenTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiopentime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiopentime().data(), this->fiopentime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(682)) goto parse_FICloseTime;
        break;
      }

      // optional string FICloseTime = 85;
      case 85: {
        if (tag == 682) {
         parse_FICloseTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ficlosetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->ficlosetime().data(), this->ficlosetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(690)) goto parse_FIIssueMode;
        break;
      }

      // optional string FIIssueMode = 86;
      case 86: {
        if (tag == 690) {
         parse_FIIssueMode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiissuemode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiissuemode().data(), this->fiissuemode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(697)) goto parse_FIFaceAmount;
        break;
      }

      // optional double FIFaceAmount = 87;
      case 87: {
        if (tag == 697) {
         parse_FIFaceAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fifaceamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(705)) goto parse_FIIssuePrice;
        break;
      }

      // optional double FIIssuePrice = 88;
      case 88: {
        if (tag == 705) {
         parse_FIIssuePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fiissueprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(714)) goto parse_FIInterestType;
        break;
      }

      // optional string FIInterestType = 89;
      case 89: {
        if (tag == 714) {
         parse_FIInterestType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiinteresttype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiinteresttype().data(), this->fiinteresttype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(722)) goto parse_FIInterestFrequency;
        break;
      }

      // optional string FIInterestFrequency = 90;
      case 90: {
        if (tag == 722) {
         parse_FIInterestFrequency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiinterestfrequency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiinterestfrequency().data(), this->fiinterestfrequency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(729)) goto parse_FIGuaranteedInterestRate;
        break;
      }

      // optional double FIGuaranteedInterestRate = 91;
      case 91: {
        if (tag == 729) {
         parse_FIGuaranteedInterestRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &figuaranteedinterestrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(737)) goto parse_FIBaseInterestRate;
        break;
      }

      // optional double FIBaseInterestRate = 92;
      case 92: {
        if (tag == 737) {
         parse_FIBaseInterestRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fibaseinterestrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(745)) goto parse_FIQuotedMargin;
        break;
      }

      // optional double FIQuotedMargin = 93;
      case 93: {
        if (tag == 745) {
         parse_FIQuotedMargin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fiquotedmargin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(752)) goto parse_FITimeLimit;
        break;
      }

      // optional int32 FITimeLimit = 94;
      case 94: {
        if (tag == 752) {
         parse_FITimeLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &fitimelimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(761)) goto parse_FITotalIssuance;
        break;
      }

      // optional double FITotalIssuance = 95;
      case 95: {
        if (tag == 761) {
         parse_FITotalIssuance:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fitotalissuance_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(770)) goto parse_FIIssueStartDate;
        break;
      }

      // optional string FIIssueStartDate = 96;
      case 96: {
        if (tag == 770) {
         parse_FIIssueStartDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiissuestartdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiissuestartdate().data(), this->fiissuestartdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(778)) goto parse_FIIssueEndDate;
        break;
      }

      // optional string FIIssueEndDate = 97;
      case 97: {
        if (tag == 778) {
         parse_FIIssueEndDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiissueenddate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiissueenddate().data(), this->fiissueenddate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(786)) goto parse_FIListDate;
        break;
      }

      // optional string FIListDate = 98;
      case 98: {
        if (tag == 786) {
         parse_FIListDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_filistdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->filistdate().data(), this->filistdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIListDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(794)) goto parse_FIExpireDate;
        break;
      }

      // optional string FIExpireDate = 99;
      case 99: {
        if (tag == 794) {
         parse_FIExpireDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiexpiredate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiexpiredate().data(), this->fiexpiredate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(802)) goto parse_FINationalDebtType;
        break;
      }

      // optional string FINationalDebtType = 100;
      case 100: {
        if (tag == 802) {
         parse_FINationalDebtType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_finationaldebttype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->finationaldebttype().data(), this->finationaldebttype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(810)) goto parse_FIIssueMethod;
        break;
      }

      // optional string FIIssueMethod = 101;
      case 101: {
        if (tag == 810) {
         parse_FIIssueMethod:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fiissuemethod()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fiissuemethod().data(), this->fiissuemethod().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(816)) goto parse_FICrossMarket;
        break;
      }

      // optional bool FICrossMarket = 102;
      case 102: {
        if (tag == 816) {
         parse_FICrossMarket:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &ficrossmarket_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(824)) goto parse_FIShortSellFlag;
        break;
      }

      // optional bool FIShortSellFlag = 103;
      case 103: {
        if (tag == 824) {
         parse_FIShortSellFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &fishortsellflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(833)) goto parse_FITotalShortSellQuota;
        break;
      }

      // optional double FITotalShortSellQuota = 104;
      case 104: {
        if (tag == 833) {
         parse_FITotalShortSellQuota:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fitotalshortsellquota_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(841)) goto parse_FIDealerShortSellQuota;
        break;
      }

      // optional double FIDealerShortSellQuota = 105;
      case 105: {
        if (tag == 841) {
         parse_FIDealerShortSellQuota:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fidealershortsellquota_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(849)) goto parse_FIPreClosePx;
        break;
      }

      // optional double FIPreClosePx = 106;
      case 106: {
        if (tag == 849) {
         parse_FIPreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fipreclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(857)) goto parse_FIPreWeightedPx;
        break;
      }

      // optional double FIPreWeightedPx = 107;
      case 107: {
        if (tag == 857) {
         parse_FIPreWeightedPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &fipreweightedpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(882)) goto parse_OptionListType;
        break;
      }

      // optional string OptionListType = 110;
      case 110: {
        if (tag == 882) {
         parse_OptionListType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionlisttype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionlisttype().data(), this->optionlisttype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionListType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(890)) goto parse_OptionDeliveryType;
        break;
      }

      // optional string OptionDeliveryType = 111;
      case 111: {
        if (tag == 890) {
         parse_OptionDeliveryType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optiondeliverytype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optiondeliverytype().data(), this->optiondeliverytype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(896)) goto parse_OptionAdjustTimes;
        break;
      }

      // optional int32 OptionAdjustTimes = 112;
      case 112: {
        if (tag == 896) {
         parse_OptionAdjustTimes:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &optionadjusttimes_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(904)) goto parse_OptionContractPosition;
        break;
      }

      // optional int64 OptionContractPosition = 113;
      case 113: {
        if (tag == 904) {
         parse_OptionContractPosition:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optioncontractposition_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(912)) goto parse_OptionBuyQtyUpperLimit;
        break;
      }

      // optional int64 OptionBuyQtyUpperLimit = 114;
      case 114: {
        if (tag == 912) {
         parse_OptionBuyQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionbuyqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(920)) goto parse_OptionSellQtyUpperLimit;
        break;
      }

      // optional int64 OptionSellQtyUpperLimit = 115;
      case 115: {
        if (tag == 920) {
         parse_OptionSellQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionsellqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(928)) goto parse_OptionMarketOrderBuyQtyUpperLimit;
        break;
      }

      // optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
      case 116: {
        if (tag == 928) {
         parse_OptionMarketOrderBuyQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionmarketorderbuyqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(936)) goto parse_OptionMarketOrderSellQtyUpperLimit;
        break;
      }

      // optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
      case 117: {
        if (tag == 936) {
         parse_OptionMarketOrderSellQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionmarketordersellqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(944)) goto parse_OptionQuoteOrderBuyQtyUpperLimit;
        break;
      }

      // optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
      case 118: {
        if (tag == 944) {
         parse_OptionQuoteOrderBuyQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionquoteorderbuyqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(952)) goto parse_OptionQuoteOrderSellQtyUpperLimit;
        break;
      }

      // optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
      case 119: {
        if (tag == 952) {
         parse_OptionQuoteOrderSellQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionquoteordersellqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(960)) goto parse_OptionBuyQtyUnit;
        break;
      }

      // optional int64 OptionBuyQtyUnit = 120;
      case 120: {
        if (tag == 960) {
         parse_OptionBuyQtyUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionbuyqtyunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(968)) goto parse_OptionSellQtyUnit;
        break;
      }

      // optional int64 OptionSellQtyUnit = 121;
      case 121: {
        if (tag == 968) {
         parse_OptionSellQtyUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &optionsellqtyunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(977)) goto parse_OptionLastSellMargin;
        break;
      }

      // optional double OptionLastSellMargin = 122;
      case 122: {
        if (tag == 977) {
         parse_OptionLastSellMargin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionlastsellmargin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(985)) goto parse_OptionSellMargin;
        break;
      }

      // optional double OptionSellMargin = 123;
      case 123: {
        if (tag == 985) {
         parse_OptionSellMargin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &optionsellmargin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(994)) goto parse_OptionMarketMakerFlag;
        break;
      }

      // optional string OptionMarketMakerFlag = 124;
      case 124: {
        if (tag == 994) {
         parse_OptionMarketMakerFlag:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optionmarketmakerflag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optionmarketmakerflag().data(), this->optionmarketmakerflag().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1002)) goto parse_OptionCombinationStrategy;
        break;
      }

      // optional string OptionCombinationStrategy = 125;
      case 125: {
        if (tag == 1002) {
         parse_OptionCombinationStrategy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optioncombinationstrategy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optioncombinationstrategy().data(), this->optioncombinationstrategy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1010)) goto parse_DeliveryYear;
        break;
      }

      // optional string DeliveryYear = 126;
      case 126: {
        if (tag == 1010) {
         parse_DeliveryYear:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_deliveryyear()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->deliveryyear().data(), this->deliveryyear().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1018)) goto parse_DeliveryMonth;
        break;
      }

      // optional string DeliveryMonth = 127;
      case 127: {
        if (tag == 1018) {
         parse_DeliveryMonth:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_deliverymonth()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->deliverymonth().data(), this->deliverymonth().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1026)) goto parse_InstrumentID;
        break;
      }

      // optional string InstrumentID = 128;
      case 128: {
        if (tag == 1026) {
         parse_InstrumentID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrumentid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrumentid().data(), this->instrumentid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1034)) goto parse_InstrumentName;
        break;
      }

      // optional string InstrumentName = 129;
      case 129: {
        if (tag == 1034) {
         parse_InstrumentName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrumentname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrumentname().data(), this->instrumentname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1042)) goto parse_ExchangeInstID;
        break;
      }

      // optional string ExchangeInstID = 130;
      case 130: {
        if (tag == 1042) {
         parse_ExchangeInstID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exchangeinstid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exchangeinstid().data(), this->exchangeinstid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1050)) goto parse_ProductID;
        break;
      }

      // optional string ProductID = 131;
      case 131: {
        if (tag == 1050) {
         parse_ProductID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_productid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->productid().data(), this->productid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ProductID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1056)) goto parse_MaxMarketOrderVolume;
        break;
      }

      // optional int64 MaxMarketOrderVolume = 132;
      case 132: {
        if (tag == 1056) {
         parse_MaxMarketOrderVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxmarketordervolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1064)) goto parse_MinMarketOrderVolume;
        break;
      }

      // optional int64 MinMarketOrderVolume = 133;
      case 133: {
        if (tag == 1064) {
         parse_MinMarketOrderVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minmarketordervolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1072)) goto parse_MaxLimitOrderVolume;
        break;
      }

      // optional int64 MaxLimitOrderVolume = 134;
      case 134: {
        if (tag == 1072) {
         parse_MaxLimitOrderVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxlimitordervolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1080)) goto parse_MinLimitOrderVolume;
        break;
      }

      // optional int64 MinLimitOrderVolume = 135;
      case 135: {
        if (tag == 1080) {
         parse_MinLimitOrderVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minlimitordervolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1088)) goto parse_VolumeMultiple;
        break;
      }

      // optional int64 VolumeMultiple = 136;
      case 136: {
        if (tag == 1088) {
         parse_VolumeMultiple:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volumemultiple_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1098)) goto parse_CreateDate;
        break;
      }

      // optional string CreateDate = 137;
      case 137: {
        if (tag == 1098) {
         parse_CreateDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_createdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->createdate().data(), this->createdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.CreateDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1106)) goto parse_ExpireDate;
        break;
      }

      // optional string ExpireDate = 138;
      case 138: {
        if (tag == 1106) {
         parse_ExpireDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_expiredate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->expiredate().data(), this->expiredate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1114)) goto parse_StartDelivDate;
        break;
      }

      // optional string StartDelivDate = 139;
      case 139: {
        if (tag == 1114) {
         parse_StartDelivDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_startdelivdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->startdelivdate().data(), this->startdelivdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1122)) goto parse_EndDelivDate;
        break;
      }

      // optional string EndDelivDate = 140;
      case 140: {
        if (tag == 1122) {
         parse_EndDelivDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_enddelivdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->enddelivdate().data(), this->enddelivdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1130)) goto parse_PositionType;
        break;
      }

      // optional string PositionType = 141;
      case 141: {
        if (tag == 1130) {
         parse_PositionType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_positiontype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->positiontype().data(), this->positiontype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.PositionType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1137)) goto parse_LongMarginRatio;
        break;
      }

      // optional double LongMarginRatio = 142;
      case 142: {
        if (tag == 1137) {
         parse_LongMarginRatio:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &longmarginratio_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1145)) goto parse_ShortMarginRatio;
        break;
      }

      // optional double ShortMarginRatio = 143;
      case 143: {
        if (tag == 1145) {
         parse_ShortMarginRatio:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &shortmarginratio_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1154)) goto parse_MaxMarginSideAlgorithm;
        break;
      }

      // optional string MaxMarginSideAlgorithm = 144;
      case 144: {
        if (tag == 1154) {
         parse_MaxMarginSideAlgorithm:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_maxmarginsidealgorithm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->maxmarginsidealgorithm().data(), this->maxmarginsidealgorithm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1161)) goto parse_StrikePrice;
        break;
      }

      // optional double StrikePrice = 145;
      case 145: {
        if (tag == 1161) {
         parse_StrikePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &strikeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1169)) goto parse_PreOpenInterest;
        break;
      }

      // optional double PreOpenInterest = 146;
      case 146: {
        if (tag == 1169) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1178)) goto parse_FormerSymbol;
        break;
      }

      // optional string FormerSymbol = 147;
      case 147: {
        if (tag == 1178) {
         parse_FormerSymbol:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_formersymbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->formersymbol().data(), this->formersymbol().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1186)) goto parse_DelistDate;
        break;
      }

      // optional string DelistDate = 148;
      case 148: {
        if (tag == 1186) {
         parse_DelistDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_delistdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->delistdate().data(), this->delistdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.DelistDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1192)) goto parse_BuyQtyUnit;
        break;
      }

      // optional int64 BuyQtyUnit = 149;
      case 149: {
        if (tag == 1192) {
         parse_BuyQtyUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyqtyunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1200)) goto parse_SellQtyUnit;
        break;
      }

      // optional int64 SellQtyUnit = 150;
      case 150: {
        if (tag == 1200) {
         parse_SellQtyUnit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellqtyunit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1288)) goto parse_BuyQtyUpperLimit;
        break;
      }

      // optional int64 BuyQtyUpperLimit = 161;
      case 161: {
        if (tag == 1288) {
         parse_BuyQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1296)) goto parse_SellQtyUpperLimit;
        break;
      }

      // optional int64 SellQtyUpperLimit = 162;
      case 162: {
        if (tag == 1296) {
         parse_SellQtyUpperLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellqtyupperlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1304)) goto parse_BuyQtyLowerLimit;
        break;
      }

      // optional int64 BuyQtyLowerLimit = 163;
      case 163: {
        if (tag == 1304) {
         parse_BuyQtyLowerLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyqtylowerlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1312)) goto parse_SellQtyLowerLimit;
        break;
      }

      // optional int64 SellQtyLowerLimit = 164;
      case 164: {
        if (tag == 1312) {
         parse_SellQtyLowerLimit:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellqtylowerlimit_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1320)) goto parse_VCMFlag;
        break;
      }

      // optional int32 VCMFlag = 165;
      case 165: {
        if (tag == 1320) {
         parse_VCMFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &vcmflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1328)) goto parse_CASFlag;
        break;
      }

      // optional int32 CASFlag = 166;
      case 166: {
        if (tag == 1328) {
         parse_CASFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &casflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1336)) goto parse_POSFlag;
        break;
      }

      // optional int32 POSFlag = 167;
      case 167: {
        if (tag == 1336) {
         parse_POSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &posflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1345)) goto parse_POSUpperLimitPx;
        break;
      }

      // optional double POSUpperLimitPx = 168;
      case 168: {
        if (tag == 1345) {
         parse_POSUpperLimitPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &posupperlimitpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1353)) goto parse_POSLowerLimitPx;
        break;
      }

      // optional double POSLowerLimitPx = 169;
      case 169: {
        if (tag == 1353) {
         parse_POSLowerLimitPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &poslowerlimitpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1362)) goto parse_BaseContractID;
        break;
      }

      // optional string BaseContractID = 170;
      case 170: {
        if (tag == 1362) {
         parse_BaseContractID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_basecontractid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->basecontractid().data(), this->basecontractid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1370)) goto parse_constantParams;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
      case 171: {
        if (tag == 1370) {
         parse_constantParams:
          DO_(input->IncrementRecursionDepth());
         parse_loop_constantParams:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_constantparams()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1370)) goto parse_loop_constantParams;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(1376)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 172;
      case 172: {
        if (tag == 1376) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(1386)) goto parse_InterestAccrualDate;
        break;
      }

      // optional string InterestAccrualDate = 173;
      case 173: {
        if (tag == 1386) {
         parse_InterestAccrualDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_interestaccrualdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->interestaccrualdate().data(), this->interestaccrualdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDBasicInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDBasicInfo)
  return false;
#undef DO_
}

void MDBasicInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDBasicInfo)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.SecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->securityid(), output);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.Symbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->symbol(), output);
  }

  // optional string ChiSpelling = 4;
  if (this->chispelling().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->chispelling().data(), this->chispelling().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->chispelling(), output);
  }

  // optional string EnglishName = 5;
  if (this->englishname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->englishname().data(), this->englishname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.EnglishName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->englishname(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional string SecuritySubType = 8;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->securitysubtype(), output);
  }

  // optional string ListDate = 9;
  if (this->listdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->listdate().data(), this->listdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ListDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->listdate(), output);
  }

  // optional string Currency = 10;
  if (this->currency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currency().data(), this->currency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.Currency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->currency(), output);
  }

  // optional int64 OutstandingShare = 11;
  if (this->outstandingshare() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->outstandingshare(), output);
  }

  // optional int64 PublicFloatShareQuantity = 12;
  if (this->publicfloatsharequantity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->publicfloatsharequantity(), output);
  }

  // optional int32 MDDate = 13;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->mddate(), output);
  }

  // optional string TradingPhaseCode = 14;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->tradingphasecode(), output);
  }

  // optional int64 PreClosePx = 15;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->preclosepx(), output);
  }

  // optional int64 MaxPx = 16;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->maxpx(), output);
  }

  // optional int64 MinPx = 17;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->minpx(), output);
  }

  // optional int64 LotSize = 18;
  if (this->lotsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lotsize(), output);
  }

  // optional bool ShortSellFlag = 19;
  if (this->shortsellflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(19, this->shortsellflag(), output);
  }

  // optional string ExchangeDate = 20;
  if (this->exchangedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangedate().data(), this->exchangedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->exchangedate(), output);
  }

  // optional string ExchangeSymbol = 21;
  if (this->exchangesymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangesymbol().data(), this->exchangesymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->exchangesymbol(), output);
  }

  // optional double TickSize = 22;
  if (this->ticksize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->ticksize(), output);
  }

  // optional int32 LoanMarginIndicator = 23;
  if (this->loanmarginindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(23, this->loanmarginindicator(), output);
  }

  // optional int32 PxAccuracy = 24;
  if (this->pxaccuracy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(24, this->pxaccuracy(), output);
  }

  // optional int32 IPOProfitable = 25;
  if (this->ipoprofitable() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->ipoprofitable(), output);
  }

  // optional int32 DiffRightsIndicator = 26;
  if (this->diffrightsindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->diffrightsindicator(), output);
  }

  // optional string HKSpreadTableCode = 27;
  if (this->hkspreadtablecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hkspreadtablecode().data(), this->hkspreadtablecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      27, this->hkspreadtablecode(), output);
  }

  // optional double PreSettlePx = 28;
  if (this->presettlepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(28, this->presettlepx(), output);
  }

  // optional double PreIOPV = 29;
  if (this->preiopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(29, this->preiopv(), output);
  }

  // optional int32 ShHkConnect = 30;
  if (this->shhkconnect() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->shhkconnect(), output);
  }

  // optional int32 SzHkConnect = 31;
  if (this->szhkconnect() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(31, this->szhkconnect(), output);
  }

  // optional string OptionContractID = 40;
  if (this->optioncontractid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncontractid().data(), this->optioncontractid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      40, this->optioncontractid(), output);
  }

  // optional string OptionContractSymbol = 41;
  if (this->optioncontractsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncontractsymbol().data(), this->optioncontractsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      41, this->optioncontractsymbol(), output);
  }

  // optional string OptionUnderlyingSecurityID = 42;
  if (this->optionunderlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingsecurityid().data(), this->optionunderlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      42, this->optionunderlyingsecurityid(), output);
  }

  // optional string OptionUnderlyingSymbol = 43;
  if (this->optionunderlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingsymbol().data(), this->optionunderlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      43, this->optionunderlyingsymbol(), output);
  }

  // optional string OptionUnderlyingType = 44;
  if (this->optionunderlyingtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingtype().data(), this->optionunderlyingtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      44, this->optionunderlyingtype(), output);
  }

  // optional string OptionOptionType = 45;
  if (this->optionoptiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionoptiontype().data(), this->optionoptiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      45, this->optionoptiontype(), output);
  }

  // optional string OptionCallOrPut = 46;
  if (this->optioncallorput().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncallorput().data(), this->optioncallorput().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      46, this->optioncallorput(), output);
  }

  // optional int64 OptionContractMultiplierUnit = 47;
  if (this->optioncontractmultiplierunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(47, this->optioncontractmultiplierunit(), output);
  }

  // optional double OptionExercisePrice = 48;
  if (this->optionexerciseprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(48, this->optionexerciseprice(), output);
  }

  // optional string OptionStartDate = 49;
  if (this->optionstartdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionstartdate().data(), this->optionstartdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      49, this->optionstartdate(), output);
  }

  // optional string OptionEndDate = 50;
  if (this->optionenddate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionenddate().data(), this->optionenddate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      50, this->optionenddate(), output);
  }

  // optional string OptionExerciseDate = 51;
  if (this->optionexercisedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionexercisedate().data(), this->optionexercisedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      51, this->optionexercisedate(), output);
  }

  // optional string OptionDeliveryDate = 52;
  if (this->optiondeliverydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiondeliverydate().data(), this->optiondeliverydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      52, this->optiondeliverydate(), output);
  }

  // optional string OptionExpireDate = 53;
  if (this->optionexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionexpiredate().data(), this->optionexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      53, this->optionexpiredate(), output);
  }

  // optional string OptionUpdateVersion = 54;
  if (this->optionupdateversion().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionupdateversion().data(), this->optionupdateversion().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      54, this->optionupdateversion(), output);
  }

  // optional int64 OptionTotalLongPosition = 55;
  if (this->optiontotallongposition() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(55, this->optiontotallongposition(), output);
  }

  // optional double OptionSecurityClosePx = 56;
  if (this->optionsecurityclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(56, this->optionsecurityclosepx(), output);
  }

  // optional double OptionSettlPrice = 57;
  if (this->optionsettlprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(57, this->optionsettlprice(), output);
  }

  // optional double OptionUnderlyingClosePx = 58;
  if (this->optionunderlyingclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(58, this->optionunderlyingclosepx(), output);
  }

  // optional string OptionPriceLimitType = 59;
  if (this->optionpricelimittype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionpricelimittype().data(), this->optionpricelimittype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      59, this->optionpricelimittype(), output);
  }

  // optional double OptionDailyPriceUpLimit = 60;
  if (this->optiondailypriceuplimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(60, this->optiondailypriceuplimit(), output);
  }

  // optional double OptionDailyPriceDownLimit = 61;
  if (this->optiondailypricedownlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(61, this->optiondailypricedownlimit(), output);
  }

  // optional double OptionMarginUnit = 62;
  if (this->optionmarginunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(62, this->optionmarginunit(), output);
  }

  // optional double OptionMarginRatioParam1 = 63;
  if (this->optionmarginratioparam1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(63, this->optionmarginratioparam1(), output);
  }

  // optional double OptionMarginRatioParam2 = 64;
  if (this->optionmarginratioparam2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(64, this->optionmarginratioparam2(), output);
  }

  // optional int64 OptionRoundLot = 65;
  if (this->optionroundlot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(65, this->optionroundlot(), output);
  }

  // optional int64 OptionLmtOrdMinFloor = 66;
  if (this->optionlmtordminfloor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(66, this->optionlmtordminfloor(), output);
  }

  // optional int64 OptionLmtOrdMaxFloor = 67;
  if (this->optionlmtordmaxfloor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(67, this->optionlmtordmaxfloor(), output);
  }

  // optional int64 OptionMktOrdMinFloor = 68;
  if (this->optionmktordminfloor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(68, this->optionmktordminfloor(), output);
  }

  // optional int64 OptionMktOrdMaxFloor = 69;
  if (this->optionmktordmaxfloor() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(69, this->optionmktordmaxfloor(), output);
  }

  // optional double OptionTickSize = 70;
  if (this->optionticksize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(70, this->optionticksize(), output);
  }

  // optional string OptionSecurityStatusFlag = 71;
  if (this->optionsecuritystatusflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionsecuritystatusflag().data(), this->optionsecuritystatusflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      71, this->optionsecuritystatusflag(), output);
  }

  // optional string OptionCarryInterestDate = 72;
  if (this->optioncarryinterestdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncarryinterestdate().data(), this->optioncarryinterestdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      72, this->optioncarryinterestdate(), output);
  }

  // optional string OptionEarlyExpireDate = 73;
  if (this->optionearlyexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionearlyexpiredate().data(), this->optionearlyexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      73, this->optionearlyexpiredate(), output);
  }

  // optional string OptionStrategySecurityID = 74;
  if (this->optionstrategysecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionstrategysecurityid().data(), this->optionstrategysecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      74, this->optionstrategysecurityid(), output);
  }

  // optional string FITradeProductType = 80;
  if (this->fitradeproducttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fitradeproducttype().data(), this->fitradeproducttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      80, this->fitradeproducttype(), output);
  }

  // optional string FISecurityProperty = 81;
  if (this->fisecurityproperty().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fisecurityproperty().data(), this->fisecurityproperty().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      81, this->fisecurityproperty(), output);
  }

  // optional string FISecurityStatus = 82;
  if (this->fisecuritystatus().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fisecuritystatus().data(), this->fisecuritystatus().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      82, this->fisecuritystatus(), output);
  }

  // optional string FIPledgedSecurityID = 83;
  if (this->fipledgedsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fipledgedsecurityid().data(), this->fipledgedsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      83, this->fipledgedsecurityid(), output);
  }

  // optional string FIOpenTime = 84;
  if (this->fiopentime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiopentime().data(), this->fiopentime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      84, this->fiopentime(), output);
  }

  // optional string FICloseTime = 85;
  if (this->ficlosetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ficlosetime().data(), this->ficlosetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      85, this->ficlosetime(), output);
  }

  // optional string FIIssueMode = 86;
  if (this->fiissuemode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuemode().data(), this->fiissuemode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      86, this->fiissuemode(), output);
  }

  // optional double FIFaceAmount = 87;
  if (this->fifaceamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(87, this->fifaceamount(), output);
  }

  // optional double FIIssuePrice = 88;
  if (this->fiissueprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(88, this->fiissueprice(), output);
  }

  // optional string FIInterestType = 89;
  if (this->fiinteresttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiinteresttype().data(), this->fiinteresttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      89, this->fiinteresttype(), output);
  }

  // optional string FIInterestFrequency = 90;
  if (this->fiinterestfrequency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiinterestfrequency().data(), this->fiinterestfrequency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      90, this->fiinterestfrequency(), output);
  }

  // optional double FIGuaranteedInterestRate = 91;
  if (this->figuaranteedinterestrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(91, this->figuaranteedinterestrate(), output);
  }

  // optional double FIBaseInterestRate = 92;
  if (this->fibaseinterestrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(92, this->fibaseinterestrate(), output);
  }

  // optional double FIQuotedMargin = 93;
  if (this->fiquotedmargin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(93, this->fiquotedmargin(), output);
  }

  // optional int32 FITimeLimit = 94;
  if (this->fitimelimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(94, this->fitimelimit(), output);
  }

  // optional double FITotalIssuance = 95;
  if (this->fitotalissuance() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(95, this->fitotalissuance(), output);
  }

  // optional string FIIssueStartDate = 96;
  if (this->fiissuestartdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuestartdate().data(), this->fiissuestartdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      96, this->fiissuestartdate(), output);
  }

  // optional string FIIssueEndDate = 97;
  if (this->fiissueenddate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissueenddate().data(), this->fiissueenddate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      97, this->fiissueenddate(), output);
  }

  // optional string FIListDate = 98;
  if (this->filistdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->filistdate().data(), this->filistdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIListDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      98, this->filistdate(), output);
  }

  // optional string FIExpireDate = 99;
  if (this->fiexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiexpiredate().data(), this->fiexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      99, this->fiexpiredate(), output);
  }

  // optional string FINationalDebtType = 100;
  if (this->finationaldebttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->finationaldebttype().data(), this->finationaldebttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      100, this->finationaldebttype(), output);
  }

  // optional string FIIssueMethod = 101;
  if (this->fiissuemethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuemethod().data(), this->fiissuemethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      101, this->fiissuemethod(), output);
  }

  // optional bool FICrossMarket = 102;
  if (this->ficrossmarket() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(102, this->ficrossmarket(), output);
  }

  // optional bool FIShortSellFlag = 103;
  if (this->fishortsellflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(103, this->fishortsellflag(), output);
  }

  // optional double FITotalShortSellQuota = 104;
  if (this->fitotalshortsellquota() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(104, this->fitotalshortsellquota(), output);
  }

  // optional double FIDealerShortSellQuota = 105;
  if (this->fidealershortsellquota() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(105, this->fidealershortsellquota(), output);
  }

  // optional double FIPreClosePx = 106;
  if (this->fipreclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(106, this->fipreclosepx(), output);
  }

  // optional double FIPreWeightedPx = 107;
  if (this->fipreweightedpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(107, this->fipreweightedpx(), output);
  }

  // optional string OptionListType = 110;
  if (this->optionlisttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionlisttype().data(), this->optionlisttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionListType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      110, this->optionlisttype(), output);
  }

  // optional string OptionDeliveryType = 111;
  if (this->optiondeliverytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiondeliverytype().data(), this->optiondeliverytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      111, this->optiondeliverytype(), output);
  }

  // optional int32 OptionAdjustTimes = 112;
  if (this->optionadjusttimes() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(112, this->optionadjusttimes(), output);
  }

  // optional int64 OptionContractPosition = 113;
  if (this->optioncontractposition() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(113, this->optioncontractposition(), output);
  }

  // optional int64 OptionBuyQtyUpperLimit = 114;
  if (this->optionbuyqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(114, this->optionbuyqtyupperlimit(), output);
  }

  // optional int64 OptionSellQtyUpperLimit = 115;
  if (this->optionsellqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(115, this->optionsellqtyupperlimit(), output);
  }

  // optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
  if (this->optionmarketorderbuyqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(116, this->optionmarketorderbuyqtyupperlimit(), output);
  }

  // optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
  if (this->optionmarketordersellqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(117, this->optionmarketordersellqtyupperlimit(), output);
  }

  // optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
  if (this->optionquoteorderbuyqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(118, this->optionquoteorderbuyqtyupperlimit(), output);
  }

  // optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
  if (this->optionquoteordersellqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(119, this->optionquoteordersellqtyupperlimit(), output);
  }

  // optional int64 OptionBuyQtyUnit = 120;
  if (this->optionbuyqtyunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(120, this->optionbuyqtyunit(), output);
  }

  // optional int64 OptionSellQtyUnit = 121;
  if (this->optionsellqtyunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(121, this->optionsellqtyunit(), output);
  }

  // optional double OptionLastSellMargin = 122;
  if (this->optionlastsellmargin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(122, this->optionlastsellmargin(), output);
  }

  // optional double OptionSellMargin = 123;
  if (this->optionsellmargin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(123, this->optionsellmargin(), output);
  }

  // optional string OptionMarketMakerFlag = 124;
  if (this->optionmarketmakerflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionmarketmakerflag().data(), this->optionmarketmakerflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      124, this->optionmarketmakerflag(), output);
  }

  // optional string OptionCombinationStrategy = 125;
  if (this->optioncombinationstrategy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncombinationstrategy().data(), this->optioncombinationstrategy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      125, this->optioncombinationstrategy(), output);
  }

  // optional string DeliveryYear = 126;
  if (this->deliveryyear().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deliveryyear().data(), this->deliveryyear().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      126, this->deliveryyear(), output);
  }

  // optional string DeliveryMonth = 127;
  if (this->deliverymonth().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deliverymonth().data(), this->deliverymonth().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      127, this->deliverymonth(), output);
  }

  // optional string InstrumentID = 128;
  if (this->instrumentid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentid().data(), this->instrumentid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      128, this->instrumentid(), output);
  }

  // optional string InstrumentName = 129;
  if (this->instrumentname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentname().data(), this->instrumentname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      129, this->instrumentname(), output);
  }

  // optional string ExchangeInstID = 130;
  if (this->exchangeinstid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangeinstid().data(), this->exchangeinstid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      130, this->exchangeinstid(), output);
  }

  // optional string ProductID = 131;
  if (this->productid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->productid().data(), this->productid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ProductID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      131, this->productid(), output);
  }

  // optional int64 MaxMarketOrderVolume = 132;
  if (this->maxmarketordervolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(132, this->maxmarketordervolume(), output);
  }

  // optional int64 MinMarketOrderVolume = 133;
  if (this->minmarketordervolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(133, this->minmarketordervolume(), output);
  }

  // optional int64 MaxLimitOrderVolume = 134;
  if (this->maxlimitordervolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(134, this->maxlimitordervolume(), output);
  }

  // optional int64 MinLimitOrderVolume = 135;
  if (this->minlimitordervolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(135, this->minlimitordervolume(), output);
  }

  // optional int64 VolumeMultiple = 136;
  if (this->volumemultiple() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(136, this->volumemultiple(), output);
  }

  // optional string CreateDate = 137;
  if (this->createdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->createdate().data(), this->createdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.CreateDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      137, this->createdate(), output);
  }

  // optional string ExpireDate = 138;
  if (this->expiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expiredate().data(), this->expiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      138, this->expiredate(), output);
  }

  // optional string StartDelivDate = 139;
  if (this->startdelivdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->startdelivdate().data(), this->startdelivdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      139, this->startdelivdate(), output);
  }

  // optional string EndDelivDate = 140;
  if (this->enddelivdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enddelivdate().data(), this->enddelivdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      140, this->enddelivdate(), output);
  }

  // optional string PositionType = 141;
  if (this->positiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->positiontype().data(), this->positiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.PositionType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      141, this->positiontype(), output);
  }

  // optional double LongMarginRatio = 142;
  if (this->longmarginratio() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(142, this->longmarginratio(), output);
  }

  // optional double ShortMarginRatio = 143;
  if (this->shortmarginratio() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(143, this->shortmarginratio(), output);
  }

  // optional string MaxMarginSideAlgorithm = 144;
  if (this->maxmarginsidealgorithm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maxmarginsidealgorithm().data(), this->maxmarginsidealgorithm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      144, this->maxmarginsidealgorithm(), output);
  }

  // optional double StrikePrice = 145;
  if (this->strikeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(145, this->strikeprice(), output);
  }

  // optional double PreOpenInterest = 146;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(146, this->preopeninterest(), output);
  }

  // optional string FormerSymbol = 147;
  if (this->formersymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->formersymbol().data(), this->formersymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      147, this->formersymbol(), output);
  }

  // optional string DelistDate = 148;
  if (this->delistdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->delistdate().data(), this->delistdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DelistDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      148, this->delistdate(), output);
  }

  // optional int64 BuyQtyUnit = 149;
  if (this->buyqtyunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(149, this->buyqtyunit(), output);
  }

  // optional int64 SellQtyUnit = 150;
  if (this->sellqtyunit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(150, this->sellqtyunit(), output);
  }

  // optional int64 BuyQtyUpperLimit = 161;
  if (this->buyqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(161, this->buyqtyupperlimit(), output);
  }

  // optional int64 SellQtyUpperLimit = 162;
  if (this->sellqtyupperlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(162, this->sellqtyupperlimit(), output);
  }

  // optional int64 BuyQtyLowerLimit = 163;
  if (this->buyqtylowerlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(163, this->buyqtylowerlimit(), output);
  }

  // optional int64 SellQtyLowerLimit = 164;
  if (this->sellqtylowerlimit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(164, this->sellqtylowerlimit(), output);
  }

  // optional int32 VCMFlag = 165;
  if (this->vcmflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(165, this->vcmflag(), output);
  }

  // optional int32 CASFlag = 166;
  if (this->casflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(166, this->casflag(), output);
  }

  // optional int32 POSFlag = 167;
  if (this->posflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(167, this->posflag(), output);
  }

  // optional double POSUpperLimitPx = 168;
  if (this->posupperlimitpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(168, this->posupperlimitpx(), output);
  }

  // optional double POSLowerLimitPx = 169;
  if (this->poslowerlimitpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(169, this->poslowerlimitpx(), output);
  }

  // optional string BaseContractID = 170;
  if (this->basecontractid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->basecontractid().data(), this->basecontractid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      170, this->basecontractid(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
  for (unsigned int i = 0, n = this->constantparams_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      171, this->constantparams(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 172;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(172, this->datamultiplepowerof10(), output);
  }

  // optional string InterestAccrualDate = 173;
  if (this->interestaccrualdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->interestaccrualdate().data(), this->interestaccrualdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      173, this->interestaccrualdate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDBasicInfo)
}

::google::protobuf::uint8* MDBasicInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDBasicInfo)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securityid().data(), this->securityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.SecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->securityid(), target);
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->symbol().data(), this->symbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.Symbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->symbol(), target);
  }

  // optional string ChiSpelling = 4;
  if (this->chispelling().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->chispelling().data(), this->chispelling().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->chispelling(), target);
  }

  // optional string EnglishName = 5;
  if (this->englishname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->englishname().data(), this->englishname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.EnglishName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->englishname(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional string SecuritySubType = 8;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->securitysubtype(), target);
  }

  // optional string ListDate = 9;
  if (this->listdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->listdate().data(), this->listdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ListDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->listdate(), target);
  }

  // optional string Currency = 10;
  if (this->currency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->currency().data(), this->currency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.Currency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->currency(), target);
  }

  // optional int64 OutstandingShare = 11;
  if (this->outstandingshare() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->outstandingshare(), target);
  }

  // optional int64 PublicFloatShareQuantity = 12;
  if (this->publicfloatsharequantity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->publicfloatsharequantity(), target);
  }

  // optional int32 MDDate = 13;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->mddate(), target);
  }

  // optional string TradingPhaseCode = 14;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->tradingphasecode(), target);
  }

  // optional int64 PreClosePx = 15;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->preclosepx(), target);
  }

  // optional int64 MaxPx = 16;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->maxpx(), target);
  }

  // optional int64 MinPx = 17;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->minpx(), target);
  }

  // optional int64 LotSize = 18;
  if (this->lotsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lotsize(), target);
  }

  // optional bool ShortSellFlag = 19;
  if (this->shortsellflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(19, this->shortsellflag(), target);
  }

  // optional string ExchangeDate = 20;
  if (this->exchangedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangedate().data(), this->exchangedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->exchangedate(), target);
  }

  // optional string ExchangeSymbol = 21;
  if (this->exchangesymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangesymbol().data(), this->exchangesymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->exchangesymbol(), target);
  }

  // optional double TickSize = 22;
  if (this->ticksize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->ticksize(), target);
  }

  // optional int32 LoanMarginIndicator = 23;
  if (this->loanmarginindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(23, this->loanmarginindicator(), target);
  }

  // optional int32 PxAccuracy = 24;
  if (this->pxaccuracy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(24, this->pxaccuracy(), target);
  }

  // optional int32 IPOProfitable = 25;
  if (this->ipoprofitable() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->ipoprofitable(), target);
  }

  // optional int32 DiffRightsIndicator = 26;
  if (this->diffrightsindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->diffrightsindicator(), target);
  }

  // optional string HKSpreadTableCode = 27;
  if (this->hkspreadtablecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hkspreadtablecode().data(), this->hkspreadtablecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        27, this->hkspreadtablecode(), target);
  }

  // optional double PreSettlePx = 28;
  if (this->presettlepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(28, this->presettlepx(), target);
  }

  // optional double PreIOPV = 29;
  if (this->preiopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(29, this->preiopv(), target);
  }

  // optional int32 ShHkConnect = 30;
  if (this->shhkconnect() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->shhkconnect(), target);
  }

  // optional int32 SzHkConnect = 31;
  if (this->szhkconnect() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(31, this->szhkconnect(), target);
  }

  // optional string OptionContractID = 40;
  if (this->optioncontractid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncontractid().data(), this->optioncontractid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        40, this->optioncontractid(), target);
  }

  // optional string OptionContractSymbol = 41;
  if (this->optioncontractsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncontractsymbol().data(), this->optioncontractsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        41, this->optioncontractsymbol(), target);
  }

  // optional string OptionUnderlyingSecurityID = 42;
  if (this->optionunderlyingsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingsecurityid().data(), this->optionunderlyingsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        42, this->optionunderlyingsecurityid(), target);
  }

  // optional string OptionUnderlyingSymbol = 43;
  if (this->optionunderlyingsymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingsymbol().data(), this->optionunderlyingsymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        43, this->optionunderlyingsymbol(), target);
  }

  // optional string OptionUnderlyingType = 44;
  if (this->optionunderlyingtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionunderlyingtype().data(), this->optionunderlyingtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        44, this->optionunderlyingtype(), target);
  }

  // optional string OptionOptionType = 45;
  if (this->optionoptiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionoptiontype().data(), this->optionoptiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        45, this->optionoptiontype(), target);
  }

  // optional string OptionCallOrPut = 46;
  if (this->optioncallorput().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncallorput().data(), this->optioncallorput().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        46, this->optioncallorput(), target);
  }

  // optional int64 OptionContractMultiplierUnit = 47;
  if (this->optioncontractmultiplierunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(47, this->optioncontractmultiplierunit(), target);
  }

  // optional double OptionExercisePrice = 48;
  if (this->optionexerciseprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(48, this->optionexerciseprice(), target);
  }

  // optional string OptionStartDate = 49;
  if (this->optionstartdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionstartdate().data(), this->optionstartdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        49, this->optionstartdate(), target);
  }

  // optional string OptionEndDate = 50;
  if (this->optionenddate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionenddate().data(), this->optionenddate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        50, this->optionenddate(), target);
  }

  // optional string OptionExerciseDate = 51;
  if (this->optionexercisedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionexercisedate().data(), this->optionexercisedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        51, this->optionexercisedate(), target);
  }

  // optional string OptionDeliveryDate = 52;
  if (this->optiondeliverydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiondeliverydate().data(), this->optiondeliverydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        52, this->optiondeliverydate(), target);
  }

  // optional string OptionExpireDate = 53;
  if (this->optionexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionexpiredate().data(), this->optionexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        53, this->optionexpiredate(), target);
  }

  // optional string OptionUpdateVersion = 54;
  if (this->optionupdateversion().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionupdateversion().data(), this->optionupdateversion().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        54, this->optionupdateversion(), target);
  }

  // optional int64 OptionTotalLongPosition = 55;
  if (this->optiontotallongposition() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(55, this->optiontotallongposition(), target);
  }

  // optional double OptionSecurityClosePx = 56;
  if (this->optionsecurityclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(56, this->optionsecurityclosepx(), target);
  }

  // optional double OptionSettlPrice = 57;
  if (this->optionsettlprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(57, this->optionsettlprice(), target);
  }

  // optional double OptionUnderlyingClosePx = 58;
  if (this->optionunderlyingclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(58, this->optionunderlyingclosepx(), target);
  }

  // optional string OptionPriceLimitType = 59;
  if (this->optionpricelimittype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionpricelimittype().data(), this->optionpricelimittype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        59, this->optionpricelimittype(), target);
  }

  // optional double OptionDailyPriceUpLimit = 60;
  if (this->optiondailypriceuplimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(60, this->optiondailypriceuplimit(), target);
  }

  // optional double OptionDailyPriceDownLimit = 61;
  if (this->optiondailypricedownlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(61, this->optiondailypricedownlimit(), target);
  }

  // optional double OptionMarginUnit = 62;
  if (this->optionmarginunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(62, this->optionmarginunit(), target);
  }

  // optional double OptionMarginRatioParam1 = 63;
  if (this->optionmarginratioparam1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(63, this->optionmarginratioparam1(), target);
  }

  // optional double OptionMarginRatioParam2 = 64;
  if (this->optionmarginratioparam2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(64, this->optionmarginratioparam2(), target);
  }

  // optional int64 OptionRoundLot = 65;
  if (this->optionroundlot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(65, this->optionroundlot(), target);
  }

  // optional int64 OptionLmtOrdMinFloor = 66;
  if (this->optionlmtordminfloor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(66, this->optionlmtordminfloor(), target);
  }

  // optional int64 OptionLmtOrdMaxFloor = 67;
  if (this->optionlmtordmaxfloor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(67, this->optionlmtordmaxfloor(), target);
  }

  // optional int64 OptionMktOrdMinFloor = 68;
  if (this->optionmktordminfloor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(68, this->optionmktordminfloor(), target);
  }

  // optional int64 OptionMktOrdMaxFloor = 69;
  if (this->optionmktordmaxfloor() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(69, this->optionmktordmaxfloor(), target);
  }

  // optional double OptionTickSize = 70;
  if (this->optionticksize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(70, this->optionticksize(), target);
  }

  // optional string OptionSecurityStatusFlag = 71;
  if (this->optionsecuritystatusflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionsecuritystatusflag().data(), this->optionsecuritystatusflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        71, this->optionsecuritystatusflag(), target);
  }

  // optional string OptionCarryInterestDate = 72;
  if (this->optioncarryinterestdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncarryinterestdate().data(), this->optioncarryinterestdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        72, this->optioncarryinterestdate(), target);
  }

  // optional string OptionEarlyExpireDate = 73;
  if (this->optionearlyexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionearlyexpiredate().data(), this->optionearlyexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        73, this->optionearlyexpiredate(), target);
  }

  // optional string OptionStrategySecurityID = 74;
  if (this->optionstrategysecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionstrategysecurityid().data(), this->optionstrategysecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        74, this->optionstrategysecurityid(), target);
  }

  // optional string FITradeProductType = 80;
  if (this->fitradeproducttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fitradeproducttype().data(), this->fitradeproducttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        80, this->fitradeproducttype(), target);
  }

  // optional string FISecurityProperty = 81;
  if (this->fisecurityproperty().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fisecurityproperty().data(), this->fisecurityproperty().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        81, this->fisecurityproperty(), target);
  }

  // optional string FISecurityStatus = 82;
  if (this->fisecuritystatus().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fisecuritystatus().data(), this->fisecuritystatus().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        82, this->fisecuritystatus(), target);
  }

  // optional string FIPledgedSecurityID = 83;
  if (this->fipledgedsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fipledgedsecurityid().data(), this->fipledgedsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        83, this->fipledgedsecurityid(), target);
  }

  // optional string FIOpenTime = 84;
  if (this->fiopentime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiopentime().data(), this->fiopentime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        84, this->fiopentime(), target);
  }

  // optional string FICloseTime = 85;
  if (this->ficlosetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ficlosetime().data(), this->ficlosetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        85, this->ficlosetime(), target);
  }

  // optional string FIIssueMode = 86;
  if (this->fiissuemode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuemode().data(), this->fiissuemode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        86, this->fiissuemode(), target);
  }

  // optional double FIFaceAmount = 87;
  if (this->fifaceamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(87, this->fifaceamount(), target);
  }

  // optional double FIIssuePrice = 88;
  if (this->fiissueprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(88, this->fiissueprice(), target);
  }

  // optional string FIInterestType = 89;
  if (this->fiinteresttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiinteresttype().data(), this->fiinteresttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        89, this->fiinteresttype(), target);
  }

  // optional string FIInterestFrequency = 90;
  if (this->fiinterestfrequency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiinterestfrequency().data(), this->fiinterestfrequency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        90, this->fiinterestfrequency(), target);
  }

  // optional double FIGuaranteedInterestRate = 91;
  if (this->figuaranteedinterestrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(91, this->figuaranteedinterestrate(), target);
  }

  // optional double FIBaseInterestRate = 92;
  if (this->fibaseinterestrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(92, this->fibaseinterestrate(), target);
  }

  // optional double FIQuotedMargin = 93;
  if (this->fiquotedmargin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(93, this->fiquotedmargin(), target);
  }

  // optional int32 FITimeLimit = 94;
  if (this->fitimelimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(94, this->fitimelimit(), target);
  }

  // optional double FITotalIssuance = 95;
  if (this->fitotalissuance() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(95, this->fitotalissuance(), target);
  }

  // optional string FIIssueStartDate = 96;
  if (this->fiissuestartdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuestartdate().data(), this->fiissuestartdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        96, this->fiissuestartdate(), target);
  }

  // optional string FIIssueEndDate = 97;
  if (this->fiissueenddate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissueenddate().data(), this->fiissueenddate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        97, this->fiissueenddate(), target);
  }

  // optional string FIListDate = 98;
  if (this->filistdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->filistdate().data(), this->filistdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIListDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        98, this->filistdate(), target);
  }

  // optional string FIExpireDate = 99;
  if (this->fiexpiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiexpiredate().data(), this->fiexpiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        99, this->fiexpiredate(), target);
  }

  // optional string FINationalDebtType = 100;
  if (this->finationaldebttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->finationaldebttype().data(), this->finationaldebttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        100, this->finationaldebttype(), target);
  }

  // optional string FIIssueMethod = 101;
  if (this->fiissuemethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fiissuemethod().data(), this->fiissuemethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        101, this->fiissuemethod(), target);
  }

  // optional bool FICrossMarket = 102;
  if (this->ficrossmarket() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(102, this->ficrossmarket(), target);
  }

  // optional bool FIShortSellFlag = 103;
  if (this->fishortsellflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(103, this->fishortsellflag(), target);
  }

  // optional double FITotalShortSellQuota = 104;
  if (this->fitotalshortsellquota() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(104, this->fitotalshortsellquota(), target);
  }

  // optional double FIDealerShortSellQuota = 105;
  if (this->fidealershortsellquota() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(105, this->fidealershortsellquota(), target);
  }

  // optional double FIPreClosePx = 106;
  if (this->fipreclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(106, this->fipreclosepx(), target);
  }

  // optional double FIPreWeightedPx = 107;
  if (this->fipreweightedpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(107, this->fipreweightedpx(), target);
  }

  // optional string OptionListType = 110;
  if (this->optionlisttype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionlisttype().data(), this->optionlisttype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionListType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        110, this->optionlisttype(), target);
  }

  // optional string OptionDeliveryType = 111;
  if (this->optiondeliverytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiondeliverytype().data(), this->optiondeliverytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        111, this->optiondeliverytype(), target);
  }

  // optional int32 OptionAdjustTimes = 112;
  if (this->optionadjusttimes() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(112, this->optionadjusttimes(), target);
  }

  // optional int64 OptionContractPosition = 113;
  if (this->optioncontractposition() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(113, this->optioncontractposition(), target);
  }

  // optional int64 OptionBuyQtyUpperLimit = 114;
  if (this->optionbuyqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(114, this->optionbuyqtyupperlimit(), target);
  }

  // optional int64 OptionSellQtyUpperLimit = 115;
  if (this->optionsellqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(115, this->optionsellqtyupperlimit(), target);
  }

  // optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
  if (this->optionmarketorderbuyqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(116, this->optionmarketorderbuyqtyupperlimit(), target);
  }

  // optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
  if (this->optionmarketordersellqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(117, this->optionmarketordersellqtyupperlimit(), target);
  }

  // optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
  if (this->optionquoteorderbuyqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(118, this->optionquoteorderbuyqtyupperlimit(), target);
  }

  // optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
  if (this->optionquoteordersellqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(119, this->optionquoteordersellqtyupperlimit(), target);
  }

  // optional int64 OptionBuyQtyUnit = 120;
  if (this->optionbuyqtyunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(120, this->optionbuyqtyunit(), target);
  }

  // optional int64 OptionSellQtyUnit = 121;
  if (this->optionsellqtyunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(121, this->optionsellqtyunit(), target);
  }

  // optional double OptionLastSellMargin = 122;
  if (this->optionlastsellmargin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(122, this->optionlastsellmargin(), target);
  }

  // optional double OptionSellMargin = 123;
  if (this->optionsellmargin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(123, this->optionsellmargin(), target);
  }

  // optional string OptionMarketMakerFlag = 124;
  if (this->optionmarketmakerflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optionmarketmakerflag().data(), this->optionmarketmakerflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        124, this->optionmarketmakerflag(), target);
  }

  // optional string OptionCombinationStrategy = 125;
  if (this->optioncombinationstrategy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optioncombinationstrategy().data(), this->optioncombinationstrategy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        125, this->optioncombinationstrategy(), target);
  }

  // optional string DeliveryYear = 126;
  if (this->deliveryyear().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deliveryyear().data(), this->deliveryyear().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        126, this->deliveryyear(), target);
  }

  // optional string DeliveryMonth = 127;
  if (this->deliverymonth().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deliverymonth().data(), this->deliverymonth().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        127, this->deliverymonth(), target);
  }

  // optional string InstrumentID = 128;
  if (this->instrumentid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentid().data(), this->instrumentid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        128, this->instrumentid(), target);
  }

  // optional string InstrumentName = 129;
  if (this->instrumentname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentname().data(), this->instrumentname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        129, this->instrumentname(), target);
  }

  // optional string ExchangeInstID = 130;
  if (this->exchangeinstid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exchangeinstid().data(), this->exchangeinstid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        130, this->exchangeinstid(), target);
  }

  // optional string ProductID = 131;
  if (this->productid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->productid().data(), this->productid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ProductID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        131, this->productid(), target);
  }

  // optional int64 MaxMarketOrderVolume = 132;
  if (this->maxmarketordervolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(132, this->maxmarketordervolume(), target);
  }

  // optional int64 MinMarketOrderVolume = 133;
  if (this->minmarketordervolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(133, this->minmarketordervolume(), target);
  }

  // optional int64 MaxLimitOrderVolume = 134;
  if (this->maxlimitordervolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(134, this->maxlimitordervolume(), target);
  }

  // optional int64 MinLimitOrderVolume = 135;
  if (this->minlimitordervolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(135, this->minlimitordervolume(), target);
  }

  // optional int64 VolumeMultiple = 136;
  if (this->volumemultiple() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(136, this->volumemultiple(), target);
  }

  // optional string CreateDate = 137;
  if (this->createdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->createdate().data(), this->createdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.CreateDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        137, this->createdate(), target);
  }

  // optional string ExpireDate = 138;
  if (this->expiredate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expiredate().data(), this->expiredate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        138, this->expiredate(), target);
  }

  // optional string StartDelivDate = 139;
  if (this->startdelivdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->startdelivdate().data(), this->startdelivdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        139, this->startdelivdate(), target);
  }

  // optional string EndDelivDate = 140;
  if (this->enddelivdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->enddelivdate().data(), this->enddelivdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        140, this->enddelivdate(), target);
  }

  // optional string PositionType = 141;
  if (this->positiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->positiontype().data(), this->positiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.PositionType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        141, this->positiontype(), target);
  }

  // optional double LongMarginRatio = 142;
  if (this->longmarginratio() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(142, this->longmarginratio(), target);
  }

  // optional double ShortMarginRatio = 143;
  if (this->shortmarginratio() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(143, this->shortmarginratio(), target);
  }

  // optional string MaxMarginSideAlgorithm = 144;
  if (this->maxmarginsidealgorithm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->maxmarginsidealgorithm().data(), this->maxmarginsidealgorithm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        144, this->maxmarginsidealgorithm(), target);
  }

  // optional double StrikePrice = 145;
  if (this->strikeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(145, this->strikeprice(), target);
  }

  // optional double PreOpenInterest = 146;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(146, this->preopeninterest(), target);
  }

  // optional string FormerSymbol = 147;
  if (this->formersymbol().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->formersymbol().data(), this->formersymbol().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        147, this->formersymbol(), target);
  }

  // optional string DelistDate = 148;
  if (this->delistdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->delistdate().data(), this->delistdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.DelistDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        148, this->delistdate(), target);
  }

  // optional int64 BuyQtyUnit = 149;
  if (this->buyqtyunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(149, this->buyqtyunit(), target);
  }

  // optional int64 SellQtyUnit = 150;
  if (this->sellqtyunit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(150, this->sellqtyunit(), target);
  }

  // optional int64 BuyQtyUpperLimit = 161;
  if (this->buyqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(161, this->buyqtyupperlimit(), target);
  }

  // optional int64 SellQtyUpperLimit = 162;
  if (this->sellqtyupperlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(162, this->sellqtyupperlimit(), target);
  }

  // optional int64 BuyQtyLowerLimit = 163;
  if (this->buyqtylowerlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(163, this->buyqtylowerlimit(), target);
  }

  // optional int64 SellQtyLowerLimit = 164;
  if (this->sellqtylowerlimit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(164, this->sellqtylowerlimit(), target);
  }

  // optional int32 VCMFlag = 165;
  if (this->vcmflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(165, this->vcmflag(), target);
  }

  // optional int32 CASFlag = 166;
  if (this->casflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(166, this->casflag(), target);
  }

  // optional int32 POSFlag = 167;
  if (this->posflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(167, this->posflag(), target);
  }

  // optional double POSUpperLimitPx = 168;
  if (this->posupperlimitpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(168, this->posupperlimitpx(), target);
  }

  // optional double POSLowerLimitPx = 169;
  if (this->poslowerlimitpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(169, this->poslowerlimitpx(), target);
  }

  // optional string BaseContractID = 170;
  if (this->basecontractid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->basecontractid().data(), this->basecontractid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        170, this->basecontractid(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
  for (unsigned int i = 0, n = this->constantparams_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        171, this->constantparams(i), false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 172;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(172, this->datamultiplepowerof10(), target);
  }

  // optional string InterestAccrualDate = 173;
  if (this->interestaccrualdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->interestaccrualdate().data(), this->interestaccrualdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        173, this->interestaccrualdate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDBasicInfo)
  return target;
}

size_t MDBasicInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDBasicInfo)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional string SecurityID = 2;
  if (this->securityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securityid());
  }

  // optional string Symbol = 3;
  if (this->symbol().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->symbol());
  }

  // optional string ChiSpelling = 4;
  if (this->chispelling().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->chispelling());
  }

  // optional string EnglishName = 5;
  if (this->englishname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->englishname());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string SecuritySubType = 8;
  if (this->securitysubtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitysubtype());
  }

  // optional string ListDate = 9;
  if (this->listdate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->listdate());
  }

  // optional string Currency = 10;
  if (this->currency().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->currency());
  }

  // optional int64 OutstandingShare = 11;
  if (this->outstandingshare() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->outstandingshare());
  }

  // optional int64 PublicFloatShareQuantity = 12;
  if (this->publicfloatsharequantity() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->publicfloatsharequantity());
  }

  // optional int32 MDDate = 13;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional string TradingPhaseCode = 14;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional int64 PreClosePx = 15;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 MaxPx = 16;
  if (this->maxpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 17;
  if (this->minpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 LotSize = 18;
  if (this->lotsize() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lotsize());
  }

  // optional bool ShortSellFlag = 19;
  if (this->shortsellflag() != 0) {
    total_size += 2 + 1;
  }

  // optional string ExchangeDate = 20;
  if (this->exchangedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangedate());
  }

  // optional string ExchangeSymbol = 21;
  if (this->exchangesymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangesymbol());
  }

  // optional double TickSize = 22;
  if (this->ticksize() != 0) {
    total_size += 2 + 8;
  }

  // optional int32 LoanMarginIndicator = 23;
  if (this->loanmarginindicator() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->loanmarginindicator());
  }

  // optional int32 PxAccuracy = 24;
  if (this->pxaccuracy() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->pxaccuracy());
  }

  // optional int32 IPOProfitable = 25;
  if (this->ipoprofitable() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ipoprofitable());
  }

  // optional int32 DiffRightsIndicator = 26;
  if (this->diffrightsindicator() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->diffrightsindicator());
  }

  // optional string HKSpreadTableCode = 27;
  if (this->hkspreadtablecode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->hkspreadtablecode());
  }

  // optional double PreSettlePx = 28;
  if (this->presettlepx() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreIOPV = 29;
  if (this->preiopv() != 0) {
    total_size += 2 + 8;
  }

  // optional int32 ShHkConnect = 30;
  if (this->shhkconnect() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->shhkconnect());
  }

  // optional int32 SzHkConnect = 31;
  if (this->szhkconnect() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->szhkconnect());
  }

  // optional string OptionContractID = 40;
  if (this->optioncontractid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optioncontractid());
  }

  // optional string OptionContractSymbol = 41;
  if (this->optioncontractsymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optioncontractsymbol());
  }

  // optional string OptionUnderlyingSecurityID = 42;
  if (this->optionunderlyingsecurityid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionunderlyingsecurityid());
  }

  // optional string OptionUnderlyingSymbol = 43;
  if (this->optionunderlyingsymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionunderlyingsymbol());
  }

  // optional string OptionUnderlyingType = 44;
  if (this->optionunderlyingtype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionunderlyingtype());
  }

  // optional string OptionOptionType = 45;
  if (this->optionoptiontype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionoptiontype());
  }

  // optional string OptionCallOrPut = 46;
  if (this->optioncallorput().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optioncallorput());
  }

  // optional int64 OptionContractMultiplierUnit = 47;
  if (this->optioncontractmultiplierunit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optioncontractmultiplierunit());
  }

  // optional double OptionExercisePrice = 48;
  if (this->optionexerciseprice() != 0) {
    total_size += 2 + 8;
  }

  // optional string OptionStartDate = 49;
  if (this->optionstartdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionstartdate());
  }

  // optional string OptionEndDate = 50;
  if (this->optionenddate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionenddate());
  }

  // optional string OptionExerciseDate = 51;
  if (this->optionexercisedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionexercisedate());
  }

  // optional string OptionDeliveryDate = 52;
  if (this->optiondeliverydate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optiondeliverydate());
  }

  // optional string OptionExpireDate = 53;
  if (this->optionexpiredate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionexpiredate());
  }

  // optional string OptionUpdateVersion = 54;
  if (this->optionupdateversion().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionupdateversion());
  }

  // optional int64 OptionTotalLongPosition = 55;
  if (this->optiontotallongposition() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optiontotallongposition());
  }

  // optional double OptionSecurityClosePx = 56;
  if (this->optionsecurityclosepx() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionSettlPrice = 57;
  if (this->optionsettlprice() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionUnderlyingClosePx = 58;
  if (this->optionunderlyingclosepx() != 0) {
    total_size += 2 + 8;
  }

  // optional string OptionPriceLimitType = 59;
  if (this->optionpricelimittype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionpricelimittype());
  }

  // optional double OptionDailyPriceUpLimit = 60;
  if (this->optiondailypriceuplimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionDailyPriceDownLimit = 61;
  if (this->optiondailypricedownlimit() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionMarginUnit = 62;
  if (this->optionmarginunit() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionMarginRatioParam1 = 63;
  if (this->optionmarginratioparam1() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionMarginRatioParam2 = 64;
  if (this->optionmarginratioparam2() != 0) {
    total_size += 2 + 8;
  }

  // optional int64 OptionRoundLot = 65;
  if (this->optionroundlot() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionroundlot());
  }

  // optional int64 OptionLmtOrdMinFloor = 66;
  if (this->optionlmtordminfloor() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionlmtordminfloor());
  }

  // optional int64 OptionLmtOrdMaxFloor = 67;
  if (this->optionlmtordmaxfloor() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionlmtordmaxfloor());
  }

  // optional int64 OptionMktOrdMinFloor = 68;
  if (this->optionmktordminfloor() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionmktordminfloor());
  }

  // optional int64 OptionMktOrdMaxFloor = 69;
  if (this->optionmktordmaxfloor() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionmktordmaxfloor());
  }

  // optional double OptionTickSize = 70;
  if (this->optionticksize() != 0) {
    total_size += 2 + 8;
  }

  // optional string OptionSecurityStatusFlag = 71;
  if (this->optionsecuritystatusflag().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionsecuritystatusflag());
  }

  // optional string OptionCarryInterestDate = 72;
  if (this->optioncarryinterestdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optioncarryinterestdate());
  }

  // optional string OptionEarlyExpireDate = 73;
  if (this->optionearlyexpiredate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionearlyexpiredate());
  }

  // optional string OptionStrategySecurityID = 74;
  if (this->optionstrategysecurityid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionstrategysecurityid());
  }

  // optional string FITradeProductType = 80;
  if (this->fitradeproducttype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fitradeproducttype());
  }

  // optional string FISecurityProperty = 81;
  if (this->fisecurityproperty().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fisecurityproperty());
  }

  // optional string FISecurityStatus = 82;
  if (this->fisecuritystatus().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fisecuritystatus());
  }

  // optional string FIPledgedSecurityID = 83;
  if (this->fipledgedsecurityid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fipledgedsecurityid());
  }

  // optional string FIOpenTime = 84;
  if (this->fiopentime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiopentime());
  }

  // optional string FICloseTime = 85;
  if (this->ficlosetime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->ficlosetime());
  }

  // optional string FIIssueMode = 86;
  if (this->fiissuemode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiissuemode());
  }

  // optional double FIFaceAmount = 87;
  if (this->fifaceamount() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIIssuePrice = 88;
  if (this->fiissueprice() != 0) {
    total_size += 2 + 8;
  }

  // optional string FIInterestType = 89;
  if (this->fiinteresttype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiinteresttype());
  }

  // optional string FIInterestFrequency = 90;
  if (this->fiinterestfrequency().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiinterestfrequency());
  }

  // optional double FIGuaranteedInterestRate = 91;
  if (this->figuaranteedinterestrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIBaseInterestRate = 92;
  if (this->fibaseinterestrate() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIQuotedMargin = 93;
  if (this->fiquotedmargin() != 0) {
    total_size += 2 + 8;
  }

  // optional int32 FITimeLimit = 94;
  if (this->fitimelimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->fitimelimit());
  }

  // optional double FITotalIssuance = 95;
  if (this->fitotalissuance() != 0) {
    total_size += 2 + 8;
  }

  // optional string FIIssueStartDate = 96;
  if (this->fiissuestartdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiissuestartdate());
  }

  // optional string FIIssueEndDate = 97;
  if (this->fiissueenddate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiissueenddate());
  }

  // optional string FIListDate = 98;
  if (this->filistdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->filistdate());
  }

  // optional string FIExpireDate = 99;
  if (this->fiexpiredate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiexpiredate());
  }

  // optional string FINationalDebtType = 100;
  if (this->finationaldebttype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->finationaldebttype());
  }

  // optional string FIIssueMethod = 101;
  if (this->fiissuemethod().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fiissuemethod());
  }

  // optional bool FICrossMarket = 102;
  if (this->ficrossmarket() != 0) {
    total_size += 2 + 1;
  }

  // optional bool FIShortSellFlag = 103;
  if (this->fishortsellflag() != 0) {
    total_size += 2 + 1;
  }

  // optional double FITotalShortSellQuota = 104;
  if (this->fitotalshortsellquota() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIDealerShortSellQuota = 105;
  if (this->fidealershortsellquota() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIPreClosePx = 106;
  if (this->fipreclosepx() != 0) {
    total_size += 2 + 8;
  }

  // optional double FIPreWeightedPx = 107;
  if (this->fipreweightedpx() != 0) {
    total_size += 2 + 8;
  }

  // optional string OptionListType = 110;
  if (this->optionlisttype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionlisttype());
  }

  // optional string OptionDeliveryType = 111;
  if (this->optiondeliverytype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optiondeliverytype());
  }

  // optional int32 OptionAdjustTimes = 112;
  if (this->optionadjusttimes() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->optionadjusttimes());
  }

  // optional int64 OptionContractPosition = 113;
  if (this->optioncontractposition() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optioncontractposition());
  }

  // optional int64 OptionBuyQtyUpperLimit = 114;
  if (this->optionbuyqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionbuyqtyupperlimit());
  }

  // optional int64 OptionSellQtyUpperLimit = 115;
  if (this->optionsellqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionsellqtyupperlimit());
  }

  // optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
  if (this->optionmarketorderbuyqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionmarketorderbuyqtyupperlimit());
  }

  // optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
  if (this->optionmarketordersellqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionmarketordersellqtyupperlimit());
  }

  // optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
  if (this->optionquoteorderbuyqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionquoteorderbuyqtyupperlimit());
  }

  // optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
  if (this->optionquoteordersellqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionquoteordersellqtyupperlimit());
  }

  // optional int64 OptionBuyQtyUnit = 120;
  if (this->optionbuyqtyunit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionbuyqtyunit());
  }

  // optional int64 OptionSellQtyUnit = 121;
  if (this->optionsellqtyunit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->optionsellqtyunit());
  }

  // optional double OptionLastSellMargin = 122;
  if (this->optionlastsellmargin() != 0) {
    total_size += 2 + 8;
  }

  // optional double OptionSellMargin = 123;
  if (this->optionsellmargin() != 0) {
    total_size += 2 + 8;
  }

  // optional string OptionMarketMakerFlag = 124;
  if (this->optionmarketmakerflag().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optionmarketmakerflag());
  }

  // optional string OptionCombinationStrategy = 125;
  if (this->optioncombinationstrategy().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optioncombinationstrategy());
  }

  // optional string DeliveryYear = 126;
  if (this->deliveryyear().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->deliveryyear());
  }

  // optional string DeliveryMonth = 127;
  if (this->deliverymonth().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->deliverymonth());
  }

  // optional string InstrumentID = 128;
  if (this->instrumentid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrumentid());
  }

  // optional string InstrumentName = 129;
  if (this->instrumentname().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrumentname());
  }

  // optional string ExchangeInstID = 130;
  if (this->exchangeinstid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exchangeinstid());
  }

  // optional string ProductID = 131;
  if (this->productid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->productid());
  }

  // optional int64 MaxMarketOrderVolume = 132;
  if (this->maxmarketordervolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxmarketordervolume());
  }

  // optional int64 MinMarketOrderVolume = 133;
  if (this->minmarketordervolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minmarketordervolume());
  }

  // optional int64 MaxLimitOrderVolume = 134;
  if (this->maxlimitordervolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxlimitordervolume());
  }

  // optional int64 MinLimitOrderVolume = 135;
  if (this->minlimitordervolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minlimitordervolume());
  }

  // optional int64 VolumeMultiple = 136;
  if (this->volumemultiple() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volumemultiple());
  }

  // optional string CreateDate = 137;
  if (this->createdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->createdate());
  }

  // optional string ExpireDate = 138;
  if (this->expiredate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->expiredate());
  }

  // optional string StartDelivDate = 139;
  if (this->startdelivdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->startdelivdate());
  }

  // optional string EndDelivDate = 140;
  if (this->enddelivdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->enddelivdate());
  }

  // optional string PositionType = 141;
  if (this->positiontype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->positiontype());
  }

  // optional double LongMarginRatio = 142;
  if (this->longmarginratio() != 0) {
    total_size += 2 + 8;
  }

  // optional double ShortMarginRatio = 143;
  if (this->shortmarginratio() != 0) {
    total_size += 2 + 8;
  }

  // optional string MaxMarginSideAlgorithm = 144;
  if (this->maxmarginsidealgorithm().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->maxmarginsidealgorithm());
  }

  // optional double StrikePrice = 145;
  if (this->strikeprice() != 0) {
    total_size += 2 + 8;
  }

  // optional double PreOpenInterest = 146;
  if (this->preopeninterest() != 0) {
    total_size += 2 + 8;
  }

  // optional string FormerSymbol = 147;
  if (this->formersymbol().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->formersymbol());
  }

  // optional string DelistDate = 148;
  if (this->delistdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->delistdate());
  }

  // optional int64 BuyQtyUnit = 149;
  if (this->buyqtyunit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyqtyunit());
  }

  // optional int64 SellQtyUnit = 150;
  if (this->sellqtyunit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellqtyunit());
  }

  // optional int64 BuyQtyUpperLimit = 161;
  if (this->buyqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyqtyupperlimit());
  }

  // optional int64 SellQtyUpperLimit = 162;
  if (this->sellqtyupperlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellqtyupperlimit());
  }

  // optional int64 BuyQtyLowerLimit = 163;
  if (this->buyqtylowerlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyqtylowerlimit());
  }

  // optional int64 SellQtyLowerLimit = 164;
  if (this->sellqtylowerlimit() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellqtylowerlimit());
  }

  // optional int32 VCMFlag = 165;
  if (this->vcmflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->vcmflag());
  }

  // optional int32 CASFlag = 166;
  if (this->casflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->casflag());
  }

  // optional int32 POSFlag = 167;
  if (this->posflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->posflag());
  }

  // optional double POSUpperLimitPx = 168;
  if (this->posupperlimitpx() != 0) {
    total_size += 2 + 8;
  }

  // optional double POSLowerLimitPx = 169;
  if (this->poslowerlimitpx() != 0) {
    total_size += 2 + 8;
  }

  // optional string BaseContractID = 170;
  if (this->basecontractid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->basecontractid());
  }

  // optional int32 DataMultiplePowerOf10 = 172;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string InterestAccrualDate = 173;
  if (this->interestaccrualdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->interestaccrualdate());
  }

  // repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
  {
    unsigned int count = this->constantparams_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->constantparams(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDBasicInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDBasicInfo)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDBasicInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDBasicInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDBasicInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDBasicInfo)
    UnsafeMergeFrom(*source);
  }
}

void MDBasicInfo::MergeFrom(const MDBasicInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDBasicInfo)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDBasicInfo::UnsafeMergeFrom(const MDBasicInfo& from) {
  GOOGLE_DCHECK(&from != this);
  constantparams_.MergeFrom(from.constantparams_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securityid().size() > 0) {

    securityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securityid_);
  }
  if (from.symbol().size() > 0) {

    symbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.symbol_);
  }
  if (from.chispelling().size() > 0) {

    chispelling_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.chispelling_);
  }
  if (from.englishname().size() > 0) {

    englishname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.englishname_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securitysubtype().size() > 0) {

    securitysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitysubtype_);
  }
  if (from.listdate().size() > 0) {

    listdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.listdate_);
  }
  if (from.currency().size() > 0) {

    currency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.currency_);
  }
  if (from.outstandingshare() != 0) {
    set_outstandingshare(from.outstandingshare());
  }
  if (from.publicfloatsharequantity() != 0) {
    set_publicfloatsharequantity(from.publicfloatsharequantity());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.lotsize() != 0) {
    set_lotsize(from.lotsize());
  }
  if (from.shortsellflag() != 0) {
    set_shortsellflag(from.shortsellflag());
  }
  if (from.exchangedate().size() > 0) {

    exchangedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangedate_);
  }
  if (from.exchangesymbol().size() > 0) {

    exchangesymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangesymbol_);
  }
  if (from.ticksize() != 0) {
    set_ticksize(from.ticksize());
  }
  if (from.loanmarginindicator() != 0) {
    set_loanmarginindicator(from.loanmarginindicator());
  }
  if (from.pxaccuracy() != 0) {
    set_pxaccuracy(from.pxaccuracy());
  }
  if (from.ipoprofitable() != 0) {
    set_ipoprofitable(from.ipoprofitable());
  }
  if (from.diffrightsindicator() != 0) {
    set_diffrightsindicator(from.diffrightsindicator());
  }
  if (from.hkspreadtablecode().size() > 0) {

    hkspreadtablecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.hkspreadtablecode_);
  }
  if (from.presettlepx() != 0) {
    set_presettlepx(from.presettlepx());
  }
  if (from.preiopv() != 0) {
    set_preiopv(from.preiopv());
  }
  if (from.shhkconnect() != 0) {
    set_shhkconnect(from.shhkconnect());
  }
  if (from.szhkconnect() != 0) {
    set_szhkconnect(from.szhkconnect());
  }
  if (from.optioncontractid().size() > 0) {

    optioncontractid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optioncontractid_);
  }
  if (from.optioncontractsymbol().size() > 0) {

    optioncontractsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optioncontractsymbol_);
  }
  if (from.optionunderlyingsecurityid().size() > 0) {

    optionunderlyingsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionunderlyingsecurityid_);
  }
  if (from.optionunderlyingsymbol().size() > 0) {

    optionunderlyingsymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionunderlyingsymbol_);
  }
  if (from.optionunderlyingtype().size() > 0) {

    optionunderlyingtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionunderlyingtype_);
  }
  if (from.optionoptiontype().size() > 0) {

    optionoptiontype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionoptiontype_);
  }
  if (from.optioncallorput().size() > 0) {

    optioncallorput_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optioncallorput_);
  }
  if (from.optioncontractmultiplierunit() != 0) {
    set_optioncontractmultiplierunit(from.optioncontractmultiplierunit());
  }
  if (from.optionexerciseprice() != 0) {
    set_optionexerciseprice(from.optionexerciseprice());
  }
  if (from.optionstartdate().size() > 0) {

    optionstartdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionstartdate_);
  }
  if (from.optionenddate().size() > 0) {

    optionenddate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionenddate_);
  }
  if (from.optionexercisedate().size() > 0) {

    optionexercisedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionexercisedate_);
  }
  if (from.optiondeliverydate().size() > 0) {

    optiondeliverydate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optiondeliverydate_);
  }
  if (from.optionexpiredate().size() > 0) {

    optionexpiredate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionexpiredate_);
  }
  if (from.optionupdateversion().size() > 0) {

    optionupdateversion_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionupdateversion_);
  }
  if (from.optiontotallongposition() != 0) {
    set_optiontotallongposition(from.optiontotallongposition());
  }
  if (from.optionsecurityclosepx() != 0) {
    set_optionsecurityclosepx(from.optionsecurityclosepx());
  }
  if (from.optionsettlprice() != 0) {
    set_optionsettlprice(from.optionsettlprice());
  }
  if (from.optionunderlyingclosepx() != 0) {
    set_optionunderlyingclosepx(from.optionunderlyingclosepx());
  }
  if (from.optionpricelimittype().size() > 0) {

    optionpricelimittype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionpricelimittype_);
  }
  if (from.optiondailypriceuplimit() != 0) {
    set_optiondailypriceuplimit(from.optiondailypriceuplimit());
  }
  if (from.optiondailypricedownlimit() != 0) {
    set_optiondailypricedownlimit(from.optiondailypricedownlimit());
  }
  if (from.optionmarginunit() != 0) {
    set_optionmarginunit(from.optionmarginunit());
  }
  if (from.optionmarginratioparam1() != 0) {
    set_optionmarginratioparam1(from.optionmarginratioparam1());
  }
  if (from.optionmarginratioparam2() != 0) {
    set_optionmarginratioparam2(from.optionmarginratioparam2());
  }
  if (from.optionroundlot() != 0) {
    set_optionroundlot(from.optionroundlot());
  }
  if (from.optionlmtordminfloor() != 0) {
    set_optionlmtordminfloor(from.optionlmtordminfloor());
  }
  if (from.optionlmtordmaxfloor() != 0) {
    set_optionlmtordmaxfloor(from.optionlmtordmaxfloor());
  }
  if (from.optionmktordminfloor() != 0) {
    set_optionmktordminfloor(from.optionmktordminfloor());
  }
  if (from.optionmktordmaxfloor() != 0) {
    set_optionmktordmaxfloor(from.optionmktordmaxfloor());
  }
  if (from.optionticksize() != 0) {
    set_optionticksize(from.optionticksize());
  }
  if (from.optionsecuritystatusflag().size() > 0) {

    optionsecuritystatusflag_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionsecuritystatusflag_);
  }
  if (from.optioncarryinterestdate().size() > 0) {

    optioncarryinterestdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optioncarryinterestdate_);
  }
  if (from.optionearlyexpiredate().size() > 0) {

    optionearlyexpiredate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionearlyexpiredate_);
  }
  if (from.optionstrategysecurityid().size() > 0) {

    optionstrategysecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionstrategysecurityid_);
  }
  if (from.fitradeproducttype().size() > 0) {

    fitradeproducttype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fitradeproducttype_);
  }
  if (from.fisecurityproperty().size() > 0) {

    fisecurityproperty_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fisecurityproperty_);
  }
  if (from.fisecuritystatus().size() > 0) {

    fisecuritystatus_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fisecuritystatus_);
  }
  if (from.fipledgedsecurityid().size() > 0) {

    fipledgedsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fipledgedsecurityid_);
  }
  if (from.fiopentime().size() > 0) {

    fiopentime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiopentime_);
  }
  if (from.ficlosetime().size() > 0) {

    ficlosetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ficlosetime_);
  }
  if (from.fiissuemode().size() > 0) {

    fiissuemode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiissuemode_);
  }
  if (from.fifaceamount() != 0) {
    set_fifaceamount(from.fifaceamount());
  }
  if (from.fiissueprice() != 0) {
    set_fiissueprice(from.fiissueprice());
  }
  if (from.fiinteresttype().size() > 0) {

    fiinteresttype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiinteresttype_);
  }
  if (from.fiinterestfrequency().size() > 0) {

    fiinterestfrequency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiinterestfrequency_);
  }
  if (from.figuaranteedinterestrate() != 0) {
    set_figuaranteedinterestrate(from.figuaranteedinterestrate());
  }
  if (from.fibaseinterestrate() != 0) {
    set_fibaseinterestrate(from.fibaseinterestrate());
  }
  if (from.fiquotedmargin() != 0) {
    set_fiquotedmargin(from.fiquotedmargin());
  }
  if (from.fitimelimit() != 0) {
    set_fitimelimit(from.fitimelimit());
  }
  if (from.fitotalissuance() != 0) {
    set_fitotalissuance(from.fitotalissuance());
  }
  if (from.fiissuestartdate().size() > 0) {

    fiissuestartdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiissuestartdate_);
  }
  if (from.fiissueenddate().size() > 0) {

    fiissueenddate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiissueenddate_);
  }
  if (from.filistdate().size() > 0) {

    filistdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.filistdate_);
  }
  if (from.fiexpiredate().size() > 0) {

    fiexpiredate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiexpiredate_);
  }
  if (from.finationaldebttype().size() > 0) {

    finationaldebttype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.finationaldebttype_);
  }
  if (from.fiissuemethod().size() > 0) {

    fiissuemethod_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fiissuemethod_);
  }
  if (from.ficrossmarket() != 0) {
    set_ficrossmarket(from.ficrossmarket());
  }
  if (from.fishortsellflag() != 0) {
    set_fishortsellflag(from.fishortsellflag());
  }
  if (from.fitotalshortsellquota() != 0) {
    set_fitotalshortsellquota(from.fitotalshortsellquota());
  }
  if (from.fidealershortsellquota() != 0) {
    set_fidealershortsellquota(from.fidealershortsellquota());
  }
  if (from.fipreclosepx() != 0) {
    set_fipreclosepx(from.fipreclosepx());
  }
  if (from.fipreweightedpx() != 0) {
    set_fipreweightedpx(from.fipreweightedpx());
  }
  if (from.optionlisttype().size() > 0) {

    optionlisttype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionlisttype_);
  }
  if (from.optiondeliverytype().size() > 0) {

    optiondeliverytype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optiondeliverytype_);
  }
  if (from.optionadjusttimes() != 0) {
    set_optionadjusttimes(from.optionadjusttimes());
  }
  if (from.optioncontractposition() != 0) {
    set_optioncontractposition(from.optioncontractposition());
  }
  if (from.optionbuyqtyupperlimit() != 0) {
    set_optionbuyqtyupperlimit(from.optionbuyqtyupperlimit());
  }
  if (from.optionsellqtyupperlimit() != 0) {
    set_optionsellqtyupperlimit(from.optionsellqtyupperlimit());
  }
  if (from.optionmarketorderbuyqtyupperlimit() != 0) {
    set_optionmarketorderbuyqtyupperlimit(from.optionmarketorderbuyqtyupperlimit());
  }
  if (from.optionmarketordersellqtyupperlimit() != 0) {
    set_optionmarketordersellqtyupperlimit(from.optionmarketordersellqtyupperlimit());
  }
  if (from.optionquoteorderbuyqtyupperlimit() != 0) {
    set_optionquoteorderbuyqtyupperlimit(from.optionquoteorderbuyqtyupperlimit());
  }
  if (from.optionquoteordersellqtyupperlimit() != 0) {
    set_optionquoteordersellqtyupperlimit(from.optionquoteordersellqtyupperlimit());
  }
  if (from.optionbuyqtyunit() != 0) {
    set_optionbuyqtyunit(from.optionbuyqtyunit());
  }
  if (from.optionsellqtyunit() != 0) {
    set_optionsellqtyunit(from.optionsellqtyunit());
  }
  if (from.optionlastsellmargin() != 0) {
    set_optionlastsellmargin(from.optionlastsellmargin());
  }
  if (from.optionsellmargin() != 0) {
    set_optionsellmargin(from.optionsellmargin());
  }
  if (from.optionmarketmakerflag().size() > 0) {

    optionmarketmakerflag_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optionmarketmakerflag_);
  }
  if (from.optioncombinationstrategy().size() > 0) {

    optioncombinationstrategy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optioncombinationstrategy_);
  }
  if (from.deliveryyear().size() > 0) {

    deliveryyear_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.deliveryyear_);
  }
  if (from.deliverymonth().size() > 0) {

    deliverymonth_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.deliverymonth_);
  }
  if (from.instrumentid().size() > 0) {

    instrumentid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrumentid_);
  }
  if (from.instrumentname().size() > 0) {

    instrumentname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrumentname_);
  }
  if (from.exchangeinstid().size() > 0) {

    exchangeinstid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exchangeinstid_);
  }
  if (from.productid().size() > 0) {

    productid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.productid_);
  }
  if (from.maxmarketordervolume() != 0) {
    set_maxmarketordervolume(from.maxmarketordervolume());
  }
  if (from.minmarketordervolume() != 0) {
    set_minmarketordervolume(from.minmarketordervolume());
  }
  if (from.maxlimitordervolume() != 0) {
    set_maxlimitordervolume(from.maxlimitordervolume());
  }
  if (from.minlimitordervolume() != 0) {
    set_minlimitordervolume(from.minlimitordervolume());
  }
  if (from.volumemultiple() != 0) {
    set_volumemultiple(from.volumemultiple());
  }
  if (from.createdate().size() > 0) {

    createdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.createdate_);
  }
  if (from.expiredate().size() > 0) {

    expiredate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expiredate_);
  }
  if (from.startdelivdate().size() > 0) {

    startdelivdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.startdelivdate_);
  }
  if (from.enddelivdate().size() > 0) {

    enddelivdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.enddelivdate_);
  }
  if (from.positiontype().size() > 0) {

    positiontype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.positiontype_);
  }
  if (from.longmarginratio() != 0) {
    set_longmarginratio(from.longmarginratio());
  }
  if (from.shortmarginratio() != 0) {
    set_shortmarginratio(from.shortmarginratio());
  }
  if (from.maxmarginsidealgorithm().size() > 0) {

    maxmarginsidealgorithm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.maxmarginsidealgorithm_);
  }
  if (from.strikeprice() != 0) {
    set_strikeprice(from.strikeprice());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.formersymbol().size() > 0) {

    formersymbol_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.formersymbol_);
  }
  if (from.delistdate().size() > 0) {

    delistdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.delistdate_);
  }
  if (from.buyqtyunit() != 0) {
    set_buyqtyunit(from.buyqtyunit());
  }
  if (from.sellqtyunit() != 0) {
    set_sellqtyunit(from.sellqtyunit());
  }
  if (from.buyqtyupperlimit() != 0) {
    set_buyqtyupperlimit(from.buyqtyupperlimit());
  }
  if (from.sellqtyupperlimit() != 0) {
    set_sellqtyupperlimit(from.sellqtyupperlimit());
  }
  if (from.buyqtylowerlimit() != 0) {
    set_buyqtylowerlimit(from.buyqtylowerlimit());
  }
  if (from.sellqtylowerlimit() != 0) {
    set_sellqtylowerlimit(from.sellqtylowerlimit());
  }
  if (from.vcmflag() != 0) {
    set_vcmflag(from.vcmflag());
  }
  if (from.casflag() != 0) {
    set_casflag(from.casflag());
  }
  if (from.posflag() != 0) {
    set_posflag(from.posflag());
  }
  if (from.posupperlimitpx() != 0) {
    set_posupperlimitpx(from.posupperlimitpx());
  }
  if (from.poslowerlimitpx() != 0) {
    set_poslowerlimitpx(from.poslowerlimitpx());
  }
  if (from.basecontractid().size() > 0) {

    basecontractid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.basecontractid_);
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.interestaccrualdate().size() > 0) {

    interestaccrualdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.interestaccrualdate_);
  }
}

void MDBasicInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDBasicInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDBasicInfo::CopyFrom(const MDBasicInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDBasicInfo)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDBasicInfo::IsInitialized() const {

  return true;
}

void MDBasicInfo::Swap(MDBasicInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDBasicInfo::InternalSwap(MDBasicInfo* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  securityid_.Swap(&other->securityid_);
  symbol_.Swap(&other->symbol_);
  chispelling_.Swap(&other->chispelling_);
  englishname_.Swap(&other->englishname_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  securitysubtype_.Swap(&other->securitysubtype_);
  listdate_.Swap(&other->listdate_);
  currency_.Swap(&other->currency_);
  std::swap(outstandingshare_, other->outstandingshare_);
  std::swap(publicfloatsharequantity_, other->publicfloatsharequantity_);
  std::swap(mddate_, other->mddate_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(lotsize_, other->lotsize_);
  std::swap(shortsellflag_, other->shortsellflag_);
  exchangedate_.Swap(&other->exchangedate_);
  exchangesymbol_.Swap(&other->exchangesymbol_);
  std::swap(ticksize_, other->ticksize_);
  std::swap(loanmarginindicator_, other->loanmarginindicator_);
  std::swap(pxaccuracy_, other->pxaccuracy_);
  std::swap(ipoprofitable_, other->ipoprofitable_);
  std::swap(diffrightsindicator_, other->diffrightsindicator_);
  hkspreadtablecode_.Swap(&other->hkspreadtablecode_);
  std::swap(presettlepx_, other->presettlepx_);
  std::swap(preiopv_, other->preiopv_);
  std::swap(shhkconnect_, other->shhkconnect_);
  std::swap(szhkconnect_, other->szhkconnect_);
  optioncontractid_.Swap(&other->optioncontractid_);
  optioncontractsymbol_.Swap(&other->optioncontractsymbol_);
  optionunderlyingsecurityid_.Swap(&other->optionunderlyingsecurityid_);
  optionunderlyingsymbol_.Swap(&other->optionunderlyingsymbol_);
  optionunderlyingtype_.Swap(&other->optionunderlyingtype_);
  optionoptiontype_.Swap(&other->optionoptiontype_);
  optioncallorput_.Swap(&other->optioncallorput_);
  std::swap(optioncontractmultiplierunit_, other->optioncontractmultiplierunit_);
  std::swap(optionexerciseprice_, other->optionexerciseprice_);
  optionstartdate_.Swap(&other->optionstartdate_);
  optionenddate_.Swap(&other->optionenddate_);
  optionexercisedate_.Swap(&other->optionexercisedate_);
  optiondeliverydate_.Swap(&other->optiondeliverydate_);
  optionexpiredate_.Swap(&other->optionexpiredate_);
  optionupdateversion_.Swap(&other->optionupdateversion_);
  std::swap(optiontotallongposition_, other->optiontotallongposition_);
  std::swap(optionsecurityclosepx_, other->optionsecurityclosepx_);
  std::swap(optionsettlprice_, other->optionsettlprice_);
  std::swap(optionunderlyingclosepx_, other->optionunderlyingclosepx_);
  optionpricelimittype_.Swap(&other->optionpricelimittype_);
  std::swap(optiondailypriceuplimit_, other->optiondailypriceuplimit_);
  std::swap(optiondailypricedownlimit_, other->optiondailypricedownlimit_);
  std::swap(optionmarginunit_, other->optionmarginunit_);
  std::swap(optionmarginratioparam1_, other->optionmarginratioparam1_);
  std::swap(optionmarginratioparam2_, other->optionmarginratioparam2_);
  std::swap(optionroundlot_, other->optionroundlot_);
  std::swap(optionlmtordminfloor_, other->optionlmtordminfloor_);
  std::swap(optionlmtordmaxfloor_, other->optionlmtordmaxfloor_);
  std::swap(optionmktordminfloor_, other->optionmktordminfloor_);
  std::swap(optionmktordmaxfloor_, other->optionmktordmaxfloor_);
  std::swap(optionticksize_, other->optionticksize_);
  optionsecuritystatusflag_.Swap(&other->optionsecuritystatusflag_);
  optioncarryinterestdate_.Swap(&other->optioncarryinterestdate_);
  optionearlyexpiredate_.Swap(&other->optionearlyexpiredate_);
  optionstrategysecurityid_.Swap(&other->optionstrategysecurityid_);
  fitradeproducttype_.Swap(&other->fitradeproducttype_);
  fisecurityproperty_.Swap(&other->fisecurityproperty_);
  fisecuritystatus_.Swap(&other->fisecuritystatus_);
  fipledgedsecurityid_.Swap(&other->fipledgedsecurityid_);
  fiopentime_.Swap(&other->fiopentime_);
  ficlosetime_.Swap(&other->ficlosetime_);
  fiissuemode_.Swap(&other->fiissuemode_);
  std::swap(fifaceamount_, other->fifaceamount_);
  std::swap(fiissueprice_, other->fiissueprice_);
  fiinteresttype_.Swap(&other->fiinteresttype_);
  fiinterestfrequency_.Swap(&other->fiinterestfrequency_);
  std::swap(figuaranteedinterestrate_, other->figuaranteedinterestrate_);
  std::swap(fibaseinterestrate_, other->fibaseinterestrate_);
  std::swap(fiquotedmargin_, other->fiquotedmargin_);
  std::swap(fitimelimit_, other->fitimelimit_);
  std::swap(fitotalissuance_, other->fitotalissuance_);
  fiissuestartdate_.Swap(&other->fiissuestartdate_);
  fiissueenddate_.Swap(&other->fiissueenddate_);
  filistdate_.Swap(&other->filistdate_);
  fiexpiredate_.Swap(&other->fiexpiredate_);
  finationaldebttype_.Swap(&other->finationaldebttype_);
  fiissuemethod_.Swap(&other->fiissuemethod_);
  std::swap(ficrossmarket_, other->ficrossmarket_);
  std::swap(fishortsellflag_, other->fishortsellflag_);
  std::swap(fitotalshortsellquota_, other->fitotalshortsellquota_);
  std::swap(fidealershortsellquota_, other->fidealershortsellquota_);
  std::swap(fipreclosepx_, other->fipreclosepx_);
  std::swap(fipreweightedpx_, other->fipreweightedpx_);
  optionlisttype_.Swap(&other->optionlisttype_);
  optiondeliverytype_.Swap(&other->optiondeliverytype_);
  std::swap(optionadjusttimes_, other->optionadjusttimes_);
  std::swap(optioncontractposition_, other->optioncontractposition_);
  std::swap(optionbuyqtyupperlimit_, other->optionbuyqtyupperlimit_);
  std::swap(optionsellqtyupperlimit_, other->optionsellqtyupperlimit_);
  std::swap(optionmarketorderbuyqtyupperlimit_, other->optionmarketorderbuyqtyupperlimit_);
  std::swap(optionmarketordersellqtyupperlimit_, other->optionmarketordersellqtyupperlimit_);
  std::swap(optionquoteorderbuyqtyupperlimit_, other->optionquoteorderbuyqtyupperlimit_);
  std::swap(optionquoteordersellqtyupperlimit_, other->optionquoteordersellqtyupperlimit_);
  std::swap(optionbuyqtyunit_, other->optionbuyqtyunit_);
  std::swap(optionsellqtyunit_, other->optionsellqtyunit_);
  std::swap(optionlastsellmargin_, other->optionlastsellmargin_);
  std::swap(optionsellmargin_, other->optionsellmargin_);
  optionmarketmakerflag_.Swap(&other->optionmarketmakerflag_);
  optioncombinationstrategy_.Swap(&other->optioncombinationstrategy_);
  deliveryyear_.Swap(&other->deliveryyear_);
  deliverymonth_.Swap(&other->deliverymonth_);
  instrumentid_.Swap(&other->instrumentid_);
  instrumentname_.Swap(&other->instrumentname_);
  exchangeinstid_.Swap(&other->exchangeinstid_);
  productid_.Swap(&other->productid_);
  std::swap(maxmarketordervolume_, other->maxmarketordervolume_);
  std::swap(minmarketordervolume_, other->minmarketordervolume_);
  std::swap(maxlimitordervolume_, other->maxlimitordervolume_);
  std::swap(minlimitordervolume_, other->minlimitordervolume_);
  std::swap(volumemultiple_, other->volumemultiple_);
  createdate_.Swap(&other->createdate_);
  expiredate_.Swap(&other->expiredate_);
  startdelivdate_.Swap(&other->startdelivdate_);
  enddelivdate_.Swap(&other->enddelivdate_);
  positiontype_.Swap(&other->positiontype_);
  std::swap(longmarginratio_, other->longmarginratio_);
  std::swap(shortmarginratio_, other->shortmarginratio_);
  maxmarginsidealgorithm_.Swap(&other->maxmarginsidealgorithm_);
  std::swap(strikeprice_, other->strikeprice_);
  std::swap(preopeninterest_, other->preopeninterest_);
  formersymbol_.Swap(&other->formersymbol_);
  delistdate_.Swap(&other->delistdate_);
  std::swap(buyqtyunit_, other->buyqtyunit_);
  std::swap(sellqtyunit_, other->sellqtyunit_);
  std::swap(buyqtyupperlimit_, other->buyqtyupperlimit_);
  std::swap(sellqtyupperlimit_, other->sellqtyupperlimit_);
  std::swap(buyqtylowerlimit_, other->buyqtylowerlimit_);
  std::swap(sellqtylowerlimit_, other->sellqtylowerlimit_);
  std::swap(vcmflag_, other->vcmflag_);
  std::swap(casflag_, other->casflag_);
  std::swap(posflag_, other->posflag_);
  std::swap(posupperlimitpx_, other->posupperlimitpx_);
  std::swap(poslowerlimitpx_, other->poslowerlimitpx_);
  basecontractid_.Swap(&other->basecontractid_);
  constantparams_.UnsafeArenaSwap(&other->constantparams_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  interestaccrualdate_.Swap(&other->interestaccrualdate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDBasicInfo::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDBasicInfo_descriptor_;
  metadata.reflection = MDBasicInfo_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDBasicInfo_ConstantParam

// optional string ParamName = 1;
void MDBasicInfo_ConstantParam::clear_paramname() {
  paramname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo_ConstantParam::paramname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  return paramname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo_ConstantParam::set_paramname(const ::std::string& value) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
void MDBasicInfo_ConstantParam::set_paramname(const char* value) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
void MDBasicInfo_ConstantParam::set_paramname(const char* value, size_t size) {
  
  paramname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}
::std::string* MDBasicInfo_ConstantParam::mutable_paramname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  return paramname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo_ConstantParam::release_paramname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
  
  return paramname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo_ConstantParam::set_allocated_paramname(::std::string* paramname) {
  if (paramname != NULL) {
    
  } else {
    
  }
  paramname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamName)
}

// optional string ParamValue = 2;
void MDBasicInfo_ConstantParam::clear_paramvalue() {
  paramvalue_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo_ConstantParam::paramvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  return paramvalue_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo_ConstantParam::set_paramvalue(const ::std::string& value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
void MDBasicInfo_ConstantParam::set_paramvalue(const char* value) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
void MDBasicInfo_ConstantParam::set_paramvalue(const char* value, size_t size) {
  
  paramvalue_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}
::std::string* MDBasicInfo_ConstantParam::mutable_paramvalue() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  return paramvalue_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo_ConstantParam::release_paramvalue() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
  
  return paramvalue_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo_ConstantParam::set_allocated_paramvalue(::std::string* paramvalue) {
  if (paramvalue != NULL) {
    
  } else {
    
  }
  paramvalue_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), paramvalue);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam.ParamValue)
}

inline const MDBasicInfo_ConstantParam* MDBasicInfo_ConstantParam::internal_default_instance() {
  return &MDBasicInfo_ConstantParam_default_instance_.get();
}
// -------------------------------------------------------------------

// MDBasicInfo

// optional string HTSCSecurityID = 1;
void MDBasicInfo::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
void MDBasicInfo::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
void MDBasicInfo::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}
::std::string* MDBasicInfo::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.HTSCSecurityID)
}

// optional string SecurityID = 2;
void MDBasicInfo::clear_securityid() {
  securityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::securityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  return securityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_securityid(const ::std::string& value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
void MDBasicInfo::set_securityid(const char* value) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
void MDBasicInfo::set_securityid(const char* value, size_t size) {
  
  securityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}
::std::string* MDBasicInfo::mutable_securityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  return securityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_securityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
  
  return securityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_securityid(::std::string* securityid) {
  if (securityid != NULL) {
    
  } else {
    
  }
  securityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.SecurityID)
}

// optional string Symbol = 3;
void MDBasicInfo::clear_symbol() {
  symbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::symbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  return symbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_symbol(const ::std::string& value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
void MDBasicInfo::set_symbol(const char* value) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
void MDBasicInfo::set_symbol(const char* value, size_t size) {
  
  symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}
::std::string* MDBasicInfo::mutable_symbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  return symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_symbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
  
  return symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_symbol(::std::string* symbol) {
  if (symbol != NULL) {
    
  } else {
    
  }
  symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), symbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.Symbol)
}

// optional string ChiSpelling = 4;
void MDBasicInfo::clear_chispelling() {
  chispelling_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::chispelling() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  return chispelling_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_chispelling(const ::std::string& value) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
void MDBasicInfo::set_chispelling(const char* value) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
void MDBasicInfo::set_chispelling(const char* value, size_t size) {
  
  chispelling_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}
::std::string* MDBasicInfo::mutable_chispelling() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  return chispelling_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_chispelling() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
  
  return chispelling_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_chispelling(::std::string* chispelling) {
  if (chispelling != NULL) {
    
  } else {
    
  }
  chispelling_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), chispelling);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ChiSpelling)
}

// optional string EnglishName = 5;
void MDBasicInfo::clear_englishname() {
  englishname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::englishname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  return englishname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_englishname(const ::std::string& value) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
void MDBasicInfo::set_englishname(const char* value) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
void MDBasicInfo::set_englishname(const char* value, size_t size) {
  
  englishname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}
::std::string* MDBasicInfo::mutable_englishname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  return englishname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_englishname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
  
  return englishname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_englishname(::std::string* englishname) {
  if (englishname != NULL) {
    
  } else {
    
  }
  englishname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), englishname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.EnglishName)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDBasicInfo::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDBasicInfo::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDBasicInfo::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDBasicInfo::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDBasicInfo::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDBasicInfo::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.securityType)
}

// optional string SecuritySubType = 8;
void MDBasicInfo::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
void MDBasicInfo::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
void MDBasicInfo::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}
::std::string* MDBasicInfo::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.SecuritySubType)
}

// optional string ListDate = 9;
void MDBasicInfo::clear_listdate() {
  listdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::listdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  return listdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_listdate(const ::std::string& value) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
void MDBasicInfo::set_listdate(const char* value) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
void MDBasicInfo::set_listdate(const char* value, size_t size) {
  
  listdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}
::std::string* MDBasicInfo::mutable_listdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  return listdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_listdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
  
  return listdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_listdate(::std::string* listdate) {
  if (listdate != NULL) {
    
  } else {
    
  }
  listdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), listdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ListDate)
}

// optional string Currency = 10;
void MDBasicInfo::clear_currency() {
  currency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::currency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  return currency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_currency(const ::std::string& value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
void MDBasicInfo::set_currency(const char* value) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
void MDBasicInfo::set_currency(const char* value, size_t size) {
  
  currency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}
::std::string* MDBasicInfo::mutable_currency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  return currency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_currency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
  
  return currency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_currency(::std::string* currency) {
  if (currency != NULL) {
    
  } else {
    
  }
  currency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), currency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.Currency)
}

// optional int64 OutstandingShare = 11;
void MDBasicInfo::clear_outstandingshare() {
  outstandingshare_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::outstandingshare() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OutstandingShare)
  return outstandingshare_;
}
void MDBasicInfo::set_outstandingshare(::google::protobuf::int64 value) {
  
  outstandingshare_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OutstandingShare)
}

// optional int64 PublicFloatShareQuantity = 12;
void MDBasicInfo::clear_publicfloatsharequantity() {
  publicfloatsharequantity_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::publicfloatsharequantity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PublicFloatShareQuantity)
  return publicfloatsharequantity_;
}
void MDBasicInfo::set_publicfloatsharequantity(::google::protobuf::int64 value) {
  
  publicfloatsharequantity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PublicFloatShareQuantity)
}

// optional int32 MDDate = 13;
void MDBasicInfo::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDBasicInfo::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MDDate)
  return mddate_;
}
void MDBasicInfo::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MDDate)
}

// optional string TradingPhaseCode = 14;
void MDBasicInfo::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
void MDBasicInfo::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
void MDBasicInfo::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}
::std::string* MDBasicInfo::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.TradingPhaseCode)
}

// optional int64 PreClosePx = 15;
void MDBasicInfo::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreClosePx)
  return preclosepx_;
}
void MDBasicInfo::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreClosePx)
}

// optional int64 MaxPx = 16;
void MDBasicInfo::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxPx)
  return maxpx_;
}
void MDBasicInfo::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxPx)
}

// optional int64 MinPx = 17;
void MDBasicInfo::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinPx)
  return minpx_;
}
void MDBasicInfo::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinPx)
}

// optional int64 LotSize = 18;
void MDBasicInfo::clear_lotsize() {
  lotsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::lotsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LotSize)
  return lotsize_;
}
void MDBasicInfo::set_lotsize(::google::protobuf::int64 value) {
  
  lotsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LotSize)
}

// optional bool ShortSellFlag = 19;
void MDBasicInfo::clear_shortsellflag() {
  shortsellflag_ = false;
}
bool MDBasicInfo::shortsellflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShortSellFlag)
  return shortsellflag_;
}
void MDBasicInfo::set_shortsellflag(bool value) {
  
  shortsellflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShortSellFlag)
}

// optional string ExchangeDate = 20;
void MDBasicInfo::clear_exchangedate() {
  exchangedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  return exchangedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_exchangedate(const ::std::string& value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
void MDBasicInfo::set_exchangedate(const char* value) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
void MDBasicInfo::set_exchangedate(const char* value, size_t size) {
  
  exchangedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}
::std::string* MDBasicInfo::mutable_exchangedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  return exchangedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_exchangedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
  
  return exchangedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_exchangedate(::std::string* exchangedate) {
  if (exchangedate != NULL) {
    
  } else {
    
  }
  exchangedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeDate)
}

// optional string ExchangeSymbol = 21;
void MDBasicInfo::clear_exchangesymbol() {
  exchangesymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::exchangesymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  return exchangesymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_exchangesymbol(const ::std::string& value) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
void MDBasicInfo::set_exchangesymbol(const char* value) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
void MDBasicInfo::set_exchangesymbol(const char* value, size_t size) {
  
  exchangesymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}
::std::string* MDBasicInfo::mutable_exchangesymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  return exchangesymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_exchangesymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
  
  return exchangesymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_exchangesymbol(::std::string* exchangesymbol) {
  if (exchangesymbol != NULL) {
    
  } else {
    
  }
  exchangesymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangesymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeSymbol)
}

// optional double TickSize = 22;
void MDBasicInfo::clear_ticksize() {
  ticksize_ = 0;
}
double MDBasicInfo::ticksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.TickSize)
  return ticksize_;
}
void MDBasicInfo::set_ticksize(double value) {
  
  ticksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.TickSize)
}

// optional int32 LoanMarginIndicator = 23;
void MDBasicInfo::clear_loanmarginindicator() {
  loanmarginindicator_ = 0;
}
::google::protobuf::int32 MDBasicInfo::loanmarginindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LoanMarginIndicator)
  return loanmarginindicator_;
}
void MDBasicInfo::set_loanmarginindicator(::google::protobuf::int32 value) {
  
  loanmarginindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LoanMarginIndicator)
}

// optional int32 PxAccuracy = 24;
void MDBasicInfo::clear_pxaccuracy() {
  pxaccuracy_ = 0;
}
::google::protobuf::int32 MDBasicInfo::pxaccuracy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PxAccuracy)
  return pxaccuracy_;
}
void MDBasicInfo::set_pxaccuracy(::google::protobuf::int32 value) {
  
  pxaccuracy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PxAccuracy)
}

// optional int32 IPOProfitable = 25;
void MDBasicInfo::clear_ipoprofitable() {
  ipoprofitable_ = 0;
}
::google::protobuf::int32 MDBasicInfo::ipoprofitable() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.IPOProfitable)
  return ipoprofitable_;
}
void MDBasicInfo::set_ipoprofitable(::google::protobuf::int32 value) {
  
  ipoprofitable_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.IPOProfitable)
}

// optional int32 DiffRightsIndicator = 26;
void MDBasicInfo::clear_diffrightsindicator() {
  diffrightsindicator_ = 0;
}
::google::protobuf::int32 MDBasicInfo::diffrightsindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DiffRightsIndicator)
  return diffrightsindicator_;
}
void MDBasicInfo::set_diffrightsindicator(::google::protobuf::int32 value) {
  
  diffrightsindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DiffRightsIndicator)
}

// optional string HKSpreadTableCode = 27;
void MDBasicInfo::clear_hkspreadtablecode() {
  hkspreadtablecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::hkspreadtablecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  return hkspreadtablecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_hkspreadtablecode(const ::std::string& value) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
void MDBasicInfo::set_hkspreadtablecode(const char* value) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
void MDBasicInfo::set_hkspreadtablecode(const char* value, size_t size) {
  
  hkspreadtablecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}
::std::string* MDBasicInfo::mutable_hkspreadtablecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  return hkspreadtablecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_hkspreadtablecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
  
  return hkspreadtablecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_hkspreadtablecode(::std::string* hkspreadtablecode) {
  if (hkspreadtablecode != NULL) {
    
  } else {
    
  }
  hkspreadtablecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hkspreadtablecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.HKSpreadTableCode)
}

// optional double PreSettlePx = 28;
void MDBasicInfo::clear_presettlepx() {
  presettlepx_ = 0;
}
double MDBasicInfo::presettlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreSettlePx)
  return presettlepx_;
}
void MDBasicInfo::set_presettlepx(double value) {
  
  presettlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreSettlePx)
}

// optional double PreIOPV = 29;
void MDBasicInfo::clear_preiopv() {
  preiopv_ = 0;
}
double MDBasicInfo::preiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreIOPV)
  return preiopv_;
}
void MDBasicInfo::set_preiopv(double value) {
  
  preiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreIOPV)
}

// optional int32 ShHkConnect = 30;
void MDBasicInfo::clear_shhkconnect() {
  shhkconnect_ = 0;
}
::google::protobuf::int32 MDBasicInfo::shhkconnect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShHkConnect)
  return shhkconnect_;
}
void MDBasicInfo::set_shhkconnect(::google::protobuf::int32 value) {
  
  shhkconnect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShHkConnect)
}

// optional int32 SzHkConnect = 31;
void MDBasicInfo::clear_szhkconnect() {
  szhkconnect_ = 0;
}
::google::protobuf::int32 MDBasicInfo::szhkconnect() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SzHkConnect)
  return szhkconnect_;
}
void MDBasicInfo::set_szhkconnect(::google::protobuf::int32 value) {
  
  szhkconnect_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SzHkConnect)
}

// optional string OptionContractID = 40;
void MDBasicInfo::clear_optioncontractid() {
  optioncontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optioncontractid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  return optioncontractid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optioncontractid(const ::std::string& value) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
void MDBasicInfo::set_optioncontractid(const char* value) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
void MDBasicInfo::set_optioncontractid(const char* value, size_t size) {
  
  optioncontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}
::std::string* MDBasicInfo::mutable_optioncontractid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  return optioncontractid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optioncontractid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
  
  return optioncontractid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optioncontractid(::std::string* optioncontractid) {
  if (optioncontractid != NULL) {
    
  } else {
    
  }
  optioncontractid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncontractid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractID)
}

// optional string OptionContractSymbol = 41;
void MDBasicInfo::clear_optioncontractsymbol() {
  optioncontractsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optioncontractsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  return optioncontractsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optioncontractsymbol(const ::std::string& value) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
void MDBasicInfo::set_optioncontractsymbol(const char* value) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
void MDBasicInfo::set_optioncontractsymbol(const char* value, size_t size) {
  
  optioncontractsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}
::std::string* MDBasicInfo::mutable_optioncontractsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  return optioncontractsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optioncontractsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
  
  return optioncontractsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optioncontractsymbol(::std::string* optioncontractsymbol) {
  if (optioncontractsymbol != NULL) {
    
  } else {
    
  }
  optioncontractsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncontractsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractSymbol)
}

// optional string OptionUnderlyingSecurityID = 42;
void MDBasicInfo::clear_optionunderlyingsecurityid() {
  optionunderlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionunderlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  return optionunderlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionunderlyingsecurityid(const ::std::string& value) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
void MDBasicInfo::set_optionunderlyingsecurityid(const char* value) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
void MDBasicInfo::set_optionunderlyingsecurityid(const char* value, size_t size) {
  
  optionunderlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}
::std::string* MDBasicInfo::mutable_optionunderlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  return optionunderlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionunderlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
  
  return optionunderlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionunderlyingsecurityid(::std::string* optionunderlyingsecurityid) {
  if (optionunderlyingsecurityid != NULL) {
    
  } else {
    
  }
  optionunderlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSecurityID)
}

// optional string OptionUnderlyingSymbol = 43;
void MDBasicInfo::clear_optionunderlyingsymbol() {
  optionunderlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionunderlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  return optionunderlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionunderlyingsymbol(const ::std::string& value) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
void MDBasicInfo::set_optionunderlyingsymbol(const char* value) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
void MDBasicInfo::set_optionunderlyingsymbol(const char* value, size_t size) {
  
  optionunderlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}
::std::string* MDBasicInfo::mutable_optionunderlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  return optionunderlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionunderlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
  
  return optionunderlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionunderlyingsymbol(::std::string* optionunderlyingsymbol) {
  if (optionunderlyingsymbol != NULL) {
    
  } else {
    
  }
  optionunderlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingSymbol)
}

// optional string OptionUnderlyingType = 44;
void MDBasicInfo::clear_optionunderlyingtype() {
  optionunderlyingtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionunderlyingtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  return optionunderlyingtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionunderlyingtype(const ::std::string& value) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
void MDBasicInfo::set_optionunderlyingtype(const char* value) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
void MDBasicInfo::set_optionunderlyingtype(const char* value, size_t size) {
  
  optionunderlyingtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}
::std::string* MDBasicInfo::mutable_optionunderlyingtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  return optionunderlyingtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionunderlyingtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
  
  return optionunderlyingtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionunderlyingtype(::std::string* optionunderlyingtype) {
  if (optionunderlyingtype != NULL) {
    
  } else {
    
  }
  optionunderlyingtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionunderlyingtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingType)
}

// optional string OptionOptionType = 45;
void MDBasicInfo::clear_optionoptiontype() {
  optionoptiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionoptiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  return optionoptiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionoptiontype(const ::std::string& value) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
void MDBasicInfo::set_optionoptiontype(const char* value) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
void MDBasicInfo::set_optionoptiontype(const char* value, size_t size) {
  
  optionoptiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}
::std::string* MDBasicInfo::mutable_optionoptiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  return optionoptiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionoptiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
  
  return optionoptiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionoptiontype(::std::string* optionoptiontype) {
  if (optionoptiontype != NULL) {
    
  } else {
    
  }
  optionoptiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionoptiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionOptionType)
}

// optional string OptionCallOrPut = 46;
void MDBasicInfo::clear_optioncallorput() {
  optioncallorput_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optioncallorput() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  return optioncallorput_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optioncallorput(const ::std::string& value) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
void MDBasicInfo::set_optioncallorput(const char* value) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
void MDBasicInfo::set_optioncallorput(const char* value, size_t size) {
  
  optioncallorput_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}
::std::string* MDBasicInfo::mutable_optioncallorput() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  return optioncallorput_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optioncallorput() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
  
  return optioncallorput_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optioncallorput(::std::string* optioncallorput) {
  if (optioncallorput != NULL) {
    
  } else {
    
  }
  optioncallorput_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncallorput);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCallOrPut)
}

// optional int64 OptionContractMultiplierUnit = 47;
void MDBasicInfo::clear_optioncontractmultiplierunit() {
  optioncontractmultiplierunit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optioncontractmultiplierunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractMultiplierUnit)
  return optioncontractmultiplierunit_;
}
void MDBasicInfo::set_optioncontractmultiplierunit(::google::protobuf::int64 value) {
  
  optioncontractmultiplierunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractMultiplierUnit)
}

// optional double OptionExercisePrice = 48;
void MDBasicInfo::clear_optionexerciseprice() {
  optionexerciseprice_ = 0;
}
double MDBasicInfo::optionexerciseprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExercisePrice)
  return optionexerciseprice_;
}
void MDBasicInfo::set_optionexerciseprice(double value) {
  
  optionexerciseprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExercisePrice)
}

// optional string OptionStartDate = 49;
void MDBasicInfo::clear_optionstartdate() {
  optionstartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionstartdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  return optionstartdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionstartdate(const ::std::string& value) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
void MDBasicInfo::set_optionstartdate(const char* value) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
void MDBasicInfo::set_optionstartdate(const char* value, size_t size) {
  
  optionstartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}
::std::string* MDBasicInfo::mutable_optionstartdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  return optionstartdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionstartdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
  
  return optionstartdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionstartdate(::std::string* optionstartdate) {
  if (optionstartdate != NULL) {
    
  } else {
    
  }
  optionstartdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionstartdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionStartDate)
}

// optional string OptionEndDate = 50;
void MDBasicInfo::clear_optionenddate() {
  optionenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionenddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  return optionenddate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionenddate(const ::std::string& value) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
void MDBasicInfo::set_optionenddate(const char* value) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
void MDBasicInfo::set_optionenddate(const char* value, size_t size) {
  
  optionenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}
::std::string* MDBasicInfo::mutable_optionenddate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  return optionenddate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionenddate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
  
  return optionenddate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionenddate(::std::string* optionenddate) {
  if (optionenddate != NULL) {
    
  } else {
    
  }
  optionenddate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionenddate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionEndDate)
}

// optional string OptionExerciseDate = 51;
void MDBasicInfo::clear_optionexercisedate() {
  optionexercisedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionexercisedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  return optionexercisedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionexercisedate(const ::std::string& value) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
void MDBasicInfo::set_optionexercisedate(const char* value) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
void MDBasicInfo::set_optionexercisedate(const char* value, size_t size) {
  
  optionexercisedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}
::std::string* MDBasicInfo::mutable_optionexercisedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  return optionexercisedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionexercisedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
  
  return optionexercisedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionexercisedate(::std::string* optionexercisedate) {
  if (optionexercisedate != NULL) {
    
  } else {
    
  }
  optionexercisedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionexercisedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionExerciseDate)
}

// optional string OptionDeliveryDate = 52;
void MDBasicInfo::clear_optiondeliverydate() {
  optiondeliverydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optiondeliverydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  return optiondeliverydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optiondeliverydate(const ::std::string& value) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
void MDBasicInfo::set_optiondeliverydate(const char* value) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
void MDBasicInfo::set_optiondeliverydate(const char* value, size_t size) {
  
  optiondeliverydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}
::std::string* MDBasicInfo::mutable_optiondeliverydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  return optiondeliverydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optiondeliverydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
  
  return optiondeliverydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optiondeliverydate(::std::string* optiondeliverydate) {
  if (optiondeliverydate != NULL) {
    
  } else {
    
  }
  optiondeliverydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiondeliverydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryDate)
}

// optional string OptionExpireDate = 53;
void MDBasicInfo::clear_optionexpiredate() {
  optionexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  return optionexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionexpiredate(const ::std::string& value) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
void MDBasicInfo::set_optionexpiredate(const char* value) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
void MDBasicInfo::set_optionexpiredate(const char* value, size_t size) {
  
  optionexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}
::std::string* MDBasicInfo::mutable_optionexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  return optionexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
  
  return optionexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionexpiredate(::std::string* optionexpiredate) {
  if (optionexpiredate != NULL) {
    
  } else {
    
  }
  optionexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionExpireDate)
}

// optional string OptionUpdateVersion = 54;
void MDBasicInfo::clear_optionupdateversion() {
  optionupdateversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionupdateversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  return optionupdateversion_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionupdateversion(const ::std::string& value) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
void MDBasicInfo::set_optionupdateversion(const char* value) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
void MDBasicInfo::set_optionupdateversion(const char* value, size_t size) {
  
  optionupdateversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}
::std::string* MDBasicInfo::mutable_optionupdateversion() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  return optionupdateversion_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionupdateversion() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
  
  return optionupdateversion_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionupdateversion(::std::string* optionupdateversion) {
  if (optionupdateversion != NULL) {
    
  } else {
    
  }
  optionupdateversion_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionupdateversion);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionUpdateVersion)
}

// optional int64 OptionTotalLongPosition = 55;
void MDBasicInfo::clear_optiontotallongposition() {
  optiontotallongposition_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optiontotallongposition() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionTotalLongPosition)
  return optiontotallongposition_;
}
void MDBasicInfo::set_optiontotallongposition(::google::protobuf::int64 value) {
  
  optiontotallongposition_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionTotalLongPosition)
}

// optional double OptionSecurityClosePx = 56;
void MDBasicInfo::clear_optionsecurityclosepx() {
  optionsecurityclosepx_ = 0;
}
double MDBasicInfo::optionsecurityclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityClosePx)
  return optionsecurityclosepx_;
}
void MDBasicInfo::set_optionsecurityclosepx(double value) {
  
  optionsecurityclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityClosePx)
}

// optional double OptionSettlPrice = 57;
void MDBasicInfo::clear_optionsettlprice() {
  optionsettlprice_ = 0;
}
double MDBasicInfo::optionsettlprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSettlPrice)
  return optionsettlprice_;
}
void MDBasicInfo::set_optionsettlprice(double value) {
  
  optionsettlprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSettlPrice)
}

// optional double OptionUnderlyingClosePx = 58;
void MDBasicInfo::clear_optionunderlyingclosepx() {
  optionunderlyingclosepx_ = 0;
}
double MDBasicInfo::optionunderlyingclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingClosePx)
  return optionunderlyingclosepx_;
}
void MDBasicInfo::set_optionunderlyingclosepx(double value) {
  
  optionunderlyingclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionUnderlyingClosePx)
}

// optional string OptionPriceLimitType = 59;
void MDBasicInfo::clear_optionpricelimittype() {
  optionpricelimittype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionpricelimittype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  return optionpricelimittype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionpricelimittype(const ::std::string& value) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
void MDBasicInfo::set_optionpricelimittype(const char* value) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
void MDBasicInfo::set_optionpricelimittype(const char* value, size_t size) {
  
  optionpricelimittype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}
::std::string* MDBasicInfo::mutable_optionpricelimittype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  return optionpricelimittype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionpricelimittype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
  
  return optionpricelimittype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionpricelimittype(::std::string* optionpricelimittype) {
  if (optionpricelimittype != NULL) {
    
  } else {
    
  }
  optionpricelimittype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionpricelimittype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionPriceLimitType)
}

// optional double OptionDailyPriceUpLimit = 60;
void MDBasicInfo::clear_optiondailypriceuplimit() {
  optiondailypriceuplimit_ = 0;
}
double MDBasicInfo::optiondailypriceuplimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceUpLimit)
  return optiondailypriceuplimit_;
}
void MDBasicInfo::set_optiondailypriceuplimit(double value) {
  
  optiondailypriceuplimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceUpLimit)
}

// optional double OptionDailyPriceDownLimit = 61;
void MDBasicInfo::clear_optiondailypricedownlimit() {
  optiondailypricedownlimit_ = 0;
}
double MDBasicInfo::optiondailypricedownlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceDownLimit)
  return optiondailypricedownlimit_;
}
void MDBasicInfo::set_optiondailypricedownlimit(double value) {
  
  optiondailypricedownlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDailyPriceDownLimit)
}

// optional double OptionMarginUnit = 62;
void MDBasicInfo::clear_optionmarginunit() {
  optionmarginunit_ = 0;
}
double MDBasicInfo::optionmarginunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginUnit)
  return optionmarginunit_;
}
void MDBasicInfo::set_optionmarginunit(double value) {
  
  optionmarginunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginUnit)
}

// optional double OptionMarginRatioParam1 = 63;
void MDBasicInfo::clear_optionmarginratioparam1() {
  optionmarginratioparam1_ = 0;
}
double MDBasicInfo::optionmarginratioparam1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam1)
  return optionmarginratioparam1_;
}
void MDBasicInfo::set_optionmarginratioparam1(double value) {
  
  optionmarginratioparam1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam1)
}

// optional double OptionMarginRatioParam2 = 64;
void MDBasicInfo::clear_optionmarginratioparam2() {
  optionmarginratioparam2_ = 0;
}
double MDBasicInfo::optionmarginratioparam2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam2)
  return optionmarginratioparam2_;
}
void MDBasicInfo::set_optionmarginratioparam2(double value) {
  
  optionmarginratioparam2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarginRatioParam2)
}

// optional int64 OptionRoundLot = 65;
void MDBasicInfo::clear_optionroundlot() {
  optionroundlot_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionroundlot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionRoundLot)
  return optionroundlot_;
}
void MDBasicInfo::set_optionroundlot(::google::protobuf::int64 value) {
  
  optionroundlot_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionRoundLot)
}

// optional int64 OptionLmtOrdMinFloor = 66;
void MDBasicInfo::clear_optionlmtordminfloor() {
  optionlmtordminfloor_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionlmtordminfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMinFloor)
  return optionlmtordminfloor_;
}
void MDBasicInfo::set_optionlmtordminfloor(::google::protobuf::int64 value) {
  
  optionlmtordminfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMinFloor)
}

// optional int64 OptionLmtOrdMaxFloor = 67;
void MDBasicInfo::clear_optionlmtordmaxfloor() {
  optionlmtordmaxfloor_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionlmtordmaxfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMaxFloor)
  return optionlmtordmaxfloor_;
}
void MDBasicInfo::set_optionlmtordmaxfloor(::google::protobuf::int64 value) {
  
  optionlmtordmaxfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLmtOrdMaxFloor)
}

// optional int64 OptionMktOrdMinFloor = 68;
void MDBasicInfo::clear_optionmktordminfloor() {
  optionmktordminfloor_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionmktordminfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMinFloor)
  return optionmktordminfloor_;
}
void MDBasicInfo::set_optionmktordminfloor(::google::protobuf::int64 value) {
  
  optionmktordminfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMinFloor)
}

// optional int64 OptionMktOrdMaxFloor = 69;
void MDBasicInfo::clear_optionmktordmaxfloor() {
  optionmktordmaxfloor_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionmktordmaxfloor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMaxFloor)
  return optionmktordmaxfloor_;
}
void MDBasicInfo::set_optionmktordmaxfloor(::google::protobuf::int64 value) {
  
  optionmktordmaxfloor_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMktOrdMaxFloor)
}

// optional double OptionTickSize = 70;
void MDBasicInfo::clear_optionticksize() {
  optionticksize_ = 0;
}
double MDBasicInfo::optionticksize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionTickSize)
  return optionticksize_;
}
void MDBasicInfo::set_optionticksize(double value) {
  
  optionticksize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionTickSize)
}

// optional string OptionSecurityStatusFlag = 71;
void MDBasicInfo::clear_optionsecuritystatusflag() {
  optionsecuritystatusflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionsecuritystatusflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  return optionsecuritystatusflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionsecuritystatusflag(const ::std::string& value) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
void MDBasicInfo::set_optionsecuritystatusflag(const char* value) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
void MDBasicInfo::set_optionsecuritystatusflag(const char* value, size_t size) {
  
  optionsecuritystatusflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}
::std::string* MDBasicInfo::mutable_optionsecuritystatusflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  return optionsecuritystatusflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionsecuritystatusflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
  
  return optionsecuritystatusflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionsecuritystatusflag(::std::string* optionsecuritystatusflag) {
  if (optionsecuritystatusflag != NULL) {
    
  } else {
    
  }
  optionsecuritystatusflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionsecuritystatusflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionSecurityStatusFlag)
}

// optional string OptionCarryInterestDate = 72;
void MDBasicInfo::clear_optioncarryinterestdate() {
  optioncarryinterestdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optioncarryinterestdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  return optioncarryinterestdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optioncarryinterestdate(const ::std::string& value) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
void MDBasicInfo::set_optioncarryinterestdate(const char* value) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
void MDBasicInfo::set_optioncarryinterestdate(const char* value, size_t size) {
  
  optioncarryinterestdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}
::std::string* MDBasicInfo::mutable_optioncarryinterestdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  return optioncarryinterestdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optioncarryinterestdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
  
  return optioncarryinterestdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optioncarryinterestdate(::std::string* optioncarryinterestdate) {
  if (optioncarryinterestdate != NULL) {
    
  } else {
    
  }
  optioncarryinterestdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncarryinterestdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCarryInterestDate)
}

// optional string OptionEarlyExpireDate = 73;
void MDBasicInfo::clear_optionearlyexpiredate() {
  optionearlyexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionearlyexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  return optionearlyexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionearlyexpiredate(const ::std::string& value) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
void MDBasicInfo::set_optionearlyexpiredate(const char* value) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
void MDBasicInfo::set_optionearlyexpiredate(const char* value, size_t size) {
  
  optionearlyexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}
::std::string* MDBasicInfo::mutable_optionearlyexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  return optionearlyexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionearlyexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
  
  return optionearlyexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionearlyexpiredate(::std::string* optionearlyexpiredate) {
  if (optionearlyexpiredate != NULL) {
    
  } else {
    
  }
  optionearlyexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionearlyexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionEarlyExpireDate)
}

// optional string OptionStrategySecurityID = 74;
void MDBasicInfo::clear_optionstrategysecurityid() {
  optionstrategysecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionstrategysecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  return optionstrategysecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionstrategysecurityid(const ::std::string& value) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
void MDBasicInfo::set_optionstrategysecurityid(const char* value) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
void MDBasicInfo::set_optionstrategysecurityid(const char* value, size_t size) {
  
  optionstrategysecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}
::std::string* MDBasicInfo::mutable_optionstrategysecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  return optionstrategysecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionstrategysecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
  
  return optionstrategysecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionstrategysecurityid(::std::string* optionstrategysecurityid) {
  if (optionstrategysecurityid != NULL) {
    
  } else {
    
  }
  optionstrategysecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionstrategysecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionStrategySecurityID)
}

// optional string FITradeProductType = 80;
void MDBasicInfo::clear_fitradeproducttype() {
  fitradeproducttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fitradeproducttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  return fitradeproducttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fitradeproducttype(const ::std::string& value) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
void MDBasicInfo::set_fitradeproducttype(const char* value) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
void MDBasicInfo::set_fitradeproducttype(const char* value, size_t size) {
  
  fitradeproducttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}
::std::string* MDBasicInfo::mutable_fitradeproducttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  return fitradeproducttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fitradeproducttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
  
  return fitradeproducttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fitradeproducttype(::std::string* fitradeproducttype) {
  if (fitradeproducttype != NULL) {
    
  } else {
    
  }
  fitradeproducttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fitradeproducttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FITradeProductType)
}

// optional string FISecurityProperty = 81;
void MDBasicInfo::clear_fisecurityproperty() {
  fisecurityproperty_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fisecurityproperty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  return fisecurityproperty_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fisecurityproperty(const ::std::string& value) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
void MDBasicInfo::set_fisecurityproperty(const char* value) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
void MDBasicInfo::set_fisecurityproperty(const char* value, size_t size) {
  
  fisecurityproperty_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}
::std::string* MDBasicInfo::mutable_fisecurityproperty() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  return fisecurityproperty_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fisecurityproperty() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
  
  return fisecurityproperty_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fisecurityproperty(::std::string* fisecurityproperty) {
  if (fisecurityproperty != NULL) {
    
  } else {
    
  }
  fisecurityproperty_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fisecurityproperty);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityProperty)
}

// optional string FISecurityStatus = 82;
void MDBasicInfo::clear_fisecuritystatus() {
  fisecuritystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fisecuritystatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  return fisecuritystatus_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fisecuritystatus(const ::std::string& value) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
void MDBasicInfo::set_fisecuritystatus(const char* value) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
void MDBasicInfo::set_fisecuritystatus(const char* value, size_t size) {
  
  fisecuritystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}
::std::string* MDBasicInfo::mutable_fisecuritystatus() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  return fisecuritystatus_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fisecuritystatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
  
  return fisecuritystatus_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fisecuritystatus(::std::string* fisecuritystatus) {
  if (fisecuritystatus != NULL) {
    
  } else {
    
  }
  fisecuritystatus_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fisecuritystatus);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FISecurityStatus)
}

// optional string FIPledgedSecurityID = 83;
void MDBasicInfo::clear_fipledgedsecurityid() {
  fipledgedsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fipledgedsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  return fipledgedsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fipledgedsecurityid(const ::std::string& value) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
void MDBasicInfo::set_fipledgedsecurityid(const char* value) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
void MDBasicInfo::set_fipledgedsecurityid(const char* value, size_t size) {
  
  fipledgedsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}
::std::string* MDBasicInfo::mutable_fipledgedsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  return fipledgedsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fipledgedsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
  
  return fipledgedsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fipledgedsecurityid(::std::string* fipledgedsecurityid) {
  if (fipledgedsecurityid != NULL) {
    
  } else {
    
  }
  fipledgedsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fipledgedsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIPledgedSecurityID)
}

// optional string FIOpenTime = 84;
void MDBasicInfo::clear_fiopentime() {
  fiopentime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiopentime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  return fiopentime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiopentime(const ::std::string& value) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
void MDBasicInfo::set_fiopentime(const char* value) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
void MDBasicInfo::set_fiopentime(const char* value, size_t size) {
  
  fiopentime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}
::std::string* MDBasicInfo::mutable_fiopentime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  return fiopentime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiopentime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
  
  return fiopentime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiopentime(::std::string* fiopentime) {
  if (fiopentime != NULL) {
    
  } else {
    
  }
  fiopentime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiopentime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIOpenTime)
}

// optional string FICloseTime = 85;
void MDBasicInfo::clear_ficlosetime() {
  ficlosetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::ficlosetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  return ficlosetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_ficlosetime(const ::std::string& value) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
void MDBasicInfo::set_ficlosetime(const char* value) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
void MDBasicInfo::set_ficlosetime(const char* value, size_t size) {
  
  ficlosetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}
::std::string* MDBasicInfo::mutable_ficlosetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  return ficlosetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_ficlosetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
  
  return ficlosetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_ficlosetime(::std::string* ficlosetime) {
  if (ficlosetime != NULL) {
    
  } else {
    
  }
  ficlosetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ficlosetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FICloseTime)
}

// optional string FIIssueMode = 86;
void MDBasicInfo::clear_fiissuemode() {
  fiissuemode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiissuemode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  return fiissuemode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiissuemode(const ::std::string& value) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
void MDBasicInfo::set_fiissuemode(const char* value) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
void MDBasicInfo::set_fiissuemode(const char* value, size_t size) {
  
  fiissuemode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}
::std::string* MDBasicInfo::mutable_fiissuemode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  return fiissuemode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiissuemode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
  
  return fiissuemode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiissuemode(::std::string* fiissuemode) {
  if (fiissuemode != NULL) {
    
  } else {
    
  }
  fiissuemode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuemode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMode)
}

// optional double FIFaceAmount = 87;
void MDBasicInfo::clear_fifaceamount() {
  fifaceamount_ = 0;
}
double MDBasicInfo::fifaceamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIFaceAmount)
  return fifaceamount_;
}
void MDBasicInfo::set_fifaceamount(double value) {
  
  fifaceamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIFaceAmount)
}

// optional double FIIssuePrice = 88;
void MDBasicInfo::clear_fiissueprice() {
  fiissueprice_ = 0;
}
double MDBasicInfo::fiissueprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssuePrice)
  return fiissueprice_;
}
void MDBasicInfo::set_fiissueprice(double value) {
  
  fiissueprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssuePrice)
}

// optional string FIInterestType = 89;
void MDBasicInfo::clear_fiinteresttype() {
  fiinteresttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiinteresttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  return fiinteresttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiinteresttype(const ::std::string& value) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
void MDBasicInfo::set_fiinteresttype(const char* value) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
void MDBasicInfo::set_fiinteresttype(const char* value, size_t size) {
  
  fiinteresttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}
::std::string* MDBasicInfo::mutable_fiinteresttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  return fiinteresttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiinteresttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
  
  return fiinteresttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiinteresttype(::std::string* fiinteresttype) {
  if (fiinteresttype != NULL) {
    
  } else {
    
  }
  fiinteresttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiinteresttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestType)
}

// optional string FIInterestFrequency = 90;
void MDBasicInfo::clear_fiinterestfrequency() {
  fiinterestfrequency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiinterestfrequency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  return fiinterestfrequency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiinterestfrequency(const ::std::string& value) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
void MDBasicInfo::set_fiinterestfrequency(const char* value) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
void MDBasicInfo::set_fiinterestfrequency(const char* value, size_t size) {
  
  fiinterestfrequency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}
::std::string* MDBasicInfo::mutable_fiinterestfrequency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  return fiinterestfrequency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiinterestfrequency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
  
  return fiinterestfrequency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiinterestfrequency(::std::string* fiinterestfrequency) {
  if (fiinterestfrequency != NULL) {
    
  } else {
    
  }
  fiinterestfrequency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiinterestfrequency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIInterestFrequency)
}

// optional double FIGuaranteedInterestRate = 91;
void MDBasicInfo::clear_figuaranteedinterestrate() {
  figuaranteedinterestrate_ = 0;
}
double MDBasicInfo::figuaranteedinterestrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIGuaranteedInterestRate)
  return figuaranteedinterestrate_;
}
void MDBasicInfo::set_figuaranteedinterestrate(double value) {
  
  figuaranteedinterestrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIGuaranteedInterestRate)
}

// optional double FIBaseInterestRate = 92;
void MDBasicInfo::clear_fibaseinterestrate() {
  fibaseinterestrate_ = 0;
}
double MDBasicInfo::fibaseinterestrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIBaseInterestRate)
  return fibaseinterestrate_;
}
void MDBasicInfo::set_fibaseinterestrate(double value) {
  
  fibaseinterestrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIBaseInterestRate)
}

// optional double FIQuotedMargin = 93;
void MDBasicInfo::clear_fiquotedmargin() {
  fiquotedmargin_ = 0;
}
double MDBasicInfo::fiquotedmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIQuotedMargin)
  return fiquotedmargin_;
}
void MDBasicInfo::set_fiquotedmargin(double value) {
  
  fiquotedmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIQuotedMargin)
}

// optional int32 FITimeLimit = 94;
void MDBasicInfo::clear_fitimelimit() {
  fitimelimit_ = 0;
}
::google::protobuf::int32 MDBasicInfo::fitimelimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITimeLimit)
  return fitimelimit_;
}
void MDBasicInfo::set_fitimelimit(::google::protobuf::int32 value) {
  
  fitimelimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITimeLimit)
}

// optional double FITotalIssuance = 95;
void MDBasicInfo::clear_fitotalissuance() {
  fitotalissuance_ = 0;
}
double MDBasicInfo::fitotalissuance() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITotalIssuance)
  return fitotalissuance_;
}
void MDBasicInfo::set_fitotalissuance(double value) {
  
  fitotalissuance_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITotalIssuance)
}

// optional string FIIssueStartDate = 96;
void MDBasicInfo::clear_fiissuestartdate() {
  fiissuestartdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiissuestartdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  return fiissuestartdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiissuestartdate(const ::std::string& value) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
void MDBasicInfo::set_fiissuestartdate(const char* value) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
void MDBasicInfo::set_fiissuestartdate(const char* value, size_t size) {
  
  fiissuestartdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}
::std::string* MDBasicInfo::mutable_fiissuestartdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  return fiissuestartdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiissuestartdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
  
  return fiissuestartdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiissuestartdate(::std::string* fiissuestartdate) {
  if (fiissuestartdate != NULL) {
    
  } else {
    
  }
  fiissuestartdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuestartdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueStartDate)
}

// optional string FIIssueEndDate = 97;
void MDBasicInfo::clear_fiissueenddate() {
  fiissueenddate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiissueenddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  return fiissueenddate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiissueenddate(const ::std::string& value) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
void MDBasicInfo::set_fiissueenddate(const char* value) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
void MDBasicInfo::set_fiissueenddate(const char* value, size_t size) {
  
  fiissueenddate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}
::std::string* MDBasicInfo::mutable_fiissueenddate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  return fiissueenddate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiissueenddate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
  
  return fiissueenddate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiissueenddate(::std::string* fiissueenddate) {
  if (fiissueenddate != NULL) {
    
  } else {
    
  }
  fiissueenddate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissueenddate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueEndDate)
}

// optional string FIListDate = 98;
void MDBasicInfo::clear_filistdate() {
  filistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::filistdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  return filistdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_filistdate(const ::std::string& value) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
void MDBasicInfo::set_filistdate(const char* value) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
void MDBasicInfo::set_filistdate(const char* value, size_t size) {
  
  filistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}
::std::string* MDBasicInfo::mutable_filistdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  return filistdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_filistdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
  
  return filistdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_filistdate(::std::string* filistdate) {
  if (filistdate != NULL) {
    
  } else {
    
  }
  filistdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), filistdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIListDate)
}

// optional string FIExpireDate = 99;
void MDBasicInfo::clear_fiexpiredate() {
  fiexpiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiexpiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  return fiexpiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiexpiredate(const ::std::string& value) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
void MDBasicInfo::set_fiexpiredate(const char* value) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
void MDBasicInfo::set_fiexpiredate(const char* value, size_t size) {
  
  fiexpiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}
::std::string* MDBasicInfo::mutable_fiexpiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  return fiexpiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiexpiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
  
  return fiexpiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiexpiredate(::std::string* fiexpiredate) {
  if (fiexpiredate != NULL) {
    
  } else {
    
  }
  fiexpiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiexpiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIExpireDate)
}

// optional string FINationalDebtType = 100;
void MDBasicInfo::clear_finationaldebttype() {
  finationaldebttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::finationaldebttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  return finationaldebttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_finationaldebttype(const ::std::string& value) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
void MDBasicInfo::set_finationaldebttype(const char* value) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
void MDBasicInfo::set_finationaldebttype(const char* value, size_t size) {
  
  finationaldebttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}
::std::string* MDBasicInfo::mutable_finationaldebttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  return finationaldebttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_finationaldebttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
  
  return finationaldebttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_finationaldebttype(::std::string* finationaldebttype) {
  if (finationaldebttype != NULL) {
    
  } else {
    
  }
  finationaldebttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), finationaldebttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FINationalDebtType)
}

// optional string FIIssueMethod = 101;
void MDBasicInfo::clear_fiissuemethod() {
  fiissuemethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::fiissuemethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  return fiissuemethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_fiissuemethod(const ::std::string& value) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
void MDBasicInfo::set_fiissuemethod(const char* value) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
void MDBasicInfo::set_fiissuemethod(const char* value, size_t size) {
  
  fiissuemethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}
::std::string* MDBasicInfo::mutable_fiissuemethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  return fiissuemethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_fiissuemethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
  
  return fiissuemethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_fiissuemethod(::std::string* fiissuemethod) {
  if (fiissuemethod != NULL) {
    
  } else {
    
  }
  fiissuemethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fiissuemethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FIIssueMethod)
}

// optional bool FICrossMarket = 102;
void MDBasicInfo::clear_ficrossmarket() {
  ficrossmarket_ = false;
}
bool MDBasicInfo::ficrossmarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FICrossMarket)
  return ficrossmarket_;
}
void MDBasicInfo::set_ficrossmarket(bool value) {
  
  ficrossmarket_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FICrossMarket)
}

// optional bool FIShortSellFlag = 103;
void MDBasicInfo::clear_fishortsellflag() {
  fishortsellflag_ = false;
}
bool MDBasicInfo::fishortsellflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIShortSellFlag)
  return fishortsellflag_;
}
void MDBasicInfo::set_fishortsellflag(bool value) {
  
  fishortsellflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIShortSellFlag)
}

// optional double FITotalShortSellQuota = 104;
void MDBasicInfo::clear_fitotalshortsellquota() {
  fitotalshortsellquota_ = 0;
}
double MDBasicInfo::fitotalshortsellquota() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FITotalShortSellQuota)
  return fitotalshortsellquota_;
}
void MDBasicInfo::set_fitotalshortsellquota(double value) {
  
  fitotalshortsellquota_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FITotalShortSellQuota)
}

// optional double FIDealerShortSellQuota = 105;
void MDBasicInfo::clear_fidealershortsellquota() {
  fidealershortsellquota_ = 0;
}
double MDBasicInfo::fidealershortsellquota() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIDealerShortSellQuota)
  return fidealershortsellquota_;
}
void MDBasicInfo::set_fidealershortsellquota(double value) {
  
  fidealershortsellquota_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIDealerShortSellQuota)
}

// optional double FIPreClosePx = 106;
void MDBasicInfo::clear_fipreclosepx() {
  fipreclosepx_ = 0;
}
double MDBasicInfo::fipreclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPreClosePx)
  return fipreclosepx_;
}
void MDBasicInfo::set_fipreclosepx(double value) {
  
  fipreclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPreClosePx)
}

// optional double FIPreWeightedPx = 107;
void MDBasicInfo::clear_fipreweightedpx() {
  fipreweightedpx_ = 0;
}
double MDBasicInfo::fipreweightedpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FIPreWeightedPx)
  return fipreweightedpx_;
}
void MDBasicInfo::set_fipreweightedpx(double value) {
  
  fipreweightedpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FIPreWeightedPx)
}

// optional string OptionListType = 110;
void MDBasicInfo::clear_optionlisttype() {
  optionlisttype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionlisttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  return optionlisttype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionlisttype(const ::std::string& value) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
void MDBasicInfo::set_optionlisttype(const char* value) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
void MDBasicInfo::set_optionlisttype(const char* value, size_t size) {
  
  optionlisttype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}
::std::string* MDBasicInfo::mutable_optionlisttype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  return optionlisttype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionlisttype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
  
  return optionlisttype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionlisttype(::std::string* optionlisttype) {
  if (optionlisttype != NULL) {
    
  } else {
    
  }
  optionlisttype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionlisttype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionListType)
}

// optional string OptionDeliveryType = 111;
void MDBasicInfo::clear_optiondeliverytype() {
  optiondeliverytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optiondeliverytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  return optiondeliverytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optiondeliverytype(const ::std::string& value) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
void MDBasicInfo::set_optiondeliverytype(const char* value) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
void MDBasicInfo::set_optiondeliverytype(const char* value, size_t size) {
  
  optiondeliverytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}
::std::string* MDBasicInfo::mutable_optiondeliverytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  return optiondeliverytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optiondeliverytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
  
  return optiondeliverytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optiondeliverytype(::std::string* optiondeliverytype) {
  if (optiondeliverytype != NULL) {
    
  } else {
    
  }
  optiondeliverytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiondeliverytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionDeliveryType)
}

// optional int32 OptionAdjustTimes = 112;
void MDBasicInfo::clear_optionadjusttimes() {
  optionadjusttimes_ = 0;
}
::google::protobuf::int32 MDBasicInfo::optionadjusttimes() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionAdjustTimes)
  return optionadjusttimes_;
}
void MDBasicInfo::set_optionadjusttimes(::google::protobuf::int32 value) {
  
  optionadjusttimes_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionAdjustTimes)
}

// optional int64 OptionContractPosition = 113;
void MDBasicInfo::clear_optioncontractposition() {
  optioncontractposition_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optioncontractposition() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractPosition)
  return optioncontractposition_;
}
void MDBasicInfo::set_optioncontractposition(::google::protobuf::int64 value) {
  
  optioncontractposition_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionContractPosition)
}

// optional int64 OptionBuyQtyUpperLimit = 114;
void MDBasicInfo::clear_optionbuyqtyupperlimit() {
  optionbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUpperLimit)
  return optionbuyqtyupperlimit_;
}
void MDBasicInfo::set_optionbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUpperLimit)
}

// optional int64 OptionSellQtyUpperLimit = 115;
void MDBasicInfo::clear_optionsellqtyupperlimit() {
  optionsellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionsellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUpperLimit)
  return optionsellqtyupperlimit_;
}
void MDBasicInfo::set_optionsellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionsellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUpperLimit)
}

// optional int64 OptionMarketOrderBuyQtyUpperLimit = 116;
void MDBasicInfo::clear_optionmarketorderbuyqtyupperlimit() {
  optionmarketorderbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionmarketorderbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderBuyQtyUpperLimit)
  return optionmarketorderbuyqtyupperlimit_;
}
void MDBasicInfo::set_optionmarketorderbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionmarketorderbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderBuyQtyUpperLimit)
}

// optional int64 OptionMarketOrderSellQtyUpperLimit = 117;
void MDBasicInfo::clear_optionmarketordersellqtyupperlimit() {
  optionmarketordersellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionmarketordersellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderSellQtyUpperLimit)
  return optionmarketordersellqtyupperlimit_;
}
void MDBasicInfo::set_optionmarketordersellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionmarketordersellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketOrderSellQtyUpperLimit)
}

// optional int64 OptionQuoteOrderBuyQtyUpperLimit = 118;
void MDBasicInfo::clear_optionquoteorderbuyqtyupperlimit() {
  optionquoteorderbuyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionquoteorderbuyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderBuyQtyUpperLimit)
  return optionquoteorderbuyqtyupperlimit_;
}
void MDBasicInfo::set_optionquoteorderbuyqtyupperlimit(::google::protobuf::int64 value) {
  
  optionquoteorderbuyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderBuyQtyUpperLimit)
}

// optional int64 OptionQuoteOrderSellQtyUpperLimit = 119;
void MDBasicInfo::clear_optionquoteordersellqtyupperlimit() {
  optionquoteordersellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionquoteordersellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderSellQtyUpperLimit)
  return optionquoteordersellqtyupperlimit_;
}
void MDBasicInfo::set_optionquoteordersellqtyupperlimit(::google::protobuf::int64 value) {
  
  optionquoteordersellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionQuoteOrderSellQtyUpperLimit)
}

// optional int64 OptionBuyQtyUnit = 120;
void MDBasicInfo::clear_optionbuyqtyunit() {
  optionbuyqtyunit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionbuyqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUnit)
  return optionbuyqtyunit_;
}
void MDBasicInfo::set_optionbuyqtyunit(::google::protobuf::int64 value) {
  
  optionbuyqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionBuyQtyUnit)
}

// optional int64 OptionSellQtyUnit = 121;
void MDBasicInfo::clear_optionsellqtyunit() {
  optionsellqtyunit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::optionsellqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUnit)
  return optionsellqtyunit_;
}
void MDBasicInfo::set_optionsellqtyunit(::google::protobuf::int64 value) {
  
  optionsellqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellQtyUnit)
}

// optional double OptionLastSellMargin = 122;
void MDBasicInfo::clear_optionlastsellmargin() {
  optionlastsellmargin_ = 0;
}
double MDBasicInfo::optionlastsellmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionLastSellMargin)
  return optionlastsellmargin_;
}
void MDBasicInfo::set_optionlastsellmargin(double value) {
  
  optionlastsellmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionLastSellMargin)
}

// optional double OptionSellMargin = 123;
void MDBasicInfo::clear_optionsellmargin() {
  optionsellmargin_ = 0;
}
double MDBasicInfo::optionsellmargin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellMargin)
  return optionsellmargin_;
}
void MDBasicInfo::set_optionsellmargin(double value) {
  
  optionsellmargin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionSellMargin)
}

// optional string OptionMarketMakerFlag = 124;
void MDBasicInfo::clear_optionmarketmakerflag() {
  optionmarketmakerflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optionmarketmakerflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  return optionmarketmakerflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optionmarketmakerflag(const ::std::string& value) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
void MDBasicInfo::set_optionmarketmakerflag(const char* value) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
void MDBasicInfo::set_optionmarketmakerflag(const char* value, size_t size) {
  
  optionmarketmakerflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}
::std::string* MDBasicInfo::mutable_optionmarketmakerflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  return optionmarketmakerflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optionmarketmakerflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
  
  return optionmarketmakerflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optionmarketmakerflag(::std::string* optionmarketmakerflag) {
  if (optionmarketmakerflag != NULL) {
    
  } else {
    
  }
  optionmarketmakerflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optionmarketmakerflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionMarketMakerFlag)
}

// optional string OptionCombinationStrategy = 125;
void MDBasicInfo::clear_optioncombinationstrategy() {
  optioncombinationstrategy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::optioncombinationstrategy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  return optioncombinationstrategy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_optioncombinationstrategy(const ::std::string& value) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
void MDBasicInfo::set_optioncombinationstrategy(const char* value) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
void MDBasicInfo::set_optioncombinationstrategy(const char* value, size_t size) {
  
  optioncombinationstrategy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}
::std::string* MDBasicInfo::mutable_optioncombinationstrategy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  return optioncombinationstrategy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_optioncombinationstrategy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
  
  return optioncombinationstrategy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_optioncombinationstrategy(::std::string* optioncombinationstrategy) {
  if (optioncombinationstrategy != NULL) {
    
  } else {
    
  }
  optioncombinationstrategy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optioncombinationstrategy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.OptionCombinationStrategy)
}

// optional string DeliveryYear = 126;
void MDBasicInfo::clear_deliveryyear() {
  deliveryyear_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::deliveryyear() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  return deliveryyear_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_deliveryyear(const ::std::string& value) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
void MDBasicInfo::set_deliveryyear(const char* value) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
void MDBasicInfo::set_deliveryyear(const char* value, size_t size) {
  
  deliveryyear_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}
::std::string* MDBasicInfo::mutable_deliveryyear() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  return deliveryyear_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_deliveryyear() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
  
  return deliveryyear_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_deliveryyear(::std::string* deliveryyear) {
  if (deliveryyear != NULL) {
    
  } else {
    
  }
  deliveryyear_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deliveryyear);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryYear)
}

// optional string DeliveryMonth = 127;
void MDBasicInfo::clear_deliverymonth() {
  deliverymonth_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::deliverymonth() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  return deliverymonth_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_deliverymonth(const ::std::string& value) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
void MDBasicInfo::set_deliverymonth(const char* value) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
void MDBasicInfo::set_deliverymonth(const char* value, size_t size) {
  
  deliverymonth_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}
::std::string* MDBasicInfo::mutable_deliverymonth() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  return deliverymonth_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_deliverymonth() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
  
  return deliverymonth_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_deliverymonth(::std::string* deliverymonth) {
  if (deliverymonth != NULL) {
    
  } else {
    
  }
  deliverymonth_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deliverymonth);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DeliveryMonth)
}

// optional string InstrumentID = 128;
void MDBasicInfo::clear_instrumentid() {
  instrumentid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::instrumentid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  return instrumentid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_instrumentid(const ::std::string& value) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
void MDBasicInfo::set_instrumentid(const char* value) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
void MDBasicInfo::set_instrumentid(const char* value, size_t size) {
  
  instrumentid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}
::std::string* MDBasicInfo::mutable_instrumentid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  return instrumentid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_instrumentid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
  
  return instrumentid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_instrumentid(::std::string* instrumentid) {
  if (instrumentid != NULL) {
    
  } else {
    
  }
  instrumentid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentID)
}

// optional string InstrumentName = 129;
void MDBasicInfo::clear_instrumentname() {
  instrumentname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::instrumentname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  return instrumentname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_instrumentname(const ::std::string& value) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
void MDBasicInfo::set_instrumentname(const char* value) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
void MDBasicInfo::set_instrumentname(const char* value, size_t size) {
  
  instrumentname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}
::std::string* MDBasicInfo::mutable_instrumentname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  return instrumentname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_instrumentname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
  
  return instrumentname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_instrumentname(::std::string* instrumentname) {
  if (instrumentname != NULL) {
    
  } else {
    
  }
  instrumentname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InstrumentName)
}

// optional string ExchangeInstID = 130;
void MDBasicInfo::clear_exchangeinstid() {
  exchangeinstid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::exchangeinstid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  return exchangeinstid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_exchangeinstid(const ::std::string& value) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
void MDBasicInfo::set_exchangeinstid(const char* value) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
void MDBasicInfo::set_exchangeinstid(const char* value, size_t size) {
  
  exchangeinstid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}
::std::string* MDBasicInfo::mutable_exchangeinstid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  return exchangeinstid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_exchangeinstid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
  
  return exchangeinstid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_exchangeinstid(::std::string* exchangeinstid) {
  if (exchangeinstid != NULL) {
    
  } else {
    
  }
  exchangeinstid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exchangeinstid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExchangeInstID)
}

// optional string ProductID = 131;
void MDBasicInfo::clear_productid() {
  productid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::productid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  return productid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_productid(const ::std::string& value) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
void MDBasicInfo::set_productid(const char* value) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
void MDBasicInfo::set_productid(const char* value, size_t size) {
  
  productid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}
::std::string* MDBasicInfo::mutable_productid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  return productid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_productid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
  
  return productid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_productid(::std::string* productid) {
  if (productid != NULL) {
    
  } else {
    
  }
  productid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), productid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ProductID)
}

// optional int64 MaxMarketOrderVolume = 132;
void MDBasicInfo::clear_maxmarketordervolume() {
  maxmarketordervolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::maxmarketordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarketOrderVolume)
  return maxmarketordervolume_;
}
void MDBasicInfo::set_maxmarketordervolume(::google::protobuf::int64 value) {
  
  maxmarketordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarketOrderVolume)
}

// optional int64 MinMarketOrderVolume = 133;
void MDBasicInfo::clear_minmarketordervolume() {
  minmarketordervolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::minmarketordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinMarketOrderVolume)
  return minmarketordervolume_;
}
void MDBasicInfo::set_minmarketordervolume(::google::protobuf::int64 value) {
  
  minmarketordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinMarketOrderVolume)
}

// optional int64 MaxLimitOrderVolume = 134;
void MDBasicInfo::clear_maxlimitordervolume() {
  maxlimitordervolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::maxlimitordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxLimitOrderVolume)
  return maxlimitordervolume_;
}
void MDBasicInfo::set_maxlimitordervolume(::google::protobuf::int64 value) {
  
  maxlimitordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxLimitOrderVolume)
}

// optional int64 MinLimitOrderVolume = 135;
void MDBasicInfo::clear_minlimitordervolume() {
  minlimitordervolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::minlimitordervolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MinLimitOrderVolume)
  return minlimitordervolume_;
}
void MDBasicInfo::set_minlimitordervolume(::google::protobuf::int64 value) {
  
  minlimitordervolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MinLimitOrderVolume)
}

// optional int64 VolumeMultiple = 136;
void MDBasicInfo::clear_volumemultiple() {
  volumemultiple_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::volumemultiple() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.VolumeMultiple)
  return volumemultiple_;
}
void MDBasicInfo::set_volumemultiple(::google::protobuf::int64 value) {
  
  volumemultiple_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.VolumeMultiple)
}

// optional string CreateDate = 137;
void MDBasicInfo::clear_createdate() {
  createdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::createdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  return createdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_createdate(const ::std::string& value) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
void MDBasicInfo::set_createdate(const char* value) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
void MDBasicInfo::set_createdate(const char* value, size_t size) {
  
  createdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}
::std::string* MDBasicInfo::mutable_createdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  return createdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_createdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
  
  return createdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_createdate(::std::string* createdate) {
  if (createdate != NULL) {
    
  } else {
    
  }
  createdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), createdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.CreateDate)
}

// optional string ExpireDate = 138;
void MDBasicInfo::clear_expiredate() {
  expiredate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::expiredate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  return expiredate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_expiredate(const ::std::string& value) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
void MDBasicInfo::set_expiredate(const char* value) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
void MDBasicInfo::set_expiredate(const char* value, size_t size) {
  
  expiredate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}
::std::string* MDBasicInfo::mutable_expiredate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  return expiredate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_expiredate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
  
  return expiredate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_expiredate(::std::string* expiredate) {
  if (expiredate != NULL) {
    
  } else {
    
  }
  expiredate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), expiredate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.ExpireDate)
}

// optional string StartDelivDate = 139;
void MDBasicInfo::clear_startdelivdate() {
  startdelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::startdelivdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  return startdelivdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_startdelivdate(const ::std::string& value) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
void MDBasicInfo::set_startdelivdate(const char* value) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
void MDBasicInfo::set_startdelivdate(const char* value, size_t size) {
  
  startdelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}
::std::string* MDBasicInfo::mutable_startdelivdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  return startdelivdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_startdelivdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
  
  return startdelivdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_startdelivdate(::std::string* startdelivdate) {
  if (startdelivdate != NULL) {
    
  } else {
    
  }
  startdelivdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), startdelivdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.StartDelivDate)
}

// optional string EndDelivDate = 140;
void MDBasicInfo::clear_enddelivdate() {
  enddelivdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::enddelivdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  return enddelivdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_enddelivdate(const ::std::string& value) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
void MDBasicInfo::set_enddelivdate(const char* value) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
void MDBasicInfo::set_enddelivdate(const char* value, size_t size) {
  
  enddelivdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}
::std::string* MDBasicInfo::mutable_enddelivdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  return enddelivdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_enddelivdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
  
  return enddelivdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_enddelivdate(::std::string* enddelivdate) {
  if (enddelivdate != NULL) {
    
  } else {
    
  }
  enddelivdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), enddelivdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.EndDelivDate)
}

// optional string PositionType = 141;
void MDBasicInfo::clear_positiontype() {
  positiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::positiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  return positiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_positiontype(const ::std::string& value) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
void MDBasicInfo::set_positiontype(const char* value) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
void MDBasicInfo::set_positiontype(const char* value, size_t size) {
  
  positiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}
::std::string* MDBasicInfo::mutable_positiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  return positiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_positiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
  
  return positiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_positiontype(::std::string* positiontype) {
  if (positiontype != NULL) {
    
  } else {
    
  }
  positiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), positiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.PositionType)
}

// optional double LongMarginRatio = 142;
void MDBasicInfo::clear_longmarginratio() {
  longmarginratio_ = 0;
}
double MDBasicInfo::longmarginratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.LongMarginRatio)
  return longmarginratio_;
}
void MDBasicInfo::set_longmarginratio(double value) {
  
  longmarginratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.LongMarginRatio)
}

// optional double ShortMarginRatio = 143;
void MDBasicInfo::clear_shortmarginratio() {
  shortmarginratio_ = 0;
}
double MDBasicInfo::shortmarginratio() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.ShortMarginRatio)
  return shortmarginratio_;
}
void MDBasicInfo::set_shortmarginratio(double value) {
  
  shortmarginratio_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.ShortMarginRatio)
}

// optional string MaxMarginSideAlgorithm = 144;
void MDBasicInfo::clear_maxmarginsidealgorithm() {
  maxmarginsidealgorithm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::maxmarginsidealgorithm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  return maxmarginsidealgorithm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_maxmarginsidealgorithm(const ::std::string& value) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
void MDBasicInfo::set_maxmarginsidealgorithm(const char* value) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
void MDBasicInfo::set_maxmarginsidealgorithm(const char* value, size_t size) {
  
  maxmarginsidealgorithm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}
::std::string* MDBasicInfo::mutable_maxmarginsidealgorithm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  return maxmarginsidealgorithm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_maxmarginsidealgorithm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
  
  return maxmarginsidealgorithm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_maxmarginsidealgorithm(::std::string* maxmarginsidealgorithm) {
  if (maxmarginsidealgorithm != NULL) {
    
  } else {
    
  }
  maxmarginsidealgorithm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), maxmarginsidealgorithm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.MaxMarginSideAlgorithm)
}

// optional double StrikePrice = 145;
void MDBasicInfo::clear_strikeprice() {
  strikeprice_ = 0;
}
double MDBasicInfo::strikeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.StrikePrice)
  return strikeprice_;
}
void MDBasicInfo::set_strikeprice(double value) {
  
  strikeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.StrikePrice)
}

// optional double PreOpenInterest = 146;
void MDBasicInfo::clear_preopeninterest() {
  preopeninterest_ = 0;
}
double MDBasicInfo::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.PreOpenInterest)
  return preopeninterest_;
}
void MDBasicInfo::set_preopeninterest(double value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.PreOpenInterest)
}

// optional string FormerSymbol = 147;
void MDBasicInfo::clear_formersymbol() {
  formersymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::formersymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  return formersymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_formersymbol(const ::std::string& value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
void MDBasicInfo::set_formersymbol(const char* value) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
void MDBasicInfo::set_formersymbol(const char* value, size_t size) {
  
  formersymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}
::std::string* MDBasicInfo::mutable_formersymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  return formersymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_formersymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
  
  return formersymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_formersymbol(::std::string* formersymbol) {
  if (formersymbol != NULL) {
    
  } else {
    
  }
  formersymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), formersymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.FormerSymbol)
}

// optional string DelistDate = 148;
void MDBasicInfo::clear_delistdate() {
  delistdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::delistdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  return delistdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_delistdate(const ::std::string& value) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
void MDBasicInfo::set_delistdate(const char* value) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
void MDBasicInfo::set_delistdate(const char* value, size_t size) {
  
  delistdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}
::std::string* MDBasicInfo::mutable_delistdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  return delistdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_delistdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
  
  return delistdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_delistdate(::std::string* delistdate) {
  if (delistdate != NULL) {
    
  } else {
    
  }
  delistdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), delistdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.DelistDate)
}

// optional int64 BuyQtyUnit = 149;
void MDBasicInfo::clear_buyqtyunit() {
  buyqtyunit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::buyqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUnit)
  return buyqtyunit_;
}
void MDBasicInfo::set_buyqtyunit(::google::protobuf::int64 value) {
  
  buyqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUnit)
}

// optional int64 SellQtyUnit = 150;
void MDBasicInfo::clear_sellqtyunit() {
  sellqtyunit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::sellqtyunit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUnit)
  return sellqtyunit_;
}
void MDBasicInfo::set_sellqtyunit(::google::protobuf::int64 value) {
  
  sellqtyunit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUnit)
}

// optional int64 BuyQtyUpperLimit = 161;
void MDBasicInfo::clear_buyqtyupperlimit() {
  buyqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::buyqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUpperLimit)
  return buyqtyupperlimit_;
}
void MDBasicInfo::set_buyqtyupperlimit(::google::protobuf::int64 value) {
  
  buyqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyUpperLimit)
}

// optional int64 SellQtyUpperLimit = 162;
void MDBasicInfo::clear_sellqtyupperlimit() {
  sellqtyupperlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::sellqtyupperlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUpperLimit)
  return sellqtyupperlimit_;
}
void MDBasicInfo::set_sellqtyupperlimit(::google::protobuf::int64 value) {
  
  sellqtyupperlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyUpperLimit)
}

// optional int64 BuyQtyLowerLimit = 163;
void MDBasicInfo::clear_buyqtylowerlimit() {
  buyqtylowerlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::buyqtylowerlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyLowerLimit)
  return buyqtylowerlimit_;
}
void MDBasicInfo::set_buyqtylowerlimit(::google::protobuf::int64 value) {
  
  buyqtylowerlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BuyQtyLowerLimit)
}

// optional int64 SellQtyLowerLimit = 164;
void MDBasicInfo::clear_sellqtylowerlimit() {
  sellqtylowerlimit_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDBasicInfo::sellqtylowerlimit() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyLowerLimit)
  return sellqtylowerlimit_;
}
void MDBasicInfo::set_sellqtylowerlimit(::google::protobuf::int64 value) {
  
  sellqtylowerlimit_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.SellQtyLowerLimit)
}

// optional int32 VCMFlag = 165;
void MDBasicInfo::clear_vcmflag() {
  vcmflag_ = 0;
}
::google::protobuf::int32 MDBasicInfo::vcmflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.VCMFlag)
  return vcmflag_;
}
void MDBasicInfo::set_vcmflag(::google::protobuf::int32 value) {
  
  vcmflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.VCMFlag)
}

// optional int32 CASFlag = 166;
void MDBasicInfo::clear_casflag() {
  casflag_ = 0;
}
::google::protobuf::int32 MDBasicInfo::casflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.CASFlag)
  return casflag_;
}
void MDBasicInfo::set_casflag(::google::protobuf::int32 value) {
  
  casflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.CASFlag)
}

// optional int32 POSFlag = 167;
void MDBasicInfo::clear_posflag() {
  posflag_ = 0;
}
::google::protobuf::int32 MDBasicInfo::posflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSFlag)
  return posflag_;
}
void MDBasicInfo::set_posflag(::google::protobuf::int32 value) {
  
  posflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSFlag)
}

// optional double POSUpperLimitPx = 168;
void MDBasicInfo::clear_posupperlimitpx() {
  posupperlimitpx_ = 0;
}
double MDBasicInfo::posupperlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSUpperLimitPx)
  return posupperlimitpx_;
}
void MDBasicInfo::set_posupperlimitpx(double value) {
  
  posupperlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSUpperLimitPx)
}

// optional double POSLowerLimitPx = 169;
void MDBasicInfo::clear_poslowerlimitpx() {
  poslowerlimitpx_ = 0;
}
double MDBasicInfo::poslowerlimitpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.POSLowerLimitPx)
  return poslowerlimitpx_;
}
void MDBasicInfo::set_poslowerlimitpx(double value) {
  
  poslowerlimitpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.POSLowerLimitPx)
}

// optional string BaseContractID = 170;
void MDBasicInfo::clear_basecontractid() {
  basecontractid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::basecontractid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  return basecontractid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_basecontractid(const ::std::string& value) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
void MDBasicInfo::set_basecontractid(const char* value) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
void MDBasicInfo::set_basecontractid(const char* value, size_t size) {
  
  basecontractid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}
::std::string* MDBasicInfo::mutable_basecontractid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  return basecontractid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_basecontractid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
  
  return basecontractid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_basecontractid(::std::string* basecontractid) {
  if (basecontractid != NULL) {
    
  } else {
    
  }
  basecontractid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), basecontractid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.BaseContractID)
}

// repeated .com.htsc.mdc.insight.model.MDBasicInfo.ConstantParam constantParams = 171;
int MDBasicInfo::constantparams_size() const {
  return constantparams_.size();
}
void MDBasicInfo::clear_constantparams() {
  constantparams_.Clear();
}
const ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam& MDBasicInfo::constantparams(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Get(index);
}
::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* MDBasicInfo::mutable_constantparams(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam* MDBasicInfo::add_constantparams() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >*
MDBasicInfo::mutable_constantparams() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return &constantparams_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDBasicInfo_ConstantParam >&
MDBasicInfo::constantparams() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBasicInfo.constantParams)
  return constantparams_;
}

// optional int32 DataMultiplePowerOf10 = 172;
void MDBasicInfo::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDBasicInfo::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDBasicInfo::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.DataMultiplePowerOf10)
}

// optional string InterestAccrualDate = 173;
void MDBasicInfo::clear_interestaccrualdate() {
  interestaccrualdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDBasicInfo::interestaccrualdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  return interestaccrualdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_interestaccrualdate(const ::std::string& value) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
void MDBasicInfo::set_interestaccrualdate(const char* value) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
void MDBasicInfo::set_interestaccrualdate(const char* value, size_t size) {
  
  interestaccrualdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}
::std::string* MDBasicInfo::mutable_interestaccrualdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  return interestaccrualdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDBasicInfo::release_interestaccrualdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
  
  return interestaccrualdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDBasicInfo::set_allocated_interestaccrualdate(::std::string* interestaccrualdate) {
  if (interestaccrualdate != NULL) {
    
  } else {
    
  }
  interestaccrualdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), interestaccrualdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBasicInfo.InterestAccrualDate)
}

inline const MDBasicInfo* MDBasicInfo::internal_default_instance() {
  return &MDBasicInfo_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
