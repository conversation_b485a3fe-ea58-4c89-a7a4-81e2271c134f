// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDOption.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDOption.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDOption_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDOption_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDOption_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDOption_2eproto() {
  protobuf_AddDesc_MDOption_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDOption.proto");
  GOOGLE_CHECK(file != NULL);
  MDOption_descriptor_ = file->message_type(0);
  static const int MDOption_offsets_[57] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, diffpx1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, diffpx2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalbuyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalsellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, weightedavgbuypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, weightedavgsellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawbuyamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawbuymoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawsellamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, withdrawsellmoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, totalsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, buytrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, selltrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, numbuyorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, numsellorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, tradingdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, presettleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, openinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, settleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, predelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, currdelta_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, referenceprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, delaytype_),
  };
  MDOption_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDOption_descriptor_,
      MDOption::internal_default_instance(),
      MDOption_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDOption),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDOption, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDOption_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDOption_descriptor_, MDOption::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDOption_2eproto() {
  MDOption_default_instance_.Shutdown();
  delete MDOption_reflection_;
}

void protobuf_InitDefaults_MDOption_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDOption_default_instance_.DefaultConstruct();
  MDOption_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDOption_2eproto_once_);
void protobuf_InitDefaults_MDOption_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDOption_2eproto_once_,
                 &protobuf_InitDefaults_MDOption_2eproto_impl);
}
void protobuf_AddDesc_MDOption_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDOption_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\016MDOption.proto\022\032com.htsc.mdc.insight.m"
    "odel\032\027ESecurityIDSource.proto\032\023ESecurity"
    "Type.proto\"\374\n\n\010MDOption\022\026\n\016HTSCSecurityI"
    "D\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022"
    "\025\n\rDataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCo"
    "de\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com"
    ".htsc.mdc.model.ESecurityIDSource\0227\n\014sec"
    "urityType\030\007 \001(\0162!.com.htsc.mdc.model.ESe"
    "curityType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003"
    "\022\022\n\nPreClosePx\030\n \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022"
    "\030\n\020TotalVolumeTrade\030\014 \001(\003\022\027\n\017TotalValueT"
    "rade\030\r \001(\003\022\016\n\006LastPx\030\016 \001(\003\022\016\n\006OpenPx\030\017 \001"
    "(\003\022\017\n\007ClosePx\030\020 \001(\003\022\016\n\006HighPx\030\021 \001(\003\022\r\n\005L"
    "owPx\030\022 \001(\003\022\017\n\007DiffPx1\030\023 \001(\003\022\017\n\007DiffPx2\030\024"
    " \001(\003\022\023\n\013TotalBuyQty\030\025 \001(\003\022\024\n\014TotalSellQt"
    "y\030\026 \001(\003\022\030\n\020WeightedAvgBuyPx\030\027 \001(\003\022\031\n\021Wei"
    "ghtedAvgSellPx\030\030 \001(\003\022\031\n\021WithdrawBuyNumbe"
    "r\030\031 \001(\003\022\031\n\021WithdrawBuyAmount\030\032 \001(\003\022\030\n\020Wi"
    "thdrawBuyMoney\030\033 \001(\003\022\032\n\022WithdrawSellNumb"
    "er\030\034 \001(\003\022\032\n\022WithdrawSellAmount\030\035 \001(\003\022\031\n\021"
    "WithdrawSellMoney\030\036 \001(\003\022\026\n\016TotalBuyNumbe"
    "r\030\037 \001(\003\022\027\n\017TotalSellNumber\030  \001(\003\022\033\n\023BuyT"
    "radeMaxDuration\030! \001(\003\022\034\n\024SellTradeMaxDur"
    "ation\030\" \001(\003\022\024\n\014NumBuyOrders\030# \001(\005\022\025\n\rNum"
    "SellOrders\030$ \001(\005\022\023\n\013TradingDate\030% \001(\005\022\027\n"
    "\017PreOpenInterest\030& \001(\003\022\026\n\016PreSettlePrice"
    "\030\' \001(\003\022\024\n\014OpenInterest\030( \001(\003\022\023\n\013SettlePr"
    "ice\030) \001(\003\022\020\n\010PreDelta\030* \001(\003\022\021\n\tCurrDelta"
    "\030+ \001(\003\022\024\n\014ExchangeDate\030, \001(\005\022\024\n\014Exchange"
    "Time\030- \001(\005\022\026\n\016ReferencePrice\030. \001(\003\022\021\n\tCh"
    "annelNo\0302 \001(\005\022\031\n\rBuyPriceQueue\0303 \003(\003B\002\020\001"
    "\022\034\n\020BuyOrderQtyQueue\0304 \003(\003B\002\020\001\022\032\n\016SellPr"
    "iceQueue\0305 \003(\003B\002\020\001\022\035\n\021SellOrderQtyQueue\030"
    "6 \003(\003B\002\020\001\022\031\n\rBuyOrderQueue\0307 \003(\003B\002\020\001\022\032\n\016"
    "SellOrderQueue\0308 \003(\003B\002\020\001\022\035\n\021BuyNumOrders"
    "Queue\0309 \003(\003B\002\020\001\022\036\n\022SellNumOrdersQueue\030: "
    "\003(\003B\002\020\001\022\035\n\025DataMultiplePowerOf10\030; \001(\005\022\021"
    "\n\tDelayType\030e \001(\005B1\n\032com.htsc.mdc.insigh"
    "t.modelB\016MDOptionProtosH\001\240\001\001b\006proto3", 1556);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDOption.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDOption_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDOption_2eproto_once_);
void protobuf_AddDesc_MDOption_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDOption_2eproto_once_,
                 &protobuf_AddDesc_MDOption_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDOption_2eproto {
  StaticDescriptorInitializer_MDOption_2eproto() {
    protobuf_AddDesc_MDOption_2eproto();
  }
} static_descriptor_initializer_MDOption_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDOption::kHTSCSecurityIDFieldNumber;
const int MDOption::kMDDateFieldNumber;
const int MDOption::kMDTimeFieldNumber;
const int MDOption::kDataTimestampFieldNumber;
const int MDOption::kTradingPhaseCodeFieldNumber;
const int MDOption::kSecurityIDSourceFieldNumber;
const int MDOption::kSecurityTypeFieldNumber;
const int MDOption::kMaxPxFieldNumber;
const int MDOption::kMinPxFieldNumber;
const int MDOption::kPreClosePxFieldNumber;
const int MDOption::kNumTradesFieldNumber;
const int MDOption::kTotalVolumeTradeFieldNumber;
const int MDOption::kTotalValueTradeFieldNumber;
const int MDOption::kLastPxFieldNumber;
const int MDOption::kOpenPxFieldNumber;
const int MDOption::kClosePxFieldNumber;
const int MDOption::kHighPxFieldNumber;
const int MDOption::kLowPxFieldNumber;
const int MDOption::kDiffPx1FieldNumber;
const int MDOption::kDiffPx2FieldNumber;
const int MDOption::kTotalBuyQtyFieldNumber;
const int MDOption::kTotalSellQtyFieldNumber;
const int MDOption::kWeightedAvgBuyPxFieldNumber;
const int MDOption::kWeightedAvgSellPxFieldNumber;
const int MDOption::kWithdrawBuyNumberFieldNumber;
const int MDOption::kWithdrawBuyAmountFieldNumber;
const int MDOption::kWithdrawBuyMoneyFieldNumber;
const int MDOption::kWithdrawSellNumberFieldNumber;
const int MDOption::kWithdrawSellAmountFieldNumber;
const int MDOption::kWithdrawSellMoneyFieldNumber;
const int MDOption::kTotalBuyNumberFieldNumber;
const int MDOption::kTotalSellNumberFieldNumber;
const int MDOption::kBuyTradeMaxDurationFieldNumber;
const int MDOption::kSellTradeMaxDurationFieldNumber;
const int MDOption::kNumBuyOrdersFieldNumber;
const int MDOption::kNumSellOrdersFieldNumber;
const int MDOption::kTradingDateFieldNumber;
const int MDOption::kPreOpenInterestFieldNumber;
const int MDOption::kPreSettlePriceFieldNumber;
const int MDOption::kOpenInterestFieldNumber;
const int MDOption::kSettlePriceFieldNumber;
const int MDOption::kPreDeltaFieldNumber;
const int MDOption::kCurrDeltaFieldNumber;
const int MDOption::kExchangeDateFieldNumber;
const int MDOption::kExchangeTimeFieldNumber;
const int MDOption::kReferencePriceFieldNumber;
const int MDOption::kChannelNoFieldNumber;
const int MDOption::kBuyPriceQueueFieldNumber;
const int MDOption::kBuyOrderQtyQueueFieldNumber;
const int MDOption::kSellPriceQueueFieldNumber;
const int MDOption::kSellOrderQtyQueueFieldNumber;
const int MDOption::kBuyOrderQueueFieldNumber;
const int MDOption::kSellOrderQueueFieldNumber;
const int MDOption::kBuyNumOrdersQueueFieldNumber;
const int MDOption::kSellNumOrdersQueueFieldNumber;
const int MDOption::kDataMultiplePowerOf10FieldNumber;
const int MDOption::kDelayTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDOption::MDOption()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDOption_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDOption)
}

void MDOption::InitAsDefaultInstance() {
}

MDOption::MDOption(const MDOption& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDOption)
}

void MDOption::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&delaytype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(delaytype_));
  _cached_size_ = 0;
}

MDOption::~MDOption() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDOption)
  SharedDtor();
}

void MDOption::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDOption::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDOption::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDOption_descriptor_;
}

const MDOption& MDOption::default_instance() {
  protobuf_InitDefaults_MDOption_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDOption> MDOption_default_instance_;

MDOption* MDOption::New(::google::protobuf::Arena* arena) const {
  MDOption* n = new MDOption;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDOption::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDOption)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDOption, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDOption*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, weightedavgsellpx_);
  ZR_(withdrawbuynumber_, totalsellnumber_);
  ZR_(buytrademaxduration_, tradingdate_);
  ZR_(exchangedate_, channelno_);
  datamultiplepowerof10_ = 0;
  delaytype_ = 0;

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
}

bool MDOption::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDOption)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOption.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDOption.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_DiffPx1;
        break;
      }

      // optional int64 DiffPx1 = 19;
      case 19: {
        if (tag == 152) {
         parse_DiffPx1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DiffPx2;
        break;
      }

      // optional int64 DiffPx2 = 20;
      case 20: {
        if (tag == 160) {
         parse_DiffPx2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TotalBuyQty;
        break;
      }

      // optional int64 TotalBuyQty = 21;
      case 21: {
        if (tag == 168) {
         parse_TotalBuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TotalSellQty;
        break;
      }

      // optional int64 TotalSellQty = 22;
      case 22: {
        if (tag == 176) {
         parse_TotalSellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_WeightedAvgBuyPx;
        break;
      }

      // optional int64 WeightedAvgBuyPx = 23;
      case 23: {
        if (tag == 184) {
         parse_WeightedAvgBuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgbuypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_WeightedAvgSellPx;
        break;
      }

      // optional int64 WeightedAvgSellPx = 24;
      case 24: {
        if (tag == 192) {
         parse_WeightedAvgSellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgsellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_WithdrawBuyNumber;
        break;
      }

      // optional int64 WithdrawBuyNumber = 25;
      case 25: {
        if (tag == 200) {
         parse_WithdrawBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_WithdrawBuyAmount;
        break;
      }

      // optional int64 WithdrawBuyAmount = 26;
      case 26: {
        if (tag == 208) {
         parse_WithdrawBuyAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuyamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_WithdrawBuyMoney;
        break;
      }

      // optional int64 WithdrawBuyMoney = 27;
      case 27: {
        if (tag == 216) {
         parse_WithdrawBuyMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuymoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_WithdrawSellNumber;
        break;
      }

      // optional int64 WithdrawSellNumber = 28;
      case 28: {
        if (tag == 224) {
         parse_WithdrawSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_WithdrawSellAmount;
        break;
      }

      // optional int64 WithdrawSellAmount = 29;
      case 29: {
        if (tag == 232) {
         parse_WithdrawSellAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_WithdrawSellMoney;
        break;
      }

      // optional int64 WithdrawSellMoney = 30;
      case 30: {
        if (tag == 240) {
         parse_WithdrawSellMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellmoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_TotalBuyNumber;
        break;
      }

      // optional int64 TotalBuyNumber = 31;
      case 31: {
        if (tag == 248) {
         parse_TotalBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_TotalSellNumber;
        break;
      }

      // optional int64 TotalSellNumber = 32;
      case 32: {
        if (tag == 256) {
         parse_TotalSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_BuyTradeMaxDuration;
        break;
      }

      // optional int64 BuyTradeMaxDuration = 33;
      case 33: {
        if (tag == 264) {
         parse_BuyTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buytrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_SellTradeMaxDuration;
        break;
      }

      // optional int64 SellTradeMaxDuration = 34;
      case 34: {
        if (tag == 272) {
         parse_SellTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &selltrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_NumBuyOrders;
        break;
      }

      // optional int32 NumBuyOrders = 35;
      case 35: {
        if (tag == 280) {
         parse_NumBuyOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numbuyorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_NumSellOrders;
        break;
      }

      // optional int32 NumSellOrders = 36;
      case 36: {
        if (tag == 288) {
         parse_NumSellOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numsellorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_TradingDate;
        break;
      }

      // optional int32 TradingDate = 37;
      case 37: {
        if (tag == 296) {
         parse_TradingDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradingdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_PreOpenInterest;
        break;
      }

      // optional int64 PreOpenInterest = 38;
      case 38: {
        if (tag == 304) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_PreSettlePrice;
        break;
      }

      // optional int64 PreSettlePrice = 39;
      case 39: {
        if (tag == 312) {
         parse_PreSettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_OpenInterest;
        break;
      }

      // optional int64 OpenInterest = 40;
      case 40: {
        if (tag == 320) {
         parse_OpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_SettlePrice;
        break;
      }

      // optional int64 SettlePrice = 41;
      case 41: {
        if (tag == 328) {
         parse_SettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(336)) goto parse_PreDelta;
        break;
      }

      // optional int64 PreDelta = 42;
      case 42: {
        if (tag == 336) {
         parse_PreDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &predelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_CurrDelta;
        break;
      }

      // optional int64 CurrDelta = 43;
      case 43: {
        if (tag == 344) {
         parse_CurrDelta:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &currdelta_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 44;
      case 44: {
        if (tag == 352) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 45;
      case 45: {
        if (tag == 360) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_ReferencePrice;
        break;
      }

      // optional int64 ReferencePrice = 46;
      case 46: {
        if (tag == 368) {
         parse_ReferencePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &referenceprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 50;
      case 50: {
        if (tag == 400) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 59;
      case 59: {
        if (tag == 472) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_DelayType;
        break;
      }

      // optional int32 DelayType = 101;
      case 101: {
        if (tag == 808) {
         parse_DelayType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDOption)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDOption)
  return false;
#undef DO_
}

void MDOption::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDOption)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOption.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOption.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->diffpx1(), output);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->diffpx2(), output);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->totalbuyqty(), output);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->totalsellqty(), output);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->weightedavgbuypx(), output);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->weightedavgsellpx(), output);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->withdrawbuynumber(), output);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->withdrawbuyamount(), output);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->withdrawbuymoney(), output);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->withdrawsellnumber(), output);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->withdrawsellamount(), output);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->withdrawsellmoney(), output);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->totalbuynumber(), output);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->totalsellnumber(), output);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->buytrademaxduration(), output);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->selltrademaxduration(), output);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(35, this->numbuyorders(), output);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(36, this->numsellorders(), output);
  }

  // optional int32 TradingDate = 37;
  if (this->tradingdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(37, this->tradingdate(), output);
  }

  // optional int64 PreOpenInterest = 38;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(38, this->preopeninterest(), output);
  }

  // optional int64 PreSettlePrice = 39;
  if (this->presettleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(39, this->presettleprice(), output);
  }

  // optional int64 OpenInterest = 40;
  if (this->openinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(40, this->openinterest(), output);
  }

  // optional int64 SettlePrice = 41;
  if (this->settleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(41, this->settleprice(), output);
  }

  // optional int64 PreDelta = 42;
  if (this->predelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(42, this->predelta(), output);
  }

  // optional int64 CurrDelta = 43;
  if (this->currdelta() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(43, this->currdelta(), output);
  }

  // optional int32 ExchangeDate = 44;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(44, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 45;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(45, this->exchangetime(), output);
  }

  // optional int64 ReferencePrice = 46;
  if (this->referenceprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(46, this->referenceprice(), output);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(50, this->channelno(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(59, this->datamultiplepowerof10(), output);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(101, this->delaytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDOption)
}

::google::protobuf::uint8* MDOption::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDOption)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOption.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDOption.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->diffpx1(), target);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->diffpx2(), target);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->totalbuyqty(), target);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->totalsellqty(), target);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->weightedavgbuypx(), target);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->weightedavgsellpx(), target);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->withdrawbuynumber(), target);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->withdrawbuyamount(), target);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->withdrawbuymoney(), target);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->withdrawsellnumber(), target);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->withdrawsellamount(), target);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->withdrawsellmoney(), target);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->totalbuynumber(), target);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->totalsellnumber(), target);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->buytrademaxduration(), target);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->selltrademaxduration(), target);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(35, this->numbuyorders(), target);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(36, this->numsellorders(), target);
  }

  // optional int32 TradingDate = 37;
  if (this->tradingdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(37, this->tradingdate(), target);
  }

  // optional int64 PreOpenInterest = 38;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(38, this->preopeninterest(), target);
  }

  // optional int64 PreSettlePrice = 39;
  if (this->presettleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(39, this->presettleprice(), target);
  }

  // optional int64 OpenInterest = 40;
  if (this->openinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(40, this->openinterest(), target);
  }

  // optional int64 SettlePrice = 41;
  if (this->settleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(41, this->settleprice(), target);
  }

  // optional int64 PreDelta = 42;
  if (this->predelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(42, this->predelta(), target);
  }

  // optional int64 CurrDelta = 43;
  if (this->currdelta() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(43, this->currdelta(), target);
  }

  // optional int32 ExchangeDate = 44;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(44, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 45;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(45, this->exchangetime(), target);
  }

  // optional int64 ReferencePrice = 46;
  if (this->referenceprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(46, this->referenceprice(), target);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(50, this->channelno(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(59, this->datamultiplepowerof10(), target);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(101, this->delaytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDOption)
  return target;
}

size_t MDOption::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDOption)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx1());
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx2());
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuyqty());
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellqty());
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgbuypx());
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgsellpx());
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuynumber());
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuyamount());
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuymoney());
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellnumber());
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellamount());
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellmoney());
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuynumber());
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellnumber());
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buytrademaxduration());
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->selltrademaxduration());
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numbuyorders());
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numsellorders());
  }

  // optional int32 TradingDate = 37;
  if (this->tradingdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradingdate());
  }

  // optional int64 PreOpenInterest = 38;
  if (this->preopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preopeninterest());
  }

  // optional int64 PreSettlePrice = 39;
  if (this->presettleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->presettleprice());
  }

  // optional int64 OpenInterest = 40;
  if (this->openinterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openinterest());
  }

  // optional int64 SettlePrice = 41;
  if (this->settleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settleprice());
  }

  // optional int64 PreDelta = 42;
  if (this->predelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->predelta());
  }

  // optional int64 CurrDelta = 43;
  if (this->currdelta() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->currdelta());
  }

  // optional int32 ExchangeDate = 44;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 45;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 ReferencePrice = 46;
  if (this->referenceprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->referenceprice());
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaytype());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDOption::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDOption)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDOption* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDOption>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDOption)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDOption)
    UnsafeMergeFrom(*source);
  }
}

void MDOption::MergeFrom(const MDOption& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDOption)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDOption::UnsafeMergeFrom(const MDOption& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.diffpx1() != 0) {
    set_diffpx1(from.diffpx1());
  }
  if (from.diffpx2() != 0) {
    set_diffpx2(from.diffpx2());
  }
  if (from.totalbuyqty() != 0) {
    set_totalbuyqty(from.totalbuyqty());
  }
  if (from.totalsellqty() != 0) {
    set_totalsellqty(from.totalsellqty());
  }
  if (from.weightedavgbuypx() != 0) {
    set_weightedavgbuypx(from.weightedavgbuypx());
  }
  if (from.weightedavgsellpx() != 0) {
    set_weightedavgsellpx(from.weightedavgsellpx());
  }
  if (from.withdrawbuynumber() != 0) {
    set_withdrawbuynumber(from.withdrawbuynumber());
  }
  if (from.withdrawbuyamount() != 0) {
    set_withdrawbuyamount(from.withdrawbuyamount());
  }
  if (from.withdrawbuymoney() != 0) {
    set_withdrawbuymoney(from.withdrawbuymoney());
  }
  if (from.withdrawsellnumber() != 0) {
    set_withdrawsellnumber(from.withdrawsellnumber());
  }
  if (from.withdrawsellamount() != 0) {
    set_withdrawsellamount(from.withdrawsellamount());
  }
  if (from.withdrawsellmoney() != 0) {
    set_withdrawsellmoney(from.withdrawsellmoney());
  }
  if (from.totalbuynumber() != 0) {
    set_totalbuynumber(from.totalbuynumber());
  }
  if (from.totalsellnumber() != 0) {
    set_totalsellnumber(from.totalsellnumber());
  }
  if (from.buytrademaxduration() != 0) {
    set_buytrademaxduration(from.buytrademaxduration());
  }
  if (from.selltrademaxduration() != 0) {
    set_selltrademaxduration(from.selltrademaxduration());
  }
  if (from.numbuyorders() != 0) {
    set_numbuyorders(from.numbuyorders());
  }
  if (from.numsellorders() != 0) {
    set_numsellorders(from.numsellorders());
  }
  if (from.tradingdate() != 0) {
    set_tradingdate(from.tradingdate());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.presettleprice() != 0) {
    set_presettleprice(from.presettleprice());
  }
  if (from.openinterest() != 0) {
    set_openinterest(from.openinterest());
  }
  if (from.settleprice() != 0) {
    set_settleprice(from.settleprice());
  }
  if (from.predelta() != 0) {
    set_predelta(from.predelta());
  }
  if (from.currdelta() != 0) {
    set_currdelta(from.currdelta());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.referenceprice() != 0) {
    set_referenceprice(from.referenceprice());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.delaytype() != 0) {
    set_delaytype(from.delaytype());
  }
}

void MDOption::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDOption)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDOption::CopyFrom(const MDOption& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDOption)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDOption::IsInitialized() const {

  return true;
}

void MDOption::Swap(MDOption* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDOption::InternalSwap(MDOption* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(diffpx1_, other->diffpx1_);
  std::swap(diffpx2_, other->diffpx2_);
  std::swap(totalbuyqty_, other->totalbuyqty_);
  std::swap(totalsellqty_, other->totalsellqty_);
  std::swap(weightedavgbuypx_, other->weightedavgbuypx_);
  std::swap(weightedavgsellpx_, other->weightedavgsellpx_);
  std::swap(withdrawbuynumber_, other->withdrawbuynumber_);
  std::swap(withdrawbuyamount_, other->withdrawbuyamount_);
  std::swap(withdrawbuymoney_, other->withdrawbuymoney_);
  std::swap(withdrawsellnumber_, other->withdrawsellnumber_);
  std::swap(withdrawsellamount_, other->withdrawsellamount_);
  std::swap(withdrawsellmoney_, other->withdrawsellmoney_);
  std::swap(totalbuynumber_, other->totalbuynumber_);
  std::swap(totalsellnumber_, other->totalsellnumber_);
  std::swap(buytrademaxduration_, other->buytrademaxduration_);
  std::swap(selltrademaxduration_, other->selltrademaxduration_);
  std::swap(numbuyorders_, other->numbuyorders_);
  std::swap(numsellorders_, other->numsellorders_);
  std::swap(tradingdate_, other->tradingdate_);
  std::swap(preopeninterest_, other->preopeninterest_);
  std::swap(presettleprice_, other->presettleprice_);
  std::swap(openinterest_, other->openinterest_);
  std::swap(settleprice_, other->settleprice_);
  std::swap(predelta_, other->predelta_);
  std::swap(currdelta_, other->currdelta_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(referenceprice_, other->referenceprice_);
  std::swap(channelno_, other->channelno_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(delaytype_, other->delaytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDOption::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDOption_descriptor_;
  metadata.reflection = MDOption_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDOption

// optional string HTSCSecurityID = 1;
void MDOption::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOption::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOption::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
void MDOption::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
void MDOption::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}
::std::string* MDOption::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOption::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOption::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOption.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDOption::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDOption::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MDDate)
  return mddate_;
}
void MDOption::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MDDate)
}

// optional int32 MDTime = 3;
void MDOption::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDOption::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MDTime)
  return mdtime_;
}
void MDOption::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDOption::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DataTimestamp)
  return datatimestamp_;
}
void MDOption::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDOption::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDOption::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOption::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
void MDOption::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
void MDOption::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}
::std::string* MDOption::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDOption::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDOption::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOption.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDOption::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDOption::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDOption::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDOption::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDOption::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDOption::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.securityType)
}

// optional int64 MaxPx = 8;
void MDOption::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MaxPx)
  return maxpx_;
}
void MDOption::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MaxPx)
}

// optional int64 MinPx = 9;
void MDOption::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.MinPx)
  return minpx_;
}
void MDOption::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.MinPx)
}

// optional int64 PreClosePx = 10;
void MDOption::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreClosePx)
  return preclosepx_;
}
void MDOption::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDOption::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumTrades)
  return numtrades_;
}
void MDOption::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDOption::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDOption::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDOption::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalValueTrade)
  return totalvaluetrade_;
}
void MDOption::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDOption::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.LastPx)
  return lastpx_;
}
void MDOption::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.LastPx)
}

// optional int64 OpenPx = 15;
void MDOption::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.OpenPx)
  return openpx_;
}
void MDOption::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.OpenPx)
}

// optional int64 ClosePx = 16;
void MDOption::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ClosePx)
  return closepx_;
}
void MDOption::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ClosePx)
}

// optional int64 HighPx = 17;
void MDOption::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.HighPx)
  return highpx_;
}
void MDOption::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.HighPx)
}

// optional int64 LowPx = 18;
void MDOption::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.LowPx)
  return lowpx_;
}
void MDOption::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.LowPx)
}

// optional int64 DiffPx1 = 19;
void MDOption::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DiffPx1)
  return diffpx1_;
}
void MDOption::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DiffPx1)
}

// optional int64 DiffPx2 = 20;
void MDOption::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DiffPx2)
  return diffpx2_;
}
void MDOption::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
void MDOption::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalBuyQty)
  return totalbuyqty_;
}
void MDOption::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
void MDOption::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalSellQty)
  return totalsellqty_;
}
void MDOption::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
void MDOption::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
void MDOption::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
void MDOption::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
void MDOption::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
void MDOption::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
void MDOption::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
void MDOption::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
void MDOption::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
void MDOption::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
void MDOption::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
void MDOption::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellNumber)
  return withdrawsellnumber_;
}
void MDOption::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
void MDOption::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellAmount)
  return withdrawsellamount_;
}
void MDOption::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
void MDOption::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.WithdrawSellMoney)
  return withdrawsellmoney_;
}
void MDOption::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
void MDOption::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalBuyNumber)
  return totalbuynumber_;
}
void MDOption::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
void MDOption::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TotalSellNumber)
  return totalsellnumber_;
}
void MDOption::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
void MDOption::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
void MDOption::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
void MDOption::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellTradeMaxDuration)
  return selltrademaxduration_;
}
void MDOption::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
void MDOption::clear_numbuyorders() {
  numbuyorders_ = 0;
}
::google::protobuf::int32 MDOption::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumBuyOrders)
  return numbuyorders_;
}
void MDOption::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
void MDOption::clear_numsellorders() {
  numsellorders_ = 0;
}
::google::protobuf::int32 MDOption::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.NumSellOrders)
  return numsellorders_;
}
void MDOption::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.NumSellOrders)
}

// optional int32 TradingDate = 37;
void MDOption::clear_tradingdate() {
  tradingdate_ = 0;
}
::google::protobuf::int32 MDOption::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.TradingDate)
  return tradingdate_;
}
void MDOption::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.TradingDate)
}

// optional int64 PreOpenInterest = 38;
void MDOption::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreOpenInterest)
  return preopeninterest_;
}
void MDOption::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreOpenInterest)
}

// optional int64 PreSettlePrice = 39;
void MDOption::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreSettlePrice)
  return presettleprice_;
}
void MDOption::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreSettlePrice)
}

// optional int64 OpenInterest = 40;
void MDOption::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.OpenInterest)
  return openinterest_;
}
void MDOption::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.OpenInterest)
}

// optional int64 SettlePrice = 41;
void MDOption::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SettlePrice)
  return settleprice_;
}
void MDOption::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SettlePrice)
}

// optional int64 PreDelta = 42;
void MDOption::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.PreDelta)
  return predelta_;
}
void MDOption::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.PreDelta)
}

// optional int64 CurrDelta = 43;
void MDOption::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.CurrDelta)
  return currdelta_;
}
void MDOption::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.CurrDelta)
}

// optional int32 ExchangeDate = 44;
void MDOption::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDOption::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ExchangeDate)
  return exchangedate_;
}
void MDOption::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ExchangeDate)
}

// optional int32 ExchangeTime = 45;
void MDOption::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDOption::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ExchangeTime)
  return exchangetime_;
}
void MDOption::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ExchangeTime)
}

// optional int64 ReferencePrice = 46;
void MDOption::clear_referenceprice() {
  referenceprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDOption::referenceprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ReferencePrice)
  return referenceprice_;
}
void MDOption::set_referenceprice(::google::protobuf::int64 value) {
  
  referenceprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ReferencePrice)
}

// optional int32 ChannelNo = 50;
void MDOption::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDOption::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.ChannelNo)
  return channelno_;
}
void MDOption::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDOption::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDOption::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDOption::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDOption::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
}
void MDOption::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDOption::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDOption::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDOption::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDOption::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
}
void MDOption::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDOption::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDOption::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDOption::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDOption::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
}
void MDOption::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDOption::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDOption::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDOption::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDOption::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
}
void MDOption::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDOption::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDOption::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDOption::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDOption::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
}
void MDOption::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDOption::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDOption::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDOption::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDOption::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
}
void MDOption::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDOption::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDOption::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDOption::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDOption::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
}
void MDOption::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDOption::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDOption::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDOption::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDOption::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
}
void MDOption::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDOption::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDOption::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDOption.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
void MDOption::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDOption::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDOption::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DataMultiplePowerOf10)
}

// optional int32 DelayType = 101;
void MDOption::clear_delaytype() {
  delaytype_ = 0;
}
::google::protobuf::int32 MDOption::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOption.DelayType)
  return delaytype_;
}
void MDOption::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOption.DelayType)
}

inline const MDOption* MDOption::internal_default_instance() {
  return &MDOption_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
