// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsBondSnapshot.proto

#ifndef PROTOBUF_MDCfetsBondSnapshot_2eproto__INCLUDED
#define PROTOBUF_MDCfetsBondSnapshot_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDCfetsBondSnapshot_2eproto();
void protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

class BondForwardSnapshot;
class BondLendingSnapshot;
class CashBondTradingSnapshot;
class MDCfetsBondSnapshot;
class StandardisedBondForwardSnapshot;

// ===================================================================

class MDCfetsBondSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDCfetsBondSnapshot) */ {
 public:
  MDCfetsBondSnapshot();
  virtual ~MDCfetsBondSnapshot();

  MDCfetsBondSnapshot(const MDCfetsBondSnapshot& from);

  inline MDCfetsBondSnapshot& operator=(const MDCfetsBondSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDCfetsBondSnapshot& default_instance();

  static const MDCfetsBondSnapshot* internal_default_instance();

  void Swap(MDCfetsBondSnapshot* other);

  // implements Message ----------------------------------------------

  inline MDCfetsBondSnapshot* New() const { return New(NULL); }

  MDCfetsBondSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDCfetsBondSnapshot& from);
  void MergeFrom(const MDCfetsBondSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDCfetsBondSnapshot* other);
  void UnsafeMergeFrom(const MDCfetsBondSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 6;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 6;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TransactTime = 7;
  void clear_transacttime();
  static const int kTransactTimeFieldNumber = 7;
  const ::std::string& transacttime() const;
  void set_transacttime(const ::std::string& value);
  void set_transacttime(const char* value);
  void set_transacttime(const char* value, size_t size);
  ::std::string* mutable_transacttime();
  ::std::string* release_transacttime();
  void set_allocated_transacttime(::std::string* transacttime);

  // optional string MarketIndicator = 8;
  void clear_marketindicator();
  static const int kMarketIndicatorFieldNumber = 8;
  const ::std::string& marketindicator() const;
  void set_marketindicator(const ::std::string& value);
  void set_marketindicator(const char* value);
  void set_marketindicator(const char* value, size_t size);
  ::std::string* mutable_marketindicator();
  ::std::string* release_marketindicator();
  void set_allocated_marketindicator(::std::string* marketindicator);

  // optional int32 DataMultiplePowerOf10 = 9;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 9;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 BondSnapshotType = 16;
  void clear_bondsnapshottype();
  static const int kBondSnapshotTypeFieldNumber = 16;
  ::google::protobuf::int32 bondsnapshottype() const;
  void set_bondsnapshottype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
  bool has_cashbondtradingsnapshot() const;
  void clear_cashbondtradingsnapshot();
  static const int kCashBondTradingSnapshotFieldNumber = 17;
  const ::com::htsc::mdc::insight::model::CashBondTradingSnapshot& cashbondtradingsnapshot() const;
  ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* mutable_cashbondtradingsnapshot();
  ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* release_cashbondtradingsnapshot();
  void set_allocated_cashbondtradingsnapshot(::com::htsc::mdc::insight::model::CashBondTradingSnapshot* cashbondtradingsnapshot);

  // optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
  bool has_bondforwardsnapshot() const;
  void clear_bondforwardsnapshot();
  static const int kBondForwardSnapshotFieldNumber = 18;
  const ::com::htsc::mdc::insight::model::BondForwardSnapshot& bondforwardsnapshot() const;
  ::com::htsc::mdc::insight::model::BondForwardSnapshot* mutable_bondforwardsnapshot();
  ::com::htsc::mdc::insight::model::BondForwardSnapshot* release_bondforwardsnapshot();
  void set_allocated_bondforwardsnapshot(::com::htsc::mdc::insight::model::BondForwardSnapshot* bondforwardsnapshot);

  // optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
  bool has_bondlendingsnapshot() const;
  void clear_bondlendingsnapshot();
  static const int kBondLendingSnapshotFieldNumber = 19;
  const ::com::htsc::mdc::insight::model::BondLendingSnapshot& bondlendingsnapshot() const;
  ::com::htsc::mdc::insight::model::BondLendingSnapshot* mutable_bondlendingsnapshot();
  ::com::htsc::mdc::insight::model::BondLendingSnapshot* release_bondlendingsnapshot();
  void set_allocated_bondlendingsnapshot(::com::htsc::mdc::insight::model::BondLendingSnapshot* bondlendingsnapshot);

  // optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
  bool has_standardisedbondforwardsnapshot() const;
  void clear_standardisedbondforwardsnapshot();
  static const int kStandardisedBondForwardSnapshotFieldNumber = 20;
  const ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot& standardisedbondforwardsnapshot() const;
  ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* mutable_standardisedbondforwardsnapshot();
  ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* release_standardisedbondforwardsnapshot();
  void set_allocated_standardisedbondforwardsnapshot(::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* standardisedbondforwardsnapshot);

  // optional int64 MessageNumber = 100;
  void clear_messagenumber();
  static const int kMessageNumberFieldNumber = 100;
  ::google::protobuf::int64 messagenumber() const;
  void set_messagenumber(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDCfetsBondSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr transacttime_;
  ::google::protobuf::internal::ArenaStringPtr marketindicator_;
  ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* cashbondtradingsnapshot_;
  ::com::htsc::mdc::insight::model::BondForwardSnapshot* bondforwardsnapshot_;
  ::com::htsc::mdc::insight::model::BondLendingSnapshot* bondlendingsnapshot_;
  ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* standardisedbondforwardsnapshot_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 bondsnapshottype_;
  ::google::protobuf::int64 messagenumber_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDCfetsBondSnapshot> MDCfetsBondSnapshot_default_instance_;

// -------------------------------------------------------------------

class CashBondTradingSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.CashBondTradingSnapshot) */ {
 public:
  CashBondTradingSnapshot();
  virtual ~CashBondTradingSnapshot();

  CashBondTradingSnapshot(const CashBondTradingSnapshot& from);

  inline CashBondTradingSnapshot& operator=(const CashBondTradingSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const CashBondTradingSnapshot& default_instance();

  static const CashBondTradingSnapshot* internal_default_instance();

  void Swap(CashBondTradingSnapshot* other);

  // implements Message ----------------------------------------------

  inline CashBondTradingSnapshot* New() const { return New(NULL); }

  CashBondTradingSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const CashBondTradingSnapshot& from);
  void MergeFrom(const CashBondTradingSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(CashBondTradingSnapshot* other);
  void UnsafeMergeFrom(const CashBondTradingSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeMethod = 1;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 1;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional string SettlType = 2;
  void clear_settltype();
  static const int kSettlTypeFieldNumber = 2;
  const ::std::string& settltype() const;
  void set_settltype(const ::std::string& value);
  void set_settltype(const char* value);
  void set_settltype(const char* value, size_t size);
  ::std::string* mutable_settltype();
  ::std::string* release_settltype();
  void set_allocated_settltype(::std::string* settltype);

  // optional string Side = 3;
  void clear_side();
  static const int kSideFieldNumber = 3;
  const ::std::string& side() const;
  void set_side(const ::std::string& value);
  void set_side(const char* value);
  void set_side(const char* value, size_t size);
  ::std::string* mutable_side();
  ::std::string* release_side();
  void set_allocated_side(::std::string* side);

  // optional bool PreMarketBondIndicator = 4;
  void clear_premarketbondindicator();
  static const int kPreMarketBondIndicatorFieldNumber = 4;
  bool premarketbondindicator() const;
  void set_premarketbondindicator(bool value);

  // optional double PreCloseCleanPx = 11;
  void clear_preclosecleanpx();
  static const int kPreCloseCleanPxFieldNumber = 11;
  double preclosecleanpx() const;
  void set_preclosecleanpx(double value);

  // optional double PreWeightedAvgCleanPx = 12;
  void clear_preweightedavgcleanpx();
  static const int kPreWeightedAvgCleanPxFieldNumber = 12;
  double preweightedavgcleanpx() const;
  void set_preweightedavgcleanpx(double value);

  // optional double OpenCleanPx = 13;
  void clear_opencleanpx();
  static const int kOpenCleanPxFieldNumber = 13;
  double opencleanpx() const;
  void set_opencleanpx(double value);

  // optional double LastCleanPx = 14;
  void clear_lastcleanpx();
  static const int kLastCleanPxFieldNumber = 14;
  double lastcleanpx() const;
  void set_lastcleanpx(double value);

  // optional double ChangePercent = 15;
  void clear_changepercent();
  static const int kChangePercentFieldNumber = 15;
  double changepercent() const;
  void set_changepercent(double value);

  // optional double HighCleanPx = 16;
  void clear_highcleanpx();
  static const int kHighCleanPxFieldNumber = 16;
  double highcleanpx() const;
  void set_highcleanpx(double value);

  // optional double LowCleanPx = 17;
  void clear_lowcleanpx();
  static const int kLowCleanPxFieldNumber = 17;
  double lowcleanpx() const;
  void set_lowcleanpx(double value);

  // optional double CloseCleanPx = 18;
  void clear_closecleanpx();
  static const int kCloseCleanPxFieldNumber = 18;
  double closecleanpx() const;
  void set_closecleanpx(double value);

  // optional double WeightedAvgCleanPx = 19;
  void clear_weightedavgcleanpx();
  static const int kWeightedAvgCleanPxFieldNumber = 19;
  double weightedavgcleanpx() const;
  void set_weightedavgcleanpx(double value);

  // optional double PreCloseYield = 20;
  void clear_precloseyield();
  static const int kPreCloseYieldFieldNumber = 20;
  double precloseyield() const;
  void set_precloseyield(double value);

  // optional double PreWeightedAvgYield = 21;
  void clear_preweightedavgyield();
  static const int kPreWeightedAvgYieldFieldNumber = 21;
  double preweightedavgyield() const;
  void set_preweightedavgyield(double value);

  // optional double OpenYield = 22;
  void clear_openyield();
  static const int kOpenYieldFieldNumber = 22;
  double openyield() const;
  void set_openyield(double value);

  // optional double LastYield = 23;
  void clear_lastyield();
  static const int kLastYieldFieldNumber = 23;
  double lastyield() const;
  void set_lastyield(double value);

  // optional double HighYield = 24;
  void clear_highyield();
  static const int kHighYieldFieldNumber = 24;
  double highyield() const;
  void set_highyield(double value);

  // optional double LowYield = 25;
  void clear_lowyield();
  static const int kLowYieldFieldNumber = 25;
  double lowyield() const;
  void set_lowyield(double value);

  // optional double CloseYield = 26;
  void clear_closeyield();
  static const int kCloseYieldFieldNumber = 26;
  double closeyield() const;
  void set_closeyield(double value);

  // optional double WeightedAvgYield = 27;
  void clear_weightedavgyield();
  static const int kWeightedAvgYieldFieldNumber = 27;
  double weightedavgyield() const;
  void set_weightedavgyield(double value);

  // optional double TradeVolume = 28;
  void clear_tradevolume();
  static const int kTradeVolumeFieldNumber = 28;
  double tradevolume() const;
  void set_tradevolume(double value);

  // optional double CleanPxChange = 29;
  void clear_cleanpxchange();
  static const int kCleanPxChangeFieldNumber = 29;
  double cleanpxchange() const;
  void set_cleanpxchange(double value);

  // optional double CleanPxChangePercent = 30;
  void clear_cleanpxchangepercent();
  static const int kCleanPxChangePercentFieldNumber = 30;
  double cleanpxchangepercent() const;
  void set_cleanpxchangepercent(double value);

  // optional double YieldChangeBP = 31;
  void clear_yieldchangebp();
  static const int kYieldChangeBPFieldNumber = 31;
  double yieldchangebp() const;
  void set_yieldchangebp(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.CashBondTradingSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr settltype_;
  ::google::protobuf::internal::ArenaStringPtr side_;
  ::google::protobuf::int32 trademethod_;
  bool premarketbondindicator_;
  double preclosecleanpx_;
  double preweightedavgcleanpx_;
  double opencleanpx_;
  double lastcleanpx_;
  double changepercent_;
  double highcleanpx_;
  double lowcleanpx_;
  double closecleanpx_;
  double weightedavgcleanpx_;
  double precloseyield_;
  double preweightedavgyield_;
  double openyield_;
  double lastyield_;
  double highyield_;
  double lowyield_;
  double closeyield_;
  double weightedavgyield_;
  double tradevolume_;
  double cleanpxchange_;
  double cleanpxchangepercent_;
  double yieldchangebp_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<CashBondTradingSnapshot> CashBondTradingSnapshot_default_instance_;

// -------------------------------------------------------------------

class BondForwardSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BondForwardSnapshot) */ {
 public:
  BondForwardSnapshot();
  virtual ~BondForwardSnapshot();

  BondForwardSnapshot(const BondForwardSnapshot& from);

  inline BondForwardSnapshot& operator=(const BondForwardSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BondForwardSnapshot& default_instance();

  static const BondForwardSnapshot* internal_default_instance();

  void Swap(BondForwardSnapshot* other);

  // implements Message ----------------------------------------------

  inline BondForwardSnapshot* New() const { return New(NULL); }

  BondForwardSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BondForwardSnapshot& from);
  void MergeFrom(const BondForwardSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BondForwardSnapshot* other);
  void UnsafeMergeFrom(const BondForwardSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string UnderlyingSymbol = 1;
  void clear_underlyingsymbol();
  static const int kUnderlyingSymbolFieldNumber = 1;
  const ::std::string& underlyingsymbol() const;
  void set_underlyingsymbol(const ::std::string& value);
  void set_underlyingsymbol(const char* value);
  void set_underlyingsymbol(const char* value, size_t size);
  ::std::string* mutable_underlyingsymbol();
  ::std::string* release_underlyingsymbol();
  void set_allocated_underlyingsymbol(::std::string* underlyingsymbol);

  // optional string UnderlyingSecurityID = 2;
  void clear_underlyingsecurityid();
  static const int kUnderlyingSecurityIDFieldNumber = 2;
  const ::std::string& underlyingsecurityid() const;
  void set_underlyingsecurityid(const ::std::string& value);
  void set_underlyingsecurityid(const char* value);
  void set_underlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_underlyingsecurityid();
  ::std::string* release_underlyingsecurityid();
  void set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid);

  // optional double PreCloseCleanPx = 11;
  void clear_preclosecleanpx();
  static const int kPreCloseCleanPxFieldNumber = 11;
  double preclosecleanpx() const;
  void set_preclosecleanpx(double value);

  // optional double PreWeightedAvgCleanPx = 12;
  void clear_preweightedavgcleanpx();
  static const int kPreWeightedAvgCleanPxFieldNumber = 12;
  double preweightedavgcleanpx() const;
  void set_preweightedavgcleanpx(double value);

  // optional double CleanPxChangePercent = 13;
  void clear_cleanpxchangepercent();
  static const int kCleanPxChangePercentFieldNumber = 13;
  double cleanpxchangepercent() const;
  void set_cleanpxchangepercent(double value);

  // optional double OpenCleanPx = 14;
  void clear_opencleanpx();
  static const int kOpenCleanPxFieldNumber = 14;
  double opencleanpx() const;
  void set_opencleanpx(double value);

  // optional double LastCleanPx = 15;
  void clear_lastcleanpx();
  static const int kLastCleanPxFieldNumber = 15;
  double lastcleanpx() const;
  void set_lastcleanpx(double value);

  // optional double HighCleanPx = 16;
  void clear_highcleanpx();
  static const int kHighCleanPxFieldNumber = 16;
  double highcleanpx() const;
  void set_highcleanpx(double value);

  // optional double LowCleanPx = 17;
  void clear_lowcleanpx();
  static const int kLowCleanPxFieldNumber = 17;
  double lowcleanpx() const;
  void set_lowcleanpx(double value);

  // optional double CloseCleanPx = 18;
  void clear_closecleanpx();
  static const int kCloseCleanPxFieldNumber = 18;
  double closecleanpx() const;
  void set_closecleanpx(double value);

  // optional double WeightedAvgCleanPx = 19;
  void clear_weightedavgcleanpx();
  static const int kWeightedAvgCleanPxFieldNumber = 19;
  double weightedavgcleanpx() const;
  void set_weightedavgcleanpx(double value);

  // optional double PreCloseYield = 20;
  void clear_precloseyield();
  static const int kPreCloseYieldFieldNumber = 20;
  double precloseyield() const;
  void set_precloseyield(double value);

  // optional double PreWeightedAvgYield = 21;
  void clear_preweightedavgyield();
  static const int kPreWeightedAvgYieldFieldNumber = 21;
  double preweightedavgyield() const;
  void set_preweightedavgyield(double value);

  // optional double OpenYield = 22;
  void clear_openyield();
  static const int kOpenYieldFieldNumber = 22;
  double openyield() const;
  void set_openyield(double value);

  // optional double LastYield = 23;
  void clear_lastyield();
  static const int kLastYieldFieldNumber = 23;
  double lastyield() const;
  void set_lastyield(double value);

  // optional double HighYield = 24;
  void clear_highyield();
  static const int kHighYieldFieldNumber = 24;
  double highyield() const;
  void set_highyield(double value);

  // optional double LowYield = 25;
  void clear_lowyield();
  static const int kLowYieldFieldNumber = 25;
  double lowyield() const;
  void set_lowyield(double value);

  // optional double CloseYield = 26;
  void clear_closeyield();
  static const int kCloseYieldFieldNumber = 26;
  double closeyield() const;
  void set_closeyield(double value);

  // optional double WeightedAvgYield = 27;
  void clear_weightedavgyield();
  static const int kWeightedAvgYieldFieldNumber = 27;
  double weightedavgyield() const;
  void set_weightedavgyield(double value);

  // optional double TradeVolume = 28;
  void clear_tradevolume();
  static const int kTradeVolumeFieldNumber = 28;
  double tradevolume() const;
  void set_tradevolume(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BondForwardSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsymbol_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsecurityid_;
  double preclosecleanpx_;
  double preweightedavgcleanpx_;
  double cleanpxchangepercent_;
  double opencleanpx_;
  double lastcleanpx_;
  double highcleanpx_;
  double lowcleanpx_;
  double closecleanpx_;
  double weightedavgcleanpx_;
  double precloseyield_;
  double preweightedavgyield_;
  double openyield_;
  double lastyield_;
  double highyield_;
  double lowyield_;
  double closeyield_;
  double weightedavgyield_;
  double tradevolume_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BondForwardSnapshot> BondForwardSnapshot_default_instance_;

// -------------------------------------------------------------------

class BondLendingSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.BondLendingSnapshot) */ {
 public:
  BondLendingSnapshot();
  virtual ~BondLendingSnapshot();

  BondLendingSnapshot(const BondLendingSnapshot& from);

  inline BondLendingSnapshot& operator=(const BondLendingSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const BondLendingSnapshot& default_instance();

  static const BondLendingSnapshot* internal_default_instance();

  void Swap(BondLendingSnapshot* other);

  // implements Message ----------------------------------------------

  inline BondLendingSnapshot* New() const { return New(NULL); }

  BondLendingSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const BondLendingSnapshot& from);
  void MergeFrom(const BondLendingSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BondLendingSnapshot* other);
  void UnsafeMergeFrom(const BondLendingSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string UnderlyingSymbol = 1;
  void clear_underlyingsymbol();
  static const int kUnderlyingSymbolFieldNumber = 1;
  const ::std::string& underlyingsymbol() const;
  void set_underlyingsymbol(const ::std::string& value);
  void set_underlyingsymbol(const char* value);
  void set_underlyingsymbol(const char* value, size_t size);
  ::std::string* mutable_underlyingsymbol();
  ::std::string* release_underlyingsymbol();
  void set_allocated_underlyingsymbol(::std::string* underlyingsymbol);

  // optional string UnderlyingSecurityID = 2;
  void clear_underlyingsecurityid();
  static const int kUnderlyingSecurityIDFieldNumber = 2;
  const ::std::string& underlyingsecurityid() const;
  void set_underlyingsecurityid(const ::std::string& value);
  void set_underlyingsecurityid(const char* value);
  void set_underlyingsecurityid(const char* value, size_t size);
  ::std::string* mutable_underlyingsecurityid();
  ::std::string* release_underlyingsecurityid();
  void set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid);

  // optional double PreCloseRate = 11;
  void clear_precloserate();
  static const int kPreCloseRateFieldNumber = 11;
  double precloserate() const;
  void set_precloserate(double value);

  // optional double PreWeightedAvgRate = 12;
  void clear_preweightedavgrate();
  static const int kPreWeightedAvgRateFieldNumber = 12;
  double preweightedavgrate() const;
  void set_preweightedavgrate(double value);

  // optional double OpenRate = 13;
  void clear_openrate();
  static const int kOpenRateFieldNumber = 13;
  double openrate() const;
  void set_openrate(double value);

  // optional double LastRate = 14;
  void clear_lastrate();
  static const int kLastRateFieldNumber = 14;
  double lastrate() const;
  void set_lastrate(double value);

  // optional double HighRate = 15;
  void clear_highrate();
  static const int kHighRateFieldNumber = 15;
  double highrate() const;
  void set_highrate(double value);

  // optional double LowRate = 16;
  void clear_lowrate();
  static const int kLowRateFieldNumber = 16;
  double lowrate() const;
  void set_lowrate(double value);

  // optional double CloseRate = 17;
  void clear_closerate();
  static const int kCloseRateFieldNumber = 17;
  double closerate() const;
  void set_closerate(double value);

  // optional double WeightedAvgRate = 18;
  void clear_weightedavgrate();
  static const int kWeightedAvgRateFieldNumber = 18;
  double weightedavgrate() const;
  void set_weightedavgrate(double value);

  // optional double TradeVolume = 19;
  void clear_tradevolume();
  static const int kTradeVolumeFieldNumber = 19;
  double tradevolume() const;
  void set_tradevolume(double value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.BondLendingSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsymbol_;
  ::google::protobuf::internal::ArenaStringPtr underlyingsecurityid_;
  double precloserate_;
  double preweightedavgrate_;
  double openrate_;
  double lastrate_;
  double highrate_;
  double lowrate_;
  double closerate_;
  double weightedavgrate_;
  double tradevolume_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<BondLendingSnapshot> BondLendingSnapshot_default_instance_;

// -------------------------------------------------------------------

class StandardisedBondForwardSnapshot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot) */ {
 public:
  StandardisedBondForwardSnapshot();
  virtual ~StandardisedBondForwardSnapshot();

  StandardisedBondForwardSnapshot(const StandardisedBondForwardSnapshot& from);

  inline StandardisedBondForwardSnapshot& operator=(const StandardisedBondForwardSnapshot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const StandardisedBondForwardSnapshot& default_instance();

  static const StandardisedBondForwardSnapshot* internal_default_instance();

  void Swap(StandardisedBondForwardSnapshot* other);

  // implements Message ----------------------------------------------

  inline StandardisedBondForwardSnapshot* New() const { return New(NULL); }

  StandardisedBondForwardSnapshot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const StandardisedBondForwardSnapshot& from);
  void MergeFrom(const StandardisedBondForwardSnapshot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(StandardisedBondForwardSnapshot* other);
  void UnsafeMergeFrom(const StandardisedBondForwardSnapshot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional int32 TradeMethod = 1;
  void clear_trademethod();
  static const int kTradeMethodFieldNumber = 1;
  ::google::protobuf::int32 trademethod() const;
  void set_trademethod(::google::protobuf::int32 value);

  // optional double OpenPx = 11;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 11;
  double openpx() const;
  void set_openpx(double value);

  // optional double HighPx = 12;
  void clear_highpx();
  static const int kHighPxFieldNumber = 12;
  double highpx() const;
  void set_highpx(double value);

  // optional double LowPx = 13;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 13;
  double lowpx() const;
  void set_lowpx(double value);

  // optional double LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  double lastpx() const;
  void set_lastpx(double value);

  // optional double TotalVolumeTrade = 15;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 15;
  double totalvolumetrade() const;
  void set_totalvolumetrade(double value);

  // optional double LastVolumeTrade = 16;
  void clear_lastvolumetrade();
  static const int kLastVolumeTradeFieldNumber = 16;
  double lastvolumetrade() const;
  void set_lastvolumetrade(double value);

  // optional double SettlePx = 50;
  void clear_settlepx();
  static const int kSettlePxFieldNumber = 50;
  double settlepx() const;
  void set_settlepx(double value);

  // optional string SettlePxDate = 51;
  void clear_settlepxdate();
  static const int kSettlePxDateFieldNumber = 51;
  const ::std::string& settlepxdate() const;
  void set_settlepxdate(const ::std::string& value);
  void set_settlepxdate(const char* value);
  void set_settlepxdate(const char* value, size_t size);
  ::std::string* mutable_settlepxdate();
  ::std::string* release_settlepxdate();
  void set_allocated_settlepxdate(::std::string* settlepxdate);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr settlepxdate_;
  double openpx_;
  double highpx_;
  double lowpx_;
  double lastpx_;
  double totalvolumetrade_;
  double lastvolumetrade_;
  double settlepx_;
  ::google::protobuf::int32 trademethod_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto_impl();
  friend void  protobuf_AddDesc_MDCfetsBondSnapshot_2eproto_impl();
  friend void protobuf_AssignDesc_MDCfetsBondSnapshot_2eproto();
  friend void protobuf_ShutdownFile_MDCfetsBondSnapshot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<StandardisedBondForwardSnapshot> StandardisedBondForwardSnapshot_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsBondSnapshot

// optional string HTSCSecurityID = 1;
inline void MDCfetsBondSnapshot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondSnapshot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
inline void MDCfetsBondSnapshot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
inline void MDCfetsBondSnapshot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}
inline ::std::string* MDCfetsBondSnapshot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondSnapshot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDCfetsBondSnapshot::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDCfetsBondSnapshot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDCfetsBondSnapshot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDCfetsBondSnapshot::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDCfetsBondSnapshot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDCfetsBondSnapshot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDCfetsBondSnapshot::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondSnapshot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDDate)
  return mddate_;
}
inline void MDCfetsBondSnapshot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDDate)
}

// optional int32 MDTime = 5;
inline void MDCfetsBondSnapshot::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondSnapshot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDTime)
  return mdtime_;
}
inline void MDCfetsBondSnapshot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MDTime)
}

// optional int64 DataTimestamp = 6;
inline void MDCfetsBondSnapshot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBondSnapshot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataTimestamp)
  return datatimestamp_;
}
inline void MDCfetsBondSnapshot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataTimestamp)
}

// optional string TransactTime = 7;
inline void MDCfetsBondSnapshot::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondSnapshot::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
inline void MDCfetsBondSnapshot::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
inline void MDCfetsBondSnapshot::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}
inline ::std::string* MDCfetsBondSnapshot::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondSnapshot::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.TransactTime)
}

// optional string MarketIndicator = 8;
inline void MDCfetsBondSnapshot::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDCfetsBondSnapshot::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
inline void MDCfetsBondSnapshot::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
inline void MDCfetsBondSnapshot::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}
inline ::std::string* MDCfetsBondSnapshot::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDCfetsBondSnapshot::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDCfetsBondSnapshot::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MarketIndicator)
}

// optional int32 DataMultiplePowerOf10 = 9;
inline void MDCfetsBondSnapshot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondSnapshot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDCfetsBondSnapshot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.DataMultiplePowerOf10)
}

// optional int32 BondSnapshotType = 16;
inline void MDCfetsBondSnapshot::clear_bondsnapshottype() {
  bondsnapshottype_ = 0;
}
inline ::google::protobuf::int32 MDCfetsBondSnapshot::bondsnapshottype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondSnapshotType)
  return bondsnapshottype_;
}
inline void MDCfetsBondSnapshot::set_bondsnapshottype(::google::protobuf::int32 value) {
  
  bondsnapshottype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondSnapshotType)
}

// optional .com.htsc.mdc.insight.model.CashBondTradingSnapshot CashBondTradingSnapshot = 17;
inline bool MDCfetsBondSnapshot::has_cashbondtradingsnapshot() const {
  return this != internal_default_instance() && cashbondtradingsnapshot_ != NULL;
}
inline void MDCfetsBondSnapshot::clear_cashbondtradingsnapshot() {
  if (GetArenaNoVirtual() == NULL && cashbondtradingsnapshot_ != NULL) delete cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::CashBondTradingSnapshot& MDCfetsBondSnapshot::cashbondtradingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  return cashbondtradingsnapshot_ != NULL ? *cashbondtradingsnapshot_
                         : *::com::htsc::mdc::insight::model::CashBondTradingSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* MDCfetsBondSnapshot::mutable_cashbondtradingsnapshot() {
  
  if (cashbondtradingsnapshot_ == NULL) {
    cashbondtradingsnapshot_ = new ::com::htsc::mdc::insight::model::CashBondTradingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  return cashbondtradingsnapshot_;
}
inline ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* MDCfetsBondSnapshot::release_cashbondtradingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
  
  ::com::htsc::mdc::insight::model::CashBondTradingSnapshot* temp = cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsBondSnapshot::set_allocated_cashbondtradingsnapshot(::com::htsc::mdc::insight::model::CashBondTradingSnapshot* cashbondtradingsnapshot) {
  delete cashbondtradingsnapshot_;
  cashbondtradingsnapshot_ = cashbondtradingsnapshot;
  if (cashbondtradingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.CashBondTradingSnapshot)
}

// optional .com.htsc.mdc.insight.model.BondForwardSnapshot BondForwardSnapshot = 18;
inline bool MDCfetsBondSnapshot::has_bondforwardsnapshot() const {
  return this != internal_default_instance() && bondforwardsnapshot_ != NULL;
}
inline void MDCfetsBondSnapshot::clear_bondforwardsnapshot() {
  if (GetArenaNoVirtual() == NULL && bondforwardsnapshot_ != NULL) delete bondforwardsnapshot_;
  bondforwardsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::BondForwardSnapshot& MDCfetsBondSnapshot::bondforwardsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  return bondforwardsnapshot_ != NULL ? *bondforwardsnapshot_
                         : *::com::htsc::mdc::insight::model::BondForwardSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::BondForwardSnapshot* MDCfetsBondSnapshot::mutable_bondforwardsnapshot() {
  
  if (bondforwardsnapshot_ == NULL) {
    bondforwardsnapshot_ = new ::com::htsc::mdc::insight::model::BondForwardSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  return bondforwardsnapshot_;
}
inline ::com::htsc::mdc::insight::model::BondForwardSnapshot* MDCfetsBondSnapshot::release_bondforwardsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
  
  ::com::htsc::mdc::insight::model::BondForwardSnapshot* temp = bondforwardsnapshot_;
  bondforwardsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsBondSnapshot::set_allocated_bondforwardsnapshot(::com::htsc::mdc::insight::model::BondForwardSnapshot* bondforwardsnapshot) {
  delete bondforwardsnapshot_;
  bondforwardsnapshot_ = bondforwardsnapshot;
  if (bondforwardsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondForwardSnapshot)
}

// optional .com.htsc.mdc.insight.model.BondLendingSnapshot BondLendingSnapshot = 19;
inline bool MDCfetsBondSnapshot::has_bondlendingsnapshot() const {
  return this != internal_default_instance() && bondlendingsnapshot_ != NULL;
}
inline void MDCfetsBondSnapshot::clear_bondlendingsnapshot() {
  if (GetArenaNoVirtual() == NULL && bondlendingsnapshot_ != NULL) delete bondlendingsnapshot_;
  bondlendingsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::BondLendingSnapshot& MDCfetsBondSnapshot::bondlendingsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  return bondlendingsnapshot_ != NULL ? *bondlendingsnapshot_
                         : *::com::htsc::mdc::insight::model::BondLendingSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::BondLendingSnapshot* MDCfetsBondSnapshot::mutable_bondlendingsnapshot() {
  
  if (bondlendingsnapshot_ == NULL) {
    bondlendingsnapshot_ = new ::com::htsc::mdc::insight::model::BondLendingSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  return bondlendingsnapshot_;
}
inline ::com::htsc::mdc::insight::model::BondLendingSnapshot* MDCfetsBondSnapshot::release_bondlendingsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
  
  ::com::htsc::mdc::insight::model::BondLendingSnapshot* temp = bondlendingsnapshot_;
  bondlendingsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsBondSnapshot::set_allocated_bondlendingsnapshot(::com::htsc::mdc::insight::model::BondLendingSnapshot* bondlendingsnapshot) {
  delete bondlendingsnapshot_;
  bondlendingsnapshot_ = bondlendingsnapshot;
  if (bondlendingsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.BondLendingSnapshot)
}

// optional .com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot StandardisedBondForwardSnapshot = 20;
inline bool MDCfetsBondSnapshot::has_standardisedbondforwardsnapshot() const {
  return this != internal_default_instance() && standardisedbondforwardsnapshot_ != NULL;
}
inline void MDCfetsBondSnapshot::clear_standardisedbondforwardsnapshot() {
  if (GetArenaNoVirtual() == NULL && standardisedbondforwardsnapshot_ != NULL) delete standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot& MDCfetsBondSnapshot::standardisedbondforwardsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  return standardisedbondforwardsnapshot_ != NULL ? *standardisedbondforwardsnapshot_
                         : *::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* MDCfetsBondSnapshot::mutable_standardisedbondforwardsnapshot() {
  
  if (standardisedbondforwardsnapshot_ == NULL) {
    standardisedbondforwardsnapshot_ = new ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  return standardisedbondforwardsnapshot_;
}
inline ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* MDCfetsBondSnapshot::release_standardisedbondforwardsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
  
  ::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* temp = standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = NULL;
  return temp;
}
inline void MDCfetsBondSnapshot::set_allocated_standardisedbondforwardsnapshot(::com::htsc::mdc::insight::model::StandardisedBondForwardSnapshot* standardisedbondforwardsnapshot) {
  delete standardisedbondforwardsnapshot_;
  standardisedbondforwardsnapshot_ = standardisedbondforwardsnapshot;
  if (standardisedbondforwardsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.StandardisedBondForwardSnapshot)
}

// optional int64 MessageNumber = 100;
inline void MDCfetsBondSnapshot::clear_messagenumber() {
  messagenumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDCfetsBondSnapshot::messagenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MessageNumber)
  return messagenumber_;
}
inline void MDCfetsBondSnapshot::set_messagenumber(::google::protobuf::int64 value) {
  
  messagenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsBondSnapshot.MessageNumber)
}

inline const MDCfetsBondSnapshot* MDCfetsBondSnapshot::internal_default_instance() {
  return &MDCfetsBondSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// CashBondTradingSnapshot

// optional int32 TradeMethod = 1;
inline void CashBondTradingSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 CashBondTradingSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeMethod)
  return trademethod_;
}
inline void CashBondTradingSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeMethod)
}

// optional string SettlType = 2;
inline void CashBondTradingSnapshot::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CashBondTradingSnapshot::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingSnapshot::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
inline void CashBondTradingSnapshot::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
inline void CashBondTradingSnapshot::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}
inline ::std::string* CashBondTradingSnapshot::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CashBondTradingSnapshot::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingSnapshot::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingSnapshot.SettlType)
}

// optional string Side = 3;
inline void CashBondTradingSnapshot::clear_side() {
  side_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& CashBondTradingSnapshot::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  return side_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingSnapshot::set_side(const ::std::string& value) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
inline void CashBondTradingSnapshot::set_side(const char* value) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
inline void CashBondTradingSnapshot::set_side(const char* value, size_t size) {
  
  side_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}
inline ::std::string* CashBondTradingSnapshot::mutable_side() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  return side_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* CashBondTradingSnapshot::release_side() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
  
  return side_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void CashBondTradingSnapshot::set_allocated_side(::std::string* side) {
  if (side != NULL) {
    
  } else {
    
  }
  side_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), side);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.CashBondTradingSnapshot.Side)
}

// optional bool PreMarketBondIndicator = 4;
inline void CashBondTradingSnapshot::clear_premarketbondindicator() {
  premarketbondindicator_ = false;
}
inline bool CashBondTradingSnapshot::premarketbondindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreMarketBondIndicator)
  return premarketbondindicator_;
}
inline void CashBondTradingSnapshot::set_premarketbondindicator(bool value) {
  
  premarketbondindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreMarketBondIndicator)
}

// optional double PreCloseCleanPx = 11;
inline void CashBondTradingSnapshot::clear_preclosecleanpx() {
  preclosecleanpx_ = 0;
}
inline double CashBondTradingSnapshot::preclosecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseCleanPx)
  return preclosecleanpx_;
}
inline void CashBondTradingSnapshot::set_preclosecleanpx(double value) {
  
  preclosecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseCleanPx)
}

// optional double PreWeightedAvgCleanPx = 12;
inline void CashBondTradingSnapshot::clear_preweightedavgcleanpx() {
  preweightedavgcleanpx_ = 0;
}
inline double CashBondTradingSnapshot::preweightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgCleanPx)
  return preweightedavgcleanpx_;
}
inline void CashBondTradingSnapshot::set_preweightedavgcleanpx(double value) {
  
  preweightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgCleanPx)
}

// optional double OpenCleanPx = 13;
inline void CashBondTradingSnapshot::clear_opencleanpx() {
  opencleanpx_ = 0;
}
inline double CashBondTradingSnapshot::opencleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenCleanPx)
  return opencleanpx_;
}
inline void CashBondTradingSnapshot::set_opencleanpx(double value) {
  
  opencleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenCleanPx)
}

// optional double LastCleanPx = 14;
inline void CashBondTradingSnapshot::clear_lastcleanpx() {
  lastcleanpx_ = 0;
}
inline double CashBondTradingSnapshot::lastcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastCleanPx)
  return lastcleanpx_;
}
inline void CashBondTradingSnapshot::set_lastcleanpx(double value) {
  
  lastcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastCleanPx)
}

// optional double ChangePercent = 15;
inline void CashBondTradingSnapshot::clear_changepercent() {
  changepercent_ = 0;
}
inline double CashBondTradingSnapshot::changepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.ChangePercent)
  return changepercent_;
}
inline void CashBondTradingSnapshot::set_changepercent(double value) {
  
  changepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.ChangePercent)
}

// optional double HighCleanPx = 16;
inline void CashBondTradingSnapshot::clear_highcleanpx() {
  highcleanpx_ = 0;
}
inline double CashBondTradingSnapshot::highcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighCleanPx)
  return highcleanpx_;
}
inline void CashBondTradingSnapshot::set_highcleanpx(double value) {
  
  highcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighCleanPx)
}

// optional double LowCleanPx = 17;
inline void CashBondTradingSnapshot::clear_lowcleanpx() {
  lowcleanpx_ = 0;
}
inline double CashBondTradingSnapshot::lowcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowCleanPx)
  return lowcleanpx_;
}
inline void CashBondTradingSnapshot::set_lowcleanpx(double value) {
  
  lowcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowCleanPx)
}

// optional double CloseCleanPx = 18;
inline void CashBondTradingSnapshot::clear_closecleanpx() {
  closecleanpx_ = 0;
}
inline double CashBondTradingSnapshot::closecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseCleanPx)
  return closecleanpx_;
}
inline void CashBondTradingSnapshot::set_closecleanpx(double value) {
  
  closecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseCleanPx)
}

// optional double WeightedAvgCleanPx = 19;
inline void CashBondTradingSnapshot::clear_weightedavgcleanpx() {
  weightedavgcleanpx_ = 0;
}
inline double CashBondTradingSnapshot::weightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgCleanPx)
  return weightedavgcleanpx_;
}
inline void CashBondTradingSnapshot::set_weightedavgcleanpx(double value) {
  
  weightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgCleanPx)
}

// optional double PreCloseYield = 20;
inline void CashBondTradingSnapshot::clear_precloseyield() {
  precloseyield_ = 0;
}
inline double CashBondTradingSnapshot::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseYield)
  return precloseyield_;
}
inline void CashBondTradingSnapshot::set_precloseyield(double value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreCloseYield)
}

// optional double PreWeightedAvgYield = 21;
inline void CashBondTradingSnapshot::clear_preweightedavgyield() {
  preweightedavgyield_ = 0;
}
inline double CashBondTradingSnapshot::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgYield)
  return preweightedavgyield_;
}
inline void CashBondTradingSnapshot::set_preweightedavgyield(double value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.PreWeightedAvgYield)
}

// optional double OpenYield = 22;
inline void CashBondTradingSnapshot::clear_openyield() {
  openyield_ = 0;
}
inline double CashBondTradingSnapshot::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenYield)
  return openyield_;
}
inline void CashBondTradingSnapshot::set_openyield(double value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.OpenYield)
}

// optional double LastYield = 23;
inline void CashBondTradingSnapshot::clear_lastyield() {
  lastyield_ = 0;
}
inline double CashBondTradingSnapshot::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastYield)
  return lastyield_;
}
inline void CashBondTradingSnapshot::set_lastyield(double value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LastYield)
}

// optional double HighYield = 24;
inline void CashBondTradingSnapshot::clear_highyield() {
  highyield_ = 0;
}
inline double CashBondTradingSnapshot::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighYield)
  return highyield_;
}
inline void CashBondTradingSnapshot::set_highyield(double value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.HighYield)
}

// optional double LowYield = 25;
inline void CashBondTradingSnapshot::clear_lowyield() {
  lowyield_ = 0;
}
inline double CashBondTradingSnapshot::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowYield)
  return lowyield_;
}
inline void CashBondTradingSnapshot::set_lowyield(double value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.LowYield)
}

// optional double CloseYield = 26;
inline void CashBondTradingSnapshot::clear_closeyield() {
  closeyield_ = 0;
}
inline double CashBondTradingSnapshot::closeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseYield)
  return closeyield_;
}
inline void CashBondTradingSnapshot::set_closeyield(double value) {
  
  closeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CloseYield)
}

// optional double WeightedAvgYield = 27;
inline void CashBondTradingSnapshot::clear_weightedavgyield() {
  weightedavgyield_ = 0;
}
inline double CashBondTradingSnapshot::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgYield)
  return weightedavgyield_;
}
inline void CashBondTradingSnapshot::set_weightedavgyield(double value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.WeightedAvgYield)
}

// optional double TradeVolume = 28;
inline void CashBondTradingSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
inline double CashBondTradingSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeVolume)
  return tradevolume_;
}
inline void CashBondTradingSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.TradeVolume)
}

// optional double CleanPxChange = 29;
inline void CashBondTradingSnapshot::clear_cleanpxchange() {
  cleanpxchange_ = 0;
}
inline double CashBondTradingSnapshot::cleanpxchange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChange)
  return cleanpxchange_;
}
inline void CashBondTradingSnapshot::set_cleanpxchange(double value) {
  
  cleanpxchange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChange)
}

// optional double CleanPxChangePercent = 30;
inline void CashBondTradingSnapshot::clear_cleanpxchangepercent() {
  cleanpxchangepercent_ = 0;
}
inline double CashBondTradingSnapshot::cleanpxchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChangePercent)
  return cleanpxchangepercent_;
}
inline void CashBondTradingSnapshot::set_cleanpxchangepercent(double value) {
  
  cleanpxchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.CleanPxChangePercent)
}

// optional double YieldChangeBP = 31;
inline void CashBondTradingSnapshot::clear_yieldchangebp() {
  yieldchangebp_ = 0;
}
inline double CashBondTradingSnapshot::yieldchangebp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.CashBondTradingSnapshot.YieldChangeBP)
  return yieldchangebp_;
}
inline void CashBondTradingSnapshot::set_yieldchangebp(double value) {
  
  yieldchangebp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.CashBondTradingSnapshot.YieldChangeBP)
}

inline const CashBondTradingSnapshot* CashBondTradingSnapshot::internal_default_instance() {
  return &CashBondTradingSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// BondForwardSnapshot

// optional string UnderlyingSymbol = 1;
inline void BondForwardSnapshot::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondForwardSnapshot::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondForwardSnapshot::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
inline void BondForwardSnapshot::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
inline void BondForwardSnapshot::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}
inline ::std::string* BondForwardSnapshot::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondForwardSnapshot::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondForwardSnapshot::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 2;
inline void BondForwardSnapshot::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondForwardSnapshot::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondForwardSnapshot::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
inline void BondForwardSnapshot::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
inline void BondForwardSnapshot::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}
inline ::std::string* BondForwardSnapshot::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondForwardSnapshot::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondForwardSnapshot::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondForwardSnapshot.UnderlyingSecurityID)
}

// optional double PreCloseCleanPx = 11;
inline void BondForwardSnapshot::clear_preclosecleanpx() {
  preclosecleanpx_ = 0;
}
inline double BondForwardSnapshot::preclosecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseCleanPx)
  return preclosecleanpx_;
}
inline void BondForwardSnapshot::set_preclosecleanpx(double value) {
  
  preclosecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseCleanPx)
}

// optional double PreWeightedAvgCleanPx = 12;
inline void BondForwardSnapshot::clear_preweightedavgcleanpx() {
  preweightedavgcleanpx_ = 0;
}
inline double BondForwardSnapshot::preweightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgCleanPx)
  return preweightedavgcleanpx_;
}
inline void BondForwardSnapshot::set_preweightedavgcleanpx(double value) {
  
  preweightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgCleanPx)
}

// optional double CleanPxChangePercent = 13;
inline void BondForwardSnapshot::clear_cleanpxchangepercent() {
  cleanpxchangepercent_ = 0;
}
inline double BondForwardSnapshot::cleanpxchangepercent() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CleanPxChangePercent)
  return cleanpxchangepercent_;
}
inline void BondForwardSnapshot::set_cleanpxchangepercent(double value) {
  
  cleanpxchangepercent_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CleanPxChangePercent)
}

// optional double OpenCleanPx = 14;
inline void BondForwardSnapshot::clear_opencleanpx() {
  opencleanpx_ = 0;
}
inline double BondForwardSnapshot::opencleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenCleanPx)
  return opencleanpx_;
}
inline void BondForwardSnapshot::set_opencleanpx(double value) {
  
  opencleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenCleanPx)
}

// optional double LastCleanPx = 15;
inline void BondForwardSnapshot::clear_lastcleanpx() {
  lastcleanpx_ = 0;
}
inline double BondForwardSnapshot::lastcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LastCleanPx)
  return lastcleanpx_;
}
inline void BondForwardSnapshot::set_lastcleanpx(double value) {
  
  lastcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LastCleanPx)
}

// optional double HighCleanPx = 16;
inline void BondForwardSnapshot::clear_highcleanpx() {
  highcleanpx_ = 0;
}
inline double BondForwardSnapshot::highcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.HighCleanPx)
  return highcleanpx_;
}
inline void BondForwardSnapshot::set_highcleanpx(double value) {
  
  highcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.HighCleanPx)
}

// optional double LowCleanPx = 17;
inline void BondForwardSnapshot::clear_lowcleanpx() {
  lowcleanpx_ = 0;
}
inline double BondForwardSnapshot::lowcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LowCleanPx)
  return lowcleanpx_;
}
inline void BondForwardSnapshot::set_lowcleanpx(double value) {
  
  lowcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LowCleanPx)
}

// optional double CloseCleanPx = 18;
inline void BondForwardSnapshot::clear_closecleanpx() {
  closecleanpx_ = 0;
}
inline double BondForwardSnapshot::closecleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseCleanPx)
  return closecleanpx_;
}
inline void BondForwardSnapshot::set_closecleanpx(double value) {
  
  closecleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseCleanPx)
}

// optional double WeightedAvgCleanPx = 19;
inline void BondForwardSnapshot::clear_weightedavgcleanpx() {
  weightedavgcleanpx_ = 0;
}
inline double BondForwardSnapshot::weightedavgcleanpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgCleanPx)
  return weightedavgcleanpx_;
}
inline void BondForwardSnapshot::set_weightedavgcleanpx(double value) {
  
  weightedavgcleanpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgCleanPx)
}

// optional double PreCloseYield = 20;
inline void BondForwardSnapshot::clear_precloseyield() {
  precloseyield_ = 0;
}
inline double BondForwardSnapshot::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseYield)
  return precloseyield_;
}
inline void BondForwardSnapshot::set_precloseyield(double value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreCloseYield)
}

// optional double PreWeightedAvgYield = 21;
inline void BondForwardSnapshot::clear_preweightedavgyield() {
  preweightedavgyield_ = 0;
}
inline double BondForwardSnapshot::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgYield)
  return preweightedavgyield_;
}
inline void BondForwardSnapshot::set_preweightedavgyield(double value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.PreWeightedAvgYield)
}

// optional double OpenYield = 22;
inline void BondForwardSnapshot::clear_openyield() {
  openyield_ = 0;
}
inline double BondForwardSnapshot::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenYield)
  return openyield_;
}
inline void BondForwardSnapshot::set_openyield(double value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.OpenYield)
}

// optional double LastYield = 23;
inline void BondForwardSnapshot::clear_lastyield() {
  lastyield_ = 0;
}
inline double BondForwardSnapshot::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LastYield)
  return lastyield_;
}
inline void BondForwardSnapshot::set_lastyield(double value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LastYield)
}

// optional double HighYield = 24;
inline void BondForwardSnapshot::clear_highyield() {
  highyield_ = 0;
}
inline double BondForwardSnapshot::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.HighYield)
  return highyield_;
}
inline void BondForwardSnapshot::set_highyield(double value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.HighYield)
}

// optional double LowYield = 25;
inline void BondForwardSnapshot::clear_lowyield() {
  lowyield_ = 0;
}
inline double BondForwardSnapshot::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.LowYield)
  return lowyield_;
}
inline void BondForwardSnapshot::set_lowyield(double value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.LowYield)
}

// optional double CloseYield = 26;
inline void BondForwardSnapshot::clear_closeyield() {
  closeyield_ = 0;
}
inline double BondForwardSnapshot::closeyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseYield)
  return closeyield_;
}
inline void BondForwardSnapshot::set_closeyield(double value) {
  
  closeyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.CloseYield)
}

// optional double WeightedAvgYield = 27;
inline void BondForwardSnapshot::clear_weightedavgyield() {
  weightedavgyield_ = 0;
}
inline double BondForwardSnapshot::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgYield)
  return weightedavgyield_;
}
inline void BondForwardSnapshot::set_weightedavgyield(double value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.WeightedAvgYield)
}

// optional double TradeVolume = 28;
inline void BondForwardSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
inline double BondForwardSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondForwardSnapshot.TradeVolume)
  return tradevolume_;
}
inline void BondForwardSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondForwardSnapshot.TradeVolume)
}

inline const BondForwardSnapshot* BondForwardSnapshot::internal_default_instance() {
  return &BondForwardSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// BondLendingSnapshot

// optional string UnderlyingSymbol = 1;
inline void BondLendingSnapshot::clear_underlyingsymbol() {
  underlyingsymbol_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingSnapshot::underlyingsymbol() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingSnapshot::set_underlyingsymbol(const ::std::string& value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
inline void BondLendingSnapshot::set_underlyingsymbol(const char* value) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
inline void BondLendingSnapshot::set_underlyingsymbol(const char* value, size_t size) {
  
  underlyingsymbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}
inline ::std::string* BondLendingSnapshot::mutable_underlyingsymbol() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  return underlyingsymbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingSnapshot::release_underlyingsymbol() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
  
  return underlyingsymbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingSnapshot::set_allocated_underlyingsymbol(::std::string* underlyingsymbol) {
  if (underlyingsymbol != NULL) {
    
  } else {
    
  }
  underlyingsymbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsymbol);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSymbol)
}

// optional string UnderlyingSecurityID = 2;
inline void BondLendingSnapshot::clear_underlyingsecurityid() {
  underlyingsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& BondLendingSnapshot::underlyingsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingSnapshot::set_underlyingsecurityid(const ::std::string& value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
inline void BondLendingSnapshot::set_underlyingsecurityid(const char* value) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
inline void BondLendingSnapshot::set_underlyingsecurityid(const char* value, size_t size) {
  
  underlyingsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}
inline ::std::string* BondLendingSnapshot::mutable_underlyingsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  return underlyingsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* BondLendingSnapshot::release_underlyingsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
  
  return underlyingsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void BondLendingSnapshot::set_allocated_underlyingsecurityid(::std::string* underlyingsecurityid) {
  if (underlyingsecurityid != NULL) {
    
  } else {
    
  }
  underlyingsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), underlyingsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.BondLendingSnapshot.UnderlyingSecurityID)
}

// optional double PreCloseRate = 11;
inline void BondLendingSnapshot::clear_precloserate() {
  precloserate_ = 0;
}
inline double BondLendingSnapshot::precloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.PreCloseRate)
  return precloserate_;
}
inline void BondLendingSnapshot::set_precloserate(double value) {
  
  precloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.PreCloseRate)
}

// optional double PreWeightedAvgRate = 12;
inline void BondLendingSnapshot::clear_preweightedavgrate() {
  preweightedavgrate_ = 0;
}
inline double BondLendingSnapshot::preweightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.PreWeightedAvgRate)
  return preweightedavgrate_;
}
inline void BondLendingSnapshot::set_preweightedavgrate(double value) {
  
  preweightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.PreWeightedAvgRate)
}

// optional double OpenRate = 13;
inline void BondLendingSnapshot::clear_openrate() {
  openrate_ = 0;
}
inline double BondLendingSnapshot::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.OpenRate)
  return openrate_;
}
inline void BondLendingSnapshot::set_openrate(double value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.OpenRate)
}

// optional double LastRate = 14;
inline void BondLendingSnapshot::clear_lastrate() {
  lastrate_ = 0;
}
inline double BondLendingSnapshot::lastrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.LastRate)
  return lastrate_;
}
inline void BondLendingSnapshot::set_lastrate(double value) {
  
  lastrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.LastRate)
}

// optional double HighRate = 15;
inline void BondLendingSnapshot::clear_highrate() {
  highrate_ = 0;
}
inline double BondLendingSnapshot::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.HighRate)
  return highrate_;
}
inline void BondLendingSnapshot::set_highrate(double value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.HighRate)
}

// optional double LowRate = 16;
inline void BondLendingSnapshot::clear_lowrate() {
  lowrate_ = 0;
}
inline double BondLendingSnapshot::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.LowRate)
  return lowrate_;
}
inline void BondLendingSnapshot::set_lowrate(double value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.LowRate)
}

// optional double CloseRate = 17;
inline void BondLendingSnapshot::clear_closerate() {
  closerate_ = 0;
}
inline double BondLendingSnapshot::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.CloseRate)
  return closerate_;
}
inline void BondLendingSnapshot::set_closerate(double value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.CloseRate)
}

// optional double WeightedAvgRate = 18;
inline void BondLendingSnapshot::clear_weightedavgrate() {
  weightedavgrate_ = 0;
}
inline double BondLendingSnapshot::weightedavgrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.WeightedAvgRate)
  return weightedavgrate_;
}
inline void BondLendingSnapshot::set_weightedavgrate(double value) {
  
  weightedavgrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.WeightedAvgRate)
}

// optional double TradeVolume = 19;
inline void BondLendingSnapshot::clear_tradevolume() {
  tradevolume_ = 0;
}
inline double BondLendingSnapshot::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BondLendingSnapshot.TradeVolume)
  return tradevolume_;
}
inline void BondLendingSnapshot::set_tradevolume(double value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BondLendingSnapshot.TradeVolume)
}

inline const BondLendingSnapshot* BondLendingSnapshot::internal_default_instance() {
  return &BondLendingSnapshot_default_instance_.get();
}
// -------------------------------------------------------------------

// StandardisedBondForwardSnapshot

// optional int32 TradeMethod = 1;
inline void StandardisedBondForwardSnapshot::clear_trademethod() {
  trademethod_ = 0;
}
inline ::google::protobuf::int32 StandardisedBondForwardSnapshot::trademethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TradeMethod)
  return trademethod_;
}
inline void StandardisedBondForwardSnapshot::set_trademethod(::google::protobuf::int32 value) {
  
  trademethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TradeMethod)
}

// optional double OpenPx = 11;
inline void StandardisedBondForwardSnapshot::clear_openpx() {
  openpx_ = 0;
}
inline double StandardisedBondForwardSnapshot::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.OpenPx)
  return openpx_;
}
inline void StandardisedBondForwardSnapshot::set_openpx(double value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.OpenPx)
}

// optional double HighPx = 12;
inline void StandardisedBondForwardSnapshot::clear_highpx() {
  highpx_ = 0;
}
inline double StandardisedBondForwardSnapshot::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.HighPx)
  return highpx_;
}
inline void StandardisedBondForwardSnapshot::set_highpx(double value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.HighPx)
}

// optional double LowPx = 13;
inline void StandardisedBondForwardSnapshot::clear_lowpx() {
  lowpx_ = 0;
}
inline double StandardisedBondForwardSnapshot::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LowPx)
  return lowpx_;
}
inline void StandardisedBondForwardSnapshot::set_lowpx(double value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LowPx)
}

// optional double LastPx = 14;
inline void StandardisedBondForwardSnapshot::clear_lastpx() {
  lastpx_ = 0;
}
inline double StandardisedBondForwardSnapshot::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastPx)
  return lastpx_;
}
inline void StandardisedBondForwardSnapshot::set_lastpx(double value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastPx)
}

// optional double TotalVolumeTrade = 15;
inline void StandardisedBondForwardSnapshot::clear_totalvolumetrade() {
  totalvolumetrade_ = 0;
}
inline double StandardisedBondForwardSnapshot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void StandardisedBondForwardSnapshot::set_totalvolumetrade(double value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.TotalVolumeTrade)
}

// optional double LastVolumeTrade = 16;
inline void StandardisedBondForwardSnapshot::clear_lastvolumetrade() {
  lastvolumetrade_ = 0;
}
inline double StandardisedBondForwardSnapshot::lastvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastVolumeTrade)
  return lastvolumetrade_;
}
inline void StandardisedBondForwardSnapshot::set_lastvolumetrade(double value) {
  
  lastvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.LastVolumeTrade)
}

// optional double SettlePx = 50;
inline void StandardisedBondForwardSnapshot::clear_settlepx() {
  settlepx_ = 0;
}
inline double StandardisedBondForwardSnapshot::settlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePx)
  return settlepx_;
}
inline void StandardisedBondForwardSnapshot::set_settlepx(double value) {
  
  settlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePx)
}

// optional string SettlePxDate = 51;
inline void StandardisedBondForwardSnapshot::clear_settlepxdate() {
  settlepxdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& StandardisedBondForwardSnapshot::settlepxdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  return settlepxdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void StandardisedBondForwardSnapshot::set_settlepxdate(const ::std::string& value) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
inline void StandardisedBondForwardSnapshot::set_settlepxdate(const char* value) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
inline void StandardisedBondForwardSnapshot::set_settlepxdate(const char* value, size_t size) {
  
  settlepxdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}
inline ::std::string* StandardisedBondForwardSnapshot::mutable_settlepxdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  return settlepxdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* StandardisedBondForwardSnapshot::release_settlepxdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
  
  return settlepxdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void StandardisedBondForwardSnapshot::set_allocated_settlepxdate(::std::string* settlepxdate) {
  if (settlepxdate != NULL) {
    
  } else {
    
  }
  settlepxdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settlepxdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.StandardisedBondForwardSnapshot.SettlePxDate)
}

inline const StandardisedBondForwardSnapshot* StandardisedBondForwardSnapshot::internal_default_instance() {
  return &StandardisedBondForwardSnapshot_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDCfetsBondSnapshot_2eproto__INCLUDED
