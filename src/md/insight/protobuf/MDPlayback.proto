syntax = "proto3";

package com.htsc.mdc.insight.model;

message MDPlaybackControlRequest {
  string playbackID = 1;
  int32 controlType = 2; // 1=start, 2=pause, 3=stop, 4=seek
  int32 seekTime = 3;    // Unix timestamp, if controlType=4
}

message MDPlaybackStatus {
  string playbackID = 1;
  int32 status = 2;      // 0=idle, 1=playing, 2=paused, 3=stopped
  int32 currentTime = 3; // Unix timestamp
}
