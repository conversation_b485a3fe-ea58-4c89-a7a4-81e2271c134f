// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDBond.proto

#ifndef PROTOBUF_MDBond_2eproto__INCLUDED
#define PROTOBUF_MDBond_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDBond_2eproto();
void protobuf_InitDefaults_MDBond_2eproto();
void protobuf_AssignDesc_MDBond_2eproto();
void protobuf_ShutdownFile_MDBond_2eproto();

class MDBond;

// ===================================================================

class MDBond : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDBond) */ {
 public:
  MDBond();
  virtual ~MDBond();

  MDBond(const MDBond& from);

  inline MDBond& operator=(const MDBond& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDBond& default_instance();

  static const MDBond* internal_default_instance();

  void Swap(MDBond* other);

  // implements Message ----------------------------------------------

  inline MDBond* New() const { return New(NULL); }

  MDBond* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDBond& from);
  void MergeFrom(const MDBond& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDBond* other);
  void UnsafeMergeFrom(const MDBond& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int64 DiffPx1 = 19;
  void clear_diffpx1();
  static const int kDiffPx1FieldNumber = 19;
  ::google::protobuf::int64 diffpx1() const;
  void set_diffpx1(::google::protobuf::int64 value);

  // optional int64 DiffPx2 = 20;
  void clear_diffpx2();
  static const int kDiffPx2FieldNumber = 20;
  ::google::protobuf::int64 diffpx2() const;
  void set_diffpx2(::google::protobuf::int64 value);

  // optional int64 TotalBuyQty = 21;
  void clear_totalbuyqty();
  static const int kTotalBuyQtyFieldNumber = 21;
  ::google::protobuf::int64 totalbuyqty() const;
  void set_totalbuyqty(::google::protobuf::int64 value);

  // optional int64 TotalSellQty = 22;
  void clear_totalsellqty();
  static const int kTotalSellQtyFieldNumber = 22;
  ::google::protobuf::int64 totalsellqty() const;
  void set_totalsellqty(::google::protobuf::int64 value);

  // optional int64 WeightedAvgBuyPx = 23;
  void clear_weightedavgbuypx();
  static const int kWeightedAvgBuyPxFieldNumber = 23;
  ::google::protobuf::int64 weightedavgbuypx() const;
  void set_weightedavgbuypx(::google::protobuf::int64 value);

  // optional int64 WeightedAvgSellPx = 24;
  void clear_weightedavgsellpx();
  static const int kWeightedAvgSellPxFieldNumber = 24;
  ::google::protobuf::int64 weightedavgsellpx() const;
  void set_weightedavgsellpx(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyNumber = 25;
  void clear_withdrawbuynumber();
  static const int kWithdrawBuyNumberFieldNumber = 25;
  ::google::protobuf::int64 withdrawbuynumber() const;
  void set_withdrawbuynumber(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyAmount = 26;
  void clear_withdrawbuyamount();
  static const int kWithdrawBuyAmountFieldNumber = 26;
  ::google::protobuf::int64 withdrawbuyamount() const;
  void set_withdrawbuyamount(::google::protobuf::int64 value);

  // optional int64 WithdrawBuyMoney = 27;
  void clear_withdrawbuymoney();
  static const int kWithdrawBuyMoneyFieldNumber = 27;
  ::google::protobuf::int64 withdrawbuymoney() const;
  void set_withdrawbuymoney(::google::protobuf::int64 value);

  // optional int64 WithdrawSellNumber = 28;
  void clear_withdrawsellnumber();
  static const int kWithdrawSellNumberFieldNumber = 28;
  ::google::protobuf::int64 withdrawsellnumber() const;
  void set_withdrawsellnumber(::google::protobuf::int64 value);

  // optional int64 WithdrawSellAmount = 29;
  void clear_withdrawsellamount();
  static const int kWithdrawSellAmountFieldNumber = 29;
  ::google::protobuf::int64 withdrawsellamount() const;
  void set_withdrawsellamount(::google::protobuf::int64 value);

  // optional int64 WithdrawSellMoney = 30;
  void clear_withdrawsellmoney();
  static const int kWithdrawSellMoneyFieldNumber = 30;
  ::google::protobuf::int64 withdrawsellmoney() const;
  void set_withdrawsellmoney(::google::protobuf::int64 value);

  // optional int64 TotalBuyNumber = 31;
  void clear_totalbuynumber();
  static const int kTotalBuyNumberFieldNumber = 31;
  ::google::protobuf::int64 totalbuynumber() const;
  void set_totalbuynumber(::google::protobuf::int64 value);

  // optional int64 TotalSellNumber = 32;
  void clear_totalsellnumber();
  static const int kTotalSellNumberFieldNumber = 32;
  ::google::protobuf::int64 totalsellnumber() const;
  void set_totalsellnumber(::google::protobuf::int64 value);

  // optional int64 BuyTradeMaxDuration = 33;
  void clear_buytrademaxduration();
  static const int kBuyTradeMaxDurationFieldNumber = 33;
  ::google::protobuf::int64 buytrademaxduration() const;
  void set_buytrademaxduration(::google::protobuf::int64 value);

  // optional int64 SellTradeMaxDuration = 34;
  void clear_selltrademaxduration();
  static const int kSellTradeMaxDurationFieldNumber = 34;
  ::google::protobuf::int64 selltrademaxduration() const;
  void set_selltrademaxduration(::google::protobuf::int64 value);

  // optional int32 NumBuyOrders = 35;
  void clear_numbuyorders();
  static const int kNumBuyOrdersFieldNumber = 35;
  ::google::protobuf::int32 numbuyorders() const;
  void set_numbuyorders(::google::protobuf::int32 value);

  // optional int32 NumSellOrders = 36;
  void clear_numsellorders();
  static const int kNumSellOrdersFieldNumber = 36;
  ::google::protobuf::int32 numsellorders() const;
  void set_numsellorders(::google::protobuf::int32 value);

  // optional int64 YieldToMaturity = 37;
  void clear_yieldtomaturity();
  static const int kYieldToMaturityFieldNumber = 37;
  ::google::protobuf::int64 yieldtomaturity() const;
  void set_yieldtomaturity(::google::protobuf::int64 value);

  // optional int64 WeightedAvgPx = 38;
  void clear_weightedavgpx();
  static const int kWeightedAvgPxFieldNumber = 38;
  ::google::protobuf::int64 weightedavgpx() const;
  void set_weightedavgpx(::google::protobuf::int64 value);

  // optional int64 WeightedAvgPxBP = 39;
  void clear_weightedavgpxbp();
  static const int kWeightedAvgPxBPFieldNumber = 39;
  ::google::protobuf::int64 weightedavgpxbp() const;
  void set_weightedavgpxbp(::google::protobuf::int64 value);

  // optional int64 PreCloseWeightedAvgPx = 40;
  void clear_precloseweightedavgpx();
  static const int kPreCloseWeightedAvgPxFieldNumber = 40;
  ::google::protobuf::int64 precloseweightedavgpx() const;
  void set_precloseweightedavgpx(::google::protobuf::int64 value);

  // optional int32 ExchangeDate = 41;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 41;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 42;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 42;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 PreCloseYield = 43;
  void clear_precloseyield();
  static const int kPreCloseYieldFieldNumber = 43;
  ::google::protobuf::int64 precloseyield() const;
  void set_precloseyield(::google::protobuf::int64 value);

  // optional int64 PreWeightedAvgYield = 44;
  void clear_preweightedavgyield();
  static const int kPreWeightedAvgYieldFieldNumber = 44;
  ::google::protobuf::int64 preweightedavgyield() const;
  void set_preweightedavgyield(::google::protobuf::int64 value);

  // optional int64 OpenYield = 45;
  void clear_openyield();
  static const int kOpenYieldFieldNumber = 45;
  ::google::protobuf::int64 openyield() const;
  void set_openyield(::google::protobuf::int64 value);

  // optional int64 HighYield = 46;
  void clear_highyield();
  static const int kHighYieldFieldNumber = 46;
  ::google::protobuf::int64 highyield() const;
  void set_highyield(::google::protobuf::int64 value);

  // optional int64 LowYield = 47;
  void clear_lowyield();
  static const int kLowYieldFieldNumber = 47;
  ::google::protobuf::int64 lowyield() const;
  void set_lowyield(::google::protobuf::int64 value);

  // optional int64 LastYield = 48;
  void clear_lastyield();
  static const int kLastYieldFieldNumber = 48;
  ::google::protobuf::int64 lastyield() const;
  void set_lastyield(::google::protobuf::int64 value);

  // optional int64 WeightedAvgYield = 49;
  void clear_weightedavgyield();
  static const int kWeightedAvgYieldFieldNumber = 49;
  ::google::protobuf::int64 weightedavgyield() const;
  void set_weightedavgyield(::google::protobuf::int64 value);

  // optional int32 ChannelNo = 50;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 50;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int64 NorminalPx = 59;
  void clear_norminalpx();
  static const int kNorminalPxFieldNumber = 59;
  ::google::protobuf::int64 norminalpx() const;
  void set_norminalpx(::google::protobuf::int64 value);

  // optional int64 ShortSellSharesTraded = 60;
  void clear_shortsellsharestraded();
  static const int kShortSellSharesTradedFieldNumber = 60;
  ::google::protobuf::int64 shortsellsharestraded() const;
  void set_shortsellsharestraded(::google::protobuf::int64 value);

  // optional int64 ShortSellTurnover = 61;
  void clear_shortsellturnover();
  static const int kShortSellTurnoverFieldNumber = 61;
  ::google::protobuf::int64 shortsellturnover() const;
  void set_shortsellturnover(::google::protobuf::int64 value);

  // repeated int32 BuySettlTypeQueue = 62 [packed = true];
  int buysettltypequeue_size() const;
  void clear_buysettltypequeue();
  static const int kBuySettlTypeQueueFieldNumber = 62;
  ::google::protobuf::int32 buysettltypequeue(int index) const;
  void set_buysettltypequeue(int index, ::google::protobuf::int32 value);
  void add_buysettltypequeue(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      buysettltypequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_buysettltypequeue();

  // repeated int32 SellSettlTypeQueue = 63 [packed = true];
  int sellsettltypequeue_size() const;
  void clear_sellsettltypequeue();
  static const int kSellSettlTypeQueueFieldNumber = 63;
  ::google::protobuf::int32 sellsettltypequeue(int index) const;
  void set_sellsettltypequeue(int index, ::google::protobuf::int32 value);
  void add_sellsettltypequeue(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      sellsettltypequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_sellsettltypequeue();

  // repeated int64 BuyYieldQueue = 64 [packed = true];
  int buyyieldqueue_size() const;
  void clear_buyyieldqueue();
  static const int kBuyYieldQueueFieldNumber = 64;
  ::google::protobuf::int64 buyyieldqueue(int index) const;
  void set_buyyieldqueue(int index, ::google::protobuf::int64 value);
  void add_buyyieldqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyyieldqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyyieldqueue();

  // repeated int64 SellYieldQueue = 65 [packed = true];
  int sellyieldqueue_size() const;
  void clear_sellyieldqueue();
  static const int kSellYieldQueueFieldNumber = 65;
  ::google::protobuf::int64 sellyieldqueue(int index) const;
  void set_sellyieldqueue(int index, ::google::protobuf::int64 value);
  void add_sellyieldqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellyieldqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellyieldqueue();

  // optional int64 PreMarketLastPx = 66;
  void clear_premarketlastpx();
  static const int kPreMarketLastPxFieldNumber = 66;
  ::google::protobuf::int64 premarketlastpx() const;
  void set_premarketlastpx(::google::protobuf::int64 value);

  // optional int64 PreMarketTotalVolumeTrade = 67;
  void clear_premarkettotalvolumetrade();
  static const int kPreMarketTotalVolumeTradeFieldNumber = 67;
  ::google::protobuf::int64 premarkettotalvolumetrade() const;
  void set_premarkettotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 PreMarketTotalValueTrade = 68;
  void clear_premarkettotalvaluetrade();
  static const int kPreMarketTotalValueTradeFieldNumber = 68;
  ::google::protobuf::int64 premarkettotalvaluetrade() const;
  void set_premarkettotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 PreMarketHighPx = 69;
  void clear_premarkethighpx();
  static const int kPreMarketHighPxFieldNumber = 69;
  ::google::protobuf::int64 premarkethighpx() const;
  void set_premarkethighpx(::google::protobuf::int64 value);

  // optional int64 PreMarketLowPx = 70;
  void clear_premarketlowpx();
  static const int kPreMarketLowPxFieldNumber = 70;
  ::google::protobuf::int64 premarketlowpx() const;
  void set_premarketlowpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursLastPx = 71;
  void clear_afterhourslastpx();
  static const int kAfterHoursLastPxFieldNumber = 71;
  ::google::protobuf::int64 afterhourslastpx() const;
  void set_afterhourslastpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursTotalVolumeTrade = 72;
  void clear_afterhourstotalvolumetrade();
  static const int kAfterHoursTotalVolumeTradeFieldNumber = 72;
  ::google::protobuf::int64 afterhourstotalvolumetrade() const;
  void set_afterhourstotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 AfterHoursTotalValueTrade = 73;
  void clear_afterhourstotalvaluetrade();
  static const int kAfterHoursTotalValueTradeFieldNumber = 73;
  ::google::protobuf::int64 afterhourstotalvaluetrade() const;
  void set_afterhourstotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 AfterHoursHighPx = 74;
  void clear_afterhourshighpx();
  static const int kAfterHoursHighPxFieldNumber = 74;
  ::google::protobuf::int64 afterhourshighpx() const;
  void set_afterhourshighpx(::google::protobuf::int64 value);

  // optional int64 AfterHoursLowPx = 75;
  void clear_afterhourslowpx();
  static const int kAfterHoursLowPxFieldNumber = 75;
  ::google::protobuf::int64 afterhourslowpx() const;
  void set_afterhourslowpx(::google::protobuf::int64 value);

  // optional string MarketPhaseCode = 76;
  void clear_marketphasecode();
  static const int kMarketPhaseCodeFieldNumber = 76;
  const ::std::string& marketphasecode() const;
  void set_marketphasecode(const ::std::string& value);
  void set_marketphasecode(const char* value);
  void set_marketphasecode(const char* value, size_t size);
  ::std::string* mutable_marketphasecode();
  ::std::string* release_marketphasecode();
  void set_allocated_marketphasecode(::std::string* marketphasecode);

  // optional string SubTradingPhaseCode1 = 77;
  void clear_subtradingphasecode1();
  static const int kSubTradingPhaseCode1FieldNumber = 77;
  const ::std::string& subtradingphasecode1() const;
  void set_subtradingphasecode1(const ::std::string& value);
  void set_subtradingphasecode1(const char* value);
  void set_subtradingphasecode1(const char* value, size_t size);
  ::std::string* mutable_subtradingphasecode1();
  ::std::string* release_subtradingphasecode1();
  void set_allocated_subtradingphasecode1(::std::string* subtradingphasecode1);

  // optional string SubTradingPhaseCode2 = 78;
  void clear_subtradingphasecode2();
  static const int kSubTradingPhaseCode2FieldNumber = 78;
  const ::std::string& subtradingphasecode2() const;
  void set_subtradingphasecode2(const ::std::string& value);
  void set_subtradingphasecode2(const char* value);
  void set_subtradingphasecode2(const char* value, size_t size);
  ::std::string* mutable_subtradingphasecode2();
  ::std::string* release_subtradingphasecode2();
  void set_allocated_subtradingphasecode2(::std::string* subtradingphasecode2);

  // optional string SubTradingPhaseCode3 = 79;
  void clear_subtradingphasecode3();
  static const int kSubTradingPhaseCode3FieldNumber = 79;
  const ::std::string& subtradingphasecode3() const;
  void set_subtradingphasecode3(const ::std::string& value);
  void set_subtradingphasecode3(const char* value);
  void set_subtradingphasecode3(const char* value, size_t size);
  ::std::string* mutable_subtradingphasecode3();
  ::std::string* release_subtradingphasecode3();
  void set_allocated_subtradingphasecode3(::std::string* subtradingphasecode3);

  // optional string SubTradingPhaseCode4 = 80;
  void clear_subtradingphasecode4();
  static const int kSubTradingPhaseCode4FieldNumber = 80;
  const ::std::string& subtradingphasecode4() const;
  void set_subtradingphasecode4(const ::std::string& value);
  void set_subtradingphasecode4(const char* value);
  void set_subtradingphasecode4(const char* value, size_t size);
  ::std::string* mutable_subtradingphasecode4();
  ::std::string* release_subtradingphasecode4();
  void set_allocated_subtradingphasecode4(::std::string* subtradingphasecode4);

  // optional string SubTradingPhaseCode5 = 81;
  void clear_subtradingphasecode5();
  static const int kSubTradingPhaseCode5FieldNumber = 81;
  const ::std::string& subtradingphasecode5() const;
  void set_subtradingphasecode5(const ::std::string& value);
  void set_subtradingphasecode5(const char* value);
  void set_subtradingphasecode5(const char* value, size_t size);
  ::std::string* mutable_subtradingphasecode5();
  ::std::string* release_subtradingphasecode5();
  void set_allocated_subtradingphasecode5(::std::string* subtradingphasecode5);

  // optional int32 LastPxType = 82;
  void clear_lastpxtype();
  static const int kLastPxTypeFieldNumber = 82;
  ::google::protobuf::int32 lastpxtype() const;
  void set_lastpxtype(::google::protobuf::int32 value);

  // optional int64 AuctionLastPx = 83;
  void clear_auctionlastpx();
  static const int kAuctionLastPxFieldNumber = 83;
  ::google::protobuf::int64 auctionlastpx() const;
  void set_auctionlastpx(::google::protobuf::int64 value);

  // optional int64 AuctionVolumeTrade = 84;
  void clear_auctionvolumetrade();
  static const int kAuctionVolumeTradeFieldNumber = 84;
  ::google::protobuf::int64 auctionvolumetrade() const;
  void set_auctionvolumetrade(::google::protobuf::int64 value);

  // optional int64 AuctionValueTrade = 85;
  void clear_auctionvaluetrade();
  static const int kAuctionValueTradeFieldNumber = 85;
  ::google::protobuf::int64 auctionvaluetrade() const;
  void set_auctionvaluetrade(::google::protobuf::int64 value);

  // optional int64 USConsolidateVolume = 86;
  void clear_usconsolidatevolume();
  static const int kUSConsolidateVolumeFieldNumber = 86;
  ::google::protobuf::int64 usconsolidatevolume() const;
  void set_usconsolidatevolume(::google::protobuf::int64 value);

  // optional int64 USCompositeClosePx = 87;
  void clear_uscompositeclosepx();
  static const int kUSCompositeClosePxFieldNumber = 87;
  ::google::protobuf::int64 uscompositeclosepx() const;
  void set_uscompositeclosepx(::google::protobuf::int64 value);

  // optional string TradingHaltReason = 88;
  void clear_tradinghaltreason();
  static const int kTradingHaltReasonFieldNumber = 88;
  const ::std::string& tradinghaltreason() const;
  void set_tradinghaltreason(const ::std::string& value);
  void set_tradinghaltreason(const char* value);
  void set_tradinghaltreason(const char* value, size_t size);
  ::std::string* mutable_tradinghaltreason();
  ::std::string* release_tradinghaltreason();
  void set_allocated_tradinghaltreason(::std::string* tradinghaltreason);

  // optional int64 OtcTotalVolumeTrade = 89;
  void clear_otctotalvolumetrade();
  static const int kOtcTotalVolumeTradeFieldNumber = 89;
  ::google::protobuf::int64 otctotalvolumetrade() const;
  void set_otctotalvolumetrade(::google::protobuf::int64 value);

  // optional int64 OtcTotalValueTrade = 90;
  void clear_otctotalvaluetrade();
  static const int kOtcTotalValueTradeFieldNumber = 90;
  ::google::protobuf::int64 otctotalvaluetrade() const;
  void set_otctotalvaluetrade(::google::protobuf::int64 value);

  // optional int64 OtcNumTrades = 91;
  void clear_otcnumtrades();
  static const int kOtcNumTradesFieldNumber = 91;
  ::google::protobuf::int64 otcnumtrades() const;
  void set_otcnumtrades(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 92;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 92;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 ReferencePx = 93;
  void clear_referencepx();
  static const int kReferencePxFieldNumber = 93;
  ::google::protobuf::int64 referencepx() const;
  void set_referencepx(::google::protobuf::int64 value);

  // optional int64 MaxBuyPrice = 94;
  void clear_maxbuyprice();
  static const int kMaxBuyPriceFieldNumber = 94;
  ::google::protobuf::int64 maxbuyprice() const;
  void set_maxbuyprice(::google::protobuf::int64 value);

  // optional int64 MinBuyPrice = 95;
  void clear_minbuyprice();
  static const int kMinBuyPriceFieldNumber = 95;
  ::google::protobuf::int64 minbuyprice() const;
  void set_minbuyprice(::google::protobuf::int64 value);

  // optional int64 MaxSellPrice = 96;
  void clear_maxsellprice();
  static const int kMaxSellPriceFieldNumber = 96;
  ::google::protobuf::int64 maxsellprice() const;
  void set_maxsellprice(::google::protobuf::int64 value);

  // optional int64 MinSellPrice = 97;
  void clear_minsellprice();
  static const int kMinSellPriceFieldNumber = 97;
  ::google::protobuf::int64 minsellprice() const;
  void set_minsellprice(::google::protobuf::int64 value);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDBond)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > buysettltypequeue_;
  mutable int _buysettltypequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > sellsettltypequeue_;
  mutable int _sellsettltypequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyyieldqueue_;
  mutable int _buyyieldqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellyieldqueue_;
  mutable int _sellyieldqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr marketphasecode_;
  ::google::protobuf::internal::ArenaStringPtr subtradingphasecode1_;
  ::google::protobuf::internal::ArenaStringPtr subtradingphasecode2_;
  ::google::protobuf::internal::ArenaStringPtr subtradingphasecode3_;
  ::google::protobuf::internal::ArenaStringPtr subtradingphasecode4_;
  ::google::protobuf::internal::ArenaStringPtr subtradingphasecode5_;
  ::google::protobuf::internal::ArenaStringPtr tradinghaltreason_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 diffpx1_;
  ::google::protobuf::int64 diffpx2_;
  ::google::protobuf::int64 totalbuyqty_;
  ::google::protobuf::int64 totalsellqty_;
  ::google::protobuf::int64 weightedavgbuypx_;
  ::google::protobuf::int64 weightedavgsellpx_;
  ::google::protobuf::int64 withdrawbuynumber_;
  ::google::protobuf::int64 withdrawbuyamount_;
  ::google::protobuf::int64 withdrawbuymoney_;
  ::google::protobuf::int64 withdrawsellnumber_;
  ::google::protobuf::int64 withdrawsellamount_;
  ::google::protobuf::int64 withdrawsellmoney_;
  ::google::protobuf::int64 totalbuynumber_;
  ::google::protobuf::int64 totalsellnumber_;
  ::google::protobuf::int64 buytrademaxduration_;
  ::google::protobuf::int64 selltrademaxduration_;
  ::google::protobuf::int32 numbuyorders_;
  ::google::protobuf::int32 numsellorders_;
  ::google::protobuf::int64 yieldtomaturity_;
  ::google::protobuf::int64 weightedavgpx_;
  ::google::protobuf::int64 weightedavgpxbp_;
  ::google::protobuf::int64 precloseweightedavgpx_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int64 precloseyield_;
  ::google::protobuf::int64 preweightedavgyield_;
  ::google::protobuf::int64 openyield_;
  ::google::protobuf::int64 highyield_;
  ::google::protobuf::int64 lowyield_;
  ::google::protobuf::int64 lastyield_;
  ::google::protobuf::int64 weightedavgyield_;
  ::google::protobuf::int64 norminalpx_;
  ::google::protobuf::int64 shortsellsharestraded_;
  ::google::protobuf::int64 shortsellturnover_;
  ::google::protobuf::int64 premarketlastpx_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 lastpxtype_;
  ::google::protobuf::int64 premarkettotalvolumetrade_;
  ::google::protobuf::int64 premarkettotalvaluetrade_;
  ::google::protobuf::int64 premarkethighpx_;
  ::google::protobuf::int64 premarketlowpx_;
  ::google::protobuf::int64 afterhourslastpx_;
  ::google::protobuf::int64 afterhourstotalvolumetrade_;
  ::google::protobuf::int64 afterhourstotalvaluetrade_;
  ::google::protobuf::int64 afterhourshighpx_;
  ::google::protobuf::int64 afterhourslowpx_;
  ::google::protobuf::int64 auctionlastpx_;
  ::google::protobuf::int64 auctionvolumetrade_;
  ::google::protobuf::int64 auctionvaluetrade_;
  ::google::protobuf::int64 usconsolidatevolume_;
  ::google::protobuf::int64 uscompositeclosepx_;
  ::google::protobuf::int64 otctotalvolumetrade_;
  ::google::protobuf::int64 otctotalvaluetrade_;
  ::google::protobuf::int64 otcnumtrades_;
  ::google::protobuf::int64 referencepx_;
  ::google::protobuf::int64 maxbuyprice_;
  ::google::protobuf::int64 minbuyprice_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 delaytype_;
  ::google::protobuf::int64 maxsellprice_;
  ::google::protobuf::int64 minsellprice_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDBond_2eproto_impl();
  friend void  protobuf_AddDesc_MDBond_2eproto_impl();
  friend void protobuf_AssignDesc_MDBond_2eproto();
  friend void protobuf_ShutdownFile_MDBond_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDBond> MDBond_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDBond

// optional string HTSCSecurityID = 1;
inline void MDBond::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
inline void MDBond::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
inline void MDBond::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}
inline ::std::string* MDBond::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDBond::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDBond::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MDDate)
  return mddate_;
}
inline void MDBond::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MDDate)
}

// optional int32 MDTime = 3;
inline void MDBond::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDBond::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MDTime)
  return mdtime_;
}
inline void MDBond::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDBond::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DataTimestamp)
  return datatimestamp_;
}
inline void MDBond::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDBond::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
inline void MDBond::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
inline void MDBond::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}
inline ::std::string* MDBond::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDBond::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDBond::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDBond::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDBond::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDBond::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDBond::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.securityType)
}

// optional int64 MaxPx = 8;
inline void MDBond::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxPx)
  return maxpx_;
}
inline void MDBond::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDBond::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinPx)
  return minpx_;
}
inline void MDBond::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDBond::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreClosePx)
  return preclosepx_;
}
inline void MDBond::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDBond::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumTrades)
  return numtrades_;
}
inline void MDBond::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDBond::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDBond::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDBond::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDBond::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void MDBond::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastPx)
  return lastpx_;
}
inline void MDBond::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastPx)
}

// optional int64 OpenPx = 15;
inline void MDBond::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OpenPx)
  return openpx_;
}
inline void MDBond::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OpenPx)
}

// optional int64 ClosePx = 16;
inline void MDBond::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ClosePx)
  return closepx_;
}
inline void MDBond::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ClosePx)
}

// optional int64 HighPx = 17;
inline void MDBond::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HighPx)
  return highpx_;
}
inline void MDBond::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HighPx)
}

// optional int64 LowPx = 18;
inline void MDBond::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LowPx)
  return lowpx_;
}
inline void MDBond::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LowPx)
}

// optional int64 DiffPx1 = 19;
inline void MDBond::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DiffPx1)
  return diffpx1_;
}
inline void MDBond::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DiffPx1)
}

// optional int64 DiffPx2 = 20;
inline void MDBond::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DiffPx2)
  return diffpx2_;
}
inline void MDBond::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
inline void MDBond::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalBuyQty)
  return totalbuyqty_;
}
inline void MDBond::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
inline void MDBond::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalSellQty)
  return totalsellqty_;
}
inline void MDBond::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
inline void MDBond::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
inline void MDBond::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
inline void MDBond::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
inline void MDBond::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
inline void MDBond::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
inline void MDBond::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
inline void MDBond::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
inline void MDBond::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
inline void MDBond::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
inline void MDBond::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
inline void MDBond::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellNumber)
  return withdrawsellnumber_;
}
inline void MDBond::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
inline void MDBond::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellAmount)
  return withdrawsellamount_;
}
inline void MDBond::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
inline void MDBond::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WithdrawSellMoney)
  return withdrawsellmoney_;
}
inline void MDBond::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
inline void MDBond::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalBuyNumber)
  return totalbuynumber_;
}
inline void MDBond::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
inline void MDBond::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TotalSellNumber)
  return totalsellnumber_;
}
inline void MDBond::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
inline void MDBond::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
inline void MDBond::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
inline void MDBond::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellTradeMaxDuration)
  return selltrademaxduration_;
}
inline void MDBond::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
inline void MDBond::clear_numbuyorders() {
  numbuyorders_ = 0;
}
inline ::google::protobuf::int32 MDBond::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumBuyOrders)
  return numbuyorders_;
}
inline void MDBond::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
inline void MDBond::clear_numsellorders() {
  numsellorders_ = 0;
}
inline ::google::protobuf::int32 MDBond::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NumSellOrders)
  return numsellorders_;
}
inline void MDBond::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NumSellOrders)
}

// optional int64 YieldToMaturity = 37;
inline void MDBond::clear_yieldtomaturity() {
  yieldtomaturity_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::yieldtomaturity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.YieldToMaturity)
  return yieldtomaturity_;
}
inline void MDBond::set_yieldtomaturity(::google::protobuf::int64 value) {
  
  yieldtomaturity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.YieldToMaturity)
}

// optional int64 WeightedAvgPx = 38;
inline void MDBond::clear_weightedavgpx() {
  weightedavgpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::weightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgPx)
  return weightedavgpx_;
}
inline void MDBond::set_weightedavgpx(::google::protobuf::int64 value) {
  
  weightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgPx)
}

// optional int64 WeightedAvgPxBP = 39;
inline void MDBond::clear_weightedavgpxbp() {
  weightedavgpxbp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::weightedavgpxbp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgPxBP)
  return weightedavgpxbp_;
}
inline void MDBond::set_weightedavgpxbp(::google::protobuf::int64 value) {
  
  weightedavgpxbp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgPxBP)
}

// optional int64 PreCloseWeightedAvgPx = 40;
inline void MDBond::clear_precloseweightedavgpx() {
  precloseweightedavgpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::precloseweightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreCloseWeightedAvgPx)
  return precloseweightedavgpx_;
}
inline void MDBond::set_precloseweightedavgpx(::google::protobuf::int64 value) {
  
  precloseweightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreCloseWeightedAvgPx)
}

// optional int32 ExchangeDate = 41;
inline void MDBond::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDBond::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ExchangeDate)
  return exchangedate_;
}
inline void MDBond::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ExchangeDate)
}

// optional int32 ExchangeTime = 42;
inline void MDBond::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDBond::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ExchangeTime)
  return exchangetime_;
}
inline void MDBond::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ExchangeTime)
}

// optional int64 PreCloseYield = 43;
inline void MDBond::clear_precloseyield() {
  precloseyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::precloseyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreCloseYield)
  return precloseyield_;
}
inline void MDBond::set_precloseyield(::google::protobuf::int64 value) {
  
  precloseyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreCloseYield)
}

// optional int64 PreWeightedAvgYield = 44;
inline void MDBond::clear_preweightedavgyield() {
  preweightedavgyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::preweightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreWeightedAvgYield)
  return preweightedavgyield_;
}
inline void MDBond::set_preweightedavgyield(::google::protobuf::int64 value) {
  
  preweightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreWeightedAvgYield)
}

// optional int64 OpenYield = 45;
inline void MDBond::clear_openyield() {
  openyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::openyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OpenYield)
  return openyield_;
}
inline void MDBond::set_openyield(::google::protobuf::int64 value) {
  
  openyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OpenYield)
}

// optional int64 HighYield = 46;
inline void MDBond::clear_highyield() {
  highyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::highyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.HighYield)
  return highyield_;
}
inline void MDBond::set_highyield(::google::protobuf::int64 value) {
  
  highyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.HighYield)
}

// optional int64 LowYield = 47;
inline void MDBond::clear_lowyield() {
  lowyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::lowyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LowYield)
  return lowyield_;
}
inline void MDBond::set_lowyield(::google::protobuf::int64 value) {
  
  lowyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LowYield)
}

// optional int64 LastYield = 48;
inline void MDBond::clear_lastyield() {
  lastyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::lastyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastYield)
  return lastyield_;
}
inline void MDBond::set_lastyield(::google::protobuf::int64 value) {
  
  lastyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastYield)
}

// optional int64 WeightedAvgYield = 49;
inline void MDBond::clear_weightedavgyield() {
  weightedavgyield_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::weightedavgyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.WeightedAvgYield)
  return weightedavgyield_;
}
inline void MDBond::set_weightedavgyield(::google::protobuf::int64 value) {
  
  weightedavgyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.WeightedAvgYield)
}

// optional int32 ChannelNo = 50;
inline void MDBond::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDBond::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ChannelNo)
  return channelno_;
}
inline void MDBond::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDBond::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDBond::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDBond::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDBond::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
}
inline void MDBond::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDBond::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDBond::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDBond::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
}
inline void MDBond::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDBond::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDBond::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDBond::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDBond::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
}
inline void MDBond::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDBond::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDBond::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDBond::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
}
inline void MDBond::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDBond::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDBond::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDBond::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
}
inline void MDBond::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDBond::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDBond::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDBond::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
}
inline void MDBond::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDBond::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDBond::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDBond::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
}
inline void MDBond::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDBond::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDBond::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDBond::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
}
inline void MDBond::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int64 NorminalPx = 59;
inline void MDBond::clear_norminalpx() {
  norminalpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::norminalpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.NorminalPx)
  return norminalpx_;
}
inline void MDBond::set_norminalpx(::google::protobuf::int64 value) {
  
  norminalpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.NorminalPx)
}

// optional int64 ShortSellSharesTraded = 60;
inline void MDBond::clear_shortsellsharestraded() {
  shortsellsharestraded_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::shortsellsharestraded() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ShortSellSharesTraded)
  return shortsellsharestraded_;
}
inline void MDBond::set_shortsellsharestraded(::google::protobuf::int64 value) {
  
  shortsellsharestraded_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ShortSellSharesTraded)
}

// optional int64 ShortSellTurnover = 61;
inline void MDBond::clear_shortsellturnover() {
  shortsellturnover_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::shortsellturnover() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ShortSellTurnover)
  return shortsellturnover_;
}
inline void MDBond::set_shortsellturnover(::google::protobuf::int64 value) {
  
  shortsellturnover_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ShortSellTurnover)
}

// repeated int32 BuySettlTypeQueue = 62 [packed = true];
inline int MDBond::buysettltypequeue_size() const {
  return buysettltypequeue_.size();
}
inline void MDBond::clear_buysettltypequeue() {
  buysettltypequeue_.Clear();
}
inline ::google::protobuf::int32 MDBond::buysettltypequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return buysettltypequeue_.Get(index);
}
inline void MDBond::set_buysettltypequeue(int index, ::google::protobuf::int32 value) {
  buysettltypequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
}
inline void MDBond::add_buysettltypequeue(::google::protobuf::int32 value) {
  buysettltypequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
MDBond::buysettltypequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return buysettltypequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
MDBond::mutable_buysettltypequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuySettlTypeQueue)
  return &buysettltypequeue_;
}

// repeated int32 SellSettlTypeQueue = 63 [packed = true];
inline int MDBond::sellsettltypequeue_size() const {
  return sellsettltypequeue_.size();
}
inline void MDBond::clear_sellsettltypequeue() {
  sellsettltypequeue_.Clear();
}
inline ::google::protobuf::int32 MDBond::sellsettltypequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return sellsettltypequeue_.Get(index);
}
inline void MDBond::set_sellsettltypequeue(int index, ::google::protobuf::int32 value) {
  sellsettltypequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
}
inline void MDBond::add_sellsettltypequeue(::google::protobuf::int32 value) {
  sellsettltypequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
MDBond::sellsettltypequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return sellsettltypequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
MDBond::mutable_sellsettltypequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellSettlTypeQueue)
  return &sellsettltypequeue_;
}

// repeated int64 BuyYieldQueue = 64 [packed = true];
inline int MDBond::buyyieldqueue_size() const {
  return buyyieldqueue_.size();
}
inline void MDBond::clear_buyyieldqueue() {
  buyyieldqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::buyyieldqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return buyyieldqueue_.Get(index);
}
inline void MDBond::set_buyyieldqueue(int index, ::google::protobuf::int64 value) {
  buyyieldqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
}
inline void MDBond::add_buyyieldqueue(::google::protobuf::int64 value) {
  buyyieldqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::buyyieldqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return buyyieldqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_buyyieldqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.BuyYieldQueue)
  return &buyyieldqueue_;
}

// repeated int64 SellYieldQueue = 65 [packed = true];
inline int MDBond::sellyieldqueue_size() const {
  return sellyieldqueue_.size();
}
inline void MDBond::clear_sellyieldqueue() {
  sellyieldqueue_.Clear();
}
inline ::google::protobuf::int64 MDBond::sellyieldqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return sellyieldqueue_.Get(index);
}
inline void MDBond::set_sellyieldqueue(int index, ::google::protobuf::int64 value) {
  sellyieldqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
}
inline void MDBond::add_sellyieldqueue(::google::protobuf::int64 value) {
  sellyieldqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDBond::sellyieldqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return sellyieldqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDBond::mutable_sellyieldqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDBond.SellYieldQueue)
  return &sellyieldqueue_;
}

// optional int64 PreMarketLastPx = 66;
inline void MDBond::clear_premarketlastpx() {
  premarketlastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::premarketlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketLastPx)
  return premarketlastpx_;
}
inline void MDBond::set_premarketlastpx(::google::protobuf::int64 value) {
  
  premarketlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketLastPx)
}

// optional int64 PreMarketTotalVolumeTrade = 67;
inline void MDBond::clear_premarkettotalvolumetrade() {
  premarkettotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::premarkettotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketTotalVolumeTrade)
  return premarkettotalvolumetrade_;
}
inline void MDBond::set_premarkettotalvolumetrade(::google::protobuf::int64 value) {
  
  premarkettotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketTotalVolumeTrade)
}

// optional int64 PreMarketTotalValueTrade = 68;
inline void MDBond::clear_premarkettotalvaluetrade() {
  premarkettotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::premarkettotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketTotalValueTrade)
  return premarkettotalvaluetrade_;
}
inline void MDBond::set_premarkettotalvaluetrade(::google::protobuf::int64 value) {
  
  premarkettotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketTotalValueTrade)
}

// optional int64 PreMarketHighPx = 69;
inline void MDBond::clear_premarkethighpx() {
  premarkethighpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::premarkethighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketHighPx)
  return premarkethighpx_;
}
inline void MDBond::set_premarkethighpx(::google::protobuf::int64 value) {
  
  premarkethighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketHighPx)
}

// optional int64 PreMarketLowPx = 70;
inline void MDBond::clear_premarketlowpx() {
  premarketlowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::premarketlowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.PreMarketLowPx)
  return premarketlowpx_;
}
inline void MDBond::set_premarketlowpx(::google::protobuf::int64 value) {
  
  premarketlowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.PreMarketLowPx)
}

// optional int64 AfterHoursLastPx = 71;
inline void MDBond::clear_afterhourslastpx() {
  afterhourslastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::afterhourslastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursLastPx)
  return afterhourslastpx_;
}
inline void MDBond::set_afterhourslastpx(::google::protobuf::int64 value) {
  
  afterhourslastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursLastPx)
}

// optional int64 AfterHoursTotalVolumeTrade = 72;
inline void MDBond::clear_afterhourstotalvolumetrade() {
  afterhourstotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::afterhourstotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalVolumeTrade)
  return afterhourstotalvolumetrade_;
}
inline void MDBond::set_afterhourstotalvolumetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalVolumeTrade)
}

// optional int64 AfterHoursTotalValueTrade = 73;
inline void MDBond::clear_afterhourstotalvaluetrade() {
  afterhourstotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::afterhourstotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalValueTrade)
  return afterhourstotalvaluetrade_;
}
inline void MDBond::set_afterhourstotalvaluetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursTotalValueTrade)
}

// optional int64 AfterHoursHighPx = 74;
inline void MDBond::clear_afterhourshighpx() {
  afterhourshighpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::afterhourshighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursHighPx)
  return afterhourshighpx_;
}
inline void MDBond::set_afterhourshighpx(::google::protobuf::int64 value) {
  
  afterhourshighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursHighPx)
}

// optional int64 AfterHoursLowPx = 75;
inline void MDBond::clear_afterhourslowpx() {
  afterhourslowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::afterhourslowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AfterHoursLowPx)
  return afterhourslowpx_;
}
inline void MDBond::set_afterhourslowpx(::google::protobuf::int64 value) {
  
  afterhourslowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AfterHoursLowPx)
}

// optional string MarketPhaseCode = 76;
inline void MDBond::clear_marketphasecode() {
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  return marketphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_marketphasecode(const ::std::string& value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
inline void MDBond::set_marketphasecode(const char* value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
inline void MDBond::set_marketphasecode(const char* value, size_t size) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}
inline ::std::string* MDBond::mutable_marketphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  return marketphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_marketphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
  
  return marketphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_marketphasecode(::std::string* marketphasecode) {
  if (marketphasecode != NULL) {
    
  } else {
    
  }
  marketphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.MarketPhaseCode)
}

// optional string SubTradingPhaseCode1 = 77;
inline void MDBond::clear_subtradingphasecode1() {
  subtradingphasecode1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::subtradingphasecode1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  return subtradingphasecode1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_subtradingphasecode1(const ::std::string& value) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
inline void MDBond::set_subtradingphasecode1(const char* value) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
inline void MDBond::set_subtradingphasecode1(const char* value, size_t size) {
  
  subtradingphasecode1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}
inline ::std::string* MDBond::mutable_subtradingphasecode1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  return subtradingphasecode1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_subtradingphasecode1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
  
  return subtradingphasecode1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_subtradingphasecode1(::std::string* subtradingphasecode1) {
  if (subtradingphasecode1 != NULL) {
    
  } else {
    
  }
  subtradingphasecode1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode1)
}

// optional string SubTradingPhaseCode2 = 78;
inline void MDBond::clear_subtradingphasecode2() {
  subtradingphasecode2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::subtradingphasecode2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  return subtradingphasecode2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_subtradingphasecode2(const ::std::string& value) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
inline void MDBond::set_subtradingphasecode2(const char* value) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
inline void MDBond::set_subtradingphasecode2(const char* value, size_t size) {
  
  subtradingphasecode2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}
inline ::std::string* MDBond::mutable_subtradingphasecode2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  return subtradingphasecode2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_subtradingphasecode2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
  
  return subtradingphasecode2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_subtradingphasecode2(::std::string* subtradingphasecode2) {
  if (subtradingphasecode2 != NULL) {
    
  } else {
    
  }
  subtradingphasecode2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode2)
}

// optional string SubTradingPhaseCode3 = 79;
inline void MDBond::clear_subtradingphasecode3() {
  subtradingphasecode3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::subtradingphasecode3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  return subtradingphasecode3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_subtradingphasecode3(const ::std::string& value) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
inline void MDBond::set_subtradingphasecode3(const char* value) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
inline void MDBond::set_subtradingphasecode3(const char* value, size_t size) {
  
  subtradingphasecode3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}
inline ::std::string* MDBond::mutable_subtradingphasecode3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  return subtradingphasecode3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_subtradingphasecode3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
  
  return subtradingphasecode3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_subtradingphasecode3(::std::string* subtradingphasecode3) {
  if (subtradingphasecode3 != NULL) {
    
  } else {
    
  }
  subtradingphasecode3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode3)
}

// optional string SubTradingPhaseCode4 = 80;
inline void MDBond::clear_subtradingphasecode4() {
  subtradingphasecode4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::subtradingphasecode4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  return subtradingphasecode4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_subtradingphasecode4(const ::std::string& value) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
inline void MDBond::set_subtradingphasecode4(const char* value) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
inline void MDBond::set_subtradingphasecode4(const char* value, size_t size) {
  
  subtradingphasecode4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}
inline ::std::string* MDBond::mutable_subtradingphasecode4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  return subtradingphasecode4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_subtradingphasecode4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
  
  return subtradingphasecode4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_subtradingphasecode4(::std::string* subtradingphasecode4) {
  if (subtradingphasecode4 != NULL) {
    
  } else {
    
  }
  subtradingphasecode4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode4)
}

// optional string SubTradingPhaseCode5 = 81;
inline void MDBond::clear_subtradingphasecode5() {
  subtradingphasecode5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::subtradingphasecode5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  return subtradingphasecode5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_subtradingphasecode5(const ::std::string& value) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
inline void MDBond::set_subtradingphasecode5(const char* value) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
inline void MDBond::set_subtradingphasecode5(const char* value, size_t size) {
  
  subtradingphasecode5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}
inline ::std::string* MDBond::mutable_subtradingphasecode5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  return subtradingphasecode5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_subtradingphasecode5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
  
  return subtradingphasecode5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_subtradingphasecode5(::std::string* subtradingphasecode5) {
  if (subtradingphasecode5 != NULL) {
    
  } else {
    
  }
  subtradingphasecode5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), subtradingphasecode5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.SubTradingPhaseCode5)
}

// optional int32 LastPxType = 82;
inline void MDBond::clear_lastpxtype() {
  lastpxtype_ = 0;
}
inline ::google::protobuf::int32 MDBond::lastpxtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.LastPxType)
  return lastpxtype_;
}
inline void MDBond::set_lastpxtype(::google::protobuf::int32 value) {
  
  lastpxtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.LastPxType)
}

// optional int64 AuctionLastPx = 83;
inline void MDBond::clear_auctionlastpx() {
  auctionlastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::auctionlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionLastPx)
  return auctionlastpx_;
}
inline void MDBond::set_auctionlastpx(::google::protobuf::int64 value) {
  
  auctionlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionLastPx)
}

// optional int64 AuctionVolumeTrade = 84;
inline void MDBond::clear_auctionvolumetrade() {
  auctionvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::auctionvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionVolumeTrade)
  return auctionvolumetrade_;
}
inline void MDBond::set_auctionvolumetrade(::google::protobuf::int64 value) {
  
  auctionvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionVolumeTrade)
}

// optional int64 AuctionValueTrade = 85;
inline void MDBond::clear_auctionvaluetrade() {
  auctionvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::auctionvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.AuctionValueTrade)
  return auctionvaluetrade_;
}
inline void MDBond::set_auctionvaluetrade(::google::protobuf::int64 value) {
  
  auctionvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.AuctionValueTrade)
}

// optional int64 USConsolidateVolume = 86;
inline void MDBond::clear_usconsolidatevolume() {
  usconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::usconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.USConsolidateVolume)
  return usconsolidatevolume_;
}
inline void MDBond::set_usconsolidatevolume(::google::protobuf::int64 value) {
  
  usconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.USConsolidateVolume)
}

// optional int64 USCompositeClosePx = 87;
inline void MDBond::clear_uscompositeclosepx() {
  uscompositeclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::uscompositeclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.USCompositeClosePx)
  return uscompositeclosepx_;
}
inline void MDBond::set_uscompositeclosepx(::google::protobuf::int64 value) {
  
  uscompositeclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.USCompositeClosePx)
}

// optional string TradingHaltReason = 88;
inline void MDBond::clear_tradinghaltreason() {
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDBond::tradinghaltreason() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  return tradinghaltreason_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_tradinghaltreason(const ::std::string& value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
inline void MDBond::set_tradinghaltreason(const char* value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
inline void MDBond::set_tradinghaltreason(const char* value, size_t size) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}
inline ::std::string* MDBond::mutable_tradinghaltreason() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  return tradinghaltreason_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDBond::release_tradinghaltreason() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
  
  return tradinghaltreason_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDBond::set_allocated_tradinghaltreason(::std::string* tradinghaltreason) {
  if (tradinghaltreason != NULL) {
    
  } else {
    
  }
  tradinghaltreason_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradinghaltreason);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDBond.TradingHaltReason)
}

// optional int64 OtcTotalVolumeTrade = 89;
inline void MDBond::clear_otctotalvolumetrade() {
  otctotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::otctotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcTotalVolumeTrade)
  return otctotalvolumetrade_;
}
inline void MDBond::set_otctotalvolumetrade(::google::protobuf::int64 value) {
  
  otctotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcTotalVolumeTrade)
}

// optional int64 OtcTotalValueTrade = 90;
inline void MDBond::clear_otctotalvaluetrade() {
  otctotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::otctotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcTotalValueTrade)
  return otctotalvaluetrade_;
}
inline void MDBond::set_otctotalvaluetrade(::google::protobuf::int64 value) {
  
  otctotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcTotalValueTrade)
}

// optional int64 OtcNumTrades = 91;
inline void MDBond::clear_otcnumtrades() {
  otcnumtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::otcnumtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.OtcNumTrades)
  return otcnumtrades_;
}
inline void MDBond::set_otcnumtrades(::google::protobuf::int64 value) {
  
  otcnumtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.OtcNumTrades)
}

// optional int32 DataMultiplePowerOf10 = 92;
inline void MDBond::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDBond::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDBond::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DataMultiplePowerOf10)
}

// optional int64 ReferencePx = 93;
inline void MDBond::clear_referencepx() {
  referencepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::referencepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.ReferencePx)
  return referencepx_;
}
inline void MDBond::set_referencepx(::google::protobuf::int64 value) {
  
  referencepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.ReferencePx)
}

// optional int64 MaxBuyPrice = 94;
inline void MDBond::clear_maxbuyprice() {
  maxbuyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::maxbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxBuyPrice)
  return maxbuyprice_;
}
inline void MDBond::set_maxbuyprice(::google::protobuf::int64 value) {
  
  maxbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxBuyPrice)
}

// optional int64 MinBuyPrice = 95;
inline void MDBond::clear_minbuyprice() {
  minbuyprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::minbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinBuyPrice)
  return minbuyprice_;
}
inline void MDBond::set_minbuyprice(::google::protobuf::int64 value) {
  
  minbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinBuyPrice)
}

// optional int64 MaxSellPrice = 96;
inline void MDBond::clear_maxsellprice() {
  maxsellprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::maxsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MaxSellPrice)
  return maxsellprice_;
}
inline void MDBond::set_maxsellprice(::google::protobuf::int64 value) {
  
  maxsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MaxSellPrice)
}

// optional int64 MinSellPrice = 97;
inline void MDBond::clear_minsellprice() {
  minsellprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDBond::minsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.MinSellPrice)
  return minsellprice_;
}
inline void MDBond::set_minsellprice(::google::protobuf::int64 value) {
  
  minsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.MinSellPrice)
}

// optional int32 DelayType = 101;
inline void MDBond::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDBond::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDBond.DelayType)
  return delaytype_;
}
inline void MDBond::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDBond.DelayType)
}

inline const MDBond* MDBond::internal_default_instance() {
  return &MDBond_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDBond_2eproto__INCLUDED
