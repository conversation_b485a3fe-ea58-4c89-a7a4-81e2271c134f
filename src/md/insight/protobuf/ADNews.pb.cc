// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADNews.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "ADNews.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* ADNews_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADNews_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADNewsEmotion_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADNewsEmotion_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADInvolvedSecurity_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADInvolvedSecurity_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADRelatedCompany_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADRelatedCompany_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADRelatedPerson_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADRelatedPerson_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADNewsDataEntry_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADNewsDataEntry_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_ADNews_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_ADNews_2eproto() {
  protobuf_AddDesc_ADNews_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "ADNews.proto");
  GOOGLE_CHECK(file != NULL);
  ADNews_descriptor_ = file->message_type(0);
  static const int ADNews_offsets_[25] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, author_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, content_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, eventcat_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, eventname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, eventpos_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, id_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, industry_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, newshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, newstime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, summ_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, title_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, url_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, newsemotion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, seculist_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, peremlist_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, comemlist_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, sentimentcontent_),
  };
  ADNews_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADNews_descriptor_,
      ADNews::internal_default_instance(),
      ADNews_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADNews),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNews, _internal_metadata_));
  ADNewsEmotion_descriptor_ = file->message_type(1);
  static const int ADNewsEmotion_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsEmotion, negative_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsEmotion, neutral_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsEmotion, pos_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsEmotion, positive_),
  };
  ADNewsEmotion_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADNewsEmotion_descriptor_,
      ADNewsEmotion::internal_default_instance(),
      ADNewsEmotion_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADNewsEmotion),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsEmotion, _internal_metadata_));
  ADInvolvedSecurity_descriptor_ = file->message_type(2);
  static const int ADInvolvedSecurity_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADInvolvedSecurity, comcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADInvolvedSecurity, secucode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADInvolvedSecurity, secuname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADInvolvedSecurity, htscsecurityid_),
  };
  ADInvolvedSecurity_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADInvolvedSecurity_descriptor_,
      ADInvolvedSecurity::internal_default_instance(),
      ADInvolvedSecurity_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADInvolvedSecurity),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADInvolvedSecurity, _internal_metadata_));
  ADRelatedCompany_descriptor_ = file->message_type(3);
  static const int ADRelatedCompany_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, chiname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, comcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, com_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, generalpos_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, secucode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, htscsecurityid_),
  };
  ADRelatedCompany_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADRelatedCompany_descriptor_,
      ADRelatedCompany::internal_default_instance(),
      ADRelatedCompany_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADRelatedCompany),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedCompany, _internal_metadata_));
  ADRelatedPerson_descriptor_ = file->message_type(4);
  static const int ADRelatedPerson_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedPerson, per_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedPerson, comcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedPerson, com_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedPerson, generalpos_),
  };
  ADRelatedPerson_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADRelatedPerson_descriptor_,
      ADRelatedPerson::internal_default_instance(),
      ADRelatedPerson_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADRelatedPerson),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADRelatedPerson, _internal_metadata_));
  ADNewsDataEntry_descriptor_ = file->message_type(5);
  static const int ADNewsDataEntry_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsDataEntry, key_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsDataEntry, value_),
  };
  ADNewsDataEntry_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADNewsDataEntry_descriptor_,
      ADNewsDataEntry::internal_default_instance(),
      ADNewsDataEntry_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADNewsDataEntry),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADNewsDataEntry, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_ADNews_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADNews_descriptor_, ADNews::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADNewsEmotion_descriptor_, ADNewsEmotion::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADInvolvedSecurity_descriptor_, ADInvolvedSecurity::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADRelatedCompany_descriptor_, ADRelatedCompany::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADRelatedPerson_descriptor_, ADRelatedPerson::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADNewsDataEntry_descriptor_, ADNewsDataEntry::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_ADNews_2eproto() {
  ADNews_default_instance_.Shutdown();
  delete ADNews_reflection_;
  ADNewsEmotion_default_instance_.Shutdown();
  delete ADNewsEmotion_reflection_;
  ADInvolvedSecurity_default_instance_.Shutdown();
  delete ADInvolvedSecurity_reflection_;
  ADRelatedCompany_default_instance_.Shutdown();
  delete ADRelatedCompany_reflection_;
  ADRelatedPerson_default_instance_.Shutdown();
  delete ADRelatedPerson_reflection_;
  ADNewsDataEntry_default_instance_.Shutdown();
  delete ADNewsDataEntry_reflection_;
}

void protobuf_InitDefaults_ADNews_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  ADNews_default_instance_.DefaultConstruct();
  ADNewsEmotion_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADInvolvedSecurity_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADRelatedCompany_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADRelatedPerson_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ADNewsDataEntry_default_instance_.DefaultConstruct();
  ADNews_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADNewsEmotion_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADInvolvedSecurity_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADRelatedCompany_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADRelatedPerson_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADNewsDataEntry_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_ADNews_2eproto_once_);
void protobuf_InitDefaults_ADNews_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_ADNews_2eproto_once_,
                 &protobuf_InitDefaults_ADNews_2eproto_impl);
}
void protobuf_AddDesc_ADNews_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_ADNews_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\014ADNews.proto\022\032com.htsc.mdc.insight.mod"
    "el\032\023ESecurityType.proto\032\027ESecurityIDSour"
    "ce.proto\"\227\006\n\006ADNews\022\026\n\016HTSCSecurityID\030\001 "
    "\001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rD"
    "ataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode\030\005"
    " \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.hts"
    "c.mdc.model.ESecurityIDSource\0227\n\014securit"
    "yType\030\007 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\016\n\006author\030\010 \001(\t\022\017\n\007content\030\t \001(\t\022"
    "\020\n\010eventCat\030\n \001(\t\022\021\n\teventName\030\013 \001(\t\022\020\n\010"
    "eventPos\030\014 \001(\005\022\n\n\002id\030\r \001(\t\022\020\n\010industry\030\016"
    " \001(\t\022\017\n\007newsHot\030\017 \001(\001\022\020\n\010newsTime\030\020 \001(\t\022"
    "\014\n\004summ\030\021 \001(\t\022\r\n\005title\030\022 \001(\t\022\013\n\003url\030\023 \001("
    "\t\022>\n\013newsEmotion\030\024 \001(\0132).com.htsc.mdc.in"
    "sight.model.ADNewsEmotion\022@\n\010secuList\030\025 "
    "\003(\0132..com.htsc.mdc.insight.model.ADInvol"
    "vedSecurity\022>\n\tperEmList\030\026 \003(\0132+.com.hts"
    "c.mdc.insight.model.ADRelatedPerson\022\?\n\tc"
    "omEmList\030\027 \003(\0132,.com.htsc.mdc.insight.mo"
    "del.ADRelatedCompany\022\035\n\025DataMultiplePowe"
    "rOf10\030\030 \001(\005\022E\n\020sentimentContent\030\031 \003(\0132+."
    "com.htsc.mdc.insight.model.ADNewsDataEnt"
    "ry\"Q\n\rADNewsEmotion\022\020\n\010negative\030\001 \001(\001\022\017\n"
    "\007neutral\030\002 \001(\001\022\013\n\003pos\030\003 \001(\005\022\020\n\010positive\030"
    "\004 \001(\001\"a\n\022ADInvolvedSecurity\022\017\n\007comCode\030\001"
    " \001(\t\022\020\n\010secuCode\030\002 \001(\t\022\020\n\010secuName\030\003 \001(\t"
    "\022\026\n\016HTSCSecurityID\030\004 \001(\t\"\177\n\020ADRelatedCom"
    "pany\022\017\n\007chiname\030\001 \001(\t\022\017\n\007comCode\030\002 \001(\t\022\013"
    "\n\003com\030\003 \001(\t\022\022\n\ngeneralPos\030\004 \001(\005\022\020\n\010secuC"
    "ode\030\005 \001(\t\022\026\n\016HTSCSecurityID\030\006 \001(\t\"P\n\017ADR"
    "elatedPerson\022\013\n\003per\030\001 \001(\t\022\017\n\007comCode\030\002 \001"
    "(\t\022\013\n\003com\030\003 \001(\t\022\022\n\ngeneralPos\030\004 \001(\005\"-\n\017A"
    "DNewsDataEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001"
    "(\tB/\n\032com.htsc.mdc.insight.modelB\014ADNews"
    "ProtosH\001\240\001\001b\006proto3", 1379);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "ADNews.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_ADNews_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_ADNews_2eproto_once_);
void protobuf_AddDesc_ADNews_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_ADNews_2eproto_once_,
                 &protobuf_AddDesc_ADNews_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_ADNews_2eproto {
  StaticDescriptorInitializer_ADNews_2eproto() {
    protobuf_AddDesc_ADNews_2eproto();
  }
} static_descriptor_initializer_ADNews_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADNews::kHTSCSecurityIDFieldNumber;
const int ADNews::kMDDateFieldNumber;
const int ADNews::kMDTimeFieldNumber;
const int ADNews::kDataTimestampFieldNumber;
const int ADNews::kTradingPhaseCodeFieldNumber;
const int ADNews::kSecurityIDSourceFieldNumber;
const int ADNews::kSecurityTypeFieldNumber;
const int ADNews::kAuthorFieldNumber;
const int ADNews::kContentFieldNumber;
const int ADNews::kEventCatFieldNumber;
const int ADNews::kEventNameFieldNumber;
const int ADNews::kEventPosFieldNumber;
const int ADNews::kIdFieldNumber;
const int ADNews::kIndustryFieldNumber;
const int ADNews::kNewsHotFieldNumber;
const int ADNews::kNewsTimeFieldNumber;
const int ADNews::kSummFieldNumber;
const int ADNews::kTitleFieldNumber;
const int ADNews::kUrlFieldNumber;
const int ADNews::kNewsEmotionFieldNumber;
const int ADNews::kSecuListFieldNumber;
const int ADNews::kPerEmListFieldNumber;
const int ADNews::kComEmListFieldNumber;
const int ADNews::kDataMultiplePowerOf10FieldNumber;
const int ADNews::kSentimentContentFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADNews::ADNews()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADNews)
}

void ADNews::InitAsDefaultInstance() {
  newsemotion_ = const_cast< ::com::htsc::mdc::insight::model::ADNewsEmotion*>(
      ::com::htsc::mdc::insight::model::ADNewsEmotion::internal_default_instance());
}

ADNews::ADNews(const ADNews& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADNews)
}

void ADNews::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  author_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventcat_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  industry_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  newstime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summ_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  title_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  url_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  newsemotion_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

ADNews::~ADNews() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADNews)
  SharedDtor();
}

void ADNews::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  author_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  content_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventcat_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  industry_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  newstime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summ_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  title_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  url_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &ADNews_default_instance_.get()) {
    delete newsemotion_;
  }
}

void ADNews::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADNews::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADNews_descriptor_;
}

const ADNews& ADNews::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADNews> ADNews_default_instance_;

ADNews* ADNews::New(::google::protobuf::Arena* arena) const {
  ADNews* n = new ADNews;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADNews::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADNews)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADNews, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADNews*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, securitytype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  author_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(newshot_, eventpos_);
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventcat_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  eventname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  industry_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  newstime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  summ_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  title_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  url_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && newsemotion_ != NULL) delete newsemotion_;
  newsemotion_ = NULL;
  datamultiplepowerof10_ = 0;

#undef ZR_HELPER_
#undef ZR_

  seculist_.Clear();
  peremlist_.Clear();
  comemlist_.Clear();
  sentimentcontent_.Clear();
}

bool ADNews::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADNews)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_author;
        break;
      }

      // optional string author = 8;
      case 8: {
        if (tag == 66) {
         parse_author:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_author()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->author().data(), this->author().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.author"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_content;
        break;
      }

      // optional string content = 9;
      case 9: {
        if (tag == 74) {
         parse_content:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_content()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->content().data(), this->content().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.content"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_eventCat;
        break;
      }

      // optional string eventCat = 10;
      case 10: {
        if (tag == 82) {
         parse_eventCat:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_eventcat()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->eventcat().data(), this->eventcat().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.eventCat"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_eventName;
        break;
      }

      // optional string eventName = 11;
      case 11: {
        if (tag == 90) {
         parse_eventName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_eventname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->eventname().data(), this->eventname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.eventName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_eventPos;
        break;
      }

      // optional int32 eventPos = 12;
      case 12: {
        if (tag == 96) {
         parse_eventPos:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &eventpos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_id;
        break;
      }

      // optional string id = 13;
      case 13: {
        if (tag == 106) {
         parse_id:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_id()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->id().data(), this->id().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.id"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_industry;
        break;
      }

      // optional string industry = 14;
      case 14: {
        if (tag == 114) {
         parse_industry:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_industry()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->industry().data(), this->industry().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.industry"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_newsHot;
        break;
      }

      // optional double newsHot = 15;
      case 15: {
        if (tag == 121) {
         parse_newsHot:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &newshot_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_newsTime;
        break;
      }

      // optional string newsTime = 16;
      case 16: {
        if (tag == 130) {
         parse_newsTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_newstime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->newstime().data(), this->newstime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.newsTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_summ;
        break;
      }

      // optional string summ = 17;
      case 17: {
        if (tag == 138) {
         parse_summ:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_summ()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->summ().data(), this->summ().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.summ"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_title;
        break;
      }

      // optional string title = 18;
      case 18: {
        if (tag == 146) {
         parse_title:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_title()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->title().data(), this->title().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.title"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_url;
        break;
      }

      // optional string url = 19;
      case 19: {
        if (tag == 154) {
         parse_url:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_url()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->url().data(), this->url().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNews.url"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_newsEmotion;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADNewsEmotion newsEmotion = 20;
      case 20: {
        if (tag == 162) {
         parse_newsEmotion:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_newsemotion()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_secuList;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADInvolvedSecurity secuList = 21;
      case 21: {
        if (tag == 170) {
         parse_secuList:
          DO_(input->IncrementRecursionDepth());
         parse_loop_secuList:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_seculist()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_loop_secuList;
        if (input->ExpectTag(178)) goto parse_loop_perEmList;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADRelatedPerson perEmList = 22;
      case 22: {
        if (tag == 178) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_perEmList:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_peremlist()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_loop_perEmList;
        if (input->ExpectTag(186)) goto parse_loop_comEmList;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADRelatedCompany comEmList = 23;
      case 23: {
        if (tag == 186) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_comEmList:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_comemlist()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_loop_comEmList;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(192)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 24;
      case 24: {
        if (tag == 192) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_sentimentContent;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADNewsDataEntry sentimentContent = 25;
      case 25: {
        if (tag == 202) {
         parse_sentimentContent:
          DO_(input->IncrementRecursionDepth());
         parse_loop_sentimentContent:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_sentimentcontent()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_loop_sentimentContent;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADNews)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADNews)
  return false;
#undef DO_
}

void ADNews::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADNews)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional string author = 8;
  if (this->author().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->author().data(), this->author().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.author");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->author(), output);
  }

  // optional string content = 9;
  if (this->content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content().data(), this->content().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.content");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->content(), output);
  }

  // optional string eventCat = 10;
  if (this->eventcat().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->eventcat().data(), this->eventcat().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.eventCat");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->eventcat(), output);
  }

  // optional string eventName = 11;
  if (this->eventname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->eventname().data(), this->eventname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.eventName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->eventname(), output);
  }

  // optional int32 eventPos = 12;
  if (this->eventpos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->eventpos(), output);
  }

  // optional string id = 13;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.id");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->id(), output);
  }

  // optional string industry = 14;
  if (this->industry().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->industry().data(), this->industry().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.industry");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->industry(), output);
  }

  // optional double newsHot = 15;
  if (this->newshot() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->newshot(), output);
  }

  // optional string newsTime = 16;
  if (this->newstime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->newstime().data(), this->newstime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.newsTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->newstime(), output);
  }

  // optional string summ = 17;
  if (this->summ().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summ().data(), this->summ().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.summ");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->summ(), output);
  }

  // optional string title = 18;
  if (this->title().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->title().data(), this->title().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.title");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->title(), output);
  }

  // optional string url = 19;
  if (this->url().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->url().data(), this->url().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.url");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->url(), output);
  }

  // optional .com.htsc.mdc.insight.model.ADNewsEmotion newsEmotion = 20;
  if (this->has_newsemotion()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->newsemotion_, output);
  }

  // repeated .com.htsc.mdc.insight.model.ADInvolvedSecurity secuList = 21;
  for (unsigned int i = 0, n = this->seculist_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, this->seculist(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedPerson perEmList = 22;
  for (unsigned int i = 0, n = this->peremlist_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, this->peremlist(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedCompany comEmList = 23;
  for (unsigned int i = 0, n = this->comemlist_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, this->comemlist(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 24;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(24, this->datamultiplepowerof10(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADNewsDataEntry sentimentContent = 25;
  for (unsigned int i = 0, n = this->sentimentcontent_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      25, this->sentimentcontent(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADNews)
}

::google::protobuf::uint8* ADNews::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADNews)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional string author = 8;
  if (this->author().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->author().data(), this->author().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.author");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->author(), target);
  }

  // optional string content = 9;
  if (this->content().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->content().data(), this->content().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.content");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->content(), target);
  }

  // optional string eventCat = 10;
  if (this->eventcat().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->eventcat().data(), this->eventcat().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.eventCat");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->eventcat(), target);
  }

  // optional string eventName = 11;
  if (this->eventname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->eventname().data(), this->eventname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.eventName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->eventname(), target);
  }

  // optional int32 eventPos = 12;
  if (this->eventpos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->eventpos(), target);
  }

  // optional string id = 13;
  if (this->id().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->id().data(), this->id().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.id");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->id(), target);
  }

  // optional string industry = 14;
  if (this->industry().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->industry().data(), this->industry().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.industry");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->industry(), target);
  }

  // optional double newsHot = 15;
  if (this->newshot() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->newshot(), target);
  }

  // optional string newsTime = 16;
  if (this->newstime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->newstime().data(), this->newstime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.newsTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->newstime(), target);
  }

  // optional string summ = 17;
  if (this->summ().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->summ().data(), this->summ().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.summ");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->summ(), target);
  }

  // optional string title = 18;
  if (this->title().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->title().data(), this->title().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.title");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->title(), target);
  }

  // optional string url = 19;
  if (this->url().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->url().data(), this->url().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNews.url");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->url(), target);
  }

  // optional .com.htsc.mdc.insight.model.ADNewsEmotion newsEmotion = 20;
  if (this->has_newsemotion()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->newsemotion_, false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADInvolvedSecurity secuList = 21;
  for (unsigned int i = 0, n = this->seculist_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, this->seculist(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedPerson perEmList = 22;
  for (unsigned int i = 0, n = this->peremlist_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, this->peremlist(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedCompany comEmList = 23;
  for (unsigned int i = 0, n = this->comemlist_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, this->comemlist(i), false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 24;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(24, this->datamultiplepowerof10(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADNewsDataEntry sentimentContent = 25;
  for (unsigned int i = 0, n = this->sentimentcontent_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        25, this->sentimentcontent(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADNews)
  return target;
}

size_t ADNews::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADNews)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string author = 8;
  if (this->author().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->author());
  }

  // optional string content = 9;
  if (this->content().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->content());
  }

  // optional string eventCat = 10;
  if (this->eventcat().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->eventcat());
  }

  // optional string eventName = 11;
  if (this->eventname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->eventname());
  }

  // optional int32 eventPos = 12;
  if (this->eventpos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->eventpos());
  }

  // optional string id = 13;
  if (this->id().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->id());
  }

  // optional string industry = 14;
  if (this->industry().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->industry());
  }

  // optional double newsHot = 15;
  if (this->newshot() != 0) {
    total_size += 1 + 8;
  }

  // optional string newsTime = 16;
  if (this->newstime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->newstime());
  }

  // optional string summ = 17;
  if (this->summ().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->summ());
  }

  // optional string title = 18;
  if (this->title().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->title());
  }

  // optional string url = 19;
  if (this->url().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->url());
  }

  // optional .com.htsc.mdc.insight.model.ADNewsEmotion newsEmotion = 20;
  if (this->has_newsemotion()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->newsemotion_);
  }

  // optional int32 DataMultiplePowerOf10 = 24;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.ADInvolvedSecurity secuList = 21;
  {
    unsigned int count = this->seculist_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->seculist(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedPerson perEmList = 22;
  {
    unsigned int count = this->peremlist_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->peremlist(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADRelatedCompany comEmList = 23;
  {
    unsigned int count = this->comemlist_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->comemlist(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.ADNewsDataEntry sentimentContent = 25;
  {
    unsigned int count = this->sentimentcontent_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->sentimentcontent(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADNews::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADNews)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADNews* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADNews>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADNews)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADNews)
    UnsafeMergeFrom(*source);
  }
}

void ADNews::MergeFrom(const ADNews& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADNews)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADNews::UnsafeMergeFrom(const ADNews& from) {
  GOOGLE_DCHECK(&from != this);
  seculist_.MergeFrom(from.seculist_);
  peremlist_.MergeFrom(from.peremlist_);
  comemlist_.MergeFrom(from.comemlist_);
  sentimentcontent_.MergeFrom(from.sentimentcontent_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.author().size() > 0) {

    author_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.author_);
  }
  if (from.content().size() > 0) {

    content_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.content_);
  }
  if (from.eventcat().size() > 0) {

    eventcat_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.eventcat_);
  }
  if (from.eventname().size() > 0) {

    eventname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.eventname_);
  }
  if (from.eventpos() != 0) {
    set_eventpos(from.eventpos());
  }
  if (from.id().size() > 0) {

    id_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.id_);
  }
  if (from.industry().size() > 0) {

    industry_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.industry_);
  }
  if (from.newshot() != 0) {
    set_newshot(from.newshot());
  }
  if (from.newstime().size() > 0) {

    newstime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.newstime_);
  }
  if (from.summ().size() > 0) {

    summ_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.summ_);
  }
  if (from.title().size() > 0) {

    title_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.title_);
  }
  if (from.url().size() > 0) {

    url_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.url_);
  }
  if (from.has_newsemotion()) {
    mutable_newsemotion()->::com::htsc::mdc::insight::model::ADNewsEmotion::MergeFrom(from.newsemotion());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void ADNews::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADNews)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADNews::CopyFrom(const ADNews& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADNews)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADNews::IsInitialized() const {

  return true;
}

void ADNews::Swap(ADNews* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADNews::InternalSwap(ADNews* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  author_.Swap(&other->author_);
  content_.Swap(&other->content_);
  eventcat_.Swap(&other->eventcat_);
  eventname_.Swap(&other->eventname_);
  std::swap(eventpos_, other->eventpos_);
  id_.Swap(&other->id_);
  industry_.Swap(&other->industry_);
  std::swap(newshot_, other->newshot_);
  newstime_.Swap(&other->newstime_);
  summ_.Swap(&other->summ_);
  title_.Swap(&other->title_);
  url_.Swap(&other->url_);
  std::swap(newsemotion_, other->newsemotion_);
  seculist_.UnsafeArenaSwap(&other->seculist_);
  peremlist_.UnsafeArenaSwap(&other->peremlist_);
  comemlist_.UnsafeArenaSwap(&other->comemlist_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  sentimentcontent_.UnsafeArenaSwap(&other->sentimentcontent_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADNews::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADNews_descriptor_;
  metadata.reflection = ADNews_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADNews

// optional string HTSCSecurityID = 1;
void ADNews::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
}
void ADNews::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
}
void ADNews::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
}
::std::string* ADNews::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void ADNews::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 ADNews::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.MDDate)
  return mddate_;
}
void ADNews::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.MDDate)
}

// optional int32 MDTime = 3;
void ADNews::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 ADNews::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.MDTime)
  return mdtime_;
}
void ADNews::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.MDTime)
}

// optional int64 DataTimestamp = 4;
void ADNews::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADNews::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.DataTimestamp)
  return datatimestamp_;
}
void ADNews::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void ADNews::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
}
void ADNews::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
}
void ADNews::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
}
::std::string* ADNews::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void ADNews::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource ADNews::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void ADNews::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void ADNews::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType ADNews::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void ADNews::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.securityType)
}

// optional string author = 8;
void ADNews::clear_author() {
  author_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::author() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.author)
  return author_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_author(const ::std::string& value) {
  
  author_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.author)
}
void ADNews::set_author(const char* value) {
  
  author_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.author)
}
void ADNews::set_author(const char* value, size_t size) {
  
  author_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.author)
}
::std::string* ADNews::mutable_author() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.author)
  return author_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_author() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.author)
  
  return author_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_author(::std::string* author) {
  if (author != NULL) {
    
  } else {
    
  }
  author_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), author);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.author)
}

// optional string content = 9;
void ADNews::clear_content() {
  content_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::content() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.content)
  return content_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_content(const ::std::string& value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.content)
}
void ADNews::set_content(const char* value) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.content)
}
void ADNews::set_content(const char* value, size_t size) {
  
  content_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.content)
}
::std::string* ADNews::mutable_content() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.content)
  return content_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_content() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.content)
  
  return content_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_content(::std::string* content) {
  if (content != NULL) {
    
  } else {
    
  }
  content_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), content);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.content)
}

// optional string eventCat = 10;
void ADNews::clear_eventcat() {
  eventcat_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::eventcat() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.eventCat)
  return eventcat_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_eventcat(const ::std::string& value) {
  
  eventcat_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.eventCat)
}
void ADNews::set_eventcat(const char* value) {
  
  eventcat_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.eventCat)
}
void ADNews::set_eventcat(const char* value, size_t size) {
  
  eventcat_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.eventCat)
}
::std::string* ADNews::mutable_eventcat() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.eventCat)
  return eventcat_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_eventcat() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.eventCat)
  
  return eventcat_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_eventcat(::std::string* eventcat) {
  if (eventcat != NULL) {
    
  } else {
    
  }
  eventcat_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), eventcat);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.eventCat)
}

// optional string eventName = 11;
void ADNews::clear_eventname() {
  eventname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::eventname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.eventName)
  return eventname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_eventname(const ::std::string& value) {
  
  eventname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.eventName)
}
void ADNews::set_eventname(const char* value) {
  
  eventname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.eventName)
}
void ADNews::set_eventname(const char* value, size_t size) {
  
  eventname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.eventName)
}
::std::string* ADNews::mutable_eventname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.eventName)
  return eventname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_eventname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.eventName)
  
  return eventname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_eventname(::std::string* eventname) {
  if (eventname != NULL) {
    
  } else {
    
  }
  eventname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), eventname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.eventName)
}

// optional int32 eventPos = 12;
void ADNews::clear_eventpos() {
  eventpos_ = 0;
}
::google::protobuf::int32 ADNews::eventpos() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.eventPos)
  return eventpos_;
}
void ADNews::set_eventpos(::google::protobuf::int32 value) {
  
  eventpos_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.eventPos)
}

// optional string id = 13;
void ADNews::clear_id() {
  id_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::id() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.id)
  return id_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_id(const ::std::string& value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.id)
}
void ADNews::set_id(const char* value) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.id)
}
void ADNews::set_id(const char* value, size_t size) {
  
  id_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.id)
}
::std::string* ADNews::mutable_id() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.id)
  return id_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_id() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.id)
  
  return id_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_id(::std::string* id) {
  if (id != NULL) {
    
  } else {
    
  }
  id_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), id);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.id)
}

// optional string industry = 14;
void ADNews::clear_industry() {
  industry_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::industry() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.industry)
  return industry_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_industry(const ::std::string& value) {
  
  industry_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.industry)
}
void ADNews::set_industry(const char* value) {
  
  industry_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.industry)
}
void ADNews::set_industry(const char* value, size_t size) {
  
  industry_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.industry)
}
::std::string* ADNews::mutable_industry() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.industry)
  return industry_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_industry() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.industry)
  
  return industry_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_industry(::std::string* industry) {
  if (industry != NULL) {
    
  } else {
    
  }
  industry_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), industry);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.industry)
}

// optional double newsHot = 15;
void ADNews::clear_newshot() {
  newshot_ = 0;
}
double ADNews::newshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.newsHot)
  return newshot_;
}
void ADNews::set_newshot(double value) {
  
  newshot_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.newsHot)
}

// optional string newsTime = 16;
void ADNews::clear_newstime() {
  newstime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::newstime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.newsTime)
  return newstime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_newstime(const ::std::string& value) {
  
  newstime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.newsTime)
}
void ADNews::set_newstime(const char* value) {
  
  newstime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.newsTime)
}
void ADNews::set_newstime(const char* value, size_t size) {
  
  newstime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.newsTime)
}
::std::string* ADNews::mutable_newstime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.newsTime)
  return newstime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_newstime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.newsTime)
  
  return newstime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_newstime(::std::string* newstime) {
  if (newstime != NULL) {
    
  } else {
    
  }
  newstime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), newstime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.newsTime)
}

// optional string summ = 17;
void ADNews::clear_summ() {
  summ_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::summ() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.summ)
  return summ_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_summ(const ::std::string& value) {
  
  summ_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.summ)
}
void ADNews::set_summ(const char* value) {
  
  summ_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.summ)
}
void ADNews::set_summ(const char* value, size_t size) {
  
  summ_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.summ)
}
::std::string* ADNews::mutable_summ() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.summ)
  return summ_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_summ() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.summ)
  
  return summ_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_summ(::std::string* summ) {
  if (summ != NULL) {
    
  } else {
    
  }
  summ_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), summ);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.summ)
}

// optional string title = 18;
void ADNews::clear_title() {
  title_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::title() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.title)
  return title_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_title(const ::std::string& value) {
  
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.title)
}
void ADNews::set_title(const char* value) {
  
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.title)
}
void ADNews::set_title(const char* value, size_t size) {
  
  title_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.title)
}
::std::string* ADNews::mutable_title() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.title)
  return title_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_title() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.title)
  
  return title_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_title(::std::string* title) {
  if (title != NULL) {
    
  } else {
    
  }
  title_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), title);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.title)
}

// optional string url = 19;
void ADNews::clear_url() {
  url_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNews::url() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.url)
  return url_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_url(const ::std::string& value) {
  
  url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.url)
}
void ADNews::set_url(const char* value) {
  
  url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNews.url)
}
void ADNews::set_url(const char* value, size_t size) {
  
  url_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNews.url)
}
::std::string* ADNews::mutable_url() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.url)
  return url_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNews::release_url() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.url)
  
  return url_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNews::set_allocated_url(::std::string* url) {
  if (url != NULL) {
    
  } else {
    
  }
  url_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), url);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.url)
}

// optional .com.htsc.mdc.insight.model.ADNewsEmotion newsEmotion = 20;
bool ADNews::has_newsemotion() const {
  return this != internal_default_instance() && newsemotion_ != NULL;
}
void ADNews::clear_newsemotion() {
  if (GetArenaNoVirtual() == NULL && newsemotion_ != NULL) delete newsemotion_;
  newsemotion_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADNewsEmotion& ADNews::newsemotion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.newsEmotion)
  return newsemotion_ != NULL ? *newsemotion_
                         : *::com::htsc::mdc::insight::model::ADNewsEmotion::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADNewsEmotion* ADNews::mutable_newsemotion() {
  
  if (newsemotion_ == NULL) {
    newsemotion_ = new ::com::htsc::mdc::insight::model::ADNewsEmotion;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.newsEmotion)
  return newsemotion_;
}
::com::htsc::mdc::insight::model::ADNewsEmotion* ADNews::release_newsemotion() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNews.newsEmotion)
  
  ::com::htsc::mdc::insight::model::ADNewsEmotion* temp = newsemotion_;
  newsemotion_ = NULL;
  return temp;
}
void ADNews::set_allocated_newsemotion(::com::htsc::mdc::insight::model::ADNewsEmotion* newsemotion) {
  delete newsemotion_;
  newsemotion_ = newsemotion;
  if (newsemotion) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNews.newsEmotion)
}

// repeated .com.htsc.mdc.insight.model.ADInvolvedSecurity secuList = 21;
int ADNews::seculist_size() const {
  return seculist_.size();
}
void ADNews::clear_seculist() {
  seculist_.Clear();
}
const ::com::htsc::mdc::insight::model::ADInvolvedSecurity& ADNews::seculist(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.secuList)
  return seculist_.Get(index);
}
::com::htsc::mdc::insight::model::ADInvolvedSecurity* ADNews::mutable_seculist(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.secuList)
  return seculist_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADInvolvedSecurity* ADNews::add_seculist() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADNews.secuList)
  return seculist_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADInvolvedSecurity >*
ADNews::mutable_seculist() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADNews.secuList)
  return &seculist_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADInvolvedSecurity >&
ADNews::seculist() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADNews.secuList)
  return seculist_;
}

// repeated .com.htsc.mdc.insight.model.ADRelatedPerson perEmList = 22;
int ADNews::peremlist_size() const {
  return peremlist_.size();
}
void ADNews::clear_peremlist() {
  peremlist_.Clear();
}
const ::com::htsc::mdc::insight::model::ADRelatedPerson& ADNews::peremlist(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.perEmList)
  return peremlist_.Get(index);
}
::com::htsc::mdc::insight::model::ADRelatedPerson* ADNews::mutable_peremlist(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.perEmList)
  return peremlist_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADRelatedPerson* ADNews::add_peremlist() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADNews.perEmList)
  return peremlist_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADRelatedPerson >*
ADNews::mutable_peremlist() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADNews.perEmList)
  return &peremlist_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADRelatedPerson >&
ADNews::peremlist() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADNews.perEmList)
  return peremlist_;
}

// repeated .com.htsc.mdc.insight.model.ADRelatedCompany comEmList = 23;
int ADNews::comemlist_size() const {
  return comemlist_.size();
}
void ADNews::clear_comemlist() {
  comemlist_.Clear();
}
const ::com::htsc::mdc::insight::model::ADRelatedCompany& ADNews::comemlist(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.comEmList)
  return comemlist_.Get(index);
}
::com::htsc::mdc::insight::model::ADRelatedCompany* ADNews::mutable_comemlist(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.comEmList)
  return comemlist_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADRelatedCompany* ADNews::add_comemlist() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADNews.comEmList)
  return comemlist_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADRelatedCompany >*
ADNews::mutable_comemlist() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADNews.comEmList)
  return &comemlist_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADRelatedCompany >&
ADNews::comemlist() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADNews.comEmList)
  return comemlist_;
}

// optional int32 DataMultiplePowerOf10 = 24;
void ADNews::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 ADNews::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void ADNews::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNews.DataMultiplePowerOf10)
}

// repeated .com.htsc.mdc.insight.model.ADNewsDataEntry sentimentContent = 25;
int ADNews::sentimentcontent_size() const {
  return sentimentcontent_.size();
}
void ADNews::clear_sentimentcontent() {
  sentimentcontent_.Clear();
}
const ::com::htsc::mdc::insight::model::ADNewsDataEntry& ADNews::sentimentcontent(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNews.sentimentContent)
  return sentimentcontent_.Get(index);
}
::com::htsc::mdc::insight::model::ADNewsDataEntry* ADNews::mutable_sentimentcontent(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNews.sentimentContent)
  return sentimentcontent_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADNewsDataEntry* ADNews::add_sentimentcontent() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADNews.sentimentContent)
  return sentimentcontent_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADNewsDataEntry >*
ADNews::mutable_sentimentcontent() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADNews.sentimentContent)
  return &sentimentcontent_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADNewsDataEntry >&
ADNews::sentimentcontent() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADNews.sentimentContent)
  return sentimentcontent_;
}

inline const ADNews* ADNews::internal_default_instance() {
  return &ADNews_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADNewsEmotion::kNegativeFieldNumber;
const int ADNewsEmotion::kNeutralFieldNumber;
const int ADNewsEmotion::kPosFieldNumber;
const int ADNewsEmotion::kPositiveFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADNewsEmotion::ADNewsEmotion()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADNewsEmotion)
}

void ADNewsEmotion::InitAsDefaultInstance() {
}

ADNewsEmotion::ADNewsEmotion(const ADNewsEmotion& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADNewsEmotion)
}

void ADNewsEmotion::SharedCtor() {
  ::memset(&negative_, 0, reinterpret_cast<char*>(&pos_) -
    reinterpret_cast<char*>(&negative_) + sizeof(pos_));
  _cached_size_ = 0;
}

ADNewsEmotion::~ADNewsEmotion() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADNewsEmotion)
  SharedDtor();
}

void ADNewsEmotion::SharedDtor() {
}

void ADNewsEmotion::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADNewsEmotion::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADNewsEmotion_descriptor_;
}

const ADNewsEmotion& ADNewsEmotion::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADNewsEmotion> ADNewsEmotion_default_instance_;

ADNewsEmotion* ADNewsEmotion::New(::google::protobuf::Arena* arena) const {
  ADNewsEmotion* n = new ADNewsEmotion;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADNewsEmotion::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADNewsEmotion)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADNewsEmotion, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADNewsEmotion*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(negative_, pos_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADNewsEmotion::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional double negative = 1;
      case 1: {
        if (tag == 9) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &negative_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(17)) goto parse_neutral;
        break;
      }

      // optional double neutral = 2;
      case 2: {
        if (tag == 17) {
         parse_neutral:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &neutral_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_pos;
        break;
      }

      // optional int32 pos = 3;
      case 3: {
        if (tag == 24) {
         parse_pos:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &pos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(33)) goto parse_positive;
        break;
      }

      // optional double positive = 4;
      case 4: {
        if (tag == 33) {
         parse_positive:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &positive_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADNewsEmotion)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADNewsEmotion)
  return false;
#undef DO_
}

void ADNewsEmotion::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  // optional double negative = 1;
  if (this->negative() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->negative(), output);
  }

  // optional double neutral = 2;
  if (this->neutral() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->neutral(), output);
  }

  // optional int32 pos = 3;
  if (this->pos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->pos(), output);
  }

  // optional double positive = 4;
  if (this->positive() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->positive(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADNewsEmotion)
}

::google::protobuf::uint8* ADNewsEmotion::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  // optional double negative = 1;
  if (this->negative() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->negative(), target);
  }

  // optional double neutral = 2;
  if (this->neutral() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->neutral(), target);
  }

  // optional int32 pos = 3;
  if (this->pos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->pos(), target);
  }

  // optional double positive = 4;
  if (this->positive() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->positive(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADNewsEmotion)
  return target;
}

size_t ADNewsEmotion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  size_t total_size = 0;

  // optional double negative = 1;
  if (this->negative() != 0) {
    total_size += 1 + 8;
  }

  // optional double neutral = 2;
  if (this->neutral() != 0) {
    total_size += 1 + 8;
  }

  // optional int32 pos = 3;
  if (this->pos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->pos());
  }

  // optional double positive = 4;
  if (this->positive() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADNewsEmotion::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADNewsEmotion* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADNewsEmotion>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADNewsEmotion)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADNewsEmotion)
    UnsafeMergeFrom(*source);
  }
}

void ADNewsEmotion::MergeFrom(const ADNewsEmotion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADNewsEmotion::UnsafeMergeFrom(const ADNewsEmotion& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.negative() != 0) {
    set_negative(from.negative());
  }
  if (from.neutral() != 0) {
    set_neutral(from.neutral());
  }
  if (from.pos() != 0) {
    set_pos(from.pos());
  }
  if (from.positive() != 0) {
    set_positive(from.positive());
  }
}

void ADNewsEmotion::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADNewsEmotion::CopyFrom(const ADNewsEmotion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADNewsEmotion)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADNewsEmotion::IsInitialized() const {

  return true;
}

void ADNewsEmotion::Swap(ADNewsEmotion* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADNewsEmotion::InternalSwap(ADNewsEmotion* other) {
  std::swap(negative_, other->negative_);
  std::swap(neutral_, other->neutral_);
  std::swap(pos_, other->pos_);
  std::swap(positive_, other->positive_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADNewsEmotion::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADNewsEmotion_descriptor_;
  metadata.reflection = ADNewsEmotion_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADNewsEmotion

// optional double negative = 1;
void ADNewsEmotion::clear_negative() {
  negative_ = 0;
}
double ADNewsEmotion::negative() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsEmotion.negative)
  return negative_;
}
void ADNewsEmotion::set_negative(double value) {
  
  negative_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsEmotion.negative)
}

// optional double neutral = 2;
void ADNewsEmotion::clear_neutral() {
  neutral_ = 0;
}
double ADNewsEmotion::neutral() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsEmotion.neutral)
  return neutral_;
}
void ADNewsEmotion::set_neutral(double value) {
  
  neutral_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsEmotion.neutral)
}

// optional int32 pos = 3;
void ADNewsEmotion::clear_pos() {
  pos_ = 0;
}
::google::protobuf::int32 ADNewsEmotion::pos() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsEmotion.pos)
  return pos_;
}
void ADNewsEmotion::set_pos(::google::protobuf::int32 value) {
  
  pos_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsEmotion.pos)
}

// optional double positive = 4;
void ADNewsEmotion::clear_positive() {
  positive_ = 0;
}
double ADNewsEmotion::positive() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsEmotion.positive)
  return positive_;
}
void ADNewsEmotion::set_positive(double value) {
  
  positive_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsEmotion.positive)
}

inline const ADNewsEmotion* ADNewsEmotion::internal_default_instance() {
  return &ADNewsEmotion_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADInvolvedSecurity::kComCodeFieldNumber;
const int ADInvolvedSecurity::kSecuCodeFieldNumber;
const int ADInvolvedSecurity::kSecuNameFieldNumber;
const int ADInvolvedSecurity::kHTSCSecurityIDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADInvolvedSecurity::ADInvolvedSecurity()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADInvolvedSecurity)
}

void ADInvolvedSecurity::InitAsDefaultInstance() {
}

ADInvolvedSecurity::ADInvolvedSecurity(const ADInvolvedSecurity& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADInvolvedSecurity)
}

void ADInvolvedSecurity::SharedCtor() {
  comcode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secucode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secuname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

ADInvolvedSecurity::~ADInvolvedSecurity() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  SharedDtor();
}

void ADInvolvedSecurity::SharedDtor() {
  comcode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secucode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secuname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADInvolvedSecurity::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADInvolvedSecurity::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADInvolvedSecurity_descriptor_;
}

const ADInvolvedSecurity& ADInvolvedSecurity::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADInvolvedSecurity> ADInvolvedSecurity_default_instance_;

ADInvolvedSecurity* ADInvolvedSecurity::New(::google::protobuf::Arena* arena) const {
  ADInvolvedSecurity* n = new ADInvolvedSecurity;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADInvolvedSecurity::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secucode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secuname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool ADInvolvedSecurity::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string comCode = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comcode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comcode().data(), this->comcode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_secuCode;
        break;
      }

      // optional string secuCode = 2;
      case 2: {
        if (tag == 18) {
         parse_secuCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_secucode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->secucode().data(), this->secucode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_secuName;
        break;
      }

      // optional string secuName = 3;
      case 3: {
        if (tag == 26) {
         parse_secuName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_secuname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->secuname().data(), this->secuname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_HTSCSecurityID;
        break;
      }

      // optional string HTSCSecurityID = 4;
      case 4: {
        if (tag == 34) {
         parse_HTSCSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  return false;
#undef DO_
}

void ADInvolvedSecurity::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  // optional string comCode = 1;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->comcode(), output);
  }

  // optional string secuCode = 2;
  if (this->secucode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secucode().data(), this->secucode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->secucode(), output);
  }

  // optional string secuName = 3;
  if (this->secuname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secuname().data(), this->secuname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->secuname(), output);
  }

  // optional string HTSCSecurityID = 4;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->htscsecurityid(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADInvolvedSecurity)
}

::google::protobuf::uint8* ADInvolvedSecurity::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  // optional string comCode = 1;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->comcode(), target);
  }

  // optional string secuCode = 2;
  if (this->secucode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secucode().data(), this->secucode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->secucode(), target);
  }

  // optional string secuName = 3;
  if (this->secuname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secuname().data(), this->secuname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->secuname(), target);
  }

  // optional string HTSCSecurityID = 4;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->htscsecurityid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  return target;
}

size_t ADInvolvedSecurity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  size_t total_size = 0;

  // optional string comCode = 1;
  if (this->comcode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comcode());
  }

  // optional string secuCode = 2;
  if (this->secucode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->secucode());
  }

  // optional string secuName = 3;
  if (this->secuname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->secuname());
  }

  // optional string HTSCSecurityID = 4;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADInvolvedSecurity::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADInvolvedSecurity* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADInvolvedSecurity>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADInvolvedSecurity)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADInvolvedSecurity)
    UnsafeMergeFrom(*source);
  }
}

void ADInvolvedSecurity::MergeFrom(const ADInvolvedSecurity& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADInvolvedSecurity::UnsafeMergeFrom(const ADInvolvedSecurity& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.comcode().size() > 0) {

    comcode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comcode_);
  }
  if (from.secucode().size() > 0) {

    secucode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.secucode_);
  }
  if (from.secuname().size() > 0) {

    secuname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.secuname_);
  }
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
}

void ADInvolvedSecurity::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADInvolvedSecurity::CopyFrom(const ADInvolvedSecurity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADInvolvedSecurity)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADInvolvedSecurity::IsInitialized() const {

  return true;
}

void ADInvolvedSecurity::Swap(ADInvolvedSecurity* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADInvolvedSecurity::InternalSwap(ADInvolvedSecurity* other) {
  comcode_.Swap(&other->comcode_);
  secucode_.Swap(&other->secucode_);
  secuname_.Swap(&other->secuname_);
  htscsecurityid_.Swap(&other->htscsecurityid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADInvolvedSecurity::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADInvolvedSecurity_descriptor_;
  metadata.reflection = ADInvolvedSecurity_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADInvolvedSecurity

// optional string comCode = 1;
void ADInvolvedSecurity::clear_comcode() {
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADInvolvedSecurity::comcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
  return comcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_comcode(const ::std::string& value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
}
void ADInvolvedSecurity::set_comcode(const char* value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
}
void ADInvolvedSecurity::set_comcode(const char* value, size_t size) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
}
::std::string* ADInvolvedSecurity::mutable_comcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
  return comcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADInvolvedSecurity::release_comcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
  
  return comcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_allocated_comcode(::std::string* comcode) {
  if (comcode != NULL) {
    
  } else {
    
  }
  comcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADInvolvedSecurity.comCode)
}

// optional string secuCode = 2;
void ADInvolvedSecurity::clear_secucode() {
  secucode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADInvolvedSecurity::secucode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
  return secucode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_secucode(const ::std::string& value) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
}
void ADInvolvedSecurity::set_secucode(const char* value) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
}
void ADInvolvedSecurity::set_secucode(const char* value, size_t size) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
}
::std::string* ADInvolvedSecurity::mutable_secucode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
  return secucode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADInvolvedSecurity::release_secucode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
  
  return secucode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_allocated_secucode(::std::string* secucode) {
  if (secucode != NULL) {
    
  } else {
    
  }
  secucode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secucode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuCode)
}

// optional string secuName = 3;
void ADInvolvedSecurity::clear_secuname() {
  secuname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADInvolvedSecurity::secuname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
  return secuname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_secuname(const ::std::string& value) {
  
  secuname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
}
void ADInvolvedSecurity::set_secuname(const char* value) {
  
  secuname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
}
void ADInvolvedSecurity::set_secuname(const char* value, size_t size) {
  
  secuname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
}
::std::string* ADInvolvedSecurity::mutable_secuname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
  return secuname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADInvolvedSecurity::release_secuname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
  
  return secuname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_allocated_secuname(::std::string* secuname) {
  if (secuname != NULL) {
    
  } else {
    
  }
  secuname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secuname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADInvolvedSecurity.secuName)
}

// optional string HTSCSecurityID = 4;
void ADInvolvedSecurity::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADInvolvedSecurity::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
}
void ADInvolvedSecurity::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
}
void ADInvolvedSecurity::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
}
::std::string* ADInvolvedSecurity::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADInvolvedSecurity::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADInvolvedSecurity::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADInvolvedSecurity.HTSCSecurityID)
}

inline const ADInvolvedSecurity* ADInvolvedSecurity::internal_default_instance() {
  return &ADInvolvedSecurity_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADRelatedCompany::kChinameFieldNumber;
const int ADRelatedCompany::kComCodeFieldNumber;
const int ADRelatedCompany::kComFieldNumber;
const int ADRelatedCompany::kGeneralPosFieldNumber;
const int ADRelatedCompany::kSecuCodeFieldNumber;
const int ADRelatedCompany::kHTSCSecurityIDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADRelatedCompany::ADRelatedCompany()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADRelatedCompany)
}

void ADRelatedCompany::InitAsDefaultInstance() {
}

ADRelatedCompany::ADRelatedCompany(const ADRelatedCompany& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADRelatedCompany)
}

void ADRelatedCompany::SharedCtor() {
  chiname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secucode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generalpos_ = 0;
  _cached_size_ = 0;
}

ADRelatedCompany::~ADRelatedCompany() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADRelatedCompany)
  SharedDtor();
}

void ADRelatedCompany::SharedDtor() {
  chiname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secucode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADRelatedCompany::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADRelatedCompany::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADRelatedCompany_descriptor_;
}

const ADRelatedCompany& ADRelatedCompany::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADRelatedCompany> ADRelatedCompany_default_instance_;

ADRelatedCompany* ADRelatedCompany::New(::google::protobuf::Arena* arena) const {
  ADRelatedCompany* n = new ADRelatedCompany;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADRelatedCompany::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  chiname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generalpos_ = 0;
  secucode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool ADRelatedCompany::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string chiname = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_chiname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->chiname().data(), this->chiname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedCompany.chiname"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_comCode;
        break;
      }

      // optional string comCode = 2;
      case 2: {
        if (tag == 18) {
         parse_comCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comcode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comcode().data(), this->comcode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedCompany.comCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_com;
        break;
      }

      // optional string com = 3;
      case 3: {
        if (tag == 26) {
         parse_com:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_com()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->com().data(), this->com().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedCompany.com"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_generalPos;
        break;
      }

      // optional int32 generalPos = 4;
      case 4: {
        if (tag == 32) {
         parse_generalPos:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &generalpos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_secuCode;
        break;
      }

      // optional string secuCode = 5;
      case 5: {
        if (tag == 42) {
         parse_secuCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_secucode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->secucode().data(), this->secucode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedCompany.secuCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_HTSCSecurityID;
        break;
      }

      // optional string HTSCSecurityID = 6;
      case 6: {
        if (tag == 50) {
         parse_HTSCSecurityID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADRelatedCompany)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADRelatedCompany)
  return false;
#undef DO_
}

void ADRelatedCompany::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  // optional string chiname = 1;
  if (this->chiname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->chiname().data(), this->chiname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.chiname");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->chiname(), output);
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.comCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->comcode(), output);
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->com().data(), this->com().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.com");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->com(), output);
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->generalpos(), output);
  }

  // optional string secuCode = 5;
  if (this->secucode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secucode().data(), this->secucode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.secuCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->secucode(), output);
  }

  // optional string HTSCSecurityID = 6;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->htscsecurityid(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADRelatedCompany)
}

::google::protobuf::uint8* ADRelatedCompany::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  // optional string chiname = 1;
  if (this->chiname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->chiname().data(), this->chiname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.chiname");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->chiname(), target);
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.comCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->comcode(), target);
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->com().data(), this->com().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.com");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->com(), target);
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->generalpos(), target);
  }

  // optional string secuCode = 5;
  if (this->secucode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secucode().data(), this->secucode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.secuCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->secucode(), target);
  }

  // optional string HTSCSecurityID = 6;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->htscsecurityid(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADRelatedCompany)
  return target;
}

size_t ADRelatedCompany::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  size_t total_size = 0;

  // optional string chiname = 1;
  if (this->chiname().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->chiname());
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comcode());
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->com());
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->generalpos());
  }

  // optional string secuCode = 5;
  if (this->secucode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->secucode());
  }

  // optional string HTSCSecurityID = 6;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADRelatedCompany::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADRelatedCompany* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADRelatedCompany>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADRelatedCompany)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADRelatedCompany)
    UnsafeMergeFrom(*source);
  }
}

void ADRelatedCompany::MergeFrom(const ADRelatedCompany& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADRelatedCompany::UnsafeMergeFrom(const ADRelatedCompany& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.chiname().size() > 0) {

    chiname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.chiname_);
  }
  if (from.comcode().size() > 0) {

    comcode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comcode_);
  }
  if (from.com().size() > 0) {

    com_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.com_);
  }
  if (from.generalpos() != 0) {
    set_generalpos(from.generalpos());
  }
  if (from.secucode().size() > 0) {

    secucode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.secucode_);
  }
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
}

void ADRelatedCompany::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADRelatedCompany::CopyFrom(const ADRelatedCompany& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADRelatedCompany)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADRelatedCompany::IsInitialized() const {

  return true;
}

void ADRelatedCompany::Swap(ADRelatedCompany* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADRelatedCompany::InternalSwap(ADRelatedCompany* other) {
  chiname_.Swap(&other->chiname_);
  comcode_.Swap(&other->comcode_);
  com_.Swap(&other->com_);
  std::swap(generalpos_, other->generalpos_);
  secucode_.Swap(&other->secucode_);
  htscsecurityid_.Swap(&other->htscsecurityid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADRelatedCompany::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADRelatedCompany_descriptor_;
  metadata.reflection = ADRelatedCompany_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADRelatedCompany

// optional string chiname = 1;
void ADRelatedCompany::clear_chiname() {
  chiname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedCompany::chiname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
  return chiname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_chiname(const ::std::string& value) {
  
  chiname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
}
void ADRelatedCompany::set_chiname(const char* value) {
  
  chiname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
}
void ADRelatedCompany::set_chiname(const char* value, size_t size) {
  
  chiname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
}
::std::string* ADRelatedCompany::mutable_chiname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
  return chiname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedCompany::release_chiname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
  
  return chiname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_allocated_chiname(::std::string* chiname) {
  if (chiname != NULL) {
    
  } else {
    
  }
  chiname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), chiname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedCompany.chiname)
}

// optional string comCode = 2;
void ADRelatedCompany::clear_comcode() {
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedCompany::comcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
  return comcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_comcode(const ::std::string& value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
}
void ADRelatedCompany::set_comcode(const char* value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
}
void ADRelatedCompany::set_comcode(const char* value, size_t size) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
}
::std::string* ADRelatedCompany::mutable_comcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
  return comcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedCompany::release_comcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
  
  return comcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_allocated_comcode(::std::string* comcode) {
  if (comcode != NULL) {
    
  } else {
    
  }
  comcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedCompany.comCode)
}

// optional string com = 3;
void ADRelatedCompany::clear_com() {
  com_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedCompany::com() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.com)
  return com_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_com(const ::std::string& value) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.com)
}
void ADRelatedCompany::set_com(const char* value) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedCompany.com)
}
void ADRelatedCompany::set_com(const char* value, size_t size) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedCompany.com)
}
::std::string* ADRelatedCompany::mutable_com() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedCompany.com)
  return com_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedCompany::release_com() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedCompany.com)
  
  return com_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_allocated_com(::std::string* com) {
  if (com != NULL) {
    
  } else {
    
  }
  com_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), com);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedCompany.com)
}

// optional int32 generalPos = 4;
void ADRelatedCompany::clear_generalpos() {
  generalpos_ = 0;
}
::google::protobuf::int32 ADRelatedCompany::generalpos() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.generalPos)
  return generalpos_;
}
void ADRelatedCompany::set_generalpos(::google::protobuf::int32 value) {
  
  generalpos_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.generalPos)
}

// optional string secuCode = 5;
void ADRelatedCompany::clear_secucode() {
  secucode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedCompany::secucode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
  return secucode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_secucode(const ::std::string& value) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
}
void ADRelatedCompany::set_secucode(const char* value) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
}
void ADRelatedCompany::set_secucode(const char* value, size_t size) {
  
  secucode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
}
::std::string* ADRelatedCompany::mutable_secucode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
  return secucode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedCompany::release_secucode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
  
  return secucode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_allocated_secucode(::std::string* secucode) {
  if (secucode != NULL) {
    
  } else {
    
  }
  secucode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secucode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedCompany.secuCode)
}

// optional string HTSCSecurityID = 6;
void ADRelatedCompany::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedCompany::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
}
void ADRelatedCompany::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
}
void ADRelatedCompany::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
}
::std::string* ADRelatedCompany::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedCompany::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedCompany::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedCompany.HTSCSecurityID)
}

inline const ADRelatedCompany* ADRelatedCompany::internal_default_instance() {
  return &ADRelatedCompany_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADRelatedPerson::kPerFieldNumber;
const int ADRelatedPerson::kComCodeFieldNumber;
const int ADRelatedPerson::kComFieldNumber;
const int ADRelatedPerson::kGeneralPosFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADRelatedPerson::ADRelatedPerson()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADRelatedPerson)
}

void ADRelatedPerson::InitAsDefaultInstance() {
}

ADRelatedPerson::ADRelatedPerson(const ADRelatedPerson& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADRelatedPerson)
}

void ADRelatedPerson::SharedCtor() {
  per_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generalpos_ = 0;
  _cached_size_ = 0;
}

ADRelatedPerson::~ADRelatedPerson() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADRelatedPerson)
  SharedDtor();
}

void ADRelatedPerson::SharedDtor() {
  per_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADRelatedPerson::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADRelatedPerson::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADRelatedPerson_descriptor_;
}

const ADRelatedPerson& ADRelatedPerson::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADRelatedPerson> ADRelatedPerson_default_instance_;

ADRelatedPerson* ADRelatedPerson::New(::google::protobuf::Arena* arena) const {
  ADRelatedPerson* n = new ADRelatedPerson;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADRelatedPerson::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  per_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  com_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generalpos_ = 0;
}

bool ADRelatedPerson::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string per = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_per()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->per().data(), this->per().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedPerson.per"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_comCode;
        break;
      }

      // optional string comCode = 2;
      case 2: {
        if (tag == 18) {
         parse_comCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_comcode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->comcode().data(), this->comcode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedPerson.comCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_com;
        break;
      }

      // optional string com = 3;
      case 3: {
        if (tag == 26) {
         parse_com:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_com()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->com().data(), this->com().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADRelatedPerson.com"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_generalPos;
        break;
      }

      // optional int32 generalPos = 4;
      case 4: {
        if (tag == 32) {
         parse_generalPos:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &generalpos_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADRelatedPerson)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADRelatedPerson)
  return false;
#undef DO_
}

void ADRelatedPerson::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  // optional string per = 1;
  if (this->per().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->per().data(), this->per().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.per");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->per(), output);
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.comCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->comcode(), output);
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->com().data(), this->com().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.com");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->com(), output);
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->generalpos(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADRelatedPerson)
}

::google::protobuf::uint8* ADRelatedPerson::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  // optional string per = 1;
  if (this->per().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->per().data(), this->per().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.per");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->per(), target);
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->comcode().data(), this->comcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.comCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->comcode(), target);
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->com().data(), this->com().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADRelatedPerson.com");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->com(), target);
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->generalpos(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADRelatedPerson)
  return target;
}

size_t ADRelatedPerson::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  size_t total_size = 0;

  // optional string per = 1;
  if (this->per().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->per());
  }

  // optional string comCode = 2;
  if (this->comcode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->comcode());
  }

  // optional string com = 3;
  if (this->com().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->com());
  }

  // optional int32 generalPos = 4;
  if (this->generalpos() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->generalpos());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADRelatedPerson::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADRelatedPerson* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADRelatedPerson>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADRelatedPerson)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADRelatedPerson)
    UnsafeMergeFrom(*source);
  }
}

void ADRelatedPerson::MergeFrom(const ADRelatedPerson& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADRelatedPerson::UnsafeMergeFrom(const ADRelatedPerson& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.per().size() > 0) {

    per_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.per_);
  }
  if (from.comcode().size() > 0) {

    comcode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.comcode_);
  }
  if (from.com().size() > 0) {

    com_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.com_);
  }
  if (from.generalpos() != 0) {
    set_generalpos(from.generalpos());
  }
}

void ADRelatedPerson::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADRelatedPerson::CopyFrom(const ADRelatedPerson& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADRelatedPerson)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADRelatedPerson::IsInitialized() const {

  return true;
}

void ADRelatedPerson::Swap(ADRelatedPerson* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADRelatedPerson::InternalSwap(ADRelatedPerson* other) {
  per_.Swap(&other->per_);
  comcode_.Swap(&other->comcode_);
  com_.Swap(&other->com_);
  std::swap(generalpos_, other->generalpos_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADRelatedPerson::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADRelatedPerson_descriptor_;
  metadata.reflection = ADRelatedPerson_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADRelatedPerson

// optional string per = 1;
void ADRelatedPerson::clear_per() {
  per_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedPerson::per() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedPerson.per)
  return per_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_per(const ::std::string& value) {
  
  per_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedPerson.per)
}
void ADRelatedPerson::set_per(const char* value) {
  
  per_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedPerson.per)
}
void ADRelatedPerson::set_per(const char* value, size_t size) {
  
  per_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedPerson.per)
}
::std::string* ADRelatedPerson::mutable_per() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedPerson.per)
  return per_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedPerson::release_per() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedPerson.per)
  
  return per_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_allocated_per(::std::string* per) {
  if (per != NULL) {
    
  } else {
    
  }
  per_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), per);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedPerson.per)
}

// optional string comCode = 2;
void ADRelatedPerson::clear_comcode() {
  comcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedPerson::comcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
  return comcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_comcode(const ::std::string& value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
}
void ADRelatedPerson::set_comcode(const char* value) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
}
void ADRelatedPerson::set_comcode(const char* value, size_t size) {
  
  comcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
}
::std::string* ADRelatedPerson::mutable_comcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
  return comcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedPerson::release_comcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
  
  return comcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_allocated_comcode(::std::string* comcode) {
  if (comcode != NULL) {
    
  } else {
    
  }
  comcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), comcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedPerson.comCode)
}

// optional string com = 3;
void ADRelatedPerson::clear_com() {
  com_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADRelatedPerson::com() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedPerson.com)
  return com_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_com(const ::std::string& value) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedPerson.com)
}
void ADRelatedPerson::set_com(const char* value) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADRelatedPerson.com)
}
void ADRelatedPerson::set_com(const char* value, size_t size) {
  
  com_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADRelatedPerson.com)
}
::std::string* ADRelatedPerson::mutable_com() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADRelatedPerson.com)
  return com_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADRelatedPerson::release_com() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADRelatedPerson.com)
  
  return com_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADRelatedPerson::set_allocated_com(::std::string* com) {
  if (com != NULL) {
    
  } else {
    
  }
  com_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), com);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADRelatedPerson.com)
}

// optional int32 generalPos = 4;
void ADRelatedPerson::clear_generalpos() {
  generalpos_ = 0;
}
::google::protobuf::int32 ADRelatedPerson::generalpos() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADRelatedPerson.generalPos)
  return generalpos_;
}
void ADRelatedPerson::set_generalpos(::google::protobuf::int32 value) {
  
  generalpos_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADRelatedPerson.generalPos)
}

inline const ADRelatedPerson* ADRelatedPerson::internal_default_instance() {
  return &ADRelatedPerson_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADNewsDataEntry::kKeyFieldNumber;
const int ADNewsDataEntry::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADNewsDataEntry::ADNewsDataEntry()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADNews_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADNewsDataEntry)
}

void ADNewsDataEntry::InitAsDefaultInstance() {
}

ADNewsDataEntry::ADNewsDataEntry(const ADNewsDataEntry& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADNewsDataEntry)
}

void ADNewsDataEntry::SharedCtor() {
  key_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

ADNewsDataEntry::~ADNewsDataEntry() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADNewsDataEntry)
  SharedDtor();
}

void ADNewsDataEntry::SharedDtor() {
  key_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADNewsDataEntry::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADNewsDataEntry::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADNewsDataEntry_descriptor_;
}

const ADNewsDataEntry& ADNewsDataEntry::default_instance() {
  protobuf_InitDefaults_ADNews_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADNewsDataEntry> ADNewsDataEntry_default_instance_;

ADNewsDataEntry* ADNewsDataEntry::New(::google::protobuf::Arena* arena) const {
  ADNewsDataEntry* n = new ADNewsDataEntry;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADNewsDataEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool ADNewsDataEntry::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string key = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_key()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->key().data(), this->key().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNewsDataEntry.key"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_value;
        break;
      }

      // optional string value = 2;
      case 2: {
        if (tag == 18) {
         parse_value:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADNewsDataEntry.value"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADNewsDataEntry)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADNewsDataEntry)
  return false;
#undef DO_
}

void ADNewsDataEntry::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNewsDataEntry.key");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->key(), output);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNewsDataEntry.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADNewsDataEntry)
}

::google::protobuf::uint8* ADNewsDataEntry::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  // optional string key = 1;
  if (this->key().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->key().data(), this->key().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNewsDataEntry.key");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->key(), target);
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADNewsDataEntry.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADNewsDataEntry)
  return target;
}

size_t ADNewsDataEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  size_t total_size = 0;

  // optional string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->key());
  }

  // optional string value = 2;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADNewsDataEntry::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADNewsDataEntry* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADNewsDataEntry>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADNewsDataEntry)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADNewsDataEntry)
    UnsafeMergeFrom(*source);
  }
}

void ADNewsDataEntry::MergeFrom(const ADNewsDataEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADNewsDataEntry::UnsafeMergeFrom(const ADNewsDataEntry& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.key().size() > 0) {

    key_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.key_);
  }
  if (from.value().size() > 0) {

    value_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value_);
  }
}

void ADNewsDataEntry::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADNewsDataEntry::CopyFrom(const ADNewsDataEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADNewsDataEntry)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADNewsDataEntry::IsInitialized() const {

  return true;
}

void ADNewsDataEntry::Swap(ADNewsDataEntry* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADNewsDataEntry::InternalSwap(ADNewsDataEntry* other) {
  key_.Swap(&other->key_);
  value_.Swap(&other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADNewsDataEntry::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADNewsDataEntry_descriptor_;
  metadata.reflection = ADNewsDataEntry_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADNewsDataEntry

// optional string key = 1;
void ADNewsDataEntry::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNewsDataEntry::key() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
  return key_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNewsDataEntry::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
}
void ADNewsDataEntry::set_key(const char* value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
}
void ADNewsDataEntry::set_key(const char* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
}
::std::string* ADNewsDataEntry::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNewsDataEntry::release_key() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNewsDataEntry::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNewsDataEntry.key)
}

// optional string value = 2;
void ADNewsDataEntry::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADNewsDataEntry::value() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
  return value_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNewsDataEntry::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
}
void ADNewsDataEntry::set_value(const char* value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
}
void ADNewsDataEntry::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
}
::std::string* ADNewsDataEntry::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADNewsDataEntry::release_value() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADNewsDataEntry::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADNewsDataEntry.value)
}

inline const ADNewsDataEntry* ADNewsDataEntry::internal_default_instance() {
  return &ADNewsDataEntry_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
