// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDCashBondQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCashBondQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDFxBestQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDFxBestQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* NeeqMarketMakerQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NeeqMarketMakerQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* BrokerQueue_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  BrokerQueue_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDQuote_2eproto() {
  protobuf_AddDesc_MDQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDQuote_descriptor_ = file->message_type(0);
  static const int MDQuote_offsets_[27] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mdbooktype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, marketdepth_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mdsubbooktype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mdcashbondquotes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, volatilitysurface_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, mdfxbestquotes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, neeqinvestorbuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, neeqinvestorsell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, neeqmarketmakerbuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, neeqmarketmakersell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, buybrokerqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, sellbrokerqueue_),
  };
  MDQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDQuote_descriptor_,
      MDQuote::internal_default_instance(),
      MDQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDQuote, _internal_metadata_));
  MDCashBondQuote_descriptor_ = file->message_type(1);
  static const int MDCashBondQuote_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, quotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, side_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, pricelevel_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, quoteid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, quotedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, quotetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, cleanprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, dirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, totalfacevalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, clearingmethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, settltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, settldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, settlcurrency_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, settlcurrfxrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, partyrole_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, tradercode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, maturityyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, deliverytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, traderaccountid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, datatype_),
  };
  MDCashBondQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCashBondQuote_descriptor_,
      MDCashBondQuote::internal_default_instance(),
      MDCashBondQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCashBondQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCashBondQuote, _internal_metadata_));
  MDFxBestQuote_descriptor_ = file->message_type(2);
  static const int MDFxBestQuote_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, side_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, price_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, tenor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, date_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, time_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, liquidproviders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, legsign_),
  };
  MDFxBestQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDFxBestQuote_descriptor_,
      MDFxBestQuote::internal_default_instance(),
      MDFxBestQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDFxBestQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFxBestQuote, _internal_metadata_));
  NeeqMarketMakerQuote_descriptor_ = file->message_type(3);
  static const int NeeqMarketMakerQuote_offsets_[6] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, sequence_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, quotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, price_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, generatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, backupfield_),
  };
  NeeqMarketMakerQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NeeqMarketMakerQuote_descriptor_,
      NeeqMarketMakerQuote::internal_default_instance(),
      NeeqMarketMakerQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(NeeqMarketMakerQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NeeqMarketMakerQuote, _internal_metadata_));
  BrokerQueue_descriptor_ = file->message_type(4);
  static const int BrokerQueue_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BrokerQueue, price_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BrokerQueue, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BrokerQueue, numorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BrokerQueue, brokerid_),
  };
  BrokerQueue_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      BrokerQueue_descriptor_,
      BrokerQueue::internal_default_instance(),
      BrokerQueue_offsets_,
      -1,
      -1,
      -1,
      sizeof(BrokerQueue),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BrokerQueue, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDQuote_descriptor_, MDQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCashBondQuote_descriptor_, MDCashBondQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDFxBestQuote_descriptor_, MDFxBestQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NeeqMarketMakerQuote_descriptor_, NeeqMarketMakerQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      BrokerQueue_descriptor_, BrokerQueue::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDQuote_2eproto() {
  MDQuote_default_instance_.Shutdown();
  delete MDQuote_reflection_;
  MDCashBondQuote_default_instance_.Shutdown();
  delete MDCashBondQuote_reflection_;
  MDFxBestQuote_default_instance_.Shutdown();
  delete MDFxBestQuote_reflection_;
  NeeqMarketMakerQuote_default_instance_.Shutdown();
  delete NeeqMarketMakerQuote_reflection_;
  BrokerQueue_default_instance_.Shutdown();
  delete BrokerQueue_reflection_;
}

void protobuf_InitDefaults_MDQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDCashBondQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDFxBestQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  NeeqMarketMakerQuote_default_instance_.DefaultConstruct();
  BrokerQueue_default_instance_.DefaultConstruct();
  MDQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDCashBondQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDFxBestQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  NeeqMarketMakerQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  BrokerQueue_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDQuote_2eproto_once_);
void protobuf_InitDefaults_MDQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDQuote_2eproto_impl);
}
void protobuf_AddDesc_MDQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\rMDQuote.proto\022\032com.htsc.mdc.insight.mo"
    "del\032\027ESecurityIDSource.proto\032\023ESecurityT"
    "ype.proto\"\261\010\n\007MDQuote\022\026\n\016HTSCSecurityID\030"
    "\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n"
    "\rDataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode"
    "\030\005 \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.h"
    "tsc.mdc.model.ESecurityIDSource\0227\n\014secur"
    "ityType\030\007 \001(\0162!.com.htsc.mdc.model.ESecu"
    "rityType\022\024\n\014ExchangeDate\030\010 \001(\005\022\024\n\014Exchan"
    "geTime\030\t \001(\005\022\r\n\005MaxPx\030\n \001(\003\022\r\n\005MinPx\030\013 \001"
    "(\003\022\021\n\tChannelNo\030\014 \001(\005\022\022\n\nApplSeqNum\030\r \001("
    "\003\022\022\n\nMDBookType\030\016 \001(\005\022\027\n\017MarketIndicator"
    "\030\017 \001(\t\022\023\n\013MarketDepth\030\020 \001(\005\022\025\n\rMDSubBook"
    "Type\030\021 \001(\005\022E\n\020MDCashBondQuotes\030\022 \003(\0132+.c"
    "om.htsc.mdc.insight.model.MDCashBondQuot"
    "e\022\035\n\025DataMultiplePowerOf10\030\023 \001(\005\022\031\n\021Vola"
    "tilitySurface\030\024 \001(\t\022A\n\016MDFxBestQuotes\030\025 "
    "\003(\0132).com.htsc.mdc.insight.model.MDFxBes"
    "tQuote\022I\n\017NeeqInvestorBuy\030\026 \003(\01320.com.ht"
    "sc.mdc.insight.model.NeeqMarketMakerQuot"
    "e\022J\n\020NeeqInvestorSell\030\027 \003(\01320.com.htsc.m"
    "dc.insight.model.NeeqMarketMakerQuote\022L\n"
    "\022NeeqMarketMakerBuy\030\030 \003(\01320.com.htsc.mdc"
    ".insight.model.NeeqMarketMakerQuote\022M\n\023N"
    "eeqMarketMakerSell\030\031 \003(\01320.com.htsc.mdc."
    "insight.model.NeeqMarketMakerQuote\022\?\n\016Bu"
    "yBrokerQueue\030\032 \003(\0132\'.com.htsc.mdc.insigh"
    "t.model.BrokerQueue\022@\n\017SellBrokerQueue\030\033"
    " \003(\0132\'.com.htsc.mdc.insight.model.Broker"
    "Queue\"\252\003\n\017MDCashBondQuote\022\021\n\tQuoteType\030\001"
    " \001(\005\022\014\n\004Side\030\002 \001(\005\022\022\n\nPriceLevel\030\003 \001(\005\022\017"
    "\n\007QuoteID\030\004 \001(\t\022\021\n\tQuoteDate\030\005 \001(\005\022\021\n\tQu"
    "oteTime\030\006 \001(\005\022\022\n\nCleanPrice\030\007 \001(\003\022\022\n\nDir"
    "tyPrice\030\010 \001(\003\022\026\n\016TotalFaceValue\030\t \001(\003\022\026\n"
    "\016ClearingMethod\030\n \001(\005\022\021\n\tSettlType\030\013 \001(\t"
    "\022\021\n\tSettlDate\030\014 \001(\005\022\025\n\rSettlCurrency\030\r \001"
    "(\t\022\027\n\017SettlCurrFxRate\030\016 \001(\003\022\021\n\tPartyRole"
    "\030\017 \001(\005\022\022\n\nTraderCode\030\020 \001(\t\022\025\n\rMaturityYi"
    "eld\030\021 \001(\003\022\024\n\014DeliveryType\030\022 \001(\005\022\027\n\017Trade"
    "rAccountID\030\023 \001(\t\022\020\n\010DataType\030\024 \001(\005\"\201\001\n\rM"
    "DFxBestQuote\022\014\n\004Side\030\001 \001(\005\022\r\n\005Price\030\002 \001("
    "\003\022\r\n\005Tenor\030\003 \001(\t\022\014\n\004Date\030\004 \001(\t\022\014\n\004Time\030\005"
    " \001(\t\022\027\n\017LiquidProviders\030\006 \003(\t\022\017\n\007LegSign"
    "\030\007 \001(\t\"\207\001\n\024NeeqMarketMakerQuote\022\020\n\010Seque"
    "nce\030\001 \001(\005\022\021\n\tQuoteType\030\002 \001(\t\022\r\n\005Price\030\003 "
    "\001(\003\022\020\n\010OrderQty\030\004 \001(\003\022\024\n\014GenerateTime\030\005 "
    "\001(\t\022\023\n\013BackupField\030\006 \001(\t\"S\n\013BrokerQueue\022"
    "\r\n\005Price\030\001 \001(\003\022\020\n\010OrderQty\030\002 \001(\003\022\021\n\tNumO"
    "rders\030\003 \001(\003\022\020\n\010BrokerID\030\004 \003(\005B0\n\032com.hts"
    "c.mdc.insight.modelB\rMDQuoteProtosH\001\240\001\001b"
    "\006proto3", 2007);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDQuote_2eproto_once_);
void protobuf_AddDesc_MDQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDQuote_2eproto_once_,
                 &protobuf_AddDesc_MDQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDQuote_2eproto {
  StaticDescriptorInitializer_MDQuote_2eproto() {
    protobuf_AddDesc_MDQuote_2eproto();
  }
} static_descriptor_initializer_MDQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDQuote::kHTSCSecurityIDFieldNumber;
const int MDQuote::kMDDateFieldNumber;
const int MDQuote::kMDTimeFieldNumber;
const int MDQuote::kDataTimestampFieldNumber;
const int MDQuote::kTradingPhaseCodeFieldNumber;
const int MDQuote::kSecurityIDSourceFieldNumber;
const int MDQuote::kSecurityTypeFieldNumber;
const int MDQuote::kExchangeDateFieldNumber;
const int MDQuote::kExchangeTimeFieldNumber;
const int MDQuote::kMaxPxFieldNumber;
const int MDQuote::kMinPxFieldNumber;
const int MDQuote::kChannelNoFieldNumber;
const int MDQuote::kApplSeqNumFieldNumber;
const int MDQuote::kMDBookTypeFieldNumber;
const int MDQuote::kMarketIndicatorFieldNumber;
const int MDQuote::kMarketDepthFieldNumber;
const int MDQuote::kMDSubBookTypeFieldNumber;
const int MDQuote::kMDCashBondQuotesFieldNumber;
const int MDQuote::kDataMultiplePowerOf10FieldNumber;
const int MDQuote::kVolatilitySurfaceFieldNumber;
const int MDQuote::kMDFxBestQuotesFieldNumber;
const int MDQuote::kNeeqInvestorBuyFieldNumber;
const int MDQuote::kNeeqInvestorSellFieldNumber;
const int MDQuote::kNeeqMarketMakerBuyFieldNumber;
const int MDQuote::kNeeqMarketMakerSellFieldNumber;
const int MDQuote::kBuyBrokerQueueFieldNumber;
const int MDQuote::kSellBrokerQueueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDQuote::MDQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDQuote)
}

void MDQuote::InitAsDefaultInstance() {
}

MDQuote::MDQuote(const MDQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDQuote)
}

void MDQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitysurface_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDQuote::~MDQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDQuote)
  SharedDtor();
}

void MDQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitysurface_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDQuote_descriptor_;
}

const MDQuote& MDQuote::default_instance() {
  protobuf_InitDefaults_MDQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDQuote> MDQuote_default_instance_;

MDQuote* MDQuote::New(::google::protobuf::Arena* arena) const {
  MDQuote* n = new MDQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangedate_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(exchangetime_, marketdepth_);
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(mdsubbooktype_, datamultiplepowerof10_);
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

  mdcashbondquotes_.Clear();
  mdfxbestquotes_.Clear();
  neeqinvestorbuy_.Clear();
  neeqinvestorsell_.Clear();
  neeqmarketmakerbuy_.Clear();
  neeqmarketmakersell_.Clear();
  buybrokerqueue_.Clear();
  sellbrokerqueue_.Clear();
}

bool MDQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 9;
      case 9: {
        if (tag == 72) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 10;
      case 10: {
        if (tag == 80) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 11;
      case 11: {
        if (tag == 88) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 12;
      case 12: {
        if (tag == 96) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 13;
      case 13: {
        if (tag == 104) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_MDBookType;
        break;
      }

      // optional int32 MDBookType = 14;
      case 14: {
        if (tag == 112) {
         parse_MDBookType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdbooktype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 15;
      case 15: {
        if (tag == 122) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQuote.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_MarketDepth;
        break;
      }

      // optional int32 MarketDepth = 16;
      case 16: {
        if (tag == 128) {
         parse_MarketDepth:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &marketdepth_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_MDSubBookType;
        break;
      }

      // optional int32 MDSubBookType = 17;
      case 17: {
        if (tag == 136) {
         parse_MDSubBookType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdsubbooktype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_MDCashBondQuotes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
      case 18: {
        if (tag == 146) {
         parse_MDCashBondQuotes:
          DO_(input->IncrementRecursionDepth());
         parse_loop_MDCashBondQuotes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_mdcashbondquotes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_loop_MDCashBondQuotes;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(152)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 19;
      case 19: {
        if (tag == 152) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_VolatilitySurface;
        break;
      }

      // optional string VolatilitySurface = 20;
      case 20: {
        if (tag == 162) {
         parse_VolatilitySurface:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_volatilitysurface()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->volatilitysurface().data(), this->volatilitysurface().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDQuote.VolatilitySurface"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_MDFxBestQuotes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
      case 21: {
        if (tag == 170) {
         parse_MDFxBestQuotes:
          DO_(input->IncrementRecursionDepth());
         parse_loop_MDFxBestQuotes:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_mdfxbestquotes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_loop_MDFxBestQuotes;
        if (input->ExpectTag(178)) goto parse_loop_NeeqInvestorBuy;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
      case 22: {
        if (tag == 178) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_NeeqInvestorBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_neeqinvestorbuy()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_loop_NeeqInvestorBuy;
        if (input->ExpectTag(186)) goto parse_loop_NeeqInvestorSell;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
      case 23: {
        if (tag == 186) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_NeeqInvestorSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_neeqinvestorsell()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_loop_NeeqInvestorSell;
        if (input->ExpectTag(194)) goto parse_loop_NeeqMarketMakerBuy;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
      case 24: {
        if (tag == 194) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_NeeqMarketMakerBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_neeqmarketmakerbuy()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_loop_NeeqMarketMakerBuy;
        if (input->ExpectTag(202)) goto parse_loop_NeeqMarketMakerSell;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
      case 25: {
        if (tag == 202) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_NeeqMarketMakerSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_neeqmarketmakersell()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_loop_NeeqMarketMakerSell;
        if (input->ExpectTag(210)) goto parse_loop_BuyBrokerQueue;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
      case 26: {
        if (tag == 210) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_BuyBrokerQueue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_buybrokerqueue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_loop_BuyBrokerQueue;
        if (input->ExpectTag(218)) goto parse_loop_SellBrokerQueue;
        input->UnsafeDecrementRecursionDepth();
        break;
      }

      // repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
      case 27: {
        if (tag == 218) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_SellBrokerQueue:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_sellbrokerqueue()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_loop_SellBrokerQueue;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDQuote)
  return false;
#undef DO_
}

void MDQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->exchangetime(), output);
  }

  // optional int64 MaxPx = 10;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->maxpx(), output);
  }

  // optional int64 MinPx = 11;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->minpx(), output);
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->channelno(), output);
  }

  // optional int64 ApplSeqNum = 13;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->applseqnum(), output);
  }

  // optional int32 MDBookType = 14;
  if (this->mdbooktype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->mdbooktype(), output);
  }

  // optional string MarketIndicator = 15;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->marketindicator(), output);
  }

  // optional int32 MarketDepth = 16;
  if (this->marketdepth() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->marketdepth(), output);
  }

  // optional int32 MDSubBookType = 17;
  if (this->mdsubbooktype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->mdsubbooktype(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
  for (unsigned int i = 0, n = this->mdcashbondquotes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, this->mdcashbondquotes(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->datamultiplepowerof10(), output);
  }

  // optional string VolatilitySurface = 20;
  if (this->volatilitysurface().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitysurface().data(), this->volatilitysurface().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.VolatilitySurface");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->volatilitysurface(), output);
  }

  // repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
  for (unsigned int i = 0, n = this->mdfxbestquotes_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, this->mdfxbestquotes(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
  for (unsigned int i = 0, n = this->neeqinvestorbuy_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, this->neeqinvestorbuy(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
  for (unsigned int i = 0, n = this->neeqinvestorsell_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, this->neeqinvestorsell(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
  for (unsigned int i = 0, n = this->neeqmarketmakerbuy_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      24, this->neeqmarketmakerbuy(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
  for (unsigned int i = 0, n = this->neeqmarketmakersell_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      25, this->neeqmarketmakersell(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
  for (unsigned int i = 0, n = this->buybrokerqueue_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      26, this->buybrokerqueue(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
  for (unsigned int i = 0, n = this->sellbrokerqueue_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      27, this->sellbrokerqueue(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDQuote)
}

::google::protobuf::uint8* MDQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->exchangetime(), target);
  }

  // optional int64 MaxPx = 10;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->maxpx(), target);
  }

  // optional int64 MinPx = 11;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->minpx(), target);
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->channelno(), target);
  }

  // optional int64 ApplSeqNum = 13;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->applseqnum(), target);
  }

  // optional int32 MDBookType = 14;
  if (this->mdbooktype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->mdbooktype(), target);
  }

  // optional string MarketIndicator = 15;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->marketindicator(), target);
  }

  // optional int32 MarketDepth = 16;
  if (this->marketdepth() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->marketdepth(), target);
  }

  // optional int32 MDSubBookType = 17;
  if (this->mdsubbooktype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->mdsubbooktype(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
  for (unsigned int i = 0, n = this->mdcashbondquotes_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, this->mdcashbondquotes(i), false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->datamultiplepowerof10(), target);
  }

  // optional string VolatilitySurface = 20;
  if (this->volatilitysurface().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitysurface().data(), this->volatilitysurface().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDQuote.VolatilitySurface");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->volatilitysurface(), target);
  }

  // repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
  for (unsigned int i = 0, n = this->mdfxbestquotes_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, this->mdfxbestquotes(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
  for (unsigned int i = 0, n = this->neeqinvestorbuy_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, this->neeqinvestorbuy(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
  for (unsigned int i = 0, n = this->neeqinvestorsell_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, this->neeqinvestorsell(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
  for (unsigned int i = 0, n = this->neeqmarketmakerbuy_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        24, this->neeqmarketmakerbuy(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
  for (unsigned int i = 0, n = this->neeqmarketmakersell_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        25, this->neeqmarketmakersell(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
  for (unsigned int i = 0, n = this->buybrokerqueue_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        26, this->buybrokerqueue(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
  for (unsigned int i = 0, n = this->sellbrokerqueue_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        27, this->sellbrokerqueue(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDQuote)
  return target;
}

size_t MDQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 8;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 9;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 MaxPx = 10;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 11;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int32 ChannelNo = 12;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 ApplSeqNum = 13;
  if (this->applseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional int32 MDBookType = 14;
  if (this->mdbooktype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdbooktype());
  }

  // optional string MarketIndicator = 15;
  if (this->marketindicator().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  // optional int32 MarketDepth = 16;
  if (this->marketdepth() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->marketdepth());
  }

  // optional int32 MDSubBookType = 17;
  if (this->mdsubbooktype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdsubbooktype());
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string VolatilitySurface = 20;
  if (this->volatilitysurface().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->volatilitysurface());
  }

  // repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
  {
    unsigned int count = this->mdcashbondquotes_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->mdcashbondquotes(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
  {
    unsigned int count = this->mdfxbestquotes_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->mdfxbestquotes(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
  {
    unsigned int count = this->neeqinvestorbuy_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->neeqinvestorbuy(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
  {
    unsigned int count = this->neeqinvestorsell_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->neeqinvestorsell(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
  {
    unsigned int count = this->neeqmarketmakerbuy_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->neeqmarketmakerbuy(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
  {
    unsigned int count = this->neeqmarketmakersell_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->neeqmarketmakersell(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
  {
    unsigned int count = this->buybrokerqueue_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->buybrokerqueue(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
  {
    unsigned int count = this->sellbrokerqueue_size();
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->sellbrokerqueue(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDQuote::MergeFrom(const MDQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDQuote::UnsafeMergeFrom(const MDQuote& from) {
  GOOGLE_DCHECK(&from != this);
  mdcashbondquotes_.MergeFrom(from.mdcashbondquotes_);
  mdfxbestquotes_.MergeFrom(from.mdfxbestquotes_);
  neeqinvestorbuy_.MergeFrom(from.neeqinvestorbuy_);
  neeqinvestorsell_.MergeFrom(from.neeqinvestorsell_);
  neeqmarketmakerbuy_.MergeFrom(from.neeqmarketmakerbuy_);
  neeqmarketmakersell_.MergeFrom(from.neeqmarketmakersell_);
  buybrokerqueue_.MergeFrom(from.buybrokerqueue_);
  sellbrokerqueue_.MergeFrom(from.sellbrokerqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.mdbooktype() != 0) {
    set_mdbooktype(from.mdbooktype());
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
  if (from.marketdepth() != 0) {
    set_marketdepth(from.marketdepth());
  }
  if (from.mdsubbooktype() != 0) {
    set_mdsubbooktype(from.mdsubbooktype());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.volatilitysurface().size() > 0) {

    volatilitysurface_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.volatilitysurface_);
  }
}

void MDQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDQuote::CopyFrom(const MDQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDQuote::IsInitialized() const {

  return true;
}

void MDQuote::Swap(MDQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDQuote::InternalSwap(MDQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(channelno_, other->channelno_);
  std::swap(applseqnum_, other->applseqnum_);
  std::swap(mdbooktype_, other->mdbooktype_);
  marketindicator_.Swap(&other->marketindicator_);
  std::swap(marketdepth_, other->marketdepth_);
  std::swap(mdsubbooktype_, other->mdsubbooktype_);
  mdcashbondquotes_.UnsafeArenaSwap(&other->mdcashbondquotes_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  volatilitysurface_.Swap(&other->volatilitysurface_);
  mdfxbestquotes_.UnsafeArenaSwap(&other->mdfxbestquotes_);
  neeqinvestorbuy_.UnsafeArenaSwap(&other->neeqinvestorbuy_);
  neeqinvestorsell_.UnsafeArenaSwap(&other->neeqinvestorsell_);
  neeqmarketmakerbuy_.UnsafeArenaSwap(&other->neeqmarketmakerbuy_);
  neeqmarketmakersell_.UnsafeArenaSwap(&other->neeqmarketmakersell_);
  buybrokerqueue_.UnsafeArenaSwap(&other->buybrokerqueue_);
  sellbrokerqueue_.UnsafeArenaSwap(&other->sellbrokerqueue_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDQuote_descriptor_;
  metadata.reflection = MDQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDQuote

// optional string HTSCSecurityID = 1;
void MDQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
void MDQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
void MDQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}
::std::string* MDQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDDate)
  return mddate_;
}
void MDQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDTime)
  return mdtime_;
}
void MDQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.DataTimestamp)
  return datatimestamp_;
}
void MDQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
void MDQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
void MDQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}
::std::string* MDQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.securityType)
}

// optional int32 ExchangeDate = 8;
void MDQuote::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDQuote::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ExchangeDate)
  return exchangedate_;
}
void MDQuote::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ExchangeDate)
}

// optional int32 ExchangeTime = 9;
void MDQuote::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDQuote::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ExchangeTime)
  return exchangetime_;
}
void MDQuote::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ExchangeTime)
}

// optional int64 MaxPx = 10;
void MDQuote::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQuote::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MaxPx)
  return maxpx_;
}
void MDQuote::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MaxPx)
}

// optional int64 MinPx = 11;
void MDQuote::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQuote::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MinPx)
  return minpx_;
}
void MDQuote::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MinPx)
}

// optional int32 ChannelNo = 12;
void MDQuote::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDQuote::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ChannelNo)
  return channelno_;
}
void MDQuote::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ChannelNo)
}

// optional int64 ApplSeqNum = 13;
void MDQuote::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDQuote::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.ApplSeqNum)
  return applseqnum_;
}
void MDQuote::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.ApplSeqNum)
}

// optional int32 MDBookType = 14;
void MDQuote::clear_mdbooktype() {
  mdbooktype_ = 0;
}
::google::protobuf::int32 MDQuote::mdbooktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDBookType)
  return mdbooktype_;
}
void MDQuote::set_mdbooktype(::google::protobuf::int32 value) {
  
  mdbooktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDBookType)
}

// optional string MarketIndicator = 15;
void MDQuote::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQuote::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
void MDQuote::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
void MDQuote::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}
::std::string* MDQuote::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQuote::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.MarketIndicator)
}

// optional int32 MarketDepth = 16;
void MDQuote::clear_marketdepth() {
  marketdepth_ = 0;
}
::google::protobuf::int32 MDQuote::marketdepth() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MarketDepth)
  return marketdepth_;
}
void MDQuote::set_marketdepth(::google::protobuf::int32 value) {
  
  marketdepth_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MarketDepth)
}

// optional int32 MDSubBookType = 17;
void MDQuote::clear_mdsubbooktype() {
  mdsubbooktype_ = 0;
}
::google::protobuf::int32 MDQuote::mdsubbooktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDSubBookType)
  return mdsubbooktype_;
}
void MDQuote::set_mdsubbooktype(::google::protobuf::int32 value) {
  
  mdsubbooktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.MDSubBookType)
}

// repeated .com.htsc.mdc.insight.model.MDCashBondQuote MDCashBondQuotes = 18;
int MDQuote::mdcashbondquotes_size() const {
  return mdcashbondquotes_.size();
}
void MDQuote::clear_mdcashbondquotes() {
  mdcashbondquotes_.Clear();
}
const ::com::htsc::mdc::insight::model::MDCashBondQuote& MDQuote::mdcashbondquotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Get(index);
}
::com::htsc::mdc::insight::model::MDCashBondQuote* MDQuote::mutable_mdcashbondquotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDCashBondQuote* MDQuote::add_mdcashbondquotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >*
MDQuote::mutable_mdcashbondquotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return &mdcashbondquotes_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDCashBondQuote >&
MDQuote::mdcashbondquotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.MDCashBondQuotes)
  return mdcashbondquotes_;
}

// optional int32 DataMultiplePowerOf10 = 19;
void MDQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.DataMultiplePowerOf10)
}

// optional string VolatilitySurface = 20;
void MDQuote::clear_volatilitysurface() {
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDQuote::volatilitysurface() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  return volatilitysurface_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_volatilitysurface(const ::std::string& value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
void MDQuote::set_volatilitysurface(const char* value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
void MDQuote::set_volatilitysurface(const char* value, size_t size) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}
::std::string* MDQuote::mutable_volatilitysurface() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  return volatilitysurface_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDQuote::release_volatilitysurface() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
  
  return volatilitysurface_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDQuote::set_allocated_volatilitysurface(::std::string* volatilitysurface) {
  if (volatilitysurface != NULL) {
    
  } else {
    
  }
  volatilitysurface_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitysurface);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDQuote.VolatilitySurface)
}

// repeated .com.htsc.mdc.insight.model.MDFxBestQuote MDFxBestQuotes = 21;
int MDQuote::mdfxbestquotes_size() const {
  return mdfxbestquotes_.size();
}
void MDQuote::clear_mdfxbestquotes() {
  mdfxbestquotes_.Clear();
}
const ::com::htsc::mdc::insight::model::MDFxBestQuote& MDQuote::mdfxbestquotes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Get(index);
}
::com::htsc::mdc::insight::model::MDFxBestQuote* MDQuote::mutable_mdfxbestquotes(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Mutable(index);
}
::com::htsc::mdc::insight::model::MDFxBestQuote* MDQuote::add_mdfxbestquotes() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >*
MDQuote::mutable_mdfxbestquotes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return &mdfxbestquotes_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MDFxBestQuote >&
MDQuote::mdfxbestquotes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.MDFxBestQuotes)
  return mdfxbestquotes_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorBuy = 22;
int MDQuote::neeqinvestorbuy_size() const {
  return neeqinvestorbuy_.size();
}
void MDQuote::clear_neeqinvestorbuy() {
  neeqinvestorbuy_.Clear();
}
const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqinvestorbuy(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Get(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqinvestorbuy(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Mutable(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqinvestorbuy() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqinvestorbuy() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return &neeqinvestorbuy_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqinvestorbuy() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorBuy)
  return neeqinvestorbuy_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqInvestorSell = 23;
int MDQuote::neeqinvestorsell_size() const {
  return neeqinvestorsell_.size();
}
void MDQuote::clear_neeqinvestorsell() {
  neeqinvestorsell_.Clear();
}
const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqinvestorsell(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Get(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqinvestorsell(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Mutable(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqinvestorsell() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqinvestorsell() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return &neeqinvestorsell_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqinvestorsell() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqInvestorSell)
  return neeqinvestorsell_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerBuy = 24;
int MDQuote::neeqmarketmakerbuy_size() const {
  return neeqmarketmakerbuy_.size();
}
void MDQuote::clear_neeqmarketmakerbuy() {
  neeqmarketmakerbuy_.Clear();
}
const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqmarketmakerbuy(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Get(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqmarketmakerbuy(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Mutable(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqmarketmakerbuy() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqmarketmakerbuy() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return &neeqmarketmakerbuy_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqmarketmakerbuy() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerBuy)
  return neeqmarketmakerbuy_;
}

// repeated .com.htsc.mdc.insight.model.NeeqMarketMakerQuote NeeqMarketMakerSell = 25;
int MDQuote::neeqmarketmakersell_size() const {
  return neeqmarketmakersell_.size();
}
void MDQuote::clear_neeqmarketmakersell() {
  neeqmarketmakersell_.Clear();
}
const ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote& MDQuote::neeqmarketmakersell(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Get(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::mutable_neeqmarketmakersell(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Mutable(index);
}
::com::htsc::mdc::insight::model::NeeqMarketMakerQuote* MDQuote::add_neeqmarketmakersell() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >*
MDQuote::mutable_neeqmarketmakersell() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return &neeqmarketmakersell_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::NeeqMarketMakerQuote >&
MDQuote::neeqmarketmakersell() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.NeeqMarketMakerSell)
  return neeqmarketmakersell_;
}

// repeated .com.htsc.mdc.insight.model.BrokerQueue BuyBrokerQueue = 26;
int MDQuote::buybrokerqueue_size() const {
  return buybrokerqueue_.size();
}
void MDQuote::clear_buybrokerqueue() {
  buybrokerqueue_.Clear();
}
const ::com::htsc::mdc::insight::model::BrokerQueue& MDQuote::buybrokerqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Get(index);
}
::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::mutable_buybrokerqueue(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Mutable(index);
}
::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::add_buybrokerqueue() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
MDQuote::mutable_buybrokerqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return &buybrokerqueue_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
MDQuote::buybrokerqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.BuyBrokerQueue)
  return buybrokerqueue_;
}

// repeated .com.htsc.mdc.insight.model.BrokerQueue SellBrokerQueue = 27;
int MDQuote::sellbrokerqueue_size() const {
  return sellbrokerqueue_.size();
}
void MDQuote::clear_sellbrokerqueue() {
  sellbrokerqueue_.Clear();
}
const ::com::htsc::mdc::insight::model::BrokerQueue& MDQuote::sellbrokerqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Get(index);
}
::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::mutable_sellbrokerqueue(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Mutable(index);
}
::com::htsc::mdc::insight::model::BrokerQueue* MDQuote::add_sellbrokerqueue() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >*
MDQuote::mutable_sellbrokerqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return &sellbrokerqueue_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::BrokerQueue >&
MDQuote::sellbrokerqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDQuote.SellBrokerQueue)
  return sellbrokerqueue_;
}

inline const MDQuote* MDQuote::internal_default_instance() {
  return &MDQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCashBondQuote::kQuoteTypeFieldNumber;
const int MDCashBondQuote::kSideFieldNumber;
const int MDCashBondQuote::kPriceLevelFieldNumber;
const int MDCashBondQuote::kQuoteIDFieldNumber;
const int MDCashBondQuote::kQuoteDateFieldNumber;
const int MDCashBondQuote::kQuoteTimeFieldNumber;
const int MDCashBondQuote::kCleanPriceFieldNumber;
const int MDCashBondQuote::kDirtyPriceFieldNumber;
const int MDCashBondQuote::kTotalFaceValueFieldNumber;
const int MDCashBondQuote::kClearingMethodFieldNumber;
const int MDCashBondQuote::kSettlTypeFieldNumber;
const int MDCashBondQuote::kSettlDateFieldNumber;
const int MDCashBondQuote::kSettlCurrencyFieldNumber;
const int MDCashBondQuote::kSettlCurrFxRateFieldNumber;
const int MDCashBondQuote::kPartyRoleFieldNumber;
const int MDCashBondQuote::kTraderCodeFieldNumber;
const int MDCashBondQuote::kMaturityYieldFieldNumber;
const int MDCashBondQuote::kDeliveryTypeFieldNumber;
const int MDCashBondQuote::kTraderAccountIDFieldNumber;
const int MDCashBondQuote::kDataTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCashBondQuote::MDCashBondQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCashBondQuote)
}

void MDCashBondQuote::InitAsDefaultInstance() {
}

MDCashBondQuote::MDCashBondQuote(const MDCashBondQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCashBondQuote)
}

void MDCashBondQuote::SharedCtor() {
  quoteid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settltype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settlcurrency_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  traderaccountid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&quotetype_, 0, reinterpret_cast<char*>(&datatype_) -
    reinterpret_cast<char*>(&quotetype_) + sizeof(datatype_));
  _cached_size_ = 0;
}

MDCashBondQuote::~MDCashBondQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCashBondQuote)
  SharedDtor();
}

void MDCashBondQuote::SharedDtor() {
  quoteid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settltype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settlcurrency_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  traderaccountid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDCashBondQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCashBondQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCashBondQuote_descriptor_;
}

const MDCashBondQuote& MDCashBondQuote::default_instance() {
  protobuf_InitDefaults_MDQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCashBondQuote> MDCashBondQuote_default_instance_;

MDCashBondQuote* MDCashBondQuote::New(::google::protobuf::Arena* arena) const {
  MDCashBondQuote* n = new MDCashBondQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCashBondQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCashBondQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCashBondQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCashBondQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(quotetype_, quotetime_);
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(clearingmethod_, settlcurrfxrate_);
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  settlcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(maturityyield_, datatype_);
  traderaccountid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDCashBondQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 QuoteType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Side;
        break;
      }

      // optional int32 Side = 2;
      case 2: {
        if (tag == 16) {
         parse_Side:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &side_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PriceLevel;
        break;
      }

      // optional int32 PriceLevel = 3;
      case 3: {
        if (tag == 24) {
         parse_PriceLevel:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &pricelevel_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_QuoteID;
        break;
      }

      // optional string QuoteID = 4;
      case 4: {
        if (tag == 34) {
         parse_QuoteID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quoteid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quoteid().data(), this->quoteid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_QuoteDate;
        break;
      }

      // optional int32 QuoteDate = 5;
      case 5: {
        if (tag == 40) {
         parse_QuoteDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_QuoteTime;
        break;
      }

      // optional int32 QuoteTime = 6;
      case 6: {
        if (tag == 48) {
         parse_QuoteTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_CleanPrice;
        break;
      }

      // optional int64 CleanPrice = 7;
      case 7: {
        if (tag == 56) {
         parse_CleanPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &cleanprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_DirtyPrice;
        break;
      }

      // optional int64 DirtyPrice = 8;
      case 8: {
        if (tag == 64) {
         parse_DirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TotalFaceValue;
        break;
      }

      // optional int64 TotalFaceValue = 9;
      case 9: {
        if (tag == 72) {
         parse_TotalFaceValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalfacevalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ClearingMethod;
        break;
      }

      // optional int32 ClearingMethod = 10;
      case 10: {
        if (tag == 80) {
         parse_ClearingMethod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &clearingmethod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_SettlType;
        break;
      }

      // optional string SettlType = 11;
      case 11: {
        if (tag == 90) {
         parse_SettlType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settltype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settltype().data(), this->settltype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCashBondQuote.SettlType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_SettlDate;
        break;
      }

      // optional int32 SettlDate = 12;
      case 12: {
        if (tag == 96) {
         parse_SettlDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settldate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_SettlCurrency;
        break;
      }

      // optional string SettlCurrency = 13;
      case 13: {
        if (tag == 106) {
         parse_SettlCurrency:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_settlcurrency()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->settlcurrency().data(), this->settlcurrency().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_SettlCurrFxRate;
        break;
      }

      // optional int64 SettlCurrFxRate = 14;
      case 14: {
        if (tag == 112) {
         parse_SettlCurrFxRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settlcurrfxrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_PartyRole;
        break;
      }

      // optional int32 PartyRole = 15;
      case 15: {
        if (tag == 120) {
         parse_PartyRole:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &partyrole_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_TraderCode;
        break;
      }

      // optional string TraderCode = 16;
      case 16: {
        if (tag == 130) {
         parse_TraderCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradercode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradercode().data(), this->tradercode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_MaturityYield;
        break;
      }

      // optional int64 MaturityYield = 17;
      case 17: {
        if (tag == 136) {
         parse_MaturityYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maturityyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_DeliveryType;
        break;
      }

      // optional int32 DeliveryType = 18;
      case 18: {
        if (tag == 144) {
         parse_DeliveryType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &deliverytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_TraderAccountID;
        break;
      }

      // optional string TraderAccountID = 19;
      case 19: {
        if (tag == 154) {
         parse_TraderAccountID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_traderaccountid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->traderaccountid().data(), this->traderaccountid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DataType;
        break;
      }

      // optional int32 DataType = 20;
      case 20: {
        if (tag == 160) {
         parse_DataType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCashBondQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCashBondQuote)
  return false;
#undef DO_
}

void MDCashBondQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  // optional int32 QuoteType = 1;
  if (this->quotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->quotetype(), output);
  }

  // optional int32 Side = 2;
  if (this->side() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->side(), output);
  }

  // optional int32 PriceLevel = 3;
  if (this->pricelevel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->pricelevel(), output);
  }

  // optional string QuoteID = 4;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->quoteid(), output);
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->quotedate(), output);
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->quotetime(), output);
  }

  // optional int64 CleanPrice = 7;
  if (this->cleanprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->cleanprice(), output);
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->dirtyprice(), output);
  }

  // optional int64 TotalFaceValue = 9;
  if (this->totalfacevalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->totalfacevalue(), output);
  }

  // optional int32 ClearingMethod = 10;
  if (this->clearingmethod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->clearingmethod(), output);
  }

  // optional string SettlType = 11;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.SettlType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->settltype(), output);
  }

  // optional int32 SettlDate = 12;
  if (this->settldate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->settldate(), output);
  }

  // optional string SettlCurrency = 13;
  if (this->settlcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settlcurrency().data(), this->settlcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->settlcurrency(), output);
  }

  // optional int64 SettlCurrFxRate = 14;
  if (this->settlcurrfxrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->settlcurrfxrate(), output);
  }

  // optional int32 PartyRole = 15;
  if (this->partyrole() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->partyrole(), output);
  }

  // optional string TraderCode = 16;
  if (this->tradercode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradercode().data(), this->tradercode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->tradercode(), output);
  }

  // optional int64 MaturityYield = 17;
  if (this->maturityyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->maturityyield(), output);
  }

  // optional int32 DeliveryType = 18;
  if (this->deliverytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->deliverytype(), output);
  }

  // optional string TraderAccountID = 19;
  if (this->traderaccountid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->traderaccountid().data(), this->traderaccountid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->traderaccountid(), output);
  }

  // optional int32 DataType = 20;
  if (this->datatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->datatype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCashBondQuote)
}

::google::protobuf::uint8* MDCashBondQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  // optional int32 QuoteType = 1;
  if (this->quotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->quotetype(), target);
  }

  // optional int32 Side = 2;
  if (this->side() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->side(), target);
  }

  // optional int32 PriceLevel = 3;
  if (this->pricelevel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->pricelevel(), target);
  }

  // optional string QuoteID = 4;
  if (this->quoteid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quoteid().data(), this->quoteid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->quoteid(), target);
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->quotedate(), target);
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->quotetime(), target);
  }

  // optional int64 CleanPrice = 7;
  if (this->cleanprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->cleanprice(), target);
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->dirtyprice(), target);
  }

  // optional int64 TotalFaceValue = 9;
  if (this->totalfacevalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->totalfacevalue(), target);
  }

  // optional int32 ClearingMethod = 10;
  if (this->clearingmethod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->clearingmethod(), target);
  }

  // optional string SettlType = 11;
  if (this->settltype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settltype().data(), this->settltype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.SettlType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->settltype(), target);
  }

  // optional int32 SettlDate = 12;
  if (this->settldate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->settldate(), target);
  }

  // optional string SettlCurrency = 13;
  if (this->settlcurrency().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->settlcurrency().data(), this->settlcurrency().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->settlcurrency(), target);
  }

  // optional int64 SettlCurrFxRate = 14;
  if (this->settlcurrfxrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->settlcurrfxrate(), target);
  }

  // optional int32 PartyRole = 15;
  if (this->partyrole() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->partyrole(), target);
  }

  // optional string TraderCode = 16;
  if (this->tradercode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradercode().data(), this->tradercode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->tradercode(), target);
  }

  // optional int64 MaturityYield = 17;
  if (this->maturityyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->maturityyield(), target);
  }

  // optional int32 DeliveryType = 18;
  if (this->deliverytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->deliverytype(), target);
  }

  // optional string TraderAccountID = 19;
  if (this->traderaccountid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->traderaccountid().data(), this->traderaccountid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->traderaccountid(), target);
  }

  // optional int32 DataType = 20;
  if (this->datatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->datatype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCashBondQuote)
  return target;
}

size_t MDCashBondQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  size_t total_size = 0;

  // optional int32 QuoteType = 1;
  if (this->quotetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetype());
  }

  // optional int32 Side = 2;
  if (this->side() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->side());
  }

  // optional int32 PriceLevel = 3;
  if (this->pricelevel() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->pricelevel());
  }

  // optional string QuoteID = 4;
  if (this->quoteid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quoteid());
  }

  // optional int32 QuoteDate = 5;
  if (this->quotedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotedate());
  }

  // optional int32 QuoteTime = 6;
  if (this->quotetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotetime());
  }

  // optional int64 CleanPrice = 7;
  if (this->cleanprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->cleanprice());
  }

  // optional int64 DirtyPrice = 8;
  if (this->dirtyprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dirtyprice());
  }

  // optional int64 TotalFaceValue = 9;
  if (this->totalfacevalue() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalfacevalue());
  }

  // optional int32 ClearingMethod = 10;
  if (this->clearingmethod() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->clearingmethod());
  }

  // optional string SettlType = 11;
  if (this->settltype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settltype());
  }

  // optional int32 SettlDate = 12;
  if (this->settldate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settldate());
  }

  // optional string SettlCurrency = 13;
  if (this->settlcurrency().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->settlcurrency());
  }

  // optional int64 SettlCurrFxRate = 14;
  if (this->settlcurrfxrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settlcurrfxrate());
  }

  // optional int32 PartyRole = 15;
  if (this->partyrole() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->partyrole());
  }

  // optional string TraderCode = 16;
  if (this->tradercode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradercode());
  }

  // optional int64 MaturityYield = 17;
  if (this->maturityyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maturityyield());
  }

  // optional int32 DeliveryType = 18;
  if (this->deliverytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->deliverytype());
  }

  // optional string TraderAccountID = 19;
  if (this->traderaccountid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->traderaccountid());
  }

  // optional int32 DataType = 20;
  if (this->datatype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datatype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCashBondQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCashBondQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCashBondQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCashBondQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCashBondQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDCashBondQuote::MergeFrom(const MDCashBondQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCashBondQuote::UnsafeMergeFrom(const MDCashBondQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.quotetype() != 0) {
    set_quotetype(from.quotetype());
  }
  if (from.side() != 0) {
    set_side(from.side());
  }
  if (from.pricelevel() != 0) {
    set_pricelevel(from.pricelevel());
  }
  if (from.quoteid().size() > 0) {

    quoteid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quoteid_);
  }
  if (from.quotedate() != 0) {
    set_quotedate(from.quotedate());
  }
  if (from.quotetime() != 0) {
    set_quotetime(from.quotetime());
  }
  if (from.cleanprice() != 0) {
    set_cleanprice(from.cleanprice());
  }
  if (from.dirtyprice() != 0) {
    set_dirtyprice(from.dirtyprice());
  }
  if (from.totalfacevalue() != 0) {
    set_totalfacevalue(from.totalfacevalue());
  }
  if (from.clearingmethod() != 0) {
    set_clearingmethod(from.clearingmethod());
  }
  if (from.settltype().size() > 0) {

    settltype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settltype_);
  }
  if (from.settldate() != 0) {
    set_settldate(from.settldate());
  }
  if (from.settlcurrency().size() > 0) {

    settlcurrency_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.settlcurrency_);
  }
  if (from.settlcurrfxrate() != 0) {
    set_settlcurrfxrate(from.settlcurrfxrate());
  }
  if (from.partyrole() != 0) {
    set_partyrole(from.partyrole());
  }
  if (from.tradercode().size() > 0) {

    tradercode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradercode_);
  }
  if (from.maturityyield() != 0) {
    set_maturityyield(from.maturityyield());
  }
  if (from.deliverytype() != 0) {
    set_deliverytype(from.deliverytype());
  }
  if (from.traderaccountid().size() > 0) {

    traderaccountid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.traderaccountid_);
  }
  if (from.datatype() != 0) {
    set_datatype(from.datatype());
  }
}

void MDCashBondQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCashBondQuote::CopyFrom(const MDCashBondQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCashBondQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCashBondQuote::IsInitialized() const {

  return true;
}

void MDCashBondQuote::Swap(MDCashBondQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCashBondQuote::InternalSwap(MDCashBondQuote* other) {
  std::swap(quotetype_, other->quotetype_);
  std::swap(side_, other->side_);
  std::swap(pricelevel_, other->pricelevel_);
  quoteid_.Swap(&other->quoteid_);
  std::swap(quotedate_, other->quotedate_);
  std::swap(quotetime_, other->quotetime_);
  std::swap(cleanprice_, other->cleanprice_);
  std::swap(dirtyprice_, other->dirtyprice_);
  std::swap(totalfacevalue_, other->totalfacevalue_);
  std::swap(clearingmethod_, other->clearingmethod_);
  settltype_.Swap(&other->settltype_);
  std::swap(settldate_, other->settldate_);
  settlcurrency_.Swap(&other->settlcurrency_);
  std::swap(settlcurrfxrate_, other->settlcurrfxrate_);
  std::swap(partyrole_, other->partyrole_);
  tradercode_.Swap(&other->tradercode_);
  std::swap(maturityyield_, other->maturityyield_);
  std::swap(deliverytype_, other->deliverytype_);
  traderaccountid_.Swap(&other->traderaccountid_);
  std::swap(datatype_, other->datatype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCashBondQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCashBondQuote_descriptor_;
  metadata.reflection = MDCashBondQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCashBondQuote

// optional int32 QuoteType = 1;
void MDCashBondQuote::clear_quotetype() {
  quotetype_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteType)
  return quotetype_;
}
void MDCashBondQuote::set_quotetype(::google::protobuf::int32 value) {
  
  quotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteType)
}

// optional int32 Side = 2;
void MDCashBondQuote::clear_side() {
  side_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.Side)
  return side_;
}
void MDCashBondQuote::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.Side)
}

// optional int32 PriceLevel = 3;
void MDCashBondQuote::clear_pricelevel() {
  pricelevel_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::pricelevel() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.PriceLevel)
  return pricelevel_;
}
void MDCashBondQuote::set_pricelevel(::google::protobuf::int32 value) {
  
  pricelevel_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.PriceLevel)
}

// optional string QuoteID = 4;
void MDCashBondQuote::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCashBondQuote::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
void MDCashBondQuote::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
void MDCashBondQuote::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}
::std::string* MDCashBondQuote::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCashBondQuote::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteID)
}

// optional int32 QuoteDate = 5;
void MDCashBondQuote::clear_quotedate() {
  quotedate_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::quotedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteDate)
  return quotedate_;
}
void MDCashBondQuote::set_quotedate(::google::protobuf::int32 value) {
  
  quotedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteDate)
}

// optional int32 QuoteTime = 6;
void MDCashBondQuote::clear_quotetime() {
  quotetime_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::quotetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteTime)
  return quotetime_;
}
void MDCashBondQuote::set_quotetime(::google::protobuf::int32 value) {
  
  quotetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.QuoteTime)
}

// optional int64 CleanPrice = 7;
void MDCashBondQuote::clear_cleanprice() {
  cleanprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCashBondQuote::cleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.CleanPrice)
  return cleanprice_;
}
void MDCashBondQuote::set_cleanprice(::google::protobuf::int64 value) {
  
  cleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.CleanPrice)
}

// optional int64 DirtyPrice = 8;
void MDCashBondQuote::clear_dirtyprice() {
  dirtyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCashBondQuote::dirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DirtyPrice)
  return dirtyprice_;
}
void MDCashBondQuote::set_dirtyprice(::google::protobuf::int64 value) {
  
  dirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DirtyPrice)
}

// optional int64 TotalFaceValue = 9;
void MDCashBondQuote::clear_totalfacevalue() {
  totalfacevalue_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCashBondQuote::totalfacevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TotalFaceValue)
  return totalfacevalue_;
}
void MDCashBondQuote::set_totalfacevalue(::google::protobuf::int64 value) {
  
  totalfacevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TotalFaceValue)
}

// optional int32 ClearingMethod = 10;
void MDCashBondQuote::clear_clearingmethod() {
  clearingmethod_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::clearingmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.ClearingMethod)
  return clearingmethod_;
}
void MDCashBondQuote::set_clearingmethod(::google::protobuf::int32 value) {
  
  clearingmethod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.ClearingMethod)
}

// optional string SettlType = 11;
void MDCashBondQuote::clear_settltype() {
  settltype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCashBondQuote::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  return settltype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_settltype(const ::std::string& value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
void MDCashBondQuote::set_settltype(const char* value) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
void MDCashBondQuote::set_settltype(const char* value, size_t size) {
  
  settltype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}
::std::string* MDCashBondQuote::mutable_settltype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  return settltype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCashBondQuote::release_settltype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
  
  return settltype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_allocated_settltype(::std::string* settltype) {
  if (settltype != NULL) {
    
  } else {
    
  }
  settltype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settltype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.SettlType)
}

// optional int32 SettlDate = 12;
void MDCashBondQuote::clear_settldate() {
  settldate_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::settldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlDate)
  return settldate_;
}
void MDCashBondQuote::set_settldate(::google::protobuf::int32 value) {
  
  settldate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlDate)
}

// optional string SettlCurrency = 13;
void MDCashBondQuote::clear_settlcurrency() {
  settlcurrency_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCashBondQuote::settlcurrency() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  return settlcurrency_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_settlcurrency(const ::std::string& value) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
void MDCashBondQuote::set_settlcurrency(const char* value) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
void MDCashBondQuote::set_settlcurrency(const char* value, size_t size) {
  
  settlcurrency_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}
::std::string* MDCashBondQuote::mutable_settlcurrency() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  return settlcurrency_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCashBondQuote::release_settlcurrency() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
  
  return settlcurrency_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_allocated_settlcurrency(::std::string* settlcurrency) {
  if (settlcurrency != NULL) {
    
  } else {
    
  }
  settlcurrency_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), settlcurrency);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrency)
}

// optional int64 SettlCurrFxRate = 14;
void MDCashBondQuote::clear_settlcurrfxrate() {
  settlcurrfxrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCashBondQuote::settlcurrfxrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrFxRate)
  return settlcurrfxrate_;
}
void MDCashBondQuote::set_settlcurrfxrate(::google::protobuf::int64 value) {
  
  settlcurrfxrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.SettlCurrFxRate)
}

// optional int32 PartyRole = 15;
void MDCashBondQuote::clear_partyrole() {
  partyrole_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::partyrole() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.PartyRole)
  return partyrole_;
}
void MDCashBondQuote::set_partyrole(::google::protobuf::int32 value) {
  
  partyrole_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.PartyRole)
}

// optional string TraderCode = 16;
void MDCashBondQuote::clear_tradercode() {
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCashBondQuote::tradercode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  return tradercode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_tradercode(const ::std::string& value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
void MDCashBondQuote::set_tradercode(const char* value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
void MDCashBondQuote::set_tradercode(const char* value, size_t size) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}
::std::string* MDCashBondQuote::mutable_tradercode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  return tradercode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCashBondQuote::release_tradercode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
  
  return tradercode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_allocated_tradercode(::std::string* tradercode) {
  if (tradercode != NULL) {
    
  } else {
    
  }
  tradercode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradercode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.TraderCode)
}

// optional int64 MaturityYield = 17;
void MDCashBondQuote::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCashBondQuote::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.MaturityYield)
  return maturityyield_;
}
void MDCashBondQuote::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.MaturityYield)
}

// optional int32 DeliveryType = 18;
void MDCashBondQuote::clear_deliverytype() {
  deliverytype_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::deliverytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DeliveryType)
  return deliverytype_;
}
void MDCashBondQuote::set_deliverytype(::google::protobuf::int32 value) {
  
  deliverytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DeliveryType)
}

// optional string TraderAccountID = 19;
void MDCashBondQuote::clear_traderaccountid() {
  traderaccountid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCashBondQuote::traderaccountid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  return traderaccountid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_traderaccountid(const ::std::string& value) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
void MDCashBondQuote::set_traderaccountid(const char* value) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
void MDCashBondQuote::set_traderaccountid(const char* value, size_t size) {
  
  traderaccountid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}
::std::string* MDCashBondQuote::mutable_traderaccountid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  return traderaccountid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCashBondQuote::release_traderaccountid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
  
  return traderaccountid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCashBondQuote::set_allocated_traderaccountid(::std::string* traderaccountid) {
  if (traderaccountid != NULL) {
    
  } else {
    
  }
  traderaccountid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), traderaccountid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCashBondQuote.TraderAccountID)
}

// optional int32 DataType = 20;
void MDCashBondQuote::clear_datatype() {
  datatype_ = 0;
}
::google::protobuf::int32 MDCashBondQuote::datatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCashBondQuote.DataType)
  return datatype_;
}
void MDCashBondQuote::set_datatype(::google::protobuf::int32 value) {
  
  datatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCashBondQuote.DataType)
}

inline const MDCashBondQuote* MDCashBondQuote::internal_default_instance() {
  return &MDCashBondQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDFxBestQuote::kSideFieldNumber;
const int MDFxBestQuote::kPriceFieldNumber;
const int MDFxBestQuote::kTenorFieldNumber;
const int MDFxBestQuote::kDateFieldNumber;
const int MDFxBestQuote::kTimeFieldNumber;
const int MDFxBestQuote::kLiquidProvidersFieldNumber;
const int MDFxBestQuote::kLegSignFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDFxBestQuote::MDFxBestQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDFxBestQuote)
}

void MDFxBestQuote::InitAsDefaultInstance() {
}

MDFxBestQuote::MDFxBestQuote(const MDFxBestQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDFxBestQuote)
}

void MDFxBestQuote::SharedCtor() {
  tenor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  date_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  time_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&price_, 0, reinterpret_cast<char*>(&side_) -
    reinterpret_cast<char*>(&price_) + sizeof(side_));
  _cached_size_ = 0;
}

MDFxBestQuote::~MDFxBestQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDFxBestQuote)
  SharedDtor();
}

void MDFxBestQuote::SharedDtor() {
  tenor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  date_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  time_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDFxBestQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDFxBestQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDFxBestQuote_descriptor_;
}

const MDFxBestQuote& MDFxBestQuote::default_instance() {
  protobuf_InitDefaults_MDQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDFxBestQuote> MDFxBestQuote_default_instance_;

MDFxBestQuote* MDFxBestQuote::New(::google::protobuf::Arena* arena) const {
  MDFxBestQuote* n = new MDFxBestQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDFxBestQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDFxBestQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDFxBestQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDFxBestQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(price_, side_);
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  date_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  time_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

  liquidproviders_.Clear();
}

bool MDFxBestQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Side = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &side_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Price;
        break;
      }

      // optional int64 Price = 2;
      case 2: {
        if (tag == 16) {
         parse_Price:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &price_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_Tenor;
        break;
      }

      // optional string Tenor = 3;
      case 3: {
        if (tag == 26) {
         parse_Tenor:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenor().data(), this->tenor().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFxBestQuote.Tenor"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_Date;
        break;
      }

      // optional string Date = 4;
      case 4: {
        if (tag == 34) {
         parse_Date:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_date()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->date().data(), this->date().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFxBestQuote.Date"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_Time;
        break;
      }

      // optional string Time = 5;
      case 5: {
        if (tag == 42) {
         parse_Time:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_time()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->time().data(), this->time().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFxBestQuote.Time"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_LiquidProviders;
        break;
      }

      // repeated string LiquidProviders = 6;
      case 6: {
        if (tag == 50) {
         parse_LiquidProviders:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_liquidproviders()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->liquidproviders(this->liquidproviders_size() - 1).data(),
            this->liquidproviders(this->liquidproviders_size() - 1).length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_LiquidProviders;
        if (input->ExpectTag(58)) goto parse_LegSign;
        break;
      }

      // optional string LegSign = 7;
      case 7: {
        if (tag == 58) {
         parse_LegSign:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_legsign()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->legsign().data(), this->legsign().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFxBestQuote.LegSign"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDFxBestQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDFxBestQuote)
  return false;
#undef DO_
}

void MDFxBestQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  // optional int32 Side = 1;
  if (this->side() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->side(), output);
  }

  // optional int64 Price = 2;
  if (this->price() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->price(), output);
  }

  // optional string Tenor = 3;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Tenor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->tenor(), output);
  }

  // optional string Date = 4;
  if (this->date().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->date().data(), this->date().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Date");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->date(), output);
  }

  // optional string Time = 5;
  if (this->time().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->time().data(), this->time().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Time");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->time(), output);
  }

  // repeated string LiquidProviders = 6;
  for (int i = 0; i < this->liquidproviders_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->liquidproviders(i).data(), this->liquidproviders(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      6, this->liquidproviders(i), output);
  }

  // optional string LegSign = 7;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.LegSign");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->legsign(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDFxBestQuote)
}

::google::protobuf::uint8* MDFxBestQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  // optional int32 Side = 1;
  if (this->side() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->side(), target);
  }

  // optional int64 Price = 2;
  if (this->price() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->price(), target);
  }

  // optional string Tenor = 3;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Tenor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->tenor(), target);
  }

  // optional string Date = 4;
  if (this->date().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->date().data(), this->date().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Date");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->date(), target);
  }

  // optional string Time = 5;
  if (this->time().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->time().data(), this->time().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.Time");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->time(), target);
  }

  // repeated string LiquidProviders = 6;
  for (int i = 0; i < this->liquidproviders_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->liquidproviders(i).data(), this->liquidproviders(i).length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(6, this->liquidproviders(i), target);
  }

  // optional string LegSign = 7;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFxBestQuote.LegSign");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->legsign(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDFxBestQuote)
  return target;
}

size_t MDFxBestQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  size_t total_size = 0;

  // optional int32 Side = 1;
  if (this->side() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->side());
  }

  // optional int64 Price = 2;
  if (this->price() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->price());
  }

  // optional string Tenor = 3;
  if (this->tenor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenor());
  }

  // optional string Date = 4;
  if (this->date().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->date());
  }

  // optional string Time = 5;
  if (this->time().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->time());
  }

  // optional string LegSign = 7;
  if (this->legsign().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->legsign());
  }

  // repeated string LiquidProviders = 6;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->liquidproviders_size());
  for (int i = 0; i < this->liquidproviders_size(); i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->liquidproviders(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDFxBestQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDFxBestQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDFxBestQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDFxBestQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDFxBestQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDFxBestQuote::MergeFrom(const MDFxBestQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDFxBestQuote::UnsafeMergeFrom(const MDFxBestQuote& from) {
  GOOGLE_DCHECK(&from != this);
  liquidproviders_.UnsafeMergeFrom(from.liquidproviders_);
  if (from.side() != 0) {
    set_side(from.side());
  }
  if (from.price() != 0) {
    set_price(from.price());
  }
  if (from.tenor().size() > 0) {

    tenor_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenor_);
  }
  if (from.date().size() > 0) {

    date_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.date_);
  }
  if (from.time().size() > 0) {

    time_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.time_);
  }
  if (from.legsign().size() > 0) {

    legsign_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.legsign_);
  }
}

void MDFxBestQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDFxBestQuote::CopyFrom(const MDFxBestQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDFxBestQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDFxBestQuote::IsInitialized() const {

  return true;
}

void MDFxBestQuote::Swap(MDFxBestQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDFxBestQuote::InternalSwap(MDFxBestQuote* other) {
  std::swap(side_, other->side_);
  std::swap(price_, other->price_);
  tenor_.Swap(&other->tenor_);
  date_.Swap(&other->date_);
  time_.Swap(&other->time_);
  liquidproviders_.UnsafeArenaSwap(&other->liquidproviders_);
  legsign_.Swap(&other->legsign_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDFxBestQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDFxBestQuote_descriptor_;
  metadata.reflection = MDFxBestQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDFxBestQuote

// optional int32 Side = 1;
void MDFxBestQuote::clear_side() {
  side_ = 0;
}
::google::protobuf::int32 MDFxBestQuote::side() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Side)
  return side_;
}
void MDFxBestQuote::set_side(::google::protobuf::int32 value) {
  
  side_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Side)
}

// optional int64 Price = 2;
void MDFxBestQuote::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFxBestQuote::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Price)
  return price_;
}
void MDFxBestQuote::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Price)
}

// optional string Tenor = 3;
void MDFxBestQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFxBestQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
void MDFxBestQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
void MDFxBestQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}
::std::string* MDFxBestQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFxBestQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Tenor)
}

// optional string Date = 4;
void MDFxBestQuote::clear_date() {
  date_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFxBestQuote::date() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  return date_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_date(const ::std::string& value) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
void MDFxBestQuote::set_date(const char* value) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
void MDFxBestQuote::set_date(const char* value, size_t size) {
  
  date_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}
::std::string* MDFxBestQuote::mutable_date() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  return date_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFxBestQuote::release_date() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
  
  return date_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_allocated_date(::std::string* date) {
  if (date != NULL) {
    
  } else {
    
  }
  date_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), date);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Date)
}

// optional string Time = 5;
void MDFxBestQuote::clear_time() {
  time_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFxBestQuote::time() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  return time_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_time(const ::std::string& value) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
void MDFxBestQuote::set_time(const char* value) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
void MDFxBestQuote::set_time(const char* value, size_t size) {
  
  time_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}
::std::string* MDFxBestQuote::mutable_time() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  return time_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFxBestQuote::release_time() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
  
  return time_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_allocated_time(::std::string* time) {
  if (time != NULL) {
    
  } else {
    
  }
  time_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), time);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.Time)
}

// repeated string LiquidProviders = 6;
int MDFxBestQuote::liquidproviders_size() const {
  return liquidproviders_.size();
}
void MDFxBestQuote::clear_liquidproviders() {
  liquidproviders_.Clear();
}
const ::std::string& MDFxBestQuote::liquidproviders(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Get(index);
}
::std::string* MDFxBestQuote::mutable_liquidproviders(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Mutable(index);
}
void MDFxBestQuote::set_liquidproviders(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  liquidproviders_.Mutable(index)->assign(value);
}
void MDFxBestQuote::set_liquidproviders(int index, const char* value) {
  liquidproviders_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
void MDFxBestQuote::set_liquidproviders(int index, const char* value, size_t size) {
  liquidproviders_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
::std::string* MDFxBestQuote::add_liquidproviders() {
  // @@protoc_insertion_point(field_add_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_.Add();
}
void MDFxBestQuote::add_liquidproviders(const ::std::string& value) {
  liquidproviders_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
void MDFxBestQuote::add_liquidproviders(const char* value) {
  liquidproviders_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
void MDFxBestQuote::add_liquidproviders(const char* value, size_t size) {
  liquidproviders_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
}
const ::google::protobuf::RepeatedPtrField< ::std::string>&
MDFxBestQuote::liquidproviders() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return liquidproviders_;
}
::google::protobuf::RepeatedPtrField< ::std::string>*
MDFxBestQuote::mutable_liquidproviders() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFxBestQuote.LiquidProviders)
  return &liquidproviders_;
}

// optional string LegSign = 7;
void MDFxBestQuote::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFxBestQuote::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
void MDFxBestQuote::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
void MDFxBestQuote::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}
::std::string* MDFxBestQuote::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFxBestQuote::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFxBestQuote::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFxBestQuote.LegSign)
}

inline const MDFxBestQuote* MDFxBestQuote::internal_default_instance() {
  return &MDFxBestQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NeeqMarketMakerQuote::kSequenceFieldNumber;
const int NeeqMarketMakerQuote::kQuoteTypeFieldNumber;
const int NeeqMarketMakerQuote::kPriceFieldNumber;
const int NeeqMarketMakerQuote::kOrderQtyFieldNumber;
const int NeeqMarketMakerQuote::kGenerateTimeFieldNumber;
const int NeeqMarketMakerQuote::kBackupFieldFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NeeqMarketMakerQuote::NeeqMarketMakerQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
}

void NeeqMarketMakerQuote::InitAsDefaultInstance() {
}

NeeqMarketMakerQuote::NeeqMarketMakerQuote(const NeeqMarketMakerQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
}

void NeeqMarketMakerQuote::SharedCtor() {
  quotetype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  backupfield_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&price_, 0, reinterpret_cast<char*>(&sequence_) -
    reinterpret_cast<char*>(&price_) + sizeof(sequence_));
  _cached_size_ = 0;
}

NeeqMarketMakerQuote::~NeeqMarketMakerQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  SharedDtor();
}

void NeeqMarketMakerQuote::SharedDtor() {
  quotetype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  backupfield_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void NeeqMarketMakerQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NeeqMarketMakerQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NeeqMarketMakerQuote_descriptor_;
}

const NeeqMarketMakerQuote& NeeqMarketMakerQuote::default_instance() {
  protobuf_InitDefaults_MDQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NeeqMarketMakerQuote> NeeqMarketMakerQuote_default_instance_;

NeeqMarketMakerQuote* NeeqMarketMakerQuote::New(::google::protobuf::Arena* arena) const {
  NeeqMarketMakerQuote* n = new NeeqMarketMakerQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NeeqMarketMakerQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(NeeqMarketMakerQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<NeeqMarketMakerQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(price_, sequence_);
  quotetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  generatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  backupfield_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool NeeqMarketMakerQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 Sequence = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sequence_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_QuoteType;
        break;
      }

      // optional string QuoteType = 2;
      case 2: {
        if (tag == 18) {
         parse_QuoteType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_quotetype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->quotetype().data(), this->quotetype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Price;
        break;
      }

      // optional int64 Price = 3;
      case 3: {
        if (tag == 24) {
         parse_Price:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &price_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_OrderQty;
        break;
      }

      // optional int64 OrderQty = 4;
      case 4: {
        if (tag == 32) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_GenerateTime;
        break;
      }

      // optional string GenerateTime = 5;
      case 5: {
        if (tag == 42) {
         parse_GenerateTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_generatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->generatetime().data(), this->generatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_BackupField;
        break;
      }

      // optional string BackupField = 6;
      case 6: {
        if (tag == 50) {
         parse_BackupField:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_backupfield()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->backupfield().data(), this->backupfield().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  return false;
#undef DO_
}

void NeeqMarketMakerQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  // optional int32 Sequence = 1;
  if (this->sequence() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->sequence(), output);
  }

  // optional string QuoteType = 2;
  if (this->quotetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotetype().data(), this->quotetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->quotetype(), output);
  }

  // optional int64 Price = 3;
  if (this->price() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->price(), output);
  }

  // optional int64 OrderQty = 4;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->orderqty(), output);
  }

  // optional string GenerateTime = 5;
  if (this->generatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->generatetime().data(), this->generatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->generatetime(), output);
  }

  // optional string BackupField = 6;
  if (this->backupfield().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->backupfield().data(), this->backupfield().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->backupfield(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
}

::google::protobuf::uint8* NeeqMarketMakerQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  // optional int32 Sequence = 1;
  if (this->sequence() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->sequence(), target);
  }

  // optional string QuoteType = 2;
  if (this->quotetype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->quotetype().data(), this->quotetype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->quotetype(), target);
  }

  // optional int64 Price = 3;
  if (this->price() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->price(), target);
  }

  // optional int64 OrderQty = 4;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->orderqty(), target);
  }

  // optional string GenerateTime = 5;
  if (this->generatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->generatetime().data(), this->generatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->generatetime(), target);
  }

  // optional string BackupField = 6;
  if (this->backupfield().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->backupfield().data(), this->backupfield().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->backupfield(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  return target;
}

size_t NeeqMarketMakerQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  size_t total_size = 0;

  // optional int32 Sequence = 1;
  if (this->sequence() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sequence());
  }

  // optional string QuoteType = 2;
  if (this->quotetype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->quotetype());
  }

  // optional int64 Price = 3;
  if (this->price() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->price());
  }

  // optional int64 OrderQty = 4;
  if (this->orderqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderqty());
  }

  // optional string GenerateTime = 5;
  if (this->generatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->generatetime());
  }

  // optional string BackupField = 6;
  if (this->backupfield().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->backupfield());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NeeqMarketMakerQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NeeqMarketMakerQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NeeqMarketMakerQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
    UnsafeMergeFrom(*source);
  }
}

void NeeqMarketMakerQuote::MergeFrom(const NeeqMarketMakerQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NeeqMarketMakerQuote::UnsafeMergeFrom(const NeeqMarketMakerQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.sequence() != 0) {
    set_sequence(from.sequence());
  }
  if (from.quotetype().size() > 0) {

    quotetype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.quotetype_);
  }
  if (from.price() != 0) {
    set_price(from.price());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.generatetime().size() > 0) {

    generatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.generatetime_);
  }
  if (from.backupfield().size() > 0) {

    backupfield_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.backupfield_);
  }
}

void NeeqMarketMakerQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NeeqMarketMakerQuote::CopyFrom(const NeeqMarketMakerQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.NeeqMarketMakerQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NeeqMarketMakerQuote::IsInitialized() const {

  return true;
}

void NeeqMarketMakerQuote::Swap(NeeqMarketMakerQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NeeqMarketMakerQuote::InternalSwap(NeeqMarketMakerQuote* other) {
  std::swap(sequence_, other->sequence_);
  quotetype_.Swap(&other->quotetype_);
  std::swap(price_, other->price_);
  std::swap(orderqty_, other->orderqty_);
  generatetime_.Swap(&other->generatetime_);
  backupfield_.Swap(&other->backupfield_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NeeqMarketMakerQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NeeqMarketMakerQuote_descriptor_;
  metadata.reflection = NeeqMarketMakerQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NeeqMarketMakerQuote

// optional int32 Sequence = 1;
void NeeqMarketMakerQuote::clear_sequence() {
  sequence_ = 0;
}
::google::protobuf::int32 NeeqMarketMakerQuote::sequence() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Sequence)
  return sequence_;
}
void NeeqMarketMakerQuote::set_sequence(::google::protobuf::int32 value) {
  
  sequence_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Sequence)
}

// optional string QuoteType = 2;
void NeeqMarketMakerQuote::clear_quotetype() {
  quotetype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NeeqMarketMakerQuote::quotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  return quotetype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_quotetype(const ::std::string& value) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
void NeeqMarketMakerQuote::set_quotetype(const char* value) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
void NeeqMarketMakerQuote::set_quotetype(const char* value, size_t size) {
  
  quotetype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}
::std::string* NeeqMarketMakerQuote::mutable_quotetype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  return quotetype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NeeqMarketMakerQuote::release_quotetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
  
  return quotetype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_allocated_quotetype(::std::string* quotetype) {
  if (quotetype != NULL) {
    
  } else {
    
  }
  quotetype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quotetype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.QuoteType)
}

// optional int64 Price = 3;
void NeeqMarketMakerQuote::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NeeqMarketMakerQuote::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Price)
  return price_;
}
void NeeqMarketMakerQuote::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.Price)
}

// optional int64 OrderQty = 4;
void NeeqMarketMakerQuote::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NeeqMarketMakerQuote::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.OrderQty)
  return orderqty_;
}
void NeeqMarketMakerQuote::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.OrderQty)
}

// optional string GenerateTime = 5;
void NeeqMarketMakerQuote::clear_generatetime() {
  generatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NeeqMarketMakerQuote::generatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  return generatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_generatetime(const ::std::string& value) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
void NeeqMarketMakerQuote::set_generatetime(const char* value) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
void NeeqMarketMakerQuote::set_generatetime(const char* value, size_t size) {
  
  generatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}
::std::string* NeeqMarketMakerQuote::mutable_generatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  return generatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NeeqMarketMakerQuote::release_generatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
  
  return generatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_allocated_generatetime(::std::string* generatetime) {
  if (generatetime != NULL) {
    
  } else {
    
  }
  generatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), generatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.GenerateTime)
}

// optional string BackupField = 6;
void NeeqMarketMakerQuote::clear_backupfield() {
  backupfield_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NeeqMarketMakerQuote::backupfield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  return backupfield_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_backupfield(const ::std::string& value) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
void NeeqMarketMakerQuote::set_backupfield(const char* value) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
void NeeqMarketMakerQuote::set_backupfield(const char* value, size_t size) {
  
  backupfield_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}
::std::string* NeeqMarketMakerQuote::mutable_backupfield() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  return backupfield_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NeeqMarketMakerQuote::release_backupfield() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
  
  return backupfield_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NeeqMarketMakerQuote::set_allocated_backupfield(::std::string* backupfield) {
  if (backupfield != NULL) {
    
  } else {
    
  }
  backupfield_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), backupfield);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NeeqMarketMakerQuote.BackupField)
}

inline const NeeqMarketMakerQuote* NeeqMarketMakerQuote::internal_default_instance() {
  return &NeeqMarketMakerQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BrokerQueue::kPriceFieldNumber;
const int BrokerQueue::kOrderQtyFieldNumber;
const int BrokerQueue::kNumOrdersFieldNumber;
const int BrokerQueue::kBrokerIDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BrokerQueue::BrokerQueue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.BrokerQueue)
}

void BrokerQueue::InitAsDefaultInstance() {
}

BrokerQueue::BrokerQueue(const BrokerQueue& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.BrokerQueue)
}

void BrokerQueue::SharedCtor() {
  ::memset(&price_, 0, reinterpret_cast<char*>(&numorders_) -
    reinterpret_cast<char*>(&price_) + sizeof(numorders_));
  _cached_size_ = 0;
}

BrokerQueue::~BrokerQueue() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.BrokerQueue)
  SharedDtor();
}

void BrokerQueue::SharedDtor() {
}

void BrokerQueue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BrokerQueue::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return BrokerQueue_descriptor_;
}

const BrokerQueue& BrokerQueue::default_instance() {
  protobuf_InitDefaults_MDQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<BrokerQueue> BrokerQueue_default_instance_;

BrokerQueue* BrokerQueue::New(::google::protobuf::Arena* arena) const {
  BrokerQueue* n = new BrokerQueue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BrokerQueue::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.BrokerQueue)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(BrokerQueue, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<BrokerQueue*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(price_, numorders_);

#undef ZR_HELPER_
#undef ZR_

  brokerid_.Clear();
}

bool BrokerQueue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.BrokerQueue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 Price = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &price_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_OrderQty;
        break;
      }

      // optional int64 OrderQty = 2;
      case 2: {
        if (tag == 16) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_NumOrders;
        break;
      }

      // optional int64 NumOrders = 3;
      case 3: {
        if (tag == 24) {
         parse_NumOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BrokerID;
        break;
      }

      // repeated int32 BrokerID = 4;
      case 4: {
        if (tag == 34) {
         parse_BrokerID:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_brokerid())));
        } else if (tag == 32) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 34, input, this->mutable_brokerid())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.BrokerQueue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.BrokerQueue)
  return false;
#undef DO_
}

void BrokerQueue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.BrokerQueue)
  // optional int64 Price = 1;
  if (this->price() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->price(), output);
  }

  // optional int64 OrderQty = 2;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->orderqty(), output);
  }

  // optional int64 NumOrders = 3;
  if (this->numorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->numorders(), output);
  }

  // repeated int32 BrokerID = 4;
  if (this->brokerid_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(4, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_brokerid_cached_byte_size_);
  }
  for (int i = 0; i < this->brokerid_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->brokerid(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.BrokerQueue)
}

::google::protobuf::uint8* BrokerQueue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.BrokerQueue)
  // optional int64 Price = 1;
  if (this->price() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->price(), target);
  }

  // optional int64 OrderQty = 2;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->orderqty(), target);
  }

  // optional int64 NumOrders = 3;
  if (this->numorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->numorders(), target);
  }

  // repeated int32 BrokerID = 4;
  if (this->brokerid_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      4,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _brokerid_cached_byte_size_, target);
  }
  for (int i = 0; i < this->brokerid_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->brokerid(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.BrokerQueue)
  return target;
}

size_t BrokerQueue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.BrokerQueue)
  size_t total_size = 0;

  // optional int64 Price = 1;
  if (this->price() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->price());
  }

  // optional int64 OrderQty = 2;
  if (this->orderqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderqty());
  }

  // optional int64 NumOrders = 3;
  if (this->numorders() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numorders());
  }

  // repeated int32 BrokerID = 4;
  {
    size_t data_size = 0;
    unsigned int count = this->brokerid_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int32Size(this->brokerid(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _brokerid_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BrokerQueue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.BrokerQueue)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const BrokerQueue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BrokerQueue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.BrokerQueue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.BrokerQueue)
    UnsafeMergeFrom(*source);
  }
}

void BrokerQueue::MergeFrom(const BrokerQueue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.BrokerQueue)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void BrokerQueue::UnsafeMergeFrom(const BrokerQueue& from) {
  GOOGLE_DCHECK(&from != this);
  brokerid_.UnsafeMergeFrom(from.brokerid_);
  if (from.price() != 0) {
    set_price(from.price());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.numorders() != 0) {
    set_numorders(from.numorders());
  }
}

void BrokerQueue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.BrokerQueue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BrokerQueue::CopyFrom(const BrokerQueue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.BrokerQueue)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool BrokerQueue::IsInitialized() const {

  return true;
}

void BrokerQueue::Swap(BrokerQueue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BrokerQueue::InternalSwap(BrokerQueue* other) {
  std::swap(price_, other->price_);
  std::swap(orderqty_, other->orderqty_);
  std::swap(numorders_, other->numorders_);
  brokerid_.UnsafeArenaSwap(&other->brokerid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BrokerQueue::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = BrokerQueue_descriptor_;
  metadata.reflection = BrokerQueue_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BrokerQueue

// optional int64 Price = 1;
void BrokerQueue::clear_price() {
  price_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 BrokerQueue::price() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.Price)
  return price_;
}
void BrokerQueue::set_price(::google::protobuf::int64 value) {
  
  price_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.Price)
}

// optional int64 OrderQty = 2;
void BrokerQueue::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 BrokerQueue::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.OrderQty)
  return orderqty_;
}
void BrokerQueue::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.OrderQty)
}

// optional int64 NumOrders = 3;
void BrokerQueue::clear_numorders() {
  numorders_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 BrokerQueue::numorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.NumOrders)
  return numorders_;
}
void BrokerQueue::set_numorders(::google::protobuf::int64 value) {
  
  numorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.NumOrders)
}

// repeated int32 BrokerID = 4;
int BrokerQueue::brokerid_size() const {
  return brokerid_.size();
}
void BrokerQueue::clear_brokerid() {
  brokerid_.Clear();
}
::google::protobuf::int32 BrokerQueue::brokerid(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return brokerid_.Get(index);
}
void BrokerQueue::set_brokerid(int index, ::google::protobuf::int32 value) {
  brokerid_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
}
void BrokerQueue::add_brokerid(::google::protobuf::int32 value) {
  brokerid_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
BrokerQueue::brokerid() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return brokerid_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
BrokerQueue::mutable_brokerid() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.BrokerQueue.BrokerID)
  return &brokerid_;
}

inline const BrokerQueue* BrokerQueue::internal_default_instance() {
  return &BrokerQueue_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
