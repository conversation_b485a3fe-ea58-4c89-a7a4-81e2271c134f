// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsFxQuote.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsFxQuote.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsFxQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsFxQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* SwpSptNdfFowFxQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SwpSptNdfFowFxQuote_reflection_ = NULL;
const ::google::protobuf::Descriptor* OptionFxQuote_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OptionFxQuote_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsFxQuote_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsFxQuote_2eproto() {
  protobuf_AddDesc_MDCfetsFxQuote_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsFxQuote.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsFxQuote_descriptor_ = file->message_type(0);
  static const int MDCfetsFxQuote_offsets_[16] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, securitysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, forexquotetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, spotfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, forwardfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, nondeliverableforwardsfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, swapfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, optionfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, transacttime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, marketindicator_),
  };
  MDCfetsFxQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsFxQuote_descriptor_,
      MDCfetsFxQuote::internal_default_instance(),
      MDCfetsFxQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsFxQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsFxQuote, _internal_metadata_));
  SwpSptNdfFowFxQuote_descriptor_ = file->message_type(1);
  static const int SwpSptNdfFowFxQuote_offsets_[18] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratedatebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratetimebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratedatesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratetimesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, bestratesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidproviderbuy1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidproviderbuy2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidproviderbuy3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidproviderbuy4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidproviderbuy5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidprovidersell1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidprovidersell2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidprovidersell3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidprovidersell4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, rateliquidprovidersell5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, legsign_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, tradedate_),
  };
  SwpSptNdfFowFxQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SwpSptNdfFowFxQuote_descriptor_,
      SwpSptNdfFowFxQuote::internal_default_instance(),
      SwpSptNdfFowFxQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(SwpSptNdfFowFxQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwpSptNdfFowFxQuote, _internal_metadata_));
  OptionFxQuote_descriptor_ = file->message_type(2);
  static const int OptionFxQuote_offsets_[21] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratedatebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratetimebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratedatesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratetimesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratebuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, bestratesell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, volatilitysurface_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, tenorbuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, tenorsell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionbuy_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionsell_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, tenor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionbuy2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionsell2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionbuy3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionsell3_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionbuy4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionsell4_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionbuy5_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, makerinstitutionsell5_),
  };
  OptionFxQuote_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OptionFxQuote_descriptor_,
      OptionFxQuote::internal_default_instance(),
      OptionFxQuote_offsets_,
      -1,
      -1,
      -1,
      sizeof(OptionFxQuote),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionFxQuote, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsFxQuote_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsFxQuote_descriptor_, MDCfetsFxQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SwpSptNdfFowFxQuote_descriptor_, SwpSptNdfFowFxQuote::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OptionFxQuote_descriptor_, OptionFxQuote::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsFxQuote_2eproto() {
  MDCfetsFxQuote_default_instance_.Shutdown();
  delete MDCfetsFxQuote_reflection_;
  SwpSptNdfFowFxQuote_default_instance_.Shutdown();
  delete SwpSptNdfFowFxQuote_reflection_;
  OptionFxQuote_default_instance_.Shutdown();
  delete OptionFxQuote_reflection_;
}

void protobuf_InitDefaults_MDCfetsFxQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsFxQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SwpSptNdfFowFxQuote_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  OptionFxQuote_default_instance_.DefaultConstruct();
  MDCfetsFxQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  SwpSptNdfFowFxQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
  OptionFxQuote_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsFxQuote_2eproto_once_);
void protobuf_InitDefaults_MDCfetsFxQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsFxQuote_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsFxQuote_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsFxQuote_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\024MDCfetsFxQuote.proto\022\032com.htsc.mdc.ins"
    "ight.model\032\027ESecurityIDSource.proto\032\023ESe"
    "curityType.proto\"\307\005\n\016MDCfetsFxQuote\022\026\n\016H"
    "TSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006M"
    "DTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020se"
    "curityIDSource\030\005 \001(\0162%.com.htsc.mdc.mode"
    "l.ESecurityIDSource\0227\n\014securityType\030\006 \001("
    "\0162!.com.htsc.mdc.model.ESecurityType\022\027\n\017"
    "SecuritySubType\030\007 \001(\t\022\026\n\016ForexQuoteType\030"
    "\010 \001(\005\022D\n\013spotFxQuote\030\t \001(\0132/.com.htsc.md"
    "c.insight.model.SwpSptNdfFowFxQuote\022G\n\016f"
    "orwardFxQuote\030\n \001(\0132/.com.htsc.mdc.insig"
    "ht.model.SwpSptNdfFowFxQuote\022V\n\035nonDeliv"
    "erableForwardsFxQuote\030\013 \001(\0132/.com.htsc.m"
    "dc.insight.model.SwpSptNdfFowFxQuote\022D\n\013"
    "swapFxQuote\030\014 \001(\0132/.com.htsc.mdc.insight"
    ".model.SwpSptNdfFowFxQuote\022@\n\roptionFxQu"
    "ote\030\r \001(\0132).com.htsc.mdc.insight.model.O"
    "ptionFxQuote\022\035\n\025DataMultiplePowerOf10\030\016 "
    "\001(\005\022\024\n\014TransactTime\030\017 \001(\t\022\027\n\017MarketIndic"
    "ator\030\020 \001(\t\"\217\004\n\023SwpSptNdfFowFxQuote\022\027\n\017Be"
    "stRateDateBuy\030\001 \001(\t\022\027\n\017BestRateTimeBuy\030\002"
    " \001(\t\022\030\n\020BestRateDateSell\030\003 \001(\t\022\030\n\020BestRa"
    "teTimeSell\030\004 \001(\t\022\023\n\013BestRateBuy\030\005 \001(\003\022\024\n"
    "\014BestRateSell\030\006 \001(\003\022\036\n\026RateLiquidProvide"
    "rBuy1\030\007 \001(\t\022\036\n\026RateLiquidProviderBuy2\030\010 "
    "\001(\t\022\036\n\026RateLiquidProviderBuy3\030\t \001(\t\022\036\n\026R"
    "ateLiquidProviderBuy4\030\n \001(\t\022\036\n\026RateLiqui"
    "dProviderBuy5\030\013 \001(\t\022\037\n\027RateLiquidProvide"
    "rSell1\030\014 \001(\t\022\037\n\027RateLiquidProviderSell2\030"
    "\r \001(\t\022\037\n\027RateLiquidProviderSell3\030\016 \001(\t\022\037"
    "\n\027RateLiquidProviderSell4\030\017 \001(\t\022\037\n\027RateL"
    "iquidProviderSell5\030\020 \001(\t\022\017\n\007LegSign\030\021 \001("
    "\t\022\021\n\tTradeDate\030\022 \001(\t\"\261\004\n\rOptionFxQuote\022\027"
    "\n\017BestRateDateBuy\030\001 \001(\t\022\027\n\017BestRateTimeB"
    "uy\030\002 \001(\t\022\030\n\020BestRateDateSell\030\003 \001(\t\022\030\n\020Be"
    "stRateTimeSell\030\004 \001(\t\022\023\n\013BestRateBuy\030\005 \001("
    "\003\022\024\n\014BestRateSell\030\006 \001(\003\022\031\n\021VolatilitySur"
    "face\030\007 \001(\t\022\020\n\010TenorBuy\030\010 \001(\t\022\021\n\tTenorSel"
    "l\030\t \001(\t\022\033\n\023MakerInstitutionBuy\030\n \001(\t\022\034\n\024"
    "MakerInstitutionSell\030\013 \001(\t\022\r\n\005Tenor\030\014 \001("
    "\t\022\021\n\tTradeDate\030\r \001(\t\022\034\n\024MakerInstitution"
    "Buy2\030\016 \001(\t\022\035\n\025MakerInstitutionSell2\030\017 \001("
    "\t\022\034\n\024MakerInstitutionBuy3\030\020 \001(\t\022\035\n\025Maker"
    "InstitutionSell3\030\021 \001(\t\022\034\n\024MakerInstituti"
    "onBuy4\030\022 \001(\t\022\035\n\025MakerInstitutionSell4\030\023 "
    "\001(\t\022\034\n\024MakerInstitutionBuy5\030\024 \001(\t\022\035\n\025Mak"
    "erInstitutionSell5\030\025 \001(\tB7\n\032com.htsc.mdc"
    ".insight.modelB\024MDCfetsFxQuoteProtosH\001\240\001"
    "\001b\006proto3", 1969);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsFxQuote.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsFxQuote_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsFxQuote_2eproto_once_);
void protobuf_AddDesc_MDCfetsFxQuote_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsFxQuote_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsFxQuote_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsFxQuote_2eproto {
  StaticDescriptorInitializer_MDCfetsFxQuote_2eproto() {
    protobuf_AddDesc_MDCfetsFxQuote_2eproto();
  }
} static_descriptor_initializer_MDCfetsFxQuote_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsFxQuote::kHTSCSecurityIDFieldNumber;
const int MDCfetsFxQuote::kMDDateFieldNumber;
const int MDCfetsFxQuote::kMDTimeFieldNumber;
const int MDCfetsFxQuote::kDataTimestampFieldNumber;
const int MDCfetsFxQuote::kSecurityIDSourceFieldNumber;
const int MDCfetsFxQuote::kSecurityTypeFieldNumber;
const int MDCfetsFxQuote::kSecuritySubTypeFieldNumber;
const int MDCfetsFxQuote::kForexQuoteTypeFieldNumber;
const int MDCfetsFxQuote::kSpotFxQuoteFieldNumber;
const int MDCfetsFxQuote::kForwardFxQuoteFieldNumber;
const int MDCfetsFxQuote::kNonDeliverableForwardsFxQuoteFieldNumber;
const int MDCfetsFxQuote::kSwapFxQuoteFieldNumber;
const int MDCfetsFxQuote::kOptionFxQuoteFieldNumber;
const int MDCfetsFxQuote::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsFxQuote::kTransactTimeFieldNumber;
const int MDCfetsFxQuote::kMarketIndicatorFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsFxQuote::MDCfetsFxQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsFxQuote)
}

void MDCfetsFxQuote::InitAsDefaultInstance() {
  spotfxquote_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance());
  forwardfxquote_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance());
  nondeliverableforwardsfxquote_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance());
  swapfxquote_ = const_cast< ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote*>(
      ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance());
  optionfxquote_ = const_cast< ::com::htsc::mdc::insight::model::OptionFxQuote*>(
      ::com::htsc::mdc::insight::model::OptionFxQuote::internal_default_instance());
}

MDCfetsFxQuote::MDCfetsFxQuote(const MDCfetsFxQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsFxQuote)
}

void MDCfetsFxQuote::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  spotfxquote_ = NULL;
  forwardfxquote_ = NULL;
  nondeliverableforwardsfxquote_ = NULL;
  swapfxquote_ = NULL;
  optionfxquote_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCfetsFxQuote::~MDCfetsFxQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  SharedDtor();
}

void MDCfetsFxQuote::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsFxQuote_default_instance_.get()) {
    delete spotfxquote_;
    delete forwardfxquote_;
    delete nondeliverableforwardsfxquote_;
    delete swapfxquote_;
    delete optionfxquote_;
  }
}

void MDCfetsFxQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsFxQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsFxQuote_descriptor_;
}

const MDCfetsFxQuote& MDCfetsFxQuote::default_instance() {
  protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsFxQuote> MDCfetsFxQuote_default_instance_;

MDCfetsFxQuote* MDCfetsFxQuote::New(::google::protobuf::Arena* arena) const {
  MDCfetsFxQuote* n = new MDCfetsFxQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsFxQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsFxQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsFxQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, forexquotetype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && spotfxquote_ != NULL) delete spotfxquote_;
  spotfxquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && forwardfxquote_ != NULL) delete forwardfxquote_;
  forwardfxquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxquote_ != NULL) delete nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && swapfxquote_ != NULL) delete swapfxquote_;
  swapfxquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && optionfxquote_ != NULL) delete optionfxquote_;
  optionfxquote_ = NULL;
  datamultiplepowerof10_ = 0;
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsFxQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SecuritySubType;
        break;
      }

      // optional string SecuritySubType = 7;
      case 7: {
        if (tag == 58) {
         parse_SecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitysubtype().data(), this->securitysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ForexQuoteType;
        break;
      }

      // optional int32 ForexQuoteType = 8;
      case 8: {
        if (tag == 64) {
         parse_ForexQuoteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &forexquotetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_spotFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
      case 9: {
        if (tag == 74) {
         parse_spotFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spotfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_forwardFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
      case 10: {
        if (tag == 82) {
         parse_forwardFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_forwardfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_nonDeliverableForwardsFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
      case 11: {
        if (tag == 90) {
         parse_nonDeliverableForwardsFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nondeliverableforwardsfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_swapFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
      case 12: {
        if (tag == 98) {
         parse_swapFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_swapfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_optionFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
      case 13: {
        if (tag == 106) {
         parse_optionFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optionfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 14;
      case 14: {
        if (tag == 112) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 15;
      case 15: {
        if (tag == 122) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_MarketIndicator;
        break;
      }

      // optional string MarketIndicator = 16;
      case 16: {
        if (tag == 130) {
         parse_MarketIndicator:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketindicator()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketindicator().data(), this->marketindicator().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  return false;
#undef DO_
}

void MDCfetsFxQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->securitysubtype(), output);
  }

  // optional int32 ForexQuoteType = 8;
  if (this->forexquotetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->forexquotetype(), output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
  if (this->has_spotfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->spotfxquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
  if (this->has_forwardfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->forwardfxquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
  if (this->has_nondeliverableforwardsfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->nondeliverableforwardsfxquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
  if (this->has_swapfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->swapfxquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
  if (this->has_optionfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->optionfxquote_, output);
  }

  // optional int32 DataMultiplePowerOf10 = 14;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->datamultiplepowerof10(), output);
  }

  // optional string TransactTime = 15;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->transacttime(), output);
  }

  // optional string MarketIndicator = 16;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->marketindicator(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsFxQuote)
}

::google::protobuf::uint8* MDCfetsFxQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->securitysubtype(), target);
  }

  // optional int32 ForexQuoteType = 8;
  if (this->forexquotetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->forexquotetype(), target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
  if (this->has_spotfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->spotfxquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
  if (this->has_forwardfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->forwardfxquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
  if (this->has_nondeliverableforwardsfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->nondeliverableforwardsfxquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
  if (this->has_swapfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->swapfxquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
  if (this->has_optionfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->optionfxquote_, false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 14;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->datamultiplepowerof10(), target);
  }

  // optional string TransactTime = 15;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->transacttime(), target);
  }

  // optional string MarketIndicator = 16;
  if (this->marketindicator().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketindicator().data(), this->marketindicator().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->marketindicator(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  return target;
}

size_t MDCfetsFxQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitysubtype());
  }

  // optional int32 ForexQuoteType = 8;
  if (this->forexquotetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->forexquotetype());
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
  if (this->has_spotfxquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spotfxquote_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
  if (this->has_forwardfxquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->forwardfxquote_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
  if (this->has_nondeliverableforwardsfxquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nondeliverableforwardsfxquote_);
  }

  // optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
  if (this->has_swapfxquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->swapfxquote_);
  }

  // optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
  if (this->has_optionfxquote()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optionfxquote_);
  }

  // optional int32 DataMultiplePowerOf10 = 14;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string TransactTime = 15;
  if (this->transacttime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  // optional string MarketIndicator = 16;
  if (this->marketindicator().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketindicator());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsFxQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsFxQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsFxQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsFxQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsFxQuote)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsFxQuote::MergeFrom(const MDCfetsFxQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsFxQuote::UnsafeMergeFrom(const MDCfetsFxQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securitysubtype().size() > 0) {

    securitysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitysubtype_);
  }
  if (from.forexquotetype() != 0) {
    set_forexquotetype(from.forexquotetype());
  }
  if (from.has_spotfxquote()) {
    mutable_spotfxquote()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::MergeFrom(from.spotfxquote());
  }
  if (from.has_forwardfxquote()) {
    mutable_forwardfxquote()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::MergeFrom(from.forwardfxquote());
  }
  if (from.has_nondeliverableforwardsfxquote()) {
    mutable_nondeliverableforwardsfxquote()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::MergeFrom(from.nondeliverableforwardsfxquote());
  }
  if (from.has_swapfxquote()) {
    mutable_swapfxquote()->::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::MergeFrom(from.swapfxquote());
  }
  if (from.has_optionfxquote()) {
    mutable_optionfxquote()->::com::htsc::mdc::insight::model::OptionFxQuote::MergeFrom(from.optionfxquote());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
  if (from.marketindicator().size() > 0) {

    marketindicator_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketindicator_);
  }
}

void MDCfetsFxQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsFxQuote::CopyFrom(const MDCfetsFxQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsFxQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsFxQuote::IsInitialized() const {

  return true;
}

void MDCfetsFxQuote::Swap(MDCfetsFxQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsFxQuote::InternalSwap(MDCfetsFxQuote* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  securitysubtype_.Swap(&other->securitysubtype_);
  std::swap(forexquotetype_, other->forexquotetype_);
  std::swap(spotfxquote_, other->spotfxquote_);
  std::swap(forwardfxquote_, other->forwardfxquote_);
  std::swap(nondeliverableforwardsfxquote_, other->nondeliverableforwardsfxquote_);
  std::swap(swapfxquote_, other->swapfxquote_);
  std::swap(optionfxquote_, other->optionfxquote_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  transacttime_.Swap(&other->transacttime_);
  marketindicator_.Swap(&other->marketindicator_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsFxQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsFxQuote_descriptor_;
  metadata.reflection = MDCfetsFxQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsFxQuote

// optional string HTSCSecurityID = 1;
void MDCfetsFxQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
void MDCfetsFxQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
void MDCfetsFxQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}
::std::string* MDCfetsFxQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCfetsFxQuote::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsFxQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDDate)
  return mddate_;
}
void MDCfetsFxQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDDate)
}

// optional int32 MDTime = 3;
void MDCfetsFxQuote::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsFxQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDTime)
  return mdtime_;
}
void MDCfetsFxQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCfetsFxQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsFxQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsFxQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCfetsFxQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsFxQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsFxQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCfetsFxQuote::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsFxQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsFxQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.securityType)
}

// optional string SecuritySubType = 7;
void MDCfetsFxQuote::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxQuote::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
void MDCfetsFxQuote::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
void MDCfetsFxQuote::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}
::std::string* MDCfetsFxQuote::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxQuote::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.SecuritySubType)
}

// optional int32 ForexQuoteType = 8;
void MDCfetsFxQuote::clear_forexquotetype() {
  forexquotetype_ = 0;
}
::google::protobuf::int32 MDCfetsFxQuote::forexquotetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.ForexQuoteType)
  return forexquotetype_;
}
void MDCfetsFxQuote::set_forexquotetype(::google::protobuf::int32 value) {
  
  forexquotetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.ForexQuoteType)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote spotFxQuote = 9;
bool MDCfetsFxQuote::has_spotfxquote() const {
  return this != internal_default_instance() && spotfxquote_ != NULL;
}
void MDCfetsFxQuote::clear_spotfxquote() {
  if (GetArenaNoVirtual() == NULL && spotfxquote_ != NULL) delete spotfxquote_;
  spotfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::spotfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  return spotfxquote_ != NULL ? *spotfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_spotfxquote() {
  
  if (spotfxquote_ == NULL) {
    spotfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  return spotfxquote_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_spotfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = spotfxquote_;
  spotfxquote_ = NULL;
  return temp;
}
void MDCfetsFxQuote::set_allocated_spotfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* spotfxquote) {
  delete spotfxquote_;
  spotfxquote_ = spotfxquote;
  if (spotfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.spotFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote forwardFxQuote = 10;
bool MDCfetsFxQuote::has_forwardfxquote() const {
  return this != internal_default_instance() && forwardfxquote_ != NULL;
}
void MDCfetsFxQuote::clear_forwardfxquote() {
  if (GetArenaNoVirtual() == NULL && forwardfxquote_ != NULL) delete forwardfxquote_;
  forwardfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::forwardfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  return forwardfxquote_ != NULL ? *forwardfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_forwardfxquote() {
  
  if (forwardfxquote_ == NULL) {
    forwardfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  return forwardfxquote_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_forwardfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = forwardfxquote_;
  forwardfxquote_ = NULL;
  return temp;
}
void MDCfetsFxQuote::set_allocated_forwardfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* forwardfxquote) {
  delete forwardfxquote_;
  forwardfxquote_ = forwardfxquote;
  if (forwardfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.forwardFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote nonDeliverableForwardsFxQuote = 11;
bool MDCfetsFxQuote::has_nondeliverableforwardsfxquote() const {
  return this != internal_default_instance() && nondeliverableforwardsfxquote_ != NULL;
}
void MDCfetsFxQuote::clear_nondeliverableforwardsfxquote() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsfxquote_ != NULL) delete nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::nondeliverableforwardsfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  return nondeliverableforwardsfxquote_ != NULL ? *nondeliverableforwardsfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_nondeliverableforwardsfxquote() {
  
  if (nondeliverableforwardsfxquote_ == NULL) {
    nondeliverableforwardsfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  return nondeliverableforwardsfxquote_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_nondeliverableforwardsfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = NULL;
  return temp;
}
void MDCfetsFxQuote::set_allocated_nondeliverableforwardsfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* nondeliverableforwardsfxquote) {
  delete nondeliverableforwardsfxquote_;
  nondeliverableforwardsfxquote_ = nondeliverableforwardsfxquote;
  if (nondeliverableforwardsfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.nonDeliverableForwardsFxQuote)
}

// optional .com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote swapFxQuote = 12;
bool MDCfetsFxQuote::has_swapfxquote() const {
  return this != internal_default_instance() && swapfxquote_ != NULL;
}
void MDCfetsFxQuote::clear_swapfxquote() {
  if (GetArenaNoVirtual() == NULL && swapfxquote_ != NULL) delete swapfxquote_;
  swapfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote& MDCfetsFxQuote::swapfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  return swapfxquote_ != NULL ? *swapfxquote_
                         : *::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::mutable_swapfxquote() {
  
  if (swapfxquote_ == NULL) {
    swapfxquote_ = new ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  return swapfxquote_;
}
::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* MDCfetsFxQuote::release_swapfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
  
  ::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* temp = swapfxquote_;
  swapfxquote_ = NULL;
  return temp;
}
void MDCfetsFxQuote::set_allocated_swapfxquote(::com::htsc::mdc::insight::model::SwpSptNdfFowFxQuote* swapfxquote) {
  delete swapfxquote_;
  swapfxquote_ = swapfxquote;
  if (swapfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.swapFxQuote)
}

// optional .com.htsc.mdc.insight.model.OptionFxQuote optionFxQuote = 13;
bool MDCfetsFxQuote::has_optionfxquote() const {
  return this != internal_default_instance() && optionfxquote_ != NULL;
}
void MDCfetsFxQuote::clear_optionfxquote() {
  if (GetArenaNoVirtual() == NULL && optionfxquote_ != NULL) delete optionfxquote_;
  optionfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::OptionFxQuote& MDCfetsFxQuote::optionfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  return optionfxquote_ != NULL ? *optionfxquote_
                         : *::com::htsc::mdc::insight::model::OptionFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::OptionFxQuote* MDCfetsFxQuote::mutable_optionfxquote() {
  
  if (optionfxquote_ == NULL) {
    optionfxquote_ = new ::com::htsc::mdc::insight::model::OptionFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  return optionfxquote_;
}
::com::htsc::mdc::insight::model::OptionFxQuote* MDCfetsFxQuote::release_optionfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
  
  ::com::htsc::mdc::insight::model::OptionFxQuote* temp = optionfxquote_;
  optionfxquote_ = NULL;
  return temp;
}
void MDCfetsFxQuote::set_allocated_optionfxquote(::com::htsc::mdc::insight::model::OptionFxQuote* optionfxquote) {
  delete optionfxquote_;
  optionfxquote_ = optionfxquote;
  if (optionfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.optionFxQuote)
}

// optional int32 DataMultiplePowerOf10 = 14;
void MDCfetsFxQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsFxQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsFxQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.DataMultiplePowerOf10)
}

// optional string TransactTime = 15;
void MDCfetsFxQuote::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxQuote::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
void MDCfetsFxQuote::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
void MDCfetsFxQuote::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}
::std::string* MDCfetsFxQuote::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxQuote::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.TransactTime)
}

// optional string MarketIndicator = 16;
void MDCfetsFxQuote::clear_marketindicator() {
  marketindicator_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsFxQuote::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  return marketindicator_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_marketindicator(const ::std::string& value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
void MDCfetsFxQuote::set_marketindicator(const char* value) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
void MDCfetsFxQuote::set_marketindicator(const char* value, size_t size) {
  
  marketindicator_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}
::std::string* MDCfetsFxQuote::mutable_marketindicator() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  return marketindicator_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsFxQuote::release_marketindicator() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
  
  return marketindicator_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsFxQuote::set_allocated_marketindicator(::std::string* marketindicator) {
  if (marketindicator != NULL) {
    
  } else {
    
  }
  marketindicator_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketindicator);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsFxQuote.MarketIndicator)
}

inline const MDCfetsFxQuote* MDCfetsFxQuote::internal_default_instance() {
  return &MDCfetsFxQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SwpSptNdfFowFxQuote::kBestRateDateBuyFieldNumber;
const int SwpSptNdfFowFxQuote::kBestRateTimeBuyFieldNumber;
const int SwpSptNdfFowFxQuote::kBestRateDateSellFieldNumber;
const int SwpSptNdfFowFxQuote::kBestRateTimeSellFieldNumber;
const int SwpSptNdfFowFxQuote::kBestRateBuyFieldNumber;
const int SwpSptNdfFowFxQuote::kBestRateSellFieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderBuy1FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderBuy2FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderBuy3FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderBuy4FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderBuy5FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderSell1FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderSell2FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderSell3FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderSell4FieldNumber;
const int SwpSptNdfFowFxQuote::kRateLiquidProviderSell5FieldNumber;
const int SwpSptNdfFowFxQuote::kLegSignFieldNumber;
const int SwpSptNdfFowFxQuote::kTradeDateFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SwpSptNdfFowFxQuote::SwpSptNdfFowFxQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
}

void SwpSptNdfFowFxQuote::InitAsDefaultInstance() {
}

SwpSptNdfFowFxQuote::SwpSptNdfFowFxQuote(const SwpSptNdfFowFxQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
}

void SwpSptNdfFowFxQuote::SharedCtor() {
  bestratedatebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy5_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell1_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell5_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&bestratebuy_, 0, reinterpret_cast<char*>(&bestratesell_) -
    reinterpret_cast<char*>(&bestratebuy_) + sizeof(bestratesell_));
  _cached_size_ = 0;
}

SwpSptNdfFowFxQuote::~SwpSptNdfFowFxQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  SharedDtor();
}

void SwpSptNdfFowFxQuote::SharedDtor() {
  bestratedatebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy5_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell1_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell5_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SwpSptNdfFowFxQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SwpSptNdfFowFxQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SwpSptNdfFowFxQuote_descriptor_;
}

const SwpSptNdfFowFxQuote& SwpSptNdfFowFxQuote::default_instance() {
  protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SwpSptNdfFowFxQuote> SwpSptNdfFowFxQuote_default_instance_;

SwpSptNdfFowFxQuote* SwpSptNdfFowFxQuote::New(::google::protobuf::Arena* arena) const {
  SwpSptNdfFowFxQuote* n = new SwpSptNdfFowFxQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SwpSptNdfFowFxQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(SwpSptNdfFowFxQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<SwpSptNdfFowFxQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(bestratebuy_, bestratesell_);
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidproviderbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rateliquidprovidersell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool SwpSptNdfFowFxQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string BestRateDateBuy = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratedatebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratedatebuy().data(), this->bestratedatebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_BestRateTimeBuy;
        break;
      }

      // optional string BestRateTimeBuy = 2;
      case 2: {
        if (tag == 18) {
         parse_BestRateTimeBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratetimebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratetimebuy().data(), this->bestratetimebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_BestRateDateSell;
        break;
      }

      // optional string BestRateDateSell = 3;
      case 3: {
        if (tag == 26) {
         parse_BestRateDateSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratedatesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratedatesell().data(), this->bestratedatesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BestRateTimeSell;
        break;
      }

      // optional string BestRateTimeSell = 4;
      case 4: {
        if (tag == 34) {
         parse_BestRateTimeSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratetimesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratetimesell().data(), this->bestratetimesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_BestRateBuy;
        break;
      }

      // optional int64 BestRateBuy = 5;
      case 5: {
        if (tag == 40) {
         parse_BestRateBuy:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestratebuy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_BestRateSell;
        break;
      }

      // optional int64 BestRateSell = 6;
      case 6: {
        if (tag == 48) {
         parse_BestRateSell:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestratesell_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_RateLiquidProviderBuy1;
        break;
      }

      // optional string RateLiquidProviderBuy1 = 7;
      case 7: {
        if (tag == 58) {
         parse_RateLiquidProviderBuy1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidproviderbuy1()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidproviderbuy1().data(), this->rateliquidproviderbuy1().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_RateLiquidProviderBuy2;
        break;
      }

      // optional string RateLiquidProviderBuy2 = 8;
      case 8: {
        if (tag == 66) {
         parse_RateLiquidProviderBuy2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidproviderbuy2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidproviderbuy2().data(), this->rateliquidproviderbuy2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_RateLiquidProviderBuy3;
        break;
      }

      // optional string RateLiquidProviderBuy3 = 9;
      case 9: {
        if (tag == 74) {
         parse_RateLiquidProviderBuy3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidproviderbuy3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidproviderbuy3().data(), this->rateliquidproviderbuy3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_RateLiquidProviderBuy4;
        break;
      }

      // optional string RateLiquidProviderBuy4 = 10;
      case 10: {
        if (tag == 82) {
         parse_RateLiquidProviderBuy4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidproviderbuy4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidproviderbuy4().data(), this->rateliquidproviderbuy4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_RateLiquidProviderBuy5;
        break;
      }

      // optional string RateLiquidProviderBuy5 = 11;
      case 11: {
        if (tag == 90) {
         parse_RateLiquidProviderBuy5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidproviderbuy5()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidproviderbuy5().data(), this->rateliquidproviderbuy5().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_RateLiquidProviderSell1;
        break;
      }

      // optional string RateLiquidProviderSell1 = 12;
      case 12: {
        if (tag == 98) {
         parse_RateLiquidProviderSell1:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidprovidersell1()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidprovidersell1().data(), this->rateliquidprovidersell1().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_RateLiquidProviderSell2;
        break;
      }

      // optional string RateLiquidProviderSell2 = 13;
      case 13: {
        if (tag == 106) {
         parse_RateLiquidProviderSell2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidprovidersell2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidprovidersell2().data(), this->rateliquidprovidersell2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_RateLiquidProviderSell3;
        break;
      }

      // optional string RateLiquidProviderSell3 = 14;
      case 14: {
        if (tag == 114) {
         parse_RateLiquidProviderSell3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidprovidersell3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidprovidersell3().data(), this->rateliquidprovidersell3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_RateLiquidProviderSell4;
        break;
      }

      // optional string RateLiquidProviderSell4 = 15;
      case 15: {
        if (tag == 122) {
         parse_RateLiquidProviderSell4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidprovidersell4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidprovidersell4().data(), this->rateliquidprovidersell4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_RateLiquidProviderSell5;
        break;
      }

      // optional string RateLiquidProviderSell5 = 16;
      case 16: {
        if (tag == 130) {
         parse_RateLiquidProviderSell5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rateliquidprovidersell5()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rateliquidprovidersell5().data(), this->rateliquidprovidersell5().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_LegSign;
        break;
      }

      // optional string LegSign = 17;
      case 17: {
        if (tag == 138) {
         parse_LegSign:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_legsign()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->legsign().data(), this->legsign().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 18;
      case 18: {
        if (tag == 146) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  return false;
#undef DO_
}

void SwpSptNdfFowFxQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatebuy().data(), this->bestratedatebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->bestratedatebuy(), output);
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimebuy().data(), this->bestratetimebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->bestratetimebuy(), output);
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatesell().data(), this->bestratedatesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->bestratedatesell(), output);
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimesell().data(), this->bestratetimesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->bestratetimesell(), output);
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->bestratebuy(), output);
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->bestratesell(), output);
  }

  // optional string RateLiquidProviderBuy1 = 7;
  if (this->rateliquidproviderbuy1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy1().data(), this->rateliquidproviderbuy1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->rateliquidproviderbuy1(), output);
  }

  // optional string RateLiquidProviderBuy2 = 8;
  if (this->rateliquidproviderbuy2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy2().data(), this->rateliquidproviderbuy2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->rateliquidproviderbuy2(), output);
  }

  // optional string RateLiquidProviderBuy3 = 9;
  if (this->rateliquidproviderbuy3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy3().data(), this->rateliquidproviderbuy3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->rateliquidproviderbuy3(), output);
  }

  // optional string RateLiquidProviderBuy4 = 10;
  if (this->rateliquidproviderbuy4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy4().data(), this->rateliquidproviderbuy4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->rateliquidproviderbuy4(), output);
  }

  // optional string RateLiquidProviderBuy5 = 11;
  if (this->rateliquidproviderbuy5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy5().data(), this->rateliquidproviderbuy5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->rateliquidproviderbuy5(), output);
  }

  // optional string RateLiquidProviderSell1 = 12;
  if (this->rateliquidprovidersell1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell1().data(), this->rateliquidprovidersell1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->rateliquidprovidersell1(), output);
  }

  // optional string RateLiquidProviderSell2 = 13;
  if (this->rateliquidprovidersell2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell2().data(), this->rateliquidprovidersell2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->rateliquidprovidersell2(), output);
  }

  // optional string RateLiquidProviderSell3 = 14;
  if (this->rateliquidprovidersell3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell3().data(), this->rateliquidprovidersell3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->rateliquidprovidersell3(), output);
  }

  // optional string RateLiquidProviderSell4 = 15;
  if (this->rateliquidprovidersell4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell4().data(), this->rateliquidprovidersell4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->rateliquidprovidersell4(), output);
  }

  // optional string RateLiquidProviderSell5 = 16;
  if (this->rateliquidprovidersell5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell5().data(), this->rateliquidprovidersell5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->rateliquidprovidersell5(), output);
  }

  // optional string LegSign = 17;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->legsign(), output);
  }

  // optional string TradeDate = 18;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->tradedate(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
}

::google::protobuf::uint8* SwpSptNdfFowFxQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatebuy().data(), this->bestratedatebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->bestratedatebuy(), target);
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimebuy().data(), this->bestratetimebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->bestratetimebuy(), target);
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatesell().data(), this->bestratedatesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->bestratedatesell(), target);
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimesell().data(), this->bestratetimesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->bestratetimesell(), target);
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->bestratebuy(), target);
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->bestratesell(), target);
  }

  // optional string RateLiquidProviderBuy1 = 7;
  if (this->rateliquidproviderbuy1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy1().data(), this->rateliquidproviderbuy1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->rateliquidproviderbuy1(), target);
  }

  // optional string RateLiquidProviderBuy2 = 8;
  if (this->rateliquidproviderbuy2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy2().data(), this->rateliquidproviderbuy2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->rateliquidproviderbuy2(), target);
  }

  // optional string RateLiquidProviderBuy3 = 9;
  if (this->rateliquidproviderbuy3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy3().data(), this->rateliquidproviderbuy3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->rateliquidproviderbuy3(), target);
  }

  // optional string RateLiquidProviderBuy4 = 10;
  if (this->rateliquidproviderbuy4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy4().data(), this->rateliquidproviderbuy4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->rateliquidproviderbuy4(), target);
  }

  // optional string RateLiquidProviderBuy5 = 11;
  if (this->rateliquidproviderbuy5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidproviderbuy5().data(), this->rateliquidproviderbuy5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->rateliquidproviderbuy5(), target);
  }

  // optional string RateLiquidProviderSell1 = 12;
  if (this->rateliquidprovidersell1().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell1().data(), this->rateliquidprovidersell1().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->rateliquidprovidersell1(), target);
  }

  // optional string RateLiquidProviderSell2 = 13;
  if (this->rateliquidprovidersell2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell2().data(), this->rateliquidprovidersell2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->rateliquidprovidersell2(), target);
  }

  // optional string RateLiquidProviderSell3 = 14;
  if (this->rateliquidprovidersell3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell3().data(), this->rateliquidprovidersell3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->rateliquidprovidersell3(), target);
  }

  // optional string RateLiquidProviderSell4 = 15;
  if (this->rateliquidprovidersell4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell4().data(), this->rateliquidprovidersell4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->rateliquidprovidersell4(), target);
  }

  // optional string RateLiquidProviderSell5 = 16;
  if (this->rateliquidprovidersell5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rateliquidprovidersell5().data(), this->rateliquidprovidersell5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->rateliquidprovidersell5(), target);
  }

  // optional string LegSign = 17;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->legsign(), target);
  }

  // optional string TradeDate = 18;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->tradedate(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  return target;
}

size_t SwpSptNdfFowFxQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  size_t total_size = 0;

  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratedatebuy());
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratetimebuy());
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratedatesell());
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratetimesell());
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestratebuy());
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestratesell());
  }

  // optional string RateLiquidProviderBuy1 = 7;
  if (this->rateliquidproviderbuy1().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidproviderbuy1());
  }

  // optional string RateLiquidProviderBuy2 = 8;
  if (this->rateliquidproviderbuy2().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidproviderbuy2());
  }

  // optional string RateLiquidProviderBuy3 = 9;
  if (this->rateliquidproviderbuy3().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidproviderbuy3());
  }

  // optional string RateLiquidProviderBuy4 = 10;
  if (this->rateliquidproviderbuy4().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidproviderbuy4());
  }

  // optional string RateLiquidProviderBuy5 = 11;
  if (this->rateliquidproviderbuy5().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidproviderbuy5());
  }

  // optional string RateLiquidProviderSell1 = 12;
  if (this->rateliquidprovidersell1().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidprovidersell1());
  }

  // optional string RateLiquidProviderSell2 = 13;
  if (this->rateliquidprovidersell2().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidprovidersell2());
  }

  // optional string RateLiquidProviderSell3 = 14;
  if (this->rateliquidprovidersell3().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidprovidersell3());
  }

  // optional string RateLiquidProviderSell4 = 15;
  if (this->rateliquidprovidersell4().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidprovidersell4());
  }

  // optional string RateLiquidProviderSell5 = 16;
  if (this->rateliquidprovidersell5().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rateliquidprovidersell5());
  }

  // optional string LegSign = 17;
  if (this->legsign().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->legsign());
  }

  // optional string TradeDate = 18;
  if (this->tradedate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SwpSptNdfFowFxQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SwpSptNdfFowFxQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SwpSptNdfFowFxQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
    UnsafeMergeFrom(*source);
  }
}

void SwpSptNdfFowFxQuote::MergeFrom(const SwpSptNdfFowFxQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SwpSptNdfFowFxQuote::UnsafeMergeFrom(const SwpSptNdfFowFxQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.bestratedatebuy().size() > 0) {

    bestratedatebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratedatebuy_);
  }
  if (from.bestratetimebuy().size() > 0) {

    bestratetimebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratetimebuy_);
  }
  if (from.bestratedatesell().size() > 0) {

    bestratedatesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratedatesell_);
  }
  if (from.bestratetimesell().size() > 0) {

    bestratetimesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratetimesell_);
  }
  if (from.bestratebuy() != 0) {
    set_bestratebuy(from.bestratebuy());
  }
  if (from.bestratesell() != 0) {
    set_bestratesell(from.bestratesell());
  }
  if (from.rateliquidproviderbuy1().size() > 0) {

    rateliquidproviderbuy1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidproviderbuy1_);
  }
  if (from.rateliquidproviderbuy2().size() > 0) {

    rateliquidproviderbuy2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidproviderbuy2_);
  }
  if (from.rateliquidproviderbuy3().size() > 0) {

    rateliquidproviderbuy3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidproviderbuy3_);
  }
  if (from.rateliquidproviderbuy4().size() > 0) {

    rateliquidproviderbuy4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidproviderbuy4_);
  }
  if (from.rateliquidproviderbuy5().size() > 0) {

    rateliquidproviderbuy5_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidproviderbuy5_);
  }
  if (from.rateliquidprovidersell1().size() > 0) {

    rateliquidprovidersell1_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidprovidersell1_);
  }
  if (from.rateliquidprovidersell2().size() > 0) {

    rateliquidprovidersell2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidprovidersell2_);
  }
  if (from.rateliquidprovidersell3().size() > 0) {

    rateliquidprovidersell3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidprovidersell3_);
  }
  if (from.rateliquidprovidersell4().size() > 0) {

    rateliquidprovidersell4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidprovidersell4_);
  }
  if (from.rateliquidprovidersell5().size() > 0) {

    rateliquidprovidersell5_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rateliquidprovidersell5_);
  }
  if (from.legsign().size() > 0) {

    legsign_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.legsign_);
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
}

void SwpSptNdfFowFxQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SwpSptNdfFowFxQuote::CopyFrom(const SwpSptNdfFowFxQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SwpSptNdfFowFxQuote::IsInitialized() const {

  return true;
}

void SwpSptNdfFowFxQuote::Swap(SwpSptNdfFowFxQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SwpSptNdfFowFxQuote::InternalSwap(SwpSptNdfFowFxQuote* other) {
  bestratedatebuy_.Swap(&other->bestratedatebuy_);
  bestratetimebuy_.Swap(&other->bestratetimebuy_);
  bestratedatesell_.Swap(&other->bestratedatesell_);
  bestratetimesell_.Swap(&other->bestratetimesell_);
  std::swap(bestratebuy_, other->bestratebuy_);
  std::swap(bestratesell_, other->bestratesell_);
  rateliquidproviderbuy1_.Swap(&other->rateliquidproviderbuy1_);
  rateliquidproviderbuy2_.Swap(&other->rateliquidproviderbuy2_);
  rateliquidproviderbuy3_.Swap(&other->rateliquidproviderbuy3_);
  rateliquidproviderbuy4_.Swap(&other->rateliquidproviderbuy4_);
  rateliquidproviderbuy5_.Swap(&other->rateliquidproviderbuy5_);
  rateliquidprovidersell1_.Swap(&other->rateliquidprovidersell1_);
  rateliquidprovidersell2_.Swap(&other->rateliquidprovidersell2_);
  rateliquidprovidersell3_.Swap(&other->rateliquidprovidersell3_);
  rateliquidprovidersell4_.Swap(&other->rateliquidprovidersell4_);
  rateliquidprovidersell5_.Swap(&other->rateliquidprovidersell5_);
  legsign_.Swap(&other->legsign_);
  tradedate_.Swap(&other->tradedate_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SwpSptNdfFowFxQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SwpSptNdfFowFxQuote_descriptor_;
  metadata.reflection = SwpSptNdfFowFxQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SwpSptNdfFowFxQuote

// optional string BestRateDateBuy = 1;
void SwpSptNdfFowFxQuote::clear_bestratedatebuy() {
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::bestratedatebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  return bestratedatebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_bestratedatebuy(const ::std::string& value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
void SwpSptNdfFowFxQuote::set_bestratedatebuy(const char* value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
void SwpSptNdfFowFxQuote::set_bestratedatebuy(const char* value, size_t size) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}
::std::string* SwpSptNdfFowFxQuote::mutable_bestratedatebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  return bestratedatebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_bestratedatebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
  
  return bestratedatebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_bestratedatebuy(::std::string* bestratedatebuy) {
  if (bestratedatebuy != NULL) {
    
  } else {
    
  }
  bestratedatebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateBuy)
}

// optional string BestRateTimeBuy = 2;
void SwpSptNdfFowFxQuote::clear_bestratetimebuy() {
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::bestratetimebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_bestratetimebuy(const ::std::string& value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
void SwpSptNdfFowFxQuote::set_bestratetimebuy(const char* value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
void SwpSptNdfFowFxQuote::set_bestratetimebuy(const char* value, size_t size) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}
::std::string* SwpSptNdfFowFxQuote::mutable_bestratetimebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_bestratetimebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
  
  return bestratetimebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_bestratetimebuy(::std::string* bestratetimebuy) {
  if (bestratetimebuy != NULL) {
    
  } else {
    
  }
  bestratetimebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeBuy)
}

// optional string BestRateDateSell = 3;
void SwpSptNdfFowFxQuote::clear_bestratedatesell() {
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::bestratedatesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  return bestratedatesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_bestratedatesell(const ::std::string& value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
void SwpSptNdfFowFxQuote::set_bestratedatesell(const char* value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
void SwpSptNdfFowFxQuote::set_bestratedatesell(const char* value, size_t size) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}
::std::string* SwpSptNdfFowFxQuote::mutable_bestratedatesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  return bestratedatesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_bestratedatesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
  
  return bestratedatesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_bestratedatesell(::std::string* bestratedatesell) {
  if (bestratedatesell != NULL) {
    
  } else {
    
  }
  bestratedatesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateDateSell)
}

// optional string BestRateTimeSell = 4;
void SwpSptNdfFowFxQuote::clear_bestratetimesell() {
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::bestratetimesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  return bestratetimesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_bestratetimesell(const ::std::string& value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
void SwpSptNdfFowFxQuote::set_bestratetimesell(const char* value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
void SwpSptNdfFowFxQuote::set_bestratetimesell(const char* value, size_t size) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}
::std::string* SwpSptNdfFowFxQuote::mutable_bestratetimesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  return bestratetimesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_bestratetimesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
  
  return bestratetimesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_bestratetimesell(::std::string* bestratetimesell) {
  if (bestratetimesell != NULL) {
    
  } else {
    
  }
  bestratetimesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateTimeSell)
}

// optional int64 BestRateBuy = 5;
void SwpSptNdfFowFxQuote::clear_bestratebuy() {
  bestratebuy_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxQuote::bestratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateBuy)
  return bestratebuy_;
}
void SwpSptNdfFowFxQuote::set_bestratebuy(::google::protobuf::int64 value) {
  
  bestratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateBuy)
}

// optional int64 BestRateSell = 6;
void SwpSptNdfFowFxQuote::clear_bestratesell() {
  bestratesell_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwpSptNdfFowFxQuote::bestratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateSell)
  return bestratesell_;
}
void SwpSptNdfFowFxQuote::set_bestratesell(::google::protobuf::int64 value) {
  
  bestratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.BestRateSell)
}

// optional string RateLiquidProviderBuy1 = 7;
void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy1() {
  rateliquidproviderbuy1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  return rateliquidproviderbuy1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const ::std::string& value) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const char* value) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy1(const char* value, size_t size) {
  
  rateliquidproviderbuy1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  return rateliquidproviderbuy1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
  
  return rateliquidproviderbuy1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy1(::std::string* rateliquidproviderbuy1) {
  if (rateliquidproviderbuy1 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy1)
}

// optional string RateLiquidProviderBuy2 = 8;
void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy2() {
  rateliquidproviderbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  return rateliquidproviderbuy2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const ::std::string& value) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const char* value) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy2(const char* value, size_t size) {
  
  rateliquidproviderbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  return rateliquidproviderbuy2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
  
  return rateliquidproviderbuy2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy2(::std::string* rateliquidproviderbuy2) {
  if (rateliquidproviderbuy2 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy2)
}

// optional string RateLiquidProviderBuy3 = 9;
void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy3() {
  rateliquidproviderbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  return rateliquidproviderbuy3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const ::std::string& value) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const char* value) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy3(const char* value, size_t size) {
  
  rateliquidproviderbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  return rateliquidproviderbuy3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
  
  return rateliquidproviderbuy3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy3(::std::string* rateliquidproviderbuy3) {
  if (rateliquidproviderbuy3 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy3)
}

// optional string RateLiquidProviderBuy4 = 10;
void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy4() {
  rateliquidproviderbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  return rateliquidproviderbuy4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const ::std::string& value) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const char* value) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy4(const char* value, size_t size) {
  
  rateliquidproviderbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  return rateliquidproviderbuy4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
  
  return rateliquidproviderbuy4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy4(::std::string* rateliquidproviderbuy4) {
  if (rateliquidproviderbuy4 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy4)
}

// optional string RateLiquidProviderBuy5 = 11;
void SwpSptNdfFowFxQuote::clear_rateliquidproviderbuy5() {
  rateliquidproviderbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidproviderbuy5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  return rateliquidproviderbuy5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const ::std::string& value) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const char* value) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
void SwpSptNdfFowFxQuote::set_rateliquidproviderbuy5(const char* value, size_t size) {
  
  rateliquidproviderbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidproviderbuy5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  return rateliquidproviderbuy5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidproviderbuy5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
  
  return rateliquidproviderbuy5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidproviderbuy5(::std::string* rateliquidproviderbuy5) {
  if (rateliquidproviderbuy5 != NULL) {
    
  } else {
    
  }
  rateliquidproviderbuy5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidproviderbuy5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderBuy5)
}

// optional string RateLiquidProviderSell1 = 12;
void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell1() {
  rateliquidprovidersell1_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  return rateliquidprovidersell1_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const ::std::string& value) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const char* value) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell1(const char* value, size_t size) {
  
  rateliquidprovidersell1_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell1() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  return rateliquidprovidersell1_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell1() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
  
  return rateliquidprovidersell1_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell1(::std::string* rateliquidprovidersell1) {
  if (rateliquidprovidersell1 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell1_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell1);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell1)
}

// optional string RateLiquidProviderSell2 = 13;
void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell2() {
  rateliquidprovidersell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  return rateliquidprovidersell2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const ::std::string& value) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const char* value) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell2(const char* value, size_t size) {
  
  rateliquidprovidersell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  return rateliquidprovidersell2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
  
  return rateliquidprovidersell2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell2(::std::string* rateliquidprovidersell2) {
  if (rateliquidprovidersell2 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell2)
}

// optional string RateLiquidProviderSell3 = 14;
void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell3() {
  rateliquidprovidersell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  return rateliquidprovidersell3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const ::std::string& value) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const char* value) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell3(const char* value, size_t size) {
  
  rateliquidprovidersell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  return rateliquidprovidersell3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
  
  return rateliquidprovidersell3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell3(::std::string* rateliquidprovidersell3) {
  if (rateliquidprovidersell3 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell3)
}

// optional string RateLiquidProviderSell4 = 15;
void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell4() {
  rateliquidprovidersell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  return rateliquidprovidersell4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const ::std::string& value) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const char* value) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell4(const char* value, size_t size) {
  
  rateliquidprovidersell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  return rateliquidprovidersell4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
  
  return rateliquidprovidersell4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell4(::std::string* rateliquidprovidersell4) {
  if (rateliquidprovidersell4 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell4)
}

// optional string RateLiquidProviderSell5 = 16;
void SwpSptNdfFowFxQuote::clear_rateliquidprovidersell5() {
  rateliquidprovidersell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::rateliquidprovidersell5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  return rateliquidprovidersell5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const ::std::string& value) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const char* value) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
void SwpSptNdfFowFxQuote::set_rateliquidprovidersell5(const char* value, size_t size) {
  
  rateliquidprovidersell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}
::std::string* SwpSptNdfFowFxQuote::mutable_rateliquidprovidersell5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  return rateliquidprovidersell5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_rateliquidprovidersell5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
  
  return rateliquidprovidersell5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_rateliquidprovidersell5(::std::string* rateliquidprovidersell5) {
  if (rateliquidprovidersell5 != NULL) {
    
  } else {
    
  }
  rateliquidprovidersell5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rateliquidprovidersell5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.RateLiquidProviderSell5)
}

// optional string LegSign = 17;
void SwpSptNdfFowFxQuote::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
void SwpSptNdfFowFxQuote::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
void SwpSptNdfFowFxQuote::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}
::std::string* SwpSptNdfFowFxQuote::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.LegSign)
}

// optional string TradeDate = 18;
void SwpSptNdfFowFxQuote::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwpSptNdfFowFxQuote::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
void SwpSptNdfFowFxQuote::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
void SwpSptNdfFowFxQuote::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}
::std::string* SwpSptNdfFowFxQuote::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwpSptNdfFowFxQuote::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwpSptNdfFowFxQuote::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwpSptNdfFowFxQuote.TradeDate)
}

inline const SwpSptNdfFowFxQuote* SwpSptNdfFowFxQuote::internal_default_instance() {
  return &SwpSptNdfFowFxQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptionFxQuote::kBestRateDateBuyFieldNumber;
const int OptionFxQuote::kBestRateTimeBuyFieldNumber;
const int OptionFxQuote::kBestRateDateSellFieldNumber;
const int OptionFxQuote::kBestRateTimeSellFieldNumber;
const int OptionFxQuote::kBestRateBuyFieldNumber;
const int OptionFxQuote::kBestRateSellFieldNumber;
const int OptionFxQuote::kVolatilitySurfaceFieldNumber;
const int OptionFxQuote::kTenorBuyFieldNumber;
const int OptionFxQuote::kTenorSellFieldNumber;
const int OptionFxQuote::kMakerInstitutionBuyFieldNumber;
const int OptionFxQuote::kMakerInstitutionSellFieldNumber;
const int OptionFxQuote::kTenorFieldNumber;
const int OptionFxQuote::kTradeDateFieldNumber;
const int OptionFxQuote::kMakerInstitutionBuy2FieldNumber;
const int OptionFxQuote::kMakerInstitutionSell2FieldNumber;
const int OptionFxQuote::kMakerInstitutionBuy3FieldNumber;
const int OptionFxQuote::kMakerInstitutionSell3FieldNumber;
const int OptionFxQuote::kMakerInstitutionBuy4FieldNumber;
const int OptionFxQuote::kMakerInstitutionSell4FieldNumber;
const int OptionFxQuote::kMakerInstitutionBuy5FieldNumber;
const int OptionFxQuote::kMakerInstitutionSell5FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptionFxQuote::OptionFxQuote()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.OptionFxQuote)
}

void OptionFxQuote::InitAsDefaultInstance() {
}

OptionFxQuote::OptionFxQuote(const OptionFxQuote& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.OptionFxQuote)
}

void OptionFxQuote::SharedCtor() {
  bestratedatebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitysurface_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorbuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorsell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell2_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell3_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell4_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy5_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell5_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&bestratebuy_, 0, reinterpret_cast<char*>(&bestratesell_) -
    reinterpret_cast<char*>(&bestratebuy_) + sizeof(bestratesell_));
  _cached_size_ = 0;
}

OptionFxQuote::~OptionFxQuote() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.OptionFxQuote)
  SharedDtor();
}

void OptionFxQuote::SharedDtor() {
  bestratedatebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitysurface_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorbuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorsell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell2_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell3_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell4_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy5_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell5_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OptionFxQuote::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OptionFxQuote::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OptionFxQuote_descriptor_;
}

const OptionFxQuote& OptionFxQuote::default_instance() {
  protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OptionFxQuote> OptionFxQuote_default_instance_;

OptionFxQuote* OptionFxQuote::New(::google::protobuf::Arena* arena) const {
  OptionFxQuote* n = new OptionFxQuote;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OptionFxQuote::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.OptionFxQuote)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(OptionFxQuote, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<OptionFxQuote*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(bestratebuy_, bestratesell_);
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenorsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  makerinstitutionsell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool OptionFxQuote::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.OptionFxQuote)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string BestRateDateBuy = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratedatebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratedatebuy().data(), this->bestratedatebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_BestRateTimeBuy;
        break;
      }

      // optional string BestRateTimeBuy = 2;
      case 2: {
        if (tag == 18) {
         parse_BestRateTimeBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratetimebuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratetimebuy().data(), this->bestratetimebuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_BestRateDateSell;
        break;
      }

      // optional string BestRateDateSell = 3;
      case 3: {
        if (tag == 26) {
         parse_BestRateDateSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratedatesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratedatesell().data(), this->bestratedatesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BestRateTimeSell;
        break;
      }

      // optional string BestRateTimeSell = 4;
      case 4: {
        if (tag == 34) {
         parse_BestRateTimeSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bestratetimesell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bestratetimesell().data(), this->bestratetimesell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_BestRateBuy;
        break;
      }

      // optional int64 BestRateBuy = 5;
      case 5: {
        if (tag == 40) {
         parse_BestRateBuy:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestratebuy_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_BestRateSell;
        break;
      }

      // optional int64 BestRateSell = 6;
      case 6: {
        if (tag == 48) {
         parse_BestRateSell:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestratesell_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_VolatilitySurface;
        break;
      }

      // optional string VolatilitySurface = 7;
      case 7: {
        if (tag == 58) {
         parse_VolatilitySurface:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_volatilitysurface()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->volatilitysurface().data(), this->volatilitysurface().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_TenorBuy;
        break;
      }

      // optional string TenorBuy = 8;
      case 8: {
        if (tag == 66) {
         parse_TenorBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenorbuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenorbuy().data(), this->tenorbuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_TenorSell;
        break;
      }

      // optional string TenorSell = 9;
      case 9: {
        if (tag == 74) {
         parse_TenorSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenorsell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenorsell().data(), this->tenorsell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.TenorSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_MakerInstitutionBuy;
        break;
      }

      // optional string MakerInstitutionBuy = 10;
      case 10: {
        if (tag == 82) {
         parse_MakerInstitutionBuy:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionbuy()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionbuy().data(), this->makerinstitutionbuy().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_MakerInstitutionSell;
        break;
      }

      // optional string MakerInstitutionSell = 11;
      case 11: {
        if (tag == 90) {
         parse_MakerInstitutionSell:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionsell()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionsell().data(), this->makerinstitutionsell().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_Tenor;
        break;
      }

      // optional string Tenor = 12;
      case 12: {
        if (tag == 98) {
         parse_Tenor:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenor().data(), this->tenor().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.Tenor"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 13;
      case 13: {
        if (tag == 106) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_MakerInstitutionBuy2;
        break;
      }

      // optional string MakerInstitutionBuy2 = 14;
      case 14: {
        if (tag == 114) {
         parse_MakerInstitutionBuy2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionbuy2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionbuy2().data(), this->makerinstitutionbuy2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_MakerInstitutionSell2;
        break;
      }

      // optional string MakerInstitutionSell2 = 15;
      case 15: {
        if (tag == 122) {
         parse_MakerInstitutionSell2:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionsell2()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionsell2().data(), this->makerinstitutionsell2().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_MakerInstitutionBuy3;
        break;
      }

      // optional string MakerInstitutionBuy3 = 16;
      case 16: {
        if (tag == 130) {
         parse_MakerInstitutionBuy3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionbuy3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionbuy3().data(), this->makerinstitutionbuy3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_MakerInstitutionSell3;
        break;
      }

      // optional string MakerInstitutionSell3 = 17;
      case 17: {
        if (tag == 138) {
         parse_MakerInstitutionSell3:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionsell3()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionsell3().data(), this->makerinstitutionsell3().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_MakerInstitutionBuy4;
        break;
      }

      // optional string MakerInstitutionBuy4 = 18;
      case 18: {
        if (tag == 146) {
         parse_MakerInstitutionBuy4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionbuy4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionbuy4().data(), this->makerinstitutionbuy4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_MakerInstitutionSell4;
        break;
      }

      // optional string MakerInstitutionSell4 = 19;
      case 19: {
        if (tag == 154) {
         parse_MakerInstitutionSell4:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionsell4()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionsell4().data(), this->makerinstitutionsell4().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_MakerInstitutionBuy5;
        break;
      }

      // optional string MakerInstitutionBuy5 = 20;
      case 20: {
        if (tag == 162) {
         parse_MakerInstitutionBuy5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionbuy5()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionbuy5().data(), this->makerinstitutionbuy5().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_MakerInstitutionSell5;
        break;
      }

      // optional string MakerInstitutionSell5 = 21;
      case 21: {
        if (tag == 170) {
         parse_MakerInstitutionSell5:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_makerinstitutionsell5()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->makerinstitutionsell5().data(), this->makerinstitutionsell5().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.OptionFxQuote)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.OptionFxQuote)
  return false;
#undef DO_
}

void OptionFxQuote::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.OptionFxQuote)
  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatebuy().data(), this->bestratedatebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->bestratedatebuy(), output);
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimebuy().data(), this->bestratetimebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->bestratetimebuy(), output);
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatesell().data(), this->bestratedatesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->bestratedatesell(), output);
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimesell().data(), this->bestratetimesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->bestratetimesell(), output);
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->bestratebuy(), output);
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->bestratesell(), output);
  }

  // optional string VolatilitySurface = 7;
  if (this->volatilitysurface().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitysurface().data(), this->volatilitysurface().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->volatilitysurface(), output);
  }

  // optional string TenorBuy = 8;
  if (this->tenorbuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenorbuy().data(), this->tenorbuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->tenorbuy(), output);
  }

  // optional string TenorSell = 9;
  if (this->tenorsell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenorsell().data(), this->tenorsell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TenorSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->tenorsell(), output);
  }

  // optional string MakerInstitutionBuy = 10;
  if (this->makerinstitutionbuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy().data(), this->makerinstitutionbuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->makerinstitutionbuy(), output);
  }

  // optional string MakerInstitutionSell = 11;
  if (this->makerinstitutionsell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell().data(), this->makerinstitutionsell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->makerinstitutionsell(), output);
  }

  // optional string Tenor = 12;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.Tenor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->tenor(), output);
  }

  // optional string TradeDate = 13;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->tradedate(), output);
  }

  // optional string MakerInstitutionBuy2 = 14;
  if (this->makerinstitutionbuy2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy2().data(), this->makerinstitutionbuy2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      14, this->makerinstitutionbuy2(), output);
  }

  // optional string MakerInstitutionSell2 = 15;
  if (this->makerinstitutionsell2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell2().data(), this->makerinstitutionsell2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->makerinstitutionsell2(), output);
  }

  // optional string MakerInstitutionBuy3 = 16;
  if (this->makerinstitutionbuy3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy3().data(), this->makerinstitutionbuy3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->makerinstitutionbuy3(), output);
  }

  // optional string MakerInstitutionSell3 = 17;
  if (this->makerinstitutionsell3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell3().data(), this->makerinstitutionsell3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->makerinstitutionsell3(), output);
  }

  // optional string MakerInstitutionBuy4 = 18;
  if (this->makerinstitutionbuy4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy4().data(), this->makerinstitutionbuy4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      18, this->makerinstitutionbuy4(), output);
  }

  // optional string MakerInstitutionSell4 = 19;
  if (this->makerinstitutionsell4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell4().data(), this->makerinstitutionsell4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->makerinstitutionsell4(), output);
  }

  // optional string MakerInstitutionBuy5 = 20;
  if (this->makerinstitutionbuy5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy5().data(), this->makerinstitutionbuy5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->makerinstitutionbuy5(), output);
  }

  // optional string MakerInstitutionSell5 = 21;
  if (this->makerinstitutionsell5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell5().data(), this->makerinstitutionsell5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->makerinstitutionsell5(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.OptionFxQuote)
}

::google::protobuf::uint8* OptionFxQuote::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.OptionFxQuote)
  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatebuy().data(), this->bestratedatebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->bestratedatebuy(), target);
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimebuy().data(), this->bestratetimebuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->bestratetimebuy(), target);
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratedatesell().data(), this->bestratedatesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->bestratedatesell(), target);
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bestratetimesell().data(), this->bestratetimesell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->bestratetimesell(), target);
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->bestratebuy(), target);
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->bestratesell(), target);
  }

  // optional string VolatilitySurface = 7;
  if (this->volatilitysurface().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->volatilitysurface().data(), this->volatilitysurface().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->volatilitysurface(), target);
  }

  // optional string TenorBuy = 8;
  if (this->tenorbuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenorbuy().data(), this->tenorbuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->tenorbuy(), target);
  }

  // optional string TenorSell = 9;
  if (this->tenorsell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenorsell().data(), this->tenorsell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TenorSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->tenorsell(), target);
  }

  // optional string MakerInstitutionBuy = 10;
  if (this->makerinstitutionbuy().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy().data(), this->makerinstitutionbuy().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->makerinstitutionbuy(), target);
  }

  // optional string MakerInstitutionSell = 11;
  if (this->makerinstitutionsell().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell().data(), this->makerinstitutionsell().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->makerinstitutionsell(), target);
  }

  // optional string Tenor = 12;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.Tenor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->tenor(), target);
  }

  // optional string TradeDate = 13;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->tradedate(), target);
  }

  // optional string MakerInstitutionBuy2 = 14;
  if (this->makerinstitutionbuy2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy2().data(), this->makerinstitutionbuy2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        14, this->makerinstitutionbuy2(), target);
  }

  // optional string MakerInstitutionSell2 = 15;
  if (this->makerinstitutionsell2().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell2().data(), this->makerinstitutionsell2().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->makerinstitutionsell2(), target);
  }

  // optional string MakerInstitutionBuy3 = 16;
  if (this->makerinstitutionbuy3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy3().data(), this->makerinstitutionbuy3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->makerinstitutionbuy3(), target);
  }

  // optional string MakerInstitutionSell3 = 17;
  if (this->makerinstitutionsell3().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell3().data(), this->makerinstitutionsell3().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->makerinstitutionsell3(), target);
  }

  // optional string MakerInstitutionBuy4 = 18;
  if (this->makerinstitutionbuy4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy4().data(), this->makerinstitutionbuy4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        18, this->makerinstitutionbuy4(), target);
  }

  // optional string MakerInstitutionSell4 = 19;
  if (this->makerinstitutionsell4().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell4().data(), this->makerinstitutionsell4().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->makerinstitutionsell4(), target);
  }

  // optional string MakerInstitutionBuy5 = 20;
  if (this->makerinstitutionbuy5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionbuy5().data(), this->makerinstitutionbuy5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->makerinstitutionbuy5(), target);
  }

  // optional string MakerInstitutionSell5 = 21;
  if (this->makerinstitutionsell5().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->makerinstitutionsell5().data(), this->makerinstitutionsell5().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->makerinstitutionsell5(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.OptionFxQuote)
  return target;
}

size_t OptionFxQuote::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.OptionFxQuote)
  size_t total_size = 0;

  // optional string BestRateDateBuy = 1;
  if (this->bestratedatebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratedatebuy());
  }

  // optional string BestRateTimeBuy = 2;
  if (this->bestratetimebuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratetimebuy());
  }

  // optional string BestRateDateSell = 3;
  if (this->bestratedatesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratedatesell());
  }

  // optional string BestRateTimeSell = 4;
  if (this->bestratetimesell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bestratetimesell());
  }

  // optional int64 BestRateBuy = 5;
  if (this->bestratebuy() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestratebuy());
  }

  // optional int64 BestRateSell = 6;
  if (this->bestratesell() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestratesell());
  }

  // optional string VolatilitySurface = 7;
  if (this->volatilitysurface().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->volatilitysurface());
  }

  // optional string TenorBuy = 8;
  if (this->tenorbuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenorbuy());
  }

  // optional string TenorSell = 9;
  if (this->tenorsell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenorsell());
  }

  // optional string MakerInstitutionBuy = 10;
  if (this->makerinstitutionbuy().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionbuy());
  }

  // optional string MakerInstitutionSell = 11;
  if (this->makerinstitutionsell().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionsell());
  }

  // optional string Tenor = 12;
  if (this->tenor().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenor());
  }

  // optional string TradeDate = 13;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string MakerInstitutionBuy2 = 14;
  if (this->makerinstitutionbuy2().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionbuy2());
  }

  // optional string MakerInstitutionSell2 = 15;
  if (this->makerinstitutionsell2().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionsell2());
  }

  // optional string MakerInstitutionBuy3 = 16;
  if (this->makerinstitutionbuy3().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionbuy3());
  }

  // optional string MakerInstitutionSell3 = 17;
  if (this->makerinstitutionsell3().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionsell3());
  }

  // optional string MakerInstitutionBuy4 = 18;
  if (this->makerinstitutionbuy4().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionbuy4());
  }

  // optional string MakerInstitutionSell4 = 19;
  if (this->makerinstitutionsell4().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionsell4());
  }

  // optional string MakerInstitutionBuy5 = 20;
  if (this->makerinstitutionbuy5().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionbuy5());
  }

  // optional string MakerInstitutionSell5 = 21;
  if (this->makerinstitutionsell5().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->makerinstitutionsell5());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OptionFxQuote::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.OptionFxQuote)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OptionFxQuote* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptionFxQuote>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.OptionFxQuote)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.OptionFxQuote)
    UnsafeMergeFrom(*source);
  }
}

void OptionFxQuote::MergeFrom(const OptionFxQuote& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.OptionFxQuote)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OptionFxQuote::UnsafeMergeFrom(const OptionFxQuote& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.bestratedatebuy().size() > 0) {

    bestratedatebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratedatebuy_);
  }
  if (from.bestratetimebuy().size() > 0) {

    bestratetimebuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratetimebuy_);
  }
  if (from.bestratedatesell().size() > 0) {

    bestratedatesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratedatesell_);
  }
  if (from.bestratetimesell().size() > 0) {

    bestratetimesell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bestratetimesell_);
  }
  if (from.bestratebuy() != 0) {
    set_bestratebuy(from.bestratebuy());
  }
  if (from.bestratesell() != 0) {
    set_bestratesell(from.bestratesell());
  }
  if (from.volatilitysurface().size() > 0) {

    volatilitysurface_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.volatilitysurface_);
  }
  if (from.tenorbuy().size() > 0) {

    tenorbuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenorbuy_);
  }
  if (from.tenorsell().size() > 0) {

    tenorsell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenorsell_);
  }
  if (from.makerinstitutionbuy().size() > 0) {

    makerinstitutionbuy_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionbuy_);
  }
  if (from.makerinstitutionsell().size() > 0) {

    makerinstitutionsell_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionsell_);
  }
  if (from.tenor().size() > 0) {

    tenor_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenor_);
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.makerinstitutionbuy2().size() > 0) {

    makerinstitutionbuy2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionbuy2_);
  }
  if (from.makerinstitutionsell2().size() > 0) {

    makerinstitutionsell2_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionsell2_);
  }
  if (from.makerinstitutionbuy3().size() > 0) {

    makerinstitutionbuy3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionbuy3_);
  }
  if (from.makerinstitutionsell3().size() > 0) {

    makerinstitutionsell3_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionsell3_);
  }
  if (from.makerinstitutionbuy4().size() > 0) {

    makerinstitutionbuy4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionbuy4_);
  }
  if (from.makerinstitutionsell4().size() > 0) {

    makerinstitutionsell4_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionsell4_);
  }
  if (from.makerinstitutionbuy5().size() > 0) {

    makerinstitutionbuy5_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionbuy5_);
  }
  if (from.makerinstitutionsell5().size() > 0) {

    makerinstitutionsell5_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.makerinstitutionsell5_);
  }
}

void OptionFxQuote::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.OptionFxQuote)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptionFxQuote::CopyFrom(const OptionFxQuote& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.OptionFxQuote)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OptionFxQuote::IsInitialized() const {

  return true;
}

void OptionFxQuote::Swap(OptionFxQuote* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OptionFxQuote::InternalSwap(OptionFxQuote* other) {
  bestratedatebuy_.Swap(&other->bestratedatebuy_);
  bestratetimebuy_.Swap(&other->bestratetimebuy_);
  bestratedatesell_.Swap(&other->bestratedatesell_);
  bestratetimesell_.Swap(&other->bestratetimesell_);
  std::swap(bestratebuy_, other->bestratebuy_);
  std::swap(bestratesell_, other->bestratesell_);
  volatilitysurface_.Swap(&other->volatilitysurface_);
  tenorbuy_.Swap(&other->tenorbuy_);
  tenorsell_.Swap(&other->tenorsell_);
  makerinstitutionbuy_.Swap(&other->makerinstitutionbuy_);
  makerinstitutionsell_.Swap(&other->makerinstitutionsell_);
  tenor_.Swap(&other->tenor_);
  tradedate_.Swap(&other->tradedate_);
  makerinstitutionbuy2_.Swap(&other->makerinstitutionbuy2_);
  makerinstitutionsell2_.Swap(&other->makerinstitutionsell2_);
  makerinstitutionbuy3_.Swap(&other->makerinstitutionbuy3_);
  makerinstitutionsell3_.Swap(&other->makerinstitutionsell3_);
  makerinstitutionbuy4_.Swap(&other->makerinstitutionbuy4_);
  makerinstitutionsell4_.Swap(&other->makerinstitutionsell4_);
  makerinstitutionbuy5_.Swap(&other->makerinstitutionbuy5_);
  makerinstitutionsell5_.Swap(&other->makerinstitutionsell5_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OptionFxQuote::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OptionFxQuote_descriptor_;
  metadata.reflection = OptionFxQuote_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OptionFxQuote

// optional string BestRateDateBuy = 1;
void OptionFxQuote::clear_bestratedatebuy() {
  bestratedatebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::bestratedatebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  return bestratedatebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_bestratedatebuy(const ::std::string& value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
void OptionFxQuote::set_bestratedatebuy(const char* value) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
void OptionFxQuote::set_bestratedatebuy(const char* value, size_t size) {
  
  bestratedatebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}
::std::string* OptionFxQuote::mutable_bestratedatebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  return bestratedatebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_bestratedatebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
  
  return bestratedatebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_bestratedatebuy(::std::string* bestratedatebuy) {
  if (bestratedatebuy != NULL) {
    
  } else {
    
  }
  bestratedatebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateBuy)
}

// optional string BestRateTimeBuy = 2;
void OptionFxQuote::clear_bestratetimebuy() {
  bestratetimebuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::bestratetimebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_bestratetimebuy(const ::std::string& value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
void OptionFxQuote::set_bestratetimebuy(const char* value) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
void OptionFxQuote::set_bestratetimebuy(const char* value, size_t size) {
  
  bestratetimebuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}
::std::string* OptionFxQuote::mutable_bestratetimebuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  return bestratetimebuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_bestratetimebuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
  
  return bestratetimebuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_bestratetimebuy(::std::string* bestratetimebuy) {
  if (bestratetimebuy != NULL) {
    
  } else {
    
  }
  bestratetimebuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimebuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeBuy)
}

// optional string BestRateDateSell = 3;
void OptionFxQuote::clear_bestratedatesell() {
  bestratedatesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::bestratedatesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  return bestratedatesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_bestratedatesell(const ::std::string& value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
void OptionFxQuote::set_bestratedatesell(const char* value) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
void OptionFxQuote::set_bestratedatesell(const char* value, size_t size) {
  
  bestratedatesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}
::std::string* OptionFxQuote::mutable_bestratedatesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  return bestratedatesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_bestratedatesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
  
  return bestratedatesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_bestratedatesell(::std::string* bestratedatesell) {
  if (bestratedatesell != NULL) {
    
  } else {
    
  }
  bestratedatesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratedatesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateDateSell)
}

// optional string BestRateTimeSell = 4;
void OptionFxQuote::clear_bestratetimesell() {
  bestratetimesell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::bestratetimesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  return bestratetimesell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_bestratetimesell(const ::std::string& value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
void OptionFxQuote::set_bestratetimesell(const char* value) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
void OptionFxQuote::set_bestratetimesell(const char* value, size_t size) {
  
  bestratetimesell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}
::std::string* OptionFxQuote::mutable_bestratetimesell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  return bestratetimesell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_bestratetimesell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
  
  return bestratetimesell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_bestratetimesell(::std::string* bestratetimesell) {
  if (bestratetimesell != NULL) {
    
  } else {
    
  }
  bestratetimesell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bestratetimesell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.BestRateTimeSell)
}

// optional int64 BestRateBuy = 5;
void OptionFxQuote::clear_bestratebuy() {
  bestratebuy_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionFxQuote::bestratebuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateBuy)
  return bestratebuy_;
}
void OptionFxQuote::set_bestratebuy(::google::protobuf::int64 value) {
  
  bestratebuy_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateBuy)
}

// optional int64 BestRateSell = 6;
void OptionFxQuote::clear_bestratesell() {
  bestratesell_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionFxQuote::bestratesell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.BestRateSell)
  return bestratesell_;
}
void OptionFxQuote::set_bestratesell(::google::protobuf::int64 value) {
  
  bestratesell_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.BestRateSell)
}

// optional string VolatilitySurface = 7;
void OptionFxQuote::clear_volatilitysurface() {
  volatilitysurface_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::volatilitysurface() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  return volatilitysurface_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_volatilitysurface(const ::std::string& value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
void OptionFxQuote::set_volatilitysurface(const char* value) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
void OptionFxQuote::set_volatilitysurface(const char* value, size_t size) {
  
  volatilitysurface_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}
::std::string* OptionFxQuote::mutable_volatilitysurface() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  return volatilitysurface_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_volatilitysurface() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
  
  return volatilitysurface_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_volatilitysurface(::std::string* volatilitysurface) {
  if (volatilitysurface != NULL) {
    
  } else {
    
  }
  volatilitysurface_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), volatilitysurface);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.VolatilitySurface)
}

// optional string TenorBuy = 8;
void OptionFxQuote::clear_tenorbuy() {
  tenorbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::tenorbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  return tenorbuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_tenorbuy(const ::std::string& value) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
void OptionFxQuote::set_tenorbuy(const char* value) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
void OptionFxQuote::set_tenorbuy(const char* value, size_t size) {
  
  tenorbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}
::std::string* OptionFxQuote::mutable_tenorbuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  return tenorbuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_tenorbuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
  
  return tenorbuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_tenorbuy(::std::string* tenorbuy) {
  if (tenorbuy != NULL) {
    
  } else {
    
  }
  tenorbuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenorbuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TenorBuy)
}

// optional string TenorSell = 9;
void OptionFxQuote::clear_tenorsell() {
  tenorsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::tenorsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  return tenorsell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_tenorsell(const ::std::string& value) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
void OptionFxQuote::set_tenorsell(const char* value) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
void OptionFxQuote::set_tenorsell(const char* value, size_t size) {
  
  tenorsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}
::std::string* OptionFxQuote::mutable_tenorsell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  return tenorsell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_tenorsell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
  
  return tenorsell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_tenorsell(::std::string* tenorsell) {
  if (tenorsell != NULL) {
    
  } else {
    
  }
  tenorsell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenorsell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TenorSell)
}

// optional string MakerInstitutionBuy = 10;
void OptionFxQuote::clear_makerinstitutionbuy() {
  makerinstitutionbuy_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionbuy() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  return makerinstitutionbuy_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionbuy(const ::std::string& value) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
void OptionFxQuote::set_makerinstitutionbuy(const char* value) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
void OptionFxQuote::set_makerinstitutionbuy(const char* value, size_t size) {
  
  makerinstitutionbuy_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}
::std::string* OptionFxQuote::mutable_makerinstitutionbuy() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  return makerinstitutionbuy_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionbuy() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
  
  return makerinstitutionbuy_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionbuy(::std::string* makerinstitutionbuy) {
  if (makerinstitutionbuy != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy)
}

// optional string MakerInstitutionSell = 11;
void OptionFxQuote::clear_makerinstitutionsell() {
  makerinstitutionsell_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionsell() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  return makerinstitutionsell_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionsell(const ::std::string& value) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
void OptionFxQuote::set_makerinstitutionsell(const char* value) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
void OptionFxQuote::set_makerinstitutionsell(const char* value, size_t size) {
  
  makerinstitutionsell_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}
::std::string* OptionFxQuote::mutable_makerinstitutionsell() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  return makerinstitutionsell_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionsell() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
  
  return makerinstitutionsell_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionsell(::std::string* makerinstitutionsell) {
  if (makerinstitutionsell != NULL) {
    
  } else {
    
  }
  makerinstitutionsell_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell)
}

// optional string Tenor = 12;
void OptionFxQuote::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
void OptionFxQuote::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
void OptionFxQuote::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}
::std::string* OptionFxQuote::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.Tenor)
}

// optional string TradeDate = 13;
void OptionFxQuote::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
void OptionFxQuote::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
void OptionFxQuote::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}
::std::string* OptionFxQuote::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.TradeDate)
}

// optional string MakerInstitutionBuy2 = 14;
void OptionFxQuote::clear_makerinstitutionbuy2() {
  makerinstitutionbuy2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionbuy2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  return makerinstitutionbuy2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionbuy2(const ::std::string& value) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
void OptionFxQuote::set_makerinstitutionbuy2(const char* value) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
void OptionFxQuote::set_makerinstitutionbuy2(const char* value, size_t size) {
  
  makerinstitutionbuy2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}
::std::string* OptionFxQuote::mutable_makerinstitutionbuy2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  return makerinstitutionbuy2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionbuy2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
  
  return makerinstitutionbuy2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionbuy2(::std::string* makerinstitutionbuy2) {
  if (makerinstitutionbuy2 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy2)
}

// optional string MakerInstitutionSell2 = 15;
void OptionFxQuote::clear_makerinstitutionsell2() {
  makerinstitutionsell2_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionsell2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  return makerinstitutionsell2_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionsell2(const ::std::string& value) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
void OptionFxQuote::set_makerinstitutionsell2(const char* value) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
void OptionFxQuote::set_makerinstitutionsell2(const char* value, size_t size) {
  
  makerinstitutionsell2_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}
::std::string* OptionFxQuote::mutable_makerinstitutionsell2() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  return makerinstitutionsell2_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionsell2() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
  
  return makerinstitutionsell2_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionsell2(::std::string* makerinstitutionsell2) {
  if (makerinstitutionsell2 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell2_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell2);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell2)
}

// optional string MakerInstitutionBuy3 = 16;
void OptionFxQuote::clear_makerinstitutionbuy3() {
  makerinstitutionbuy3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionbuy3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  return makerinstitutionbuy3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionbuy3(const ::std::string& value) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
void OptionFxQuote::set_makerinstitutionbuy3(const char* value) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
void OptionFxQuote::set_makerinstitutionbuy3(const char* value, size_t size) {
  
  makerinstitutionbuy3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}
::std::string* OptionFxQuote::mutable_makerinstitutionbuy3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  return makerinstitutionbuy3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionbuy3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
  
  return makerinstitutionbuy3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionbuy3(::std::string* makerinstitutionbuy3) {
  if (makerinstitutionbuy3 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy3)
}

// optional string MakerInstitutionSell3 = 17;
void OptionFxQuote::clear_makerinstitutionsell3() {
  makerinstitutionsell3_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionsell3() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  return makerinstitutionsell3_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionsell3(const ::std::string& value) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
void OptionFxQuote::set_makerinstitutionsell3(const char* value) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
void OptionFxQuote::set_makerinstitutionsell3(const char* value, size_t size) {
  
  makerinstitutionsell3_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}
::std::string* OptionFxQuote::mutable_makerinstitutionsell3() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  return makerinstitutionsell3_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionsell3() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
  
  return makerinstitutionsell3_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionsell3(::std::string* makerinstitutionsell3) {
  if (makerinstitutionsell3 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell3_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell3);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell3)
}

// optional string MakerInstitutionBuy4 = 18;
void OptionFxQuote::clear_makerinstitutionbuy4() {
  makerinstitutionbuy4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionbuy4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  return makerinstitutionbuy4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionbuy4(const ::std::string& value) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
void OptionFxQuote::set_makerinstitutionbuy4(const char* value) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
void OptionFxQuote::set_makerinstitutionbuy4(const char* value, size_t size) {
  
  makerinstitutionbuy4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}
::std::string* OptionFxQuote::mutable_makerinstitutionbuy4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  return makerinstitutionbuy4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionbuy4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
  
  return makerinstitutionbuy4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionbuy4(::std::string* makerinstitutionbuy4) {
  if (makerinstitutionbuy4 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy4)
}

// optional string MakerInstitutionSell4 = 19;
void OptionFxQuote::clear_makerinstitutionsell4() {
  makerinstitutionsell4_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionsell4() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  return makerinstitutionsell4_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionsell4(const ::std::string& value) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
void OptionFxQuote::set_makerinstitutionsell4(const char* value) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
void OptionFxQuote::set_makerinstitutionsell4(const char* value, size_t size) {
  
  makerinstitutionsell4_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}
::std::string* OptionFxQuote::mutable_makerinstitutionsell4() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  return makerinstitutionsell4_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionsell4() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
  
  return makerinstitutionsell4_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionsell4(::std::string* makerinstitutionsell4) {
  if (makerinstitutionsell4 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell4_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell4);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell4)
}

// optional string MakerInstitutionBuy5 = 20;
void OptionFxQuote::clear_makerinstitutionbuy5() {
  makerinstitutionbuy5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionbuy5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  return makerinstitutionbuy5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionbuy5(const ::std::string& value) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
void OptionFxQuote::set_makerinstitutionbuy5(const char* value) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
void OptionFxQuote::set_makerinstitutionbuy5(const char* value, size_t size) {
  
  makerinstitutionbuy5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}
::std::string* OptionFxQuote::mutable_makerinstitutionbuy5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  return makerinstitutionbuy5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionbuy5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
  
  return makerinstitutionbuy5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionbuy5(::std::string* makerinstitutionbuy5) {
  if (makerinstitutionbuy5 != NULL) {
    
  } else {
    
  }
  makerinstitutionbuy5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionbuy5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionBuy5)
}

// optional string MakerInstitutionSell5 = 21;
void OptionFxQuote::clear_makerinstitutionsell5() {
  makerinstitutionsell5_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionFxQuote::makerinstitutionsell5() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  return makerinstitutionsell5_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_makerinstitutionsell5(const ::std::string& value) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
void OptionFxQuote::set_makerinstitutionsell5(const char* value) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
void OptionFxQuote::set_makerinstitutionsell5(const char* value, size_t size) {
  
  makerinstitutionsell5_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}
::std::string* OptionFxQuote::mutable_makerinstitutionsell5() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  return makerinstitutionsell5_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionFxQuote::release_makerinstitutionsell5() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
  
  return makerinstitutionsell5_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionFxQuote::set_allocated_makerinstitutionsell5(::std::string* makerinstitutionsell5) {
  if (makerinstitutionsell5 != NULL) {
    
  } else {
    
  }
  makerinstitutionsell5_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), makerinstitutionsell5);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionFxQuote.MakerInstitutionSell5)
}

inline const OptionFxQuote* OptionFxQuote::internal_default_instance() {
  return &OptionFxQuote_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
