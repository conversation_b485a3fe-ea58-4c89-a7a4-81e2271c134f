syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDQBTransaction {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  string DealID = 7;
  double Price = 8;
  double Quantity = 9;
  string DealTime = 10;
  int32 DataMultiplePowerOf10 = 11;
  int64 MessageNumber = 100;
}
