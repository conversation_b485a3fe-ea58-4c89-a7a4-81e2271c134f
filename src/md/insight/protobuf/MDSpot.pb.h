// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSpot.proto

#ifndef PROTOBUF_MDSpot_2eproto__INCLUDED
#define PROTOBUF_MDSpot_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSpot_2eproto();
void protobuf_InitDefaults_MDSpot_2eproto();
void protobuf_AssignDesc_MDSpot_2eproto();
void protobuf_ShutdownFile_MDSpot_2eproto();

class MDSpot;

// ===================================================================

class MDSpot : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSpot) */ {
 public:
  MDSpot();
  virtual ~MDSpot();

  MDSpot(const MDSpot& from);

  inline MDSpot& operator=(const MDSpot& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSpot& default_instance();

  static const MDSpot* internal_default_instance();

  void Swap(MDSpot* other);

  // implements Message ----------------------------------------------

  inline MDSpot* New() const { return New(NULL); }

  MDSpot* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSpot& from);
  void MergeFrom(const MDSpot& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSpot* other);
  void UnsafeMergeFrom(const MDSpot& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 TotalWeightTrade = 14;
  void clear_totalweighttrade();
  static const int kTotalWeightTradeFieldNumber = 14;
  ::google::protobuf::int64 totalweighttrade() const;
  void set_totalweighttrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 15;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 15;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 16;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 16;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 17;
  void clear_closepx();
  static const int kClosePxFieldNumber = 17;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 18;
  void clear_highpx();
  static const int kHighPxFieldNumber = 18;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 19;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 19;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 TradingDate = 20;
  void clear_tradingdate();
  static const int kTradingDateFieldNumber = 20;
  ::google::protobuf::int32 tradingdate() const;
  void set_tradingdate(::google::protobuf::int32 value);

  // optional int64 PreOpenInterest = 21;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 21;
  ::google::protobuf::int64 preopeninterest() const;
  void set_preopeninterest(::google::protobuf::int64 value);

  // optional int64 PreSettlePrice = 22;
  void clear_presettleprice();
  static const int kPreSettlePriceFieldNumber = 22;
  ::google::protobuf::int64 presettleprice() const;
  void set_presettleprice(::google::protobuf::int64 value);

  // optional int64 OpenInterest = 23;
  void clear_openinterest();
  static const int kOpenInterestFieldNumber = 23;
  ::google::protobuf::int64 openinterest() const;
  void set_openinterest(::google::protobuf::int64 value);

  // optional int64 SettlePrice = 24;
  void clear_settleprice();
  static const int kSettlePriceFieldNumber = 24;
  ::google::protobuf::int64 settleprice() const;
  void set_settleprice(::google::protobuf::int64 value);

  // optional int64 InitOpenInterest = 25;
  void clear_initopeninterest();
  static const int kInitOpenInterestFieldNumber = 25;
  ::google::protobuf::int64 initopeninterest() const;
  void set_initopeninterest(::google::protobuf::int64 value);

  // optional int64 InterestChg = 26;
  void clear_interestchg();
  static const int kInterestChgFieldNumber = 26;
  ::google::protobuf::int64 interestchg() const;
  void set_interestchg(::google::protobuf::int64 value);

  // optional int64 AveragePx = 27;
  void clear_averagepx();
  static const int kAveragePxFieldNumber = 27;
  ::google::protobuf::int64 averagepx() const;
  void set_averagepx(::google::protobuf::int64 value);

  // optional int64 LifeHighPx = 28;
  void clear_lifehighpx();
  static const int kLifeHighPxFieldNumber = 28;
  ::google::protobuf::int64 lifehighpx() const;
  void set_lifehighpx(::google::protobuf::int64 value);

  // optional int64 LifeLowPx = 29;
  void clear_lifelowpx();
  static const int kLifeLowPxFieldNumber = 29;
  ::google::protobuf::int64 lifelowpx() const;
  void set_lifelowpx(::google::protobuf::int64 value);

  // optional int64 BuyPx = 30;
  void clear_buypx();
  static const int kBuyPxFieldNumber = 30;
  ::google::protobuf::int64 buypx() const;
  void set_buypx(::google::protobuf::int64 value);

  // optional int64 BuyQty = 31;
  void clear_buyqty();
  static const int kBuyQtyFieldNumber = 31;
  ::google::protobuf::int64 buyqty() const;
  void set_buyqty(::google::protobuf::int64 value);

  // optional int64 BuyImplyQty = 32;
  void clear_buyimplyqty();
  static const int kBuyImplyQtyFieldNumber = 32;
  ::google::protobuf::int64 buyimplyqty() const;
  void set_buyimplyqty(::google::protobuf::int64 value);

  // optional int64 SellPx = 33;
  void clear_sellpx();
  static const int kSellPxFieldNumber = 33;
  ::google::protobuf::int64 sellpx() const;
  void set_sellpx(::google::protobuf::int64 value);

  // optional int64 SellQty = 34;
  void clear_sellqty();
  static const int kSellQtyFieldNumber = 34;
  ::google::protobuf::int64 sellqty() const;
  void set_sellqty(::google::protobuf::int64 value);

  // optional int64 SellImplyQty = 35;
  void clear_sellimplyqty();
  static const int kSellImplyQtyFieldNumber = 35;
  ::google::protobuf::int64 sellimplyqty() const;
  void set_sellimplyqty(::google::protobuf::int64 value);

  // optional string CommodityContractNumber = 36;
  void clear_commoditycontractnumber();
  static const int kCommodityContractNumberFieldNumber = 36;
  const ::std::string& commoditycontractnumber() const;
  void set_commoditycontractnumber(const ::std::string& value);
  void set_commoditycontractnumber(const char* value);
  void set_commoditycontractnumber(const char* value, size_t size);
  ::std::string* mutable_commoditycontractnumber();
  ::std::string* release_commoditycontractnumber();
  void set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber);

  // optional int32 ExchangeDate = 37;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 37;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 38;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 38;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 59;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 59;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSpot)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr commoditycontractnumber_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 totalweighttrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 preopeninterest_;
  ::google::protobuf::int64 presettleprice_;
  ::google::protobuf::int64 openinterest_;
  ::google::protobuf::int64 settleprice_;
  ::google::protobuf::int64 initopeninterest_;
  ::google::protobuf::int64 interestchg_;
  ::google::protobuf::int64 averagepx_;
  ::google::protobuf::int64 lifehighpx_;
  ::google::protobuf::int32 tradingdate_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 lifelowpx_;
  ::google::protobuf::int64 buypx_;
  ::google::protobuf::int64 buyqty_;
  ::google::protobuf::int64 buyimplyqty_;
  ::google::protobuf::int64 sellpx_;
  ::google::protobuf::int64 sellqty_;
  ::google::protobuf::int64 sellimplyqty_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSpot_2eproto_impl();
  friend void  protobuf_AddDesc_MDSpot_2eproto_impl();
  friend void protobuf_AssignDesc_MDSpot_2eproto();
  friend void protobuf_ShutdownFile_MDSpot_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSpot> MDSpot_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSpot

// optional string HTSCSecurityID = 1;
inline void MDSpot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSpot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
inline void MDSpot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
inline void MDSpot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
inline ::std::string* MDSpot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSpot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSpot::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSpot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MDDate)
  return mddate_;
}
inline void MDSpot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSpot::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSpot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MDTime)
  return mdtime_;
}
inline void MDSpot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSpot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.DataTimestamp)
  return datatimestamp_;
}
inline void MDSpot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDSpot::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSpot::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
inline void MDSpot::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
inline void MDSpot::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
inline ::std::string* MDSpot::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSpot::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDSpot::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSpot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSpot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDSpot::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSpot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSpot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.securityType)
}

// optional int64 MaxPx = 8;
inline void MDSpot::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MaxPx)
  return maxpx_;
}
inline void MDSpot::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDSpot::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MinPx)
  return minpx_;
}
inline void MDSpot::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDSpot::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreClosePx)
  return preclosepx_;
}
inline void MDSpot::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDSpot::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.NumTrades)
  return numtrades_;
}
inline void MDSpot::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDSpot::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDSpot::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDSpot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDSpot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalValueTrade)
}

// optional int64 TotalWeightTrade = 14;
inline void MDSpot::clear_totalweighttrade() {
  totalweighttrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::totalweighttrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalWeightTrade)
  return totalweighttrade_;
}
inline void MDSpot::set_totalweighttrade(::google::protobuf::int64 value) {
  
  totalweighttrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalWeightTrade)
}

// optional int64 LastPx = 15;
inline void MDSpot::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LastPx)
  return lastpx_;
}
inline void MDSpot::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LastPx)
}

// optional int64 OpenPx = 16;
inline void MDSpot::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.OpenPx)
  return openpx_;
}
inline void MDSpot::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.OpenPx)
}

// optional int64 ClosePx = 17;
inline void MDSpot::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ClosePx)
  return closepx_;
}
inline void MDSpot::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ClosePx)
}

// optional int64 HighPx = 18;
inline void MDSpot::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.HighPx)
  return highpx_;
}
inline void MDSpot::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.HighPx)
}

// optional int64 LowPx = 19;
inline void MDSpot::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LowPx)
  return lowpx_;
}
inline void MDSpot::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LowPx)
}

// optional int32 TradingDate = 20;
inline void MDSpot::clear_tradingdate() {
  tradingdate_ = 0;
}
inline ::google::protobuf::int32 MDSpot::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TradingDate)
  return tradingdate_;
}
inline void MDSpot::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TradingDate)
}

// optional int64 PreOpenInterest = 21;
inline void MDSpot::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreOpenInterest)
  return preopeninterest_;
}
inline void MDSpot::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreOpenInterest)
}

// optional int64 PreSettlePrice = 22;
inline void MDSpot::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreSettlePrice)
  return presettleprice_;
}
inline void MDSpot::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreSettlePrice)
}

// optional int64 OpenInterest = 23;
inline void MDSpot::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.OpenInterest)
  return openinterest_;
}
inline void MDSpot::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.OpenInterest)
}

// optional int64 SettlePrice = 24;
inline void MDSpot::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SettlePrice)
  return settleprice_;
}
inline void MDSpot::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SettlePrice)
}

// optional int64 InitOpenInterest = 25;
inline void MDSpot::clear_initopeninterest() {
  initopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::initopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.InitOpenInterest)
  return initopeninterest_;
}
inline void MDSpot::set_initopeninterest(::google::protobuf::int64 value) {
  
  initopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.InitOpenInterest)
}

// optional int64 InterestChg = 26;
inline void MDSpot::clear_interestchg() {
  interestchg_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::interestchg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.InterestChg)
  return interestchg_;
}
inline void MDSpot::set_interestchg(::google::protobuf::int64 value) {
  
  interestchg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.InterestChg)
}

// optional int64 AveragePx = 27;
inline void MDSpot::clear_averagepx() {
  averagepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::averagepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.AveragePx)
  return averagepx_;
}
inline void MDSpot::set_averagepx(::google::protobuf::int64 value) {
  
  averagepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.AveragePx)
}

// optional int64 LifeHighPx = 28;
inline void MDSpot::clear_lifehighpx() {
  lifehighpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::lifehighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LifeHighPx)
  return lifehighpx_;
}
inline void MDSpot::set_lifehighpx(::google::protobuf::int64 value) {
  
  lifehighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LifeHighPx)
}

// optional int64 LifeLowPx = 29;
inline void MDSpot::clear_lifelowpx() {
  lifelowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::lifelowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LifeLowPx)
  return lifelowpx_;
}
inline void MDSpot::set_lifelowpx(::google::protobuf::int64 value) {
  
  lifelowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LifeLowPx)
}

// optional int64 BuyPx = 30;
inline void MDSpot::clear_buypx() {
  buypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::buypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyPx)
  return buypx_;
}
inline void MDSpot::set_buypx(::google::protobuf::int64 value) {
  
  buypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyPx)
}

// optional int64 BuyQty = 31;
inline void MDSpot::clear_buyqty() {
  buyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::buyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyQty)
  return buyqty_;
}
inline void MDSpot::set_buyqty(::google::protobuf::int64 value) {
  
  buyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyQty)
}

// optional int64 BuyImplyQty = 32;
inline void MDSpot::clear_buyimplyqty() {
  buyimplyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::buyimplyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyImplyQty)
  return buyimplyqty_;
}
inline void MDSpot::set_buyimplyqty(::google::protobuf::int64 value) {
  
  buyimplyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyImplyQty)
}

// optional int64 SellPx = 33;
inline void MDSpot::clear_sellpx() {
  sellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::sellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellPx)
  return sellpx_;
}
inline void MDSpot::set_sellpx(::google::protobuf::int64 value) {
  
  sellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellPx)
}

// optional int64 SellQty = 34;
inline void MDSpot::clear_sellqty() {
  sellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::sellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellQty)
  return sellqty_;
}
inline void MDSpot::set_sellqty(::google::protobuf::int64 value) {
  
  sellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellQty)
}

// optional int64 SellImplyQty = 35;
inline void MDSpot::clear_sellimplyqty() {
  sellimplyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSpot::sellimplyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellImplyQty)
  return sellimplyqty_;
}
inline void MDSpot::set_sellimplyqty(::google::protobuf::int64 value) {
  
  sellimplyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellImplyQty)
}

// optional string CommodityContractNumber = 36;
inline void MDSpot::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSpot::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
inline void MDSpot::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
inline void MDSpot::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
inline ::std::string* MDSpot::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSpot::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSpot::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
inline void MDSpot::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDSpot::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ExchangeDate)
  return exchangedate_;
}
inline void MDSpot::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
inline void MDSpot::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDSpot::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ExchangeTime)
  return exchangetime_;
}
inline void MDSpot::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ExchangeTime)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDSpot::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDSpot::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDSpot::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
}
inline void MDSpot::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDSpot::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDSpot::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDSpot::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
}
inline void MDSpot::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDSpot::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDSpot::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDSpot::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
}
inline void MDSpot::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDSpot::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDSpot::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDSpot::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
}
inline void MDSpot::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDSpot::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDSpot::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDSpot::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
}
inline void MDSpot::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDSpot::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDSpot::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDSpot::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
}
inline void MDSpot::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDSpot::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDSpot::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDSpot::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
}
inline void MDSpot::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDSpot::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDSpot::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDSpot::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDSpot::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
}
inline void MDSpot::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
inline void MDSpot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSpot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSpot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.DataMultiplePowerOf10)
}

inline const MDSpot* MDSpot::internal_default_instance() {
  return &MDSpot_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSpot_2eproto__INCLUDED
