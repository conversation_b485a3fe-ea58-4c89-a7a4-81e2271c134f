syntax = "proto3";

package com.htsc.mdc.insight.model;

import "EMDPeriodType.proto";
import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADTwapProtos";
option optimize_for = SPEED;

// ADTwap message represents Time-Weighted Average Price (TWAP) data for securities
message ADTwap {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Period type for TWAP calculation (1min, 5min, 1day, etc.)
    com.htsc.mdc.model.EMDPeriodType PeriodType = 7;
    
    // Time-Weighted Average Price (scaled by DataMultiplePowerOf10)
    int64 Twap = 8;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 9;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 10;
    
    // Data scaling factor (power of 10 multiplier for price fields)
    int32 DataMultiplePowerOf10 = 11;
}
