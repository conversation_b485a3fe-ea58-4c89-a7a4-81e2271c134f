syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

message MDCfetsFxQuote {
  string HTSCSecurityID = 1;
  com.htsc.mdc.model.ESecurityType SecurityType = 2;
  com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  int32 MDDate = 4;
  int32 MDTime = 5;
  int64 DataTimestamp = 6;
  string TransactTime = 7;
  string MarketIndicator = 8;
  int32 DataMultiplePowerOf10 = 9;
  FxQuoteDetail FxQuoteDetail = 16;
  int64 MessageNumber = 100;
}

message FxQuoteDetail {
  string QuoteID = 1;
  string CurrencyPair = 2;
  double BidPrice = 3;
  double AskPrice = 4;
  double BidSize = 5;
  double AskSize = 6;
  string QuoteTime = 7;
}
