syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADOrderbookSnapshotProtos";
option optimize_for = SPEED;

// ADOrderbookSnapshot message represents orderbook snapshot data for securities
message ADOrderbookSnapshot {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Trading phase code
    string TradingPhaseCode = 5;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 7;
    
    // Channel number
    int32 ChannelNo = 8;
    
    // Application sequence number
    int64 ApplSeqNum = 9;
    
    // Snapshot market data timestamp
    int64 SnapshotMDDateTime = 10;
    
    // Number of trades
    int64 NumTrades = 11;
    
    // Total volume traded
    int64 TotalVolumeTrade = 12;
    
    // Total value traded (scaled by DataMultiplePowerOf10)
    int64 TotalValueTrade = 13;
    
    // Last traded price (scaled by DataMultiplePowerOf10)
    int64 LastPx = 14;
    
    // Highest price (scaled by DataMultiplePowerOf10)
    int64 HighPx = 17;
    
    // Lowest price (scaled by DataMultiplePowerOf10)
    int64 LowPx = 18;
    
    // Total buy quantity
    int64 TotalBuyQty = 19;
    
    // Total sell quantity
    int64 TotalSellQty = 20;
    
    // Weighted average buy price (scaled by DataMultiplePowerOf10)
    int64 WeightedAvgBuyPx = 21;
    
    // Weighted average sell price (scaled by DataMultiplePowerOf10)
    int64 WeightedAvgSellPx = 22;
    
    // Total buy number
    int64 TotalBuyNumber = 23;
    
    // Total sell number
    int64 TotalSellNumber = 24;
    
    // Number of buy orders
    int32 NumBuyOrders = 25;
    
    // Number of sell orders
    int32 NumSellOrders = 26;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 27;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 28;
    
    // Buy side orderbook entries
    repeated ADEntryDetail BuyEntries = 30;
    
    // Sell side orderbook entries
    repeated ADEntryDetail SellEntries = 31;
    
    // Buy market orders (packed)
    repeated int64 BuyMarketOrders = 32 [packed = true];
    
    // Sell market orders (packed)
    repeated int64 SellMarketOrders = 33 [packed = true];
    
    // After-matched buy entries
    repeated ADEntryDetail afterMatchedBuyEntries = 34;
    
    // After-matched sell entries
    repeated ADEntryDetail afterMatchedSellEntries = 35;
    
    // Data scaling factor (power of 10 multiplier for price/value fields)
    int32 DataMultiplePowerOf10 = 36;
    
    // Maximum price limit (scaled by DataMultiplePowerOf10)
    int64 MaxPx = 37;
    
    // Minimum price limit (scaled by DataMultiplePowerOf10)
    int64 MinPx = 38;
    
    // Previous closing price (scaled by DataMultiplePowerOf10)
    int64 PreClosePx = 39;
    
    // Opening price (scaled by DataMultiplePowerOf10)
    int64 OpenPx = 40;
    
    // Closing price (scaled by DataMultiplePowerOf10)
    int64 ClosePx = 41;
    
    // Indicative Optimized Portfolio Value (for ETFs)
    int64 IOPV = 42;
    
    // Previous IOPV
    int64 PreIOPV = 43;
    
    // Data source identifier
    int32 DataSource = 44;
    
    // Period identifier
    int32 Period = 45;
    
    // Purchase number (for funds)
    int64 PurchaseNumber = 46;
    
    // Purchase amount (for funds, scaled by DataMultiplePowerOf10)
    int64 PurchaseAmount = 47;
    
    // Redemption number (for funds)
    int64 RedemptionNumber = 48;
    
    // Redemption amount (for funds, scaled by DataMultiplePowerOf10)
    int64 RedemptionAmount = 49;
}

// ADEntryDetail represents orderbook entry details
message ADEntryDetail {
    // Price level (1-based)
    int32 Level = 1;
    
    // Price at this level (scaled by parent message's DataMultiplePowerOf10)
    int64 Price = 2;
    
    // Total quantity at this price level
    int32 TotalQty = 3;
    
    // Number of orders at this price level
    int32 NumberOfOrders = 4;
    
    // List of individual order quantities (packed)
    repeated int32 OrderQtyList = 5 [packed = true];
}
