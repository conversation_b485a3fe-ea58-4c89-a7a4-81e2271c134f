// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDOrder.proto

#ifndef PROTOBUF_MDOrder_2eproto__INCLUDED
#define PROTOBUF_MDOrder_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDOrder_2eproto();
void protobuf_InitDefaults_MDOrder_2eproto();
void protobuf_AssignDesc_MDOrder_2eproto();
void protobuf_ShutdownFile_MDOrder_2eproto();

class MDOrder;

// ===================================================================

class MDOrder : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDOrder) */ {
 public:
  MDOrder();
  virtual ~MDOrder();

  MDOrder(const MDOrder& from);

  inline MDOrder& operator=(const MDOrder& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDOrder& default_instance();

  static const MDOrder* internal_default_instance();

  void Swap(MDOrder* other);

  // implements Message ----------------------------------------------

  inline MDOrder* New() const { return New(NULL); }

  MDOrder* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDOrder& from);
  void MergeFrom(const MDOrder& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDOrder* other);
  void UnsafeMergeFrom(const MDOrder& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 5;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 OrderIndex = 7;
  void clear_orderindex();
  static const int kOrderIndexFieldNumber = 7;
  ::google::protobuf::int64 orderindex() const;
  void set_orderindex(::google::protobuf::int64 value);

  // optional int32 OrderType = 8;
  void clear_ordertype();
  static const int kOrderTypeFieldNumber = 8;
  ::google::protobuf::int32 ordertype() const;
  void set_ordertype(::google::protobuf::int32 value);

  // optional int64 OrderPrice = 9;
  void clear_orderprice();
  static const int kOrderPriceFieldNumber = 9;
  ::google::protobuf::int64 orderprice() const;
  void set_orderprice(::google::protobuf::int64 value);

  // optional int64 OrderQty = 10;
  void clear_orderqty();
  static const int kOrderQtyFieldNumber = 10;
  ::google::protobuf::int64 orderqty() const;
  void set_orderqty(::google::protobuf::int64 value);

  // optional int32 OrderBSFlag = 11;
  void clear_orderbsflag();
  static const int kOrderBSFlagFieldNumber = 11;
  ::google::protobuf::int32 orderbsflag() const;
  void set_orderbsflag(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 12;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 12;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // optional int32 ExchangeDate = 13;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 13;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 14;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 14;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int64 OrderNO = 15;
  void clear_orderno();
  static const int kOrderNOFieldNumber = 15;
  ::google::protobuf::int64 orderno() const;
  void set_orderno(::google::protobuf::int64 value);

  // optional int64 ApplSeqNum = 16;
  void clear_applseqnum();
  static const int kApplSeqNumFieldNumber = 16;
  ::google::protobuf::int64 applseqnum() const;
  void set_applseqnum(::google::protobuf::int64 value);

  // optional string SecurityStatus = 17;
  void clear_securitystatus();
  static const int kSecurityStatusFieldNumber = 17;
  const ::std::string& securitystatus() const;
  void set_securitystatus(const ::std::string& value);
  void set_securitystatus(const char* value);
  void set_securitystatus(const char* value, size_t size);
  ::std::string* mutable_securitystatus();
  ::std::string* release_securitystatus();
  void set_allocated_securitystatus(::std::string* securitystatus);

  // optional string QuoteID = 18;
  void clear_quoteid();
  static const int kQuoteIDFieldNumber = 18;
  const ::std::string& quoteid() const;
  void set_quoteid(const ::std::string& value);
  void set_quoteid(const char* value);
  void set_quoteid(const char* value, size_t size);
  ::std::string* mutable_quoteid();
  ::std::string* release_quoteid();
  void set_allocated_quoteid(::std::string* quoteid);

  // optional string MemberID = 19;
  void clear_memberid();
  static const int kMemberIDFieldNumber = 19;
  const ::std::string& memberid() const;
  void set_memberid(const ::std::string& value);
  void set_memberid(const char* value);
  void set_memberid(const char* value, size_t size);
  ::std::string* mutable_memberid();
  ::std::string* release_memberid();
  void set_allocated_memberid(::std::string* memberid);

  // optional string InvestorType = 20;
  void clear_investortype();
  static const int kInvestorTypeFieldNumber = 20;
  const ::std::string& investortype() const;
  void set_investortype(const ::std::string& value);
  void set_investortype(const char* value);
  void set_investortype(const char* value, size_t size);
  ::std::string* mutable_investortype();
  ::std::string* release_investortype();
  void set_allocated_investortype(::std::string* investortype);

  // optional string InvestorID = 21;
  void clear_investorid();
  static const int kInvestorIDFieldNumber = 21;
  const ::std::string& investorid() const;
  void set_investorid(const ::std::string& value);
  void set_investorid(const char* value);
  void set_investorid(const char* value, size_t size);
  ::std::string* mutable_investorid();
  ::std::string* release_investorid();
  void set_allocated_investorid(::std::string* investorid);

  // optional string InvestorName = 22;
  void clear_investorname();
  static const int kInvestorNameFieldNumber = 22;
  const ::std::string& investorname() const;
  void set_investorname(const ::std::string& value);
  void set_investorname(const char* value);
  void set_investorname(const char* value, size_t size);
  ::std::string* mutable_investorname();
  ::std::string* release_investorname();
  void set_allocated_investorname(::std::string* investorname);

  // optional string TraderCode = 23;
  void clear_tradercode();
  static const int kTraderCodeFieldNumber = 23;
  const ::std::string& tradercode() const;
  void set_tradercode(const ::std::string& value);
  void set_tradercode(const char* value);
  void set_tradercode(const char* value, size_t size);
  ::std::string* mutable_tradercode();
  ::std::string* release_tradercode();
  void set_allocated_tradercode(::std::string* tradercode);

  // optional int32 SettlPeriod = 24;
  void clear_settlperiod();
  static const int kSettlPeriodFieldNumber = 24;
  ::google::protobuf::int32 settlperiod() const;
  void set_settlperiod(::google::protobuf::int32 value);

  // optional int32 SettlType = 25;
  void clear_settltype();
  static const int kSettlTypeFieldNumber = 25;
  ::google::protobuf::int32 settltype() const;
  void set_settltype(::google::protobuf::int32 value);

  // optional string Memo = 26;
  void clear_memo();
  static const int kMemoFieldNumber = 26;
  const ::std::string& memo() const;
  void set_memo(const ::std::string& value);
  void set_memo(const char* value);
  void set_memo(const char* value, size_t size);
  ::std::string* mutable_memo();
  ::std::string* release_memo();
  void set_allocated_memo(::std::string* memo);

  // optional int32 DataMultiplePowerOf10 = 27;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 27;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional string SecondaryOrderID = 28;
  void clear_secondaryorderid();
  static const int kSecondaryOrderIDFieldNumber = 28;
  const ::std::string& secondaryorderid() const;
  void set_secondaryorderid(const ::std::string& value);
  void set_secondaryorderid(const char* value);
  void set_secondaryorderid(const char* value, size_t size);
  ::std::string* mutable_secondaryorderid();
  ::std::string* release_secondaryorderid();
  void set_allocated_secondaryorderid(::std::string* secondaryorderid);

  // optional int32 BidTransType = 29;
  void clear_bidtranstype();
  static const int kBidTransTypeFieldNumber = 29;
  ::google::protobuf::int32 bidtranstype() const;
  void set_bidtranstype(::google::protobuf::int32 value);

  // optional int32 BidExecInstType = 30;
  void clear_bidexecinsttype();
  static const int kBidExecInstTypeFieldNumber = 30;
  ::google::protobuf::int32 bidexecinsttype() const;
  void set_bidexecinsttype(::google::protobuf::int32 value);

  // optional int64 LowLimitPrice = 31;
  void clear_lowlimitprice();
  static const int kLowLimitPriceFieldNumber = 31;
  ::google::protobuf::int64 lowlimitprice() const;
  void set_lowlimitprice(::google::protobuf::int64 value);

  // optional int64 HighLimitPrice = 32;
  void clear_highlimitprice();
  static const int kHighLimitPriceFieldNumber = 32;
  ::google::protobuf::int64 highlimitprice() const;
  void set_highlimitprice(::google::protobuf::int64 value);

  // optional int64 MinQty = 33;
  void clear_minqty();
  static const int kMinQtyFieldNumber = 33;
  ::google::protobuf::int64 minqty() const;
  void set_minqty(::google::protobuf::int64 value);

  // optional string TradeDate = 34;
  void clear_tradedate();
  static const int kTradeDateFieldNumber = 34;
  const ::std::string& tradedate() const;
  void set_tradedate(const ::std::string& value);
  void set_tradedate(const char* value);
  void set_tradedate(const char* value, size_t size);
  ::std::string* mutable_tradedate();
  ::std::string* release_tradedate();
  void set_allocated_tradedate(::std::string* tradedate);

  // optional int64 TradedQty = 35;
  void clear_tradedqty();
  static const int kTradedQtyFieldNumber = 35;
  ::google::protobuf::int64 tradedqty() const;
  void set_tradedqty(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDOrder)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr securitystatus_;
  ::google::protobuf::internal::ArenaStringPtr quoteid_;
  ::google::protobuf::internal::ArenaStringPtr memberid_;
  ::google::protobuf::internal::ArenaStringPtr investortype_;
  ::google::protobuf::internal::ArenaStringPtr investorid_;
  ::google::protobuf::internal::ArenaStringPtr investorname_;
  ::google::protobuf::internal::ArenaStringPtr tradercode_;
  ::google::protobuf::internal::ArenaStringPtr memo_;
  ::google::protobuf::internal::ArenaStringPtr secondaryorderid_;
  ::google::protobuf::internal::ArenaStringPtr tradedate_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 orderindex_;
  ::google::protobuf::int64 orderprice_;
  ::google::protobuf::int32 ordertype_;
  ::google::protobuf::int32 orderbsflag_;
  ::google::protobuf::int64 orderqty_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 orderno_;
  ::google::protobuf::int64 applseqnum_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 settlperiod_;
  ::google::protobuf::int32 settltype_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 bidtranstype_;
  ::google::protobuf::int32 bidexecinsttype_;
  ::google::protobuf::int64 lowlimitprice_;
  ::google::protobuf::int64 highlimitprice_;
  ::google::protobuf::int64 minqty_;
  ::google::protobuf::int64 tradedqty_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDOrder_2eproto_impl();
  friend void  protobuf_AddDesc_MDOrder_2eproto_impl();
  friend void protobuf_AssignDesc_MDOrder_2eproto();
  friend void protobuf_ShutdownFile_MDOrder_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDOrder> MDOrder_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDOrder

// optional string HTSCSecurityID = 1;
inline void MDOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
inline void MDOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
inline void MDOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}
inline ::std::string* MDOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDOrder::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MDDate)
  return mddate_;
}
inline void MDOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MDDate)
}

// optional int32 MDTime = 3;
inline void MDOrder::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MDTime)
  return mdtime_;
}
inline void MDOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.DataTimestamp)
  return datatimestamp_;
}
inline void MDOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
inline void MDOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
inline void MDOrder::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.securityType)
}

// optional int64 OrderIndex = 7;
inline void MDOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderIndex)
  return orderindex_;
}
inline void MDOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderIndex)
}

// optional int32 OrderType = 8;
inline void MDOrder::clear_ordertype() {
  ordertype_ = 0;
}
inline ::google::protobuf::int32 MDOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderType)
  return ordertype_;
}
inline void MDOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderType)
}

// optional int64 OrderPrice = 9;
inline void MDOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderPrice)
  return orderprice_;
}
inline void MDOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderPrice)
}

// optional int64 OrderQty = 10;
inline void MDOrder::clear_orderqty() {
  orderqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderQty)
  return orderqty_;
}
inline void MDOrder::set_orderqty(::google::protobuf::int64 value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderQty)
}

// optional int32 OrderBSFlag = 11;
inline void MDOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
inline ::google::protobuf::int32 MDOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderBSFlag)
  return orderbsflag_;
}
inline void MDOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderBSFlag)
}

// optional int32 ChannelNo = 12;
inline void MDOrder::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDOrder::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ChannelNo)
  return channelno_;
}
inline void MDOrder::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ChannelNo)
}

// optional int32 ExchangeDate = 13;
inline void MDOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ExchangeDate)
  return exchangedate_;
}
inline void MDOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 14;
inline void MDOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ExchangeTime)
  return exchangetime_;
}
inline void MDOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ExchangeTime)
}

// optional int64 OrderNO = 15;
inline void MDOrder::clear_orderno() {
  orderno_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::orderno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.OrderNO)
  return orderno_;
}
inline void MDOrder::set_orderno(::google::protobuf::int64 value) {
  
  orderno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.OrderNO)
}

// optional int64 ApplSeqNum = 16;
inline void MDOrder::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.ApplSeqNum)
  return applseqnum_;
}
inline void MDOrder::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.ApplSeqNum)
}

// optional string SecurityStatus = 17;
inline void MDOrder::clear_securitystatus() {
  securitystatus_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::securitystatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  return securitystatus_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_securitystatus(const ::std::string& value) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
inline void MDOrder::set_securitystatus(const char* value) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
inline void MDOrder::set_securitystatus(const char* value, size_t size) {
  
  securitystatus_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}
inline ::std::string* MDOrder::mutable_securitystatus() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  return securitystatus_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_securitystatus() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
  
  return securitystatus_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_securitystatus(::std::string* securitystatus) {
  if (securitystatus != NULL) {
    
  } else {
    
  }
  securitystatus_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitystatus);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.SecurityStatus)
}

// optional string QuoteID = 18;
inline void MDOrder::clear_quoteid() {
  quoteid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::quoteid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  return quoteid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_quoteid(const ::std::string& value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
inline void MDOrder::set_quoteid(const char* value) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
inline void MDOrder::set_quoteid(const char* value, size_t size) {
  
  quoteid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}
inline ::std::string* MDOrder::mutable_quoteid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  return quoteid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_quoteid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.QuoteID)
  
  return quoteid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_quoteid(::std::string* quoteid) {
  if (quoteid != NULL) {
    
  } else {
    
  }
  quoteid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), quoteid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.QuoteID)
}

// optional string MemberID = 19;
inline void MDOrder::clear_memberid() {
  memberid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::memberid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MemberID)
  return memberid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_memberid(const ::std::string& value) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
inline void MDOrder::set_memberid(const char* value) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
inline void MDOrder::set_memberid(const char* value, size_t size) {
  
  memberid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.MemberID)
}
inline ::std::string* MDOrder::mutable_memberid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.MemberID)
  return memberid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_memberid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.MemberID)
  
  return memberid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_memberid(::std::string* memberid) {
  if (memberid != NULL) {
    
  } else {
    
  }
  memberid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), memberid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.MemberID)
}

// optional string InvestorType = 20;
inline void MDOrder::clear_investortype() {
  investortype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::investortype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  return investortype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_investortype(const ::std::string& value) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
inline void MDOrder::set_investortype(const char* value) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
inline void MDOrder::set_investortype(const char* value, size_t size) {
  
  investortype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}
inline ::std::string* MDOrder::mutable_investortype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  return investortype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_investortype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorType)
  
  return investortype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_investortype(::std::string* investortype) {
  if (investortype != NULL) {
    
  } else {
    
  }
  investortype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investortype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorType)
}

// optional string InvestorID = 21;
inline void MDOrder::clear_investorid() {
  investorid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::investorid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  return investorid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_investorid(const ::std::string& value) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
inline void MDOrder::set_investorid(const char* value) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
inline void MDOrder::set_investorid(const char* value, size_t size) {
  
  investorid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}
inline ::std::string* MDOrder::mutable_investorid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  return investorid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_investorid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorID)
  
  return investorid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_investorid(::std::string* investorid) {
  if (investorid != NULL) {
    
  } else {
    
  }
  investorid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investorid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorID)
}

// optional string InvestorName = 22;
inline void MDOrder::clear_investorname() {
  investorname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::investorname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  return investorname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_investorname(const ::std::string& value) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
inline void MDOrder::set_investorname(const char* value) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
inline void MDOrder::set_investorname(const char* value, size_t size) {
  
  investorname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}
inline ::std::string* MDOrder::mutable_investorname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  return investorname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_investorname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.InvestorName)
  
  return investorname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_investorname(::std::string* investorname) {
  if (investorname != NULL) {
    
  } else {
    
  }
  investorname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), investorname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.InvestorName)
}

// optional string TraderCode = 23;
inline void MDOrder::clear_tradercode() {
  tradercode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::tradercode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  return tradercode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_tradercode(const ::std::string& value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
inline void MDOrder::set_tradercode(const char* value) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
inline void MDOrder::set_tradercode(const char* value, size_t size) {
  
  tradercode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}
inline ::std::string* MDOrder::mutable_tradercode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  return tradercode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_tradercode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.TraderCode)
  
  return tradercode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_tradercode(::std::string* tradercode) {
  if (tradercode != NULL) {
    
  } else {
    
  }
  tradercode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradercode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.TraderCode)
}

// optional int32 SettlPeriod = 24;
inline void MDOrder::clear_settlperiod() {
  settlperiod_ = 0;
}
inline ::google::protobuf::int32 MDOrder::settlperiod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SettlPeriod)
  return settlperiod_;
}
inline void MDOrder::set_settlperiod(::google::protobuf::int32 value) {
  
  settlperiod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SettlPeriod)
}

// optional int32 SettlType = 25;
inline void MDOrder::clear_settltype() {
  settltype_ = 0;
}
inline ::google::protobuf::int32 MDOrder::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SettlType)
  return settltype_;
}
inline void MDOrder::set_settltype(::google::protobuf::int32 value) {
  
  settltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SettlType)
}

// optional string Memo = 26;
inline void MDOrder::clear_memo() {
  memo_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::memo() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.Memo)
  return memo_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_memo(const ::std::string& value) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.Memo)
}
inline void MDOrder::set_memo(const char* value) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.Memo)
}
inline void MDOrder::set_memo(const char* value, size_t size) {
  
  memo_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.Memo)
}
inline ::std::string* MDOrder::mutable_memo() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.Memo)
  return memo_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_memo() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.Memo)
  
  return memo_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_memo(::std::string* memo) {
  if (memo != NULL) {
    
  } else {
    
  }
  memo_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), memo);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.Memo)
}

// optional int32 DataMultiplePowerOf10 = 27;
inline void MDOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.DataMultiplePowerOf10)
}

// optional string SecondaryOrderID = 28;
inline void MDOrder::clear_secondaryorderid() {
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::secondaryorderid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  return secondaryorderid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_secondaryorderid(const ::std::string& value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
inline void MDOrder::set_secondaryorderid(const char* value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
inline void MDOrder::set_secondaryorderid(const char* value, size_t size) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}
inline ::std::string* MDOrder::mutable_secondaryorderid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  return secondaryorderid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_secondaryorderid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
  
  return secondaryorderid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_secondaryorderid(::std::string* secondaryorderid) {
  if (secondaryorderid != NULL) {
    
  } else {
    
  }
  secondaryorderid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secondaryorderid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.SecondaryOrderID)
}

// optional int32 BidTransType = 29;
inline void MDOrder::clear_bidtranstype() {
  bidtranstype_ = 0;
}
inline ::google::protobuf::int32 MDOrder::bidtranstype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.BidTransType)
  return bidtranstype_;
}
inline void MDOrder::set_bidtranstype(::google::protobuf::int32 value) {
  
  bidtranstype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.BidTransType)
}

// optional int32 BidExecInstType = 30;
inline void MDOrder::clear_bidexecinsttype() {
  bidexecinsttype_ = 0;
}
inline ::google::protobuf::int32 MDOrder::bidexecinsttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.BidExecInstType)
  return bidexecinsttype_;
}
inline void MDOrder::set_bidexecinsttype(::google::protobuf::int32 value) {
  
  bidexecinsttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.BidExecInstType)
}

// optional int64 LowLimitPrice = 31;
inline void MDOrder::clear_lowlimitprice() {
  lowlimitprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::lowlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.LowLimitPrice)
  return lowlimitprice_;
}
inline void MDOrder::set_lowlimitprice(::google::protobuf::int64 value) {
  
  lowlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.LowLimitPrice)
}

// optional int64 HighLimitPrice = 32;
inline void MDOrder::clear_highlimitprice() {
  highlimitprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::highlimitprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.HighLimitPrice)
  return highlimitprice_;
}
inline void MDOrder::set_highlimitprice(::google::protobuf::int64 value) {
  
  highlimitprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.HighLimitPrice)
}

// optional int64 MinQty = 33;
inline void MDOrder::clear_minqty() {
  minqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::minqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.MinQty)
  return minqty_;
}
inline void MDOrder::set_minqty(::google::protobuf::int64 value) {
  
  minqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.MinQty)
}

// optional string TradeDate = 34;
inline void MDOrder::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDOrder::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
inline void MDOrder::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
inline void MDOrder::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}
inline ::std::string* MDOrder::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDOrder::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDOrder.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDOrder::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDOrder.TradeDate)
}

// optional int64 TradedQty = 35;
inline void MDOrder::clear_tradedqty() {
  tradedqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDOrder::tradedqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDOrder.TradedQty)
  return tradedqty_;
}
inline void MDOrder::set_tradedqty(::google::protobuf::int64 value) {
  
  tradedqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDOrder.TradedQty)
}

inline const MDOrder* MDOrder::internal_default_instance() {
  return &MDOrder_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDOrder_2eproto__INCLUDED
