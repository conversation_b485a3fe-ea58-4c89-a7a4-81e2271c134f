syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADChipDistributionProtos";
option optimize_for = SPEED;

// ADChipDistribution message represents chip distribution analysis data for securities
message ADChipDistribution {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 7;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 8;
    
    // Last price (scaled by DataMultiplePowerOf10)
    int64 LastPx = 11;
    
    // Previous closing price (scaled by DataMultiplePowerOf10)
    int64 PreClosePx = 12;
    
    // Total number of shares
    int64 ShareTotalNumber = 13;
    
    // Whether this is final settlement
    bool IsFinalSettlement = 14;
    
    // Total shares outstanding
    int64 TotalShare = 15;
    
    // Total A-shares
    int64 ATotalShare = 16;
    
    // Total listed A-shares
    int64 AListedTotalShare = 17;
    
    // Detailed tradable chip distribution data
    repeated ADChipDistributionDetail TradableDetails = 20;
    
    // Tradable Mean Cost of Stock Trading (MCST)
    int64 TradableMCST = 21;
    
    // Maximum cost of tradable positions (scaled by DataMultiplePowerOf10)
    int64 TradableMaxCostOfPositions = 22;
    
    // Minimum cost of tradable positions (scaled by DataMultiplePowerOf10)
    int64 TradableMinCostOfPositions = 23;
    
    // Tradable profit percentage (scaled by DataMultiplePowerOf10)
    int64 TradableProfitPercent = 24;
    
    // Tradable chip dispersion percentage (scaled by DataMultiplePowerOf10)
    int64 TradableChipDispersionPercent = 25;
    
    // Previous tradable profit percentage (scaled by DataMultiplePowerOf10)
    int64 TradablePreProfitPercent = 26;
    
    // Tradable profit change percentage (scaled by DataMultiplePowerOf10)
    int64 TradableProfitChangePercent = 27;
    
    // Tradable centralized percentage (scaled by DataMultiplePowerOf10)
    int64 TradableCentralizedPercent = 28;
    
    // Maximum cost of centralized tradable positions (scaled by DataMultiplePowerOf10)
    int64 TradableCentralizedMaxCostOfPositions = 29;
    
    // Minimum cost of centralized tradable positions (scaled by DataMultiplePowerOf10)
    int64 TradableCentralizedMinCostOfPositions = 30;
    
    // Total number of tradable shares
    int64 TradableShareTotalNumber = 31;
    
    // Tradable share percentage (scaled by DataMultiplePowerOf10)
    int64 TradableSharePercent = 32;
    
    // Detailed restricted chip distribution data
    repeated ADChipDistributionDetail RestrictedDetails = 40;
    
    // Restricted Mean Cost of Stock Trading (MCST)
    int64 RestrictedMCST = 41;
    
    // Maximum cost of restricted positions (scaled by DataMultiplePowerOf10)
    int64 RestrictedMaxCostOfPositions = 42;
    
    // Minimum cost of restricted positions (scaled by DataMultiplePowerOf10)
    int64 RestrictedMinCostOfPositions = 43;
    
    // Total number of restricted shares
    int64 RestrictedShareTotalNumber = 46;
    
    // Restricted share percentage (scaled by DataMultiplePowerOf10)
    int64 RestrictedSharePercent = 47;
    
    // Detailed restricted hold chip distribution data
    repeated ADChipDistributionDetail RestrictedHoldDetails = 48;
    
    // Detailed large shareholders chip distribution data
    repeated ADChipDistributionDetail LargeShareholdersDetails = 50;
    
    // Large shareholders Mean Cost of Stock Trading (MCST)
    int64 LargeShareholdersMCST = 51;
    
    // Maximum cost of large shareholders positions (scaled by DataMultiplePowerOf10)
    int64 LargeShareholdersMaxCostOfPositions = 52;
    
    // Minimum cost of large shareholders positions (scaled by DataMultiplePowerOf10)
    int64 LargeShareholdersMinCostOfPositions = 53;
    
    // Total number of large shareholders shares
    int64 LargeShareholdersShareTotalNumber = 54;
    
    // Large shareholders share percentage (scaled by DataMultiplePowerOf10)
    int64 LargeShareholdersSharePercent = 55;
    
    // Data scaling factor (power of 10 multiplier for price/value fields)
    int32 DataMultiplePowerOf10 = 56;
}

// ADChipDistributionDetail represents detailed chip distribution information
message ADChipDistributionDetail {
    // Price level (scaled by parent message's DataMultiplePowerOf10)
    int64 Price = 1;
    
    // Number of shares at this price level
    int64 NumberOfShares = 2;
    
    // Percentage of shares at this price level (scaled by parent message's DataMultiplePowerOf10)
    int64 NumberOfSharesPercent = 3;
    
    // Listed date (YYYYMMDD format)
    int64 ListedDate = 4;
    
    // Shareholder name (for large shareholders data)
    string ShareHolderName = 5;
}
