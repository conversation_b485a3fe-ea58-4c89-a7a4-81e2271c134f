// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDChinaBondBenchmark.proto

#ifndef PROTOBUF_MDChinaBondBenchmark_2eproto__INCLUDED
#define PROTOBUF_MDChinaBondBenchmark_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDChinaBondBenchmark_2eproto();
void protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto();
void protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto();

class MDChinaBondBenchmark;
class MDChinaBondValuation;
class MDChinaBondYieldCurve;

// ===================================================================

class MDChinaBondBenchmark : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDChinaBondBenchmark) */ {
 public:
  MDChinaBondBenchmark();
  virtual ~MDChinaBondBenchmark();

  MDChinaBondBenchmark(const MDChinaBondBenchmark& from);

  inline MDChinaBondBenchmark& operator=(const MDChinaBondBenchmark& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDChinaBondBenchmark& default_instance();

  static const MDChinaBondBenchmark* internal_default_instance();

  void Swap(MDChinaBondBenchmark* other);

  // implements Message ----------------------------------------------

  inline MDChinaBondBenchmark* New() const { return New(NULL); }

  MDChinaBondBenchmark* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDChinaBondBenchmark& from);
  void MergeFrom(const MDChinaBondBenchmark& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDChinaBondBenchmark* other);
  void UnsafeMergeFrom(const MDChinaBondBenchmark& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 2;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 3;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional int32 MDDate = 4;
  void clear_mddate();
  static const int kMDDateFieldNumber = 4;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 5;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 5;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int32 DataMultiplePowerOf10 = 6;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 6;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int32 BenchmarkType = 10;
  void clear_benchmarktype();
  static const int kBenchmarkTypeFieldNumber = 10;
  ::google::protobuf::int32 benchmarktype() const;
  void set_benchmarktype(::google::protobuf::int32 value);

  // optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
  bool has_mdchinabondvaluation() const;
  void clear_mdchinabondvaluation();
  static const int kMDChinaBondValuationFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::MDChinaBondValuation& mdchinabondvaluation() const;
  ::com::htsc::mdc::insight::model::MDChinaBondValuation* mutable_mdchinabondvaluation();
  ::com::htsc::mdc::insight::model::MDChinaBondValuation* release_mdchinabondvaluation();
  void set_allocated_mdchinabondvaluation(::com::htsc::mdc::insight::model::MDChinaBondValuation* mdchinabondvaluation);

  // optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
  bool has_mdchinabondyieldcurves() const;
  void clear_mdchinabondyieldcurves();
  static const int kMDChinaBondYieldCurvesFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve& mdchinabondyieldcurves() const;
  ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* mutable_mdchinabondyieldcurves();
  ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* release_mdchinabondyieldcurves();
  void set_allocated_mdchinabondyieldcurves(::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* mdchinabondyieldcurves);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::com::htsc::mdc::insight::model::MDChinaBondValuation* mdchinabondvaluation_;
  ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* mdchinabondyieldcurves_;
  int securitytype_;
  int securityidsource_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 benchmarktype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDChinaBondBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondBenchmark> MDChinaBondBenchmark_default_instance_;

// -------------------------------------------------------------------

class MDChinaBondValuation : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDChinaBondValuation) */ {
 public:
  MDChinaBondValuation();
  virtual ~MDChinaBondValuation();

  MDChinaBondValuation(const MDChinaBondValuation& from);

  inline MDChinaBondValuation& operator=(const MDChinaBondValuation& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDChinaBondValuation& default_instance();

  static const MDChinaBondValuation* internal_default_instance();

  void Swap(MDChinaBondValuation* other);

  // implements Message ----------------------------------------------

  inline MDChinaBondValuation* New() const { return New(NULL); }

  MDChinaBondValuation* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDChinaBondValuation& from);
  void MergeFrom(const MDChinaBondValuation& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDChinaBondValuation* other);
  void UnsafeMergeFrom(const MDChinaBondValuation& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string CalculateTime = 1;
  void clear_calculatetime();
  static const int kCalculateTimeFieldNumber = 1;
  const ::std::string& calculatetime() const;
  void set_calculatetime(const ::std::string& value);
  void set_calculatetime(const char* value);
  void set_calculatetime(const char* value, size_t size);
  ::std::string* mutable_calculatetime();
  ::std::string* release_calculatetime();
  void set_allocated_calculatetime(::std::string* calculatetime);

  // optional string TransactionTime = 2;
  void clear_transactiontime();
  static const int kTransactionTimeFieldNumber = 2;
  const ::std::string& transactiontime() const;
  void set_transactiontime(const ::std::string& value);
  void set_transactiontime(const char* value);
  void set_transactiontime(const char* value, size_t size);
  ::std::string* mutable_transactiontime();
  ::std::string* release_transactiontime();
  void set_allocated_transactiontime(::std::string* transactiontime);

  // optional string MDMkt = 11;
  void clear_mdmkt();
  static const int kMDMktFieldNumber = 11;
  const ::std::string& mdmkt() const;
  void set_mdmkt(const ::std::string& value);
  void set_mdmkt(const char* value);
  void set_mdmkt(const char* value, size_t size);
  ::std::string* mutable_mdmkt();
  ::std::string* release_mdmkt();
  void set_allocated_mdmkt(::std::string* mdmkt);

  // optional double MaturityTerm = 12;
  void clear_maturityterm();
  static const int kMaturityTermFieldNumber = 12;
  double maturityterm() const;
  void set_maturityterm(double value);

  // optional double IntraDayValuationFullPrice = 13;
  void clear_intradayvaluationfullprice();
  static const int kIntraDayValuationFullPriceFieldNumber = 13;
  double intradayvaluationfullprice() const;
  void set_intradayvaluationfullprice(double value);

  // optional double IntraDayAccruedInterest = 14;
  void clear_intradayaccruedinterest();
  static const int kIntraDayAccruedInterestFieldNumber = 14;
  double intradayaccruedinterest() const;
  void set_intradayaccruedinterest(double value);

  // optional double ValuationNetPrice = 15;
  void clear_valuationnetprice();
  static const int kValuationNetPriceFieldNumber = 15;
  double valuationnetprice() const;
  void set_valuationnetprice(double value);

  // optional double ValuationModifiedDuration = 16;
  void clear_valuationmodifiedduration();
  static const int kValuationModifiedDurationFieldNumber = 16;
  double valuationmodifiedduration() const;
  void set_valuationmodifiedduration(double value);

  // optional double ValuationConvexity = 17;
  void clear_valuationconvexity();
  static const int kValuationConvexityFieldNumber = 17;
  double valuationconvexity() const;
  void set_valuationconvexity(double value);

  // optional double ValuationBPV = 18;
  void clear_valuationbpv();
  static const int kValuationBPVFieldNumber = 18;
  double valuationbpv() const;
  void set_valuationbpv(double value);

  // optional double ValuationSpreadDuration = 19;
  void clear_valuationspreadduration();
  static const int kValuationSpreadDurationFieldNumber = 19;
  double valuationspreadduration() const;
  void set_valuationspreadduration(double value);

  // optional double ValuationSpreadConvexity = 20;
  void clear_valuationspreadconvexity();
  static const int kValuationSpreadConvexityFieldNumber = 20;
  double valuationspreadconvexity() const;
  void set_valuationspreadconvexity(double value);

  // optional double ValuationRateDuration = 21;
  void clear_valuationrateduration();
  static const int kValuationRateDurationFieldNumber = 21;
  double valuationrateduration() const;
  void set_valuationrateduration(double value);

  // optional double ValuationRateConvexity = 22;
  void clear_valuationrateconvexity();
  static const int kValuationRateConvexityFieldNumber = 22;
  double valuationrateconvexity() const;
  void set_valuationrateconvexity(double value);

  // optional double EndDayFullPrice = 23;
  void clear_enddayfullprice();
  static const int kEndDayFullPriceFieldNumber = 23;
  double enddayfullprice() const;
  void set_enddayfullprice(double value);

  // optional double EndDayAccruedInterest = 24;
  void clear_enddayaccruedinterest();
  static const int kEndDayAccruedInterestFieldNumber = 24;
  double enddayaccruedinterest() const;
  void set_enddayaccruedinterest(double value);

  // optional double SpreadYield = 25;
  void clear_spreadyield();
  static const int kSpreadYieldFieldNumber = 25;
  double spreadyield() const;
  void set_spreadyield(double value);

  // optional double EstimateCoupRateAftExeDay = 26;
  void clear_estimatecouprateaftexeday();
  static const int kEstimateCoupRateAftExeDayFieldNumber = 26;
  double estimatecouprateaftexeday() const;
  void set_estimatecouprateaftexeday(double value);

  // optional double ResiPrincipal = 27;
  void clear_resiprincipal();
  static const int kResiPrincipalFieldNumber = 27;
  double resiprincipal() const;
  void set_resiprincipal(double value);

  // optional double ValuationPreYield = 28;
  void clear_valuationpreyield();
  static const int kValuationPreYieldFieldNumber = 28;
  double valuationpreyield() const;
  void set_valuationpreyield(double value);

  // optional double ValuationYield = 29;
  void clear_valuationyield();
  static const int kValuationYieldFieldNumber = 29;
  double valuationyield() const;
  void set_valuationyield(double value);

  // optional int32 Recommendation = 30;
  void clear_recommendation();
  static const int kRecommendationFieldNumber = 30;
  ::google::protobuf::int32 recommendation() const;
  void set_recommendation(::google::protobuf::int32 value);

  // optional string InstrumentShortName = 31;
  void clear_instrumentshortname();
  static const int kInstrumentShortNameFieldNumber = 31;
  const ::std::string& instrumentshortname() const;
  void set_instrumentshortname(const ::std::string& value);
  void set_instrumentshortname(const char* value);
  void set_instrumentshortname(const char* value, size_t size);
  ::std::string* mutable_instrumentshortname();
  ::std::string* release_instrumentshortname();
  void set_allocated_instrumentshortname(::std::string* instrumentshortname);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDChinaBondValuation)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr calculatetime_;
  ::google::protobuf::internal::ArenaStringPtr transactiontime_;
  ::google::protobuf::internal::ArenaStringPtr mdmkt_;
  ::google::protobuf::internal::ArenaStringPtr instrumentshortname_;
  double maturityterm_;
  double intradayvaluationfullprice_;
  double intradayaccruedinterest_;
  double valuationnetprice_;
  double valuationmodifiedduration_;
  double valuationconvexity_;
  double valuationbpv_;
  double valuationspreadduration_;
  double valuationspreadconvexity_;
  double valuationrateduration_;
  double valuationrateconvexity_;
  double enddayfullprice_;
  double enddayaccruedinterest_;
  double spreadyield_;
  double estimatecouprateaftexeday_;
  double resiprincipal_;
  double valuationpreyield_;
  double valuationyield_;
  ::google::protobuf::int32 recommendation_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDChinaBondBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondValuation> MDChinaBondValuation_default_instance_;

// -------------------------------------------------------------------

class MDChinaBondYieldCurve : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDChinaBondYieldCurve) */ {
 public:
  MDChinaBondYieldCurve();
  virtual ~MDChinaBondYieldCurve();

  MDChinaBondYieldCurve(const MDChinaBondYieldCurve& from);

  inline MDChinaBondYieldCurve& operator=(const MDChinaBondYieldCurve& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDChinaBondYieldCurve& default_instance();

  static const MDChinaBondYieldCurve* internal_default_instance();

  void Swap(MDChinaBondYieldCurve* other);

  // implements Message ----------------------------------------------

  inline MDChinaBondYieldCurve* New() const { return New(NULL); }

  MDChinaBondYieldCurve* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDChinaBondYieldCurve& from);
  void MergeFrom(const MDChinaBondYieldCurve& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDChinaBondYieldCurve* other);
  void UnsafeMergeFrom(const MDChinaBondYieldCurve& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string CalculateTime = 1;
  void clear_calculatetime();
  static const int kCalculateTimeFieldNumber = 1;
  const ::std::string& calculatetime() const;
  void set_calculatetime(const ::std::string& value);
  void set_calculatetime(const char* value);
  void set_calculatetime(const char* value, size_t size);
  ::std::string* mutable_calculatetime();
  ::std::string* release_calculatetime();
  void set_allocated_calculatetime(::std::string* calculatetime);

  // optional string TransactionTime = 2;
  void clear_transactiontime();
  static const int kTransactionTimeFieldNumber = 2;
  const ::std::string& transactiontime() const;
  void set_transactiontime(const ::std::string& value);
  void set_transactiontime(const char* value);
  void set_transactiontime(const char* value, size_t size);
  ::std::string* mutable_transactiontime();
  ::std::string* release_transactiontime();
  void set_allocated_transactiontime(::std::string* transactiontime);

  // optional double StandSlip = 11;
  void clear_standslip();
  static const int kStandSlipFieldNumber = 11;
  double standslip() const;
  void set_standslip(double value);

  // optional string CurveDesc = 12;
  void clear_curvedesc();
  static const int kCurveDescFieldNumber = 12;
  const ::std::string& curvedesc() const;
  void set_curvedesc(const ::std::string& value);
  void set_curvedesc(const char* value);
  void set_curvedesc(const char* value, size_t size);
  ::std::string* mutable_curvedesc();
  ::std::string* release_curvedesc();
  void set_allocated_curvedesc(::std::string* curvedesc);

  // optional int32 CurveType = 13;
  void clear_curvetype();
  static const int kCurveTypeFieldNumber = 13;
  ::google::protobuf::int32 curvetype() const;
  void set_curvetype(::google::protobuf::int32 value);

  // optional double NValue = 14;
  void clear_nvalue();
  static const int kNValueFieldNumber = 14;
  double nvalue() const;
  void set_nvalue(double value);

  // optional double KValue = 15;
  void clear_kvalue();
  static const int kKValueFieldNumber = 15;
  double kvalue() const;
  void set_kvalue(double value);

  // optional double CurveYield = 17;
  void clear_curveyield();
  static const int kCurveYieldFieldNumber = 17;
  double curveyield() const;
  void set_curveyield(double value);

  // optional double CurvePreYield = 18;
  void clear_curvepreyield();
  static const int kCurvePreYieldFieldNumber = 18;
  double curvepreyield() const;
  void set_curvepreyield(double value);

  // optional string CurveName = 20;
  void clear_curvename();
  static const int kCurveNameFieldNumber = 20;
  const ::std::string& curvename() const;
  void set_curvename(const ::std::string& value);
  void set_curvename(const char* value);
  void set_curvename(const char* value, size_t size);
  ::std::string* mutable_curvename();
  ::std::string* release_curvename();
  void set_allocated_curvename(::std::string* curvename);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr calculatetime_;
  ::google::protobuf::internal::ArenaStringPtr transactiontime_;
  ::google::protobuf::internal::ArenaStringPtr curvedesc_;
  ::google::protobuf::internal::ArenaStringPtr curvename_;
  double standslip_;
  double nvalue_;
  double kvalue_;
  double curveyield_;
  double curvepreyield_;
  ::google::protobuf::int32 curvetype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_impl();
  friend void  protobuf_AddDesc_MDChinaBondBenchmark_2eproto_impl();
  friend void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto();
  friend void protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondYieldCurve> MDChinaBondYieldCurve_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDChinaBondBenchmark

// optional string HTSCSecurityID = 1;
inline void MDChinaBondBenchmark::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondBenchmark::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondBenchmark::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
inline void MDChinaBondBenchmark::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
inline void MDChinaBondBenchmark::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
inline ::std::string* MDChinaBondBenchmark::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondBenchmark::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondBenchmark::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
inline void MDChinaBondBenchmark::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDChinaBondBenchmark::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDChinaBondBenchmark::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
inline void MDChinaBondBenchmark::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDChinaBondBenchmark::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDChinaBondBenchmark::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityIDSource)
}

// optional int32 MDDate = 4;
inline void MDChinaBondBenchmark::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondBenchmark::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDDate)
  return mddate_;
}
inline void MDChinaBondBenchmark::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDDate)
}

// optional int32 MDTime = 5;
inline void MDChinaBondBenchmark::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondBenchmark::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDTime)
  return mdtime_;
}
inline void MDChinaBondBenchmark::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDTime)
}

// optional int32 DataMultiplePowerOf10 = 6;
inline void MDChinaBondBenchmark::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondBenchmark::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDChinaBondBenchmark::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.DataMultiplePowerOf10)
}

// optional int32 BenchmarkType = 10;
inline void MDChinaBondBenchmark::clear_benchmarktype() {
  benchmarktype_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondBenchmark::benchmarktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.BenchmarkType)
  return benchmarktype_;
}
inline void MDChinaBondBenchmark::set_benchmarktype(::google::protobuf::int32 value) {
  
  benchmarktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.BenchmarkType)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
inline bool MDChinaBondBenchmark::has_mdchinabondvaluation() const {
  return this != internal_default_instance() && mdchinabondvaluation_ != NULL;
}
inline void MDChinaBondBenchmark::clear_mdchinabondvaluation() {
  if (GetArenaNoVirtual() == NULL && mdchinabondvaluation_ != NULL) delete mdchinabondvaluation_;
  mdchinabondvaluation_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDChinaBondValuation& MDChinaBondBenchmark::mdchinabondvaluation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  return mdchinabondvaluation_ != NULL ? *mdchinabondvaluation_
                         : *::com::htsc::mdc::insight::model::MDChinaBondValuation::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDChinaBondValuation* MDChinaBondBenchmark::mutable_mdchinabondvaluation() {
  
  if (mdchinabondvaluation_ == NULL) {
    mdchinabondvaluation_ = new ::com::htsc::mdc::insight::model::MDChinaBondValuation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  return mdchinabondvaluation_;
}
inline ::com::htsc::mdc::insight::model::MDChinaBondValuation* MDChinaBondBenchmark::release_mdchinabondvaluation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  
  ::com::htsc::mdc::insight::model::MDChinaBondValuation* temp = mdchinabondvaluation_;
  mdchinabondvaluation_ = NULL;
  return temp;
}
inline void MDChinaBondBenchmark::set_allocated_mdchinabondvaluation(::com::htsc::mdc::insight::model::MDChinaBondValuation* mdchinabondvaluation) {
  delete mdchinabondvaluation_;
  mdchinabondvaluation_ = mdchinabondvaluation;
  if (mdchinabondvaluation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
inline bool MDChinaBondBenchmark::has_mdchinabondyieldcurves() const {
  return this != internal_default_instance() && mdchinabondyieldcurves_ != NULL;
}
inline void MDChinaBondBenchmark::clear_mdchinabondyieldcurves() {
  if (GetArenaNoVirtual() == NULL && mdchinabondyieldcurves_ != NULL) delete mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve& MDChinaBondBenchmark::mdchinabondyieldcurves() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  return mdchinabondyieldcurves_ != NULL ? *mdchinabondyieldcurves_
                         : *::com::htsc::mdc::insight::model::MDChinaBondYieldCurve::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* MDChinaBondBenchmark::mutable_mdchinabondyieldcurves() {
  
  if (mdchinabondyieldcurves_ == NULL) {
    mdchinabondyieldcurves_ = new ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  return mdchinabondyieldcurves_;
}
inline ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* MDChinaBondBenchmark::release_mdchinabondyieldcurves() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  
  ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* temp = mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = NULL;
  return temp;
}
inline void MDChinaBondBenchmark::set_allocated_mdchinabondyieldcurves(::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* mdchinabondyieldcurves) {
  delete mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = mdchinabondyieldcurves;
  if (mdchinabondyieldcurves) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
}

inline const MDChinaBondBenchmark* MDChinaBondBenchmark::internal_default_instance() {
  return &MDChinaBondBenchmark_default_instance_.get();
}
// -------------------------------------------------------------------

// MDChinaBondValuation

// optional string CalculateTime = 1;
inline void MDChinaBondValuation::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondValuation::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
inline void MDChinaBondValuation::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
inline void MDChinaBondValuation::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
inline ::std::string* MDChinaBondValuation::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondValuation::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}

// optional string TransactionTime = 2;
inline void MDChinaBondValuation::clear_transactiontime() {
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondValuation::transactiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  return transactiontime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_transactiontime(const ::std::string& value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
inline void MDChinaBondValuation::set_transactiontime(const char* value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
inline void MDChinaBondValuation::set_transactiontime(const char* value, size_t size) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
inline ::std::string* MDChinaBondValuation::mutable_transactiontime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  return transactiontime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondValuation::release_transactiontime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  
  return transactiontime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_allocated_transactiontime(::std::string* transactiontime) {
  if (transactiontime != NULL) {
    
  } else {
    
  }
  transactiontime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactiontime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}

// optional string MDMkt = 11;
inline void MDChinaBondValuation::clear_mdmkt() {
  mdmkt_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondValuation::mdmkt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  return mdmkt_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_mdmkt(const ::std::string& value) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
inline void MDChinaBondValuation::set_mdmkt(const char* value) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
inline void MDChinaBondValuation::set_mdmkt(const char* value, size_t size) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
inline ::std::string* MDChinaBondValuation::mutable_mdmkt() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  return mdmkt_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondValuation::release_mdmkt() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  
  return mdmkt_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_allocated_mdmkt(::std::string* mdmkt) {
  if (mdmkt != NULL) {
    
  } else {
    
  }
  mdmkt_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdmkt);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}

// optional double MaturityTerm = 12;
inline void MDChinaBondValuation::clear_maturityterm() {
  maturityterm_ = 0;
}
inline double MDChinaBondValuation::maturityterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.MaturityTerm)
  return maturityterm_;
}
inline void MDChinaBondValuation::set_maturityterm(double value) {
  
  maturityterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.MaturityTerm)
}

// optional double IntraDayValuationFullPrice = 13;
inline void MDChinaBondValuation::clear_intradayvaluationfullprice() {
  intradayvaluationfullprice_ = 0;
}
inline double MDChinaBondValuation::intradayvaluationfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayValuationFullPrice)
  return intradayvaluationfullprice_;
}
inline void MDChinaBondValuation::set_intradayvaluationfullprice(double value) {
  
  intradayvaluationfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayValuationFullPrice)
}

// optional double IntraDayAccruedInterest = 14;
inline void MDChinaBondValuation::clear_intradayaccruedinterest() {
  intradayaccruedinterest_ = 0;
}
inline double MDChinaBondValuation::intradayaccruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayAccruedInterest)
  return intradayaccruedinterest_;
}
inline void MDChinaBondValuation::set_intradayaccruedinterest(double value) {
  
  intradayaccruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayAccruedInterest)
}

// optional double ValuationNetPrice = 15;
inline void MDChinaBondValuation::clear_valuationnetprice() {
  valuationnetprice_ = 0;
}
inline double MDChinaBondValuation::valuationnetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationNetPrice)
  return valuationnetprice_;
}
inline void MDChinaBondValuation::set_valuationnetprice(double value) {
  
  valuationnetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationNetPrice)
}

// optional double ValuationModifiedDuration = 16;
inline void MDChinaBondValuation::clear_valuationmodifiedduration() {
  valuationmodifiedduration_ = 0;
}
inline double MDChinaBondValuation::valuationmodifiedduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationModifiedDuration)
  return valuationmodifiedduration_;
}
inline void MDChinaBondValuation::set_valuationmodifiedduration(double value) {
  
  valuationmodifiedduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationModifiedDuration)
}

// optional double ValuationConvexity = 17;
inline void MDChinaBondValuation::clear_valuationconvexity() {
  valuationconvexity_ = 0;
}
inline double MDChinaBondValuation::valuationconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationConvexity)
  return valuationconvexity_;
}
inline void MDChinaBondValuation::set_valuationconvexity(double value) {
  
  valuationconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationConvexity)
}

// optional double ValuationBPV = 18;
inline void MDChinaBondValuation::clear_valuationbpv() {
  valuationbpv_ = 0;
}
inline double MDChinaBondValuation::valuationbpv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationBPV)
  return valuationbpv_;
}
inline void MDChinaBondValuation::set_valuationbpv(double value) {
  
  valuationbpv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationBPV)
}

// optional double ValuationSpreadDuration = 19;
inline void MDChinaBondValuation::clear_valuationspreadduration() {
  valuationspreadduration_ = 0;
}
inline double MDChinaBondValuation::valuationspreadduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadDuration)
  return valuationspreadduration_;
}
inline void MDChinaBondValuation::set_valuationspreadduration(double value) {
  
  valuationspreadduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadDuration)
}

// optional double ValuationSpreadConvexity = 20;
inline void MDChinaBondValuation::clear_valuationspreadconvexity() {
  valuationspreadconvexity_ = 0;
}
inline double MDChinaBondValuation::valuationspreadconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadConvexity)
  return valuationspreadconvexity_;
}
inline void MDChinaBondValuation::set_valuationspreadconvexity(double value) {
  
  valuationspreadconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadConvexity)
}

// optional double ValuationRateDuration = 21;
inline void MDChinaBondValuation::clear_valuationrateduration() {
  valuationrateduration_ = 0;
}
inline double MDChinaBondValuation::valuationrateduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateDuration)
  return valuationrateduration_;
}
inline void MDChinaBondValuation::set_valuationrateduration(double value) {
  
  valuationrateduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateDuration)
}

// optional double ValuationRateConvexity = 22;
inline void MDChinaBondValuation::clear_valuationrateconvexity() {
  valuationrateconvexity_ = 0;
}
inline double MDChinaBondValuation::valuationrateconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateConvexity)
  return valuationrateconvexity_;
}
inline void MDChinaBondValuation::set_valuationrateconvexity(double value) {
  
  valuationrateconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateConvexity)
}

// optional double EndDayFullPrice = 23;
inline void MDChinaBondValuation::clear_enddayfullprice() {
  enddayfullprice_ = 0;
}
inline double MDChinaBondValuation::enddayfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayFullPrice)
  return enddayfullprice_;
}
inline void MDChinaBondValuation::set_enddayfullprice(double value) {
  
  enddayfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayFullPrice)
}

// optional double EndDayAccruedInterest = 24;
inline void MDChinaBondValuation::clear_enddayaccruedinterest() {
  enddayaccruedinterest_ = 0;
}
inline double MDChinaBondValuation::enddayaccruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayAccruedInterest)
  return enddayaccruedinterest_;
}
inline void MDChinaBondValuation::set_enddayaccruedinterest(double value) {
  
  enddayaccruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayAccruedInterest)
}

// optional double SpreadYield = 25;
inline void MDChinaBondValuation::clear_spreadyield() {
  spreadyield_ = 0;
}
inline double MDChinaBondValuation::spreadyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.SpreadYield)
  return spreadyield_;
}
inline void MDChinaBondValuation::set_spreadyield(double value) {
  
  spreadyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.SpreadYield)
}

// optional double EstimateCoupRateAftExeDay = 26;
inline void MDChinaBondValuation::clear_estimatecouprateaftexeday() {
  estimatecouprateaftexeday_ = 0;
}
inline double MDChinaBondValuation::estimatecouprateaftexeday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EstimateCoupRateAftExeDay)
  return estimatecouprateaftexeday_;
}
inline void MDChinaBondValuation::set_estimatecouprateaftexeday(double value) {
  
  estimatecouprateaftexeday_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EstimateCoupRateAftExeDay)
}

// optional double ResiPrincipal = 27;
inline void MDChinaBondValuation::clear_resiprincipal() {
  resiprincipal_ = 0;
}
inline double MDChinaBondValuation::resiprincipal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ResiPrincipal)
  return resiprincipal_;
}
inline void MDChinaBondValuation::set_resiprincipal(double value) {
  
  resiprincipal_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ResiPrincipal)
}

// optional double ValuationPreYield = 28;
inline void MDChinaBondValuation::clear_valuationpreyield() {
  valuationpreyield_ = 0;
}
inline double MDChinaBondValuation::valuationpreyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationPreYield)
  return valuationpreyield_;
}
inline void MDChinaBondValuation::set_valuationpreyield(double value) {
  
  valuationpreyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationPreYield)
}

// optional double ValuationYield = 29;
inline void MDChinaBondValuation::clear_valuationyield() {
  valuationyield_ = 0;
}
inline double MDChinaBondValuation::valuationyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationYield)
  return valuationyield_;
}
inline void MDChinaBondValuation::set_valuationyield(double value) {
  
  valuationyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationYield)
}

// optional int32 Recommendation = 30;
inline void MDChinaBondValuation::clear_recommendation() {
  recommendation_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondValuation::recommendation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.Recommendation)
  return recommendation_;
}
inline void MDChinaBondValuation::set_recommendation(::google::protobuf::int32 value) {
  
  recommendation_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.Recommendation)
}

// optional string InstrumentShortName = 31;
inline void MDChinaBondValuation::clear_instrumentshortname() {
  instrumentshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondValuation::instrumentshortname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  return instrumentshortname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_instrumentshortname(const ::std::string& value) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
inline void MDChinaBondValuation::set_instrumentshortname(const char* value) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
inline void MDChinaBondValuation::set_instrumentshortname(const char* value, size_t size) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
inline ::std::string* MDChinaBondValuation::mutable_instrumentshortname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  return instrumentshortname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondValuation::release_instrumentshortname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  
  return instrumentshortname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondValuation::set_allocated_instrumentshortname(::std::string* instrumentshortname) {
  if (instrumentshortname != NULL) {
    
  } else {
    
  }
  instrumentshortname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentshortname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}

inline const MDChinaBondValuation* MDChinaBondValuation::internal_default_instance() {
  return &MDChinaBondValuation_default_instance_.get();
}
// -------------------------------------------------------------------

// MDChinaBondYieldCurve

// optional string CalculateTime = 1;
inline void MDChinaBondYieldCurve::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondYieldCurve::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
inline void MDChinaBondYieldCurve::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
inline void MDChinaBondYieldCurve::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
inline ::std::string* MDChinaBondYieldCurve::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondYieldCurve::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}

// optional string TransactionTime = 2;
inline void MDChinaBondYieldCurve::clear_transactiontime() {
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondYieldCurve::transactiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  return transactiontime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_transactiontime(const ::std::string& value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
inline void MDChinaBondYieldCurve::set_transactiontime(const char* value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
inline void MDChinaBondYieldCurve::set_transactiontime(const char* value, size_t size) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
inline ::std::string* MDChinaBondYieldCurve::mutable_transactiontime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  return transactiontime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondYieldCurve::release_transactiontime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  
  return transactiontime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_allocated_transactiontime(::std::string* transactiontime) {
  if (transactiontime != NULL) {
    
  } else {
    
  }
  transactiontime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactiontime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}

// optional double StandSlip = 11;
inline void MDChinaBondYieldCurve::clear_standslip() {
  standslip_ = 0;
}
inline double MDChinaBondYieldCurve::standslip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.StandSlip)
  return standslip_;
}
inline void MDChinaBondYieldCurve::set_standslip(double value) {
  
  standslip_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.StandSlip)
}

// optional string CurveDesc = 12;
inline void MDChinaBondYieldCurve::clear_curvedesc() {
  curvedesc_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondYieldCurve::curvedesc() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  return curvedesc_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_curvedesc(const ::std::string& value) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
inline void MDChinaBondYieldCurve::set_curvedesc(const char* value) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
inline void MDChinaBondYieldCurve::set_curvedesc(const char* value, size_t size) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
inline ::std::string* MDChinaBondYieldCurve::mutable_curvedesc() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  return curvedesc_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondYieldCurve::release_curvedesc() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  
  return curvedesc_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_allocated_curvedesc(::std::string* curvedesc) {
  if (curvedesc != NULL) {
    
  } else {
    
  }
  curvedesc_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvedesc);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}

// optional int32 CurveType = 13;
inline void MDChinaBondYieldCurve::clear_curvetype() {
  curvetype_ = 0;
}
inline ::google::protobuf::int32 MDChinaBondYieldCurve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveType)
  return curvetype_;
}
inline void MDChinaBondYieldCurve::set_curvetype(::google::protobuf::int32 value) {
  
  curvetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveType)
}

// optional double NValue = 14;
inline void MDChinaBondYieldCurve::clear_nvalue() {
  nvalue_ = 0;
}
inline double MDChinaBondYieldCurve::nvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.NValue)
  return nvalue_;
}
inline void MDChinaBondYieldCurve::set_nvalue(double value) {
  
  nvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.NValue)
}

// optional double KValue = 15;
inline void MDChinaBondYieldCurve::clear_kvalue() {
  kvalue_ = 0;
}
inline double MDChinaBondYieldCurve::kvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.KValue)
  return kvalue_;
}
inline void MDChinaBondYieldCurve::set_kvalue(double value) {
  
  kvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.KValue)
}

// optional double CurveYield = 17;
inline void MDChinaBondYieldCurve::clear_curveyield() {
  curveyield_ = 0;
}
inline double MDChinaBondYieldCurve::curveyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveYield)
  return curveyield_;
}
inline void MDChinaBondYieldCurve::set_curveyield(double value) {
  
  curveyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveYield)
}

// optional double CurvePreYield = 18;
inline void MDChinaBondYieldCurve::clear_curvepreyield() {
  curvepreyield_ = 0;
}
inline double MDChinaBondYieldCurve::curvepreyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurvePreYield)
  return curvepreyield_;
}
inline void MDChinaBondYieldCurve::set_curvepreyield(double value) {
  
  curvepreyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurvePreYield)
}

// optional string CurveName = 20;
inline void MDChinaBondYieldCurve::clear_curvename() {
  curvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDChinaBondYieldCurve::curvename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  return curvename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_curvename(const ::std::string& value) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
inline void MDChinaBondYieldCurve::set_curvename(const char* value) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
inline void MDChinaBondYieldCurve::set_curvename(const char* value, size_t size) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
inline ::std::string* MDChinaBondYieldCurve::mutable_curvename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  return curvename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDChinaBondYieldCurve::release_curvename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  
  return curvename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDChinaBondYieldCurve::set_allocated_curvename(::std::string* curvename) {
  if (curvename != NULL) {
    
  } else {
    
  }
  curvename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}

inline const MDChinaBondYieldCurve* MDChinaBondYieldCurve::internal_default_instance() {
  return &MDChinaBondYieldCurve_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDChinaBondBenchmark_2eproto__INCLUDED
