// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ServiceDiscovery.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "ServiceDiscovery.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* ServiceDiscoveryRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ServiceDiscoveryRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* ServiceDiscoveryResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ServiceDiscoveryResponse_reflection_ = NULL;
const ::google::protobuf::Descriptor* ServerInfo_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ServerInfo_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_ServiceDiscovery_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_ServiceDiscovery_2eproto() {
  protobuf_AddDesc_ServiceDiscovery_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "ServiceDiscovery.proto");
  GOOGLE_CHECK(file != NULL);
  ServiceDiscoveryRequest_descriptor_ = file->message_type(0);
  static const int ServiceDiscoveryRequest_offsets_[5] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, apptype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, appversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, username_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, deviceid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, issupportcompressed_),
  };
  ServiceDiscoveryRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ServiceDiscoveryRequest_descriptor_,
      ServiceDiscoveryRequest::internal_default_instance(),
      ServiceDiscoveryRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(ServiceDiscoveryRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryRequest, _internal_metadata_));
  ServiceDiscoveryResponse_descriptor_ = file->message_type(1);
  static const int ServiceDiscoveryResponse_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryResponse, issuccess_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryResponse, errorcontext_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryResponse, servers_),
  };
  ServiceDiscoveryResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ServiceDiscoveryResponse_descriptor_,
      ServiceDiscoveryResponse::internal_default_instance(),
      ServiceDiscoveryResponse_offsets_,
      -1,
      -1,
      -1,
      sizeof(ServiceDiscoveryResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServiceDiscoveryResponse, _internal_metadata_));
  ServerInfo_descriptor_ = file->message_type(2);
  static const int ServerInfo_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, ip_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, port_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, iptype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, sitetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, sitename_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, ipversion_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, isssl_),
  };
  ServerInfo_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ServerInfo_descriptor_,
      ServerInfo::internal_default_instance(),
      ServerInfo_offsets_,
      -1,
      -1,
      -1,
      sizeof(ServerInfo),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ServerInfo, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_ServiceDiscovery_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ServiceDiscoveryRequest_descriptor_, ServiceDiscoveryRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ServiceDiscoveryResponse_descriptor_, ServiceDiscoveryResponse::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ServerInfo_descriptor_, ServerInfo::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_ServiceDiscovery_2eproto() {
  ServiceDiscoveryRequest_default_instance_.Shutdown();
  delete ServiceDiscoveryRequest_reflection_;
  ServiceDiscoveryResponse_default_instance_.Shutdown();
  delete ServiceDiscoveryResponse_reflection_;
  ServerInfo_default_instance_.Shutdown();
  delete ServerInfo_reflection_;
}

void protobuf_InitDefaults_ServiceDiscovery_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_InsightErrorContext_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  ServiceDiscoveryRequest_default_instance_.DefaultConstruct();
  ServiceDiscoveryResponse_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ServerInfo_default_instance_.DefaultConstruct();
  ServiceDiscoveryRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  ServiceDiscoveryResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
  ServerInfo_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_ServiceDiscovery_2eproto_once_);
void protobuf_InitDefaults_ServiceDiscovery_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_ServiceDiscovery_2eproto_once_,
                 &protobuf_InitDefaults_ServiceDiscovery_2eproto_impl);
}
void protobuf_AddDesc_ServiceDiscovery_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_ServiceDiscovery_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\026ServiceDiscovery.proto\022\032com.htsc.mdc.i"
    "nsight.model\032\031InsightErrorContext.proto\""
    "\177\n\027ServiceDiscoveryRequest\022\017\n\007appType\030\001 "
    "\001(\005\022\022\n\nappVersion\030\002 \001(\t\022\020\n\010userName\030\003 \001("
    "\t\022\020\n\010deviceId\030\004 \001(\t\022\033\n\023isSupportCompress"
    "ed\030\005 \001(\010\"\255\001\n\030ServiceDiscoveryResponse\022\021\n"
    "\tisSuccess\030\001 \001(\010\022E\n\014errorContext\030\002 \001(\0132/"
    ".com.htsc.mdc.insight.model.InsightError"
    "Context\0227\n\007servers\030\003 \003(\0132&.com.htsc.mdc."
    "insight.model.ServerInfo\"|\n\nServerInfo\022\n"
    "\n\002ip\030\001 \001(\t\022\014\n\004port\030\002 \001(\005\022\016\n\006ipType\030\003 \001(\005"
    "\022\020\n\010siteType\030\004 \001(\005\022\020\n\010siteName\030\005 \001(\t\022\021\n\t"
    "ipVersion\030\006 \001(\005\022\r\n\005isSsl\030\007 \001(\010B8\n\032com.ht"
    "sc.mdc.insight.modelB\025ServiceDiscoveryPr"
    "otoH\001\240\001\001b\006proto3", 576);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "ServiceDiscovery.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_InsightErrorContext_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_ServiceDiscovery_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_ServiceDiscovery_2eproto_once_);
void protobuf_AddDesc_ServiceDiscovery_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_ServiceDiscovery_2eproto_once_,
                 &protobuf_AddDesc_ServiceDiscovery_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_ServiceDiscovery_2eproto {
  StaticDescriptorInitializer_ServiceDiscovery_2eproto() {
    protobuf_AddDesc_ServiceDiscovery_2eproto();
  }
} static_descriptor_initializer_ServiceDiscovery_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServiceDiscoveryRequest::kAppTypeFieldNumber;
const int ServiceDiscoveryRequest::kAppVersionFieldNumber;
const int ServiceDiscoveryRequest::kUserNameFieldNumber;
const int ServiceDiscoveryRequest::kDeviceIdFieldNumber;
const int ServiceDiscoveryRequest::kIsSupportCompressedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServiceDiscoveryRequest::ServiceDiscoveryRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ServiceDiscovery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
}

void ServiceDiscoveryRequest::InitAsDefaultInstance() {
}

ServiceDiscoveryRequest::ServiceDiscoveryRequest(const ServiceDiscoveryRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
}

void ServiceDiscoveryRequest::SharedCtor() {
  appversion_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  username_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deviceid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&apptype_, 0, reinterpret_cast<char*>(&issupportcompressed_) -
    reinterpret_cast<char*>(&apptype_) + sizeof(issupportcompressed_));
  _cached_size_ = 0;
}

ServiceDiscoveryRequest::~ServiceDiscoveryRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  SharedDtor();
}

void ServiceDiscoveryRequest::SharedDtor() {
  appversion_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  username_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deviceid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ServiceDiscoveryRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServiceDiscoveryRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ServiceDiscoveryRequest_descriptor_;
}

const ServiceDiscoveryRequest& ServiceDiscoveryRequest::default_instance() {
  protobuf_InitDefaults_ServiceDiscovery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ServiceDiscoveryRequest> ServiceDiscoveryRequest_default_instance_;

ServiceDiscoveryRequest* ServiceDiscoveryRequest::New(::google::protobuf::Arena* arena) const {
  ServiceDiscoveryRequest* n = new ServiceDiscoveryRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServiceDiscoveryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ServiceDiscoveryRequest, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ServiceDiscoveryRequest*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(apptype_, issupportcompressed_);
  appversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  username_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  deviceid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ServiceDiscoveryRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int32 appType = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &apptype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_appVersion;
        break;
      }

      // optional string appVersion = 2;
      case 2: {
        if (tag == 18) {
         parse_appVersion:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_appversion()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->appversion().data(), this->appversion().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_userName;
        break;
      }

      // optional string userName = 3;
      case 3: {
        if (tag == 26) {
         parse_userName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_username()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->username().data(), this->username().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_deviceId;
        break;
      }

      // optional string deviceId = 4;
      case 4: {
        if (tag == 34) {
         parse_deviceId:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_deviceid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->deviceid().data(), this->deviceid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_isSupportCompressed;
        break;
      }

      // optional bool isSupportCompressed = 5;
      case 5: {
        if (tag == 40) {
         parse_isSupportCompressed:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issupportcompressed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  return false;
#undef DO_
}

void ServiceDiscoveryRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->apptype(), output);
  }

  // optional string appVersion = 2;
  if (this->appversion().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->appversion().data(), this->appversion().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->appversion(), output);
  }

  // optional string userName = 3;
  if (this->username().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->username().data(), this->username().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->username(), output);
  }

  // optional string deviceId = 4;
  if (this->deviceid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deviceid().data(), this->deviceid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->deviceid(), output);
  }

  // optional bool isSupportCompressed = 5;
  if (this->issupportcompressed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->issupportcompressed(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
}

::google::protobuf::uint8* ServiceDiscoveryRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->apptype(), target);
  }

  // optional string appVersion = 2;
  if (this->appversion().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->appversion().data(), this->appversion().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->appversion(), target);
  }

  // optional string userName = 3;
  if (this->username().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->username().data(), this->username().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->username(), target);
  }

  // optional string deviceId = 4;
  if (this->deviceid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->deviceid().data(), this->deviceid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->deviceid(), target);
  }

  // optional bool isSupportCompressed = 5;
  if (this->issupportcompressed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->issupportcompressed(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  return target;
}

size_t ServiceDiscoveryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  size_t total_size = 0;

  // optional int32 appType = 1;
  if (this->apptype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->apptype());
  }

  // optional string appVersion = 2;
  if (this->appversion().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->appversion());
  }

  // optional string userName = 3;
  if (this->username().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->username());
  }

  // optional string deviceId = 4;
  if (this->deviceid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->deviceid());
  }

  // optional bool isSupportCompressed = 5;
  if (this->issupportcompressed() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServiceDiscoveryRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ServiceDiscoveryRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServiceDiscoveryRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
    UnsafeMergeFrom(*source);
  }
}

void ServiceDiscoveryRequest::MergeFrom(const ServiceDiscoveryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ServiceDiscoveryRequest::UnsafeMergeFrom(const ServiceDiscoveryRequest& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.apptype() != 0) {
    set_apptype(from.apptype());
  }
  if (from.appversion().size() > 0) {

    appversion_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.appversion_);
  }
  if (from.username().size() > 0) {

    username_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.username_);
  }
  if (from.deviceid().size() > 0) {

    deviceid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.deviceid_);
  }
  if (from.issupportcompressed() != 0) {
    set_issupportcompressed(from.issupportcompressed());
  }
}

void ServiceDiscoveryRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServiceDiscoveryRequest::CopyFrom(const ServiceDiscoveryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ServiceDiscoveryRequest::IsInitialized() const {

  return true;
}

void ServiceDiscoveryRequest::Swap(ServiceDiscoveryRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServiceDiscoveryRequest::InternalSwap(ServiceDiscoveryRequest* other) {
  std::swap(apptype_, other->apptype_);
  appversion_.Swap(&other->appversion_);
  username_.Swap(&other->username_);
  deviceid_.Swap(&other->deviceid_);
  std::swap(issupportcompressed_, other->issupportcompressed_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServiceDiscoveryRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ServiceDiscoveryRequest_descriptor_;
  metadata.reflection = ServiceDiscoveryRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ServiceDiscoveryRequest

// optional int32 appType = 1;
void ServiceDiscoveryRequest::clear_apptype() {
  apptype_ = 0;
}
::google::protobuf::int32 ServiceDiscoveryRequest::apptype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appType)
  return apptype_;
}
void ServiceDiscoveryRequest::set_apptype(::google::protobuf::int32 value) {
  
  apptype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appType)
}

// optional string appVersion = 2;
void ServiceDiscoveryRequest::clear_appversion() {
  appversion_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ServiceDiscoveryRequest::appversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  return appversion_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_appversion(const ::std::string& value) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
void ServiceDiscoveryRequest::set_appversion(const char* value) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
void ServiceDiscoveryRequest::set_appversion(const char* value, size_t size) {
  
  appversion_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}
::std::string* ServiceDiscoveryRequest::mutable_appversion() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  return appversion_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ServiceDiscoveryRequest::release_appversion() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
  
  return appversion_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_allocated_appversion(::std::string* appversion) {
  if (appversion != NULL) {
    
  } else {
    
  }
  appversion_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), appversion);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.appVersion)
}

// optional string userName = 3;
void ServiceDiscoveryRequest::clear_username() {
  username_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ServiceDiscoveryRequest::username() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  return username_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_username(const ::std::string& value) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
void ServiceDiscoveryRequest::set_username(const char* value) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
void ServiceDiscoveryRequest::set_username(const char* value, size_t size) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}
::std::string* ServiceDiscoveryRequest::mutable_username() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  return username_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ServiceDiscoveryRequest::release_username() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
  
  return username_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_allocated_username(::std::string* username) {
  if (username != NULL) {
    
  } else {
    
  }
  username_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), username);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.userName)
}

// optional string deviceId = 4;
void ServiceDiscoveryRequest::clear_deviceid() {
  deviceid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ServiceDiscoveryRequest::deviceid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  return deviceid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_deviceid(const ::std::string& value) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
void ServiceDiscoveryRequest::set_deviceid(const char* value) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
void ServiceDiscoveryRequest::set_deviceid(const char* value, size_t size) {
  
  deviceid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}
::std::string* ServiceDiscoveryRequest::mutable_deviceid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  return deviceid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ServiceDiscoveryRequest::release_deviceid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
  
  return deviceid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServiceDiscoveryRequest::set_allocated_deviceid(::std::string* deviceid) {
  if (deviceid != NULL) {
    
  } else {
    
  }
  deviceid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), deviceid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.deviceId)
}

// optional bool isSupportCompressed = 5;
void ServiceDiscoveryRequest::clear_issupportcompressed() {
  issupportcompressed_ = false;
}
bool ServiceDiscoveryRequest::issupportcompressed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.isSupportCompressed)
  return issupportcompressed_;
}
void ServiceDiscoveryRequest::set_issupportcompressed(bool value) {
  
  issupportcompressed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryRequest.isSupportCompressed)
}

inline const ServiceDiscoveryRequest* ServiceDiscoveryRequest::internal_default_instance() {
  return &ServiceDiscoveryRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServiceDiscoveryResponse::kIsSuccessFieldNumber;
const int ServiceDiscoveryResponse::kErrorContextFieldNumber;
const int ServiceDiscoveryResponse::kServersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServiceDiscoveryResponse::ServiceDiscoveryResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ServiceDiscovery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
}

void ServiceDiscoveryResponse::InitAsDefaultInstance() {
  errorcontext_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
}

ServiceDiscoveryResponse::ServiceDiscoveryResponse(const ServiceDiscoveryResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
}

void ServiceDiscoveryResponse::SharedCtor() {
  errorcontext_ = NULL;
  issuccess_ = false;
  _cached_size_ = 0;
}

ServiceDiscoveryResponse::~ServiceDiscoveryResponse() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  SharedDtor();
}

void ServiceDiscoveryResponse::SharedDtor() {
  if (this != &ServiceDiscoveryResponse_default_instance_.get()) {
    delete errorcontext_;
  }
}

void ServiceDiscoveryResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServiceDiscoveryResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ServiceDiscoveryResponse_descriptor_;
}

const ServiceDiscoveryResponse& ServiceDiscoveryResponse::default_instance() {
  protobuf_InitDefaults_ServiceDiscovery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ServiceDiscoveryResponse> ServiceDiscoveryResponse_default_instance_;

ServiceDiscoveryResponse* ServiceDiscoveryResponse::New(::google::protobuf::Arena* arena) const {
  ServiceDiscoveryResponse* n = new ServiceDiscoveryResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServiceDiscoveryResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  issuccess_ = false;
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
  servers_.Clear();
}

bool ServiceDiscoveryResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool isSuccess = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issuccess_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_errorContext;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
      case 2: {
        if (tag == 18) {
         parse_errorContext:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_errorcontext()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_servers;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
      case 3: {
        if (tag == 26) {
         parse_servers:
          DO_(input->IncrementRecursionDepth());
         parse_loop_servers:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_servers()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_loop_servers;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  return false;
#undef DO_
}

void ServiceDiscoveryResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->issuccess(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->errorcontext_, output);
  }

  // repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
  for (unsigned int i = 0, n = this->servers_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->servers(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
}

::google::protobuf::uint8* ServiceDiscoveryResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->issuccess(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->errorcontext_, false, target);
  }

  // repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
  for (unsigned int i = 0, n = this->servers_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, this->servers(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  return target;
}

size_t ServiceDiscoveryResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  size_t total_size = 0;

  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    total_size += 1 + 1;
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->errorcontext_);
  }

  // repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
  {
    unsigned int count = this->servers_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->servers(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServiceDiscoveryResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ServiceDiscoveryResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServiceDiscoveryResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
    UnsafeMergeFrom(*source);
  }
}

void ServiceDiscoveryResponse::MergeFrom(const ServiceDiscoveryResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ServiceDiscoveryResponse::UnsafeMergeFrom(const ServiceDiscoveryResponse& from) {
  GOOGLE_DCHECK(&from != this);
  servers_.MergeFrom(from.servers_);
  if (from.issuccess() != 0) {
    set_issuccess(from.issuccess());
  }
  if (from.has_errorcontext()) {
    mutable_errorcontext()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.errorcontext());
  }
}

void ServiceDiscoveryResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServiceDiscoveryResponse::CopyFrom(const ServiceDiscoveryResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ServiceDiscoveryResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ServiceDiscoveryResponse::IsInitialized() const {

  return true;
}

void ServiceDiscoveryResponse::Swap(ServiceDiscoveryResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServiceDiscoveryResponse::InternalSwap(ServiceDiscoveryResponse* other) {
  std::swap(issuccess_, other->issuccess_);
  std::swap(errorcontext_, other->errorcontext_);
  servers_.UnsafeArenaSwap(&other->servers_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServiceDiscoveryResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ServiceDiscoveryResponse_descriptor_;
  metadata.reflection = ServiceDiscoveryResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ServiceDiscoveryResponse

// optional bool isSuccess = 1;
void ServiceDiscoveryResponse::clear_issuccess() {
  issuccess_ = false;
}
bool ServiceDiscoveryResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.isSuccess)
  return issuccess_;
}
void ServiceDiscoveryResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
bool ServiceDiscoveryResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
void ServiceDiscoveryResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& ServiceDiscoveryResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* ServiceDiscoveryResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  return errorcontext_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* ServiceDiscoveryResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
void ServiceDiscoveryResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.errorContext)
}

// repeated .com.htsc.mdc.insight.model.ServerInfo servers = 3;
int ServiceDiscoveryResponse::servers_size() const {
  return servers_.size();
}
void ServiceDiscoveryResponse::clear_servers() {
  servers_.Clear();
}
const ::com::htsc::mdc::insight::model::ServerInfo& ServiceDiscoveryResponse::servers(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Get(index);
}
::com::htsc::mdc::insight::model::ServerInfo* ServiceDiscoveryResponse::mutable_servers(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Mutable(index);
}
::com::htsc::mdc::insight::model::ServerInfo* ServiceDiscoveryResponse::add_servers() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >*
ServiceDiscoveryResponse::mutable_servers() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return &servers_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ServerInfo >&
ServiceDiscoveryResponse::servers() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ServiceDiscoveryResponse.servers)
  return servers_;
}

inline const ServiceDiscoveryResponse* ServiceDiscoveryResponse::internal_default_instance() {
  return &ServiceDiscoveryResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerInfo::kIpFieldNumber;
const int ServerInfo::kPortFieldNumber;
const int ServerInfo::kIpTypeFieldNumber;
const int ServerInfo::kSiteTypeFieldNumber;
const int ServerInfo::kSiteNameFieldNumber;
const int ServerInfo::kIpVersionFieldNumber;
const int ServerInfo::kIsSslFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerInfo::ServerInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ServiceDiscovery_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ServerInfo)
}

void ServerInfo::InitAsDefaultInstance() {
}

ServerInfo::ServerInfo(const ServerInfo& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ServerInfo)
}

void ServerInfo::SharedCtor() {
  ip_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sitename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&port_, 0, reinterpret_cast<char*>(&isssl_) -
    reinterpret_cast<char*>(&port_) + sizeof(isssl_));
  _cached_size_ = 0;
}

ServerInfo::~ServerInfo() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ServerInfo)
  SharedDtor();
}

void ServerInfo::SharedDtor() {
  ip_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sitename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ServerInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerInfo::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ServerInfo_descriptor_;
}

const ServerInfo& ServerInfo::default_instance() {
  protobuf_InitDefaults_ServiceDiscovery_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ServerInfo> ServerInfo_default_instance_;

ServerInfo* ServerInfo::New(::google::protobuf::Arena* arena) const {
  ServerInfo* n = new ServerInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ServerInfo)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ServerInfo, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ServerInfo*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(port_, isssl_);
  ip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  sitename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool ServerInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ServerInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ip = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_ip()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->ip().data(), this->ip().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ServerInfo.ip"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_port;
        break;
      }

      // optional int32 port = 2;
      case 2: {
        if (tag == 16) {
         parse_port:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &port_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_ipType;
        break;
      }

      // optional int32 ipType = 3;
      case 3: {
        if (tag == 24) {
         parse_ipType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &iptype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_siteType;
        break;
      }

      // optional int32 siteType = 4;
      case 4: {
        if (tag == 32) {
         parse_siteType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &sitetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_siteName;
        break;
      }

      // optional string siteName = 5;
      case 5: {
        if (tag == 42) {
         parse_siteName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_sitename()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->sitename().data(), this->sitename().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ServerInfo.siteName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_ipVersion;
        break;
      }

      // optional int32 ipVersion = 6;
      case 6: {
        if (tag == 48) {
         parse_ipVersion:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ipversion_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_isSsl;
        break;
      }

      // optional bool isSsl = 7;
      case 7: {
        if (tag == 56) {
         parse_isSsl:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isssl_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ServerInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ServerInfo)
  return false;
#undef DO_
}

void ServerInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ServerInfo)
  // optional string ip = 1;
  if (this->ip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ip().data(), this->ip().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServerInfo.ip");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->ip(), output);
  }

  // optional int32 port = 2;
  if (this->port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->port(), output);
  }

  // optional int32 ipType = 3;
  if (this->iptype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->iptype(), output);
  }

  // optional int32 siteType = 4;
  if (this->sitetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->sitetype(), output);
  }

  // optional string siteName = 5;
  if (this->sitename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sitename().data(), this->sitename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServerInfo.siteName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->sitename(), output);
  }

  // optional int32 ipVersion = 6;
  if (this->ipversion() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->ipversion(), output);
  }

  // optional bool isSsl = 7;
  if (this->isssl() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->isssl(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ServerInfo)
}

::google::protobuf::uint8* ServerInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ServerInfo)
  // optional string ip = 1;
  if (this->ip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->ip().data(), this->ip().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServerInfo.ip");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->ip(), target);
  }

  // optional int32 port = 2;
  if (this->port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->port(), target);
  }

  // optional int32 ipType = 3;
  if (this->iptype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->iptype(), target);
  }

  // optional int32 siteType = 4;
  if (this->sitetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->sitetype(), target);
  }

  // optional string siteName = 5;
  if (this->sitename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->sitename().data(), this->sitename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ServerInfo.siteName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->sitename(), target);
  }

  // optional int32 ipVersion = 6;
  if (this->ipversion() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->ipversion(), target);
  }

  // optional bool isSsl = 7;
  if (this->isssl() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->isssl(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ServerInfo)
  return target;
}

size_t ServerInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ServerInfo)
  size_t total_size = 0;

  // optional string ip = 1;
  if (this->ip().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->ip());
  }

  // optional int32 port = 2;
  if (this->port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->port());
  }

  // optional int32 ipType = 3;
  if (this->iptype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->iptype());
  }

  // optional int32 siteType = 4;
  if (this->sitetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->sitetype());
  }

  // optional string siteName = 5;
  if (this->sitename().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->sitename());
  }

  // optional int32 ipVersion = 6;
  if (this->ipversion() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ipversion());
  }

  // optional bool isSsl = 7;
  if (this->isssl() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ServerInfo)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ServerInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ServerInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ServerInfo)
    UnsafeMergeFrom(*source);
  }
}

void ServerInfo::MergeFrom(const ServerInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ServerInfo)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ServerInfo::UnsafeMergeFrom(const ServerInfo& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.ip().size() > 0) {

    ip_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ip_);
  }
  if (from.port() != 0) {
    set_port(from.port());
  }
  if (from.iptype() != 0) {
    set_iptype(from.iptype());
  }
  if (from.sitetype() != 0) {
    set_sitetype(from.sitetype());
  }
  if (from.sitename().size() > 0) {

    sitename_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.sitename_);
  }
  if (from.ipversion() != 0) {
    set_ipversion(from.ipversion());
  }
  if (from.isssl() != 0) {
    set_isssl(from.isssl());
  }
}

void ServerInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ServerInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerInfo::CopyFrom(const ServerInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ServerInfo)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ServerInfo::IsInitialized() const {

  return true;
}

void ServerInfo::Swap(ServerInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerInfo::InternalSwap(ServerInfo* other) {
  ip_.Swap(&other->ip_);
  std::swap(port_, other->port_);
  std::swap(iptype_, other->iptype_);
  std::swap(sitetype_, other->sitetype_);
  sitename_.Swap(&other->sitename_);
  std::swap(ipversion_, other->ipversion_);
  std::swap(isssl_, other->isssl_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerInfo::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ServerInfo_descriptor_;
  metadata.reflection = ServerInfo_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ServerInfo

// optional string ip = 1;
void ServerInfo::clear_ip() {
  ip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ServerInfo::ip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ip)
  return ip_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServerInfo::set_ip(const ::std::string& value) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ip)
}
void ServerInfo::set_ip(const char* value) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServerInfo.ip)
}
void ServerInfo::set_ip(const char* value, size_t size) {
  
  ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServerInfo.ip)
}
::std::string* ServerInfo::mutable_ip() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServerInfo.ip)
  return ip_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ServerInfo::release_ip() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServerInfo.ip)
  
  return ip_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServerInfo::set_allocated_ip(::std::string* ip) {
  if (ip != NULL) {
    
  } else {
    
  }
  ip_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ip);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServerInfo.ip)
}

// optional int32 port = 2;
void ServerInfo::clear_port() {
  port_ = 0;
}
::google::protobuf::int32 ServerInfo::port() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.port)
  return port_;
}
void ServerInfo::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.port)
}

// optional int32 ipType = 3;
void ServerInfo::clear_iptype() {
  iptype_ = 0;
}
::google::protobuf::int32 ServerInfo::iptype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ipType)
  return iptype_;
}
void ServerInfo::set_iptype(::google::protobuf::int32 value) {
  
  iptype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ipType)
}

// optional int32 siteType = 4;
void ServerInfo::clear_sitetype() {
  sitetype_ = 0;
}
::google::protobuf::int32 ServerInfo::sitetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.siteType)
  return sitetype_;
}
void ServerInfo::set_sitetype(::google::protobuf::int32 value) {
  
  sitetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.siteType)
}

// optional string siteName = 5;
void ServerInfo::clear_sitename() {
  sitename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ServerInfo::sitename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.siteName)
  return sitename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServerInfo::set_sitename(const ::std::string& value) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
void ServerInfo::set_sitename(const char* value) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
void ServerInfo::set_sitename(const char* value, size_t size) {
  
  sitename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ServerInfo.siteName)
}
::std::string* ServerInfo::mutable_sitename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ServerInfo.siteName)
  return sitename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ServerInfo::release_sitename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ServerInfo.siteName)
  
  return sitename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServerInfo::set_allocated_sitename(::std::string* sitename) {
  if (sitename != NULL) {
    
  } else {
    
  }
  sitename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), sitename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ServerInfo.siteName)
}

// optional int32 ipVersion = 6;
void ServerInfo::clear_ipversion() {
  ipversion_ = 0;
}
::google::protobuf::int32 ServerInfo::ipversion() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.ipVersion)
  return ipversion_;
}
void ServerInfo::set_ipversion(::google::protobuf::int32 value) {
  
  ipversion_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.ipVersion)
}

// optional bool isSsl = 7;
void ServerInfo::clear_isssl() {
  isssl_ = false;
}
bool ServerInfo::isssl() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ServerInfo.isSsl)
  return isssl_;
}
void ServerInfo::set_isssl(bool value) {
  
  isssl_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ServerInfo.isSsl)
}

inline const ServerInfo* ServerInfo::internal_default_instance() {
  return &ServerInfo_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
