// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDFuture.proto

#ifndef PROTOBUF_MDFuture_2eproto__INCLUDED
#define PROTOBUF_MDFuture_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDFuture_2eproto();
void protobuf_InitDefaults_MDFuture_2eproto();
void protobuf_AssignDesc_MDFuture_2eproto();
void protobuf_ShutdownFile_MDFuture_2eproto();

class MDFuture;

// ===================================================================

class MDFuture : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDFuture) */ {
 public:
  MDFuture();
  virtual ~MDFuture();

  MDFuture(const MDFuture& from);

  inline MDFuture& operator=(const MDFuture& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDFuture& default_instance();

  static const MDFuture* internal_default_instance();

  void Swap(MDFuture* other);

  // implements Message ----------------------------------------------

  inline MDFuture* New() const { return New(NULL); }

  MDFuture* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDFuture& from);
  void MergeFrom(const MDFuture& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDFuture* other);
  void UnsafeMergeFrom(const MDFuture& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 MaxPx = 8;
  void clear_maxpx();
  static const int kMaxPxFieldNumber = 8;
  ::google::protobuf::int64 maxpx() const;
  void set_maxpx(::google::protobuf::int64 value);

  // optional int64 MinPx = 9;
  void clear_minpx();
  static const int kMinPxFieldNumber = 9;
  ::google::protobuf::int64 minpx() const;
  void set_minpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 10;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 10;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 NumTrades = 11;
  void clear_numtrades();
  static const int kNumTradesFieldNumber = 11;
  ::google::protobuf::int64 numtrades() const;
  void set_numtrades(::google::protobuf::int64 value);

  // optional int64 TotalVolumeTrade = 12;
  void clear_totalvolumetrade();
  static const int kTotalVolumeTradeFieldNumber = 12;
  ::google::protobuf::int64 totalvolumetrade() const;
  void set_totalvolumetrade(::google::protobuf::int64 value);

  // optional int64 TotalValueTrade = 13;
  void clear_totalvaluetrade();
  static const int kTotalValueTradeFieldNumber = 13;
  ::google::protobuf::int64 totalvaluetrade() const;
  void set_totalvaluetrade(::google::protobuf::int64 value);

  // optional int64 LastPx = 14;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 14;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 OpenPx = 15;
  void clear_openpx();
  static const int kOpenPxFieldNumber = 15;
  ::google::protobuf::int64 openpx() const;
  void set_openpx(::google::protobuf::int64 value);

  // optional int64 ClosePx = 16;
  void clear_closepx();
  static const int kClosePxFieldNumber = 16;
  ::google::protobuf::int64 closepx() const;
  void set_closepx(::google::protobuf::int64 value);

  // optional int64 HighPx = 17;
  void clear_highpx();
  static const int kHighPxFieldNumber = 17;
  ::google::protobuf::int64 highpx() const;
  void set_highpx(::google::protobuf::int64 value);

  // optional int64 LowPx = 18;
  void clear_lowpx();
  static const int kLowPxFieldNumber = 18;
  ::google::protobuf::int64 lowpx() const;
  void set_lowpx(::google::protobuf::int64 value);

  // optional int32 TradingDate = 19;
  void clear_tradingdate();
  static const int kTradingDateFieldNumber = 19;
  ::google::protobuf::int32 tradingdate() const;
  void set_tradingdate(::google::protobuf::int32 value);

  // optional int64 PreOpenInterest = 20;
  void clear_preopeninterest();
  static const int kPreOpenInterestFieldNumber = 20;
  ::google::protobuf::int64 preopeninterest() const;
  void set_preopeninterest(::google::protobuf::int64 value);

  // optional int64 PreSettlePrice = 21;
  void clear_presettleprice();
  static const int kPreSettlePriceFieldNumber = 21;
  ::google::protobuf::int64 presettleprice() const;
  void set_presettleprice(::google::protobuf::int64 value);

  // optional int64 OpenInterest = 22;
  void clear_openinterest();
  static const int kOpenInterestFieldNumber = 22;
  ::google::protobuf::int64 openinterest() const;
  void set_openinterest(::google::protobuf::int64 value);

  // optional int64 SettlePrice = 23;
  void clear_settleprice();
  static const int kSettlePriceFieldNumber = 23;
  ::google::protobuf::int64 settleprice() const;
  void set_settleprice(::google::protobuf::int64 value);

  // optional int64 PreDelta = 24;
  void clear_predelta();
  static const int kPreDeltaFieldNumber = 24;
  ::google::protobuf::int64 predelta() const;
  void set_predelta(::google::protobuf::int64 value);

  // optional int64 CurrDelta = 25;
  void clear_currdelta();
  static const int kCurrDeltaFieldNumber = 25;
  ::google::protobuf::int64 currdelta() const;
  void set_currdelta(::google::protobuf::int64 value);

  // optional int64 MiddlePx = 26;
  void clear_middlepx();
  static const int kMiddlePxFieldNumber = 26;
  ::google::protobuf::int64 middlepx() const;
  void set_middlepx(::google::protobuf::int64 value);

  // optional int64 ImpliedBuyPx = 27;
  void clear_impliedbuypx();
  static const int kImpliedBuyPxFieldNumber = 27;
  ::google::protobuf::int64 impliedbuypx() const;
  void set_impliedbuypx(::google::protobuf::int64 value);

  // optional int64 ImpliedBuyQty = 28;
  void clear_impliedbuyqty();
  static const int kImpliedBuyQtyFieldNumber = 28;
  ::google::protobuf::int64 impliedbuyqty() const;
  void set_impliedbuyqty(::google::protobuf::int64 value);

  // optional int64 ImpliedSellPx = 29;
  void clear_impliedsellpx();
  static const int kImpliedSellPxFieldNumber = 29;
  ::google::protobuf::int64 impliedsellpx() const;
  void set_impliedsellpx(::google::protobuf::int64 value);

  // optional int64 ImpliedSellQty = 30;
  void clear_impliedsellqty();
  static const int kImpliedSellQtyFieldNumber = 30;
  ::google::protobuf::int64 impliedsellqty() const;
  void set_impliedsellqty(::google::protobuf::int64 value);

  // optional int64 PositionTrend = 31;
  void clear_positiontrend();
  static const int kPositionTrendFieldNumber = 31;
  ::google::protobuf::int64 positiontrend() const;
  void set_positiontrend(::google::protobuf::int64 value);

  // optional int64 ChangeSpeed = 32;
  void clear_changespeed();
  static const int kChangeSpeedFieldNumber = 32;
  ::google::protobuf::int64 changespeed() const;
  void set_changespeed(::google::protobuf::int64 value);

  // optional int64 ChangeRate = 33;
  void clear_changerate();
  static const int kChangeRateFieldNumber = 33;
  ::google::protobuf::int64 changerate() const;
  void set_changerate(::google::protobuf::int64 value);

  // optional int64 ChangeValue = 34;
  void clear_changevalue();
  static const int kChangeValueFieldNumber = 34;
  ::google::protobuf::int64 changevalue() const;
  void set_changevalue(::google::protobuf::int64 value);

  // optional int64 Swing = 35;
  void clear_swing();
  static const int kSwingFieldNumber = 35;
  ::google::protobuf::int64 swing() const;
  void set_swing(::google::protobuf::int64 value);

  // optional string CommodityContractNumber = 36;
  void clear_commoditycontractnumber();
  static const int kCommodityContractNumberFieldNumber = 36;
  const ::std::string& commoditycontractnumber() const;
  void set_commoditycontractnumber(const ::std::string& value);
  void set_commoditycontractnumber(const char* value);
  void set_commoditycontractnumber(const char* value, size_t size);
  ::std::string* mutable_commoditycontractnumber();
  ::std::string* release_commoditycontractnumber();
  void set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber);

  // optional int32 ExchangeDate = 37;
  void clear_exchangedate();
  static const int kExchangeDateFieldNumber = 37;
  ::google::protobuf::int32 exchangedate() const;
  void set_exchangedate(::google::protobuf::int32 value);

  // optional int32 ExchangeTime = 38;
  void clear_exchangetime();
  static const int kExchangeTimeFieldNumber = 38;
  ::google::protobuf::int32 exchangetime() const;
  void set_exchangetime(::google::protobuf::int32 value);

  // optional int32 ChannelNo = 50;
  void clear_channelno();
  static const int kChannelNoFieldNumber = 50;
  ::google::protobuf::int32 channelno() const;
  void set_channelno(::google::protobuf::int32 value);

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  int buypricequeue_size() const;
  void clear_buypricequeue();
  static const int kBuyPriceQueueFieldNumber = 51;
  ::google::protobuf::int64 buypricequeue(int index) const;
  void set_buypricequeue(int index, ::google::protobuf::int64 value);
  void add_buypricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buypricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buypricequeue();

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  int buyorderqtyqueue_size() const;
  void clear_buyorderqtyqueue();
  static const int kBuyOrderQtyQueueFieldNumber = 52;
  ::google::protobuf::int64 buyorderqtyqueue(int index) const;
  void set_buyorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqtyqueue();

  // repeated int64 SellPriceQueue = 53 [packed = true];
  int sellpricequeue_size() const;
  void clear_sellpricequeue();
  static const int kSellPriceQueueFieldNumber = 53;
  ::google::protobuf::int64 sellpricequeue(int index) const;
  void set_sellpricequeue(int index, ::google::protobuf::int64 value);
  void add_sellpricequeue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellpricequeue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellpricequeue();

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  int sellorderqtyqueue_size() const;
  void clear_sellorderqtyqueue();
  static const int kSellOrderQtyQueueFieldNumber = 54;
  ::google::protobuf::int64 sellorderqtyqueue(int index) const;
  void set_sellorderqtyqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqtyqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqtyqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqtyqueue();

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  int buyorderqueue_size() const;
  void clear_buyorderqueue();
  static const int kBuyOrderQueueFieldNumber = 55;
  ::google::protobuf::int64 buyorderqueue(int index) const;
  void set_buyorderqueue(int index, ::google::protobuf::int64 value);
  void add_buyorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buyorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buyorderqueue();

  // repeated int64 SellOrderQueue = 56 [packed = true];
  int sellorderqueue_size() const;
  void clear_sellorderqueue();
  static const int kSellOrderQueueFieldNumber = 56;
  ::google::protobuf::int64 sellorderqueue(int index) const;
  void set_sellorderqueue(int index, ::google::protobuf::int64 value);
  void add_sellorderqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellorderqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellorderqueue();

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  int buynumordersqueue_size() const;
  void clear_buynumordersqueue();
  static const int kBuyNumOrdersQueueFieldNumber = 57;
  ::google::protobuf::int64 buynumordersqueue(int index) const;
  void set_buynumordersqueue(int index, ::google::protobuf::int64 value);
  void add_buynumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      buynumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_buynumordersqueue();

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  int sellnumordersqueue_size() const;
  void clear_sellnumordersqueue();
  static const int kSellNumOrdersQueueFieldNumber = 58;
  ::google::protobuf::int64 sellnumordersqueue(int index) const;
  void set_sellnumordersqueue(int index, ::google::protobuf::int64 value);
  void add_sellnumordersqueue(::google::protobuf::int64 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
      sellnumordersqueue() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
      mutable_sellnumordersqueue();

  // optional int32 DataMultiplePowerOf10 = 59;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 59;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // optional int64 BlockVolumeTrade = 60;
  void clear_blockvolumetrade();
  static const int kBlockVolumeTradeFieldNumber = 60;
  ::google::protobuf::int64 blockvolumetrade() const;
  void set_blockvolumetrade(::google::protobuf::int64 value);

  // optional int64 EligibleVolumeTrade = 61;
  void clear_eligiblevolumetrade();
  static const int kEligibleVolumeTradeFieldNumber = 61;
  ::google::protobuf::int64 eligiblevolumetrade() const;
  void set_eligiblevolumetrade(::google::protobuf::int64 value);

  // optional int64 StrategyVolumeTrade = 62;
  void clear_strategyvolumetrade();
  static const int kStrategyVolumeTradeFieldNumber = 62;
  ::google::protobuf::int64 strategyvolumetrade() const;
  void set_strategyvolumetrade(::google::protobuf::int64 value);

  // optional int32 PreOpenInterestDate = 63;
  void clear_preopeninterestdate();
  static const int kPreOpenInterestDateFieldNumber = 63;
  ::google::protobuf::int32 preopeninterestdate() const;
  void set_preopeninterestdate(::google::protobuf::int32 value);

  // optional int32 PreSettleDate = 64;
  void clear_presettledate();
  static const int kPreSettleDateFieldNumber = 64;
  ::google::protobuf::int32 presettledate() const;
  void set_presettledate(::google::protobuf::int32 value);

  // optional int32 PreCloseDate = 65;
  void clear_preclosedate();
  static const int kPreCloseDateFieldNumber = 65;
  ::google::protobuf::int32 preclosedate() const;
  void set_preclosedate(::google::protobuf::int32 value);

  // optional int32 DelayType = 101;
  void clear_delaytype();
  static const int kDelayTypeFieldNumber = 101;
  ::google::protobuf::int32 delaytype() const;
  void set_delaytype(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDFuture)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buypricequeue_;
  mutable int _buypricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqtyqueue_;
  mutable int _buyorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellpricequeue_;
  mutable int _sellpricequeue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqtyqueue_;
  mutable int _sellorderqtyqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buyorderqueue_;
  mutable int _buyorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellorderqueue_;
  mutable int _sellorderqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > buynumordersqueue_;
  mutable int _buynumordersqueue_cached_byte_size_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int64 > sellnumordersqueue_;
  mutable int _sellnumordersqueue_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr commoditycontractnumber_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 maxpx_;
  ::google::protobuf::int64 minpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 numtrades_;
  ::google::protobuf::int64 totalvolumetrade_;
  ::google::protobuf::int64 totalvaluetrade_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 openpx_;
  ::google::protobuf::int64 closepx_;
  ::google::protobuf::int64 highpx_;
  ::google::protobuf::int64 lowpx_;
  ::google::protobuf::int64 preopeninterest_;
  ::google::protobuf::int64 presettleprice_;
  ::google::protobuf::int64 openinterest_;
  ::google::protobuf::int64 settleprice_;
  ::google::protobuf::int64 predelta_;
  ::google::protobuf::int64 currdelta_;
  ::google::protobuf::int64 middlepx_;
  ::google::protobuf::int64 impliedbuypx_;
  ::google::protobuf::int64 impliedbuyqty_;
  ::google::protobuf::int32 tradingdate_;
  ::google::protobuf::int32 exchangedate_;
  ::google::protobuf::int64 impliedsellpx_;
  ::google::protobuf::int64 impliedsellqty_;
  ::google::protobuf::int64 positiontrend_;
  ::google::protobuf::int64 changespeed_;
  ::google::protobuf::int64 changerate_;
  ::google::protobuf::int64 changevalue_;
  ::google::protobuf::int64 swing_;
  ::google::protobuf::int32 exchangetime_;
  ::google::protobuf::int32 channelno_;
  ::google::protobuf::int64 blockvolumetrade_;
  ::google::protobuf::int64 eligiblevolumetrade_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  ::google::protobuf::int32 preopeninterestdate_;
  ::google::protobuf::int64 strategyvolumetrade_;
  ::google::protobuf::int32 presettledate_;
  ::google::protobuf::int32 preclosedate_;
  ::google::protobuf::int32 delaytype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDFuture_2eproto_impl();
  friend void  protobuf_AddDesc_MDFuture_2eproto_impl();
  friend void protobuf_AssignDesc_MDFuture_2eproto();
  friend void protobuf_ShutdownFile_MDFuture_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDFuture> MDFuture_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDFuture

// optional string HTSCSecurityID = 1;
inline void MDFuture::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFuture::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
}
inline void MDFuture::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
}
inline void MDFuture::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
}
inline ::std::string* MDFuture::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFuture::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFuture.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDFuture::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.MDDate)
  return mddate_;
}
inline void MDFuture::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.MDDate)
}

// optional int32 MDTime = 3;
inline void MDFuture::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDFuture::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.MDTime)
  return mdtime_;
}
inline void MDFuture::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDFuture::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.DataTimestamp)
  return datatimestamp_;
}
inline void MDFuture::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDFuture::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFuture::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
}
inline void MDFuture::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
}
inline void MDFuture::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
}
inline ::std::string* MDFuture::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFuture::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFuture.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDFuture::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDFuture::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDFuture::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDFuture::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDFuture::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDFuture::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.securityType)
}

// optional int64 MaxPx = 8;
inline void MDFuture::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.MaxPx)
  return maxpx_;
}
inline void MDFuture::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.MaxPx)
}

// optional int64 MinPx = 9;
inline void MDFuture::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.MinPx)
  return minpx_;
}
inline void MDFuture::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.MinPx)
}

// optional int64 PreClosePx = 10;
inline void MDFuture::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreClosePx)
  return preclosepx_;
}
inline void MDFuture::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreClosePx)
}

// optional int64 NumTrades = 11;
inline void MDFuture::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.NumTrades)
  return numtrades_;
}
inline void MDFuture::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
inline void MDFuture::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.TotalVolumeTrade)
  return totalvolumetrade_;
}
inline void MDFuture::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
inline void MDFuture::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.TotalValueTrade)
  return totalvaluetrade_;
}
inline void MDFuture::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.TotalValueTrade)
}

// optional int64 LastPx = 14;
inline void MDFuture::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.LastPx)
  return lastpx_;
}
inline void MDFuture::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.LastPx)
}

// optional int64 OpenPx = 15;
inline void MDFuture::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.OpenPx)
  return openpx_;
}
inline void MDFuture::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.OpenPx)
}

// optional int64 ClosePx = 16;
inline void MDFuture::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ClosePx)
  return closepx_;
}
inline void MDFuture::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ClosePx)
}

// optional int64 HighPx = 17;
inline void MDFuture::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.HighPx)
  return highpx_;
}
inline void MDFuture::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.HighPx)
}

// optional int64 LowPx = 18;
inline void MDFuture::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.LowPx)
  return lowpx_;
}
inline void MDFuture::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.LowPx)
}

// optional int32 TradingDate = 19;
inline void MDFuture::clear_tradingdate() {
  tradingdate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.TradingDate)
  return tradingdate_;
}
inline void MDFuture::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.TradingDate)
}

// optional int64 PreOpenInterest = 20;
inline void MDFuture::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreOpenInterest)
  return preopeninterest_;
}
inline void MDFuture::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreOpenInterest)
}

// optional int64 PreSettlePrice = 21;
inline void MDFuture::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreSettlePrice)
  return presettleprice_;
}
inline void MDFuture::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreSettlePrice)
}

// optional int64 OpenInterest = 22;
inline void MDFuture::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.OpenInterest)
  return openinterest_;
}
inline void MDFuture::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.OpenInterest)
}

// optional int64 SettlePrice = 23;
inline void MDFuture::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.SettlePrice)
  return settleprice_;
}
inline void MDFuture::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.SettlePrice)
}

// optional int64 PreDelta = 24;
inline void MDFuture::clear_predelta() {
  predelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::predelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreDelta)
  return predelta_;
}
inline void MDFuture::set_predelta(::google::protobuf::int64 value) {
  
  predelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreDelta)
}

// optional int64 CurrDelta = 25;
inline void MDFuture::clear_currdelta() {
  currdelta_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::currdelta() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.CurrDelta)
  return currdelta_;
}
inline void MDFuture::set_currdelta(::google::protobuf::int64 value) {
  
  currdelta_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.CurrDelta)
}

// optional int64 MiddlePx = 26;
inline void MDFuture::clear_middlepx() {
  middlepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::middlepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.MiddlePx)
  return middlepx_;
}
inline void MDFuture::set_middlepx(::google::protobuf::int64 value) {
  
  middlepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.MiddlePx)
}

// optional int64 ImpliedBuyPx = 27;
inline void MDFuture::clear_impliedbuypx() {
  impliedbuypx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::impliedbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ImpliedBuyPx)
  return impliedbuypx_;
}
inline void MDFuture::set_impliedbuypx(::google::protobuf::int64 value) {
  
  impliedbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ImpliedBuyPx)
}

// optional int64 ImpliedBuyQty = 28;
inline void MDFuture::clear_impliedbuyqty() {
  impliedbuyqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::impliedbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ImpliedBuyQty)
  return impliedbuyqty_;
}
inline void MDFuture::set_impliedbuyqty(::google::protobuf::int64 value) {
  
  impliedbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ImpliedBuyQty)
}

// optional int64 ImpliedSellPx = 29;
inline void MDFuture::clear_impliedsellpx() {
  impliedsellpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::impliedsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ImpliedSellPx)
  return impliedsellpx_;
}
inline void MDFuture::set_impliedsellpx(::google::protobuf::int64 value) {
  
  impliedsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ImpliedSellPx)
}

// optional int64 ImpliedSellQty = 30;
inline void MDFuture::clear_impliedsellqty() {
  impliedsellqty_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::impliedsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ImpliedSellQty)
  return impliedsellqty_;
}
inline void MDFuture::set_impliedsellqty(::google::protobuf::int64 value) {
  
  impliedsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ImpliedSellQty)
}

// optional int64 PositionTrend = 31;
inline void MDFuture::clear_positiontrend() {
  positiontrend_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::positiontrend() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PositionTrend)
  return positiontrend_;
}
inline void MDFuture::set_positiontrend(::google::protobuf::int64 value) {
  
  positiontrend_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PositionTrend)
}

// optional int64 ChangeSpeed = 32;
inline void MDFuture::clear_changespeed() {
  changespeed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::changespeed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ChangeSpeed)
  return changespeed_;
}
inline void MDFuture::set_changespeed(::google::protobuf::int64 value) {
  
  changespeed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ChangeSpeed)
}

// optional int64 ChangeRate = 33;
inline void MDFuture::clear_changerate() {
  changerate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::changerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ChangeRate)
  return changerate_;
}
inline void MDFuture::set_changerate(::google::protobuf::int64 value) {
  
  changerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ChangeRate)
}

// optional int64 ChangeValue = 34;
inline void MDFuture::clear_changevalue() {
  changevalue_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::changevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ChangeValue)
  return changevalue_;
}
inline void MDFuture::set_changevalue(::google::protobuf::int64 value) {
  
  changevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ChangeValue)
}

// optional int64 Swing = 35;
inline void MDFuture::clear_swing() {
  swing_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::swing() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.Swing)
  return swing_;
}
inline void MDFuture::set_swing(::google::protobuf::int64 value) {
  
  swing_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.Swing)
}

// optional string CommodityContractNumber = 36;
inline void MDFuture::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDFuture::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
}
inline void MDFuture::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
}
inline void MDFuture::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
}
inline ::std::string* MDFuture::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDFuture::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDFuture::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFuture.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
inline void MDFuture::clear_exchangedate() {
  exchangedate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ExchangeDate)
  return exchangedate_;
}
inline void MDFuture::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
inline void MDFuture::clear_exchangetime() {
  exchangetime_ = 0;
}
inline ::google::protobuf::int32 MDFuture::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ExchangeTime)
  return exchangetime_;
}
inline void MDFuture::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ExchangeTime)
}

// optional int32 ChannelNo = 50;
inline void MDFuture::clear_channelno() {
  channelno_ = 0;
}
inline ::google::protobuf::int32 MDFuture::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.ChannelNo)
  return channelno_;
}
inline void MDFuture::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
inline int MDFuture::buypricequeue_size() const {
  return buypricequeue_.size();
}
inline void MDFuture::clear_buypricequeue() {
  buypricequeue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
inline void MDFuture::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.BuyPriceQueue)
}
inline void MDFuture::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.BuyPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.BuyPriceQueue)
  return buypricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
inline int MDFuture::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
inline void MDFuture::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
inline void MDFuture::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.BuyOrderQtyQueue)
}
inline void MDFuture::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.BuyOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
inline int MDFuture::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
inline void MDFuture::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
inline void MDFuture::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.SellPriceQueue)
}
inline void MDFuture::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.SellPriceQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.SellPriceQueue)
  return sellpricequeue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
inline int MDFuture::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
inline void MDFuture::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
inline void MDFuture::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.SellOrderQtyQueue)
}
inline void MDFuture::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.SellOrderQtyQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
inline int MDFuture::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
inline void MDFuture::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
inline void MDFuture::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.BuyOrderQueue)
}
inline void MDFuture::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.BuyOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.BuyOrderQueue)
  return buyorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
inline int MDFuture::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
inline void MDFuture::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
inline void MDFuture::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.SellOrderQueue)
}
inline void MDFuture::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.SellOrderQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.SellOrderQueue)
  return sellorderqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
inline int MDFuture::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
inline void MDFuture::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
inline void MDFuture::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.BuyNumOrdersQueue)
}
inline void MDFuture::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.BuyNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
inline int MDFuture::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
inline void MDFuture::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
inline ::google::protobuf::int64 MDFuture::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
inline void MDFuture::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.SellNumOrdersQueue)
}
inline void MDFuture::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFuture.SellNumOrdersQueue)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFuture::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFuture.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFuture::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFuture.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
inline void MDFuture::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDFuture::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDFuture::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.DataMultiplePowerOf10)
}

// optional int64 BlockVolumeTrade = 60;
inline void MDFuture::clear_blockvolumetrade() {
  blockvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::blockvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.BlockVolumeTrade)
  return blockvolumetrade_;
}
inline void MDFuture::set_blockvolumetrade(::google::protobuf::int64 value) {
  
  blockvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.BlockVolumeTrade)
}

// optional int64 EligibleVolumeTrade = 61;
inline void MDFuture::clear_eligiblevolumetrade() {
  eligiblevolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::eligiblevolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.EligibleVolumeTrade)
  return eligiblevolumetrade_;
}
inline void MDFuture::set_eligiblevolumetrade(::google::protobuf::int64 value) {
  
  eligiblevolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.EligibleVolumeTrade)
}

// optional int64 StrategyVolumeTrade = 62;
inline void MDFuture::clear_strategyvolumetrade() {
  strategyvolumetrade_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDFuture::strategyvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.StrategyVolumeTrade)
  return strategyvolumetrade_;
}
inline void MDFuture::set_strategyvolumetrade(::google::protobuf::int64 value) {
  
  strategyvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.StrategyVolumeTrade)
}

// optional int32 PreOpenInterestDate = 63;
inline void MDFuture::clear_preopeninterestdate() {
  preopeninterestdate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::preopeninterestdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreOpenInterestDate)
  return preopeninterestdate_;
}
inline void MDFuture::set_preopeninterestdate(::google::protobuf::int32 value) {
  
  preopeninterestdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreOpenInterestDate)
}

// optional int32 PreSettleDate = 64;
inline void MDFuture::clear_presettledate() {
  presettledate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::presettledate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreSettleDate)
  return presettledate_;
}
inline void MDFuture::set_presettledate(::google::protobuf::int32 value) {
  
  presettledate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreSettleDate)
}

// optional int32 PreCloseDate = 65;
inline void MDFuture::clear_preclosedate() {
  preclosedate_ = 0;
}
inline ::google::protobuf::int32 MDFuture::preclosedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.PreCloseDate)
  return preclosedate_;
}
inline void MDFuture::set_preclosedate(::google::protobuf::int32 value) {
  
  preclosedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.PreCloseDate)
}

// optional int32 DelayType = 101;
inline void MDFuture::clear_delaytype() {
  delaytype_ = 0;
}
inline ::google::protobuf::int32 MDFuture::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFuture.DelayType)
  return delaytype_;
}
inline void MDFuture::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFuture.DelayType)
}

inline const MDFuture* MDFuture::internal_default_instance() {
  return &MDFuture_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDFuture_2eproto__INCLUDED
