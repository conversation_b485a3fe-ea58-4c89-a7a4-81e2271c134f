// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDTransaction.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDTransaction.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDTransaction_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDTransaction_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDTransaction_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDTransaction_2eproto() {
  protobuf_AddDesc_MDTransaction_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDTransaction.proto");
  GOOGLE_CHECK(file != NULL);
  MDTransaction_descriptor_ = file->message_type(0);
  static const int MDTransaction_offsets_[48] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradebuyno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradesellno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradebsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradeqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, trademoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, applseqnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradecleanprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, accruedinterestamt_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, tradedirtyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, maturityyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, fitradingmethod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, accruedinterestotd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, duration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, modifiedduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, convexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, settlperiod_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, settltype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, hktradetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, secondaryorderid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, bidexecinsttype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, marginprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, dealdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, dealtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, dealnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, marketindicator_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, repoterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legsettlementamount1st_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legsettlementamount2nd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, bondcode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, bondname_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, totalfacevalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legcleanprice1st_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legcleanprice2nd_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legyield1st_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, legyield2nd_),
  };
  MDTransaction_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDTransaction_descriptor_,
      MDTransaction::internal_default_instance(),
      MDTransaction_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDTransaction),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDTransaction, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDTransaction_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDTransaction_descriptor_, MDTransaction::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDTransaction_2eproto() {
  MDTransaction_default_instance_.Shutdown();
  delete MDTransaction_reflection_;
}

void protobuf_InitDefaults_MDTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDTransaction_default_instance_.DefaultConstruct();
  MDTransaction_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDTransaction_2eproto_once_);
void protobuf_InitDefaults_MDTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDTransaction_2eproto_once_,
                 &protobuf_InitDefaults_MDTransaction_2eproto_impl);
}
void protobuf_AddDesc_MDTransaction_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDTransaction_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\023MDTransaction.proto\022\032com.htsc.mdc.insi"
    "ght.model\032\027ESecurityIDSource.proto\032\023ESec"
    "urityType.proto\"\204\t\n\rMDTransaction\022\026\n\016HTS"
    "CSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDT"
    "ime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020secu"
    "rityIDSource\030\005 \001(\0162%.com.htsc.mdc.model."
    "ESecurityIDSource\0227\n\014securityType\030\006 \001(\0162"
    "!.com.htsc.mdc.model.ESecurityType\022\022\n\nTr"
    "adeIndex\030\007 \001(\003\022\022\n\nTradeBuyNo\030\010 \001(\003\022\023\n\013Tr"
    "adeSellNo\030\t \001(\003\022\021\n\tTradeType\030\n \001(\005\022\023\n\013Tr"
    "adeBSFlag\030\013 \001(\005\022\022\n\nTradePrice\030\014 \001(\003\022\020\n\010T"
    "radeQty\030\r \001(\003\022\022\n\nTradeMoney\030\016 \001(\003\022\022\n\nApp"
    "lSeqNum\030\017 \001(\003\022\021\n\tChannelNo\030\020 \001(\005\022\024\n\014Exch"
    "angeDate\030\021 \001(\005\022\024\n\014ExchangeTime\030\022 \001(\005\022\027\n\017"
    "TradeCleanPrice\030\023 \001(\003\022\032\n\022AccruedInterest"
    "Amt\030\024 \001(\003\022\027\n\017TradeDirtyPrice\030\025 \001(\003\022\025\n\rMa"
    "turityYield\030\026 \001(\003\022\027\n\017FITradingMethod\030\027 \001"
    "(\t\022\032\n\022AccruedInterestOtd\030\030 \001(\003\022\020\n\010Durati"
    "on\030\031 \001(\003\022\030\n\020ModifiedDuration\030\032 \001(\003\022\021\n\tCo"
    "nvexity\030\033 \001(\003\022\023\n\013SettlPeriod\030\034 \001(\005\022\021\n\tSe"
    "ttlType\030\035 \001(\005\022\023\n\013HKTradeType\030\036 \001(\005\022\035\n\025Da"
    "taMultiplePowerOf10\030\037 \001(\005\022\030\n\020SecondaryOr"
    "derID\030  \001(\t\022\027\n\017BidExecInstType\030! \001(\005\022\023\n\013"
    "MarginPrice\030\" \001(\003\022\020\n\010DealDate\030# \001(\t\022\020\n\010D"
    "ealTime\030$ \001(\t\022\022\n\nDealNumber\030% \001(\t\022\027\n\017Mar"
    "ketIndicator\030& \001(\005\022\020\n\010RepoTerm\030\' \001(\005\022\036\n\026"
    "LegSettlementAmount1st\030( \001(\003\022\036\n\026LegSettl"
    "ementAmount2nd\030) \001(\003\022\020\n\010BondCode\030* \001(\t\022\020"
    "\n\010BondName\030+ \001(\t\022\026\n\016TotalFacevalue\030, \001(\003"
    "\022\030\n\020LegCleanPrice1st\030- \001(\003\022\030\n\020LegCleanPr"
    "ice2nd\030. \001(\003\022\023\n\013LegYield1st\030/ \001(\003\022\023\n\013Leg"
    "Yield2nd\0300 \001(\003B6\n\032com.htsc.mdc.insight.m"
    "odelB\023MDTransactionProtosH\001\240\001\001b\006proto3", 1318);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDTransaction.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDTransaction_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDTransaction_2eproto_once_);
void protobuf_AddDesc_MDTransaction_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDTransaction_2eproto_once_,
                 &protobuf_AddDesc_MDTransaction_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDTransaction_2eproto {
  StaticDescriptorInitializer_MDTransaction_2eproto() {
    protobuf_AddDesc_MDTransaction_2eproto();
  }
} static_descriptor_initializer_MDTransaction_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDTransaction::kHTSCSecurityIDFieldNumber;
const int MDTransaction::kMDDateFieldNumber;
const int MDTransaction::kMDTimeFieldNumber;
const int MDTransaction::kDataTimestampFieldNumber;
const int MDTransaction::kSecurityIDSourceFieldNumber;
const int MDTransaction::kSecurityTypeFieldNumber;
const int MDTransaction::kTradeIndexFieldNumber;
const int MDTransaction::kTradeBuyNoFieldNumber;
const int MDTransaction::kTradeSellNoFieldNumber;
const int MDTransaction::kTradeTypeFieldNumber;
const int MDTransaction::kTradeBSFlagFieldNumber;
const int MDTransaction::kTradePriceFieldNumber;
const int MDTransaction::kTradeQtyFieldNumber;
const int MDTransaction::kTradeMoneyFieldNumber;
const int MDTransaction::kApplSeqNumFieldNumber;
const int MDTransaction::kChannelNoFieldNumber;
const int MDTransaction::kExchangeDateFieldNumber;
const int MDTransaction::kExchangeTimeFieldNumber;
const int MDTransaction::kTradeCleanPriceFieldNumber;
const int MDTransaction::kAccruedInterestAmtFieldNumber;
const int MDTransaction::kTradeDirtyPriceFieldNumber;
const int MDTransaction::kMaturityYieldFieldNumber;
const int MDTransaction::kFITradingMethodFieldNumber;
const int MDTransaction::kAccruedInterestOtdFieldNumber;
const int MDTransaction::kDurationFieldNumber;
const int MDTransaction::kModifiedDurationFieldNumber;
const int MDTransaction::kConvexityFieldNumber;
const int MDTransaction::kSettlPeriodFieldNumber;
const int MDTransaction::kSettlTypeFieldNumber;
const int MDTransaction::kHKTradeTypeFieldNumber;
const int MDTransaction::kDataMultiplePowerOf10FieldNumber;
const int MDTransaction::kSecondaryOrderIDFieldNumber;
const int MDTransaction::kBidExecInstTypeFieldNumber;
const int MDTransaction::kMarginPriceFieldNumber;
const int MDTransaction::kDealDateFieldNumber;
const int MDTransaction::kDealTimeFieldNumber;
const int MDTransaction::kDealNumberFieldNumber;
const int MDTransaction::kMarketIndicatorFieldNumber;
const int MDTransaction::kRepoTermFieldNumber;
const int MDTransaction::kLegSettlementAmount1StFieldNumber;
const int MDTransaction::kLegSettlementAmount2NdFieldNumber;
const int MDTransaction::kBondCodeFieldNumber;
const int MDTransaction::kBondNameFieldNumber;
const int MDTransaction::kTotalFacevalueFieldNumber;
const int MDTransaction::kLegCleanPrice1StFieldNumber;
const int MDTransaction::kLegCleanPrice2NdFieldNumber;
const int MDTransaction::kLegYield1StFieldNumber;
const int MDTransaction::kLegYield2NdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDTransaction::MDTransaction()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDTransaction_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDTransaction)
}

void MDTransaction::InitAsDefaultInstance() {
}

MDTransaction::MDTransaction(const MDTransaction& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDTransaction)
}

void MDTransaction::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fitradingmethod_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secondaryorderid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealdate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealnumber_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondcode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&legyield2nd_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(legyield2nd_));
  _cached_size_ = 0;
}

MDTransaction::~MDTransaction() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDTransaction)
  SharedDtor();
}

void MDTransaction::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  fitradingmethod_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  secondaryorderid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealdate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealnumber_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondcode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDTransaction::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDTransaction::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDTransaction_descriptor_;
}

const MDTransaction& MDTransaction::default_instance() {
  protobuf_InitDefaults_MDTransaction_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDTransaction> MDTransaction_default_instance_;

MDTransaction* MDTransaction::New(::google::protobuf::Arena* arena) const {
  MDTransaction* n = new MDTransaction;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDTransaction::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDTransaction)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDTransaction, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDTransaction*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, tradebuyno_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(tradesellno_, channelno_);
  ZR_(exchangedate_, exchangetime_);
  fitradingmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  accruedinterestotd_ = GOOGLE_LONGLONG(0);
  ZR_(duration_, datamultiplepowerof10_);
  settlperiod_ = 0;
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(bidexecinsttype_, legsettlementamount1st_);
  dealdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(legsettlementamount2nd_, legyield2nd_);
  bondcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  bondname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDTransaction::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDTransaction)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_TradeIndex;
        break;
      }

      // optional int64 TradeIndex = 7;
      case 7: {
        if (tag == 56) {
         parse_TradeIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_TradeBuyNo;
        break;
      }

      // optional int64 TradeBuyNo = 8;
      case 8: {
        if (tag == 64) {
         parse_TradeBuyNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradebuyno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_TradeSellNo;
        break;
      }

      // optional int64 TradeSellNo = 9;
      case 9: {
        if (tag == 72) {
         parse_TradeSellNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradesellno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_TradeType;
        break;
      }

      // optional int32 TradeType = 10;
      case 10: {
        if (tag == 80) {
         parse_TradeType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_TradeBSFlag;
        break;
      }

      // optional int32 TradeBSFlag = 11;
      case 11: {
        if (tag == 88) {
         parse_TradeBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradebsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TradePrice;
        break;
      }

      // optional int64 TradePrice = 12;
      case 12: {
        if (tag == 96) {
         parse_TradePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TradeQty;
        break;
      }

      // optional int64 TradeQty = 13;
      case 13: {
        if (tag == 104) {
         parse_TradeQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_TradeMoney;
        break;
      }

      // optional int64 TradeMoney = 14;
      case 14: {
        if (tag == 112) {
         parse_TradeMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &trademoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_ApplSeqNum;
        break;
      }

      // optional int64 ApplSeqNum = 15;
      case 15: {
        if (tag == 120) {
         parse_ApplSeqNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &applseqnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 16;
      case 16: {
        if (tag == 128) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 17;
      case 17: {
        if (tag == 136) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 18;
      case 18: {
        if (tag == 144) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_TradeCleanPrice;
        break;
      }

      // optional int64 TradeCleanPrice = 19;
      case 19: {
        if (tag == 152) {
         parse_TradeCleanPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradecleanprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AccruedInterestAmt;
        break;
      }

      // optional int64 AccruedInterestAmt = 20;
      case 20: {
        if (tag == 160) {
         parse_AccruedInterestAmt:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accruedinterestamt_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TradeDirtyPrice;
        break;
      }

      // optional int64 TradeDirtyPrice = 21;
      case 21: {
        if (tag == 168) {
         parse_TradeDirtyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradedirtyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_MaturityYield;
        break;
      }

      // optional int64 MaturityYield = 22;
      case 22: {
        if (tag == 176) {
         parse_MaturityYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maturityyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_FITradingMethod;
        break;
      }

      // optional string FITradingMethod = 23;
      case 23: {
        if (tag == 186) {
         parse_FITradingMethod:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fitradingmethod()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fitradingmethod().data(), this->fitradingmethod().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.FITradingMethod"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_AccruedInterestOtd;
        break;
      }

      // optional int64 AccruedInterestOtd = 24;
      case 24: {
        if (tag == 192) {
         parse_AccruedInterestOtd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &accruedinterestotd_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_Duration;
        break;
      }

      // optional int64 Duration = 25;
      case 25: {
        if (tag == 200) {
         parse_Duration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &duration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_ModifiedDuration;
        break;
      }

      // optional int64 ModifiedDuration = 26;
      case 26: {
        if (tag == 208) {
         parse_ModifiedDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &modifiedduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_Convexity;
        break;
      }

      // optional int64 Convexity = 27;
      case 27: {
        if (tag == 216) {
         parse_Convexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &convexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_SettlPeriod;
        break;
      }

      // optional int32 SettlPeriod = 28;
      case 28: {
        if (tag == 224) {
         parse_SettlPeriod:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settlperiod_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_SettlType;
        break;
      }

      // optional int32 SettlType = 29;
      case 29: {
        if (tag == 232) {
         parse_SettlType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &settltype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_HKTradeType;
        break;
      }

      // optional int32 HKTradeType = 30;
      case 30: {
        if (tag == 240) {
         parse_HKTradeType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &hktradetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 31;
      case 31: {
        if (tag == 248) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_SecondaryOrderID;
        break;
      }

      // optional string SecondaryOrderID = 32;
      case 32: {
        if (tag == 258) {
         parse_SecondaryOrderID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_secondaryorderid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->secondaryorderid().data(), this->secondaryorderid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_BidExecInstType;
        break;
      }

      // optional int32 BidExecInstType = 33;
      case 33: {
        if (tag == 264) {
         parse_BidExecInstType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &bidexecinsttype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_MarginPrice;
        break;
      }

      // optional int64 MarginPrice = 34;
      case 34: {
        if (tag == 272) {
         parse_MarginPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &marginprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_DealDate;
        break;
      }

      // optional string DealDate = 35;
      case 35: {
        if (tag == 282) {
         parse_DealDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dealdate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dealdate().data(), this->dealdate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.DealDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_DealTime;
        break;
      }

      // optional string DealTime = 36;
      case 36: {
        if (tag == 290) {
         parse_DealTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dealtime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dealtime().data(), this->dealtime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.DealTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_DealNumber;
        break;
      }

      // optional string DealNumber = 37;
      case 37: {
        if (tag == 298) {
         parse_DealNumber:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dealnumber()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dealnumber().data(), this->dealnumber().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.DealNumber"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_MarketIndicator;
        break;
      }

      // optional int32 MarketIndicator = 38;
      case 38: {
        if (tag == 304) {
         parse_MarketIndicator:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &marketindicator_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_RepoTerm;
        break;
      }

      // optional int32 RepoTerm = 39;
      case 39: {
        if (tag == 312) {
         parse_RepoTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &repoterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_LegSettlementAmount1st;
        break;
      }

      // optional int64 LegSettlementAmount1st = 40;
      case 40: {
        if (tag == 320) {
         parse_LegSettlementAmount1st:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legsettlementamount1st_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_LegSettlementAmount2nd;
        break;
      }

      // optional int64 LegSettlementAmount2nd = 41;
      case 41: {
        if (tag == 328) {
         parse_LegSettlementAmount2nd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legsettlementamount2nd_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(338)) goto parse_BondCode;
        break;
      }

      // optional string BondCode = 42;
      case 42: {
        if (tag == 338) {
         parse_BondCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bondcode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bondcode().data(), this->bondcode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.BondCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_BondName;
        break;
      }

      // optional string BondName = 43;
      case 43: {
        if (tag == 346) {
         parse_BondName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_bondname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->bondname().data(), this->bondname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDTransaction.BondName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_TotalFacevalue;
        break;
      }

      // optional int64 TotalFacevalue = 44;
      case 44: {
        if (tag == 352) {
         parse_TotalFacevalue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalfacevalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_LegCleanPrice1st;
        break;
      }

      // optional int64 LegCleanPrice1st = 45;
      case 45: {
        if (tag == 360) {
         parse_LegCleanPrice1st:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legcleanprice1st_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_LegCleanPrice2nd;
        break;
      }

      // optional int64 LegCleanPrice2nd = 46;
      case 46: {
        if (tag == 368) {
         parse_LegCleanPrice2nd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legcleanprice2nd_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(376)) goto parse_LegYield1st;
        break;
      }

      // optional int64 LegYield1st = 47;
      case 47: {
        if (tag == 376) {
         parse_LegYield1st:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legyield1st_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(384)) goto parse_LegYield2nd;
        break;
      }

      // optional int64 LegYield2nd = 48;
      case 48: {
        if (tag == 384) {
         parse_LegYield2nd:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &legyield2nd_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDTransaction)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDTransaction)
  return false;
#undef DO_
}

void MDTransaction::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int64 TradeIndex = 7;
  if (this->tradeindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->tradeindex(), output);
  }

  // optional int64 TradeBuyNo = 8;
  if (this->tradebuyno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->tradebuyno(), output);
  }

  // optional int64 TradeSellNo = 9;
  if (this->tradesellno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->tradesellno(), output);
  }

  // optional int32 TradeType = 10;
  if (this->tradetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->tradetype(), output);
  }

  // optional int32 TradeBSFlag = 11;
  if (this->tradebsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->tradebsflag(), output);
  }

  // optional int64 TradePrice = 12;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->tradeprice(), output);
  }

  // optional int64 TradeQty = 13;
  if (this->tradeqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->tradeqty(), output);
  }

  // optional int64 TradeMoney = 14;
  if (this->trademoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->trademoney(), output);
  }

  // optional int64 ApplSeqNum = 15;
  if (this->applseqnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->applseqnum(), output);
  }

  // optional int32 ChannelNo = 16;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->channelno(), output);
  }

  // optional int32 ExchangeDate = 17;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 18;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->exchangetime(), output);
  }

  // optional int64 TradeCleanPrice = 19;
  if (this->tradecleanprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->tradecleanprice(), output);
  }

  // optional int64 AccruedInterestAmt = 20;
  if (this->accruedinterestamt() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->accruedinterestamt(), output);
  }

  // optional int64 TradeDirtyPrice = 21;
  if (this->tradedirtyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->tradedirtyprice(), output);
  }

  // optional int64 MaturityYield = 22;
  if (this->maturityyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->maturityyield(), output);
  }

  // optional string FITradingMethod = 23;
  if (this->fitradingmethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fitradingmethod().data(), this->fitradingmethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.FITradingMethod");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->fitradingmethod(), output);
  }

  // optional int64 AccruedInterestOtd = 24;
  if (this->accruedinterestotd() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->accruedinterestotd(), output);
  }

  // optional int64 Duration = 25;
  if (this->duration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->duration(), output);
  }

  // optional int64 ModifiedDuration = 26;
  if (this->modifiedduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->modifiedduration(), output);
  }

  // optional int64 Convexity = 27;
  if (this->convexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->convexity(), output);
  }

  // optional int32 SettlPeriod = 28;
  if (this->settlperiod() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(28, this->settlperiod(), output);
  }

  // optional int32 SettlType = 29;
  if (this->settltype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(29, this->settltype(), output);
  }

  // optional int32 HKTradeType = 30;
  if (this->hktradetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->hktradetype(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 31;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(31, this->datamultiplepowerof10(), output);
  }

  // optional string SecondaryOrderID = 32;
  if (this->secondaryorderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secondaryorderid().data(), this->secondaryorderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      32, this->secondaryorderid(), output);
  }

  // optional int32 BidExecInstType = 33;
  if (this->bidexecinsttype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(33, this->bidexecinsttype(), output);
  }

  // optional int64 MarginPrice = 34;
  if (this->marginprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->marginprice(), output);
  }

  // optional string DealDate = 35;
  if (this->dealdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealdate().data(), this->dealdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      35, this->dealdate(), output);
  }

  // optional string DealTime = 36;
  if (this->dealtime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealtime().data(), this->dealtime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      36, this->dealtime(), output);
  }

  // optional string DealNumber = 37;
  if (this->dealnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealnumber().data(), this->dealnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealNumber");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      37, this->dealnumber(), output);
  }

  // optional int32 MarketIndicator = 38;
  if (this->marketindicator() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(38, this->marketindicator(), output);
  }

  // optional int32 RepoTerm = 39;
  if (this->repoterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(39, this->repoterm(), output);
  }

  // optional int64 LegSettlementAmount1st = 40;
  if (this->legsettlementamount1st() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(40, this->legsettlementamount1st(), output);
  }

  // optional int64 LegSettlementAmount2nd = 41;
  if (this->legsettlementamount2nd() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(41, this->legsettlementamount2nd(), output);
  }

  // optional string BondCode = 42;
  if (this->bondcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bondcode().data(), this->bondcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.BondCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      42, this->bondcode(), output);
  }

  // optional string BondName = 43;
  if (this->bondname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bondname().data(), this->bondname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.BondName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      43, this->bondname(), output);
  }

  // optional int64 TotalFacevalue = 44;
  if (this->totalfacevalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(44, this->totalfacevalue(), output);
  }

  // optional int64 LegCleanPrice1st = 45;
  if (this->legcleanprice1st() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(45, this->legcleanprice1st(), output);
  }

  // optional int64 LegCleanPrice2nd = 46;
  if (this->legcleanprice2nd() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(46, this->legcleanprice2nd(), output);
  }

  // optional int64 LegYield1st = 47;
  if (this->legyield1st() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(47, this->legyield1st(), output);
  }

  // optional int64 LegYield2nd = 48;
  if (this->legyield2nd() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(48, this->legyield2nd(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDTransaction)
}

::google::protobuf::uint8* MDTransaction::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDTransaction)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int64 TradeIndex = 7;
  if (this->tradeindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->tradeindex(), target);
  }

  // optional int64 TradeBuyNo = 8;
  if (this->tradebuyno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->tradebuyno(), target);
  }

  // optional int64 TradeSellNo = 9;
  if (this->tradesellno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->tradesellno(), target);
  }

  // optional int32 TradeType = 10;
  if (this->tradetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->tradetype(), target);
  }

  // optional int32 TradeBSFlag = 11;
  if (this->tradebsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->tradebsflag(), target);
  }

  // optional int64 TradePrice = 12;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->tradeprice(), target);
  }

  // optional int64 TradeQty = 13;
  if (this->tradeqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->tradeqty(), target);
  }

  // optional int64 TradeMoney = 14;
  if (this->trademoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->trademoney(), target);
  }

  // optional int64 ApplSeqNum = 15;
  if (this->applseqnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->applseqnum(), target);
  }

  // optional int32 ChannelNo = 16;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->channelno(), target);
  }

  // optional int32 ExchangeDate = 17;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 18;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->exchangetime(), target);
  }

  // optional int64 TradeCleanPrice = 19;
  if (this->tradecleanprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->tradecleanprice(), target);
  }

  // optional int64 AccruedInterestAmt = 20;
  if (this->accruedinterestamt() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->accruedinterestamt(), target);
  }

  // optional int64 TradeDirtyPrice = 21;
  if (this->tradedirtyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->tradedirtyprice(), target);
  }

  // optional int64 MaturityYield = 22;
  if (this->maturityyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->maturityyield(), target);
  }

  // optional string FITradingMethod = 23;
  if (this->fitradingmethod().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fitradingmethod().data(), this->fitradingmethod().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.FITradingMethod");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->fitradingmethod(), target);
  }

  // optional int64 AccruedInterestOtd = 24;
  if (this->accruedinterestotd() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->accruedinterestotd(), target);
  }

  // optional int64 Duration = 25;
  if (this->duration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->duration(), target);
  }

  // optional int64 ModifiedDuration = 26;
  if (this->modifiedduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->modifiedduration(), target);
  }

  // optional int64 Convexity = 27;
  if (this->convexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->convexity(), target);
  }

  // optional int32 SettlPeriod = 28;
  if (this->settlperiod() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(28, this->settlperiod(), target);
  }

  // optional int32 SettlType = 29;
  if (this->settltype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(29, this->settltype(), target);
  }

  // optional int32 HKTradeType = 30;
  if (this->hktradetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->hktradetype(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 31;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(31, this->datamultiplepowerof10(), target);
  }

  // optional string SecondaryOrderID = 32;
  if (this->secondaryorderid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->secondaryorderid().data(), this->secondaryorderid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        32, this->secondaryorderid(), target);
  }

  // optional int32 BidExecInstType = 33;
  if (this->bidexecinsttype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(33, this->bidexecinsttype(), target);
  }

  // optional int64 MarginPrice = 34;
  if (this->marginprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->marginprice(), target);
  }

  // optional string DealDate = 35;
  if (this->dealdate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealdate().data(), this->dealdate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        35, this->dealdate(), target);
  }

  // optional string DealTime = 36;
  if (this->dealtime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealtime().data(), this->dealtime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        36, this->dealtime(), target);
  }

  // optional string DealNumber = 37;
  if (this->dealnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealnumber().data(), this->dealnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.DealNumber");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        37, this->dealnumber(), target);
  }

  // optional int32 MarketIndicator = 38;
  if (this->marketindicator() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(38, this->marketindicator(), target);
  }

  // optional int32 RepoTerm = 39;
  if (this->repoterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(39, this->repoterm(), target);
  }

  // optional int64 LegSettlementAmount1st = 40;
  if (this->legsettlementamount1st() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(40, this->legsettlementamount1st(), target);
  }

  // optional int64 LegSettlementAmount2nd = 41;
  if (this->legsettlementamount2nd() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(41, this->legsettlementamount2nd(), target);
  }

  // optional string BondCode = 42;
  if (this->bondcode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bondcode().data(), this->bondcode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.BondCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        42, this->bondcode(), target);
  }

  // optional string BondName = 43;
  if (this->bondname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->bondname().data(), this->bondname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDTransaction.BondName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        43, this->bondname(), target);
  }

  // optional int64 TotalFacevalue = 44;
  if (this->totalfacevalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(44, this->totalfacevalue(), target);
  }

  // optional int64 LegCleanPrice1st = 45;
  if (this->legcleanprice1st() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(45, this->legcleanprice1st(), target);
  }

  // optional int64 LegCleanPrice2nd = 46;
  if (this->legcleanprice2nd() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(46, this->legcleanprice2nd(), target);
  }

  // optional int64 LegYield1st = 47;
  if (this->legyield1st() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(47, this->legyield1st(), target);
  }

  // optional int64 LegYield2nd = 48;
  if (this->legyield2nd() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(48, this->legyield2nd(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDTransaction)
  return target;
}

size_t MDTransaction::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDTransaction)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 TradeIndex = 7;
  if (this->tradeindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeindex());
  }

  // optional int64 TradeBuyNo = 8;
  if (this->tradebuyno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradebuyno());
  }

  // optional int64 TradeSellNo = 9;
  if (this->tradesellno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradesellno());
  }

  // optional int32 TradeType = 10;
  if (this->tradetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradetype());
  }

  // optional int32 TradeBSFlag = 11;
  if (this->tradebsflag() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradebsflag());
  }

  // optional int64 TradePrice = 12;
  if (this->tradeprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeprice());
  }

  // optional int64 TradeQty = 13;
  if (this->tradeqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeqty());
  }

  // optional int64 TradeMoney = 14;
  if (this->trademoney() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->trademoney());
  }

  // optional int64 ApplSeqNum = 15;
  if (this->applseqnum() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->applseqnum());
  }

  // optional int32 ChannelNo = 16;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int32 ExchangeDate = 17;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 18;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int64 TradeCleanPrice = 19;
  if (this->tradecleanprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradecleanprice());
  }

  // optional int64 AccruedInterestAmt = 20;
  if (this->accruedinterestamt() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accruedinterestamt());
  }

  // optional int64 TradeDirtyPrice = 21;
  if (this->tradedirtyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradedirtyprice());
  }

  // optional int64 MaturityYield = 22;
  if (this->maturityyield() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maturityyield());
  }

  // optional string FITradingMethod = 23;
  if (this->fitradingmethod().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fitradingmethod());
  }

  // optional int64 AccruedInterestOtd = 24;
  if (this->accruedinterestotd() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->accruedinterestotd());
  }

  // optional int64 Duration = 25;
  if (this->duration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->duration());
  }

  // optional int64 ModifiedDuration = 26;
  if (this->modifiedduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->modifiedduration());
  }

  // optional int64 Convexity = 27;
  if (this->convexity() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->convexity());
  }

  // optional int32 SettlPeriod = 28;
  if (this->settlperiod() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settlperiod());
  }

  // optional int32 SettlType = 29;
  if (this->settltype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->settltype());
  }

  // optional int32 HKTradeType = 30;
  if (this->hktradetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->hktradetype());
  }

  // optional int32 DataMultiplePowerOf10 = 31;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string SecondaryOrderID = 32;
  if (this->secondaryorderid().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->secondaryorderid());
  }

  // optional int32 BidExecInstType = 33;
  if (this->bidexecinsttype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->bidexecinsttype());
  }

  // optional int64 MarginPrice = 34;
  if (this->marginprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->marginprice());
  }

  // optional string DealDate = 35;
  if (this->dealdate().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dealdate());
  }

  // optional string DealTime = 36;
  if (this->dealtime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dealtime());
  }

  // optional string DealNumber = 37;
  if (this->dealnumber().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dealnumber());
  }

  // optional int32 MarketIndicator = 38;
  if (this->marketindicator() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->marketindicator());
  }

  // optional int32 RepoTerm = 39;
  if (this->repoterm() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->repoterm());
  }

  // optional int64 LegSettlementAmount1st = 40;
  if (this->legsettlementamount1st() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legsettlementamount1st());
  }

  // optional int64 LegSettlementAmount2nd = 41;
  if (this->legsettlementamount2nd() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legsettlementamount2nd());
  }

  // optional string BondCode = 42;
  if (this->bondcode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bondcode());
  }

  // optional string BondName = 43;
  if (this->bondname().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->bondname());
  }

  // optional int64 TotalFacevalue = 44;
  if (this->totalfacevalue() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalfacevalue());
  }

  // optional int64 LegCleanPrice1st = 45;
  if (this->legcleanprice1st() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legcleanprice1st());
  }

  // optional int64 LegCleanPrice2nd = 46;
  if (this->legcleanprice2nd() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legcleanprice2nd());
  }

  // optional int64 LegYield1st = 47;
  if (this->legyield1st() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legyield1st());
  }

  // optional int64 LegYield2nd = 48;
  if (this->legyield2nd() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->legyield2nd());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDTransaction::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDTransaction)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDTransaction* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDTransaction>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDTransaction)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDTransaction)
    UnsafeMergeFrom(*source);
  }
}

void MDTransaction::MergeFrom(const MDTransaction& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDTransaction)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDTransaction::UnsafeMergeFrom(const MDTransaction& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.tradeindex() != 0) {
    set_tradeindex(from.tradeindex());
  }
  if (from.tradebuyno() != 0) {
    set_tradebuyno(from.tradebuyno());
  }
  if (from.tradesellno() != 0) {
    set_tradesellno(from.tradesellno());
  }
  if (from.tradetype() != 0) {
    set_tradetype(from.tradetype());
  }
  if (from.tradebsflag() != 0) {
    set_tradebsflag(from.tradebsflag());
  }
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.tradeqty() != 0) {
    set_tradeqty(from.tradeqty());
  }
  if (from.trademoney() != 0) {
    set_trademoney(from.trademoney());
  }
  if (from.applseqnum() != 0) {
    set_applseqnum(from.applseqnum());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.tradecleanprice() != 0) {
    set_tradecleanprice(from.tradecleanprice());
  }
  if (from.accruedinterestamt() != 0) {
    set_accruedinterestamt(from.accruedinterestamt());
  }
  if (from.tradedirtyprice() != 0) {
    set_tradedirtyprice(from.tradedirtyprice());
  }
  if (from.maturityyield() != 0) {
    set_maturityyield(from.maturityyield());
  }
  if (from.fitradingmethod().size() > 0) {

    fitradingmethod_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fitradingmethod_);
  }
  if (from.accruedinterestotd() != 0) {
    set_accruedinterestotd(from.accruedinterestotd());
  }
  if (from.duration() != 0) {
    set_duration(from.duration());
  }
  if (from.modifiedduration() != 0) {
    set_modifiedduration(from.modifiedduration());
  }
  if (from.convexity() != 0) {
    set_convexity(from.convexity());
  }
  if (from.settlperiod() != 0) {
    set_settlperiod(from.settlperiod());
  }
  if (from.settltype() != 0) {
    set_settltype(from.settltype());
  }
  if (from.hktradetype() != 0) {
    set_hktradetype(from.hktradetype());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.secondaryorderid().size() > 0) {

    secondaryorderid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.secondaryorderid_);
  }
  if (from.bidexecinsttype() != 0) {
    set_bidexecinsttype(from.bidexecinsttype());
  }
  if (from.marginprice() != 0) {
    set_marginprice(from.marginprice());
  }
  if (from.dealdate().size() > 0) {

    dealdate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dealdate_);
  }
  if (from.dealtime().size() > 0) {

    dealtime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dealtime_);
  }
  if (from.dealnumber().size() > 0) {

    dealnumber_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dealnumber_);
  }
  if (from.marketindicator() != 0) {
    set_marketindicator(from.marketindicator());
  }
  if (from.repoterm() != 0) {
    set_repoterm(from.repoterm());
  }
  if (from.legsettlementamount1st() != 0) {
    set_legsettlementamount1st(from.legsettlementamount1st());
  }
  if (from.legsettlementamount2nd() != 0) {
    set_legsettlementamount2nd(from.legsettlementamount2nd());
  }
  if (from.bondcode().size() > 0) {

    bondcode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bondcode_);
  }
  if (from.bondname().size() > 0) {

    bondname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.bondname_);
  }
  if (from.totalfacevalue() != 0) {
    set_totalfacevalue(from.totalfacevalue());
  }
  if (from.legcleanprice1st() != 0) {
    set_legcleanprice1st(from.legcleanprice1st());
  }
  if (from.legcleanprice2nd() != 0) {
    set_legcleanprice2nd(from.legcleanprice2nd());
  }
  if (from.legyield1st() != 0) {
    set_legyield1st(from.legyield1st());
  }
  if (from.legyield2nd() != 0) {
    set_legyield2nd(from.legyield2nd());
  }
}

void MDTransaction::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDTransaction)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDTransaction::CopyFrom(const MDTransaction& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDTransaction)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDTransaction::IsInitialized() const {

  return true;
}

void MDTransaction::Swap(MDTransaction* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDTransaction::InternalSwap(MDTransaction* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(tradeindex_, other->tradeindex_);
  std::swap(tradebuyno_, other->tradebuyno_);
  std::swap(tradesellno_, other->tradesellno_);
  std::swap(tradetype_, other->tradetype_);
  std::swap(tradebsflag_, other->tradebsflag_);
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(tradeqty_, other->tradeqty_);
  std::swap(trademoney_, other->trademoney_);
  std::swap(applseqnum_, other->applseqnum_);
  std::swap(channelno_, other->channelno_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(tradecleanprice_, other->tradecleanprice_);
  std::swap(accruedinterestamt_, other->accruedinterestamt_);
  std::swap(tradedirtyprice_, other->tradedirtyprice_);
  std::swap(maturityyield_, other->maturityyield_);
  fitradingmethod_.Swap(&other->fitradingmethod_);
  std::swap(accruedinterestotd_, other->accruedinterestotd_);
  std::swap(duration_, other->duration_);
  std::swap(modifiedduration_, other->modifiedduration_);
  std::swap(convexity_, other->convexity_);
  std::swap(settlperiod_, other->settlperiod_);
  std::swap(settltype_, other->settltype_);
  std::swap(hktradetype_, other->hktradetype_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  secondaryorderid_.Swap(&other->secondaryorderid_);
  std::swap(bidexecinsttype_, other->bidexecinsttype_);
  std::swap(marginprice_, other->marginprice_);
  dealdate_.Swap(&other->dealdate_);
  dealtime_.Swap(&other->dealtime_);
  dealnumber_.Swap(&other->dealnumber_);
  std::swap(marketindicator_, other->marketindicator_);
  std::swap(repoterm_, other->repoterm_);
  std::swap(legsettlementamount1st_, other->legsettlementamount1st_);
  std::swap(legsettlementamount2nd_, other->legsettlementamount2nd_);
  bondcode_.Swap(&other->bondcode_);
  bondname_.Swap(&other->bondname_);
  std::swap(totalfacevalue_, other->totalfacevalue_);
  std::swap(legcleanprice1st_, other->legcleanprice1st_);
  std::swap(legcleanprice2nd_, other->legcleanprice2nd_);
  std::swap(legyield1st_, other->legyield1st_);
  std::swap(legyield2nd_, other->legyield2nd_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDTransaction::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDTransaction_descriptor_;
  metadata.reflection = MDTransaction_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDTransaction

// optional string HTSCSecurityID = 1;
void MDTransaction::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
void MDTransaction::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
void MDTransaction::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}
::std::string* MDTransaction::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDTransaction::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDTransaction::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MDDate)
  return mddate_;
}
void MDTransaction::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MDDate)
}

// optional int32 MDTime = 3;
void MDTransaction::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDTransaction::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MDTime)
  return mdtime_;
}
void MDTransaction::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDTransaction::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DataTimestamp)
  return datatimestamp_;
}
void MDTransaction::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDTransaction::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDTransaction::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDTransaction::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDTransaction::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDTransaction::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDTransaction::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.securityType)
}

// optional int64 TradeIndex = 7;
void MDTransaction::clear_tradeindex() {
  tradeindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeIndex)
  return tradeindex_;
}
void MDTransaction::set_tradeindex(::google::protobuf::int64 value) {
  
  tradeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeIndex)
}

// optional int64 TradeBuyNo = 8;
void MDTransaction::clear_tradebuyno() {
  tradebuyno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradebuyno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeBuyNo)
  return tradebuyno_;
}
void MDTransaction::set_tradebuyno(::google::protobuf::int64 value) {
  
  tradebuyno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeBuyNo)
}

// optional int64 TradeSellNo = 9;
void MDTransaction::clear_tradesellno() {
  tradesellno_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradesellno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeSellNo)
  return tradesellno_;
}
void MDTransaction::set_tradesellno(::google::protobuf::int64 value) {
  
  tradesellno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeSellNo)
}

// optional int32 TradeType = 10;
void MDTransaction::clear_tradetype() {
  tradetype_ = 0;
}
::google::protobuf::int32 MDTransaction::tradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeType)
  return tradetype_;
}
void MDTransaction::set_tradetype(::google::protobuf::int32 value) {
  
  tradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeType)
}

// optional int32 TradeBSFlag = 11;
void MDTransaction::clear_tradebsflag() {
  tradebsflag_ = 0;
}
::google::protobuf::int32 MDTransaction::tradebsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeBSFlag)
  return tradebsflag_;
}
void MDTransaction::set_tradebsflag(::google::protobuf::int32 value) {
  
  tradebsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeBSFlag)
}

// optional int64 TradePrice = 12;
void MDTransaction::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradePrice)
  return tradeprice_;
}
void MDTransaction::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradePrice)
}

// optional int64 TradeQty = 13;
void MDTransaction::clear_tradeqty() {
  tradeqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradeqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeQty)
  return tradeqty_;
}
void MDTransaction::set_tradeqty(::google::protobuf::int64 value) {
  
  tradeqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeQty)
}

// optional int64 TradeMoney = 14;
void MDTransaction::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeMoney)
  return trademoney_;
}
void MDTransaction::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeMoney)
}

// optional int64 ApplSeqNum = 15;
void MDTransaction::clear_applseqnum() {
  applseqnum_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::applseqnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ApplSeqNum)
  return applseqnum_;
}
void MDTransaction::set_applseqnum(::google::protobuf::int64 value) {
  
  applseqnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ApplSeqNum)
}

// optional int32 ChannelNo = 16;
void MDTransaction::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDTransaction::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ChannelNo)
  return channelno_;
}
void MDTransaction::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ChannelNo)
}

// optional int32 ExchangeDate = 17;
void MDTransaction::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDTransaction::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ExchangeDate)
  return exchangedate_;
}
void MDTransaction::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ExchangeDate)
}

// optional int32 ExchangeTime = 18;
void MDTransaction::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDTransaction::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ExchangeTime)
  return exchangetime_;
}
void MDTransaction::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ExchangeTime)
}

// optional int64 TradeCleanPrice = 19;
void MDTransaction::clear_tradecleanprice() {
  tradecleanprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradecleanprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeCleanPrice)
  return tradecleanprice_;
}
void MDTransaction::set_tradecleanprice(::google::protobuf::int64 value) {
  
  tradecleanprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeCleanPrice)
}

// optional int64 AccruedInterestAmt = 20;
void MDTransaction::clear_accruedinterestamt() {
  accruedinterestamt_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::accruedinterestamt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestAmt)
  return accruedinterestamt_;
}
void MDTransaction::set_accruedinterestamt(::google::protobuf::int64 value) {
  
  accruedinterestamt_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestAmt)
}

// optional int64 TradeDirtyPrice = 21;
void MDTransaction::clear_tradedirtyprice() {
  tradedirtyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::tradedirtyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TradeDirtyPrice)
  return tradedirtyprice_;
}
void MDTransaction::set_tradedirtyprice(::google::protobuf::int64 value) {
  
  tradedirtyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TradeDirtyPrice)
}

// optional int64 MaturityYield = 22;
void MDTransaction::clear_maturityyield() {
  maturityyield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::maturityyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MaturityYield)
  return maturityyield_;
}
void MDTransaction::set_maturityyield(::google::protobuf::int64 value) {
  
  maturityyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MaturityYield)
}

// optional string FITradingMethod = 23;
void MDTransaction::clear_fitradingmethod() {
  fitradingmethod_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::fitradingmethod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  return fitradingmethod_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_fitradingmethod(const ::std::string& value) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
void MDTransaction::set_fitradingmethod(const char* value) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
void MDTransaction::set_fitradingmethod(const char* value, size_t size) {
  
  fitradingmethod_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}
::std::string* MDTransaction::mutable_fitradingmethod() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  return fitradingmethod_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_fitradingmethod() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
  
  return fitradingmethod_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_fitradingmethod(::std::string* fitradingmethod) {
  if (fitradingmethod != NULL) {
    
  } else {
    
  }
  fitradingmethod_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fitradingmethod);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.FITradingMethod)
}

// optional int64 AccruedInterestOtd = 24;
void MDTransaction::clear_accruedinterestotd() {
  accruedinterestotd_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::accruedinterestotd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestOtd)
  return accruedinterestotd_;
}
void MDTransaction::set_accruedinterestotd(::google::protobuf::int64 value) {
  
  accruedinterestotd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.AccruedInterestOtd)
}

// optional int64 Duration = 25;
void MDTransaction::clear_duration() {
  duration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::duration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.Duration)
  return duration_;
}
void MDTransaction::set_duration(::google::protobuf::int64 value) {
  
  duration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.Duration)
}

// optional int64 ModifiedDuration = 26;
void MDTransaction::clear_modifiedduration() {
  modifiedduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::modifiedduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.ModifiedDuration)
  return modifiedduration_;
}
void MDTransaction::set_modifiedduration(::google::protobuf::int64 value) {
  
  modifiedduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.ModifiedDuration)
}

// optional int64 Convexity = 27;
void MDTransaction::clear_convexity() {
  convexity_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::convexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.Convexity)
  return convexity_;
}
void MDTransaction::set_convexity(::google::protobuf::int64 value) {
  
  convexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.Convexity)
}

// optional int32 SettlPeriod = 28;
void MDTransaction::clear_settlperiod() {
  settlperiod_ = 0;
}
::google::protobuf::int32 MDTransaction::settlperiod() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SettlPeriod)
  return settlperiod_;
}
void MDTransaction::set_settlperiod(::google::protobuf::int32 value) {
  
  settlperiod_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SettlPeriod)
}

// optional int32 SettlType = 29;
void MDTransaction::clear_settltype() {
  settltype_ = 0;
}
::google::protobuf::int32 MDTransaction::settltype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SettlType)
  return settltype_;
}
void MDTransaction::set_settltype(::google::protobuf::int32 value) {
  
  settltype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SettlType)
}

// optional int32 HKTradeType = 30;
void MDTransaction::clear_hktradetype() {
  hktradetype_ = 0;
}
::google::protobuf::int32 MDTransaction::hktradetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.HKTradeType)
  return hktradetype_;
}
void MDTransaction::set_hktradetype(::google::protobuf::int32 value) {
  
  hktradetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.HKTradeType)
}

// optional int32 DataMultiplePowerOf10 = 31;
void MDTransaction::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDTransaction::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDTransaction::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DataMultiplePowerOf10)
}

// optional string SecondaryOrderID = 32;
void MDTransaction::clear_secondaryorderid() {
  secondaryorderid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::secondaryorderid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  return secondaryorderid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_secondaryorderid(const ::std::string& value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
void MDTransaction::set_secondaryorderid(const char* value) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
void MDTransaction::set_secondaryorderid(const char* value, size_t size) {
  
  secondaryorderid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}
::std::string* MDTransaction::mutable_secondaryorderid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  return secondaryorderid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_secondaryorderid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
  
  return secondaryorderid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_secondaryorderid(::std::string* secondaryorderid) {
  if (secondaryorderid != NULL) {
    
  } else {
    
  }
  secondaryorderid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), secondaryorderid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.SecondaryOrderID)
}

// optional int32 BidExecInstType = 33;
void MDTransaction::clear_bidexecinsttype() {
  bidexecinsttype_ = 0;
}
::google::protobuf::int32 MDTransaction::bidexecinsttype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BidExecInstType)
  return bidexecinsttype_;
}
void MDTransaction::set_bidexecinsttype(::google::protobuf::int32 value) {
  
  bidexecinsttype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BidExecInstType)
}

// optional int64 MarginPrice = 34;
void MDTransaction::clear_marginprice() {
  marginprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::marginprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MarginPrice)
  return marginprice_;
}
void MDTransaction::set_marginprice(::google::protobuf::int64 value) {
  
  marginprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MarginPrice)
}

// optional string DealDate = 35;
void MDTransaction::clear_dealdate() {
  dealdate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::dealdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  return dealdate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_dealdate(const ::std::string& value) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
void MDTransaction::set_dealdate(const char* value) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
void MDTransaction::set_dealdate(const char* value, size_t size) {
  
  dealdate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}
::std::string* MDTransaction::mutable_dealdate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  return dealdate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_dealdate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealDate)
  
  return dealdate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_dealdate(::std::string* dealdate) {
  if (dealdate != NULL) {
    
  } else {
    
  }
  dealdate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealdate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealDate)
}

// optional string DealTime = 36;
void MDTransaction::clear_dealtime() {
  dealtime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::dealtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  return dealtime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_dealtime(const ::std::string& value) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
void MDTransaction::set_dealtime(const char* value) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
void MDTransaction::set_dealtime(const char* value, size_t size) {
  
  dealtime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}
::std::string* MDTransaction::mutable_dealtime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  return dealtime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_dealtime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealTime)
  
  return dealtime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_dealtime(::std::string* dealtime) {
  if (dealtime != NULL) {
    
  } else {
    
  }
  dealtime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealtime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealTime)
}

// optional string DealNumber = 37;
void MDTransaction::clear_dealnumber() {
  dealnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::dealnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  return dealnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_dealnumber(const ::std::string& value) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
void MDTransaction::set_dealnumber(const char* value) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
void MDTransaction::set_dealnumber(const char* value, size_t size) {
  
  dealnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}
::std::string* MDTransaction::mutable_dealnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  return dealnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_dealnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
  
  return dealnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_dealnumber(::std::string* dealnumber) {
  if (dealnumber != NULL) {
    
  } else {
    
  }
  dealnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.DealNumber)
}

// optional int32 MarketIndicator = 38;
void MDTransaction::clear_marketindicator() {
  marketindicator_ = 0;
}
::google::protobuf::int32 MDTransaction::marketindicator() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.MarketIndicator)
  return marketindicator_;
}
void MDTransaction::set_marketindicator(::google::protobuf::int32 value) {
  
  marketindicator_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.MarketIndicator)
}

// optional int32 RepoTerm = 39;
void MDTransaction::clear_repoterm() {
  repoterm_ = 0;
}
::google::protobuf::int32 MDTransaction::repoterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.RepoTerm)
  return repoterm_;
}
void MDTransaction::set_repoterm(::google::protobuf::int32 value) {
  
  repoterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.RepoTerm)
}

// optional int64 LegSettlementAmount1st = 40;
void MDTransaction::clear_legsettlementamount1st() {
  legsettlementamount1st_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legsettlementamount1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount1st)
  return legsettlementamount1st_;
}
void MDTransaction::set_legsettlementamount1st(::google::protobuf::int64 value) {
  
  legsettlementamount1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount1st)
}

// optional int64 LegSettlementAmount2nd = 41;
void MDTransaction::clear_legsettlementamount2nd() {
  legsettlementamount2nd_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legsettlementamount2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount2nd)
  return legsettlementamount2nd_;
}
void MDTransaction::set_legsettlementamount2nd(::google::protobuf::int64 value) {
  
  legsettlementamount2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegSettlementAmount2nd)
}

// optional string BondCode = 42;
void MDTransaction::clear_bondcode() {
  bondcode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::bondcode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  return bondcode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_bondcode(const ::std::string& value) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
void MDTransaction::set_bondcode(const char* value) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
void MDTransaction::set_bondcode(const char* value, size_t size) {
  
  bondcode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}
::std::string* MDTransaction::mutable_bondcode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  return bondcode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_bondcode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.BondCode)
  
  return bondcode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_bondcode(::std::string* bondcode) {
  if (bondcode != NULL) {
    
  } else {
    
  }
  bondcode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bondcode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.BondCode)
}

// optional string BondName = 43;
void MDTransaction::clear_bondname() {
  bondname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDTransaction::bondname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.BondName)
  return bondname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_bondname(const ::std::string& value) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
void MDTransaction::set_bondname(const char* value) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
void MDTransaction::set_bondname(const char* value, size_t size) {
  
  bondname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDTransaction.BondName)
}
::std::string* MDTransaction::mutable_bondname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDTransaction.BondName)
  return bondname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDTransaction::release_bondname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDTransaction.BondName)
  
  return bondname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDTransaction::set_allocated_bondname(::std::string* bondname) {
  if (bondname != NULL) {
    
  } else {
    
  }
  bondname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), bondname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDTransaction.BondName)
}

// optional int64 TotalFacevalue = 44;
void MDTransaction::clear_totalfacevalue() {
  totalfacevalue_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::totalfacevalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.TotalFacevalue)
  return totalfacevalue_;
}
void MDTransaction::set_totalfacevalue(::google::protobuf::int64 value) {
  
  totalfacevalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.TotalFacevalue)
}

// optional int64 LegCleanPrice1st = 45;
void MDTransaction::clear_legcleanprice1st() {
  legcleanprice1st_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legcleanprice1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice1st)
  return legcleanprice1st_;
}
void MDTransaction::set_legcleanprice1st(::google::protobuf::int64 value) {
  
  legcleanprice1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice1st)
}

// optional int64 LegCleanPrice2nd = 46;
void MDTransaction::clear_legcleanprice2nd() {
  legcleanprice2nd_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legcleanprice2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice2nd)
  return legcleanprice2nd_;
}
void MDTransaction::set_legcleanprice2nd(::google::protobuf::int64 value) {
  
  legcleanprice2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegCleanPrice2nd)
}

// optional int64 LegYield1st = 47;
void MDTransaction::clear_legyield1st() {
  legyield1st_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legyield1st() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegYield1st)
  return legyield1st_;
}
void MDTransaction::set_legyield1st(::google::protobuf::int64 value) {
  
  legyield1st_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegYield1st)
}

// optional int64 LegYield2nd = 48;
void MDTransaction::clear_legyield2nd() {
  legyield2nd_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDTransaction::legyield2nd() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDTransaction.LegYield2nd)
  return legyield2nd_;
}
void MDTransaction::set_legyield2nd(::google::protobuf::int64 value) {
  
  legyield2nd_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDTransaction.LegYield2nd)
}

inline const MDTransaction* MDTransaction::internal_default_instance() {
  return &MDTransaction_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
