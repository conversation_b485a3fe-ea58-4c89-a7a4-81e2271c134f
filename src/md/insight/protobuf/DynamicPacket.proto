syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
// Note: ESecurityType.proto is missing, but referenced.
// You may need to provide it or define it as below.

message DynamicPacket {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;  // ← Requires ESecurityType.proto
  int32 packetType = 7;
  string stringContent = 8;
  bytes bytesContent = 9;
  int32 ExchangeDate = 10;
  int32 ExchangeTime = 11;
  int32 DataMultiplePowerOf10 = 12;
}
