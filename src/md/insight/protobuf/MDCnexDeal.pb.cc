// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCnexDeal.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCnexDeal.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCnexDeal_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCnexDeal_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCnexDeal_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCnexDeal_2eproto() {
  protobuf_AddDesc_MDCnexDeal_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCnexDeal.proto");
  GOOGLE_CHECK(file != NULL);
  MDCnexDeal_descriptor_ = file->message_type(0);
  static const int MDCnexDeal_offsets_[26] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, cnexdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, issuedatatime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealsize_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, yield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, dealtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, quotestatus_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, quotepricetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, maturitydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, cnexsecuritytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, creditrating_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, text_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, statusvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, exerciseflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, tenor_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, workbench_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, datamultiplepowerof10_),
  };
  MDCnexDeal_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCnexDeal_descriptor_,
      MDCnexDeal::internal_default_instance(),
      MDCnexDeal_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCnexDeal),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCnexDeal, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCnexDeal_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCnexDeal_descriptor_, MDCnexDeal::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCnexDeal_2eproto() {
  MDCnexDeal_default_instance_.Shutdown();
  delete MDCnexDeal_reflection_;
}

void protobuf_InitDefaults_MDCnexDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCnexDeal_default_instance_.DefaultConstruct();
  MDCnexDeal_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCnexDeal_2eproto_once_);
void protobuf_InitDefaults_MDCnexDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCnexDeal_2eproto_once_,
                 &protobuf_InitDefaults_MDCnexDeal_2eproto_impl);
}
void protobuf_AddDesc_MDCnexDeal_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCnexDeal_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MDCnexDeal.proto\022\032com.htsc.mdc.insight"
    ".model\032\027ESecurityIDSource.proto\032\023ESecuri"
    "tyType.proto\"\351\004\n\nMDCnexDeal\022\026\n\016HTSCSecur"
    "ityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 "
    "\001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securityID"
    "Source\030\005 \001(\0162%.com.htsc.mdc.model.ESecur"
    "ityIDSource\0227\n\014securityType\030\006 \001(\0162!.com."
    "htsc.mdc.model.ESecurityType\022\024\n\014CnexData"
    "Type\030\007 \001(\005\022\025\n\rIssueDataTime\030\010 \001(\003\022\016\n\006Dea"
    "lID\030\t \001(\t\022\020\n\010DealType\030\n \001(\005\022\021\n\tDealPrice"
    "\030\013 \001(\003\022\020\n\010DealSize\030\014 \001(\003\022\r\n\005Yield\030\r \001(\003\022"
    "\020\n\010DealDate\030\016 \001(\005\022\020\n\010DealTime\030\017 \001(\005\022\023\n\013Q"
    "uoteStatus\030\020 \001(\005\022\026\n\016QuotePriceType\030\021 \001(\005"
    "\022\024\n\014MaturityDate\030\022 \001(\005\022\030\n\020CnexSecurityTy"
    "pe\030\023 \001(\t\022\024\n\014CreditRating\030\024 \001(\t\022\014\n\004Text\030\025"
    " \001(\t\022\023\n\013StatusValue\030\026 \001(\005\022\024\n\014ExerciseFla"
    "g\030\027 \001(\t\022\r\n\005Tenor\030\030 \001(\t\022\021\n\tWorkBench\030\031 \001("
    "\005\022\035\n\025DataMultiplePowerOf10\030\032 \001(\005B3\n\032com."
    "htsc.mdc.insight.modelB\020MDCnexDealProtos"
    "H\001\240\001\001b\006proto3", 773);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCnexDeal.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCnexDeal_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCnexDeal_2eproto_once_);
void protobuf_AddDesc_MDCnexDeal_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCnexDeal_2eproto_once_,
                 &protobuf_AddDesc_MDCnexDeal_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCnexDeal_2eproto {
  StaticDescriptorInitializer_MDCnexDeal_2eproto() {
    protobuf_AddDesc_MDCnexDeal_2eproto();
  }
} static_descriptor_initializer_MDCnexDeal_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCnexDeal::kHTSCSecurityIDFieldNumber;
const int MDCnexDeal::kMDDateFieldNumber;
const int MDCnexDeal::kMDTimeFieldNumber;
const int MDCnexDeal::kDataTimestampFieldNumber;
const int MDCnexDeal::kSecurityIDSourceFieldNumber;
const int MDCnexDeal::kSecurityTypeFieldNumber;
const int MDCnexDeal::kCnexDataTypeFieldNumber;
const int MDCnexDeal::kIssueDataTimeFieldNumber;
const int MDCnexDeal::kDealIDFieldNumber;
const int MDCnexDeal::kDealTypeFieldNumber;
const int MDCnexDeal::kDealPriceFieldNumber;
const int MDCnexDeal::kDealSizeFieldNumber;
const int MDCnexDeal::kYieldFieldNumber;
const int MDCnexDeal::kDealDateFieldNumber;
const int MDCnexDeal::kDealTimeFieldNumber;
const int MDCnexDeal::kQuoteStatusFieldNumber;
const int MDCnexDeal::kQuotePriceTypeFieldNumber;
const int MDCnexDeal::kMaturityDateFieldNumber;
const int MDCnexDeal::kCnexSecurityTypeFieldNumber;
const int MDCnexDeal::kCreditRatingFieldNumber;
const int MDCnexDeal::kTextFieldNumber;
const int MDCnexDeal::kStatusValueFieldNumber;
const int MDCnexDeal::kExerciseFlagFieldNumber;
const int MDCnexDeal::kTenorFieldNumber;
const int MDCnexDeal::kWorkBenchFieldNumber;
const int MDCnexDeal::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCnexDeal::MDCnexDeal()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCnexDeal_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCnexDeal)
}

void MDCnexDeal::InitAsDefaultInstance() {
}

MDCnexDeal::MDCnexDeal(const MDCnexDeal& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCnexDeal)
}

void MDCnexDeal::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cnexsecuritytype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exerciseflag_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCnexDeal::~MDCnexDeal() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCnexDeal)
  SharedDtor();
}

void MDCnexDeal::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dealid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cnexsecuritytype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exerciseflag_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDCnexDeal::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCnexDeal::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCnexDeal_descriptor_;
}

const MDCnexDeal& MDCnexDeal::default_instance() {
  protobuf_InitDefaults_MDCnexDeal_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCnexDeal> MDCnexDeal_default_instance_;

MDCnexDeal* MDCnexDeal::New(::google::protobuf::Arena* arena) const {
  MDCnexDeal* n = new MDCnexDeal;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCnexDeal::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCnexDeal)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCnexDeal, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCnexDeal*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, cnexdatatype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(dealtype_, quotestatus_);
  dealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(quotepricetype_, statusvalue_);
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exerciseflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(workbench_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

}

bool MDCnexDeal::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCnexDeal)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_CnexDataType;
        break;
      }

      // optional int32 CnexDataType = 7;
      case 7: {
        if (tag == 56) {
         parse_CnexDataType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cnexdatatype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_IssueDataTime;
        break;
      }

      // optional int64 IssueDataTime = 8;
      case 8: {
        if (tag == 64) {
         parse_IssueDataTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &issuedatatime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_DealID;
        break;
      }

      // optional string DealID = 9;
      case 9: {
        if (tag == 74) {
         parse_DealID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dealid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dealid().data(), this->dealid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.DealID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_DealType;
        break;
      }

      // optional int32 DealType = 10;
      case 10: {
        if (tag == 80) {
         parse_DealType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dealtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_DealPrice;
        break;
      }

      // optional int64 DealPrice = 11;
      case 11: {
        if (tag == 88) {
         parse_DealPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dealprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_DealSize;
        break;
      }

      // optional int64 DealSize = 12;
      case 12: {
        if (tag == 96) {
         parse_DealSize:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dealsize_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_Yield;
        break;
      }

      // optional int64 Yield = 13;
      case 13: {
        if (tag == 104) {
         parse_Yield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &yield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_DealDate;
        break;
      }

      // optional int32 DealDate = 14;
      case 14: {
        if (tag == 112) {
         parse_DealDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dealdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_DealTime;
        break;
      }

      // optional int32 DealTime = 15;
      case 15: {
        if (tag == 120) {
         parse_DealTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &dealtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_QuoteStatus;
        break;
      }

      // optional int32 QuoteStatus = 16;
      case 16: {
        if (tag == 128) {
         parse_QuoteStatus:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotestatus_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_QuotePriceType;
        break;
      }

      // optional int32 QuotePriceType = 17;
      case 17: {
        if (tag == 136) {
         parse_QuotePriceType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &quotepricetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_MaturityDate;
        break;
      }

      // optional int32 MaturityDate = 18;
      case 18: {
        if (tag == 144) {
         parse_MaturityDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &maturitydate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_CnexSecurityType;
        break;
      }

      // optional string CnexSecurityType = 19;
      case 19: {
        if (tag == 154) {
         parse_CnexSecurityType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cnexsecuritytype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_CreditRating;
        break;
      }

      // optional string CreditRating = 20;
      case 20: {
        if (tag == 162) {
         parse_CreditRating:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_creditrating()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->creditrating().data(), this->creditrating().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.CreditRating"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_Text;
        break;
      }

      // optional string Text = 21;
      case 21: {
        if (tag == 170) {
         parse_Text:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_text()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->text().data(), this->text().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.Text"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_StatusValue;
        break;
      }

      // optional int32 StatusValue = 22;
      case 22: {
        if (tag == 176) {
         parse_StatusValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &statusvalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_ExerciseFlag;
        break;
      }

      // optional string ExerciseFlag = 23;
      case 23: {
        if (tag == 186) {
         parse_ExerciseFlag:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_exerciseflag()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->exerciseflag().data(), this->exerciseflag().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_Tenor;
        break;
      }

      // optional string Tenor = 24;
      case 24: {
        if (tag == 194) {
         parse_Tenor:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tenor()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tenor().data(), this->tenor().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCnexDeal.Tenor"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_WorkBench;
        break;
      }

      // optional int32 WorkBench = 25;
      case 25: {
        if (tag == 200) {
         parse_WorkBench:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &workbench_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 26;
      case 26: {
        if (tag == 208) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCnexDeal)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCnexDeal)
  return false;
#undef DO_
}

void MDCnexDeal::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCnexDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 CnexDataType = 7;
  if (this->cnexdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->cnexdatatype(), output);
  }

  // optional int64 IssueDataTime = 8;
  if (this->issuedatatime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->issuedatatime(), output);
  }

  // optional string DealID = 9;
  if (this->dealid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealid().data(), this->dealid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.DealID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      9, this->dealid(), output);
  }

  // optional int32 DealType = 10;
  if (this->dealtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->dealtype(), output);
  }

  // optional int64 DealPrice = 11;
  if (this->dealprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->dealprice(), output);
  }

  // optional int64 DealSize = 12;
  if (this->dealsize() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->dealsize(), output);
  }

  // optional int64 Yield = 13;
  if (this->yield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->yield(), output);
  }

  // optional int32 DealDate = 14;
  if (this->dealdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->dealdate(), output);
  }

  // optional int32 DealTime = 15;
  if (this->dealtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->dealtime(), output);
  }

  // optional int32 QuoteStatus = 16;
  if (this->quotestatus() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->quotestatus(), output);
  }

  // optional int32 QuotePriceType = 17;
  if (this->quotepricetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->quotepricetype(), output);
  }

  // optional int32 MaturityDate = 18;
  if (this->maturitydate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->maturitydate(), output);
  }

  // optional string CnexSecurityType = 19;
  if (this->cnexsecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      19, this->cnexsecuritytype(), output);
  }

  // optional string CreditRating = 20;
  if (this->creditrating().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creditrating().data(), this->creditrating().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.CreditRating");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->creditrating(), output);
  }

  // optional string Text = 21;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.Text");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->text(), output);
  }

  // optional int32 StatusValue = 22;
  if (this->statusvalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(22, this->statusvalue(), output);
  }

  // optional string ExerciseFlag = 23;
  if (this->exerciseflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exerciseflag().data(), this->exerciseflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      23, this->exerciseflag(), output);
  }

  // optional string Tenor = 24;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.Tenor");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      24, this->tenor(), output);
  }

  // optional int32 WorkBench = 25;
  if (this->workbench() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(25, this->workbench(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 26;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(26, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCnexDeal)
}

::google::protobuf::uint8* MDCnexDeal::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCnexDeal)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 CnexDataType = 7;
  if (this->cnexdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->cnexdatatype(), target);
  }

  // optional int64 IssueDataTime = 8;
  if (this->issuedatatime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->issuedatatime(), target);
  }

  // optional string DealID = 9;
  if (this->dealid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dealid().data(), this->dealid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.DealID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        9, this->dealid(), target);
  }

  // optional int32 DealType = 10;
  if (this->dealtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->dealtype(), target);
  }

  // optional int64 DealPrice = 11;
  if (this->dealprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->dealprice(), target);
  }

  // optional int64 DealSize = 12;
  if (this->dealsize() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->dealsize(), target);
  }

  // optional int64 Yield = 13;
  if (this->yield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->yield(), target);
  }

  // optional int32 DealDate = 14;
  if (this->dealdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->dealdate(), target);
  }

  // optional int32 DealTime = 15;
  if (this->dealtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->dealtime(), target);
  }

  // optional int32 QuoteStatus = 16;
  if (this->quotestatus() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->quotestatus(), target);
  }

  // optional int32 QuotePriceType = 17;
  if (this->quotepricetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->quotepricetype(), target);
  }

  // optional int32 MaturityDate = 18;
  if (this->maturitydate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->maturitydate(), target);
  }

  // optional string CnexSecurityType = 19;
  if (this->cnexsecuritytype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cnexsecuritytype().data(), this->cnexsecuritytype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        19, this->cnexsecuritytype(), target);
  }

  // optional string CreditRating = 20;
  if (this->creditrating().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->creditrating().data(), this->creditrating().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.CreditRating");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->creditrating(), target);
  }

  // optional string Text = 21;
  if (this->text().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->text().data(), this->text().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.Text");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->text(), target);
  }

  // optional int32 StatusValue = 22;
  if (this->statusvalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(22, this->statusvalue(), target);
  }

  // optional string ExerciseFlag = 23;
  if (this->exerciseflag().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->exerciseflag().data(), this->exerciseflag().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        23, this->exerciseflag(), target);
  }

  // optional string Tenor = 24;
  if (this->tenor().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tenor().data(), this->tenor().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCnexDeal.Tenor");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        24, this->tenor(), target);
  }

  // optional int32 WorkBench = 25;
  if (this->workbench() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(25, this->workbench(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 26;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(26, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCnexDeal)
  return target;
}

size_t MDCnexDeal::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCnexDeal)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 CnexDataType = 7;
  if (this->cnexdatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->cnexdatatype());
  }

  // optional int64 IssueDataTime = 8;
  if (this->issuedatatime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->issuedatatime());
  }

  // optional string DealID = 9;
  if (this->dealid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dealid());
  }

  // optional int32 DealType = 10;
  if (this->dealtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dealtype());
  }

  // optional int64 DealPrice = 11;
  if (this->dealprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dealprice());
  }

  // optional int64 DealSize = 12;
  if (this->dealsize() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dealsize());
  }

  // optional int64 Yield = 13;
  if (this->yield() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->yield());
  }

  // optional int32 DealDate = 14;
  if (this->dealdate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dealdate());
  }

  // optional int32 DealTime = 15;
  if (this->dealtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->dealtime());
  }

  // optional int32 QuoteStatus = 16;
  if (this->quotestatus() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotestatus());
  }

  // optional int32 QuotePriceType = 17;
  if (this->quotepricetype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->quotepricetype());
  }

  // optional int32 MaturityDate = 18;
  if (this->maturitydate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->maturitydate());
  }

  // optional string CnexSecurityType = 19;
  if (this->cnexsecuritytype().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cnexsecuritytype());
  }

  // optional string CreditRating = 20;
  if (this->creditrating().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->creditrating());
  }

  // optional string Text = 21;
  if (this->text().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->text());
  }

  // optional int32 StatusValue = 22;
  if (this->statusvalue() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->statusvalue());
  }

  // optional string ExerciseFlag = 23;
  if (this->exerciseflag().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->exerciseflag());
  }

  // optional string Tenor = 24;
  if (this->tenor().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tenor());
  }

  // optional int32 WorkBench = 25;
  if (this->workbench() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->workbench());
  }

  // optional int32 DataMultiplePowerOf10 = 26;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCnexDeal::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCnexDeal)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCnexDeal* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCnexDeal>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCnexDeal)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCnexDeal)
    UnsafeMergeFrom(*source);
  }
}

void MDCnexDeal::MergeFrom(const MDCnexDeal& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCnexDeal)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCnexDeal::UnsafeMergeFrom(const MDCnexDeal& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.cnexdatatype() != 0) {
    set_cnexdatatype(from.cnexdatatype());
  }
  if (from.issuedatatime() != 0) {
    set_issuedatatime(from.issuedatatime());
  }
  if (from.dealid().size() > 0) {

    dealid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dealid_);
  }
  if (from.dealtype() != 0) {
    set_dealtype(from.dealtype());
  }
  if (from.dealprice() != 0) {
    set_dealprice(from.dealprice());
  }
  if (from.dealsize() != 0) {
    set_dealsize(from.dealsize());
  }
  if (from.yield() != 0) {
    set_yield(from.yield());
  }
  if (from.dealdate() != 0) {
    set_dealdate(from.dealdate());
  }
  if (from.dealtime() != 0) {
    set_dealtime(from.dealtime());
  }
  if (from.quotestatus() != 0) {
    set_quotestatus(from.quotestatus());
  }
  if (from.quotepricetype() != 0) {
    set_quotepricetype(from.quotepricetype());
  }
  if (from.maturitydate() != 0) {
    set_maturitydate(from.maturitydate());
  }
  if (from.cnexsecuritytype().size() > 0) {

    cnexsecuritytype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cnexsecuritytype_);
  }
  if (from.creditrating().size() > 0) {

    creditrating_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.creditrating_);
  }
  if (from.text().size() > 0) {

    text_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.text_);
  }
  if (from.statusvalue() != 0) {
    set_statusvalue(from.statusvalue());
  }
  if (from.exerciseflag().size() > 0) {

    exerciseflag_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.exerciseflag_);
  }
  if (from.tenor().size() > 0) {

    tenor_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tenor_);
  }
  if (from.workbench() != 0) {
    set_workbench(from.workbench());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDCnexDeal::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCnexDeal)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCnexDeal::CopyFrom(const MDCnexDeal& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCnexDeal)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCnexDeal::IsInitialized() const {

  return true;
}

void MDCnexDeal::Swap(MDCnexDeal* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCnexDeal::InternalSwap(MDCnexDeal* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(cnexdatatype_, other->cnexdatatype_);
  std::swap(issuedatatime_, other->issuedatatime_);
  dealid_.Swap(&other->dealid_);
  std::swap(dealtype_, other->dealtype_);
  std::swap(dealprice_, other->dealprice_);
  std::swap(dealsize_, other->dealsize_);
  std::swap(yield_, other->yield_);
  std::swap(dealdate_, other->dealdate_);
  std::swap(dealtime_, other->dealtime_);
  std::swap(quotestatus_, other->quotestatus_);
  std::swap(quotepricetype_, other->quotepricetype_);
  std::swap(maturitydate_, other->maturitydate_);
  cnexsecuritytype_.Swap(&other->cnexsecuritytype_);
  creditrating_.Swap(&other->creditrating_);
  text_.Swap(&other->text_);
  std::swap(statusvalue_, other->statusvalue_);
  exerciseflag_.Swap(&other->exerciseflag_);
  tenor_.Swap(&other->tenor_);
  std::swap(workbench_, other->workbench_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCnexDeal::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCnexDeal_descriptor_;
  metadata.reflection = MDCnexDeal_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCnexDeal

// optional string HTSCSecurityID = 1;
void MDCnexDeal::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
void MDCnexDeal::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
void MDCnexDeal::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}
::std::string* MDCnexDeal::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCnexDeal::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCnexDeal::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MDDate)
  return mddate_;
}
void MDCnexDeal::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MDDate)
}

// optional int32 MDTime = 3;
void MDCnexDeal::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCnexDeal::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MDTime)
  return mdtime_;
}
void MDCnexDeal::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCnexDeal::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexDeal::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DataTimestamp)
  return datatimestamp_;
}
void MDCnexDeal::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCnexDeal::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCnexDeal::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCnexDeal::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCnexDeal::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCnexDeal::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCnexDeal::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.securityType)
}

// optional int32 CnexDataType = 7;
void MDCnexDeal::clear_cnexdatatype() {
  cnexdatatype_ = 0;
}
::google::protobuf::int32 MDCnexDeal::cnexdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CnexDataType)
  return cnexdatatype_;
}
void MDCnexDeal::set_cnexdatatype(::google::protobuf::int32 value) {
  
  cnexdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CnexDataType)
}

// optional int64 IssueDataTime = 8;
void MDCnexDeal::clear_issuedatatime() {
  issuedatatime_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexDeal::issuedatatime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.IssueDataTime)
  return issuedatatime_;
}
void MDCnexDeal::set_issuedatatime(::google::protobuf::int64 value) {
  
  issuedatatime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.IssueDataTime)
}

// optional string DealID = 9;
void MDCnexDeal::clear_dealid() {
  dealid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::dealid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  return dealid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_dealid(const ::std::string& value) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
void MDCnexDeal::set_dealid(const char* value) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
void MDCnexDeal::set_dealid(const char* value, size_t size) {
  
  dealid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}
::std::string* MDCnexDeal::mutable_dealid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  return dealid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_dealid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
  
  return dealid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_dealid(::std::string* dealid) {
  if (dealid != NULL) {
    
  } else {
    
  }
  dealid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dealid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.DealID)
}

// optional int32 DealType = 10;
void MDCnexDeal::clear_dealtype() {
  dealtype_ = 0;
}
::google::protobuf::int32 MDCnexDeal::dealtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealType)
  return dealtype_;
}
void MDCnexDeal::set_dealtype(::google::protobuf::int32 value) {
  
  dealtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealType)
}

// optional int64 DealPrice = 11;
void MDCnexDeal::clear_dealprice() {
  dealprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexDeal::dealprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealPrice)
  return dealprice_;
}
void MDCnexDeal::set_dealprice(::google::protobuf::int64 value) {
  
  dealprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealPrice)
}

// optional int64 DealSize = 12;
void MDCnexDeal::clear_dealsize() {
  dealsize_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexDeal::dealsize() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealSize)
  return dealsize_;
}
void MDCnexDeal::set_dealsize(::google::protobuf::int64 value) {
  
  dealsize_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealSize)
}

// optional int64 Yield = 13;
void MDCnexDeal::clear_yield() {
  yield_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCnexDeal::yield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Yield)
  return yield_;
}
void MDCnexDeal::set_yield(::google::protobuf::int64 value) {
  
  yield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Yield)
}

// optional int32 DealDate = 14;
void MDCnexDeal::clear_dealdate() {
  dealdate_ = 0;
}
::google::protobuf::int32 MDCnexDeal::dealdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealDate)
  return dealdate_;
}
void MDCnexDeal::set_dealdate(::google::protobuf::int32 value) {
  
  dealdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealDate)
}

// optional int32 DealTime = 15;
void MDCnexDeal::clear_dealtime() {
  dealtime_ = 0;
}
::google::protobuf::int32 MDCnexDeal::dealtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DealTime)
  return dealtime_;
}
void MDCnexDeal::set_dealtime(::google::protobuf::int32 value) {
  
  dealtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DealTime)
}

// optional int32 QuoteStatus = 16;
void MDCnexDeal::clear_quotestatus() {
  quotestatus_ = 0;
}
::google::protobuf::int32 MDCnexDeal::quotestatus() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.QuoteStatus)
  return quotestatus_;
}
void MDCnexDeal::set_quotestatus(::google::protobuf::int32 value) {
  
  quotestatus_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.QuoteStatus)
}

// optional int32 QuotePriceType = 17;
void MDCnexDeal::clear_quotepricetype() {
  quotepricetype_ = 0;
}
::google::protobuf::int32 MDCnexDeal::quotepricetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.QuotePriceType)
  return quotepricetype_;
}
void MDCnexDeal::set_quotepricetype(::google::protobuf::int32 value) {
  
  quotepricetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.QuotePriceType)
}

// optional int32 MaturityDate = 18;
void MDCnexDeal::clear_maturitydate() {
  maturitydate_ = 0;
}
::google::protobuf::int32 MDCnexDeal::maturitydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.MaturityDate)
  return maturitydate_;
}
void MDCnexDeal::set_maturitydate(::google::protobuf::int32 value) {
  
  maturitydate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.MaturityDate)
}

// optional string CnexSecurityType = 19;
void MDCnexDeal::clear_cnexsecuritytype() {
  cnexsecuritytype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::cnexsecuritytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  return cnexsecuritytype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_cnexsecuritytype(const ::std::string& value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
void MDCnexDeal::set_cnexsecuritytype(const char* value) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
void MDCnexDeal::set_cnexsecuritytype(const char* value, size_t size) {
  
  cnexsecuritytype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}
::std::string* MDCnexDeal::mutable_cnexsecuritytype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  return cnexsecuritytype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_cnexsecuritytype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
  
  return cnexsecuritytype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_cnexsecuritytype(::std::string* cnexsecuritytype) {
  if (cnexsecuritytype != NULL) {
    
  } else {
    
  }
  cnexsecuritytype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cnexsecuritytype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.CnexSecurityType)
}

// optional string CreditRating = 20;
void MDCnexDeal::clear_creditrating() {
  creditrating_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::creditrating() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  return creditrating_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_creditrating(const ::std::string& value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
void MDCnexDeal::set_creditrating(const char* value) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
void MDCnexDeal::set_creditrating(const char* value, size_t size) {
  
  creditrating_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}
::std::string* MDCnexDeal::mutable_creditrating() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  return creditrating_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_creditrating() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
  
  return creditrating_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_creditrating(::std::string* creditrating) {
  if (creditrating != NULL) {
    
  } else {
    
  }
  creditrating_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), creditrating);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.CreditRating)
}

// optional string Text = 21;
void MDCnexDeal::clear_text() {
  text_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::text() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  return text_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_text(const ::std::string& value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
void MDCnexDeal::set_text(const char* value) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
void MDCnexDeal::set_text(const char* value, size_t size) {
  
  text_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}
::std::string* MDCnexDeal::mutable_text() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  return text_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_text() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.Text)
  
  return text_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_text(::std::string* text) {
  if (text != NULL) {
    
  } else {
    
  }
  text_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), text);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.Text)
}

// optional int32 StatusValue = 22;
void MDCnexDeal::clear_statusvalue() {
  statusvalue_ = 0;
}
::google::protobuf::int32 MDCnexDeal::statusvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.StatusValue)
  return statusvalue_;
}
void MDCnexDeal::set_statusvalue(::google::protobuf::int32 value) {
  
  statusvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.StatusValue)
}

// optional string ExerciseFlag = 23;
void MDCnexDeal::clear_exerciseflag() {
  exerciseflag_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::exerciseflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  return exerciseflag_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_exerciseflag(const ::std::string& value) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
void MDCnexDeal::set_exerciseflag(const char* value) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
void MDCnexDeal::set_exerciseflag(const char* value, size_t size) {
  
  exerciseflag_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}
::std::string* MDCnexDeal::mutable_exerciseflag() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  return exerciseflag_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_exerciseflag() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
  
  return exerciseflag_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_exerciseflag(::std::string* exerciseflag) {
  if (exerciseflag != NULL) {
    
  } else {
    
  }
  exerciseflag_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), exerciseflag);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.ExerciseFlag)
}

// optional string Tenor = 24;
void MDCnexDeal::clear_tenor() {
  tenor_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCnexDeal::tenor() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  return tenor_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_tenor(const ::std::string& value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
void MDCnexDeal::set_tenor(const char* value) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
void MDCnexDeal::set_tenor(const char* value, size_t size) {
  
  tenor_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}
::std::string* MDCnexDeal::mutable_tenor() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  return tenor_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCnexDeal::release_tenor() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
  
  return tenor_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCnexDeal::set_allocated_tenor(::std::string* tenor) {
  if (tenor != NULL) {
    
  } else {
    
  }
  tenor_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tenor);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCnexDeal.Tenor)
}

// optional int32 WorkBench = 25;
void MDCnexDeal::clear_workbench() {
  workbench_ = 0;
}
::google::protobuf::int32 MDCnexDeal::workbench() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.WorkBench)
  return workbench_;
}
void MDCnexDeal::set_workbench(::google::protobuf::int32 value) {
  
  workbench_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.WorkBench)
}

// optional int32 DataMultiplePowerOf10 = 26;
void MDCnexDeal::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCnexDeal::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCnexDeal.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCnexDeal::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCnexDeal.DataMultiplePowerOf10)
}

inline const MDCnexDeal* MDCnexDeal::internal_default_instance() {
  return &MDCnexDeal_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
