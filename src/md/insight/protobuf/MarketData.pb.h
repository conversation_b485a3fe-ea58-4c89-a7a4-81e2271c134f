// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MarketData.proto

#ifndef PROTOBUF_MarketData_2eproto__INCLUDED
#define PROTOBUF_MarketData_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "EMarketDataType.pb.h"
#include "MDStock.pb.h"
#include "MDBond.pb.h"
#include "MDFund.pb.h"
#include "MDOption.pb.h"
#include "MDIndex.pb.h"
#include "MDFuture.pb.h"
#include "MDTransaction.pb.h"
#include "MDOrder.pb.h"
#include "ADKLine.pb.h"
#include "ADTwap.pb.h"
#include "ADVwap.pb.h"
#include "MDBasicInfo.pb.h"
#include "MDSimpleTick.pb.h"
#include "ADUpsDownsAnalysis.pb.h"
#include "ADIndicatorsRanking.pb.h"
#include "DynamicPacket.pb.h"
#include "ADVolumeByPrice.pb.h"
#include "ADFundFlowAnalysis.pb.h"
#include "MDForex.pb.h"
#include "MDSpot.pb.h"
#include "MDRate.pb.h"
#include "ADOrderbookSnapshot.pb.h"
#include "ADOrderbookSnapshotWithTick.pb.h"
#include "MDQuote.pb.h"
#include "MDETFBasicInfo.pb.h"
#include "MDFIQuote.pb.h"
#include "ADChipDistribution.pb.h"
#include "MDWarrant.pb.h"
#include "MDSecurityLending.pb.h"
#include "ADNews.pb.h"
#include "ADStaringResult.pb.h"
#include "ADDerivedAnalysis.pb.h"
#include "MDQBQuote.pb.h"
#include "MDQBTransaction.pb.h"
#include "MDUSATransaction.pb.h"
#include "MDUSAOrder.pb.h"
#include "MDSLTransaction.pb.h"
#include "MDSLOrder.pb.h"
#include "MDHKGreyMarket.pb.h"
#include "MDSLIndicativeQuote.pb.h"
#include "MDSLStatistics.pb.h"
#include "MDUSAQuote.pb.h"
#include "MDSLEstimation.pb.h"
#include "MDCnexDeal.pb.h"
#include "MDCnexQuote.pb.h"
#include "MDDelaySnapshot.pb.h"
#include "MDHighAccuracyFuture.pb.h"
#include "MDCfetsForex.pb.h"
#include "MDCfetsFxSnapshot.pb.h"
#include "MDCfetsFxQuote.pb.h"
#include "SPFuture.pb.h"
#include "MDCfetsBenchmark.pb.h"
#include "MDCfetsBondDeal.pb.h"
#include "MDCfetsBondSnapshot.pb.h"
#include "MDCfetsCurrencyDeal.pb.h"
#include "MDCfetsCurrencySnapshot.pb.h"
#include "MDCfetsODMSnapshot.pb.h"
#include "MDCfetsQDMQuote.pb.h"
#include "MDCfetsRateDeal.pb.h"
#include "MDCfetsRateSnapshot.pb.h"
#include "MDCfetsFxCnyMiddlePrice.pb.h"
#include "MDIopvSnapshot.pb.h"
#include "MDChinaBondBenchmark.pb.h"
#include "MDIceTrace.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MarketData_2eproto();
void protobuf_InitDefaults_MarketData_2eproto();
void protobuf_AssignDesc_MarketData_2eproto();
void protobuf_ShutdownFile_MarketData_2eproto();

class MarketData;
class MarketDataList;
class MarketDataStream;

// ===================================================================

class MarketData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MarketData) */ {
 public:
  MarketData();
  virtual ~MarketData();

  MarketData(const MarketData& from);

  inline MarketData& operator=(const MarketData& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarketData& default_instance();

  static const MarketData* internal_default_instance();

  void Swap(MarketData* other);

  // implements Message ----------------------------------------------

  inline MarketData* New() const { return New(NULL); }

  MarketData* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MarketData& from);
  void MergeFrom(const MarketData& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MarketData* other);
  void UnsafeMergeFrom(const MarketData& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
  void clear_marketdatatype();
  static const int kMarketDataTypeFieldNumber = 1;
  ::com::htsc::mdc::insight::model::EMarketDataType marketdatatype() const;
  void set_marketdatatype(::com::htsc::mdc::insight::model::EMarketDataType value);

  // optional int64 MessageChannelNumber = 2;
  void clear_messagechannelnumber();
  static const int kMessageChannelNumberFieldNumber = 2;
  ::google::protobuf::int64 messagechannelnumber() const;
  void set_messagechannelnumber(::google::protobuf::int64 value);

  // optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
  bool has_mdstock() const;
  void clear_mdstock();
  static const int kMdStockFieldNumber = 10;
  const ::com::htsc::mdc::insight::model::MDStock& mdstock() const;
  ::com::htsc::mdc::insight::model::MDStock* mutable_mdstock();
  ::com::htsc::mdc::insight::model::MDStock* release_mdstock();
  void set_allocated_mdstock(::com::htsc::mdc::insight::model::MDStock* mdstock);

  // optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
  bool has_mdindex() const;
  void clear_mdindex();
  static const int kMdIndexFieldNumber = 11;
  const ::com::htsc::mdc::insight::model::MDIndex& mdindex() const;
  ::com::htsc::mdc::insight::model::MDIndex* mutable_mdindex();
  ::com::htsc::mdc::insight::model::MDIndex* release_mdindex();
  void set_allocated_mdindex(::com::htsc::mdc::insight::model::MDIndex* mdindex);

  // optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
  bool has_mdbond() const;
  void clear_mdbond();
  static const int kMdBondFieldNumber = 12;
  const ::com::htsc::mdc::insight::model::MDBond& mdbond() const;
  ::com::htsc::mdc::insight::model::MDBond* mutable_mdbond();
  ::com::htsc::mdc::insight::model::MDBond* release_mdbond();
  void set_allocated_mdbond(::com::htsc::mdc::insight::model::MDBond* mdbond);

  // optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
  bool has_mdfund() const;
  void clear_mdfund();
  static const int kMdFundFieldNumber = 13;
  const ::com::htsc::mdc::insight::model::MDFund& mdfund() const;
  ::com::htsc::mdc::insight::model::MDFund* mutable_mdfund();
  ::com::htsc::mdc::insight::model::MDFund* release_mdfund();
  void set_allocated_mdfund(::com::htsc::mdc::insight::model::MDFund* mdfund);

  // optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
  bool has_mdoption() const;
  void clear_mdoption();
  static const int kMdOptionFieldNumber = 14;
  const ::com::htsc::mdc::insight::model::MDOption& mdoption() const;
  ::com::htsc::mdc::insight::model::MDOption* mutable_mdoption();
  ::com::htsc::mdc::insight::model::MDOption* release_mdoption();
  void set_allocated_mdoption(::com::htsc::mdc::insight::model::MDOption* mdoption);

  // optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
  bool has_mdfuture() const;
  void clear_mdfuture();
  static const int kMdFutureFieldNumber = 15;
  const ::com::htsc::mdc::insight::model::MDFuture& mdfuture() const;
  ::com::htsc::mdc::insight::model::MDFuture* mutable_mdfuture();
  ::com::htsc::mdc::insight::model::MDFuture* release_mdfuture();
  void set_allocated_mdfuture(::com::htsc::mdc::insight::model::MDFuture* mdfuture);

  // optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
  bool has_mdtransaction() const;
  void clear_mdtransaction();
  static const int kMdTransactionFieldNumber = 16;
  const ::com::htsc::mdc::insight::model::MDTransaction& mdtransaction() const;
  ::com::htsc::mdc::insight::model::MDTransaction* mutable_mdtransaction();
  ::com::htsc::mdc::insight::model::MDTransaction* release_mdtransaction();
  void set_allocated_mdtransaction(::com::htsc::mdc::insight::model::MDTransaction* mdtransaction);

  // optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
  bool has_mdorder() const;
  void clear_mdorder();
  static const int kMdOrderFieldNumber = 17;
  const ::com::htsc::mdc::insight::model::MDOrder& mdorder() const;
  ::com::htsc::mdc::insight::model::MDOrder* mutable_mdorder();
  ::com::htsc::mdc::insight::model::MDOrder* release_mdorder();
  void set_allocated_mdorder(::com::htsc::mdc::insight::model::MDOrder* mdorder);

  // optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
  bool has_mdkline() const;
  void clear_mdkline();
  static const int kMdKLineFieldNumber = 18;
  const ::com::htsc::mdc::insight::model::ADKLine& mdkline() const;
  ::com::htsc::mdc::insight::model::ADKLine* mutable_mdkline();
  ::com::htsc::mdc::insight::model::ADKLine* release_mdkline();
  void set_allocated_mdkline(::com::htsc::mdc::insight::model::ADKLine* mdkline);

  // optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
  bool has_mdtwap() const;
  void clear_mdtwap();
  static const int kMdTwapFieldNumber = 19;
  const ::com::htsc::mdc::insight::model::ADTwap& mdtwap() const;
  ::com::htsc::mdc::insight::model::ADTwap* mutable_mdtwap();
  ::com::htsc::mdc::insight::model::ADTwap* release_mdtwap();
  void set_allocated_mdtwap(::com::htsc::mdc::insight::model::ADTwap* mdtwap);

  // optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
  bool has_mdvwap() const;
  void clear_mdvwap();
  static const int kMdVwapFieldNumber = 20;
  const ::com::htsc::mdc::insight::model::ADVwap& mdvwap() const;
  ::com::htsc::mdc::insight::model::ADVwap* mutable_mdvwap();
  ::com::htsc::mdc::insight::model::ADVwap* release_mdvwap();
  void set_allocated_mdvwap(::com::htsc::mdc::insight::model::ADVwap* mdvwap);

  // optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
  bool has_mdconstant() const;
  void clear_mdconstant();
  static const int kMdConstantFieldNumber = 21;
  const ::com::htsc::mdc::insight::model::MDBasicInfo& mdconstant() const;
  ::com::htsc::mdc::insight::model::MDBasicInfo* mutable_mdconstant();
  ::com::htsc::mdc::insight::model::MDBasicInfo* release_mdconstant();
  void set_allocated_mdconstant(::com::htsc::mdc::insight::model::MDBasicInfo* mdconstant);

  // optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
  bool has_mdsimpletick() const;
  void clear_mdsimpletick();
  static const int kMdSimpleTickFieldNumber = 22;
  const ::com::htsc::mdc::insight::model::MDSimpleTick& mdsimpletick() const;
  ::com::htsc::mdc::insight::model::MDSimpleTick* mutable_mdsimpletick();
  ::com::htsc::mdc::insight::model::MDSimpleTick* release_mdsimpletick();
  void set_allocated_mdsimpletick(::com::htsc::mdc::insight::model::MDSimpleTick* mdsimpletick);

  // optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
  bool has_mdupsdownsanalysis() const;
  void clear_mdupsdownsanalysis();
  static const int kMdUpsDownsAnalysisFieldNumber = 23;
  const ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis& mdupsdownsanalysis() const;
  ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* mutable_mdupsdownsanalysis();
  ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* release_mdupsdownsanalysis();
  void set_allocated_mdupsdownsanalysis(::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* mdupsdownsanalysis);

  // optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
  bool has_mdindicatorsranking() const;
  void clear_mdindicatorsranking();
  static const int kMdIndicatorsRankingFieldNumber = 24;
  const ::com::htsc::mdc::insight::model::ADIndicatorsRanking& mdindicatorsranking() const;
  ::com::htsc::mdc::insight::model::ADIndicatorsRanking* mutable_mdindicatorsranking();
  ::com::htsc::mdc::insight::model::ADIndicatorsRanking* release_mdindicatorsranking();
  void set_allocated_mdindicatorsranking(::com::htsc::mdc::insight::model::ADIndicatorsRanking* mdindicatorsranking);

  // optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
  bool has_dynamicpacket() const;
  void clear_dynamicpacket();
  static const int kDynamicPacketFieldNumber = 25;
  const ::com::htsc::mdc::insight::model::DynamicPacket& dynamicpacket() const;
  ::com::htsc::mdc::insight::model::DynamicPacket* mutable_dynamicpacket();
  ::com::htsc::mdc::insight::model::DynamicPacket* release_dynamicpacket();
  void set_allocated_dynamicpacket(::com::htsc::mdc::insight::model::DynamicPacket* dynamicpacket);

  // optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
  bool has_mdvolumebyprice() const;
  void clear_mdvolumebyprice();
  static const int kMdVolumeByPriceFieldNumber = 26;
  const ::com::htsc::mdc::insight::model::ADVolumeByPrice& mdvolumebyprice() const;
  ::com::htsc::mdc::insight::model::ADVolumeByPrice* mutable_mdvolumebyprice();
  ::com::htsc::mdc::insight::model::ADVolumeByPrice* release_mdvolumebyprice();
  void set_allocated_mdvolumebyprice(::com::htsc::mdc::insight::model::ADVolumeByPrice* mdvolumebyprice);

  // optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
  bool has_mdfundflowanalysis() const;
  void clear_mdfundflowanalysis();
  static const int kMdFundFlowAnalysisFieldNumber = 27;
  const ::com::htsc::mdc::insight::model::ADFundFlowAnalysis& mdfundflowanalysis() const;
  ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* mutable_mdfundflowanalysis();
  ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* release_mdfundflowanalysis();
  void set_allocated_mdfundflowanalysis(::com::htsc::mdc::insight::model::ADFundFlowAnalysis* mdfundflowanalysis);

  // optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
  bool has_mdforex() const;
  void clear_mdforex();
  static const int kMdForexFieldNumber = 28;
  const ::com::htsc::mdc::insight::model::MDForex& mdforex() const;
  ::com::htsc::mdc::insight::model::MDForex* mutable_mdforex();
  ::com::htsc::mdc::insight::model::MDForex* release_mdforex();
  void set_allocated_mdforex(::com::htsc::mdc::insight::model::MDForex* mdforex);

  // optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
  bool has_mdspot() const;
  void clear_mdspot();
  static const int kMdSpotFieldNumber = 29;
  const ::com::htsc::mdc::insight::model::MDSpot& mdspot() const;
  ::com::htsc::mdc::insight::model::MDSpot* mutable_mdspot();
  ::com::htsc::mdc::insight::model::MDSpot* release_mdspot();
  void set_allocated_mdspot(::com::htsc::mdc::insight::model::MDSpot* mdspot);

  // optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
  bool has_mdrate() const;
  void clear_mdrate();
  static const int kMdRateFieldNumber = 30;
  const ::com::htsc::mdc::insight::model::MDRate& mdrate() const;
  ::com::htsc::mdc::insight::model::MDRate* mutable_mdrate();
  ::com::htsc::mdc::insight::model::MDRate* release_mdrate();
  void set_allocated_mdrate(::com::htsc::mdc::insight::model::MDRate* mdrate);

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
  bool has_orderbooksnapshot() const;
  void clear_orderbooksnapshot();
  static const int kOrderbookSnapshotFieldNumber = 31;
  const ::com::htsc::mdc::insight::model::ADOrderbookSnapshot& orderbooksnapshot() const;
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* mutable_orderbooksnapshot();
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* release_orderbooksnapshot();
  void set_allocated_orderbooksnapshot(::com::htsc::mdc::insight::model::ADOrderbookSnapshot* orderbooksnapshot);

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
  bool has_orderbooksnapshotwithtick() const;
  void clear_orderbooksnapshotwithtick();
  static const int kOrderbookSnapshotWithTickFieldNumber = 32;
  const ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick& orderbooksnapshotwithtick() const;
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* mutable_orderbooksnapshotwithtick();
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* release_orderbooksnapshotwithtick();
  void set_allocated_orderbooksnapshotwithtick(::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* orderbooksnapshotwithtick);

  // optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
  bool has_mdquote() const;
  void clear_mdquote();
  static const int kMdQuoteFieldNumber = 33;
  const ::com::htsc::mdc::insight::model::MDQuote& mdquote() const;
  ::com::htsc::mdc::insight::model::MDQuote* mutable_mdquote();
  ::com::htsc::mdc::insight::model::MDQuote* release_mdquote();
  void set_allocated_mdquote(::com::htsc::mdc::insight::model::MDQuote* mdquote);

  // optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
  bool has_mdetfbasicinfo() const;
  void clear_mdetfbasicinfo();
  static const int kMdETFBasicInfoFieldNumber = 34;
  const ::com::htsc::mdc::insight::model::MDETFBasicInfo& mdetfbasicinfo() const;
  ::com::htsc::mdc::insight::model::MDETFBasicInfo* mutable_mdetfbasicinfo();
  ::com::htsc::mdc::insight::model::MDETFBasicInfo* release_mdetfbasicinfo();
  void set_allocated_mdetfbasicinfo(::com::htsc::mdc::insight::model::MDETFBasicInfo* mdetfbasicinfo);

  // optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
  bool has_mdfiquote() const;
  void clear_mdfiquote();
  static const int kMdFIQuoteFieldNumber = 35;
  const ::com::htsc::mdc::insight::model::MDFIQuote& mdfiquote() const;
  ::com::htsc::mdc::insight::model::MDFIQuote* mutable_mdfiquote();
  ::com::htsc::mdc::insight::model::MDFIQuote* release_mdfiquote();
  void set_allocated_mdfiquote(::com::htsc::mdc::insight::model::MDFIQuote* mdfiquote);

  // optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
  bool has_mdchipdistribution() const;
  void clear_mdchipdistribution();
  static const int kMdChipDistributionFieldNumber = 36;
  const ::com::htsc::mdc::insight::model::ADChipDistribution& mdchipdistribution() const;
  ::com::htsc::mdc::insight::model::ADChipDistribution* mutable_mdchipdistribution();
  ::com::htsc::mdc::insight::model::ADChipDistribution* release_mdchipdistribution();
  void set_allocated_mdchipdistribution(::com::htsc::mdc::insight::model::ADChipDistribution* mdchipdistribution);

  // optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
  bool has_mdwarrant() const;
  void clear_mdwarrant();
  static const int kMdWarrantFieldNumber = 37;
  const ::com::htsc::mdc::insight::model::MDWarrant& mdwarrant() const;
  ::com::htsc::mdc::insight::model::MDWarrant* mutable_mdwarrant();
  ::com::htsc::mdc::insight::model::MDWarrant* release_mdwarrant();
  void set_allocated_mdwarrant(::com::htsc::mdc::insight::model::MDWarrant* mdwarrant);

  // optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
  bool has_mdsecuritylending() const;
  void clear_mdsecuritylending();
  static const int kMdSecurityLendingFieldNumber = 38;
  const ::com::htsc::mdc::insight::model::MDSecurityLending& mdsecuritylending() const;
  ::com::htsc::mdc::insight::model::MDSecurityLending* mutable_mdsecuritylending();
  ::com::htsc::mdc::insight::model::MDSecurityLending* release_mdsecuritylending();
  void set_allocated_mdsecuritylending(::com::htsc::mdc::insight::model::MDSecurityLending* mdsecuritylending);

  // optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
  bool has_mdnews() const;
  void clear_mdnews();
  static const int kMdNewsFieldNumber = 39;
  const ::com::htsc::mdc::insight::model::ADNews& mdnews() const;
  ::com::htsc::mdc::insight::model::ADNews* mutable_mdnews();
  ::com::htsc::mdc::insight::model::ADNews* release_mdnews();
  void set_allocated_mdnews(::com::htsc::mdc::insight::model::ADNews* mdnews);

  // optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
  bool has_mdstaringresult() const;
  void clear_mdstaringresult();
  static const int kMdStaringResultFieldNumber = 40;
  const ::com::htsc::mdc::insight::model::ADStaringResult& mdstaringresult() const;
  ::com::htsc::mdc::insight::model::ADStaringResult* mutable_mdstaringresult();
  ::com::htsc::mdc::insight::model::ADStaringResult* release_mdstaringresult();
  void set_allocated_mdstaringresult(::com::htsc::mdc::insight::model::ADStaringResult* mdstaringresult);

  // optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
  bool has_mdderivedanalysis() const;
  void clear_mdderivedanalysis();
  static const int kMdDerivedAnalysisFieldNumber = 41;
  const ::com::htsc::mdc::insight::model::ADDerivedAnalysis& mdderivedanalysis() const;
  ::com::htsc::mdc::insight::model::ADDerivedAnalysis* mutable_mdderivedanalysis();
  ::com::htsc::mdc::insight::model::ADDerivedAnalysis* release_mdderivedanalysis();
  void set_allocated_mdderivedanalysis(::com::htsc::mdc::insight::model::ADDerivedAnalysis* mdderivedanalysis);

  // optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
  bool has_mdqbquote() const;
  void clear_mdqbquote();
  static const int kMdQBQuoteFieldNumber = 42;
  const ::com::htsc::mdc::insight::model::MDQBQuote& mdqbquote() const;
  ::com::htsc::mdc::insight::model::MDQBQuote* mutable_mdqbquote();
  ::com::htsc::mdc::insight::model::MDQBQuote* release_mdqbquote();
  void set_allocated_mdqbquote(::com::htsc::mdc::insight::model::MDQBQuote* mdqbquote);

  // optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
  bool has_mdqbtransaction() const;
  void clear_mdqbtransaction();
  static const int kMdQBTransactionFieldNumber = 43;
  const ::com::htsc::mdc::insight::model::MDQBTransaction& mdqbtransaction() const;
  ::com::htsc::mdc::insight::model::MDQBTransaction* mutable_mdqbtransaction();
  ::com::htsc::mdc::insight::model::MDQBTransaction* release_mdqbtransaction();
  void set_allocated_mdqbtransaction(::com::htsc::mdc::insight::model::MDQBTransaction* mdqbtransaction);

  // optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
  bool has_mdusaorder() const;
  void clear_mdusaorder();
  static const int kMdUSAOrderFieldNumber = 44;
  const ::com::htsc::mdc::insight::model::MDUSAOrder& mdusaorder() const;
  ::com::htsc::mdc::insight::model::MDUSAOrder* mutable_mdusaorder();
  ::com::htsc::mdc::insight::model::MDUSAOrder* release_mdusaorder();
  void set_allocated_mdusaorder(::com::htsc::mdc::insight::model::MDUSAOrder* mdusaorder);

  // optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
  bool has_mdusatransaction() const;
  void clear_mdusatransaction();
  static const int kMdUSATransactionFieldNumber = 45;
  const ::com::htsc::mdc::insight::model::MDUSATransaction& mdusatransaction() const;
  ::com::htsc::mdc::insight::model::MDUSATransaction* mutable_mdusatransaction();
  ::com::htsc::mdc::insight::model::MDUSATransaction* release_mdusatransaction();
  void set_allocated_mdusatransaction(::com::htsc::mdc::insight::model::MDUSATransaction* mdusatransaction);

  // optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
  bool has_mdslorder() const;
  void clear_mdslorder();
  static const int kMdSLOrderFieldNumber = 46;
  const ::com::htsc::mdc::insight::model::MDSLOrder& mdslorder() const;
  ::com::htsc::mdc::insight::model::MDSLOrder* mutable_mdslorder();
  ::com::htsc::mdc::insight::model::MDSLOrder* release_mdslorder();
  void set_allocated_mdslorder(::com::htsc::mdc::insight::model::MDSLOrder* mdslorder);

  // optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
  bool has_mdsltransaction() const;
  void clear_mdsltransaction();
  static const int kMdSLTransactionFieldNumber = 47;
  const ::com::htsc::mdc::insight::model::MDSLTransaction& mdsltransaction() const;
  ::com::htsc::mdc::insight::model::MDSLTransaction* mutable_mdsltransaction();
  ::com::htsc::mdc::insight::model::MDSLTransaction* release_mdsltransaction();
  void set_allocated_mdsltransaction(::com::htsc::mdc::insight::model::MDSLTransaction* mdsltransaction);

  // optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
  bool has_mdhkgreymarket() const;
  void clear_mdhkgreymarket();
  static const int kMdHKGreyMarketFieldNumber = 48;
  const ::com::htsc::mdc::insight::model::MDHKGreyMarket& mdhkgreymarket() const;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket* mutable_mdhkgreymarket();
  ::com::htsc::mdc::insight::model::MDHKGreyMarket* release_mdhkgreymarket();
  void set_allocated_mdhkgreymarket(::com::htsc::mdc::insight::model::MDHKGreyMarket* mdhkgreymarket);

  // optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
  bool has_mdslindicativequote() const;
  void clear_mdslindicativequote();
  static const int kMdSLIndicativeQuoteFieldNumber = 49;
  const ::com::htsc::mdc::insight::model::MDSLIndicativeQuote& mdslindicativequote() const;
  ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* mutable_mdslindicativequote();
  ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* release_mdslindicativequote();
  void set_allocated_mdslindicativequote(::com::htsc::mdc::insight::model::MDSLIndicativeQuote* mdslindicativequote);

  // optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
  bool has_mdslstatistics() const;
  void clear_mdslstatistics();
  static const int kMdSLStatisticsFieldNumber = 50;
  const ::com::htsc::mdc::insight::model::MDSLStatistics& mdslstatistics() const;
  ::com::htsc::mdc::insight::model::MDSLStatistics* mutable_mdslstatistics();
  ::com::htsc::mdc::insight::model::MDSLStatistics* release_mdslstatistics();
  void set_allocated_mdslstatistics(::com::htsc::mdc::insight::model::MDSLStatistics* mdslstatistics);

  // optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
  bool has_mdusaquote() const;
  void clear_mdusaquote();
  static const int kMdUSAQuoteFieldNumber = 51;
  const ::com::htsc::mdc::insight::model::MDUSAQuote& mdusaquote() const;
  ::com::htsc::mdc::insight::model::MDUSAQuote* mutable_mdusaquote();
  ::com::htsc::mdc::insight::model::MDUSAQuote* release_mdusaquote();
  void set_allocated_mdusaquote(::com::htsc::mdc::insight::model::MDUSAQuote* mdusaquote);

  // optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
  bool has_mdslestimation() const;
  void clear_mdslestimation();
  static const int kMdSLEstimationFieldNumber = 52;
  const ::com::htsc::mdc::insight::model::MDSLEstimation& mdslestimation() const;
  ::com::htsc::mdc::insight::model::MDSLEstimation* mutable_mdslestimation();
  ::com::htsc::mdc::insight::model::MDSLEstimation* release_mdslestimation();
  void set_allocated_mdslestimation(::com::htsc::mdc::insight::model::MDSLEstimation* mdslestimation);

  // optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
  bool has_mdcnexdeal() const;
  void clear_mdcnexdeal();
  static const int kMdCnexDealFieldNumber = 53;
  const ::com::htsc::mdc::insight::model::MDCnexDeal& mdcnexdeal() const;
  ::com::htsc::mdc::insight::model::MDCnexDeal* mutable_mdcnexdeal();
  ::com::htsc::mdc::insight::model::MDCnexDeal* release_mdcnexdeal();
  void set_allocated_mdcnexdeal(::com::htsc::mdc::insight::model::MDCnexDeal* mdcnexdeal);

  // optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
  bool has_mdcnexquote() const;
  void clear_mdcnexquote();
  static const int kMdCnexQuoteFieldNumber = 54;
  const ::com::htsc::mdc::insight::model::MDCnexQuote& mdcnexquote() const;
  ::com::htsc::mdc::insight::model::MDCnexQuote* mutable_mdcnexquote();
  ::com::htsc::mdc::insight::model::MDCnexQuote* release_mdcnexquote();
  void set_allocated_mdcnexquote(::com::htsc::mdc::insight::model::MDCnexQuote* mdcnexquote);

  // optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
  bool has_mddelaysnapshot() const;
  void clear_mddelaysnapshot();
  static const int kMdDelaySnapshotFieldNumber = 55;
  const ::com::htsc::mdc::insight::model::MDDelaySnapshot& mddelaysnapshot() const;
  ::com::htsc::mdc::insight::model::MDDelaySnapshot* mutable_mddelaysnapshot();
  ::com::htsc::mdc::insight::model::MDDelaySnapshot* release_mddelaysnapshot();
  void set_allocated_mddelaysnapshot(::com::htsc::mdc::insight::model::MDDelaySnapshot* mddelaysnapshot);

  // optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
  bool has_mdhighaccuracyfuture() const;
  void clear_mdhighaccuracyfuture();
  static const int kMdHighAccuracyFutureFieldNumber = 56;
  const ::com::htsc::mdc::insight::model::MDHighAccuracyFuture& mdhighaccuracyfuture() const;
  ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* mutable_mdhighaccuracyfuture();
  ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* release_mdhighaccuracyfuture();
  void set_allocated_mdhighaccuracyfuture(::com::htsc::mdc::insight::model::MDHighAccuracyFuture* mdhighaccuracyfuture);

  // optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
  bool has_mdcfetsforex() const;
  void clear_mdcfetsforex();
  static const int kMdCfetsForexFieldNumber = 57;
  const ::com::htsc::mdc::insight::model::MDCfetsForex& mdcfetsforex() const;
  ::com::htsc::mdc::insight::model::MDCfetsForex* mutable_mdcfetsforex();
  ::com::htsc::mdc::insight::model::MDCfetsForex* release_mdcfetsforex();
  void set_allocated_mdcfetsforex(::com::htsc::mdc::insight::model::MDCfetsForex* mdcfetsforex);

  // optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
  bool has_mdcfetsfxsnapshot() const;
  void clear_mdcfetsfxsnapshot();
  static const int kMdCfetsFxSnapshotFieldNumber = 58;
  const ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot& mdcfetsfxsnapshot() const;
  ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* mutable_mdcfetsfxsnapshot();
  ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* release_mdcfetsfxsnapshot();
  void set_allocated_mdcfetsfxsnapshot(::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* mdcfetsfxsnapshot);

  // optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
  bool has_mdcfetsfxquote() const;
  void clear_mdcfetsfxquote();
  static const int kMdCfetsFxQuoteFieldNumber = 59;
  const ::com::htsc::mdc::insight::model::MDCfetsFxQuote& mdcfetsfxquote() const;
  ::com::htsc::mdc::insight::model::MDCfetsFxQuote* mutable_mdcfetsfxquote();
  ::com::htsc::mdc::insight::model::MDCfetsFxQuote* release_mdcfetsfxquote();
  void set_allocated_mdcfetsfxquote(::com::htsc::mdc::insight::model::MDCfetsFxQuote* mdcfetsfxquote);

  // optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
  bool has_spfuture() const;
  void clear_spfuture();
  static const int kSpFutureFieldNumber = 60;
  const ::com::htsc::mdc::insight::model::SPFuture& spfuture() const;
  ::com::htsc::mdc::insight::model::SPFuture* mutable_spfuture();
  ::com::htsc::mdc::insight::model::SPFuture* release_spfuture();
  void set_allocated_spfuture(::com::htsc::mdc::insight::model::SPFuture* spfuture);

  // optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
  bool has_mdcfetsbenchmark() const;
  void clear_mdcfetsbenchmark();
  static const int kMdCfetsBenchmarkFieldNumber = 61;
  const ::com::htsc::mdc::insight::model::MDCfetsBenchmark& mdcfetsbenchmark() const;
  ::com::htsc::mdc::insight::model::MDCfetsBenchmark* mutable_mdcfetsbenchmark();
  ::com::htsc::mdc::insight::model::MDCfetsBenchmark* release_mdcfetsbenchmark();
  void set_allocated_mdcfetsbenchmark(::com::htsc::mdc::insight::model::MDCfetsBenchmark* mdcfetsbenchmark);

  // optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
  bool has_mdcfetsbonddeal() const;
  void clear_mdcfetsbonddeal();
  static const int kMdCfetsBondDealFieldNumber = 62;
  const ::com::htsc::mdc::insight::model::MDCfetsBondDeal& mdcfetsbonddeal() const;
  ::com::htsc::mdc::insight::model::MDCfetsBondDeal* mutable_mdcfetsbonddeal();
  ::com::htsc::mdc::insight::model::MDCfetsBondDeal* release_mdcfetsbonddeal();
  void set_allocated_mdcfetsbonddeal(::com::htsc::mdc::insight::model::MDCfetsBondDeal* mdcfetsbonddeal);

  // optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
  bool has_mdcfetsbondsnapshot() const;
  void clear_mdcfetsbondsnapshot();
  static const int kMdCfetsBondSnapshotFieldNumber = 63;
  const ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot& mdcfetsbondsnapshot() const;
  ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* mutable_mdcfetsbondsnapshot();
  ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* release_mdcfetsbondsnapshot();
  void set_allocated_mdcfetsbondsnapshot(::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* mdcfetsbondsnapshot);

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
  bool has_mdcfetscurrencydeal() const;
  void clear_mdcfetscurrencydeal();
  static const int kMdCfetsCurrencyDealFieldNumber = 64;
  const ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal& mdcfetscurrencydeal() const;
  ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* mutable_mdcfetscurrencydeal();
  ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* release_mdcfetscurrencydeal();
  void set_allocated_mdcfetscurrencydeal(::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* mdcfetscurrencydeal);

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
  bool has_mdcfetscurrencysnapshot() const;
  void clear_mdcfetscurrencysnapshot();
  static const int kMdCfetsCurrencySnapshotFieldNumber = 65;
  const ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot& mdcfetscurrencysnapshot() const;
  ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* mutable_mdcfetscurrencysnapshot();
  ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* release_mdcfetscurrencysnapshot();
  void set_allocated_mdcfetscurrencysnapshot(::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* mdcfetscurrencysnapshot);

  // optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
  bool has_mdcfetsodmsnapshot() const;
  void clear_mdcfetsodmsnapshot();
  static const int kMdCfetsODMSnapshotFieldNumber = 66;
  const ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot& mdcfetsodmsnapshot() const;
  ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* mutable_mdcfetsodmsnapshot();
  ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* release_mdcfetsodmsnapshot();
  void set_allocated_mdcfetsodmsnapshot(::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* mdcfetsodmsnapshot);

  // optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
  bool has_mdcfetsqdmquote() const;
  void clear_mdcfetsqdmquote();
  static const int kMdCfetsQDMQuoteFieldNumber = 67;
  const ::com::htsc::mdc::insight::model::MDCfetsQDMQuote& mdcfetsqdmquote() const;
  ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* mutable_mdcfetsqdmquote();
  ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* release_mdcfetsqdmquote();
  void set_allocated_mdcfetsqdmquote(::com::htsc::mdc::insight::model::MDCfetsQDMQuote* mdcfetsqdmquote);

  // optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
  bool has_mdcfetsratedeal() const;
  void clear_mdcfetsratedeal();
  static const int kMdCfetsRateDealFieldNumber = 68;
  const ::com::htsc::mdc::insight::model::MDCfetsRateDeal& mdcfetsratedeal() const;
  ::com::htsc::mdc::insight::model::MDCfetsRateDeal* mutable_mdcfetsratedeal();
  ::com::htsc::mdc::insight::model::MDCfetsRateDeal* release_mdcfetsratedeal();
  void set_allocated_mdcfetsratedeal(::com::htsc::mdc::insight::model::MDCfetsRateDeal* mdcfetsratedeal);

  // optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
  bool has_mdcfetsratesnapshot() const;
  void clear_mdcfetsratesnapshot();
  static const int kMdCfetsRateSnapshotFieldNumber = 69;
  const ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot& mdcfetsratesnapshot() const;
  ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* mutable_mdcfetsratesnapshot();
  ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* release_mdcfetsratesnapshot();
  void set_allocated_mdcfetsratesnapshot(::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* mdcfetsratesnapshot);

  // optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
  bool has_mdcfetsfxcnymiddleprice() const;
  void clear_mdcfetsfxcnymiddleprice();
  static const int kMdCfetsFxCnyMiddlePriceFieldNumber = 70;
  const ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice& mdcfetsfxcnymiddleprice() const;
  ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* mutable_mdcfetsfxcnymiddleprice();
  ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* release_mdcfetsfxcnymiddleprice();
  void set_allocated_mdcfetsfxcnymiddleprice(::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* mdcfetsfxcnymiddleprice);

  // optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
  bool has_mdiopvsnapshot() const;
  void clear_mdiopvsnapshot();
  static const int kMdIopvSnapshotFieldNumber = 71;
  const ::com::htsc::mdc::insight::model::MDIopvSnapshot& mdiopvsnapshot() const;
  ::com::htsc::mdc::insight::model::MDIopvSnapshot* mutable_mdiopvsnapshot();
  ::com::htsc::mdc::insight::model::MDIopvSnapshot* release_mdiopvsnapshot();
  void set_allocated_mdiopvsnapshot(::com::htsc::mdc::insight::model::MDIopvSnapshot* mdiopvsnapshot);

  // optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
  bool has_mdchinabondbenchmark() const;
  void clear_mdchinabondbenchmark();
  static const int kMdChinaBondBenchmarkFieldNumber = 72;
  const ::com::htsc::mdc::insight::model::MDChinaBondBenchmark& mdchinabondbenchmark() const;
  ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* mutable_mdchinabondbenchmark();
  ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* release_mdchinabondbenchmark();
  void set_allocated_mdchinabondbenchmark(::com::htsc::mdc::insight::model::MDChinaBondBenchmark* mdchinabondbenchmark);

  // optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
  bool has_mdicetrace() const;
  void clear_mdicetrace();
  static const int kMdIceTraceFieldNumber = 73;
  const ::com::htsc::mdc::insight::model::MDIceTrace& mdicetrace() const;
  ::com::htsc::mdc::insight::model::MDIceTrace* mutable_mdicetrace();
  ::com::htsc::mdc::insight::model::MDIceTrace* release_mdicetrace();
  void set_allocated_mdicetrace(::com::htsc::mdc::insight::model::MDIceTrace* mdicetrace);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MarketData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::com::htsc::mdc::insight::model::MDStock* mdstock_;
  ::com::htsc::mdc::insight::model::MDIndex* mdindex_;
  ::com::htsc::mdc::insight::model::MDBond* mdbond_;
  ::com::htsc::mdc::insight::model::MDFund* mdfund_;
  ::com::htsc::mdc::insight::model::MDOption* mdoption_;
  ::com::htsc::mdc::insight::model::MDFuture* mdfuture_;
  ::com::htsc::mdc::insight::model::MDTransaction* mdtransaction_;
  ::com::htsc::mdc::insight::model::MDOrder* mdorder_;
  ::com::htsc::mdc::insight::model::ADKLine* mdkline_;
  ::com::htsc::mdc::insight::model::ADTwap* mdtwap_;
  ::com::htsc::mdc::insight::model::ADVwap* mdvwap_;
  ::com::htsc::mdc::insight::model::MDBasicInfo* mdconstant_;
  ::com::htsc::mdc::insight::model::MDSimpleTick* mdsimpletick_;
  ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* mdupsdownsanalysis_;
  ::com::htsc::mdc::insight::model::ADIndicatorsRanking* mdindicatorsranking_;
  ::com::htsc::mdc::insight::model::DynamicPacket* dynamicpacket_;
  ::com::htsc::mdc::insight::model::ADVolumeByPrice* mdvolumebyprice_;
  ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* mdfundflowanalysis_;
  ::com::htsc::mdc::insight::model::MDForex* mdforex_;
  ::com::htsc::mdc::insight::model::MDSpot* mdspot_;
  ::com::htsc::mdc::insight::model::MDRate* mdrate_;
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* orderbooksnapshot_;
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* orderbooksnapshotwithtick_;
  ::com::htsc::mdc::insight::model::MDQuote* mdquote_;
  ::com::htsc::mdc::insight::model::MDETFBasicInfo* mdetfbasicinfo_;
  ::com::htsc::mdc::insight::model::MDFIQuote* mdfiquote_;
  ::com::htsc::mdc::insight::model::ADChipDistribution* mdchipdistribution_;
  ::com::htsc::mdc::insight::model::MDWarrant* mdwarrant_;
  ::com::htsc::mdc::insight::model::MDSecurityLending* mdsecuritylending_;
  ::com::htsc::mdc::insight::model::ADNews* mdnews_;
  ::com::htsc::mdc::insight::model::ADStaringResult* mdstaringresult_;
  ::com::htsc::mdc::insight::model::ADDerivedAnalysis* mdderivedanalysis_;
  ::com::htsc::mdc::insight::model::MDQBQuote* mdqbquote_;
  ::com::htsc::mdc::insight::model::MDQBTransaction* mdqbtransaction_;
  ::com::htsc::mdc::insight::model::MDUSAOrder* mdusaorder_;
  ::com::htsc::mdc::insight::model::MDUSATransaction* mdusatransaction_;
  ::com::htsc::mdc::insight::model::MDSLOrder* mdslorder_;
  ::com::htsc::mdc::insight::model::MDSLTransaction* mdsltransaction_;
  ::com::htsc::mdc::insight::model::MDHKGreyMarket* mdhkgreymarket_;
  ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* mdslindicativequote_;
  ::com::htsc::mdc::insight::model::MDSLStatistics* mdslstatistics_;
  ::com::htsc::mdc::insight::model::MDUSAQuote* mdusaquote_;
  ::com::htsc::mdc::insight::model::MDSLEstimation* mdslestimation_;
  ::com::htsc::mdc::insight::model::MDCnexDeal* mdcnexdeal_;
  ::com::htsc::mdc::insight::model::MDCnexQuote* mdcnexquote_;
  ::com::htsc::mdc::insight::model::MDDelaySnapshot* mddelaysnapshot_;
  ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* mdhighaccuracyfuture_;
  ::com::htsc::mdc::insight::model::MDCfetsForex* mdcfetsforex_;
  ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* mdcfetsfxsnapshot_;
  ::com::htsc::mdc::insight::model::MDCfetsFxQuote* mdcfetsfxquote_;
  ::com::htsc::mdc::insight::model::SPFuture* spfuture_;
  ::com::htsc::mdc::insight::model::MDCfetsBenchmark* mdcfetsbenchmark_;
  ::com::htsc::mdc::insight::model::MDCfetsBondDeal* mdcfetsbonddeal_;
  ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* mdcfetsbondsnapshot_;
  ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* mdcfetscurrencydeal_;
  ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* mdcfetscurrencysnapshot_;
  ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* mdcfetsodmsnapshot_;
  ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* mdcfetsqdmquote_;
  ::com::htsc::mdc::insight::model::MDCfetsRateDeal* mdcfetsratedeal_;
  ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* mdcfetsratesnapshot_;
  ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* mdcfetsfxcnymiddleprice_;
  ::com::htsc::mdc::insight::model::MDIopvSnapshot* mdiopvsnapshot_;
  ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* mdchinabondbenchmark_;
  ::com::htsc::mdc::insight::model::MDIceTrace* mdicetrace_;
  ::google::protobuf::int64 messagechannelnumber_;
  int marketdatatype_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MarketData_2eproto_impl();
  friend void  protobuf_AddDesc_MarketData_2eproto_impl();
  friend void protobuf_AssignDesc_MarketData_2eproto();
  friend void protobuf_ShutdownFile_MarketData_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MarketData> MarketData_default_instance_;

// -------------------------------------------------------------------

class MarketDataStream : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MarketDataStream) */ {
 public:
  MarketDataStream();
  virtual ~MarketDataStream();

  MarketDataStream(const MarketDataStream& from);

  inline MarketDataStream& operator=(const MarketDataStream& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarketDataStream& default_instance();

  static const MarketDataStream* internal_default_instance();

  void Swap(MarketDataStream* other);

  // implements Message ----------------------------------------------

  inline MarketDataStream* New() const { return New(NULL); }

  MarketDataStream* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MarketDataStream& from);
  void MergeFrom(const MarketDataStream& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MarketDataStream* other);
  void UnsafeMergeFrom(const MarketDataStream& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional bool isCompressed = 1;
  void clear_iscompressed();
  static const int kIsCompressedFieldNumber = 1;
  bool iscompressed() const;
  void set_iscompressed(bool value);

  // optional int32 originalLength = 2;
  void clear_originallength();
  static const int kOriginalLengthFieldNumber = 2;
  ::google::protobuf::int32 originallength() const;
  void set_originallength(::google::protobuf::int32 value);

  // optional bytes compressedData = 3;
  void clear_compresseddata();
  static const int kCompressedDataFieldNumber = 3;
  const ::std::string& compresseddata() const;
  void set_compresseddata(const ::std::string& value);
  void set_compresseddata(const char* value);
  void set_compresseddata(const void* value, size_t size);
  ::std::string* mutable_compresseddata();
  ::std::string* release_compresseddata();
  void set_allocated_compresseddata(::std::string* compresseddata);

  // optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
  bool has_marketdatalist() const;
  void clear_marketdatalist();
  static const int kMarketDataListFieldNumber = 4;
  const ::com::htsc::mdc::insight::model::MarketDataList& marketdatalist() const;
  ::com::htsc::mdc::insight::model::MarketDataList* mutable_marketdatalist();
  ::com::htsc::mdc::insight::model::MarketDataList* release_marketdatalist();
  void set_allocated_marketdatalist(::com::htsc::mdc::insight::model::MarketDataList* marketdatalist);

  // optional int32 totalNumber = 5;
  void clear_totalnumber();
  static const int kTotalNumberFieldNumber = 5;
  ::google::protobuf::int32 totalnumber() const;
  void set_totalnumber(::google::protobuf::int32 value);

  // optional int32 serial = 6;
  void clear_serial();
  static const int kSerialFieldNumber = 6;
  ::google::protobuf::int32 serial() const;
  void set_serial(::google::protobuf::int32 value);

  // optional bool isFinished = 7;
  void clear_isfinished();
  static const int kIsFinishedFieldNumber = 7;
  bool isfinished() const;
  void set_isfinished(bool value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MarketDataStream)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr compresseddata_;
  ::com::htsc::mdc::insight::model::MarketDataList* marketdatalist_;
  ::google::protobuf::int32 originallength_;
  bool iscompressed_;
  bool isfinished_;
  ::google::protobuf::int32 totalnumber_;
  ::google::protobuf::int32 serial_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MarketData_2eproto_impl();
  friend void  protobuf_AddDesc_MarketData_2eproto_impl();
  friend void protobuf_AssignDesc_MarketData_2eproto();
  friend void protobuf_ShutdownFile_MarketData_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MarketDataStream> MarketDataStream_default_instance_;

// -------------------------------------------------------------------

class MarketDataList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MarketDataList) */ {
 public:
  MarketDataList();
  virtual ~MarketDataList();

  MarketDataList(const MarketDataList& from);

  inline MarketDataList& operator=(const MarketDataList& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MarketDataList& default_instance();

  static const MarketDataList* internal_default_instance();

  void Swap(MarketDataList* other);

  // implements Message ----------------------------------------------

  inline MarketDataList* New() const { return New(NULL); }

  MarketDataList* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MarketDataList& from);
  void MergeFrom(const MarketDataList& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MarketDataList* other);
  void UnsafeMergeFrom(const MarketDataList& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
  int marketdatas_size() const;
  void clear_marketdatas();
  static const int kMarketDatasFieldNumber = 1;
  const ::com::htsc::mdc::insight::model::MarketData& marketdatas(int index) const;
  ::com::htsc::mdc::insight::model::MarketData* mutable_marketdatas(int index);
  ::com::htsc::mdc::insight::model::MarketData* add_marketdatas();
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >*
      mutable_marketdatas();
  const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >&
      marketdatas() const;

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MarketDataList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData > marketdatas_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MarketData_2eproto_impl();
  friend void  protobuf_AddDesc_MarketData_2eproto_impl();
  friend void protobuf_AssignDesc_MarketData_2eproto();
  friend void protobuf_ShutdownFile_MarketData_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MarketDataList> MarketDataList_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketData

// optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
inline void MarketData::clear_marketdatatype() {
  marketdatatype_ = 0;
}
inline ::com::htsc::mdc::insight::model::EMarketDataType MarketData::marketdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.marketDataType)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatype_);
}
inline void MarketData::set_marketdatatype(::com::htsc::mdc::insight::model::EMarketDataType value) {
  
  marketdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketData.marketDataType)
}

// optional int64 MessageChannelNumber = 2;
inline void MarketData::clear_messagechannelnumber() {
  messagechannelnumber_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MarketData::messagechannelnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.MessageChannelNumber)
  return messagechannelnumber_;
}
inline void MarketData::set_messagechannelnumber(::google::protobuf::int64 value) {
  
  messagechannelnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketData.MessageChannelNumber)
}

// optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
inline bool MarketData::has_mdstock() const {
  return this != internal_default_instance() && mdstock_ != NULL;
}
inline void MarketData::clear_mdstock() {
  if (GetArenaNoVirtual() == NULL && mdstock_ != NULL) delete mdstock_;
  mdstock_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDStock& MarketData::mdstock() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdStock)
  return mdstock_ != NULL ? *mdstock_
                         : *::com::htsc::mdc::insight::model::MDStock::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDStock* MarketData::mutable_mdstock() {
  
  if (mdstock_ == NULL) {
    mdstock_ = new ::com::htsc::mdc::insight::model::MDStock;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdStock)
  return mdstock_;
}
inline ::com::htsc::mdc::insight::model::MDStock* MarketData::release_mdstock() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdStock)
  
  ::com::htsc::mdc::insight::model::MDStock* temp = mdstock_;
  mdstock_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdstock(::com::htsc::mdc::insight::model::MDStock* mdstock) {
  delete mdstock_;
  mdstock_ = mdstock;
  if (mdstock) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdStock)
}

// optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
inline bool MarketData::has_mdindex() const {
  return this != internal_default_instance() && mdindex_ != NULL;
}
inline void MarketData::clear_mdindex() {
  if (GetArenaNoVirtual() == NULL && mdindex_ != NULL) delete mdindex_;
  mdindex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDIndex& MarketData::mdindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIndex)
  return mdindex_ != NULL ? *mdindex_
                         : *::com::htsc::mdc::insight::model::MDIndex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDIndex* MarketData::mutable_mdindex() {
  
  if (mdindex_ == NULL) {
    mdindex_ = new ::com::htsc::mdc::insight::model::MDIndex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIndex)
  return mdindex_;
}
inline ::com::htsc::mdc::insight::model::MDIndex* MarketData::release_mdindex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIndex)
  
  ::com::htsc::mdc::insight::model::MDIndex* temp = mdindex_;
  mdindex_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdindex(::com::htsc::mdc::insight::model::MDIndex* mdindex) {
  delete mdindex_;
  mdindex_ = mdindex;
  if (mdindex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIndex)
}

// optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
inline bool MarketData::has_mdbond() const {
  return this != internal_default_instance() && mdbond_ != NULL;
}
inline void MarketData::clear_mdbond() {
  if (GetArenaNoVirtual() == NULL && mdbond_ != NULL) delete mdbond_;
  mdbond_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDBond& MarketData::mdbond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdBond)
  return mdbond_ != NULL ? *mdbond_
                         : *::com::htsc::mdc::insight::model::MDBond::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDBond* MarketData::mutable_mdbond() {
  
  if (mdbond_ == NULL) {
    mdbond_ = new ::com::htsc::mdc::insight::model::MDBond;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdBond)
  return mdbond_;
}
inline ::com::htsc::mdc::insight::model::MDBond* MarketData::release_mdbond() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdBond)
  
  ::com::htsc::mdc::insight::model::MDBond* temp = mdbond_;
  mdbond_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdbond(::com::htsc::mdc::insight::model::MDBond* mdbond) {
  delete mdbond_;
  mdbond_ = mdbond;
  if (mdbond) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdBond)
}

// optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
inline bool MarketData::has_mdfund() const {
  return this != internal_default_instance() && mdfund_ != NULL;
}
inline void MarketData::clear_mdfund() {
  if (GetArenaNoVirtual() == NULL && mdfund_ != NULL) delete mdfund_;
  mdfund_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDFund& MarketData::mdfund() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFund)
  return mdfund_ != NULL ? *mdfund_
                         : *::com::htsc::mdc::insight::model::MDFund::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDFund* MarketData::mutable_mdfund() {
  
  if (mdfund_ == NULL) {
    mdfund_ = new ::com::htsc::mdc::insight::model::MDFund;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFund)
  return mdfund_;
}
inline ::com::htsc::mdc::insight::model::MDFund* MarketData::release_mdfund() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFund)
  
  ::com::htsc::mdc::insight::model::MDFund* temp = mdfund_;
  mdfund_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdfund(::com::htsc::mdc::insight::model::MDFund* mdfund) {
  delete mdfund_;
  mdfund_ = mdfund;
  if (mdfund) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFund)
}

// optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
inline bool MarketData::has_mdoption() const {
  return this != internal_default_instance() && mdoption_ != NULL;
}
inline void MarketData::clear_mdoption() {
  if (GetArenaNoVirtual() == NULL && mdoption_ != NULL) delete mdoption_;
  mdoption_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDOption& MarketData::mdoption() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdOption)
  return mdoption_ != NULL ? *mdoption_
                         : *::com::htsc::mdc::insight::model::MDOption::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDOption* MarketData::mutable_mdoption() {
  
  if (mdoption_ == NULL) {
    mdoption_ = new ::com::htsc::mdc::insight::model::MDOption;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdOption)
  return mdoption_;
}
inline ::com::htsc::mdc::insight::model::MDOption* MarketData::release_mdoption() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdOption)
  
  ::com::htsc::mdc::insight::model::MDOption* temp = mdoption_;
  mdoption_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdoption(::com::htsc::mdc::insight::model::MDOption* mdoption) {
  delete mdoption_;
  mdoption_ = mdoption;
  if (mdoption) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdOption)
}

// optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
inline bool MarketData::has_mdfuture() const {
  return this != internal_default_instance() && mdfuture_ != NULL;
}
inline void MarketData::clear_mdfuture() {
  if (GetArenaNoVirtual() == NULL && mdfuture_ != NULL) delete mdfuture_;
  mdfuture_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDFuture& MarketData::mdfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFuture)
  return mdfuture_ != NULL ? *mdfuture_
                         : *::com::htsc::mdc::insight::model::MDFuture::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDFuture* MarketData::mutable_mdfuture() {
  
  if (mdfuture_ == NULL) {
    mdfuture_ = new ::com::htsc::mdc::insight::model::MDFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFuture)
  return mdfuture_;
}
inline ::com::htsc::mdc::insight::model::MDFuture* MarketData::release_mdfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFuture)
  
  ::com::htsc::mdc::insight::model::MDFuture* temp = mdfuture_;
  mdfuture_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdfuture(::com::htsc::mdc::insight::model::MDFuture* mdfuture) {
  delete mdfuture_;
  mdfuture_ = mdfuture;
  if (mdfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFuture)
}

// optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
inline bool MarketData::has_mdtransaction() const {
  return this != internal_default_instance() && mdtransaction_ != NULL;
}
inline void MarketData::clear_mdtransaction() {
  if (GetArenaNoVirtual() == NULL && mdtransaction_ != NULL) delete mdtransaction_;
  mdtransaction_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDTransaction& MarketData::mdtransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  return mdtransaction_ != NULL ? *mdtransaction_
                         : *::com::htsc::mdc::insight::model::MDTransaction::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDTransaction* MarketData::mutable_mdtransaction() {
  
  if (mdtransaction_ == NULL) {
    mdtransaction_ = new ::com::htsc::mdc::insight::model::MDTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  return mdtransaction_;
}
inline ::com::htsc::mdc::insight::model::MDTransaction* MarketData::release_mdtransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  
  ::com::htsc::mdc::insight::model::MDTransaction* temp = mdtransaction_;
  mdtransaction_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdtransaction(::com::htsc::mdc::insight::model::MDTransaction* mdtransaction) {
  delete mdtransaction_;
  mdtransaction_ = mdtransaction;
  if (mdtransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdTransaction)
}

// optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
inline bool MarketData::has_mdorder() const {
  return this != internal_default_instance() && mdorder_ != NULL;
}
inline void MarketData::clear_mdorder() {
  if (GetArenaNoVirtual() == NULL && mdorder_ != NULL) delete mdorder_;
  mdorder_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDOrder& MarketData::mdorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdOrder)
  return mdorder_ != NULL ? *mdorder_
                         : *::com::htsc::mdc::insight::model::MDOrder::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDOrder* MarketData::mutable_mdorder() {
  
  if (mdorder_ == NULL) {
    mdorder_ = new ::com::htsc::mdc::insight::model::MDOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdOrder)
  return mdorder_;
}
inline ::com::htsc::mdc::insight::model::MDOrder* MarketData::release_mdorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdOrder)
  
  ::com::htsc::mdc::insight::model::MDOrder* temp = mdorder_;
  mdorder_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdorder(::com::htsc::mdc::insight::model::MDOrder* mdorder) {
  delete mdorder_;
  mdorder_ = mdorder;
  if (mdorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdOrder)
}

// optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
inline bool MarketData::has_mdkline() const {
  return this != internal_default_instance() && mdkline_ != NULL;
}
inline void MarketData::clear_mdkline() {
  if (GetArenaNoVirtual() == NULL && mdkline_ != NULL) delete mdkline_;
  mdkline_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADKLine& MarketData::mdkline() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdKLine)
  return mdkline_ != NULL ? *mdkline_
                         : *::com::htsc::mdc::insight::model::ADKLine::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADKLine* MarketData::mutable_mdkline() {
  
  if (mdkline_ == NULL) {
    mdkline_ = new ::com::htsc::mdc::insight::model::ADKLine;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdKLine)
  return mdkline_;
}
inline ::com::htsc::mdc::insight::model::ADKLine* MarketData::release_mdkline() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdKLine)
  
  ::com::htsc::mdc::insight::model::ADKLine* temp = mdkline_;
  mdkline_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdkline(::com::htsc::mdc::insight::model::ADKLine* mdkline) {
  delete mdkline_;
  mdkline_ = mdkline;
  if (mdkline) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdKLine)
}

// optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
inline bool MarketData::has_mdtwap() const {
  return this != internal_default_instance() && mdtwap_ != NULL;
}
inline void MarketData::clear_mdtwap() {
  if (GetArenaNoVirtual() == NULL && mdtwap_ != NULL) delete mdtwap_;
  mdtwap_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADTwap& MarketData::mdtwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdTwap)
  return mdtwap_ != NULL ? *mdtwap_
                         : *::com::htsc::mdc::insight::model::ADTwap::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADTwap* MarketData::mutable_mdtwap() {
  
  if (mdtwap_ == NULL) {
    mdtwap_ = new ::com::htsc::mdc::insight::model::ADTwap;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdTwap)
  return mdtwap_;
}
inline ::com::htsc::mdc::insight::model::ADTwap* MarketData::release_mdtwap() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdTwap)
  
  ::com::htsc::mdc::insight::model::ADTwap* temp = mdtwap_;
  mdtwap_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdtwap(::com::htsc::mdc::insight::model::ADTwap* mdtwap) {
  delete mdtwap_;
  mdtwap_ = mdtwap;
  if (mdtwap) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdTwap)
}

// optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
inline bool MarketData::has_mdvwap() const {
  return this != internal_default_instance() && mdvwap_ != NULL;
}
inline void MarketData::clear_mdvwap() {
  if (GetArenaNoVirtual() == NULL && mdvwap_ != NULL) delete mdvwap_;
  mdvwap_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADVwap& MarketData::mdvwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdVwap)
  return mdvwap_ != NULL ? *mdvwap_
                         : *::com::htsc::mdc::insight::model::ADVwap::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADVwap* MarketData::mutable_mdvwap() {
  
  if (mdvwap_ == NULL) {
    mdvwap_ = new ::com::htsc::mdc::insight::model::ADVwap;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdVwap)
  return mdvwap_;
}
inline ::com::htsc::mdc::insight::model::ADVwap* MarketData::release_mdvwap() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdVwap)
  
  ::com::htsc::mdc::insight::model::ADVwap* temp = mdvwap_;
  mdvwap_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdvwap(::com::htsc::mdc::insight::model::ADVwap* mdvwap) {
  delete mdvwap_;
  mdvwap_ = mdvwap;
  if (mdvwap) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdVwap)
}

// optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
inline bool MarketData::has_mdconstant() const {
  return this != internal_default_instance() && mdconstant_ != NULL;
}
inline void MarketData::clear_mdconstant() {
  if (GetArenaNoVirtual() == NULL && mdconstant_ != NULL) delete mdconstant_;
  mdconstant_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDBasicInfo& MarketData::mdconstant() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdConstant)
  return mdconstant_ != NULL ? *mdconstant_
                         : *::com::htsc::mdc::insight::model::MDBasicInfo::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDBasicInfo* MarketData::mutable_mdconstant() {
  
  if (mdconstant_ == NULL) {
    mdconstant_ = new ::com::htsc::mdc::insight::model::MDBasicInfo;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdConstant)
  return mdconstant_;
}
inline ::com::htsc::mdc::insight::model::MDBasicInfo* MarketData::release_mdconstant() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdConstant)
  
  ::com::htsc::mdc::insight::model::MDBasicInfo* temp = mdconstant_;
  mdconstant_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdconstant(::com::htsc::mdc::insight::model::MDBasicInfo* mdconstant) {
  delete mdconstant_;
  mdconstant_ = mdconstant;
  if (mdconstant) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdConstant)
}

// optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
inline bool MarketData::has_mdsimpletick() const {
  return this != internal_default_instance() && mdsimpletick_ != NULL;
}
inline void MarketData::clear_mdsimpletick() {
  if (GetArenaNoVirtual() == NULL && mdsimpletick_ != NULL) delete mdsimpletick_;
  mdsimpletick_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSimpleTick& MarketData::mdsimpletick() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  return mdsimpletick_ != NULL ? *mdsimpletick_
                         : *::com::htsc::mdc::insight::model::MDSimpleTick::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSimpleTick* MarketData::mutable_mdsimpletick() {
  
  if (mdsimpletick_ == NULL) {
    mdsimpletick_ = new ::com::htsc::mdc::insight::model::MDSimpleTick;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  return mdsimpletick_;
}
inline ::com::htsc::mdc::insight::model::MDSimpleTick* MarketData::release_mdsimpletick() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  
  ::com::htsc::mdc::insight::model::MDSimpleTick* temp = mdsimpletick_;
  mdsimpletick_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdsimpletick(::com::htsc::mdc::insight::model::MDSimpleTick* mdsimpletick) {
  delete mdsimpletick_;
  mdsimpletick_ = mdsimpletick;
  if (mdsimpletick) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
inline bool MarketData::has_mdupsdownsanalysis() const {
  return this != internal_default_instance() && mdupsdownsanalysis_ != NULL;
}
inline void MarketData::clear_mdupsdownsanalysis() {
  if (GetArenaNoVirtual() == NULL && mdupsdownsanalysis_ != NULL) delete mdupsdownsanalysis_;
  mdupsdownsanalysis_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis& MarketData::mdupsdownsanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  return mdupsdownsanalysis_ != NULL ? *mdupsdownsanalysis_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsAnalysis::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* MarketData::mutable_mdupsdownsanalysis() {
  
  if (mdupsdownsanalysis_ == NULL) {
    mdupsdownsanalysis_ = new ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  return mdupsdownsanalysis_;
}
inline ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* MarketData::release_mdupsdownsanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* temp = mdupsdownsanalysis_;
  mdupsdownsanalysis_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdupsdownsanalysis(::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* mdupsdownsanalysis) {
  delete mdupsdownsanalysis_;
  mdupsdownsanalysis_ = mdupsdownsanalysis;
  if (mdupsdownsanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
}

// optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
inline bool MarketData::has_mdindicatorsranking() const {
  return this != internal_default_instance() && mdindicatorsranking_ != NULL;
}
inline void MarketData::clear_mdindicatorsranking() {
  if (GetArenaNoVirtual() == NULL && mdindicatorsranking_ != NULL) delete mdindicatorsranking_;
  mdindicatorsranking_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADIndicatorsRanking& MarketData::mdindicatorsranking() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  return mdindicatorsranking_ != NULL ? *mdindicatorsranking_
                         : *::com::htsc::mdc::insight::model::ADIndicatorsRanking::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADIndicatorsRanking* MarketData::mutable_mdindicatorsranking() {
  
  if (mdindicatorsranking_ == NULL) {
    mdindicatorsranking_ = new ::com::htsc::mdc::insight::model::ADIndicatorsRanking;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  return mdindicatorsranking_;
}
inline ::com::htsc::mdc::insight::model::ADIndicatorsRanking* MarketData::release_mdindicatorsranking() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  
  ::com::htsc::mdc::insight::model::ADIndicatorsRanking* temp = mdindicatorsranking_;
  mdindicatorsranking_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdindicatorsranking(::com::htsc::mdc::insight::model::ADIndicatorsRanking* mdindicatorsranking) {
  delete mdindicatorsranking_;
  mdindicatorsranking_ = mdindicatorsranking;
  if (mdindicatorsranking) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
}

// optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
inline bool MarketData::has_dynamicpacket() const {
  return this != internal_default_instance() && dynamicpacket_ != NULL;
}
inline void MarketData::clear_dynamicpacket() {
  if (GetArenaNoVirtual() == NULL && dynamicpacket_ != NULL) delete dynamicpacket_;
  dynamicpacket_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::DynamicPacket& MarketData::dynamicpacket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  return dynamicpacket_ != NULL ? *dynamicpacket_
                         : *::com::htsc::mdc::insight::model::DynamicPacket::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::DynamicPacket* MarketData::mutable_dynamicpacket() {
  
  if (dynamicpacket_ == NULL) {
    dynamicpacket_ = new ::com::htsc::mdc::insight::model::DynamicPacket;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  return dynamicpacket_;
}
inline ::com::htsc::mdc::insight::model::DynamicPacket* MarketData::release_dynamicpacket() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  
  ::com::htsc::mdc::insight::model::DynamicPacket* temp = dynamicpacket_;
  dynamicpacket_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_dynamicpacket(::com::htsc::mdc::insight::model::DynamicPacket* dynamicpacket) {
  delete dynamicpacket_;
  dynamicpacket_ = dynamicpacket;
  if (dynamicpacket) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
}

// optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
inline bool MarketData::has_mdvolumebyprice() const {
  return this != internal_default_instance() && mdvolumebyprice_ != NULL;
}
inline void MarketData::clear_mdvolumebyprice() {
  if (GetArenaNoVirtual() == NULL && mdvolumebyprice_ != NULL) delete mdvolumebyprice_;
  mdvolumebyprice_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADVolumeByPrice& MarketData::mdvolumebyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  return mdvolumebyprice_ != NULL ? *mdvolumebyprice_
                         : *::com::htsc::mdc::insight::model::ADVolumeByPrice::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADVolumeByPrice* MarketData::mutable_mdvolumebyprice() {
  
  if (mdvolumebyprice_ == NULL) {
    mdvolumebyprice_ = new ::com::htsc::mdc::insight::model::ADVolumeByPrice;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  return mdvolumebyprice_;
}
inline ::com::htsc::mdc::insight::model::ADVolumeByPrice* MarketData::release_mdvolumebyprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  
  ::com::htsc::mdc::insight::model::ADVolumeByPrice* temp = mdvolumebyprice_;
  mdvolumebyprice_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdvolumebyprice(::com::htsc::mdc::insight::model::ADVolumeByPrice* mdvolumebyprice) {
  delete mdvolumebyprice_;
  mdvolumebyprice_ = mdvolumebyprice;
  if (mdvolumebyprice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
}

// optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
inline bool MarketData::has_mdfundflowanalysis() const {
  return this != internal_default_instance() && mdfundflowanalysis_ != NULL;
}
inline void MarketData::clear_mdfundflowanalysis() {
  if (GetArenaNoVirtual() == NULL && mdfundflowanalysis_ != NULL) delete mdfundflowanalysis_;
  mdfundflowanalysis_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADFundFlowAnalysis& MarketData::mdfundflowanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  return mdfundflowanalysis_ != NULL ? *mdfundflowanalysis_
                         : *::com::htsc::mdc::insight::model::ADFundFlowAnalysis::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* MarketData::mutable_mdfundflowanalysis() {
  
  if (mdfundflowanalysis_ == NULL) {
    mdfundflowanalysis_ = new ::com::htsc::mdc::insight::model::ADFundFlowAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  return mdfundflowanalysis_;
}
inline ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* MarketData::release_mdfundflowanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  
  ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* temp = mdfundflowanalysis_;
  mdfundflowanalysis_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdfundflowanalysis(::com::htsc::mdc::insight::model::ADFundFlowAnalysis* mdfundflowanalysis) {
  delete mdfundflowanalysis_;
  mdfundflowanalysis_ = mdfundflowanalysis;
  if (mdfundflowanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
}

// optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
inline bool MarketData::has_mdforex() const {
  return this != internal_default_instance() && mdforex_ != NULL;
}
inline void MarketData::clear_mdforex() {
  if (GetArenaNoVirtual() == NULL && mdforex_ != NULL) delete mdforex_;
  mdforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDForex& MarketData::mdforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdForex)
  return mdforex_ != NULL ? *mdforex_
                         : *::com::htsc::mdc::insight::model::MDForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDForex* MarketData::mutable_mdforex() {
  
  if (mdforex_ == NULL) {
    mdforex_ = new ::com::htsc::mdc::insight::model::MDForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdForex)
  return mdforex_;
}
inline ::com::htsc::mdc::insight::model::MDForex* MarketData::release_mdforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdForex)
  
  ::com::htsc::mdc::insight::model::MDForex* temp = mdforex_;
  mdforex_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdforex(::com::htsc::mdc::insight::model::MDForex* mdforex) {
  delete mdforex_;
  mdforex_ = mdforex;
  if (mdforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdForex)
}

// optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
inline bool MarketData::has_mdspot() const {
  return this != internal_default_instance() && mdspot_ != NULL;
}
inline void MarketData::clear_mdspot() {
  if (GetArenaNoVirtual() == NULL && mdspot_ != NULL) delete mdspot_;
  mdspot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSpot& MarketData::mdspot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSpot)
  return mdspot_ != NULL ? *mdspot_
                         : *::com::htsc::mdc::insight::model::MDSpot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSpot* MarketData::mutable_mdspot() {
  
  if (mdspot_ == NULL) {
    mdspot_ = new ::com::htsc::mdc::insight::model::MDSpot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSpot)
  return mdspot_;
}
inline ::com::htsc::mdc::insight::model::MDSpot* MarketData::release_mdspot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSpot)
  
  ::com::htsc::mdc::insight::model::MDSpot* temp = mdspot_;
  mdspot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdspot(::com::htsc::mdc::insight::model::MDSpot* mdspot) {
  delete mdspot_;
  mdspot_ = mdspot;
  if (mdspot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSpot)
}

// optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
inline bool MarketData::has_mdrate() const {
  return this != internal_default_instance() && mdrate_ != NULL;
}
inline void MarketData::clear_mdrate() {
  if (GetArenaNoVirtual() == NULL && mdrate_ != NULL) delete mdrate_;
  mdrate_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDRate& MarketData::mdrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdRate)
  return mdrate_ != NULL ? *mdrate_
                         : *::com::htsc::mdc::insight::model::MDRate::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDRate* MarketData::mutable_mdrate() {
  
  if (mdrate_ == NULL) {
    mdrate_ = new ::com::htsc::mdc::insight::model::MDRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdRate)
  return mdrate_;
}
inline ::com::htsc::mdc::insight::model::MDRate* MarketData::release_mdrate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdRate)
  
  ::com::htsc::mdc::insight::model::MDRate* temp = mdrate_;
  mdrate_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdrate(::com::htsc::mdc::insight::model::MDRate* mdrate) {
  delete mdrate_;
  mdrate_ = mdrate;
  if (mdrate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdRate)
}

// optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
inline bool MarketData::has_orderbooksnapshot() const {
  return this != internal_default_instance() && orderbooksnapshot_ != NULL;
}
inline void MarketData::clear_orderbooksnapshot() {
  if (GetArenaNoVirtual() == NULL && orderbooksnapshot_ != NULL) delete orderbooksnapshot_;
  orderbooksnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADOrderbookSnapshot& MarketData::orderbooksnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  return orderbooksnapshot_ != NULL ? *orderbooksnapshot_
                         : *::com::htsc::mdc::insight::model::ADOrderbookSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* MarketData::mutable_orderbooksnapshot() {
  
  if (orderbooksnapshot_ == NULL) {
    orderbooksnapshot_ = new ::com::htsc::mdc::insight::model::ADOrderbookSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  return orderbooksnapshot_;
}
inline ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* MarketData::release_orderbooksnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* temp = orderbooksnapshot_;
  orderbooksnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_orderbooksnapshot(::com::htsc::mdc::insight::model::ADOrderbookSnapshot* orderbooksnapshot) {
  delete orderbooksnapshot_;
  orderbooksnapshot_ = orderbooksnapshot;
  if (orderbooksnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
}

// optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
inline bool MarketData::has_orderbooksnapshotwithtick() const {
  return this != internal_default_instance() && orderbooksnapshotwithtick_ != NULL;
}
inline void MarketData::clear_orderbooksnapshotwithtick() {
  if (GetArenaNoVirtual() == NULL && orderbooksnapshotwithtick_ != NULL) delete orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick& MarketData::orderbooksnapshotwithtick() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  return orderbooksnapshotwithtick_ != NULL ? *orderbooksnapshotwithtick_
                         : *::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* MarketData::mutable_orderbooksnapshotwithtick() {
  
  if (orderbooksnapshotwithtick_ == NULL) {
    orderbooksnapshotwithtick_ = new ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  return orderbooksnapshotwithtick_;
}
inline ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* MarketData::release_orderbooksnapshotwithtick() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* temp = orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_orderbooksnapshotwithtick(::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* orderbooksnapshotwithtick) {
  delete orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = orderbooksnapshotwithtick;
  if (orderbooksnapshotwithtick) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
}

// optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
inline bool MarketData::has_mdquote() const {
  return this != internal_default_instance() && mdquote_ != NULL;
}
inline void MarketData::clear_mdquote() {
  if (GetArenaNoVirtual() == NULL && mdquote_ != NULL) delete mdquote_;
  mdquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDQuote& MarketData::mdquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQuote)
  return mdquote_ != NULL ? *mdquote_
                         : *::com::htsc::mdc::insight::model::MDQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDQuote* MarketData::mutable_mdquote() {
  
  if (mdquote_ == NULL) {
    mdquote_ = new ::com::htsc::mdc::insight::model::MDQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQuote)
  return mdquote_;
}
inline ::com::htsc::mdc::insight::model::MDQuote* MarketData::release_mdquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQuote)
  
  ::com::htsc::mdc::insight::model::MDQuote* temp = mdquote_;
  mdquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdquote(::com::htsc::mdc::insight::model::MDQuote* mdquote) {
  delete mdquote_;
  mdquote_ = mdquote;
  if (mdquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQuote)
}

// optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
inline bool MarketData::has_mdetfbasicinfo() const {
  return this != internal_default_instance() && mdetfbasicinfo_ != NULL;
}
inline void MarketData::clear_mdetfbasicinfo() {
  if (GetArenaNoVirtual() == NULL && mdetfbasicinfo_ != NULL) delete mdetfbasicinfo_;
  mdetfbasicinfo_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDETFBasicInfo& MarketData::mdetfbasicinfo() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  return mdetfbasicinfo_ != NULL ? *mdetfbasicinfo_
                         : *::com::htsc::mdc::insight::model::MDETFBasicInfo::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDETFBasicInfo* MarketData::mutable_mdetfbasicinfo() {
  
  if (mdetfbasicinfo_ == NULL) {
    mdetfbasicinfo_ = new ::com::htsc::mdc::insight::model::MDETFBasicInfo;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  return mdetfbasicinfo_;
}
inline ::com::htsc::mdc::insight::model::MDETFBasicInfo* MarketData::release_mdetfbasicinfo() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  
  ::com::htsc::mdc::insight::model::MDETFBasicInfo* temp = mdetfbasicinfo_;
  mdetfbasicinfo_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdetfbasicinfo(::com::htsc::mdc::insight::model::MDETFBasicInfo* mdetfbasicinfo) {
  delete mdetfbasicinfo_;
  mdetfbasicinfo_ = mdetfbasicinfo;
  if (mdetfbasicinfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
}

// optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
inline bool MarketData::has_mdfiquote() const {
  return this != internal_default_instance() && mdfiquote_ != NULL;
}
inline void MarketData::clear_mdfiquote() {
  if (GetArenaNoVirtual() == NULL && mdfiquote_ != NULL) delete mdfiquote_;
  mdfiquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDFIQuote& MarketData::mdfiquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  return mdfiquote_ != NULL ? *mdfiquote_
                         : *::com::htsc::mdc::insight::model::MDFIQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDFIQuote* MarketData::mutable_mdfiquote() {
  
  if (mdfiquote_ == NULL) {
    mdfiquote_ = new ::com::htsc::mdc::insight::model::MDFIQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  return mdfiquote_;
}
inline ::com::htsc::mdc::insight::model::MDFIQuote* MarketData::release_mdfiquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  
  ::com::htsc::mdc::insight::model::MDFIQuote* temp = mdfiquote_;
  mdfiquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdfiquote(::com::htsc::mdc::insight::model::MDFIQuote* mdfiquote) {
  delete mdfiquote_;
  mdfiquote_ = mdfiquote;
  if (mdfiquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
}

// optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
inline bool MarketData::has_mdchipdistribution() const {
  return this != internal_default_instance() && mdchipdistribution_ != NULL;
}
inline void MarketData::clear_mdchipdistribution() {
  if (GetArenaNoVirtual() == NULL && mdchipdistribution_ != NULL) delete mdchipdistribution_;
  mdchipdistribution_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADChipDistribution& MarketData::mdchipdistribution() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  return mdchipdistribution_ != NULL ? *mdchipdistribution_
                         : *::com::htsc::mdc::insight::model::ADChipDistribution::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADChipDistribution* MarketData::mutable_mdchipdistribution() {
  
  if (mdchipdistribution_ == NULL) {
    mdchipdistribution_ = new ::com::htsc::mdc::insight::model::ADChipDistribution;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  return mdchipdistribution_;
}
inline ::com::htsc::mdc::insight::model::ADChipDistribution* MarketData::release_mdchipdistribution() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  
  ::com::htsc::mdc::insight::model::ADChipDistribution* temp = mdchipdistribution_;
  mdchipdistribution_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdchipdistribution(::com::htsc::mdc::insight::model::ADChipDistribution* mdchipdistribution) {
  delete mdchipdistribution_;
  mdchipdistribution_ = mdchipdistribution;
  if (mdchipdistribution) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
}

// optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
inline bool MarketData::has_mdwarrant() const {
  return this != internal_default_instance() && mdwarrant_ != NULL;
}
inline void MarketData::clear_mdwarrant() {
  if (GetArenaNoVirtual() == NULL && mdwarrant_ != NULL) delete mdwarrant_;
  mdwarrant_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDWarrant& MarketData::mdwarrant() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  return mdwarrant_ != NULL ? *mdwarrant_
                         : *::com::htsc::mdc::insight::model::MDWarrant::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDWarrant* MarketData::mutable_mdwarrant() {
  
  if (mdwarrant_ == NULL) {
    mdwarrant_ = new ::com::htsc::mdc::insight::model::MDWarrant;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  return mdwarrant_;
}
inline ::com::htsc::mdc::insight::model::MDWarrant* MarketData::release_mdwarrant() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  
  ::com::htsc::mdc::insight::model::MDWarrant* temp = mdwarrant_;
  mdwarrant_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdwarrant(::com::htsc::mdc::insight::model::MDWarrant* mdwarrant) {
  delete mdwarrant_;
  mdwarrant_ = mdwarrant;
  if (mdwarrant) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdWarrant)
}

// optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
inline bool MarketData::has_mdsecuritylending() const {
  return this != internal_default_instance() && mdsecuritylending_ != NULL;
}
inline void MarketData::clear_mdsecuritylending() {
  if (GetArenaNoVirtual() == NULL && mdsecuritylending_ != NULL) delete mdsecuritylending_;
  mdsecuritylending_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSecurityLending& MarketData::mdsecuritylending() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  return mdsecuritylending_ != NULL ? *mdsecuritylending_
                         : *::com::htsc::mdc::insight::model::MDSecurityLending::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSecurityLending* MarketData::mutable_mdsecuritylending() {
  
  if (mdsecuritylending_ == NULL) {
    mdsecuritylending_ = new ::com::htsc::mdc::insight::model::MDSecurityLending;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  return mdsecuritylending_;
}
inline ::com::htsc::mdc::insight::model::MDSecurityLending* MarketData::release_mdsecuritylending() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  
  ::com::htsc::mdc::insight::model::MDSecurityLending* temp = mdsecuritylending_;
  mdsecuritylending_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdsecuritylending(::com::htsc::mdc::insight::model::MDSecurityLending* mdsecuritylending) {
  delete mdsecuritylending_;
  mdsecuritylending_ = mdsecuritylending;
  if (mdsecuritylending) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
}

// optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
inline bool MarketData::has_mdnews() const {
  return this != internal_default_instance() && mdnews_ != NULL;
}
inline void MarketData::clear_mdnews() {
  if (GetArenaNoVirtual() == NULL && mdnews_ != NULL) delete mdnews_;
  mdnews_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADNews& MarketData::mdnews() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdNews)
  return mdnews_ != NULL ? *mdnews_
                         : *::com::htsc::mdc::insight::model::ADNews::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADNews* MarketData::mutable_mdnews() {
  
  if (mdnews_ == NULL) {
    mdnews_ = new ::com::htsc::mdc::insight::model::ADNews;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdNews)
  return mdnews_;
}
inline ::com::htsc::mdc::insight::model::ADNews* MarketData::release_mdnews() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdNews)
  
  ::com::htsc::mdc::insight::model::ADNews* temp = mdnews_;
  mdnews_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdnews(::com::htsc::mdc::insight::model::ADNews* mdnews) {
  delete mdnews_;
  mdnews_ = mdnews;
  if (mdnews) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdNews)
}

// optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
inline bool MarketData::has_mdstaringresult() const {
  return this != internal_default_instance() && mdstaringresult_ != NULL;
}
inline void MarketData::clear_mdstaringresult() {
  if (GetArenaNoVirtual() == NULL && mdstaringresult_ != NULL) delete mdstaringresult_;
  mdstaringresult_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADStaringResult& MarketData::mdstaringresult() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  return mdstaringresult_ != NULL ? *mdstaringresult_
                         : *::com::htsc::mdc::insight::model::ADStaringResult::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADStaringResult* MarketData::mutable_mdstaringresult() {
  
  if (mdstaringresult_ == NULL) {
    mdstaringresult_ = new ::com::htsc::mdc::insight::model::ADStaringResult;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  return mdstaringresult_;
}
inline ::com::htsc::mdc::insight::model::ADStaringResult* MarketData::release_mdstaringresult() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  
  ::com::htsc::mdc::insight::model::ADStaringResult* temp = mdstaringresult_;
  mdstaringresult_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdstaringresult(::com::htsc::mdc::insight::model::ADStaringResult* mdstaringresult) {
  delete mdstaringresult_;
  mdstaringresult_ = mdstaringresult;
  if (mdstaringresult) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
}

// optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
inline bool MarketData::has_mdderivedanalysis() const {
  return this != internal_default_instance() && mdderivedanalysis_ != NULL;
}
inline void MarketData::clear_mdderivedanalysis() {
  if (GetArenaNoVirtual() == NULL && mdderivedanalysis_ != NULL) delete mdderivedanalysis_;
  mdderivedanalysis_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::ADDerivedAnalysis& MarketData::mdderivedanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  return mdderivedanalysis_ != NULL ? *mdderivedanalysis_
                         : *::com::htsc::mdc::insight::model::ADDerivedAnalysis::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::ADDerivedAnalysis* MarketData::mutable_mdderivedanalysis() {
  
  if (mdderivedanalysis_ == NULL) {
    mdderivedanalysis_ = new ::com::htsc::mdc::insight::model::ADDerivedAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  return mdderivedanalysis_;
}
inline ::com::htsc::mdc::insight::model::ADDerivedAnalysis* MarketData::release_mdderivedanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  
  ::com::htsc::mdc::insight::model::ADDerivedAnalysis* temp = mdderivedanalysis_;
  mdderivedanalysis_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdderivedanalysis(::com::htsc::mdc::insight::model::ADDerivedAnalysis* mdderivedanalysis) {
  delete mdderivedanalysis_;
  mdderivedanalysis_ = mdderivedanalysis;
  if (mdderivedanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
}

// optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
inline bool MarketData::has_mdqbquote() const {
  return this != internal_default_instance() && mdqbquote_ != NULL;
}
inline void MarketData::clear_mdqbquote() {
  if (GetArenaNoVirtual() == NULL && mdqbquote_ != NULL) delete mdqbquote_;
  mdqbquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDQBQuote& MarketData::mdqbquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  return mdqbquote_ != NULL ? *mdqbquote_
                         : *::com::htsc::mdc::insight::model::MDQBQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDQBQuote* MarketData::mutable_mdqbquote() {
  
  if (mdqbquote_ == NULL) {
    mdqbquote_ = new ::com::htsc::mdc::insight::model::MDQBQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  return mdqbquote_;
}
inline ::com::htsc::mdc::insight::model::MDQBQuote* MarketData::release_mdqbquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  
  ::com::htsc::mdc::insight::model::MDQBQuote* temp = mdqbquote_;
  mdqbquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdqbquote(::com::htsc::mdc::insight::model::MDQBQuote* mdqbquote) {
  delete mdqbquote_;
  mdqbquote_ = mdqbquote;
  if (mdqbquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
}

// optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
inline bool MarketData::has_mdqbtransaction() const {
  return this != internal_default_instance() && mdqbtransaction_ != NULL;
}
inline void MarketData::clear_mdqbtransaction() {
  if (GetArenaNoVirtual() == NULL && mdqbtransaction_ != NULL) delete mdqbtransaction_;
  mdqbtransaction_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDQBTransaction& MarketData::mdqbtransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  return mdqbtransaction_ != NULL ? *mdqbtransaction_
                         : *::com::htsc::mdc::insight::model::MDQBTransaction::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDQBTransaction* MarketData::mutable_mdqbtransaction() {
  
  if (mdqbtransaction_ == NULL) {
    mdqbtransaction_ = new ::com::htsc::mdc::insight::model::MDQBTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  return mdqbtransaction_;
}
inline ::com::htsc::mdc::insight::model::MDQBTransaction* MarketData::release_mdqbtransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  
  ::com::htsc::mdc::insight::model::MDQBTransaction* temp = mdqbtransaction_;
  mdqbtransaction_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdqbtransaction(::com::htsc::mdc::insight::model::MDQBTransaction* mdqbtransaction) {
  delete mdqbtransaction_;
  mdqbtransaction_ = mdqbtransaction;
  if (mdqbtransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
}

// optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
inline bool MarketData::has_mdusaorder() const {
  return this != internal_default_instance() && mdusaorder_ != NULL;
}
inline void MarketData::clear_mdusaorder() {
  if (GetArenaNoVirtual() == NULL && mdusaorder_ != NULL) delete mdusaorder_;
  mdusaorder_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDUSAOrder& MarketData::mdusaorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  return mdusaorder_ != NULL ? *mdusaorder_
                         : *::com::htsc::mdc::insight::model::MDUSAOrder::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDUSAOrder* MarketData::mutable_mdusaorder() {
  
  if (mdusaorder_ == NULL) {
    mdusaorder_ = new ::com::htsc::mdc::insight::model::MDUSAOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  return mdusaorder_;
}
inline ::com::htsc::mdc::insight::model::MDUSAOrder* MarketData::release_mdusaorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  
  ::com::htsc::mdc::insight::model::MDUSAOrder* temp = mdusaorder_;
  mdusaorder_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdusaorder(::com::htsc::mdc::insight::model::MDUSAOrder* mdusaorder) {
  delete mdusaorder_;
  mdusaorder_ = mdusaorder;
  if (mdusaorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
}

// optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
inline bool MarketData::has_mdusatransaction() const {
  return this != internal_default_instance() && mdusatransaction_ != NULL;
}
inline void MarketData::clear_mdusatransaction() {
  if (GetArenaNoVirtual() == NULL && mdusatransaction_ != NULL) delete mdusatransaction_;
  mdusatransaction_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDUSATransaction& MarketData::mdusatransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  return mdusatransaction_ != NULL ? *mdusatransaction_
                         : *::com::htsc::mdc::insight::model::MDUSATransaction::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDUSATransaction* MarketData::mutable_mdusatransaction() {
  
  if (mdusatransaction_ == NULL) {
    mdusatransaction_ = new ::com::htsc::mdc::insight::model::MDUSATransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  return mdusatransaction_;
}
inline ::com::htsc::mdc::insight::model::MDUSATransaction* MarketData::release_mdusatransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  
  ::com::htsc::mdc::insight::model::MDUSATransaction* temp = mdusatransaction_;
  mdusatransaction_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdusatransaction(::com::htsc::mdc::insight::model::MDUSATransaction* mdusatransaction) {
  delete mdusatransaction_;
  mdusatransaction_ = mdusatransaction;
  if (mdusatransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
}

// optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
inline bool MarketData::has_mdslorder() const {
  return this != internal_default_instance() && mdslorder_ != NULL;
}
inline void MarketData::clear_mdslorder() {
  if (GetArenaNoVirtual() == NULL && mdslorder_ != NULL) delete mdslorder_;
  mdslorder_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSLOrder& MarketData::mdslorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  return mdslorder_ != NULL ? *mdslorder_
                         : *::com::htsc::mdc::insight::model::MDSLOrder::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSLOrder* MarketData::mutable_mdslorder() {
  
  if (mdslorder_ == NULL) {
    mdslorder_ = new ::com::htsc::mdc::insight::model::MDSLOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  return mdslorder_;
}
inline ::com::htsc::mdc::insight::model::MDSLOrder* MarketData::release_mdslorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  
  ::com::htsc::mdc::insight::model::MDSLOrder* temp = mdslorder_;
  mdslorder_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdslorder(::com::htsc::mdc::insight::model::MDSLOrder* mdslorder) {
  delete mdslorder_;
  mdslorder_ = mdslorder;
  if (mdslorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
}

// optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
inline bool MarketData::has_mdsltransaction() const {
  return this != internal_default_instance() && mdsltransaction_ != NULL;
}
inline void MarketData::clear_mdsltransaction() {
  if (GetArenaNoVirtual() == NULL && mdsltransaction_ != NULL) delete mdsltransaction_;
  mdsltransaction_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSLTransaction& MarketData::mdsltransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  return mdsltransaction_ != NULL ? *mdsltransaction_
                         : *::com::htsc::mdc::insight::model::MDSLTransaction::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSLTransaction* MarketData::mutable_mdsltransaction() {
  
  if (mdsltransaction_ == NULL) {
    mdsltransaction_ = new ::com::htsc::mdc::insight::model::MDSLTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  return mdsltransaction_;
}
inline ::com::htsc::mdc::insight::model::MDSLTransaction* MarketData::release_mdsltransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  
  ::com::htsc::mdc::insight::model::MDSLTransaction* temp = mdsltransaction_;
  mdsltransaction_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdsltransaction(::com::htsc::mdc::insight::model::MDSLTransaction* mdsltransaction) {
  delete mdsltransaction_;
  mdsltransaction_ = mdsltransaction;
  if (mdsltransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
}

// optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
inline bool MarketData::has_mdhkgreymarket() const {
  return this != internal_default_instance() && mdhkgreymarket_ != NULL;
}
inline void MarketData::clear_mdhkgreymarket() {
  if (GetArenaNoVirtual() == NULL && mdhkgreymarket_ != NULL) delete mdhkgreymarket_;
  mdhkgreymarket_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDHKGreyMarket& MarketData::mdhkgreymarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  return mdhkgreymarket_ != NULL ? *mdhkgreymarket_
                         : *::com::htsc::mdc::insight::model::MDHKGreyMarket::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket* MarketData::mutable_mdhkgreymarket() {
  
  if (mdhkgreymarket_ == NULL) {
    mdhkgreymarket_ = new ::com::htsc::mdc::insight::model::MDHKGreyMarket;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  return mdhkgreymarket_;
}
inline ::com::htsc::mdc::insight::model::MDHKGreyMarket* MarketData::release_mdhkgreymarket() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  
  ::com::htsc::mdc::insight::model::MDHKGreyMarket* temp = mdhkgreymarket_;
  mdhkgreymarket_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdhkgreymarket(::com::htsc::mdc::insight::model::MDHKGreyMarket* mdhkgreymarket) {
  delete mdhkgreymarket_;
  mdhkgreymarket_ = mdhkgreymarket;
  if (mdhkgreymarket) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
}

// optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
inline bool MarketData::has_mdslindicativequote() const {
  return this != internal_default_instance() && mdslindicativequote_ != NULL;
}
inline void MarketData::clear_mdslindicativequote() {
  if (GetArenaNoVirtual() == NULL && mdslindicativequote_ != NULL) delete mdslindicativequote_;
  mdslindicativequote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSLIndicativeQuote& MarketData::mdslindicativequote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  return mdslindicativequote_ != NULL ? *mdslindicativequote_
                         : *::com::htsc::mdc::insight::model::MDSLIndicativeQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* MarketData::mutable_mdslindicativequote() {
  
  if (mdslindicativequote_ == NULL) {
    mdslindicativequote_ = new ::com::htsc::mdc::insight::model::MDSLIndicativeQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  return mdslindicativequote_;
}
inline ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* MarketData::release_mdslindicativequote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  
  ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* temp = mdslindicativequote_;
  mdslindicativequote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdslindicativequote(::com::htsc::mdc::insight::model::MDSLIndicativeQuote* mdslindicativequote) {
  delete mdslindicativequote_;
  mdslindicativequote_ = mdslindicativequote;
  if (mdslindicativequote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
}

// optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
inline bool MarketData::has_mdslstatistics() const {
  return this != internal_default_instance() && mdslstatistics_ != NULL;
}
inline void MarketData::clear_mdslstatistics() {
  if (GetArenaNoVirtual() == NULL && mdslstatistics_ != NULL) delete mdslstatistics_;
  mdslstatistics_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSLStatistics& MarketData::mdslstatistics() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  return mdslstatistics_ != NULL ? *mdslstatistics_
                         : *::com::htsc::mdc::insight::model::MDSLStatistics::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSLStatistics* MarketData::mutable_mdslstatistics() {
  
  if (mdslstatistics_ == NULL) {
    mdslstatistics_ = new ::com::htsc::mdc::insight::model::MDSLStatistics;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  return mdslstatistics_;
}
inline ::com::htsc::mdc::insight::model::MDSLStatistics* MarketData::release_mdslstatistics() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  
  ::com::htsc::mdc::insight::model::MDSLStatistics* temp = mdslstatistics_;
  mdslstatistics_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdslstatistics(::com::htsc::mdc::insight::model::MDSLStatistics* mdslstatistics) {
  delete mdslstatistics_;
  mdslstatistics_ = mdslstatistics;
  if (mdslstatistics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
}

// optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
inline bool MarketData::has_mdusaquote() const {
  return this != internal_default_instance() && mdusaquote_ != NULL;
}
inline void MarketData::clear_mdusaquote() {
  if (GetArenaNoVirtual() == NULL && mdusaquote_ != NULL) delete mdusaquote_;
  mdusaquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDUSAQuote& MarketData::mdusaquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  return mdusaquote_ != NULL ? *mdusaquote_
                         : *::com::htsc::mdc::insight::model::MDUSAQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDUSAQuote* MarketData::mutable_mdusaquote() {
  
  if (mdusaquote_ == NULL) {
    mdusaquote_ = new ::com::htsc::mdc::insight::model::MDUSAQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  return mdusaquote_;
}
inline ::com::htsc::mdc::insight::model::MDUSAQuote* MarketData::release_mdusaquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  
  ::com::htsc::mdc::insight::model::MDUSAQuote* temp = mdusaquote_;
  mdusaquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdusaquote(::com::htsc::mdc::insight::model::MDUSAQuote* mdusaquote) {
  delete mdusaquote_;
  mdusaquote_ = mdusaquote;
  if (mdusaquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
}

// optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
inline bool MarketData::has_mdslestimation() const {
  return this != internal_default_instance() && mdslestimation_ != NULL;
}
inline void MarketData::clear_mdslestimation() {
  if (GetArenaNoVirtual() == NULL && mdslestimation_ != NULL) delete mdslestimation_;
  mdslestimation_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDSLEstimation& MarketData::mdslestimation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  return mdslestimation_ != NULL ? *mdslestimation_
                         : *::com::htsc::mdc::insight::model::MDSLEstimation::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDSLEstimation* MarketData::mutable_mdslestimation() {
  
  if (mdslestimation_ == NULL) {
    mdslestimation_ = new ::com::htsc::mdc::insight::model::MDSLEstimation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  return mdslestimation_;
}
inline ::com::htsc::mdc::insight::model::MDSLEstimation* MarketData::release_mdslestimation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  
  ::com::htsc::mdc::insight::model::MDSLEstimation* temp = mdslestimation_;
  mdslestimation_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdslestimation(::com::htsc::mdc::insight::model::MDSLEstimation* mdslestimation) {
  delete mdslestimation_;
  mdslestimation_ = mdslestimation;
  if (mdslestimation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
}

// optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
inline bool MarketData::has_mdcnexdeal() const {
  return this != internal_default_instance() && mdcnexdeal_ != NULL;
}
inline void MarketData::clear_mdcnexdeal() {
  if (GetArenaNoVirtual() == NULL && mdcnexdeal_ != NULL) delete mdcnexdeal_;
  mdcnexdeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCnexDeal& MarketData::mdcnexdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  return mdcnexdeal_ != NULL ? *mdcnexdeal_
                         : *::com::htsc::mdc::insight::model::MDCnexDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCnexDeal* MarketData::mutable_mdcnexdeal() {
  
  if (mdcnexdeal_ == NULL) {
    mdcnexdeal_ = new ::com::htsc::mdc::insight::model::MDCnexDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  return mdcnexdeal_;
}
inline ::com::htsc::mdc::insight::model::MDCnexDeal* MarketData::release_mdcnexdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  
  ::com::htsc::mdc::insight::model::MDCnexDeal* temp = mdcnexdeal_;
  mdcnexdeal_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcnexdeal(::com::htsc::mdc::insight::model::MDCnexDeal* mdcnexdeal) {
  delete mdcnexdeal_;
  mdcnexdeal_ = mdcnexdeal;
  if (mdcnexdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
}

// optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
inline bool MarketData::has_mdcnexquote() const {
  return this != internal_default_instance() && mdcnexquote_ != NULL;
}
inline void MarketData::clear_mdcnexquote() {
  if (GetArenaNoVirtual() == NULL && mdcnexquote_ != NULL) delete mdcnexquote_;
  mdcnexquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCnexQuote& MarketData::mdcnexquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  return mdcnexquote_ != NULL ? *mdcnexquote_
                         : *::com::htsc::mdc::insight::model::MDCnexQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCnexQuote* MarketData::mutable_mdcnexquote() {
  
  if (mdcnexquote_ == NULL) {
    mdcnexquote_ = new ::com::htsc::mdc::insight::model::MDCnexQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  return mdcnexquote_;
}
inline ::com::htsc::mdc::insight::model::MDCnexQuote* MarketData::release_mdcnexquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  
  ::com::htsc::mdc::insight::model::MDCnexQuote* temp = mdcnexquote_;
  mdcnexquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcnexquote(::com::htsc::mdc::insight::model::MDCnexQuote* mdcnexquote) {
  delete mdcnexquote_;
  mdcnexquote_ = mdcnexquote;
  if (mdcnexquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
}

// optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
inline bool MarketData::has_mddelaysnapshot() const {
  return this != internal_default_instance() && mddelaysnapshot_ != NULL;
}
inline void MarketData::clear_mddelaysnapshot() {
  if (GetArenaNoVirtual() == NULL && mddelaysnapshot_ != NULL) delete mddelaysnapshot_;
  mddelaysnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDDelaySnapshot& MarketData::mddelaysnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  return mddelaysnapshot_ != NULL ? *mddelaysnapshot_
                         : *::com::htsc::mdc::insight::model::MDDelaySnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDDelaySnapshot* MarketData::mutable_mddelaysnapshot() {
  
  if (mddelaysnapshot_ == NULL) {
    mddelaysnapshot_ = new ::com::htsc::mdc::insight::model::MDDelaySnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  return mddelaysnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDDelaySnapshot* MarketData::release_mddelaysnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  
  ::com::htsc::mdc::insight::model::MDDelaySnapshot* temp = mddelaysnapshot_;
  mddelaysnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mddelaysnapshot(::com::htsc::mdc::insight::model::MDDelaySnapshot* mddelaysnapshot) {
  delete mddelaysnapshot_;
  mddelaysnapshot_ = mddelaysnapshot;
  if (mddelaysnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
}

// optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
inline bool MarketData::has_mdhighaccuracyfuture() const {
  return this != internal_default_instance() && mdhighaccuracyfuture_ != NULL;
}
inline void MarketData::clear_mdhighaccuracyfuture() {
  if (GetArenaNoVirtual() == NULL && mdhighaccuracyfuture_ != NULL) delete mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDHighAccuracyFuture& MarketData::mdhighaccuracyfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  return mdhighaccuracyfuture_ != NULL ? *mdhighaccuracyfuture_
                         : *::com::htsc::mdc::insight::model::MDHighAccuracyFuture::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* MarketData::mutable_mdhighaccuracyfuture() {
  
  if (mdhighaccuracyfuture_ == NULL) {
    mdhighaccuracyfuture_ = new ::com::htsc::mdc::insight::model::MDHighAccuracyFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  return mdhighaccuracyfuture_;
}
inline ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* MarketData::release_mdhighaccuracyfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  
  ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* temp = mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdhighaccuracyfuture(::com::htsc::mdc::insight::model::MDHighAccuracyFuture* mdhighaccuracyfuture) {
  delete mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = mdhighaccuracyfuture;
  if (mdhighaccuracyfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
}

// optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
inline bool MarketData::has_mdcfetsforex() const {
  return this != internal_default_instance() && mdcfetsforex_ != NULL;
}
inline void MarketData::clear_mdcfetsforex() {
  if (GetArenaNoVirtual() == NULL && mdcfetsforex_ != NULL) delete mdcfetsforex_;
  mdcfetsforex_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsForex& MarketData::mdcfetsforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  return mdcfetsforex_ != NULL ? *mdcfetsforex_
                         : *::com::htsc::mdc::insight::model::MDCfetsForex::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsForex* MarketData::mutable_mdcfetsforex() {
  
  if (mdcfetsforex_ == NULL) {
    mdcfetsforex_ = new ::com::htsc::mdc::insight::model::MDCfetsForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  return mdcfetsforex_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsForex* MarketData::release_mdcfetsforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  
  ::com::htsc::mdc::insight::model::MDCfetsForex* temp = mdcfetsforex_;
  mdcfetsforex_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsforex(::com::htsc::mdc::insight::model::MDCfetsForex* mdcfetsforex) {
  delete mdcfetsforex_;
  mdcfetsforex_ = mdcfetsforex;
  if (mdcfetsforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
inline bool MarketData::has_mdcfetsfxsnapshot() const {
  return this != internal_default_instance() && mdcfetsfxsnapshot_ != NULL;
}
inline void MarketData::clear_mdcfetsfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxsnapshot_ != NULL) delete mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot& MarketData::mdcfetsfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  return mdcfetsfxsnapshot_ != NULL ? *mdcfetsfxsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* MarketData::mutable_mdcfetsfxsnapshot() {
  
  if (mdcfetsfxsnapshot_ == NULL) {
    mdcfetsfxsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  return mdcfetsfxsnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* MarketData::release_mdcfetsfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* temp = mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsfxsnapshot(::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* mdcfetsfxsnapshot) {
  delete mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = mdcfetsfxsnapshot;
  if (mdcfetsfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
inline bool MarketData::has_mdcfetsfxquote() const {
  return this != internal_default_instance() && mdcfetsfxquote_ != NULL;
}
inline void MarketData::clear_mdcfetsfxquote() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxquote_ != NULL) delete mdcfetsfxquote_;
  mdcfetsfxquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsFxQuote& MarketData::mdcfetsfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  return mdcfetsfxquote_ != NULL ? *mdcfetsfxquote_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxQuote* MarketData::mutable_mdcfetsfxquote() {
  
  if (mdcfetsfxquote_ == NULL) {
    mdcfetsfxquote_ = new ::com::htsc::mdc::insight::model::MDCfetsFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  return mdcfetsfxquote_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxQuote* MarketData::release_mdcfetsfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxQuote* temp = mdcfetsfxquote_;
  mdcfetsfxquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsfxquote(::com::htsc::mdc::insight::model::MDCfetsFxQuote* mdcfetsfxquote) {
  delete mdcfetsfxquote_;
  mdcfetsfxquote_ = mdcfetsfxquote;
  if (mdcfetsfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
}

// optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
inline bool MarketData::has_spfuture() const {
  return this != internal_default_instance() && spfuture_ != NULL;
}
inline void MarketData::clear_spfuture() {
  if (GetArenaNoVirtual() == NULL && spfuture_ != NULL) delete spfuture_;
  spfuture_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::SPFuture& MarketData::spfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.spFuture)
  return spfuture_ != NULL ? *spfuture_
                         : *::com::htsc::mdc::insight::model::SPFuture::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::SPFuture* MarketData::mutable_spfuture() {
  
  if (spfuture_ == NULL) {
    spfuture_ = new ::com::htsc::mdc::insight::model::SPFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.spFuture)
  return spfuture_;
}
inline ::com::htsc::mdc::insight::model::SPFuture* MarketData::release_spfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.spFuture)
  
  ::com::htsc::mdc::insight::model::SPFuture* temp = spfuture_;
  spfuture_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_spfuture(::com::htsc::mdc::insight::model::SPFuture* spfuture) {
  delete spfuture_;
  spfuture_ = spfuture;
  if (spfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.spFuture)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
inline bool MarketData::has_mdcfetsbenchmark() const {
  return this != internal_default_instance() && mdcfetsbenchmark_ != NULL;
}
inline void MarketData::clear_mdcfetsbenchmark() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbenchmark_ != NULL) delete mdcfetsbenchmark_;
  mdcfetsbenchmark_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsBenchmark& MarketData::mdcfetsbenchmark() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  return mdcfetsbenchmark_ != NULL ? *mdcfetsbenchmark_
                         : *::com::htsc::mdc::insight::model::MDCfetsBenchmark::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsBenchmark* MarketData::mutable_mdcfetsbenchmark() {
  
  if (mdcfetsbenchmark_ == NULL) {
    mdcfetsbenchmark_ = new ::com::htsc::mdc::insight::model::MDCfetsBenchmark;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  return mdcfetsbenchmark_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsBenchmark* MarketData::release_mdcfetsbenchmark() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  
  ::com::htsc::mdc::insight::model::MDCfetsBenchmark* temp = mdcfetsbenchmark_;
  mdcfetsbenchmark_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsbenchmark(::com::htsc::mdc::insight::model::MDCfetsBenchmark* mdcfetsbenchmark) {
  delete mdcfetsbenchmark_;
  mdcfetsbenchmark_ = mdcfetsbenchmark;
  if (mdcfetsbenchmark) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
inline bool MarketData::has_mdcfetsbonddeal() const {
  return this != internal_default_instance() && mdcfetsbonddeal_ != NULL;
}
inline void MarketData::clear_mdcfetsbonddeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbonddeal_ != NULL) delete mdcfetsbonddeal_;
  mdcfetsbonddeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsBondDeal& MarketData::mdcfetsbonddeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  return mdcfetsbonddeal_ != NULL ? *mdcfetsbonddeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsBondDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsBondDeal* MarketData::mutable_mdcfetsbonddeal() {
  
  if (mdcfetsbonddeal_ == NULL) {
    mdcfetsbonddeal_ = new ::com::htsc::mdc::insight::model::MDCfetsBondDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  return mdcfetsbonddeal_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsBondDeal* MarketData::release_mdcfetsbonddeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsBondDeal* temp = mdcfetsbonddeal_;
  mdcfetsbonddeal_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsbonddeal(::com::htsc::mdc::insight::model::MDCfetsBondDeal* mdcfetsbonddeal) {
  delete mdcfetsbonddeal_;
  mdcfetsbonddeal_ = mdcfetsbonddeal;
  if (mdcfetsbonddeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
inline bool MarketData::has_mdcfetsbondsnapshot() const {
  return this != internal_default_instance() && mdcfetsbondsnapshot_ != NULL;
}
inline void MarketData::clear_mdcfetsbondsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbondsnapshot_ != NULL) delete mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot& MarketData::mdcfetsbondsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  return mdcfetsbondsnapshot_ != NULL ? *mdcfetsbondsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsBondSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* MarketData::mutable_mdcfetsbondsnapshot() {
  
  if (mdcfetsbondsnapshot_ == NULL) {
    mdcfetsbondsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  return mdcfetsbondsnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* MarketData::release_mdcfetsbondsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* temp = mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsbondsnapshot(::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* mdcfetsbondsnapshot) {
  delete mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = mdcfetsbondsnapshot;
  if (mdcfetsbondsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
inline bool MarketData::has_mdcfetscurrencydeal() const {
  return this != internal_default_instance() && mdcfetscurrencydeal_ != NULL;
}
inline void MarketData::clear_mdcfetscurrencydeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencydeal_ != NULL) delete mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal& MarketData::mdcfetscurrencydeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  return mdcfetscurrencydeal_ != NULL ? *mdcfetscurrencydeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* MarketData::mutable_mdcfetscurrencydeal() {
  
  if (mdcfetscurrencydeal_ == NULL) {
    mdcfetscurrencydeal_ = new ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  return mdcfetscurrencydeal_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* MarketData::release_mdcfetscurrencydeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* temp = mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetscurrencydeal(::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* mdcfetscurrencydeal) {
  delete mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = mdcfetscurrencydeal;
  if (mdcfetscurrencydeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
inline bool MarketData::has_mdcfetscurrencysnapshot() const {
  return this != internal_default_instance() && mdcfetscurrencysnapshot_ != NULL;
}
inline void MarketData::clear_mdcfetscurrencysnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencysnapshot_ != NULL) delete mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot& MarketData::mdcfetscurrencysnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  return mdcfetscurrencysnapshot_ != NULL ? *mdcfetscurrencysnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* MarketData::mutable_mdcfetscurrencysnapshot() {
  
  if (mdcfetscurrencysnapshot_ == NULL) {
    mdcfetscurrencysnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  return mdcfetscurrencysnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* MarketData::release_mdcfetscurrencysnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* temp = mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetscurrencysnapshot(::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* mdcfetscurrencysnapshot) {
  delete mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = mdcfetscurrencysnapshot;
  if (mdcfetscurrencysnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
inline bool MarketData::has_mdcfetsodmsnapshot() const {
  return this != internal_default_instance() && mdcfetsodmsnapshot_ != NULL;
}
inline void MarketData::clear_mdcfetsodmsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsodmsnapshot_ != NULL) delete mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot& MarketData::mdcfetsodmsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  return mdcfetsodmsnapshot_ != NULL ? *mdcfetsodmsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsODMSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* MarketData::mutable_mdcfetsodmsnapshot() {
  
  if (mdcfetsodmsnapshot_ == NULL) {
    mdcfetsodmsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  return mdcfetsodmsnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* MarketData::release_mdcfetsodmsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* temp = mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsodmsnapshot(::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* mdcfetsodmsnapshot) {
  delete mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = mdcfetsodmsnapshot;
  if (mdcfetsodmsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
inline bool MarketData::has_mdcfetsqdmquote() const {
  return this != internal_default_instance() && mdcfetsqdmquote_ != NULL;
}
inline void MarketData::clear_mdcfetsqdmquote() {
  if (GetArenaNoVirtual() == NULL && mdcfetsqdmquote_ != NULL) delete mdcfetsqdmquote_;
  mdcfetsqdmquote_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsQDMQuote& MarketData::mdcfetsqdmquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  return mdcfetsqdmquote_ != NULL ? *mdcfetsqdmquote_
                         : *::com::htsc::mdc::insight::model::MDCfetsQDMQuote::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* MarketData::mutable_mdcfetsqdmquote() {
  
  if (mdcfetsqdmquote_ == NULL) {
    mdcfetsqdmquote_ = new ::com::htsc::mdc::insight::model::MDCfetsQDMQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  return mdcfetsqdmquote_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* MarketData::release_mdcfetsqdmquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  
  ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* temp = mdcfetsqdmquote_;
  mdcfetsqdmquote_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsqdmquote(::com::htsc::mdc::insight::model::MDCfetsQDMQuote* mdcfetsqdmquote) {
  delete mdcfetsqdmquote_;
  mdcfetsqdmquote_ = mdcfetsqdmquote;
  if (mdcfetsqdmquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
}

// optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
inline bool MarketData::has_mdcfetsratedeal() const {
  return this != internal_default_instance() && mdcfetsratedeal_ != NULL;
}
inline void MarketData::clear_mdcfetsratedeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetsratedeal_ != NULL) delete mdcfetsratedeal_;
  mdcfetsratedeal_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsRateDeal& MarketData::mdcfetsratedeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  return mdcfetsratedeal_ != NULL ? *mdcfetsratedeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsRateDeal::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsRateDeal* MarketData::mutable_mdcfetsratedeal() {
  
  if (mdcfetsratedeal_ == NULL) {
    mdcfetsratedeal_ = new ::com::htsc::mdc::insight::model::MDCfetsRateDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  return mdcfetsratedeal_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsRateDeal* MarketData::release_mdcfetsratedeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsRateDeal* temp = mdcfetsratedeal_;
  mdcfetsratedeal_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsratedeal(::com::htsc::mdc::insight::model::MDCfetsRateDeal* mdcfetsratedeal) {
  delete mdcfetsratedeal_;
  mdcfetsratedeal_ = mdcfetsratedeal;
  if (mdcfetsratedeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
inline bool MarketData::has_mdcfetsratesnapshot() const {
  return this != internal_default_instance() && mdcfetsratesnapshot_ != NULL;
}
inline void MarketData::clear_mdcfetsratesnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsratesnapshot_ != NULL) delete mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot& MarketData::mdcfetsratesnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  return mdcfetsratesnapshot_ != NULL ? *mdcfetsratesnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsRateSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* MarketData::mutable_mdcfetsratesnapshot() {
  
  if (mdcfetsratesnapshot_ == NULL) {
    mdcfetsratesnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  return mdcfetsratesnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* MarketData::release_mdcfetsratesnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* temp = mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsratesnapshot(::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* mdcfetsratesnapshot) {
  delete mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = mdcfetsratesnapshot;
  if (mdcfetsratesnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
inline bool MarketData::has_mdcfetsfxcnymiddleprice() const {
  return this != internal_default_instance() && mdcfetsfxcnymiddleprice_ != NULL;
}
inline void MarketData::clear_mdcfetsfxcnymiddleprice() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxcnymiddleprice_ != NULL) delete mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice& MarketData::mdcfetsfxcnymiddleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  return mdcfetsfxcnymiddleprice_ != NULL ? *mdcfetsfxcnymiddleprice_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* MarketData::mutable_mdcfetsfxcnymiddleprice() {
  
  if (mdcfetsfxcnymiddleprice_ == NULL) {
    mdcfetsfxcnymiddleprice_ = new ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  return mdcfetsfxcnymiddleprice_;
}
inline ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* MarketData::release_mdcfetsfxcnymiddleprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* temp = mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdcfetsfxcnymiddleprice(::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* mdcfetsfxcnymiddleprice) {
  delete mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = mdcfetsfxcnymiddleprice;
  if (mdcfetsfxcnymiddleprice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
}

// optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
inline bool MarketData::has_mdiopvsnapshot() const {
  return this != internal_default_instance() && mdiopvsnapshot_ != NULL;
}
inline void MarketData::clear_mdiopvsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdiopvsnapshot_ != NULL) delete mdiopvsnapshot_;
  mdiopvsnapshot_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDIopvSnapshot& MarketData::mdiopvsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  return mdiopvsnapshot_ != NULL ? *mdiopvsnapshot_
                         : *::com::htsc::mdc::insight::model::MDIopvSnapshot::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDIopvSnapshot* MarketData::mutable_mdiopvsnapshot() {
  
  if (mdiopvsnapshot_ == NULL) {
    mdiopvsnapshot_ = new ::com::htsc::mdc::insight::model::MDIopvSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  return mdiopvsnapshot_;
}
inline ::com::htsc::mdc::insight::model::MDIopvSnapshot* MarketData::release_mdiopvsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  
  ::com::htsc::mdc::insight::model::MDIopvSnapshot* temp = mdiopvsnapshot_;
  mdiopvsnapshot_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdiopvsnapshot(::com::htsc::mdc::insight::model::MDIopvSnapshot* mdiopvsnapshot) {
  delete mdiopvsnapshot_;
  mdiopvsnapshot_ = mdiopvsnapshot;
  if (mdiopvsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
inline bool MarketData::has_mdchinabondbenchmark() const {
  return this != internal_default_instance() && mdchinabondbenchmark_ != NULL;
}
inline void MarketData::clear_mdchinabondbenchmark() {
  if (GetArenaNoVirtual() == NULL && mdchinabondbenchmark_ != NULL) delete mdchinabondbenchmark_;
  mdchinabondbenchmark_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDChinaBondBenchmark& MarketData::mdchinabondbenchmark() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  return mdchinabondbenchmark_ != NULL ? *mdchinabondbenchmark_
                         : *::com::htsc::mdc::insight::model::MDChinaBondBenchmark::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* MarketData::mutable_mdchinabondbenchmark() {
  
  if (mdchinabondbenchmark_ == NULL) {
    mdchinabondbenchmark_ = new ::com::htsc::mdc::insight::model::MDChinaBondBenchmark;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  return mdchinabondbenchmark_;
}
inline ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* MarketData::release_mdchinabondbenchmark() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  
  ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* temp = mdchinabondbenchmark_;
  mdchinabondbenchmark_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdchinabondbenchmark(::com::htsc::mdc::insight::model::MDChinaBondBenchmark* mdchinabondbenchmark) {
  delete mdchinabondbenchmark_;
  mdchinabondbenchmark_ = mdchinabondbenchmark;
  if (mdchinabondbenchmark) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
}

// optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
inline bool MarketData::has_mdicetrace() const {
  return this != internal_default_instance() && mdicetrace_ != NULL;
}
inline void MarketData::clear_mdicetrace() {
  if (GetArenaNoVirtual() == NULL && mdicetrace_ != NULL) delete mdicetrace_;
  mdicetrace_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MDIceTrace& MarketData::mdicetrace() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  return mdicetrace_ != NULL ? *mdicetrace_
                         : *::com::htsc::mdc::insight::model::MDIceTrace::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MDIceTrace* MarketData::mutable_mdicetrace() {
  
  if (mdicetrace_ == NULL) {
    mdicetrace_ = new ::com::htsc::mdc::insight::model::MDIceTrace;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  return mdicetrace_;
}
inline ::com::htsc::mdc::insight::model::MDIceTrace* MarketData::release_mdicetrace() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  
  ::com::htsc::mdc::insight::model::MDIceTrace* temp = mdicetrace_;
  mdicetrace_ = NULL;
  return temp;
}
inline void MarketData::set_allocated_mdicetrace(::com::htsc::mdc::insight::model::MDIceTrace* mdicetrace) {
  delete mdicetrace_;
  mdicetrace_ = mdicetrace;
  if (mdicetrace) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
}

inline const MarketData* MarketData::internal_default_instance() {
  return &MarketData_default_instance_.get();
}
// -------------------------------------------------------------------

// MarketDataStream

// optional bool isCompressed = 1;
inline void MarketDataStream::clear_iscompressed() {
  iscompressed_ = false;
}
inline bool MarketDataStream::iscompressed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.isCompressed)
  return iscompressed_;
}
inline void MarketDataStream::set_iscompressed(bool value) {
  
  iscompressed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.isCompressed)
}

// optional int32 originalLength = 2;
inline void MarketDataStream::clear_originallength() {
  originallength_ = 0;
}
inline ::google::protobuf::int32 MarketDataStream::originallength() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.originalLength)
  return originallength_;
}
inline void MarketDataStream::set_originallength(::google::protobuf::int32 value) {
  
  originallength_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.originalLength)
}

// optional bytes compressedData = 3;
inline void MarketDataStream::clear_compresseddata() {
  compresseddata_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MarketDataStream::compresseddata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  return compresseddata_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketDataStream::set_compresseddata(const ::std::string& value) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
inline void MarketDataStream::set_compresseddata(const char* value) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
inline void MarketDataStream::set_compresseddata(const void* value, size_t size) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
inline ::std::string* MarketDataStream::mutable_compresseddata() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  return compresseddata_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MarketDataStream::release_compresseddata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  
  return compresseddata_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MarketDataStream::set_allocated_compresseddata(::std::string* compresseddata) {
  if (compresseddata != NULL) {
    
  } else {
    
  }
  compresseddata_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), compresseddata);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}

// optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
inline bool MarketDataStream::has_marketdatalist() const {
  return this != internal_default_instance() && marketdatalist_ != NULL;
}
inline void MarketDataStream::clear_marketdatalist() {
  if (GetArenaNoVirtual() == NULL && marketdatalist_ != NULL) delete marketdatalist_;
  marketdatalist_ = NULL;
}
inline const ::com::htsc::mdc::insight::model::MarketDataList& MarketDataStream::marketdatalist() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  return marketdatalist_ != NULL ? *marketdatalist_
                         : *::com::htsc::mdc::insight::model::MarketDataList::internal_default_instance();
}
inline ::com::htsc::mdc::insight::model::MarketDataList* MarketDataStream::mutable_marketdatalist() {
  
  if (marketdatalist_ == NULL) {
    marketdatalist_ = new ::com::htsc::mdc::insight::model::MarketDataList;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  return marketdatalist_;
}
inline ::com::htsc::mdc::insight::model::MarketDataList* MarketDataStream::release_marketdatalist() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  
  ::com::htsc::mdc::insight::model::MarketDataList* temp = marketdatalist_;
  marketdatalist_ = NULL;
  return temp;
}
inline void MarketDataStream::set_allocated_marketdatalist(::com::htsc::mdc::insight::model::MarketDataList* marketdatalist) {
  delete marketdatalist_;
  marketdatalist_ = marketdatalist;
  if (marketdatalist) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
}

// optional int32 totalNumber = 5;
inline void MarketDataStream::clear_totalnumber() {
  totalnumber_ = 0;
}
inline ::google::protobuf::int32 MarketDataStream::totalnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.totalNumber)
  return totalnumber_;
}
inline void MarketDataStream::set_totalnumber(::google::protobuf::int32 value) {
  
  totalnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.totalNumber)
}

// optional int32 serial = 6;
inline void MarketDataStream::clear_serial() {
  serial_ = 0;
}
inline ::google::protobuf::int32 MarketDataStream::serial() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.serial)
  return serial_;
}
inline void MarketDataStream::set_serial(::google::protobuf::int32 value) {
  
  serial_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.serial)
}

// optional bool isFinished = 7;
inline void MarketDataStream::clear_isfinished() {
  isfinished_ = false;
}
inline bool MarketDataStream::isfinished() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.isFinished)
  return isfinished_;
}
inline void MarketDataStream::set_isfinished(bool value) {
  
  isfinished_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.isFinished)
}

inline const MarketDataStream* MarketDataStream::internal_default_instance() {
  return &MarketDataStream_default_instance_.get();
}
// -------------------------------------------------------------------

// MarketDataList

// repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
inline int MarketDataList::marketdatas_size() const {
  return marketdatas_.size();
}
inline void MarketDataList::clear_marketdatas() {
  marketdatas_.Clear();
}
inline const ::com::htsc::mdc::insight::model::MarketData& MarketDataList::marketdatas(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Get(index);
}
inline ::com::htsc::mdc::insight::model::MarketData* MarketDataList::mutable_marketdatas(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Mutable(index);
}
inline ::com::htsc::mdc::insight::model::MarketData* MarketDataList::add_marketdatas() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >*
MarketDataList::mutable_marketdatas() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return &marketdatas_;
}
inline const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >&
MarketDataList::marketdatas() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_;
}

inline const MarketDataList* MarketDataList::internal_default_instance() {
  return &MarketDataList_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MarketData_2eproto__INCLUDED
