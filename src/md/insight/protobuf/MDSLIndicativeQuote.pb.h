// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSLIndicativeQuote.proto

#ifndef PROTOBUF_MDSLIndicativeQuote_2eproto__INCLUDED
#define PROTOBUF_MDSLIndicativeQuote_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3001000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3001000 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/unknown_field_set.h>
#include "ESecurityIDSource.pb.h"
#include "ESecurityType.pb.h"
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

// Internal implementation detail -- do not call these.
void protobuf_AddDesc_MDSLIndicativeQuote_2eproto();
void protobuf_InitDefaults_MDSLIndicativeQuote_2eproto();
void protobuf_AssignDesc_MDSLIndicativeQuote_2eproto();
void protobuf_ShutdownFile_MDSLIndicativeQuote_2eproto();

class MDSLIndicativeQuote;

// ===================================================================

class MDSLIndicativeQuote : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:com.htsc.mdc.insight.model.MDSLIndicativeQuote) */ {
 public:
  MDSLIndicativeQuote();
  virtual ~MDSLIndicativeQuote();

  MDSLIndicativeQuote(const MDSLIndicativeQuote& from);

  inline MDSLIndicativeQuote& operator=(const MDSLIndicativeQuote& from) {
    CopyFrom(from);
    return *this;
  }

  static const ::google::protobuf::Descriptor* descriptor();
  static const MDSLIndicativeQuote& default_instance();

  static const MDSLIndicativeQuote* internal_default_instance();

  void Swap(MDSLIndicativeQuote* other);

  // implements Message ----------------------------------------------

  inline MDSLIndicativeQuote* New() const { return New(NULL); }

  MDSLIndicativeQuote* New(::google::protobuf::Arena* arena) const;
  void CopyFrom(const ::google::protobuf::Message& from);
  void MergeFrom(const ::google::protobuf::Message& from);
  void CopyFrom(const MDSLIndicativeQuote& from);
  void MergeFrom(const MDSLIndicativeQuote& from);
  void Clear();
  bool IsInitialized() const;

  size_t ByteSizeLong() const;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input);
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* output) const;
  ::google::protobuf::uint8* SerializeWithCachedSizesToArray(::google::protobuf::uint8* output) const {
    return InternalSerializeWithCachedSizesToArray(false, output);
  }
  int GetCachedSize() const { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(MDSLIndicativeQuote* other);
  void UnsafeMergeFrom(const MDSLIndicativeQuote& from);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // optional string HTSCSecurityID = 1;
  void clear_htscsecurityid();
  static const int kHTSCSecurityIDFieldNumber = 1;
  const ::std::string& htscsecurityid() const;
  void set_htscsecurityid(const ::std::string& value);
  void set_htscsecurityid(const char* value);
  void set_htscsecurityid(const char* value, size_t size);
  ::std::string* mutable_htscsecurityid();
  ::std::string* release_htscsecurityid();
  void set_allocated_htscsecurityid(::std::string* htscsecurityid);

  // optional int32 MDDate = 2;
  void clear_mddate();
  static const int kMDDateFieldNumber = 2;
  ::google::protobuf::int32 mddate() const;
  void set_mddate(::google::protobuf::int32 value);

  // optional int32 MDTime = 3;
  void clear_mdtime();
  static const int kMDTimeFieldNumber = 3;
  ::google::protobuf::int32 mdtime() const;
  void set_mdtime(::google::protobuf::int32 value);

  // optional int64 DataTimestamp = 4;
  void clear_datatimestamp();
  static const int kDataTimestampFieldNumber = 4;
  ::google::protobuf::int64 datatimestamp() const;
  void set_datatimestamp(::google::protobuf::int64 value);

  // optional string TradingPhaseCode = 5;
  void clear_tradingphasecode();
  static const int kTradingPhaseCodeFieldNumber = 5;
  const ::std::string& tradingphasecode() const;
  void set_tradingphasecode(const ::std::string& value);
  void set_tradingphasecode(const char* value);
  void set_tradingphasecode(const char* value, size_t size);
  ::std::string* mutable_tradingphasecode();
  ::std::string* release_tradingphasecode();
  void set_allocated_tradingphasecode(::std::string* tradingphasecode);

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  void clear_securityidsource();
  static const int kSecurityIDSourceFieldNumber = 6;
  ::com::htsc::mdc::model::ESecurityIDSource securityidsource() const;
  void set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value);

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  void clear_securitytype();
  static const int kSecurityTypeFieldNumber = 7;
  ::com::htsc::mdc::model::ESecurityType securitytype() const;
  void set_securitytype(::com::htsc::mdc::model::ESecurityType value);

  // optional int64 LastPx = 8;
  void clear_lastpx();
  static const int kLastPxFieldNumber = 8;
  ::google::protobuf::int64 lastpx() const;
  void set_lastpx(::google::protobuf::int64 value);

  // optional int64 PreClosePx = 9;
  void clear_preclosepx();
  static const int kPreClosePxFieldNumber = 9;
  ::google::protobuf::int64 preclosepx() const;
  void set_preclosepx(::google::protobuf::int64 value);

  // optional int64 HtscLendAmount = 10;
  void clear_htsclendamount();
  static const int kHtscLendAmountFieldNumber = 10;
  ::google::protobuf::int64 htsclendamount() const;
  void set_htsclendamount(::google::protobuf::int64 value);

  // optional string HtscLendTerms = 11;
  void clear_htsclendterms();
  static const int kHtscLendTermsFieldNumber = 11;
  const ::std::string& htsclendterms() const;
  void set_htsclendterms(const ::std::string& value);
  void set_htsclendterms(const char* value);
  void set_htsclendterms(const char* value, size_t size);
  ::std::string* mutable_htsclendterms();
  ::std::string* release_htsclendterms();
  void set_allocated_htsclendterms(::std::string* htsclendterms);

  // optional int64 HtscBestLendRate = 12;
  void clear_htscbestlendrate();
  static const int kHtscBestLendRateFieldNumber = 12;
  ::google::protobuf::int64 htscbestlendrate() const;
  void set_htscbestlendrate(::google::protobuf::int64 value);

  // optional int64 HtscBorrowAmount = 13;
  void clear_htscborrowamount();
  static const int kHtscBorrowAmountFieldNumber = 13;
  ::google::protobuf::int64 htscborrowamount() const;
  void set_htscborrowamount(::google::protobuf::int64 value);

  // optional string HtscBorrowTerms = 14;
  void clear_htscborrowterms();
  static const int kHtscBorrowTermsFieldNumber = 14;
  const ::std::string& htscborrowterms() const;
  void set_htscborrowterms(const ::std::string& value);
  void set_htscborrowterms(const char* value);
  void set_htscborrowterms(const char* value, size_t size);
  ::std::string* mutable_htscborrowterms();
  ::std::string* release_htscborrowterms();
  void set_allocated_htscborrowterms(::std::string* htscborrowterms);

  // optional int64 HtscBorrowRate = 15;
  void clear_htscborrowrate();
  static const int kHtscBorrowRateFieldNumber = 15;
  ::google::protobuf::int64 htscborrowrate() const;
  void set_htscborrowrate(::google::protobuf::int64 value);

  // optional int64 TradeVolume = 16;
  void clear_tradevolume();
  static const int kTradeVolumeFieldNumber = 16;
  ::google::protobuf::int64 tradevolume() const;
  void set_tradevolume(::google::protobuf::int64 value);

  // optional int64 TradeMoney = 17;
  void clear_trademoney();
  static const int kTradeMoneyFieldNumber = 17;
  ::google::protobuf::int64 trademoney() const;
  void set_trademoney(::google::protobuf::int64 value);

  // optional int64 PreTradeVolume = 18;
  void clear_pretradevolume();
  static const int kPreTradeVolumeFieldNumber = 18;
  ::google::protobuf::int64 pretradevolume() const;
  void set_pretradevolume(::google::protobuf::int64 value);

  // optional int64 PreTradeMoney = 19;
  void clear_pretrademoney();
  static const int kPreTradeMoneyFieldNumber = 19;
  ::google::protobuf::int64 pretrademoney() const;
  void set_pretrademoney(::google::protobuf::int64 value);

  // optional int32 DataMultiplePowerOf10 = 20;
  void clear_datamultiplepowerof10();
  static const int kDataMultiplePowerOf10FieldNumber = 20;
  ::google::protobuf::int32 datamultiplepowerof10() const;
  void set_datamultiplepowerof10(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:com.htsc.mdc.insight.model.MDSLIndicativeQuote)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr htscsecurityid_;
  ::google::protobuf::internal::ArenaStringPtr tradingphasecode_;
  ::google::protobuf::internal::ArenaStringPtr htsclendterms_;
  ::google::protobuf::internal::ArenaStringPtr htscborrowterms_;
  ::google::protobuf::int32 mddate_;
  ::google::protobuf::int32 mdtime_;
  ::google::protobuf::int64 datatimestamp_;
  int securityidsource_;
  int securitytype_;
  ::google::protobuf::int64 lastpx_;
  ::google::protobuf::int64 preclosepx_;
  ::google::protobuf::int64 htsclendamount_;
  ::google::protobuf::int64 htscbestlendrate_;
  ::google::protobuf::int64 htscborrowamount_;
  ::google::protobuf::int64 htscborrowrate_;
  ::google::protobuf::int64 tradevolume_;
  ::google::protobuf::int64 trademoney_;
  ::google::protobuf::int64 pretradevolume_;
  ::google::protobuf::int64 pretrademoney_;
  ::google::protobuf::int32 datamultiplepowerof10_;
  mutable int _cached_size_;
  friend void  protobuf_InitDefaults_MDSLIndicativeQuote_2eproto_impl();
  friend void  protobuf_AddDesc_MDSLIndicativeQuote_2eproto_impl();
  friend void protobuf_AssignDesc_MDSLIndicativeQuote_2eproto();
  friend void protobuf_ShutdownFile_MDSLIndicativeQuote_2eproto();

  void InitAsDefaultInstance();
};
extern ::google::protobuf::internal::ExplicitlyConstructed<MDSLIndicativeQuote> MDSLIndicativeQuote_default_instance_;

// ===================================================================


// ===================================================================

#if !PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSLIndicativeQuote

// optional string HTSCSecurityID = 1;
inline void MDSLIndicativeQuote::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLIndicativeQuote::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
inline void MDSLIndicativeQuote::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
inline void MDSLIndicativeQuote::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}
inline ::std::string* MDSLIndicativeQuote::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLIndicativeQuote::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HTSCSecurityID)
}

// optional int32 MDDate = 2;
inline void MDSLIndicativeQuote::clear_mddate() {
  mddate_ = 0;
}
inline ::google::protobuf::int32 MDSLIndicativeQuote::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDDate)
  return mddate_;
}
inline void MDSLIndicativeQuote::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDDate)
}

// optional int32 MDTime = 3;
inline void MDSLIndicativeQuote::clear_mdtime() {
  mdtime_ = 0;
}
inline ::google::protobuf::int32 MDSLIndicativeQuote::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDTime)
  return mdtime_;
}
inline void MDSLIndicativeQuote::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.MDTime)
}

// optional int64 DataTimestamp = 4;
inline void MDSLIndicativeQuote::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataTimestamp)
  return datatimestamp_;
}
inline void MDSLIndicativeQuote::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
inline void MDSLIndicativeQuote::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLIndicativeQuote::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
inline void MDSLIndicativeQuote::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
inline void MDSLIndicativeQuote::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}
inline ::std::string* MDSLIndicativeQuote::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLIndicativeQuote::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
inline void MDSLIndicativeQuote::clear_securityidsource() {
  securityidsource_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityIDSource MDSLIndicativeQuote::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
inline void MDSLIndicativeQuote::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
inline void MDSLIndicativeQuote::clear_securitytype() {
  securitytype_ = 0;
}
inline ::com::htsc::mdc::model::ESecurityType MDSLIndicativeQuote::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
inline void MDSLIndicativeQuote::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.securityType)
}

// optional int64 LastPx = 8;
inline void MDSLIndicativeQuote::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.LastPx)
  return lastpx_;
}
inline void MDSLIndicativeQuote::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.LastPx)
}

// optional int64 PreClosePx = 9;
inline void MDSLIndicativeQuote::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreClosePx)
  return preclosepx_;
}
inline void MDSLIndicativeQuote::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreClosePx)
}

// optional int64 HtscLendAmount = 10;
inline void MDSLIndicativeQuote::clear_htsclendamount() {
  htsclendamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::htsclendamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendAmount)
  return htsclendamount_;
}
inline void MDSLIndicativeQuote::set_htsclendamount(::google::protobuf::int64 value) {
  
  htsclendamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendAmount)
}

// optional string HtscLendTerms = 11;
inline void MDSLIndicativeQuote::clear_htsclendterms() {
  htsclendterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLIndicativeQuote::htsclendterms() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  return htsclendterms_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_htsclendterms(const ::std::string& value) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
inline void MDSLIndicativeQuote::set_htsclendterms(const char* value) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
inline void MDSLIndicativeQuote::set_htsclendterms(const char* value, size_t size) {
  
  htsclendterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}
inline ::std::string* MDSLIndicativeQuote::mutable_htsclendterms() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  return htsclendterms_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLIndicativeQuote::release_htsclendterms() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
  
  return htsclendterms_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_allocated_htsclendterms(::std::string* htsclendterms) {
  if (htsclendterms != NULL) {
    
  } else {
    
  }
  htsclendterms_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htsclendterms);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscLendTerms)
}

// optional int64 HtscBestLendRate = 12;
inline void MDSLIndicativeQuote::clear_htscbestlendrate() {
  htscbestlendrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::htscbestlendrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBestLendRate)
  return htscbestlendrate_;
}
inline void MDSLIndicativeQuote::set_htscbestlendrate(::google::protobuf::int64 value) {
  
  htscbestlendrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBestLendRate)
}

// optional int64 HtscBorrowAmount = 13;
inline void MDSLIndicativeQuote::clear_htscborrowamount() {
  htscborrowamount_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::htscborrowamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowAmount)
  return htscborrowamount_;
}
inline void MDSLIndicativeQuote::set_htscborrowamount(::google::protobuf::int64 value) {
  
  htscborrowamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowAmount)
}

// optional string HtscBorrowTerms = 14;
inline void MDSLIndicativeQuote::clear_htscborrowterms() {
  htscborrowterms_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& MDSLIndicativeQuote::htscborrowterms() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  return htscborrowterms_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_htscborrowterms(const ::std::string& value) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
inline void MDSLIndicativeQuote::set_htscborrowterms(const char* value) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
inline void MDSLIndicativeQuote::set_htscborrowterms(const char* value, size_t size) {
  
  htscborrowterms_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}
inline ::std::string* MDSLIndicativeQuote::mutable_htscborrowterms() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  return htscborrowterms_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* MDSLIndicativeQuote::release_htscborrowterms() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
  
  return htscborrowterms_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void MDSLIndicativeQuote::set_allocated_htscborrowterms(::std::string* htscborrowterms) {
  if (htscborrowterms != NULL) {
    
  } else {
    
  }
  htscborrowterms_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscborrowterms);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowTerms)
}

// optional int64 HtscBorrowRate = 15;
inline void MDSLIndicativeQuote::clear_htscborrowrate() {
  htscborrowrate_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::htscborrowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowRate)
  return htscborrowrate_;
}
inline void MDSLIndicativeQuote::set_htscborrowrate(::google::protobuf::int64 value) {
  
  htscborrowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.HtscBorrowRate)
}

// optional int64 TradeVolume = 16;
inline void MDSLIndicativeQuote::clear_tradevolume() {
  tradevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::tradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeVolume)
  return tradevolume_;
}
inline void MDSLIndicativeQuote::set_tradevolume(::google::protobuf::int64 value) {
  
  tradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeVolume)
}

// optional int64 TradeMoney = 17;
inline void MDSLIndicativeQuote::clear_trademoney() {
  trademoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::trademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeMoney)
  return trademoney_;
}
inline void MDSLIndicativeQuote::set_trademoney(::google::protobuf::int64 value) {
  
  trademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.TradeMoney)
}

// optional int64 PreTradeVolume = 18;
inline void MDSLIndicativeQuote::clear_pretradevolume() {
  pretradevolume_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::pretradevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeVolume)
  return pretradevolume_;
}
inline void MDSLIndicativeQuote::set_pretradevolume(::google::protobuf::int64 value) {
  
  pretradevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeVolume)
}

// optional int64 PreTradeMoney = 19;
inline void MDSLIndicativeQuote::clear_pretrademoney() {
  pretrademoney_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 MDSLIndicativeQuote::pretrademoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeMoney)
  return pretrademoney_;
}
inline void MDSLIndicativeQuote::set_pretrademoney(::google::protobuf::int64 value) {
  
  pretrademoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.PreTradeMoney)
}

// optional int32 DataMultiplePowerOf10 = 20;
inline void MDSLIndicativeQuote::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
inline ::google::protobuf::int32 MDSLIndicativeQuote::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
inline void MDSLIndicativeQuote::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSLIndicativeQuote.DataMultiplePowerOf10)
}

inline const MDSLIndicativeQuote* MDSLIndicativeQuote::internal_default_instance() {
  return &MDSLIndicativeQuote_default_instance_.get();
}
#endif  // !PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_MDSLIndicativeQuote_2eproto__INCLUDED
