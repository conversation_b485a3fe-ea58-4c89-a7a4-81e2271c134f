// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDUSAOrder.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDUSAOrder.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDUSAOrder_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDUSAOrder_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDUSAOrder_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDUSAOrder_2eproto() {
  protobuf_AddDesc_MDUSAOrder_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDUSAOrder.proto");
  GOOGLE_CHECK(file != NULL);
  MDUSAOrder_descriptor_ = file->message_type(0);
  static const int MDUSAOrder_offsets_[21] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, nanosecond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, orderindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, originalorderindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, ordertype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, orderprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, orderqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, orderbsflag_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, attribution_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, trackingnum_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, timeindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, dataindex_),
  };
  MDUSAOrder_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDUSAOrder_descriptor_,
      MDUSAOrder::internal_default_instance(),
      MDUSAOrder_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDUSAOrder),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDUSAOrder, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDUSAOrder_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDUSAOrder_descriptor_, MDUSAOrder::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDUSAOrder_2eproto() {
  MDUSAOrder_default_instance_.Shutdown();
  delete MDUSAOrder_reflection_;
}

void protobuf_InitDefaults_MDUSAOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDUSAOrder_default_instance_.DefaultConstruct();
  MDUSAOrder_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDUSAOrder_2eproto_once_);
void protobuf_InitDefaults_MDUSAOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDUSAOrder_2eproto_once_,
                 &protobuf_InitDefaults_MDUSAOrder_2eproto_impl);
}
void protobuf_AddDesc_MDUSAOrder_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDUSAOrder_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MDUSAOrder.proto\022\032com.htsc.mdc.insight"
    ".model\032\027ESecurityIDSource.proto\032\023ESecuri"
    "tyType.proto\"\225\004\n\nMDUSAOrder\022\026\n\016HTSCSecur"
    "ityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 "
    "\001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securityID"
    "Source\030\005 \001(\0162%.com.htsc.mdc.model.ESecur"
    "ityIDSource\0227\n\014securityType\030\006 \001(\0162!.com."
    "htsc.mdc.model.ESecurityType\022\024\n\014Exchange"
    "Date\030\007 \001(\005\022\024\n\014ExchangeTime\030\010 \001(\005\022\022\n\nNano"
    "second\030\t \001(\005\022\021\n\tChannelNo\030\n \001(\005\022\022\n\nOrder"
    "Index\030\013 \001(\003\022\032\n\022OriginalOrderIndex\030\014 \001(\003\022"
    "\021\n\tOrderType\030\r \001(\005\022\022\n\nOrderPrice\030\016 \001(\003\022\020"
    "\n\010OrderQty\030\017 \001(\001\022\023\n\013OrderBSFlag\030\020 \001(\005\022\023\n"
    "\013Attribution\030\021 \001(\t\022\023\n\013TrackingNum\030\022 \001(\005\022"
    "\035\n\025DataMultiplePowerOf10\030\023 \001(\005\022\021\n\tTimeIn"
    "dex\030\024 \001(\005\022\021\n\tDataIndex\030\025 \001(\003B3\n\032com.htsc"
    ".mdc.insight.modelB\020MDUSAOrderProtosH\001\240\001"
    "\001b\006proto3", 689);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDUSAOrder.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDUSAOrder_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDUSAOrder_2eproto_once_);
void protobuf_AddDesc_MDUSAOrder_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDUSAOrder_2eproto_once_,
                 &protobuf_AddDesc_MDUSAOrder_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDUSAOrder_2eproto {
  StaticDescriptorInitializer_MDUSAOrder_2eproto() {
    protobuf_AddDesc_MDUSAOrder_2eproto();
  }
} static_descriptor_initializer_MDUSAOrder_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDUSAOrder::kHTSCSecurityIDFieldNumber;
const int MDUSAOrder::kMDDateFieldNumber;
const int MDUSAOrder::kMDTimeFieldNumber;
const int MDUSAOrder::kDataTimestampFieldNumber;
const int MDUSAOrder::kSecurityIDSourceFieldNumber;
const int MDUSAOrder::kSecurityTypeFieldNumber;
const int MDUSAOrder::kExchangeDateFieldNumber;
const int MDUSAOrder::kExchangeTimeFieldNumber;
const int MDUSAOrder::kNanosecondFieldNumber;
const int MDUSAOrder::kChannelNoFieldNumber;
const int MDUSAOrder::kOrderIndexFieldNumber;
const int MDUSAOrder::kOriginalOrderIndexFieldNumber;
const int MDUSAOrder::kOrderTypeFieldNumber;
const int MDUSAOrder::kOrderPriceFieldNumber;
const int MDUSAOrder::kOrderQtyFieldNumber;
const int MDUSAOrder::kOrderBSFlagFieldNumber;
const int MDUSAOrder::kAttributionFieldNumber;
const int MDUSAOrder::kTrackingNumFieldNumber;
const int MDUSAOrder::kDataMultiplePowerOf10FieldNumber;
const int MDUSAOrder::kTimeIndexFieldNumber;
const int MDUSAOrder::kDataIndexFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDUSAOrder::MDUSAOrder()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDUSAOrder_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDUSAOrder)
}

void MDUSAOrder::InitAsDefaultInstance() {
}

MDUSAOrder::MDUSAOrder(const MDUSAOrder& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDUSAOrder)
}

void MDUSAOrder::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  attribution_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&timeindex_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(timeindex_));
  _cached_size_ = 0;
}

MDUSAOrder::~MDUSAOrder() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDUSAOrder)
  SharedDtor();
}

void MDUSAOrder::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  attribution_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDUSAOrder::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDUSAOrder::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDUSAOrder_descriptor_;
}

const MDUSAOrder& MDUSAOrder::default_instance() {
  protobuf_InitDefaults_MDUSAOrder_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDUSAOrder> MDUSAOrder_default_instance_;

MDUSAOrder* MDUSAOrder::New(::google::protobuf::Arena* arena) const {
  MDUSAOrder* n = new MDUSAOrder;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDUSAOrder::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDUSAOrder)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDUSAOrder, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDUSAOrder*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, exchangetime_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(nanosecond_, orderqty_);
  ZR_(trackingnum_, timeindex_);
  attribution_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDUSAOrder::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDUSAOrder)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 7;
      case 7: {
        if (tag == 56) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 8;
      case 8: {
        if (tag == 64) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_Nanosecond;
        break;
      }

      // optional int32 Nanosecond = 9;
      case 9: {
        if (tag == 72) {
         parse_Nanosecond:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &nanosecond_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 10;
      case 10: {
        if (tag == 80) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_OrderIndex;
        break;
      }

      // optional int64 OrderIndex = 11;
      case 11: {
        if (tag == 88) {
         parse_OrderIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_OriginalOrderIndex;
        break;
      }

      // optional int64 OriginalOrderIndex = 12;
      case 12: {
        if (tag == 96) {
         parse_OriginalOrderIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &originalorderindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_OrderType;
        break;
      }

      // optional int32 OrderType = 13;
      case 13: {
        if (tag == 104) {
         parse_OrderType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &ordertype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OrderPrice;
        break;
      }

      // optional int64 OrderPrice = 14;
      case 14: {
        if (tag == 112) {
         parse_OrderPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &orderprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_OrderQty;
        break;
      }

      // optional double OrderQty = 15;
      case 15: {
        if (tag == 121) {
         parse_OrderQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &orderqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_OrderBSFlag;
        break;
      }

      // optional int32 OrderBSFlag = 16;
      case 16: {
        if (tag == 128) {
         parse_OrderBSFlag:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &orderbsflag_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_Attribution;
        break;
      }

      // optional string Attribution = 17;
      case 17: {
        if (tag == 138) {
         parse_Attribution:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_attribution()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->attribution().data(), this->attribution().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDUSAOrder.Attribution"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_TrackingNum;
        break;
      }

      // optional int32 TrackingNum = 18;
      case 18: {
        if (tag == 144) {
         parse_TrackingNum:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &trackingnum_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 19;
      case 19: {
        if (tag == 152) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_TimeIndex;
        break;
      }

      // optional int32 TimeIndex = 20;
      case 20: {
        if (tag == 160) {
         parse_TimeIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &timeindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_DataIndex;
        break;
      }

      // optional int64 DataIndex = 21;
      case 21: {
        if (tag == 168) {
         parse_DataIndex:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &dataindex_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDUSAOrder)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDUSAOrder)
  return false;
#undef DO_
}

void MDUSAOrder::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDUSAOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->exchangetime(), output);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->nanosecond(), output);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->channelno(), output);
  }

  // optional int64 OrderIndex = 11;
  if (this->orderindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->orderindex(), output);
  }

  // optional int64 OriginalOrderIndex = 12;
  if (this->originalorderindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->originalorderindex(), output);
  }

  // optional int32 OrderType = 13;
  if (this->ordertype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->ordertype(), output);
  }

  // optional int64 OrderPrice = 14;
  if (this->orderprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->orderprice(), output);
  }

  // optional double OrderQty = 15;
  if (this->orderqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->orderqty(), output);
  }

  // optional int32 OrderBSFlag = 16;
  if (this->orderbsflag() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(16, this->orderbsflag(), output);
  }

  // optional string Attribution = 17;
  if (this->attribution().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->attribution().data(), this->attribution().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAOrder.Attribution");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      17, this->attribution(), output);
  }

  // optional int32 TrackingNum = 18;
  if (this->trackingnum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->trackingnum(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->datamultiplepowerof10(), output);
  }

  // optional int32 TimeIndex = 20;
  if (this->timeindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->timeindex(), output);
  }

  // optional int64 DataIndex = 21;
  if (this->dataindex() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->dataindex(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDUSAOrder)
}

::google::protobuf::uint8* MDUSAOrder::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDUSAOrder)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->exchangetime(), target);
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->nanosecond(), target);
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->channelno(), target);
  }

  // optional int64 OrderIndex = 11;
  if (this->orderindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->orderindex(), target);
  }

  // optional int64 OriginalOrderIndex = 12;
  if (this->originalorderindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->originalorderindex(), target);
  }

  // optional int32 OrderType = 13;
  if (this->ordertype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->ordertype(), target);
  }

  // optional int64 OrderPrice = 14;
  if (this->orderprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->orderprice(), target);
  }

  // optional double OrderQty = 15;
  if (this->orderqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->orderqty(), target);
  }

  // optional int32 OrderBSFlag = 16;
  if (this->orderbsflag() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(16, this->orderbsflag(), target);
  }

  // optional string Attribution = 17;
  if (this->attribution().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->attribution().data(), this->attribution().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDUSAOrder.Attribution");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        17, this->attribution(), target);
  }

  // optional int32 TrackingNum = 18;
  if (this->trackingnum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->trackingnum(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->datamultiplepowerof10(), target);
  }

  // optional int32 TimeIndex = 20;
  if (this->timeindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->timeindex(), target);
  }

  // optional int64 DataIndex = 21;
  if (this->dataindex() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->dataindex(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDUSAOrder)
  return target;
}

size_t MDUSAOrder::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDUSAOrder)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int32 ExchangeDate = 7;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 8;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 Nanosecond = 9;
  if (this->nanosecond() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->nanosecond());
  }

  // optional int32 ChannelNo = 10;
  if (this->channelno() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 OrderIndex = 11;
  if (this->orderindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderindex());
  }

  // optional int64 OriginalOrderIndex = 12;
  if (this->originalorderindex() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->originalorderindex());
  }

  // optional int32 OrderType = 13;
  if (this->ordertype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->ordertype());
  }

  // optional int64 OrderPrice = 14;
  if (this->orderprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->orderprice());
  }

  // optional double OrderQty = 15;
  if (this->orderqty() != 0) {
    total_size += 1 + 8;
  }

  // optional int32 OrderBSFlag = 16;
  if (this->orderbsflag() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->orderbsflag());
  }

  // optional string Attribution = 17;
  if (this->attribution().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->attribution());
  }

  // optional int32 TrackingNum = 18;
  if (this->trackingnum() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->trackingnum());
  }

  // optional int32 DataMultiplePowerOf10 = 19;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 TimeIndex = 20;
  if (this->timeindex() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->timeindex());
  }

  // optional int64 DataIndex = 21;
  if (this->dataindex() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->dataindex());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDUSAOrder::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDUSAOrder)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDUSAOrder* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDUSAOrder>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDUSAOrder)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDUSAOrder)
    UnsafeMergeFrom(*source);
  }
}

void MDUSAOrder::MergeFrom(const MDUSAOrder& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDUSAOrder)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDUSAOrder::UnsafeMergeFrom(const MDUSAOrder& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.nanosecond() != 0) {
    set_nanosecond(from.nanosecond());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.orderindex() != 0) {
    set_orderindex(from.orderindex());
  }
  if (from.originalorderindex() != 0) {
    set_originalorderindex(from.originalorderindex());
  }
  if (from.ordertype() != 0) {
    set_ordertype(from.ordertype());
  }
  if (from.orderprice() != 0) {
    set_orderprice(from.orderprice());
  }
  if (from.orderqty() != 0) {
    set_orderqty(from.orderqty());
  }
  if (from.orderbsflag() != 0) {
    set_orderbsflag(from.orderbsflag());
  }
  if (from.attribution().size() > 0) {

    attribution_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.attribution_);
  }
  if (from.trackingnum() != 0) {
    set_trackingnum(from.trackingnum());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.timeindex() != 0) {
    set_timeindex(from.timeindex());
  }
  if (from.dataindex() != 0) {
    set_dataindex(from.dataindex());
  }
}

void MDUSAOrder::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDUSAOrder)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDUSAOrder::CopyFrom(const MDUSAOrder& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDUSAOrder)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDUSAOrder::IsInitialized() const {

  return true;
}

void MDUSAOrder::Swap(MDUSAOrder* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDUSAOrder::InternalSwap(MDUSAOrder* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(nanosecond_, other->nanosecond_);
  std::swap(channelno_, other->channelno_);
  std::swap(orderindex_, other->orderindex_);
  std::swap(originalorderindex_, other->originalorderindex_);
  std::swap(ordertype_, other->ordertype_);
  std::swap(orderprice_, other->orderprice_);
  std::swap(orderqty_, other->orderqty_);
  std::swap(orderbsflag_, other->orderbsflag_);
  attribution_.Swap(&other->attribution_);
  std::swap(trackingnum_, other->trackingnum_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(timeindex_, other->timeindex_);
  std::swap(dataindex_, other->dataindex_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDUSAOrder::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDUSAOrder_descriptor_;
  metadata.reflection = MDUSAOrder_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDUSAOrder

// optional string HTSCSecurityID = 1;
void MDUSAOrder::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSAOrder::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAOrder::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
void MDUSAOrder::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
void MDUSAOrder::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}
::std::string* MDUSAOrder::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSAOrder::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAOrder::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAOrder.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDUSAOrder::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDUSAOrder::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.MDDate)
  return mddate_;
}
void MDUSAOrder::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.MDDate)
}

// optional int32 MDTime = 3;
void MDUSAOrder::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDUSAOrder::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.MDTime)
  return mdtime_;
}
void MDUSAOrder::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDUSAOrder::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAOrder::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataTimestamp)
  return datatimestamp_;
}
void MDUSAOrder::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDUSAOrder::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDUSAOrder::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDUSAOrder::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDUSAOrder::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDUSAOrder::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDUSAOrder::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.securityType)
}

// optional int32 ExchangeDate = 7;
void MDUSAOrder::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDUSAOrder::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeDate)
  return exchangedate_;
}
void MDUSAOrder::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeDate)
}

// optional int32 ExchangeTime = 8;
void MDUSAOrder::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDUSAOrder::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeTime)
  return exchangetime_;
}
void MDUSAOrder::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ExchangeTime)
}

// optional int32 Nanosecond = 9;
void MDUSAOrder::clear_nanosecond() {
  nanosecond_ = 0;
}
::google::protobuf::int32 MDUSAOrder::nanosecond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.Nanosecond)
  return nanosecond_;
}
void MDUSAOrder::set_nanosecond(::google::protobuf::int32 value) {
  
  nanosecond_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.Nanosecond)
}

// optional int32 ChannelNo = 10;
void MDUSAOrder::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDUSAOrder::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.ChannelNo)
  return channelno_;
}
void MDUSAOrder::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.ChannelNo)
}

// optional int64 OrderIndex = 11;
void MDUSAOrder::clear_orderindex() {
  orderindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAOrder::orderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderIndex)
  return orderindex_;
}
void MDUSAOrder::set_orderindex(::google::protobuf::int64 value) {
  
  orderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderIndex)
}

// optional int64 OriginalOrderIndex = 12;
void MDUSAOrder::clear_originalorderindex() {
  originalorderindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAOrder::originalorderindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OriginalOrderIndex)
  return originalorderindex_;
}
void MDUSAOrder::set_originalorderindex(::google::protobuf::int64 value) {
  
  originalorderindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OriginalOrderIndex)
}

// optional int32 OrderType = 13;
void MDUSAOrder::clear_ordertype() {
  ordertype_ = 0;
}
::google::protobuf::int32 MDUSAOrder::ordertype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderType)
  return ordertype_;
}
void MDUSAOrder::set_ordertype(::google::protobuf::int32 value) {
  
  ordertype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderType)
}

// optional int64 OrderPrice = 14;
void MDUSAOrder::clear_orderprice() {
  orderprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAOrder::orderprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderPrice)
  return orderprice_;
}
void MDUSAOrder::set_orderprice(::google::protobuf::int64 value) {
  
  orderprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderPrice)
}

// optional double OrderQty = 15;
void MDUSAOrder::clear_orderqty() {
  orderqty_ = 0;
}
double MDUSAOrder::orderqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderQty)
  return orderqty_;
}
void MDUSAOrder::set_orderqty(double value) {
  
  orderqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderQty)
}

// optional int32 OrderBSFlag = 16;
void MDUSAOrder::clear_orderbsflag() {
  orderbsflag_ = 0;
}
::google::protobuf::int32 MDUSAOrder::orderbsflag() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.OrderBSFlag)
  return orderbsflag_;
}
void MDUSAOrder::set_orderbsflag(::google::protobuf::int32 value) {
  
  orderbsflag_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.OrderBSFlag)
}

// optional string Attribution = 17;
void MDUSAOrder::clear_attribution() {
  attribution_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDUSAOrder::attribution() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  return attribution_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAOrder::set_attribution(const ::std::string& value) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
void MDUSAOrder::set_attribution(const char* value) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
void MDUSAOrder::set_attribution(const char* value, size_t size) {
  
  attribution_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}
::std::string* MDUSAOrder::mutable_attribution() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  return attribution_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDUSAOrder::release_attribution() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
  
  return attribution_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDUSAOrder::set_allocated_attribution(::std::string* attribution) {
  if (attribution != NULL) {
    
  } else {
    
  }
  attribution_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), attribution);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDUSAOrder.Attribution)
}

// optional int32 TrackingNum = 18;
void MDUSAOrder::clear_trackingnum() {
  trackingnum_ = 0;
}
::google::protobuf::int32 MDUSAOrder::trackingnum() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.TrackingNum)
  return trackingnum_;
}
void MDUSAOrder::set_trackingnum(::google::protobuf::int32 value) {
  
  trackingnum_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.TrackingNum)
}

// optional int32 DataMultiplePowerOf10 = 19;
void MDUSAOrder::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDUSAOrder::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDUSAOrder::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataMultiplePowerOf10)
}

// optional int32 TimeIndex = 20;
void MDUSAOrder::clear_timeindex() {
  timeindex_ = 0;
}
::google::protobuf::int32 MDUSAOrder::timeindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.TimeIndex)
  return timeindex_;
}
void MDUSAOrder::set_timeindex(::google::protobuf::int32 value) {
  
  timeindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.TimeIndex)
}

// optional int64 DataIndex = 21;
void MDUSAOrder::clear_dataindex() {
  dataindex_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDUSAOrder::dataindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDUSAOrder.DataIndex)
  return dataindex_;
}
void MDUSAOrder::set_dataindex(::google::protobuf::int64 value) {
  
  dataindex_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDUSAOrder.DataIndex)
}

inline const MDUSAOrder* MDUSAOrder::internal_default_instance() {
  return &MDUSAOrder_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
