syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADFundFlowAnalysisProtos";
option optimize_for = SPEED;

// ADFundFlowAnalysis message represents fund flow analysis data for securities
message ADFundFlowAnalysis {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 6;
    
    // Super large order fund flow analysis
    ADFundFlowDetail SuperLargeOrder = 7;
    
    // Large order fund flow analysis
    ADFundFlowDetail LargeOrder = 8;
    
    // Medium order fund flow analysis
    ADFundFlowDetail MediumOrder = 9;
    
    // Small order fund flow analysis
    ADFundFlowDetail SmallOrder = 10;
    
    // Main order fund flow analysis
    ADFundFlowDetail MainOrder = 11;
    
    // Fund flow analysis for the last 5 days (main)
    ADFundFlowDetail TheLast5DaysMain = 12;
    
    // Fund flow analysis for the last 10 days (main)
    ADFundFlowDetail TheLast10DaysMain = 13;
    
    // Fund flow analysis for the last 20 days (main)
    ADFundFlowDetail TheLast20DaysMain = 14;
    
    // Fund flow analysis for the last 60 days (main)
    ADFundFlowDetail TheLast60DaysMain = 15;
    
    // Detailed fund flow list for the last 5 days (main)
    repeated ADFundFlowDetail TheLast5DaysMainList = 16;
    
    // Exchange date (YYYYMMDD format)
    int32 ExchangeDate = 17;
    
    // Exchange time (HHMMSS format)
    int32 ExchangeTime = 18;
    
    // Data scaling factor (power of 10 multiplier for value fields)
    int32 DataMultiplePowerOf10 = 19;
}

// ADFundFlowDetail represents detailed fund flow information
message ADFundFlowDetail {
    // Outflow value (scaled by parent message's DataMultiplePowerOf10)
    int64 OutflowValue = 1;
    
    // Inflow value (scaled by parent message's DataMultiplePowerOf10)
    int64 InflowValue = 2;
    
    // Outflow quantity (number of shares/units)
    int64 OutflowQty = 3;
    
    // Inflow quantity (number of shares/units)
    int64 InflowQty = 4;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 5;
}
