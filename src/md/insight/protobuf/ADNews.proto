syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityType.proto";
import "ESecurityIDSource.proto";

option java_package = "com.htsc.mdc.insight.model";
option java_outer_classname = "ADNewsProtos";
option optimize_for = SPEED;

// ADNews message represents news data with sentiment analysis for securities
message ADNews {
    // Security identifier from HTSC
    string HTSCSecurityID = 1;
    
    // Market data date (YYYYMMDD format)
    int32 MDDate = 2;
    
    // Market data time (HHMMSS format)
    int32 MDTime = 3;
    
    // Data timestamp (Unix timestamp in milliseconds)
    int64 DataTimestamp = 4;
    
    // Trading phase code
    string TradingPhaseCode = 5;
    
    // Security ID source (exchange identifier)
    com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
    
    // Security type (stock, bond, fund, etc.)
    com.htsc.mdc.model.ESecurityType securityType = 7;
    
    // News author
    string author = 8;
    
    // News content
    string content = 9;
    
    // Event category
    string eventCat = 10;
    
    // Event name
    string eventName = 11;
    
    // Event position/importance
    int32 eventPos = 12;
    
    // News ID
    string id = 13;
    
    // Industry classification
    string industry = 14;
    
    // News hotness/popularity score
    double newsHot = 15;
    
    // News publication time
    string newsTime = 16;
    
    // News summary
    string summ = 17;
    
    // News title
    string title = 18;
    
    // News URL
    string url = 19;
    
    // News emotion/sentiment analysis
    ADNewsEmotion newsEmotion = 20;
    
    // List of involved securities
    repeated ADInvolvedSecurity secuList = 21;
    
    // List of related persons
    repeated ADRelatedPerson perEmList = 22;
    
    // List of related companies
    repeated ADRelatedCompany comEmList = 23;
    
    // Data scaling factor (power of 10 multiplier for value fields)
    int32 DataMultiplePowerOf10 = 24;
    
    // Sentiment content data entries
    repeated ADNewsDataEntry sentimentContent = 25;
}

// ADNewsEmotion represents sentiment analysis scores
message ADNewsEmotion {
    // Negative sentiment score
    double negative = 1;
    
    // Neutral sentiment score
    double neutral = 2;
    
    // Position indicator
    int32 pos = 3;
    
    // Positive sentiment score
    double positive = 4;
}

// ADInvolvedSecurity represents securities involved in the news
message ADInvolvedSecurity {
    // Company code
    string comCode = 1;
    
    // Security code
    string secuCode = 2;
    
    // Security name
    string secuName = 3;
    
    // HTSC Security identifier
    string HTSCSecurityID = 4;
}

// ADRelatedCompany represents companies related to the news
message ADRelatedCompany {
    // Chinese company name
    string chiname = 1;
    
    // Company code
    string comCode = 2;
    
    // Company name
    string com = 3;
    
    // General position/importance
    int32 generalPos = 4;
    
    // Security code
    string secuCode = 5;
    
    // HTSC Security identifier
    string HTSCSecurityID = 6;
}

// ADRelatedPerson represents persons related to the news
message ADRelatedPerson {
    // Person name
    string per = 1;
    
    // Company code
    string comCode = 2;
    
    // Company name
    string com = 3;
    
    // General position/importance
    int32 generalPos = 4;
}

// ADNewsDataEntry represents key-value data entries
message ADNewsDataEntry {
    // Data key
    string key = 1;
    
    // Data value
    string value = 2;
}
