// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDChinaBondBenchmark.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDChinaBondBenchmark.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDChinaBondBenchmark_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDChinaBondBenchmark_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDChinaBondValuation_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDChinaBondValuation_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDChinaBondYieldCurve_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDChinaBondYieldCurve_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDChinaBondBenchmark_2eproto() {
  protobuf_AddDesc_MDChinaBondBenchmark_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDChinaBondBenchmark.proto");
  GOOGLE_CHECK(file != NULL);
  MDChinaBondBenchmark_descriptor_ = file->message_type(0);
  static const int MDChinaBondBenchmark_offsets_[9] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, benchmarktype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, mdchinabondvaluation_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, mdchinabondyieldcurves_),
  };
  MDChinaBondBenchmark_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDChinaBondBenchmark_descriptor_,
      MDChinaBondBenchmark::internal_default_instance(),
      MDChinaBondBenchmark_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDChinaBondBenchmark),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondBenchmark, _internal_metadata_));
  MDChinaBondValuation_descriptor_ = file->message_type(1);
  static const int MDChinaBondValuation_offsets_[23] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, calculatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, transactiontime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, mdmkt_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, maturityterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, intradayvaluationfullprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, intradayaccruedinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationnetprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationmodifiedduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationconvexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationbpv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationspreadduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationspreadconvexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationrateduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationrateconvexity_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, enddayfullprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, enddayaccruedinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, spreadyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, estimatecouprateaftexeday_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, resiprincipal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationpreyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, valuationyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, recommendation_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, instrumentshortname_),
  };
  MDChinaBondValuation_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDChinaBondValuation_descriptor_,
      MDChinaBondValuation::internal_default_instance(),
      MDChinaBondValuation_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDChinaBondValuation),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondValuation, _internal_metadata_));
  MDChinaBondYieldCurve_descriptor_ = file->message_type(2);
  static const int MDChinaBondYieldCurve_offsets_[10] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, calculatetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, transactiontime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, standslip_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, curvedesc_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, curvetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, nvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, kvalue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, curveyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, curvepreyield_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, curvename_),
  };
  MDChinaBondYieldCurve_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDChinaBondYieldCurve_descriptor_,
      MDChinaBondYieldCurve::internal_default_instance(),
      MDChinaBondYieldCurve_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDChinaBondYieldCurve),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDChinaBondYieldCurve, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDChinaBondBenchmark_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDChinaBondBenchmark_descriptor_, MDChinaBondBenchmark::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDChinaBondValuation_descriptor_, MDChinaBondValuation::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDChinaBondYieldCurve_descriptor_, MDChinaBondYieldCurve::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto() {
  MDChinaBondBenchmark_default_instance_.Shutdown();
  delete MDChinaBondBenchmark_reflection_;
  MDChinaBondValuation_default_instance_.Shutdown();
  delete MDChinaBondValuation_reflection_;
  MDChinaBondYieldCurve_default_instance_.Shutdown();
  delete MDChinaBondYieldCurve_reflection_;
}

void protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDChinaBondBenchmark_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDChinaBondValuation_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MDChinaBondYieldCurve_default_instance_.DefaultConstruct();
  MDChinaBondBenchmark_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDChinaBondValuation_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDChinaBondYieldCurve_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_once_);
void protobuf_InitDefaults_MDChinaBondBenchmark_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_once_,
                 &protobuf_InitDefaults_MDChinaBondBenchmark_2eproto_impl);
}
void protobuf_AddDesc_MDChinaBondBenchmark_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\032MDChinaBondBenchmark.proto\022\032com.htsc.m"
    "dc.insight.model\032\027ESecurityIDSource.prot"
    "o\032\023ESecurityType.proto\"\241\003\n\024MDChinaBondBe"
    "nchmark\022\026\n\016HTSCSecurityID\030\001 \001(\t\0227\n\014Secur"
    "ityType\030\002 \001(\0162!.com.htsc.mdc.model.ESecu"
    "rityType\022\?\n\020SecurityIDSource\030\003 \001(\0162%.com"
    ".htsc.mdc.model.ESecurityIDSource\022\016\n\006MDD"
    "ate\030\004 \001(\005\022\016\n\006MDTime\030\005 \001(\005\022\035\n\025DataMultipl"
    "ePowerOf10\030\006 \001(\005\022\025\n\rBenchmarkType\030\n \001(\005\022"
    "N\n\024MDChinaBondValuation\030\013 \001(\01320.com.htsc"
    ".mdc.insight.model.MDChinaBondValuation\022"
    "Q\n\026MDChinaBondYieldCurves\030\014 \001(\01321.com.ht"
    "sc.mdc.insight.model.MDChinaBondYieldCur"
    "ve\"\221\005\n\024MDChinaBondValuation\022\025\n\rCalculate"
    "Time\030\001 \001(\t\022\027\n\017TransactionTime\030\002 \001(\t\022\r\n\005M"
    "DMkt\030\013 \001(\t\022\024\n\014MaturityTerm\030\014 \001(\001\022\"\n\032Intr"
    "aDayValuationFullPrice\030\r \001(\001\022\037\n\027IntraDay"
    "AccruedInterest\030\016 \001(\001\022\031\n\021ValuationNetPri"
    "ce\030\017 \001(\001\022!\n\031ValuationModifiedDuration\030\020 "
    "\001(\001\022\032\n\022ValuationConvexity\030\021 \001(\001\022\024\n\014Valua"
    "tionBPV\030\022 \001(\001\022\037\n\027ValuationSpreadDuration"
    "\030\023 \001(\001\022 \n\030ValuationSpreadConvexity\030\024 \001(\001"
    "\022\035\n\025ValuationRateDuration\030\025 \001(\001\022\036\n\026Valua"
    "tionRateConvexity\030\026 \001(\001\022\027\n\017EndDayFullPri"
    "ce\030\027 \001(\001\022\035\n\025EndDayAccruedInterest\030\030 \001(\001\022"
    "\023\n\013SpreadYield\030\031 \001(\001\022!\n\031EstimateCoupRate"
    "AftExeDay\030\032 \001(\001\022\025\n\rResiPrincipal\030\033 \001(\001\022\031"
    "\n\021ValuationPreYield\030\034 \001(\001\022\026\n\016ValuationYi"
    "eld\030\035 \001(\001\022\026\n\016Recommendation\030\036 \001(\005\022\033\n\023Ins"
    "trumentShortName\030\037 \001(\t\"\336\001\n\025MDChinaBondYi"
    "eldCurve\022\025\n\rCalculateTime\030\001 \001(\t\022\027\n\017Trans"
    "actionTime\030\002 \001(\t\022\021\n\tStandSlip\030\013 \001(\001\022\021\n\tC"
    "urveDesc\030\014 \001(\t\022\021\n\tCurveType\030\r \001(\005\022\016\n\006NVa"
    "lue\030\016 \001(\001\022\016\n\006KValue\030\017 \001(\001\022\022\n\nCurveYield\030"
    "\021 \001(\001\022\025\n\rCurvePreYield\030\022 \001(\001\022\021\n\tCurveNam"
    "e\030\024 \001(\tB=\n\032com.htsc.mdc.insight.modelB\032M"
    "DChinaBondBenchmarkProtosH\001\240\001\001b\006proto3", 1478);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDChinaBondBenchmark.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDChinaBondBenchmark_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDChinaBondBenchmark_2eproto_once_);
void protobuf_AddDesc_MDChinaBondBenchmark_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDChinaBondBenchmark_2eproto_once_,
                 &protobuf_AddDesc_MDChinaBondBenchmark_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDChinaBondBenchmark_2eproto {
  StaticDescriptorInitializer_MDChinaBondBenchmark_2eproto() {
    protobuf_AddDesc_MDChinaBondBenchmark_2eproto();
  }
} static_descriptor_initializer_MDChinaBondBenchmark_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDChinaBondBenchmark::kHTSCSecurityIDFieldNumber;
const int MDChinaBondBenchmark::kSecurityTypeFieldNumber;
const int MDChinaBondBenchmark::kSecurityIDSourceFieldNumber;
const int MDChinaBondBenchmark::kMDDateFieldNumber;
const int MDChinaBondBenchmark::kMDTimeFieldNumber;
const int MDChinaBondBenchmark::kDataMultiplePowerOf10FieldNumber;
const int MDChinaBondBenchmark::kBenchmarkTypeFieldNumber;
const int MDChinaBondBenchmark::kMDChinaBondValuationFieldNumber;
const int MDChinaBondBenchmark::kMDChinaBondYieldCurvesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDChinaBondBenchmark::MDChinaBondBenchmark()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
}

void MDChinaBondBenchmark::InitAsDefaultInstance() {
  mdchinabondvaluation_ = const_cast< ::com::htsc::mdc::insight::model::MDChinaBondValuation*>(
      ::com::htsc::mdc::insight::model::MDChinaBondValuation::internal_default_instance());
  mdchinabondyieldcurves_ = const_cast< ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve*>(
      ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve::internal_default_instance());
}

MDChinaBondBenchmark::MDChinaBondBenchmark(const MDChinaBondBenchmark& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
}

void MDChinaBondBenchmark::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdchinabondvaluation_ = NULL;
  mdchinabondyieldcurves_ = NULL;
  ::memset(&securitytype_, 0, reinterpret_cast<char*>(&benchmarktype_) -
    reinterpret_cast<char*>(&securitytype_) + sizeof(benchmarktype_));
  _cached_size_ = 0;
}

MDChinaBondBenchmark::~MDChinaBondBenchmark() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  SharedDtor();
}

void MDChinaBondBenchmark::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDChinaBondBenchmark_default_instance_.get()) {
    delete mdchinabondvaluation_;
    delete mdchinabondyieldcurves_;
  }
}

void MDChinaBondBenchmark::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDChinaBondBenchmark::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDChinaBondBenchmark_descriptor_;
}

const MDChinaBondBenchmark& MDChinaBondBenchmark::default_instance() {
  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondBenchmark> MDChinaBondBenchmark_default_instance_;

MDChinaBondBenchmark* MDChinaBondBenchmark::New(::google::protobuf::Arena* arena) const {
  MDChinaBondBenchmark* n = new MDChinaBondBenchmark;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDChinaBondBenchmark::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDChinaBondBenchmark, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDChinaBondBenchmark*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(securitytype_, benchmarktype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && mdchinabondvaluation_ != NULL) delete mdchinabondvaluation_;
  mdchinabondvaluation_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdchinabondyieldcurves_ != NULL) delete mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MDChinaBondBenchmark::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_SecurityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
      case 2: {
        if (tag == 16) {
         parse_SecurityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_SecurityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
      case 3: {
        if (tag == 24) {
         parse_SecurityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 4;
      case 4: {
        if (tag == 32) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 5;
      case 5: {
        if (tag == 40) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 6;
      case 6: {
        if (tag == 48) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_BenchmarkType;
        break;
      }

      // optional int32 BenchmarkType = 10;
      case 10: {
        if (tag == 80) {
         parse_BenchmarkType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &benchmarktype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_MDChinaBondValuation;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
      case 11: {
        if (tag == 90) {
         parse_MDChinaBondValuation:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdchinabondvaluation()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_MDChinaBondYieldCurves;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
      case 12: {
        if (tag == 98) {
         parse_MDChinaBondYieldCurves:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdchinabondyieldcurves()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  return false;
#undef DO_
}

void MDChinaBondBenchmark::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->securitytype(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      3, this->securityidsource(), output);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->mddate(), output);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->mdtime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 6;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->datamultiplepowerof10(), output);
  }

  // optional int32 BenchmarkType = 10;
  if (this->benchmarktype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->benchmarktype(), output);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
  if (this->has_mdchinabondvaluation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->mdchinabondvaluation_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
  if (this->has_mdchinabondyieldcurves()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->mdchinabondyieldcurves_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
}

::google::protobuf::uint8* MDChinaBondBenchmark::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->securitytype(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      3, this->securityidsource(), target);
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->mddate(), target);
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->mdtime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 6;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->datamultiplepowerof10(), target);
  }

  // optional int32 BenchmarkType = 10;
  if (this->benchmarktype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->benchmarktype(), target);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
  if (this->has_mdchinabondvaluation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->mdchinabondvaluation_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
  if (this->has_mdchinabondyieldcurves()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->mdchinabondyieldcurves_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  return target;
}

size_t MDChinaBondBenchmark::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional int32 MDDate = 4;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 5;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int32 DataMultiplePowerOf10 = 6;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int32 BenchmarkType = 10;
  if (this->benchmarktype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->benchmarktype());
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
  if (this->has_mdchinabondvaluation()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdchinabondvaluation_);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
  if (this->has_mdchinabondyieldcurves()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdchinabondyieldcurves_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDChinaBondBenchmark::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDChinaBondBenchmark* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDChinaBondBenchmark>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
    UnsafeMergeFrom(*source);
  }
}

void MDChinaBondBenchmark::MergeFrom(const MDChinaBondBenchmark& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDChinaBondBenchmark::UnsafeMergeFrom(const MDChinaBondBenchmark& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.benchmarktype() != 0) {
    set_benchmarktype(from.benchmarktype());
  }
  if (from.has_mdchinabondvaluation()) {
    mutable_mdchinabondvaluation()->::com::htsc::mdc::insight::model::MDChinaBondValuation::MergeFrom(from.mdchinabondvaluation());
  }
  if (from.has_mdchinabondyieldcurves()) {
    mutable_mdchinabondyieldcurves()->::com::htsc::mdc::insight::model::MDChinaBondYieldCurve::MergeFrom(from.mdchinabondyieldcurves());
  }
}

void MDChinaBondBenchmark::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDChinaBondBenchmark::CopyFrom(const MDChinaBondBenchmark& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondBenchmark)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDChinaBondBenchmark::IsInitialized() const {

  return true;
}

void MDChinaBondBenchmark::Swap(MDChinaBondBenchmark* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDChinaBondBenchmark::InternalSwap(MDChinaBondBenchmark* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(benchmarktype_, other->benchmarktype_);
  std::swap(mdchinabondvaluation_, other->mdchinabondvaluation_);
  std::swap(mdchinabondyieldcurves_, other->mdchinabondyieldcurves_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDChinaBondBenchmark::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDChinaBondBenchmark_descriptor_;
  metadata.reflection = MDChinaBondBenchmark_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDChinaBondBenchmark

// optional string HTSCSecurityID = 1;
void MDChinaBondBenchmark::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondBenchmark::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondBenchmark::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
void MDChinaBondBenchmark::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
void MDChinaBondBenchmark::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}
::std::string* MDChinaBondBenchmark::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondBenchmark::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondBenchmark::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.HTSCSecurityID)
}

// optional .com.htsc.mdc.model.ESecurityType SecurityType = 2;
void MDChinaBondBenchmark::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDChinaBondBenchmark::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDChinaBondBenchmark::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityType)
}

// optional .com.htsc.mdc.model.ESecurityIDSource SecurityIDSource = 3;
void MDChinaBondBenchmark::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDChinaBondBenchmark::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDChinaBondBenchmark::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.SecurityIDSource)
}

// optional int32 MDDate = 4;
void MDChinaBondBenchmark::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDChinaBondBenchmark::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDDate)
  return mddate_;
}
void MDChinaBondBenchmark::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDDate)
}

// optional int32 MDTime = 5;
void MDChinaBondBenchmark::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDChinaBondBenchmark::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDTime)
  return mdtime_;
}
void MDChinaBondBenchmark::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDTime)
}

// optional int32 DataMultiplePowerOf10 = 6;
void MDChinaBondBenchmark::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDChinaBondBenchmark::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDChinaBondBenchmark::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.DataMultiplePowerOf10)
}

// optional int32 BenchmarkType = 10;
void MDChinaBondBenchmark::clear_benchmarktype() {
  benchmarktype_ = 0;
}
::google::protobuf::int32 MDChinaBondBenchmark::benchmarktype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.BenchmarkType)
  return benchmarktype_;
}
void MDChinaBondBenchmark::set_benchmarktype(::google::protobuf::int32 value) {
  
  benchmarktype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondBenchmark.BenchmarkType)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondValuation MDChinaBondValuation = 11;
bool MDChinaBondBenchmark::has_mdchinabondvaluation() const {
  return this != internal_default_instance() && mdchinabondvaluation_ != NULL;
}
void MDChinaBondBenchmark::clear_mdchinabondvaluation() {
  if (GetArenaNoVirtual() == NULL && mdchinabondvaluation_ != NULL) delete mdchinabondvaluation_;
  mdchinabondvaluation_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDChinaBondValuation& MDChinaBondBenchmark::mdchinabondvaluation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  return mdchinabondvaluation_ != NULL ? *mdchinabondvaluation_
                         : *::com::htsc::mdc::insight::model::MDChinaBondValuation::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDChinaBondValuation* MDChinaBondBenchmark::mutable_mdchinabondvaluation() {
  
  if (mdchinabondvaluation_ == NULL) {
    mdchinabondvaluation_ = new ::com::htsc::mdc::insight::model::MDChinaBondValuation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  return mdchinabondvaluation_;
}
::com::htsc::mdc::insight::model::MDChinaBondValuation* MDChinaBondBenchmark::release_mdchinabondvaluation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
  
  ::com::htsc::mdc::insight::model::MDChinaBondValuation* temp = mdchinabondvaluation_;
  mdchinabondvaluation_ = NULL;
  return temp;
}
void MDChinaBondBenchmark::set_allocated_mdchinabondvaluation(::com::htsc::mdc::insight::model::MDChinaBondValuation* mdchinabondvaluation) {
  delete mdchinabondvaluation_;
  mdchinabondvaluation_ = mdchinabondvaluation;
  if (mdchinabondvaluation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondValuation)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondYieldCurve MDChinaBondYieldCurves = 12;
bool MDChinaBondBenchmark::has_mdchinabondyieldcurves() const {
  return this != internal_default_instance() && mdchinabondyieldcurves_ != NULL;
}
void MDChinaBondBenchmark::clear_mdchinabondyieldcurves() {
  if (GetArenaNoVirtual() == NULL && mdchinabondyieldcurves_ != NULL) delete mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve& MDChinaBondBenchmark::mdchinabondyieldcurves() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  return mdchinabondyieldcurves_ != NULL ? *mdchinabondyieldcurves_
                         : *::com::htsc::mdc::insight::model::MDChinaBondYieldCurve::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* MDChinaBondBenchmark::mutable_mdchinabondyieldcurves() {
  
  if (mdchinabondyieldcurves_ == NULL) {
    mdchinabondyieldcurves_ = new ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  return mdchinabondyieldcurves_;
}
::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* MDChinaBondBenchmark::release_mdchinabondyieldcurves() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
  
  ::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* temp = mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = NULL;
  return temp;
}
void MDChinaBondBenchmark::set_allocated_mdchinabondyieldcurves(::com::htsc::mdc::insight::model::MDChinaBondYieldCurve* mdchinabondyieldcurves) {
  delete mdchinabondyieldcurves_;
  mdchinabondyieldcurves_ = mdchinabondyieldcurves;
  if (mdchinabondyieldcurves) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondBenchmark.MDChinaBondYieldCurves)
}

inline const MDChinaBondBenchmark* MDChinaBondBenchmark::internal_default_instance() {
  return &MDChinaBondBenchmark_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDChinaBondValuation::kCalculateTimeFieldNumber;
const int MDChinaBondValuation::kTransactionTimeFieldNumber;
const int MDChinaBondValuation::kMDMktFieldNumber;
const int MDChinaBondValuation::kMaturityTermFieldNumber;
const int MDChinaBondValuation::kIntraDayValuationFullPriceFieldNumber;
const int MDChinaBondValuation::kIntraDayAccruedInterestFieldNumber;
const int MDChinaBondValuation::kValuationNetPriceFieldNumber;
const int MDChinaBondValuation::kValuationModifiedDurationFieldNumber;
const int MDChinaBondValuation::kValuationConvexityFieldNumber;
const int MDChinaBondValuation::kValuationBPVFieldNumber;
const int MDChinaBondValuation::kValuationSpreadDurationFieldNumber;
const int MDChinaBondValuation::kValuationSpreadConvexityFieldNumber;
const int MDChinaBondValuation::kValuationRateDurationFieldNumber;
const int MDChinaBondValuation::kValuationRateConvexityFieldNumber;
const int MDChinaBondValuation::kEndDayFullPriceFieldNumber;
const int MDChinaBondValuation::kEndDayAccruedInterestFieldNumber;
const int MDChinaBondValuation::kSpreadYieldFieldNumber;
const int MDChinaBondValuation::kEstimateCoupRateAftExeDayFieldNumber;
const int MDChinaBondValuation::kResiPrincipalFieldNumber;
const int MDChinaBondValuation::kValuationPreYieldFieldNumber;
const int MDChinaBondValuation::kValuationYieldFieldNumber;
const int MDChinaBondValuation::kRecommendationFieldNumber;
const int MDChinaBondValuation::kInstrumentShortNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDChinaBondValuation::MDChinaBondValuation()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDChinaBondValuation)
}

void MDChinaBondValuation::InitAsDefaultInstance() {
}

MDChinaBondValuation::MDChinaBondValuation(const MDChinaBondValuation& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDChinaBondValuation)
}

void MDChinaBondValuation::SharedCtor() {
  calculatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdmkt_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentshortname_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&maturityterm_, 0, reinterpret_cast<char*>(&recommendation_) -
    reinterpret_cast<char*>(&maturityterm_) + sizeof(recommendation_));
  _cached_size_ = 0;
}

MDChinaBondValuation::~MDChinaBondValuation() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDChinaBondValuation)
  SharedDtor();
}

void MDChinaBondValuation::SharedDtor() {
  calculatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdmkt_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  instrumentshortname_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDChinaBondValuation::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDChinaBondValuation::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDChinaBondValuation_descriptor_;
}

const MDChinaBondValuation& MDChinaBondValuation::default_instance() {
  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondValuation> MDChinaBondValuation_default_instance_;

MDChinaBondValuation* MDChinaBondValuation::New(::google::protobuf::Arena* arena) const {
  MDChinaBondValuation* n = new MDChinaBondValuation;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDChinaBondValuation::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDChinaBondValuation, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDChinaBondValuation*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(maturityterm_, valuationmodifiedduration_);
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  mdmkt_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(valuationconvexity_, enddayaccruedinterest_);
  ZR_(spreadyield_, recommendation_);
  instrumentshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDChinaBondValuation::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string CalculateTime = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_calculatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->calculatetime().data(), this->calculatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TransactionTime;
        break;
      }

      // optional string TransactionTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TransactionTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transactiontime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transactiontime().data(), this->transactiontime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_MDMkt;
        break;
      }

      // optional string MDMkt = 11;
      case 11: {
        if (tag == 90) {
         parse_MDMkt:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_mdmkt()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->mdmkt().data(), this->mdmkt().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(97)) goto parse_MaturityTerm;
        break;
      }

      // optional double MaturityTerm = 12;
      case 12: {
        if (tag == 97) {
         parse_MaturityTerm:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &maturityterm_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(105)) goto parse_IntraDayValuationFullPrice;
        break;
      }

      // optional double IntraDayValuationFullPrice = 13;
      case 13: {
        if (tag == 105) {
         parse_IntraDayValuationFullPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &intradayvaluationfullprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_IntraDayAccruedInterest;
        break;
      }

      // optional double IntraDayAccruedInterest = 14;
      case 14: {
        if (tag == 113) {
         parse_IntraDayAccruedInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &intradayaccruedinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_ValuationNetPrice;
        break;
      }

      // optional double ValuationNetPrice = 15;
      case 15: {
        if (tag == 121) {
         parse_ValuationNetPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationnetprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(129)) goto parse_ValuationModifiedDuration;
        break;
      }

      // optional double ValuationModifiedDuration = 16;
      case 16: {
        if (tag == 129) {
         parse_ValuationModifiedDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationmodifiedduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_ValuationConvexity;
        break;
      }

      // optional double ValuationConvexity = 17;
      case 17: {
        if (tag == 137) {
         parse_ValuationConvexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationconvexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_ValuationBPV;
        break;
      }

      // optional double ValuationBPV = 18;
      case 18: {
        if (tag == 145) {
         parse_ValuationBPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationbpv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(153)) goto parse_ValuationSpreadDuration;
        break;
      }

      // optional double ValuationSpreadDuration = 19;
      case 19: {
        if (tag == 153) {
         parse_ValuationSpreadDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationspreadduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(161)) goto parse_ValuationSpreadConvexity;
        break;
      }

      // optional double ValuationSpreadConvexity = 20;
      case 20: {
        if (tag == 161) {
         parse_ValuationSpreadConvexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationspreadconvexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(169)) goto parse_ValuationRateDuration;
        break;
      }

      // optional double ValuationRateDuration = 21;
      case 21: {
        if (tag == 169) {
         parse_ValuationRateDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationrateduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(177)) goto parse_ValuationRateConvexity;
        break;
      }

      // optional double ValuationRateConvexity = 22;
      case 22: {
        if (tag == 177) {
         parse_ValuationRateConvexity:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationrateconvexity_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(185)) goto parse_EndDayFullPrice;
        break;
      }

      // optional double EndDayFullPrice = 23;
      case 23: {
        if (tag == 185) {
         parse_EndDayFullPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &enddayfullprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(193)) goto parse_EndDayAccruedInterest;
        break;
      }

      // optional double EndDayAccruedInterest = 24;
      case 24: {
        if (tag == 193) {
         parse_EndDayAccruedInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &enddayaccruedinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(201)) goto parse_SpreadYield;
        break;
      }

      // optional double SpreadYield = 25;
      case 25: {
        if (tag == 201) {
         parse_SpreadYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &spreadyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(209)) goto parse_EstimateCoupRateAftExeDay;
        break;
      }

      // optional double EstimateCoupRateAftExeDay = 26;
      case 26: {
        if (tag == 209) {
         parse_EstimateCoupRateAftExeDay:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &estimatecouprateaftexeday_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(217)) goto parse_ResiPrincipal;
        break;
      }

      // optional double ResiPrincipal = 27;
      case 27: {
        if (tag == 217) {
         parse_ResiPrincipal:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &resiprincipal_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(225)) goto parse_ValuationPreYield;
        break;
      }

      // optional double ValuationPreYield = 28;
      case 28: {
        if (tag == 225) {
         parse_ValuationPreYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationpreyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(233)) goto parse_ValuationYield;
        break;
      }

      // optional double ValuationYield = 29;
      case 29: {
        if (tag == 233) {
         parse_ValuationYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &valuationyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_Recommendation;
        break;
      }

      // optional int32 Recommendation = 30;
      case 30: {
        if (tag == 240) {
         parse_Recommendation:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &recommendation_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_InstrumentShortName;
        break;
      }

      // optional string InstrumentShortName = 31;
      case 31: {
        if (tag == 250) {
         parse_InstrumentShortName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_instrumentshortname()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->instrumentshortname().data(), this->instrumentshortname().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDChinaBondValuation)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDChinaBondValuation)
  return false;
#undef DO_
}

void MDChinaBondValuation::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->calculatetime(), output);
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactiontime().data(), this->transactiontime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->transactiontime(), output);
  }

  // optional string MDMkt = 11;
  if (this->mdmkt().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdmkt().data(), this->mdmkt().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->mdmkt(), output);
  }

  // optional double MaturityTerm = 12;
  if (this->maturityterm() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->maturityterm(), output);
  }

  // optional double IntraDayValuationFullPrice = 13;
  if (this->intradayvaluationfullprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->intradayvaluationfullprice(), output);
  }

  // optional double IntraDayAccruedInterest = 14;
  if (this->intradayaccruedinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->intradayaccruedinterest(), output);
  }

  // optional double ValuationNetPrice = 15;
  if (this->valuationnetprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->valuationnetprice(), output);
  }

  // optional double ValuationModifiedDuration = 16;
  if (this->valuationmodifiedduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->valuationmodifiedduration(), output);
  }

  // optional double ValuationConvexity = 17;
  if (this->valuationconvexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->valuationconvexity(), output);
  }

  // optional double ValuationBPV = 18;
  if (this->valuationbpv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->valuationbpv(), output);
  }

  // optional double ValuationSpreadDuration = 19;
  if (this->valuationspreadduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(19, this->valuationspreadduration(), output);
  }

  // optional double ValuationSpreadConvexity = 20;
  if (this->valuationspreadconvexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(20, this->valuationspreadconvexity(), output);
  }

  // optional double ValuationRateDuration = 21;
  if (this->valuationrateduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(21, this->valuationrateduration(), output);
  }

  // optional double ValuationRateConvexity = 22;
  if (this->valuationrateconvexity() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(22, this->valuationrateconvexity(), output);
  }

  // optional double EndDayFullPrice = 23;
  if (this->enddayfullprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(23, this->enddayfullprice(), output);
  }

  // optional double EndDayAccruedInterest = 24;
  if (this->enddayaccruedinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(24, this->enddayaccruedinterest(), output);
  }

  // optional double SpreadYield = 25;
  if (this->spreadyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(25, this->spreadyield(), output);
  }

  // optional double EstimateCoupRateAftExeDay = 26;
  if (this->estimatecouprateaftexeday() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(26, this->estimatecouprateaftexeday(), output);
  }

  // optional double ResiPrincipal = 27;
  if (this->resiprincipal() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(27, this->resiprincipal(), output);
  }

  // optional double ValuationPreYield = 28;
  if (this->valuationpreyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(28, this->valuationpreyield(), output);
  }

  // optional double ValuationYield = 29;
  if (this->valuationyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(29, this->valuationyield(), output);
  }

  // optional int32 Recommendation = 30;
  if (this->recommendation() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(30, this->recommendation(), output);
  }

  // optional string InstrumentShortName = 31;
  if (this->instrumentshortname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentshortname().data(), this->instrumentshortname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      31, this->instrumentshortname(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDChinaBondValuation)
}

::google::protobuf::uint8* MDChinaBondValuation::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->calculatetime(), target);
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactiontime().data(), this->transactiontime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->transactiontime(), target);
  }

  // optional string MDMkt = 11;
  if (this->mdmkt().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->mdmkt().data(), this->mdmkt().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->mdmkt(), target);
  }

  // optional double MaturityTerm = 12;
  if (this->maturityterm() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->maturityterm(), target);
  }

  // optional double IntraDayValuationFullPrice = 13;
  if (this->intradayvaluationfullprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->intradayvaluationfullprice(), target);
  }

  // optional double IntraDayAccruedInterest = 14;
  if (this->intradayaccruedinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->intradayaccruedinterest(), target);
  }

  // optional double ValuationNetPrice = 15;
  if (this->valuationnetprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->valuationnetprice(), target);
  }

  // optional double ValuationModifiedDuration = 16;
  if (this->valuationmodifiedduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->valuationmodifiedduration(), target);
  }

  // optional double ValuationConvexity = 17;
  if (this->valuationconvexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->valuationconvexity(), target);
  }

  // optional double ValuationBPV = 18;
  if (this->valuationbpv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->valuationbpv(), target);
  }

  // optional double ValuationSpreadDuration = 19;
  if (this->valuationspreadduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(19, this->valuationspreadduration(), target);
  }

  // optional double ValuationSpreadConvexity = 20;
  if (this->valuationspreadconvexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(20, this->valuationspreadconvexity(), target);
  }

  // optional double ValuationRateDuration = 21;
  if (this->valuationrateduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(21, this->valuationrateduration(), target);
  }

  // optional double ValuationRateConvexity = 22;
  if (this->valuationrateconvexity() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(22, this->valuationrateconvexity(), target);
  }

  // optional double EndDayFullPrice = 23;
  if (this->enddayfullprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(23, this->enddayfullprice(), target);
  }

  // optional double EndDayAccruedInterest = 24;
  if (this->enddayaccruedinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(24, this->enddayaccruedinterest(), target);
  }

  // optional double SpreadYield = 25;
  if (this->spreadyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(25, this->spreadyield(), target);
  }

  // optional double EstimateCoupRateAftExeDay = 26;
  if (this->estimatecouprateaftexeday() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(26, this->estimatecouprateaftexeday(), target);
  }

  // optional double ResiPrincipal = 27;
  if (this->resiprincipal() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(27, this->resiprincipal(), target);
  }

  // optional double ValuationPreYield = 28;
  if (this->valuationpreyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(28, this->valuationpreyield(), target);
  }

  // optional double ValuationYield = 29;
  if (this->valuationyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(29, this->valuationyield(), target);
  }

  // optional int32 Recommendation = 30;
  if (this->recommendation() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(30, this->recommendation(), target);
  }

  // optional string InstrumentShortName = 31;
  if (this->instrumentshortname().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->instrumentshortname().data(), this->instrumentshortname().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        31, this->instrumentshortname(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDChinaBondValuation)
  return target;
}

size_t MDChinaBondValuation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  size_t total_size = 0;

  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->calculatetime());
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transactiontime());
  }

  // optional string MDMkt = 11;
  if (this->mdmkt().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->mdmkt());
  }

  // optional double MaturityTerm = 12;
  if (this->maturityterm() != 0) {
    total_size += 1 + 8;
  }

  // optional double IntraDayValuationFullPrice = 13;
  if (this->intradayvaluationfullprice() != 0) {
    total_size += 1 + 8;
  }

  // optional double IntraDayAccruedInterest = 14;
  if (this->intradayaccruedinterest() != 0) {
    total_size += 1 + 8;
  }

  // optional double ValuationNetPrice = 15;
  if (this->valuationnetprice() != 0) {
    total_size += 1 + 8;
  }

  // optional double ValuationModifiedDuration = 16;
  if (this->valuationmodifiedduration() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationConvexity = 17;
  if (this->valuationconvexity() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationBPV = 18;
  if (this->valuationbpv() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationSpreadDuration = 19;
  if (this->valuationspreadduration() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationSpreadConvexity = 20;
  if (this->valuationspreadconvexity() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationRateDuration = 21;
  if (this->valuationrateduration() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationRateConvexity = 22;
  if (this->valuationrateconvexity() != 0) {
    total_size += 2 + 8;
  }

  // optional double EndDayFullPrice = 23;
  if (this->enddayfullprice() != 0) {
    total_size += 2 + 8;
  }

  // optional double EndDayAccruedInterest = 24;
  if (this->enddayaccruedinterest() != 0) {
    total_size += 2 + 8;
  }

  // optional double SpreadYield = 25;
  if (this->spreadyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double EstimateCoupRateAftExeDay = 26;
  if (this->estimatecouprateaftexeday() != 0) {
    total_size += 2 + 8;
  }

  // optional double ResiPrincipal = 27;
  if (this->resiprincipal() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationPreYield = 28;
  if (this->valuationpreyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double ValuationYield = 29;
  if (this->valuationyield() != 0) {
    total_size += 2 + 8;
  }

  // optional int32 Recommendation = 30;
  if (this->recommendation() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->recommendation());
  }

  // optional string InstrumentShortName = 31;
  if (this->instrumentshortname().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->instrumentshortname());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDChinaBondValuation::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDChinaBondValuation* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDChinaBondValuation>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDChinaBondValuation)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDChinaBondValuation)
    UnsafeMergeFrom(*source);
  }
}

void MDChinaBondValuation::MergeFrom(const MDChinaBondValuation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDChinaBondValuation::UnsafeMergeFrom(const MDChinaBondValuation& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.calculatetime().size() > 0) {

    calculatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.calculatetime_);
  }
  if (from.transactiontime().size() > 0) {

    transactiontime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transactiontime_);
  }
  if (from.mdmkt().size() > 0) {

    mdmkt_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.mdmkt_);
  }
  if (from.maturityterm() != 0) {
    set_maturityterm(from.maturityterm());
  }
  if (from.intradayvaluationfullprice() != 0) {
    set_intradayvaluationfullprice(from.intradayvaluationfullprice());
  }
  if (from.intradayaccruedinterest() != 0) {
    set_intradayaccruedinterest(from.intradayaccruedinterest());
  }
  if (from.valuationnetprice() != 0) {
    set_valuationnetprice(from.valuationnetprice());
  }
  if (from.valuationmodifiedduration() != 0) {
    set_valuationmodifiedduration(from.valuationmodifiedduration());
  }
  if (from.valuationconvexity() != 0) {
    set_valuationconvexity(from.valuationconvexity());
  }
  if (from.valuationbpv() != 0) {
    set_valuationbpv(from.valuationbpv());
  }
  if (from.valuationspreadduration() != 0) {
    set_valuationspreadduration(from.valuationspreadduration());
  }
  if (from.valuationspreadconvexity() != 0) {
    set_valuationspreadconvexity(from.valuationspreadconvexity());
  }
  if (from.valuationrateduration() != 0) {
    set_valuationrateduration(from.valuationrateduration());
  }
  if (from.valuationrateconvexity() != 0) {
    set_valuationrateconvexity(from.valuationrateconvexity());
  }
  if (from.enddayfullprice() != 0) {
    set_enddayfullprice(from.enddayfullprice());
  }
  if (from.enddayaccruedinterest() != 0) {
    set_enddayaccruedinterest(from.enddayaccruedinterest());
  }
  if (from.spreadyield() != 0) {
    set_spreadyield(from.spreadyield());
  }
  if (from.estimatecouprateaftexeday() != 0) {
    set_estimatecouprateaftexeday(from.estimatecouprateaftexeday());
  }
  if (from.resiprincipal() != 0) {
    set_resiprincipal(from.resiprincipal());
  }
  if (from.valuationpreyield() != 0) {
    set_valuationpreyield(from.valuationpreyield());
  }
  if (from.valuationyield() != 0) {
    set_valuationyield(from.valuationyield());
  }
  if (from.recommendation() != 0) {
    set_recommendation(from.recommendation());
  }
  if (from.instrumentshortname().size() > 0) {

    instrumentshortname_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.instrumentshortname_);
  }
}

void MDChinaBondValuation::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDChinaBondValuation::CopyFrom(const MDChinaBondValuation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondValuation)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDChinaBondValuation::IsInitialized() const {

  return true;
}

void MDChinaBondValuation::Swap(MDChinaBondValuation* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDChinaBondValuation::InternalSwap(MDChinaBondValuation* other) {
  calculatetime_.Swap(&other->calculatetime_);
  transactiontime_.Swap(&other->transactiontime_);
  mdmkt_.Swap(&other->mdmkt_);
  std::swap(maturityterm_, other->maturityterm_);
  std::swap(intradayvaluationfullprice_, other->intradayvaluationfullprice_);
  std::swap(intradayaccruedinterest_, other->intradayaccruedinterest_);
  std::swap(valuationnetprice_, other->valuationnetprice_);
  std::swap(valuationmodifiedduration_, other->valuationmodifiedduration_);
  std::swap(valuationconvexity_, other->valuationconvexity_);
  std::swap(valuationbpv_, other->valuationbpv_);
  std::swap(valuationspreadduration_, other->valuationspreadduration_);
  std::swap(valuationspreadconvexity_, other->valuationspreadconvexity_);
  std::swap(valuationrateduration_, other->valuationrateduration_);
  std::swap(valuationrateconvexity_, other->valuationrateconvexity_);
  std::swap(enddayfullprice_, other->enddayfullprice_);
  std::swap(enddayaccruedinterest_, other->enddayaccruedinterest_);
  std::swap(spreadyield_, other->spreadyield_);
  std::swap(estimatecouprateaftexeday_, other->estimatecouprateaftexeday_);
  std::swap(resiprincipal_, other->resiprincipal_);
  std::swap(valuationpreyield_, other->valuationpreyield_);
  std::swap(valuationyield_, other->valuationyield_);
  std::swap(recommendation_, other->recommendation_);
  instrumentshortname_.Swap(&other->instrumentshortname_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDChinaBondValuation::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDChinaBondValuation_descriptor_;
  metadata.reflection = MDChinaBondValuation_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDChinaBondValuation

// optional string CalculateTime = 1;
void MDChinaBondValuation::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondValuation::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
void MDChinaBondValuation::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
void MDChinaBondValuation::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}
::std::string* MDChinaBondValuation::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondValuation::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.CalculateTime)
}

// optional string TransactionTime = 2;
void MDChinaBondValuation::clear_transactiontime() {
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondValuation::transactiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  return transactiontime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_transactiontime(const ::std::string& value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
void MDChinaBondValuation::set_transactiontime(const char* value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
void MDChinaBondValuation::set_transactiontime(const char* value, size_t size) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}
::std::string* MDChinaBondValuation::mutable_transactiontime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  return transactiontime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondValuation::release_transactiontime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
  
  return transactiontime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_allocated_transactiontime(::std::string* transactiontime) {
  if (transactiontime != NULL) {
    
  } else {
    
  }
  transactiontime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactiontime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.TransactionTime)
}

// optional string MDMkt = 11;
void MDChinaBondValuation::clear_mdmkt() {
  mdmkt_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondValuation::mdmkt() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  return mdmkt_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_mdmkt(const ::std::string& value) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
void MDChinaBondValuation::set_mdmkt(const char* value) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
void MDChinaBondValuation::set_mdmkt(const char* value, size_t size) {
  
  mdmkt_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}
::std::string* MDChinaBondValuation::mutable_mdmkt() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  return mdmkt_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondValuation::release_mdmkt() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
  
  return mdmkt_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_allocated_mdmkt(::std::string* mdmkt) {
  if (mdmkt != NULL) {
    
  } else {
    
  }
  mdmkt_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), mdmkt);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.MDMkt)
}

// optional double MaturityTerm = 12;
void MDChinaBondValuation::clear_maturityterm() {
  maturityterm_ = 0;
}
double MDChinaBondValuation::maturityterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.MaturityTerm)
  return maturityterm_;
}
void MDChinaBondValuation::set_maturityterm(double value) {
  
  maturityterm_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.MaturityTerm)
}

// optional double IntraDayValuationFullPrice = 13;
void MDChinaBondValuation::clear_intradayvaluationfullprice() {
  intradayvaluationfullprice_ = 0;
}
double MDChinaBondValuation::intradayvaluationfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayValuationFullPrice)
  return intradayvaluationfullprice_;
}
void MDChinaBondValuation::set_intradayvaluationfullprice(double value) {
  
  intradayvaluationfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayValuationFullPrice)
}

// optional double IntraDayAccruedInterest = 14;
void MDChinaBondValuation::clear_intradayaccruedinterest() {
  intradayaccruedinterest_ = 0;
}
double MDChinaBondValuation::intradayaccruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayAccruedInterest)
  return intradayaccruedinterest_;
}
void MDChinaBondValuation::set_intradayaccruedinterest(double value) {
  
  intradayaccruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.IntraDayAccruedInterest)
}

// optional double ValuationNetPrice = 15;
void MDChinaBondValuation::clear_valuationnetprice() {
  valuationnetprice_ = 0;
}
double MDChinaBondValuation::valuationnetprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationNetPrice)
  return valuationnetprice_;
}
void MDChinaBondValuation::set_valuationnetprice(double value) {
  
  valuationnetprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationNetPrice)
}

// optional double ValuationModifiedDuration = 16;
void MDChinaBondValuation::clear_valuationmodifiedduration() {
  valuationmodifiedduration_ = 0;
}
double MDChinaBondValuation::valuationmodifiedduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationModifiedDuration)
  return valuationmodifiedduration_;
}
void MDChinaBondValuation::set_valuationmodifiedduration(double value) {
  
  valuationmodifiedduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationModifiedDuration)
}

// optional double ValuationConvexity = 17;
void MDChinaBondValuation::clear_valuationconvexity() {
  valuationconvexity_ = 0;
}
double MDChinaBondValuation::valuationconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationConvexity)
  return valuationconvexity_;
}
void MDChinaBondValuation::set_valuationconvexity(double value) {
  
  valuationconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationConvexity)
}

// optional double ValuationBPV = 18;
void MDChinaBondValuation::clear_valuationbpv() {
  valuationbpv_ = 0;
}
double MDChinaBondValuation::valuationbpv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationBPV)
  return valuationbpv_;
}
void MDChinaBondValuation::set_valuationbpv(double value) {
  
  valuationbpv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationBPV)
}

// optional double ValuationSpreadDuration = 19;
void MDChinaBondValuation::clear_valuationspreadduration() {
  valuationspreadduration_ = 0;
}
double MDChinaBondValuation::valuationspreadduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadDuration)
  return valuationspreadduration_;
}
void MDChinaBondValuation::set_valuationspreadduration(double value) {
  
  valuationspreadduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadDuration)
}

// optional double ValuationSpreadConvexity = 20;
void MDChinaBondValuation::clear_valuationspreadconvexity() {
  valuationspreadconvexity_ = 0;
}
double MDChinaBondValuation::valuationspreadconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadConvexity)
  return valuationspreadconvexity_;
}
void MDChinaBondValuation::set_valuationspreadconvexity(double value) {
  
  valuationspreadconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationSpreadConvexity)
}

// optional double ValuationRateDuration = 21;
void MDChinaBondValuation::clear_valuationrateduration() {
  valuationrateduration_ = 0;
}
double MDChinaBondValuation::valuationrateduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateDuration)
  return valuationrateduration_;
}
void MDChinaBondValuation::set_valuationrateduration(double value) {
  
  valuationrateduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateDuration)
}

// optional double ValuationRateConvexity = 22;
void MDChinaBondValuation::clear_valuationrateconvexity() {
  valuationrateconvexity_ = 0;
}
double MDChinaBondValuation::valuationrateconvexity() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateConvexity)
  return valuationrateconvexity_;
}
void MDChinaBondValuation::set_valuationrateconvexity(double value) {
  
  valuationrateconvexity_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationRateConvexity)
}

// optional double EndDayFullPrice = 23;
void MDChinaBondValuation::clear_enddayfullprice() {
  enddayfullprice_ = 0;
}
double MDChinaBondValuation::enddayfullprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayFullPrice)
  return enddayfullprice_;
}
void MDChinaBondValuation::set_enddayfullprice(double value) {
  
  enddayfullprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayFullPrice)
}

// optional double EndDayAccruedInterest = 24;
void MDChinaBondValuation::clear_enddayaccruedinterest() {
  enddayaccruedinterest_ = 0;
}
double MDChinaBondValuation::enddayaccruedinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayAccruedInterest)
  return enddayaccruedinterest_;
}
void MDChinaBondValuation::set_enddayaccruedinterest(double value) {
  
  enddayaccruedinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EndDayAccruedInterest)
}

// optional double SpreadYield = 25;
void MDChinaBondValuation::clear_spreadyield() {
  spreadyield_ = 0;
}
double MDChinaBondValuation::spreadyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.SpreadYield)
  return spreadyield_;
}
void MDChinaBondValuation::set_spreadyield(double value) {
  
  spreadyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.SpreadYield)
}

// optional double EstimateCoupRateAftExeDay = 26;
void MDChinaBondValuation::clear_estimatecouprateaftexeday() {
  estimatecouprateaftexeday_ = 0;
}
double MDChinaBondValuation::estimatecouprateaftexeday() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.EstimateCoupRateAftExeDay)
  return estimatecouprateaftexeday_;
}
void MDChinaBondValuation::set_estimatecouprateaftexeday(double value) {
  
  estimatecouprateaftexeday_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.EstimateCoupRateAftExeDay)
}

// optional double ResiPrincipal = 27;
void MDChinaBondValuation::clear_resiprincipal() {
  resiprincipal_ = 0;
}
double MDChinaBondValuation::resiprincipal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ResiPrincipal)
  return resiprincipal_;
}
void MDChinaBondValuation::set_resiprincipal(double value) {
  
  resiprincipal_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ResiPrincipal)
}

// optional double ValuationPreYield = 28;
void MDChinaBondValuation::clear_valuationpreyield() {
  valuationpreyield_ = 0;
}
double MDChinaBondValuation::valuationpreyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationPreYield)
  return valuationpreyield_;
}
void MDChinaBondValuation::set_valuationpreyield(double value) {
  
  valuationpreyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationPreYield)
}

// optional double ValuationYield = 29;
void MDChinaBondValuation::clear_valuationyield() {
  valuationyield_ = 0;
}
double MDChinaBondValuation::valuationyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationYield)
  return valuationyield_;
}
void MDChinaBondValuation::set_valuationyield(double value) {
  
  valuationyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.ValuationYield)
}

// optional int32 Recommendation = 30;
void MDChinaBondValuation::clear_recommendation() {
  recommendation_ = 0;
}
::google::protobuf::int32 MDChinaBondValuation::recommendation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.Recommendation)
  return recommendation_;
}
void MDChinaBondValuation::set_recommendation(::google::protobuf::int32 value) {
  
  recommendation_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.Recommendation)
}

// optional string InstrumentShortName = 31;
void MDChinaBondValuation::clear_instrumentshortname() {
  instrumentshortname_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondValuation::instrumentshortname() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  return instrumentshortname_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_instrumentshortname(const ::std::string& value) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
void MDChinaBondValuation::set_instrumentshortname(const char* value) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
void MDChinaBondValuation::set_instrumentshortname(const char* value, size_t size) {
  
  instrumentshortname_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}
::std::string* MDChinaBondValuation::mutable_instrumentshortname() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  return instrumentshortname_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondValuation::release_instrumentshortname() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
  
  return instrumentshortname_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondValuation::set_allocated_instrumentshortname(::std::string* instrumentshortname) {
  if (instrumentshortname != NULL) {
    
  } else {
    
  }
  instrumentshortname_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), instrumentshortname);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondValuation.InstrumentShortName)
}

inline const MDChinaBondValuation* MDChinaBondValuation::internal_default_instance() {
  return &MDChinaBondValuation_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDChinaBondYieldCurve::kCalculateTimeFieldNumber;
const int MDChinaBondYieldCurve::kTransactionTimeFieldNumber;
const int MDChinaBondYieldCurve::kStandSlipFieldNumber;
const int MDChinaBondYieldCurve::kCurveDescFieldNumber;
const int MDChinaBondYieldCurve::kCurveTypeFieldNumber;
const int MDChinaBondYieldCurve::kNValueFieldNumber;
const int MDChinaBondYieldCurve::kKValueFieldNumber;
const int MDChinaBondYieldCurve::kCurveYieldFieldNumber;
const int MDChinaBondYieldCurve::kCurvePreYieldFieldNumber;
const int MDChinaBondYieldCurve::kCurveNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDChinaBondYieldCurve::MDChinaBondYieldCurve()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
}

void MDChinaBondYieldCurve::InitAsDefaultInstance() {
}

MDChinaBondYieldCurve::MDChinaBondYieldCurve(const MDChinaBondYieldCurve& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
}

void MDChinaBondYieldCurve::SharedCtor() {
  calculatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvedesc_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&standslip_, 0, reinterpret_cast<char*>(&curvetype_) -
    reinterpret_cast<char*>(&standslip_) + sizeof(curvetype_));
  _cached_size_ = 0;
}

MDChinaBondYieldCurve::~MDChinaBondYieldCurve() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  SharedDtor();
}

void MDChinaBondYieldCurve::SharedDtor() {
  calculatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvedesc_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDChinaBondYieldCurve::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDChinaBondYieldCurve::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDChinaBondYieldCurve_descriptor_;
}

const MDChinaBondYieldCurve& MDChinaBondYieldCurve::default_instance() {
  protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDChinaBondYieldCurve> MDChinaBondYieldCurve_default_instance_;

MDChinaBondYieldCurve* MDChinaBondYieldCurve::New(::google::protobuf::Arena* arena) const {
  MDChinaBondYieldCurve* n = new MDChinaBondYieldCurve;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDChinaBondYieldCurve::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDChinaBondYieldCurve, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDChinaBondYieldCurve*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(standslip_, curveyield_);
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvedesc_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  curvetype_ = 0;
  curvepreyield_ = 0;
  curvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDChinaBondYieldCurve::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string CalculateTime = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_calculatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->calculatetime().data(), this->calculatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_TransactionTime;
        break;
      }

      // optional string TransactionTime = 2;
      case 2: {
        if (tag == 18) {
         parse_TransactionTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transactiontime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transactiontime().data(), this->transactiontime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(89)) goto parse_StandSlip;
        break;
      }

      // optional double StandSlip = 11;
      case 11: {
        if (tag == 89) {
         parse_StandSlip:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &standslip_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_CurveDesc;
        break;
      }

      // optional string CurveDesc = 12;
      case 12: {
        if (tag == 98) {
         parse_CurveDesc:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_curvedesc()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->curvedesc().data(), this->curvedesc().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_CurveType;
        break;
      }

      // optional int32 CurveType = 13;
      case 13: {
        if (tag == 104) {
         parse_CurveType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &curvetype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(113)) goto parse_NValue;
        break;
      }

      // optional double NValue = 14;
      case 14: {
        if (tag == 113) {
         parse_NValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &nvalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(121)) goto parse_KValue;
        break;
      }

      // optional double KValue = 15;
      case 15: {
        if (tag == 121) {
         parse_KValue:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &kvalue_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(137)) goto parse_CurveYield;
        break;
      }

      // optional double CurveYield = 17;
      case 17: {
        if (tag == 137) {
         parse_CurveYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &curveyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(145)) goto parse_CurvePreYield;
        break;
      }

      // optional double CurvePreYield = 18;
      case 18: {
        if (tag == 145) {
         parse_CurvePreYield:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &curvepreyield_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_CurveName;
        break;
      }

      // optional string CurveName = 20;
      case 20: {
        if (tag == 162) {
         parse_CurveName:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_curvename()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->curvename().data(), this->curvename().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  return false;
#undef DO_
}

void MDChinaBondYieldCurve::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->calculatetime(), output);
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactiontime().data(), this->transactiontime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->transactiontime(), output);
  }

  // optional double StandSlip = 11;
  if (this->standslip() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->standslip(), output);
  }

  // optional string CurveDesc = 12;
  if (this->curvedesc().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvedesc().data(), this->curvedesc().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      12, this->curvedesc(), output);
  }

  // optional int32 CurveType = 13;
  if (this->curvetype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(13, this->curvetype(), output);
  }

  // optional double NValue = 14;
  if (this->nvalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->nvalue(), output);
  }

  // optional double KValue = 15;
  if (this->kvalue() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->kvalue(), output);
  }

  // optional double CurveYield = 17;
  if (this->curveyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->curveyield(), output);
  }

  // optional double CurvePreYield = 18;
  if (this->curvepreyield() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->curvepreyield(), output);
  }

  // optional string CurveName = 20;
  if (this->curvename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvename().data(), this->curvename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      20, this->curvename(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
}

::google::protobuf::uint8* MDChinaBondYieldCurve::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->calculatetime().data(), this->calculatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->calculatetime(), target);
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transactiontime().data(), this->transactiontime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->transactiontime(), target);
  }

  // optional double StandSlip = 11;
  if (this->standslip() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->standslip(), target);
  }

  // optional string CurveDesc = 12;
  if (this->curvedesc().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvedesc().data(), this->curvedesc().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        12, this->curvedesc(), target);
  }

  // optional int32 CurveType = 13;
  if (this->curvetype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(13, this->curvetype(), target);
  }

  // optional double NValue = 14;
  if (this->nvalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->nvalue(), target);
  }

  // optional double KValue = 15;
  if (this->kvalue() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->kvalue(), target);
  }

  // optional double CurveYield = 17;
  if (this->curveyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->curveyield(), target);
  }

  // optional double CurvePreYield = 18;
  if (this->curvepreyield() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->curvepreyield(), target);
  }

  // optional string CurveName = 20;
  if (this->curvename().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->curvename().data(), this->curvename().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        20, this->curvename(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  return target;
}

size_t MDChinaBondYieldCurve::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  size_t total_size = 0;

  // optional string CalculateTime = 1;
  if (this->calculatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->calculatetime());
  }

  // optional string TransactionTime = 2;
  if (this->transactiontime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transactiontime());
  }

  // optional double StandSlip = 11;
  if (this->standslip() != 0) {
    total_size += 1 + 8;
  }

  // optional string CurveDesc = 12;
  if (this->curvedesc().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->curvedesc());
  }

  // optional int32 CurveType = 13;
  if (this->curvetype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->curvetype());
  }

  // optional double NValue = 14;
  if (this->nvalue() != 0) {
    total_size += 1 + 8;
  }

  // optional double KValue = 15;
  if (this->kvalue() != 0) {
    total_size += 1 + 8;
  }

  // optional double CurveYield = 17;
  if (this->curveyield() != 0) {
    total_size += 2 + 8;
  }

  // optional double CurvePreYield = 18;
  if (this->curvepreyield() != 0) {
    total_size += 2 + 8;
  }

  // optional string CurveName = 20;
  if (this->curvename().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->curvename());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDChinaBondYieldCurve::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDChinaBondYieldCurve* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDChinaBondYieldCurve>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
    UnsafeMergeFrom(*source);
  }
}

void MDChinaBondYieldCurve::MergeFrom(const MDChinaBondYieldCurve& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDChinaBondYieldCurve::UnsafeMergeFrom(const MDChinaBondYieldCurve& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.calculatetime().size() > 0) {

    calculatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.calculatetime_);
  }
  if (from.transactiontime().size() > 0) {

    transactiontime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transactiontime_);
  }
  if (from.standslip() != 0) {
    set_standslip(from.standslip());
  }
  if (from.curvedesc().size() > 0) {

    curvedesc_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.curvedesc_);
  }
  if (from.curvetype() != 0) {
    set_curvetype(from.curvetype());
  }
  if (from.nvalue() != 0) {
    set_nvalue(from.nvalue());
  }
  if (from.kvalue() != 0) {
    set_kvalue(from.kvalue());
  }
  if (from.curveyield() != 0) {
    set_curveyield(from.curveyield());
  }
  if (from.curvepreyield() != 0) {
    set_curvepreyield(from.curvepreyield());
  }
  if (from.curvename().size() > 0) {

    curvename_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.curvename_);
  }
}

void MDChinaBondYieldCurve::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDChinaBondYieldCurve::CopyFrom(const MDChinaBondYieldCurve& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDChinaBondYieldCurve)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDChinaBondYieldCurve::IsInitialized() const {

  return true;
}

void MDChinaBondYieldCurve::Swap(MDChinaBondYieldCurve* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDChinaBondYieldCurve::InternalSwap(MDChinaBondYieldCurve* other) {
  calculatetime_.Swap(&other->calculatetime_);
  transactiontime_.Swap(&other->transactiontime_);
  std::swap(standslip_, other->standslip_);
  curvedesc_.Swap(&other->curvedesc_);
  std::swap(curvetype_, other->curvetype_);
  std::swap(nvalue_, other->nvalue_);
  std::swap(kvalue_, other->kvalue_);
  std::swap(curveyield_, other->curveyield_);
  std::swap(curvepreyield_, other->curvepreyield_);
  curvename_.Swap(&other->curvename_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDChinaBondYieldCurve::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDChinaBondYieldCurve_descriptor_;
  metadata.reflection = MDChinaBondYieldCurve_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDChinaBondYieldCurve

// optional string CalculateTime = 1;
void MDChinaBondYieldCurve::clear_calculatetime() {
  calculatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondYieldCurve::calculatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  return calculatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_calculatetime(const ::std::string& value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
void MDChinaBondYieldCurve::set_calculatetime(const char* value) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
void MDChinaBondYieldCurve::set_calculatetime(const char* value, size_t size) {
  
  calculatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}
::std::string* MDChinaBondYieldCurve::mutable_calculatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  return calculatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondYieldCurve::release_calculatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
  
  return calculatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_allocated_calculatetime(::std::string* calculatetime) {
  if (calculatetime != NULL) {
    
  } else {
    
  }
  calculatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), calculatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CalculateTime)
}

// optional string TransactionTime = 2;
void MDChinaBondYieldCurve::clear_transactiontime() {
  transactiontime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondYieldCurve::transactiontime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  return transactiontime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_transactiontime(const ::std::string& value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
void MDChinaBondYieldCurve::set_transactiontime(const char* value) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
void MDChinaBondYieldCurve::set_transactiontime(const char* value, size_t size) {
  
  transactiontime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}
::std::string* MDChinaBondYieldCurve::mutable_transactiontime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  return transactiontime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondYieldCurve::release_transactiontime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
  
  return transactiontime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_allocated_transactiontime(::std::string* transactiontime) {
  if (transactiontime != NULL) {
    
  } else {
    
  }
  transactiontime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transactiontime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.TransactionTime)
}

// optional double StandSlip = 11;
void MDChinaBondYieldCurve::clear_standslip() {
  standslip_ = 0;
}
double MDChinaBondYieldCurve::standslip() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.StandSlip)
  return standslip_;
}
void MDChinaBondYieldCurve::set_standslip(double value) {
  
  standslip_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.StandSlip)
}

// optional string CurveDesc = 12;
void MDChinaBondYieldCurve::clear_curvedesc() {
  curvedesc_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondYieldCurve::curvedesc() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  return curvedesc_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_curvedesc(const ::std::string& value) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
void MDChinaBondYieldCurve::set_curvedesc(const char* value) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
void MDChinaBondYieldCurve::set_curvedesc(const char* value, size_t size) {
  
  curvedesc_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}
::std::string* MDChinaBondYieldCurve::mutable_curvedesc() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  return curvedesc_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondYieldCurve::release_curvedesc() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
  
  return curvedesc_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_allocated_curvedesc(::std::string* curvedesc) {
  if (curvedesc != NULL) {
    
  } else {
    
  }
  curvedesc_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvedesc);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveDesc)
}

// optional int32 CurveType = 13;
void MDChinaBondYieldCurve::clear_curvetype() {
  curvetype_ = 0;
}
::google::protobuf::int32 MDChinaBondYieldCurve::curvetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveType)
  return curvetype_;
}
void MDChinaBondYieldCurve::set_curvetype(::google::protobuf::int32 value) {
  
  curvetype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveType)
}

// optional double NValue = 14;
void MDChinaBondYieldCurve::clear_nvalue() {
  nvalue_ = 0;
}
double MDChinaBondYieldCurve::nvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.NValue)
  return nvalue_;
}
void MDChinaBondYieldCurve::set_nvalue(double value) {
  
  nvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.NValue)
}

// optional double KValue = 15;
void MDChinaBondYieldCurve::clear_kvalue() {
  kvalue_ = 0;
}
double MDChinaBondYieldCurve::kvalue() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.KValue)
  return kvalue_;
}
void MDChinaBondYieldCurve::set_kvalue(double value) {
  
  kvalue_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.KValue)
}

// optional double CurveYield = 17;
void MDChinaBondYieldCurve::clear_curveyield() {
  curveyield_ = 0;
}
double MDChinaBondYieldCurve::curveyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveYield)
  return curveyield_;
}
void MDChinaBondYieldCurve::set_curveyield(double value) {
  
  curveyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveYield)
}

// optional double CurvePreYield = 18;
void MDChinaBondYieldCurve::clear_curvepreyield() {
  curvepreyield_ = 0;
}
double MDChinaBondYieldCurve::curvepreyield() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurvePreYield)
  return curvepreyield_;
}
void MDChinaBondYieldCurve::set_curvepreyield(double value) {
  
  curvepreyield_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurvePreYield)
}

// optional string CurveName = 20;
void MDChinaBondYieldCurve::clear_curvename() {
  curvename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDChinaBondYieldCurve::curvename() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  return curvename_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_curvename(const ::std::string& value) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
void MDChinaBondYieldCurve::set_curvename(const char* value) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
void MDChinaBondYieldCurve::set_curvename(const char* value, size_t size) {
  
  curvename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}
::std::string* MDChinaBondYieldCurve::mutable_curvename() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  return curvename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDChinaBondYieldCurve::release_curvename() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
  
  return curvename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDChinaBondYieldCurve::set_allocated_curvename(::std::string* curvename) {
  if (curvename != NULL) {
    
  } else {
    
  }
  curvename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), curvename);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDChinaBondYieldCurve.CurveName)
}

inline const MDChinaBondYieldCurve* MDChinaBondYieldCurve::internal_default_instance() {
  return &MDChinaBondYieldCurve_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
