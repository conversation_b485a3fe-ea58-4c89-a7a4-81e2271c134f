// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MarketData.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MarketData.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MarketData_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MarketData_reflection_ = NULL;
const ::google::protobuf::Descriptor* MarketDataStream_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MarketDataStream_reflection_ = NULL;
const ::google::protobuf::Descriptor* MarketDataList_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MarketDataList_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MarketData_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MarketData_2eproto() {
  protobuf_AddDesc_MarketData_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MarketData.proto");
  GOOGLE_CHECK(file != NULL);
  MarketData_descriptor_ = file->message_type(0);
  static const int MarketData_offsets_[66] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, marketdatatype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, messagechannelnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdstock_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdindex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdbond_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdfund_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdoption_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdfuture_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdtransaction_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdorder_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdkline_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdtwap_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdvwap_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdconstant_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdsimpletick_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdupsdownsanalysis_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdindicatorsranking_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, dynamicpacket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdvolumebyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdfundflowanalysis_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdspot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, orderbooksnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, orderbooksnapshotwithtick_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdetfbasicinfo_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdfiquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdchipdistribution_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdwarrant_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdsecuritylending_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdnews_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdstaringresult_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdderivedanalysis_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdqbquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdqbtransaction_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdusaorder_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdusatransaction_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdslorder_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdsltransaction_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdhkgreymarket_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdslindicativequote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdslstatistics_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdusaquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdslestimation_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcnexdeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcnexquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mddelaysnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdhighaccuracyfuture_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsfxsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsfxquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, spfuture_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsbenchmark_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsbonddeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsbondsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetscurrencydeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetscurrencysnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsodmsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsqdmquote_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsratedeal_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsratesnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdcfetsfxcnymiddleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdiopvsnapshot_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdchinabondbenchmark_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, mdicetrace_),
  };
  MarketData_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MarketData_descriptor_,
      MarketData::internal_default_instance(),
      MarketData_offsets_,
      -1,
      -1,
      -1,
      sizeof(MarketData),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketData, _internal_metadata_));
  MarketDataStream_descriptor_ = file->message_type(1);
  static const int MarketDataStream_offsets_[7] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, iscompressed_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, originallength_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, compresseddata_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, marketdatalist_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, totalnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, serial_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, isfinished_),
  };
  MarketDataStream_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MarketDataStream_descriptor_,
      MarketDataStream::internal_default_instance(),
      MarketDataStream_offsets_,
      -1,
      -1,
      -1,
      sizeof(MarketDataStream),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataStream, _internal_metadata_));
  MarketDataList_descriptor_ = file->message_type(2);
  static const int MarketDataList_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataList, marketdatas_),
  };
  MarketDataList_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MarketDataList_descriptor_,
      MarketDataList::internal_default_instance(),
      MarketDataList_offsets_,
      -1,
      -1,
      -1,
      sizeof(MarketDataList),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MarketDataList, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MarketData_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MarketData_descriptor_, MarketData::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MarketDataStream_descriptor_, MarketDataStream::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MarketDataList_descriptor_, MarketDataList::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MarketData_2eproto() {
  MarketData_default_instance_.Shutdown();
  delete MarketData_reflection_;
  MarketDataStream_default_instance_.Shutdown();
  delete MarketDataStream_reflection_;
  MarketDataList_default_instance_.Shutdown();
  delete MarketDataList_reflection_;
}

void protobuf_InitDefaults_MarketData_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_EMarketDataType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDStock_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDBond_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDFund_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDOption_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDIndex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADKLine_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADTwap_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADVwap_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDBasicInfo_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSimpleTick_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADUpsDownsAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADIndicatorsRanking_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_DynamicPacket_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADFundFlowAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDForex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSpot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDRate_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADOrderbookSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADOrderbookSnapshotWithTick_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDETFBasicInfo_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDFIQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADChipDistribution_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDWarrant_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSecurityLending_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADNews_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADStaringResult_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_ADDerivedAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDQBQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDQBTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDUSATransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDUSAOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSLTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSLOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDHKGreyMarket_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSLIndicativeQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSLStatistics_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDUSAQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDSLEstimation_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCnexDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCnexQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDDelaySnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDHighAccuracyFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsForex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsFxSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsFxQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_SPFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsBenchmark_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsBondDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsBondSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsCurrencyDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsCurrencySnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsODMSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsQDMQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsRateDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsRateSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDCfetsFxCnyMiddlePrice_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDIopvSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDChinaBondBenchmark_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_MDIceTrace_2eproto();
  MarketData_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  MarketDataStream_default_instance_.DefaultConstruct();
  MarketDataList_default_instance_.DefaultConstruct();
  MarketData_default_instance_.get_mutable()->InitAsDefaultInstance();
  MarketDataStream_default_instance_.get_mutable()->InitAsDefaultInstance();
  MarketDataList_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MarketData_2eproto_once_);
void protobuf_InitDefaults_MarketData_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MarketData_2eproto_once_,
                 &protobuf_InitDefaults_MarketData_2eproto_impl);
}
void protobuf_AddDesc_MarketData_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MarketData_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\020MarketData.proto\022\032com.htsc.mdc.insight"
    ".model\032\025EMarketDataType.proto\032\rMDStock.p"
    "roto\032\014MDBond.proto\032\014MDFund.proto\032\016MDOpti"
    "on.proto\032\rMDIndex.proto\032\016MDFuture.proto\032"
    "\023MDTransaction.proto\032\rMDOrder.proto\032\rADK"
    "Line.proto\032\014ADTwap.proto\032\014ADVwap.proto\032\021"
    "MDBasicInfo.proto\032\022MDSimpleTick.proto\032\030A"
    "DUpsDownsAnalysis.proto\032\031ADIndicatorsRan"
    "king.proto\032\023DynamicPacket.proto\032\025ADVolum"
    "eByPrice.proto\032\030ADFundFlowAnalysis.proto"
    "\032\rMDForex.proto\032\014MDSpot.proto\032\014MDRate.pr"
    "oto\032\031ADOrderbookSnapshot.proto\032!ADOrderb"
    "ookSnapshotWithTick.proto\032\rMDQuote.proto"
    "\032\024MDETFBasicInfo.proto\032\017MDFIQuote.proto\032"
    "\030ADChipDistribution.proto\032\017MDWarrant.pro"
    "to\032\027MDSecurityLending.proto\032\014ADNews.prot"
    "o\032\025ADStaringResult.proto\032\027ADDerivedAnaly"
    "sis.proto\032\017MDQBQuote.proto\032\025MDQBTransact"
    "ion.proto\032\026MDUSATransaction.proto\032\020MDUSA"
    "Order.proto\032\025MDSLTransaction.proto\032\017MDSL"
    "Order.proto\032\024MDHKGreyMarket.proto\032\031MDSLI"
    "ndicativeQuote.proto\032\024MDSLStatistics.pro"
    "to\032\020MDUSAQuote.proto\032\024MDSLEstimation.pro"
    "to\032\020MDCnexDeal.proto\032\021MDCnexQuote.proto\032"
    "\025MDDelaySnapshot.proto\032\032MDHighAccuracyFu"
    "ture.proto\032\022MDCfetsForex.proto\032\027MDCfetsF"
    "xSnapshot.proto\032\024MDCfetsFxQuote.proto\032\016S"
    "PFuture.proto\032\026MDCfetsBenchmark.proto\032\025M"
    "DCfetsBondDeal.proto\032\031MDCfetsBondSnapsho"
    "t.proto\032\031MDCfetsCurrencyDeal.proto\032\035MDCf"
    "etsCurrencySnapshot.proto\032\030MDCfetsODMSna"
    "pshot.proto\032\025MDCfetsQDMQuote.proto\032\025MDCf"
    "etsRateDeal.proto\032\031MDCfetsRateSnapshot.p"
    "roto\032\035MDCfetsFxCnyMiddlePrice.proto\032\024MDI"
    "opvSnapshot.proto\032\032MDChinaBondBenchmark."
    "proto\032\020MDIceTrace.proto\"\376!\n\nMarketData\022C"
    "\n\016marketDataType\030\001 \001(\0162+.com.htsc.mdc.in"
    "sight.model.EMarketDataType\022\034\n\024MessageCh"
    "annelNumber\030\002 \001(\003\0224\n\007mdStock\030\n \001(\0132#.com"
    ".htsc.mdc.insight.model.MDStock\0224\n\007mdInd"
    "ex\030\013 \001(\0132#.com.htsc.mdc.insight.model.MD"
    "Index\0222\n\006mdBond\030\014 \001(\0132\".com.htsc.mdc.ins"
    "ight.model.MDBond\0222\n\006mdFund\030\r \001(\0132\".com."
    "htsc.mdc.insight.model.MDFund\0226\n\010mdOptio"
    "n\030\016 \001(\0132$.com.htsc.mdc.insight.model.MDO"
    "ption\0226\n\010mdFuture\030\017 \001(\0132$.com.htsc.mdc.i"
    "nsight.model.MDFuture\022@\n\rmdTransaction\030\020"
    " \001(\0132).com.htsc.mdc.insight.model.MDTran"
    "saction\0224\n\007mdOrder\030\021 \001(\0132#.com.htsc.mdc."
    "insight.model.MDOrder\0224\n\007mdKLine\030\022 \001(\0132#"
    ".com.htsc.mdc.insight.model.ADKLine\0222\n\006m"
    "dTwap\030\023 \001(\0132\".com.htsc.mdc.insight.model"
    ".ADTwap\0222\n\006mdVwap\030\024 \001(\0132\".com.htsc.mdc.i"
    "nsight.model.ADVwap\022;\n\nmdConstant\030\025 \001(\0132"
    "\'.com.htsc.mdc.insight.model.MDBasicInfo"
    "\022>\n\014mdSimpleTick\030\026 \001(\0132(.com.htsc.mdc.in"
    "sight.model.MDSimpleTick\022J\n\022mdUpsDownsAn"
    "alysis\030\027 \001(\0132..com.htsc.mdc.insight.mode"
    "l.ADUpsDownsAnalysis\022L\n\023mdIndicatorsRank"
    "ing\030\030 \001(\0132/.com.htsc.mdc.insight.model.A"
    "DIndicatorsRanking\022@\n\rdynamicPacket\030\031 \001("
    "\0132).com.htsc.mdc.insight.model.DynamicPa"
    "cket\022D\n\017mdVolumeByPrice\030\032 \001(\0132+.com.htsc"
    ".mdc.insight.model.ADVolumeByPrice\022J\n\022md"
    "FundFlowAnalysis\030\033 \001(\0132..com.htsc.mdc.in"
    "sight.model.ADFundFlowAnalysis\0224\n\007mdFore"
    "x\030\034 \001(\0132#.com.htsc.mdc.insight.model.MDF"
    "orex\0222\n\006mdSpot\030\035 \001(\0132\".com.htsc.mdc.insi"
    "ght.model.MDSpot\0222\n\006mdRate\030\036 \001(\0132\".com.h"
    "tsc.mdc.insight.model.MDRate\022J\n\021orderboo"
    "kSnapshot\030\037 \001(\0132/.com.htsc.mdc.insight.m"
    "odel.ADOrderbookSnapshot\022Z\n\031orderbookSna"
    "pshotWithTick\030  \001(\01327.com.htsc.mdc.insig"
    "ht.model.ADOrderbookSnapshotWithTick\0224\n\007"
    "mdQuote\030! \001(\0132#.com.htsc.mdc.insight.mod"
    "el.MDQuote\022B\n\016mdETFBasicInfo\030\" \001(\0132*.com"
    ".htsc.mdc.insight.model.MDETFBasicInfo\0228"
    "\n\tmdFIQuote\030# \001(\0132%.com.htsc.mdc.insight"
    ".model.MDFIQuote\022J\n\022mdChipDistribution\030$"
    " \001(\0132..com.htsc.mdc.insight.model.ADChip"
    "Distribution\0228\n\tmdWarrant\030% \001(\0132%.com.ht"
    "sc.mdc.insight.model.MDWarrant\022H\n\021mdSecu"
    "rityLending\030& \001(\0132-.com.htsc.mdc.insight"
    ".model.MDSecurityLending\0222\n\006mdNews\030\' \001(\013"
    "2\".com.htsc.mdc.insight.model.ADNews\022D\n\017"
    "mdStaringResult\030( \001(\0132+.com.htsc.mdc.ins"
    "ight.model.ADStaringResult\022H\n\021mdDerivedA"
    "nalysis\030) \001(\0132-.com.htsc.mdc.insight.mod"
    "el.ADDerivedAnalysis\0228\n\tmdQBQuote\030* \001(\0132"
    "%.com.htsc.mdc.insight.model.MDQBQuote\022D"
    "\n\017mdQBTransaction\030+ \001(\0132+.com.htsc.mdc.i"
    "nsight.model.MDQBTransaction\022:\n\nmdUSAOrd"
    "er\030, \001(\0132&.com.htsc.mdc.insight.model.MD"
    "USAOrder\022F\n\020mdUSATransaction\030- \001(\0132,.com"
    ".htsc.mdc.insight.model.MDUSATransaction"
    "\0228\n\tmdSLOrder\030. \001(\0132%.com.htsc.mdc.insig"
    "ht.model.MDSLOrder\022D\n\017mdSLTransaction\030/ "
    "\001(\0132+.com.htsc.mdc.insight.model.MDSLTra"
    "nsaction\022B\n\016mdHKGreyMarket\0300 \001(\0132*.com.h"
    "tsc.mdc.insight.model.MDHKGreyMarket\022L\n\023"
    "mdSLIndicativeQuote\0301 \001(\0132/.com.htsc.mdc"
    ".insight.model.MDSLIndicativeQuote\022B\n\016md"
    "SLStatistics\0302 \001(\0132*.com.htsc.mdc.insigh"
    "t.model.MDSLStatistics\022:\n\nmdUSAQuote\0303 \001"
    "(\0132&.com.htsc.mdc.insight.model.MDUSAQuo"
    "te\022B\n\016mdSLEstimation\0304 \001(\0132*.com.htsc.md"
    "c.insight.model.MDSLEstimation\022:\n\nmdCnex"
    "Deal\0305 \001(\0132&.com.htsc.mdc.insight.model."
    "MDCnexDeal\022<\n\013mdCnexQuote\0306 \001(\0132\'.com.ht"
    "sc.mdc.insight.model.MDCnexQuote\022D\n\017mdDe"
    "laySnapshot\0307 \001(\0132+.com.htsc.mdc.insight"
    ".model.MDDelaySnapshot\022N\n\024mdHighAccuracy"
    "Future\0308 \001(\01320.com.htsc.mdc.insight.mode"
    "l.MDHighAccuracyFuture\022>\n\014mdCfetsForex\0309"
    " \001(\0132(.com.htsc.mdc.insight.model.MDCfet"
    "sForex\022H\n\021mdCfetsFxSnapshot\030: \001(\0132-.com."
    "htsc.mdc.insight.model.MDCfetsFxSnapshot"
    "\022B\n\016mdCfetsFxQuote\030; \001(\0132*.com.htsc.mdc."
    "insight.model.MDCfetsFxQuote\0226\n\010spFuture"
    "\030< \001(\0132$.com.htsc.mdc.insight.model.SPFu"
    "ture\022F\n\020mdCfetsBenchmark\030= \001(\0132,.com.hts"
    "c.mdc.insight.model.MDCfetsBenchmark\022D\n\017"
    "mdCfetsBondDeal\030> \001(\0132+.com.htsc.mdc.ins"
    "ight.model.MDCfetsBondDeal\022L\n\023mdCfetsBon"
    "dSnapshot\030\? \001(\0132/.com.htsc.mdc.insight.m"
    "odel.MDCfetsBondSnapshot\022L\n\023mdCfetsCurre"
    "ncyDeal\030@ \001(\0132/.com.htsc.mdc.insight.mod"
    "el.MDCfetsCurrencyDeal\022T\n\027mdCfetsCurrenc"
    "ySnapshot\030A \001(\01323.com.htsc.mdc.insight.m"
    "odel.MDCfetsCurrencySnapshot\022J\n\022mdCfetsO"
    "DMSnapshot\030B \001(\0132..com.htsc.mdc.insight."
    "model.MDCfetsODMSnapshot\022D\n\017mdCfetsQDMQu"
    "ote\030C \001(\0132+.com.htsc.mdc.insight.model.M"
    "DCfetsQDMQuote\022D\n\017mdCfetsRateDeal\030D \001(\0132"
    "+.com.htsc.mdc.insight.model.MDCfetsRate"
    "Deal\022L\n\023mdCfetsRateSnapshot\030E \001(\0132/.com."
    "htsc.mdc.insight.model.MDCfetsRateSnapsh"
    "ot\022T\n\027mdCfetsFxCnyMiddlePrice\030F \001(\01323.co"
    "m.htsc.mdc.insight.model.MDCfetsFxCnyMid"
    "dlePrice\022B\n\016mdIopvSnapshot\030G \001(\0132*.com.h"
    "tsc.mdc.insight.model.MDIopvSnapshot\022N\n\024"
    "mdChinaBondBenchmark\030H \001(\01320.com.htsc.md"
    "c.insight.model.MDChinaBondBenchmark\022:\n\n"
    "mdIceTrace\030I \001(\0132&.com.htsc.mdc.insight."
    "model.MDIceTrace\"\325\001\n\020MarketDataStream\022\024\n"
    "\014isCompressed\030\001 \001(\010\022\026\n\016originalLength\030\002 "
    "\001(\005\022\026\n\016compressedData\030\003 \001(\014\022B\n\016marketDat"
    "aList\030\004 \001(\0132*.com.htsc.mdc.insight.model"
    ".MarketDataList\022\023\n\013totalNumber\030\005 \001(\005\022\016\n\006"
    "serial\030\006 \001(\005\022\022\n\nisFinished\030\007 \001(\010\"M\n\016Mark"
    "etDataList\022;\n\013marketDatas\030\001 \003(\0132&.com.ht"
    "sc.mdc.insight.model.MarketDataB2\n\032com.h"
    "tsc.mdc.insight.modelB\017MarketDataProtoH\001"
    "\240\001\001b\006proto3", 6131);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MarketData.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_EMarketDataType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDStock_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDBond_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDFund_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDOption_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDIndex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADKLine_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADTwap_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADVwap_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDBasicInfo_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSimpleTick_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADUpsDownsAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADIndicatorsRanking_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_DynamicPacket_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADVolumeByPrice_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADFundFlowAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDForex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSpot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDRate_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADOrderbookSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADOrderbookSnapshotWithTick_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDETFBasicInfo_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDFIQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADChipDistribution_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDWarrant_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSecurityLending_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADNews_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADStaringResult_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_ADDerivedAnalysis_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDQBQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDQBTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDUSATransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDUSAOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSLTransaction_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSLOrder_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDHKGreyMarket_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSLIndicativeQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSLStatistics_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDUSAQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDSLEstimation_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCnexDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCnexQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDDelaySnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDHighAccuracyFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsForex_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsFxSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsFxQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_SPFuture_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsBenchmark_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsBondDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsBondSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsCurrencyDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsCurrencySnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsODMSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsQDMQuote_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsRateDeal_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsRateSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDCfetsFxCnyMiddlePrice_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDIopvSnapshot_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDChinaBondBenchmark_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_MDIceTrace_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MarketData_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MarketData_2eproto_once_);
void protobuf_AddDesc_MarketData_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MarketData_2eproto_once_,
                 &protobuf_AddDesc_MarketData_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MarketData_2eproto {
  StaticDescriptorInitializer_MarketData_2eproto() {
    protobuf_AddDesc_MarketData_2eproto();
  }
} static_descriptor_initializer_MarketData_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MarketData::kMarketDataTypeFieldNumber;
const int MarketData::kMessageChannelNumberFieldNumber;
const int MarketData::kMdStockFieldNumber;
const int MarketData::kMdIndexFieldNumber;
const int MarketData::kMdBondFieldNumber;
const int MarketData::kMdFundFieldNumber;
const int MarketData::kMdOptionFieldNumber;
const int MarketData::kMdFutureFieldNumber;
const int MarketData::kMdTransactionFieldNumber;
const int MarketData::kMdOrderFieldNumber;
const int MarketData::kMdKLineFieldNumber;
const int MarketData::kMdTwapFieldNumber;
const int MarketData::kMdVwapFieldNumber;
const int MarketData::kMdConstantFieldNumber;
const int MarketData::kMdSimpleTickFieldNumber;
const int MarketData::kMdUpsDownsAnalysisFieldNumber;
const int MarketData::kMdIndicatorsRankingFieldNumber;
const int MarketData::kDynamicPacketFieldNumber;
const int MarketData::kMdVolumeByPriceFieldNumber;
const int MarketData::kMdFundFlowAnalysisFieldNumber;
const int MarketData::kMdForexFieldNumber;
const int MarketData::kMdSpotFieldNumber;
const int MarketData::kMdRateFieldNumber;
const int MarketData::kOrderbookSnapshotFieldNumber;
const int MarketData::kOrderbookSnapshotWithTickFieldNumber;
const int MarketData::kMdQuoteFieldNumber;
const int MarketData::kMdETFBasicInfoFieldNumber;
const int MarketData::kMdFIQuoteFieldNumber;
const int MarketData::kMdChipDistributionFieldNumber;
const int MarketData::kMdWarrantFieldNumber;
const int MarketData::kMdSecurityLendingFieldNumber;
const int MarketData::kMdNewsFieldNumber;
const int MarketData::kMdStaringResultFieldNumber;
const int MarketData::kMdDerivedAnalysisFieldNumber;
const int MarketData::kMdQBQuoteFieldNumber;
const int MarketData::kMdQBTransactionFieldNumber;
const int MarketData::kMdUSAOrderFieldNumber;
const int MarketData::kMdUSATransactionFieldNumber;
const int MarketData::kMdSLOrderFieldNumber;
const int MarketData::kMdSLTransactionFieldNumber;
const int MarketData::kMdHKGreyMarketFieldNumber;
const int MarketData::kMdSLIndicativeQuoteFieldNumber;
const int MarketData::kMdSLStatisticsFieldNumber;
const int MarketData::kMdUSAQuoteFieldNumber;
const int MarketData::kMdSLEstimationFieldNumber;
const int MarketData::kMdCnexDealFieldNumber;
const int MarketData::kMdCnexQuoteFieldNumber;
const int MarketData::kMdDelaySnapshotFieldNumber;
const int MarketData::kMdHighAccuracyFutureFieldNumber;
const int MarketData::kMdCfetsForexFieldNumber;
const int MarketData::kMdCfetsFxSnapshotFieldNumber;
const int MarketData::kMdCfetsFxQuoteFieldNumber;
const int MarketData::kSpFutureFieldNumber;
const int MarketData::kMdCfetsBenchmarkFieldNumber;
const int MarketData::kMdCfetsBondDealFieldNumber;
const int MarketData::kMdCfetsBondSnapshotFieldNumber;
const int MarketData::kMdCfetsCurrencyDealFieldNumber;
const int MarketData::kMdCfetsCurrencySnapshotFieldNumber;
const int MarketData::kMdCfetsODMSnapshotFieldNumber;
const int MarketData::kMdCfetsQDMQuoteFieldNumber;
const int MarketData::kMdCfetsRateDealFieldNumber;
const int MarketData::kMdCfetsRateSnapshotFieldNumber;
const int MarketData::kMdCfetsFxCnyMiddlePriceFieldNumber;
const int MarketData::kMdIopvSnapshotFieldNumber;
const int MarketData::kMdChinaBondBenchmarkFieldNumber;
const int MarketData::kMdIceTraceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarketData::MarketData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MarketData_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MarketData)
}

void MarketData::InitAsDefaultInstance() {
  mdstock_ = const_cast< ::com::htsc::mdc::insight::model::MDStock*>(
      ::com::htsc::mdc::insight::model::MDStock::internal_default_instance());
  mdindex_ = const_cast< ::com::htsc::mdc::insight::model::MDIndex*>(
      ::com::htsc::mdc::insight::model::MDIndex::internal_default_instance());
  mdbond_ = const_cast< ::com::htsc::mdc::insight::model::MDBond*>(
      ::com::htsc::mdc::insight::model::MDBond::internal_default_instance());
  mdfund_ = const_cast< ::com::htsc::mdc::insight::model::MDFund*>(
      ::com::htsc::mdc::insight::model::MDFund::internal_default_instance());
  mdoption_ = const_cast< ::com::htsc::mdc::insight::model::MDOption*>(
      ::com::htsc::mdc::insight::model::MDOption::internal_default_instance());
  mdfuture_ = const_cast< ::com::htsc::mdc::insight::model::MDFuture*>(
      ::com::htsc::mdc::insight::model::MDFuture::internal_default_instance());
  mdtransaction_ = const_cast< ::com::htsc::mdc::insight::model::MDTransaction*>(
      ::com::htsc::mdc::insight::model::MDTransaction::internal_default_instance());
  mdorder_ = const_cast< ::com::htsc::mdc::insight::model::MDOrder*>(
      ::com::htsc::mdc::insight::model::MDOrder::internal_default_instance());
  mdkline_ = const_cast< ::com::htsc::mdc::insight::model::ADKLine*>(
      ::com::htsc::mdc::insight::model::ADKLine::internal_default_instance());
  mdtwap_ = const_cast< ::com::htsc::mdc::insight::model::ADTwap*>(
      ::com::htsc::mdc::insight::model::ADTwap::internal_default_instance());
  mdvwap_ = const_cast< ::com::htsc::mdc::insight::model::ADVwap*>(
      ::com::htsc::mdc::insight::model::ADVwap::internal_default_instance());
  mdconstant_ = const_cast< ::com::htsc::mdc::insight::model::MDBasicInfo*>(
      ::com::htsc::mdc::insight::model::MDBasicInfo::internal_default_instance());
  mdsimpletick_ = const_cast< ::com::htsc::mdc::insight::model::MDSimpleTick*>(
      ::com::htsc::mdc::insight::model::MDSimpleTick::internal_default_instance());
  mdupsdownsanalysis_ = const_cast< ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis*>(
      ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis::internal_default_instance());
  mdindicatorsranking_ = const_cast< ::com::htsc::mdc::insight::model::ADIndicatorsRanking*>(
      ::com::htsc::mdc::insight::model::ADIndicatorsRanking::internal_default_instance());
  dynamicpacket_ = const_cast< ::com::htsc::mdc::insight::model::DynamicPacket*>(
      ::com::htsc::mdc::insight::model::DynamicPacket::internal_default_instance());
  mdvolumebyprice_ = const_cast< ::com::htsc::mdc::insight::model::ADVolumeByPrice*>(
      ::com::htsc::mdc::insight::model::ADVolumeByPrice::internal_default_instance());
  mdfundflowanalysis_ = const_cast< ::com::htsc::mdc::insight::model::ADFundFlowAnalysis*>(
      ::com::htsc::mdc::insight::model::ADFundFlowAnalysis::internal_default_instance());
  mdforex_ = const_cast< ::com::htsc::mdc::insight::model::MDForex*>(
      ::com::htsc::mdc::insight::model::MDForex::internal_default_instance());
  mdspot_ = const_cast< ::com::htsc::mdc::insight::model::MDSpot*>(
      ::com::htsc::mdc::insight::model::MDSpot::internal_default_instance());
  mdrate_ = const_cast< ::com::htsc::mdc::insight::model::MDRate*>(
      ::com::htsc::mdc::insight::model::MDRate::internal_default_instance());
  orderbooksnapshot_ = const_cast< ::com::htsc::mdc::insight::model::ADOrderbookSnapshot*>(
      ::com::htsc::mdc::insight::model::ADOrderbookSnapshot::internal_default_instance());
  orderbooksnapshotwithtick_ = const_cast< ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick*>(
      ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick::internal_default_instance());
  mdquote_ = const_cast< ::com::htsc::mdc::insight::model::MDQuote*>(
      ::com::htsc::mdc::insight::model::MDQuote::internal_default_instance());
  mdetfbasicinfo_ = const_cast< ::com::htsc::mdc::insight::model::MDETFBasicInfo*>(
      ::com::htsc::mdc::insight::model::MDETFBasicInfo::internal_default_instance());
  mdfiquote_ = const_cast< ::com::htsc::mdc::insight::model::MDFIQuote*>(
      ::com::htsc::mdc::insight::model::MDFIQuote::internal_default_instance());
  mdchipdistribution_ = const_cast< ::com::htsc::mdc::insight::model::ADChipDistribution*>(
      ::com::htsc::mdc::insight::model::ADChipDistribution::internal_default_instance());
  mdwarrant_ = const_cast< ::com::htsc::mdc::insight::model::MDWarrant*>(
      ::com::htsc::mdc::insight::model::MDWarrant::internal_default_instance());
  mdsecuritylending_ = const_cast< ::com::htsc::mdc::insight::model::MDSecurityLending*>(
      ::com::htsc::mdc::insight::model::MDSecurityLending::internal_default_instance());
  mdnews_ = const_cast< ::com::htsc::mdc::insight::model::ADNews*>(
      ::com::htsc::mdc::insight::model::ADNews::internal_default_instance());
  mdstaringresult_ = const_cast< ::com::htsc::mdc::insight::model::ADStaringResult*>(
      ::com::htsc::mdc::insight::model::ADStaringResult::internal_default_instance());
  mdderivedanalysis_ = const_cast< ::com::htsc::mdc::insight::model::ADDerivedAnalysis*>(
      ::com::htsc::mdc::insight::model::ADDerivedAnalysis::internal_default_instance());
  mdqbquote_ = const_cast< ::com::htsc::mdc::insight::model::MDQBQuote*>(
      ::com::htsc::mdc::insight::model::MDQBQuote::internal_default_instance());
  mdqbtransaction_ = const_cast< ::com::htsc::mdc::insight::model::MDQBTransaction*>(
      ::com::htsc::mdc::insight::model::MDQBTransaction::internal_default_instance());
  mdusaorder_ = const_cast< ::com::htsc::mdc::insight::model::MDUSAOrder*>(
      ::com::htsc::mdc::insight::model::MDUSAOrder::internal_default_instance());
  mdusatransaction_ = const_cast< ::com::htsc::mdc::insight::model::MDUSATransaction*>(
      ::com::htsc::mdc::insight::model::MDUSATransaction::internal_default_instance());
  mdslorder_ = const_cast< ::com::htsc::mdc::insight::model::MDSLOrder*>(
      ::com::htsc::mdc::insight::model::MDSLOrder::internal_default_instance());
  mdsltransaction_ = const_cast< ::com::htsc::mdc::insight::model::MDSLTransaction*>(
      ::com::htsc::mdc::insight::model::MDSLTransaction::internal_default_instance());
  mdhkgreymarket_ = const_cast< ::com::htsc::mdc::insight::model::MDHKGreyMarket*>(
      ::com::htsc::mdc::insight::model::MDHKGreyMarket::internal_default_instance());
  mdslindicativequote_ = const_cast< ::com::htsc::mdc::insight::model::MDSLIndicativeQuote*>(
      ::com::htsc::mdc::insight::model::MDSLIndicativeQuote::internal_default_instance());
  mdslstatistics_ = const_cast< ::com::htsc::mdc::insight::model::MDSLStatistics*>(
      ::com::htsc::mdc::insight::model::MDSLStatistics::internal_default_instance());
  mdusaquote_ = const_cast< ::com::htsc::mdc::insight::model::MDUSAQuote*>(
      ::com::htsc::mdc::insight::model::MDUSAQuote::internal_default_instance());
  mdslestimation_ = const_cast< ::com::htsc::mdc::insight::model::MDSLEstimation*>(
      ::com::htsc::mdc::insight::model::MDSLEstimation::internal_default_instance());
  mdcnexdeal_ = const_cast< ::com::htsc::mdc::insight::model::MDCnexDeal*>(
      ::com::htsc::mdc::insight::model::MDCnexDeal::internal_default_instance());
  mdcnexquote_ = const_cast< ::com::htsc::mdc::insight::model::MDCnexQuote*>(
      ::com::htsc::mdc::insight::model::MDCnexQuote::internal_default_instance());
  mddelaysnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDDelaySnapshot*>(
      ::com::htsc::mdc::insight::model::MDDelaySnapshot::internal_default_instance());
  mdhighaccuracyfuture_ = const_cast< ::com::htsc::mdc::insight::model::MDHighAccuracyFuture*>(
      ::com::htsc::mdc::insight::model::MDHighAccuracyFuture::internal_default_instance());
  mdcfetsforex_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsForex*>(
      ::com::htsc::mdc::insight::model::MDCfetsForex::internal_default_instance());
  mdcfetsfxsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot*>(
      ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot::internal_default_instance());
  mdcfetsfxquote_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsFxQuote*>(
      ::com::htsc::mdc::insight::model::MDCfetsFxQuote::internal_default_instance());
  spfuture_ = const_cast< ::com::htsc::mdc::insight::model::SPFuture*>(
      ::com::htsc::mdc::insight::model::SPFuture::internal_default_instance());
  mdcfetsbenchmark_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsBenchmark*>(
      ::com::htsc::mdc::insight::model::MDCfetsBenchmark::internal_default_instance());
  mdcfetsbonddeal_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsBondDeal*>(
      ::com::htsc::mdc::insight::model::MDCfetsBondDeal::internal_default_instance());
  mdcfetsbondsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot*>(
      ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot::internal_default_instance());
  mdcfetscurrencydeal_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal*>(
      ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal::internal_default_instance());
  mdcfetscurrencysnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot*>(
      ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot::internal_default_instance());
  mdcfetsodmsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot*>(
      ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot::internal_default_instance());
  mdcfetsqdmquote_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsQDMQuote*>(
      ::com::htsc::mdc::insight::model::MDCfetsQDMQuote::internal_default_instance());
  mdcfetsratedeal_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsRateDeal*>(
      ::com::htsc::mdc::insight::model::MDCfetsRateDeal::internal_default_instance());
  mdcfetsratesnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot*>(
      ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot::internal_default_instance());
  mdcfetsfxcnymiddleprice_ = const_cast< ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice*>(
      ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice::internal_default_instance());
  mdiopvsnapshot_ = const_cast< ::com::htsc::mdc::insight::model::MDIopvSnapshot*>(
      ::com::htsc::mdc::insight::model::MDIopvSnapshot::internal_default_instance());
  mdchinabondbenchmark_ = const_cast< ::com::htsc::mdc::insight::model::MDChinaBondBenchmark*>(
      ::com::htsc::mdc::insight::model::MDChinaBondBenchmark::internal_default_instance());
  mdicetrace_ = const_cast< ::com::htsc::mdc::insight::model::MDIceTrace*>(
      ::com::htsc::mdc::insight::model::MDIceTrace::internal_default_instance());
}

MarketData::MarketData(const MarketData& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MarketData)
}

void MarketData::SharedCtor() {
  mdstock_ = NULL;
  mdindex_ = NULL;
  mdbond_ = NULL;
  mdfund_ = NULL;
  mdoption_ = NULL;
  mdfuture_ = NULL;
  mdtransaction_ = NULL;
  mdorder_ = NULL;
  mdkline_ = NULL;
  mdtwap_ = NULL;
  mdvwap_ = NULL;
  mdconstant_ = NULL;
  mdsimpletick_ = NULL;
  mdupsdownsanalysis_ = NULL;
  mdindicatorsranking_ = NULL;
  dynamicpacket_ = NULL;
  mdvolumebyprice_ = NULL;
  mdfundflowanalysis_ = NULL;
  mdforex_ = NULL;
  mdspot_ = NULL;
  mdrate_ = NULL;
  orderbooksnapshot_ = NULL;
  orderbooksnapshotwithtick_ = NULL;
  mdquote_ = NULL;
  mdetfbasicinfo_ = NULL;
  mdfiquote_ = NULL;
  mdchipdistribution_ = NULL;
  mdwarrant_ = NULL;
  mdsecuritylending_ = NULL;
  mdnews_ = NULL;
  mdstaringresult_ = NULL;
  mdderivedanalysis_ = NULL;
  mdqbquote_ = NULL;
  mdqbtransaction_ = NULL;
  mdusaorder_ = NULL;
  mdusatransaction_ = NULL;
  mdslorder_ = NULL;
  mdsltransaction_ = NULL;
  mdhkgreymarket_ = NULL;
  mdslindicativequote_ = NULL;
  mdslstatistics_ = NULL;
  mdusaquote_ = NULL;
  mdslestimation_ = NULL;
  mdcnexdeal_ = NULL;
  mdcnexquote_ = NULL;
  mddelaysnapshot_ = NULL;
  mdhighaccuracyfuture_ = NULL;
  mdcfetsforex_ = NULL;
  mdcfetsfxsnapshot_ = NULL;
  mdcfetsfxquote_ = NULL;
  spfuture_ = NULL;
  mdcfetsbenchmark_ = NULL;
  mdcfetsbonddeal_ = NULL;
  mdcfetsbondsnapshot_ = NULL;
  mdcfetscurrencydeal_ = NULL;
  mdcfetscurrencysnapshot_ = NULL;
  mdcfetsodmsnapshot_ = NULL;
  mdcfetsqdmquote_ = NULL;
  mdcfetsratedeal_ = NULL;
  mdcfetsratesnapshot_ = NULL;
  mdcfetsfxcnymiddleprice_ = NULL;
  mdiopvsnapshot_ = NULL;
  mdchinabondbenchmark_ = NULL;
  mdicetrace_ = NULL;
  ::memset(&messagechannelnumber_, 0, reinterpret_cast<char*>(&marketdatatype_) -
    reinterpret_cast<char*>(&messagechannelnumber_) + sizeof(marketdatatype_));
  _cached_size_ = 0;
}

MarketData::~MarketData() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MarketData)
  SharedDtor();
}

void MarketData::SharedDtor() {
  if (this != &MarketData_default_instance_.get()) {
    delete mdstock_;
    delete mdindex_;
    delete mdbond_;
    delete mdfund_;
    delete mdoption_;
    delete mdfuture_;
    delete mdtransaction_;
    delete mdorder_;
    delete mdkline_;
    delete mdtwap_;
    delete mdvwap_;
    delete mdconstant_;
    delete mdsimpletick_;
    delete mdupsdownsanalysis_;
    delete mdindicatorsranking_;
    delete dynamicpacket_;
    delete mdvolumebyprice_;
    delete mdfundflowanalysis_;
    delete mdforex_;
    delete mdspot_;
    delete mdrate_;
    delete orderbooksnapshot_;
    delete orderbooksnapshotwithtick_;
    delete mdquote_;
    delete mdetfbasicinfo_;
    delete mdfiquote_;
    delete mdchipdistribution_;
    delete mdwarrant_;
    delete mdsecuritylending_;
    delete mdnews_;
    delete mdstaringresult_;
    delete mdderivedanalysis_;
    delete mdqbquote_;
    delete mdqbtransaction_;
    delete mdusaorder_;
    delete mdusatransaction_;
    delete mdslorder_;
    delete mdsltransaction_;
    delete mdhkgreymarket_;
    delete mdslindicativequote_;
    delete mdslstatistics_;
    delete mdusaquote_;
    delete mdslestimation_;
    delete mdcnexdeal_;
    delete mdcnexquote_;
    delete mddelaysnapshot_;
    delete mdhighaccuracyfuture_;
    delete mdcfetsforex_;
    delete mdcfetsfxsnapshot_;
    delete mdcfetsfxquote_;
    delete spfuture_;
    delete mdcfetsbenchmark_;
    delete mdcfetsbonddeal_;
    delete mdcfetsbondsnapshot_;
    delete mdcfetscurrencydeal_;
    delete mdcfetscurrencysnapshot_;
    delete mdcfetsodmsnapshot_;
    delete mdcfetsqdmquote_;
    delete mdcfetsratedeal_;
    delete mdcfetsratesnapshot_;
    delete mdcfetsfxcnymiddleprice_;
    delete mdiopvsnapshot_;
    delete mdchinabondbenchmark_;
    delete mdicetrace_;
  }
}

void MarketData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarketData::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MarketData_descriptor_;
}

const MarketData& MarketData::default_instance() {
  protobuf_InitDefaults_MarketData_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MarketData> MarketData_default_instance_;

MarketData* MarketData::New(::google::protobuf::Arena* arena) const {
  MarketData* n = new MarketData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarketData::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MarketData)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MarketData, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MarketData*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(messagechannelnumber_, marketdatatype_);
  if (GetArenaNoVirtual() == NULL && mdstock_ != NULL) delete mdstock_;
  mdstock_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdindex_ != NULL) delete mdindex_;
  mdindex_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdbond_ != NULL) delete mdbond_;
  mdbond_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdfund_ != NULL) delete mdfund_;
  mdfund_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdoption_ != NULL) delete mdoption_;
  mdoption_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdfuture_ != NULL) delete mdfuture_;
  mdfuture_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdtransaction_ != NULL) delete mdtransaction_;
  mdtransaction_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdorder_ != NULL) delete mdorder_;
  mdorder_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdkline_ != NULL) delete mdkline_;
  mdkline_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdtwap_ != NULL) delete mdtwap_;
  mdtwap_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdvwap_ != NULL) delete mdvwap_;
  mdvwap_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdconstant_ != NULL) delete mdconstant_;
  mdconstant_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdsimpletick_ != NULL) delete mdsimpletick_;
  mdsimpletick_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdupsdownsanalysis_ != NULL) delete mdupsdownsanalysis_;
  mdupsdownsanalysis_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdindicatorsranking_ != NULL) delete mdindicatorsranking_;
  mdindicatorsranking_ = NULL;
  if (GetArenaNoVirtual() == NULL && dynamicpacket_ != NULL) delete dynamicpacket_;
  dynamicpacket_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdvolumebyprice_ != NULL) delete mdvolumebyprice_;
  mdvolumebyprice_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdfundflowanalysis_ != NULL) delete mdfundflowanalysis_;
  mdfundflowanalysis_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdforex_ != NULL) delete mdforex_;
  mdforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdspot_ != NULL) delete mdspot_;
  mdspot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdrate_ != NULL) delete mdrate_;
  mdrate_ = NULL;
  if (GetArenaNoVirtual() == NULL && orderbooksnapshot_ != NULL) delete orderbooksnapshot_;
  orderbooksnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && orderbooksnapshotwithtick_ != NULL) delete orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdquote_ != NULL) delete mdquote_;
  mdquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdetfbasicinfo_ != NULL) delete mdetfbasicinfo_;
  mdetfbasicinfo_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdfiquote_ != NULL) delete mdfiquote_;
  mdfiquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdchipdistribution_ != NULL) delete mdchipdistribution_;
  mdchipdistribution_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdwarrant_ != NULL) delete mdwarrant_;
  mdwarrant_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdsecuritylending_ != NULL) delete mdsecuritylending_;
  mdsecuritylending_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdnews_ != NULL) delete mdnews_;
  mdnews_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdstaringresult_ != NULL) delete mdstaringresult_;
  mdstaringresult_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdderivedanalysis_ != NULL) delete mdderivedanalysis_;
  mdderivedanalysis_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdqbquote_ != NULL) delete mdqbquote_;
  mdqbquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdqbtransaction_ != NULL) delete mdqbtransaction_;
  mdqbtransaction_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdusaorder_ != NULL) delete mdusaorder_;
  mdusaorder_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdusatransaction_ != NULL) delete mdusatransaction_;
  mdusatransaction_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdslorder_ != NULL) delete mdslorder_;
  mdslorder_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdsltransaction_ != NULL) delete mdsltransaction_;
  mdsltransaction_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdhkgreymarket_ != NULL) delete mdhkgreymarket_;
  mdhkgreymarket_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdslindicativequote_ != NULL) delete mdslindicativequote_;
  mdslindicativequote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdslstatistics_ != NULL) delete mdslstatistics_;
  mdslstatistics_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdusaquote_ != NULL) delete mdusaquote_;
  mdusaquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdslestimation_ != NULL) delete mdslestimation_;
  mdslestimation_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcnexdeal_ != NULL) delete mdcnexdeal_;
  mdcnexdeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcnexquote_ != NULL) delete mdcnexquote_;
  mdcnexquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mddelaysnapshot_ != NULL) delete mddelaysnapshot_;
  mddelaysnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdhighaccuracyfuture_ != NULL) delete mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsforex_ != NULL) delete mdcfetsforex_;
  mdcfetsforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsfxsnapshot_ != NULL) delete mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsfxquote_ != NULL) delete mdcfetsfxquote_;
  mdcfetsfxquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && spfuture_ != NULL) delete spfuture_;
  spfuture_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsbenchmark_ != NULL) delete mdcfetsbenchmark_;
  mdcfetsbenchmark_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsbonddeal_ != NULL) delete mdcfetsbonddeal_;
  mdcfetsbonddeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsbondsnapshot_ != NULL) delete mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencydeal_ != NULL) delete mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencysnapshot_ != NULL) delete mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsodmsnapshot_ != NULL) delete mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsqdmquote_ != NULL) delete mdcfetsqdmquote_;
  mdcfetsqdmquote_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsratedeal_ != NULL) delete mdcfetsratedeal_;
  mdcfetsratedeal_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsratesnapshot_ != NULL) delete mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdcfetsfxcnymiddleprice_ != NULL) delete mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdiopvsnapshot_ != NULL) delete mdiopvsnapshot_;
  mdiopvsnapshot_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdchinabondbenchmark_ != NULL) delete mdchinabondbenchmark_;
  mdchinabondbenchmark_ = NULL;
  if (GetArenaNoVirtual() == NULL && mdicetrace_ != NULL) delete mdicetrace_;
  mdicetrace_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MarketData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MarketData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_marketdatatype(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MessageChannelNumber;
        break;
      }

      // optional int64 MessageChannelNumber = 2;
      case 2: {
        if (tag == 16) {
         parse_MessageChannelNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &messagechannelnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_mdStock;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
      case 10: {
        if (tag == 82) {
         parse_mdStock:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdstock()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_mdIndex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
      case 11: {
        if (tag == 90) {
         parse_mdIndex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdindex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_mdBond;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
      case 12: {
        if (tag == 98) {
         parse_mdBond:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdbond()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_mdFund;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
      case 13: {
        if (tag == 106) {
         parse_mdFund:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdfund()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_mdOption;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
      case 14: {
        if (tag == 114) {
         parse_mdOption:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdoption()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(122)) goto parse_mdFuture;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
      case 15: {
        if (tag == 122) {
         parse_mdFuture:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdfuture()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_mdTransaction;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
      case 16: {
        if (tag == 130) {
         parse_mdTransaction:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdtransaction()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(138)) goto parse_mdOrder;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
      case 17: {
        if (tag == 138) {
         parse_mdOrder:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdorder()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(146)) goto parse_mdKLine;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
      case 18: {
        if (tag == 146) {
         parse_mdKLine:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdkline()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(154)) goto parse_mdTwap;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
      case 19: {
        if (tag == 154) {
         parse_mdTwap:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdtwap()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(162)) goto parse_mdVwap;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
      case 20: {
        if (tag == 162) {
         parse_mdVwap:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdvwap()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_mdConstant;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
      case 21: {
        if (tag == 170) {
         parse_mdConstant:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdconstant()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(178)) goto parse_mdSimpleTick;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
      case 22: {
        if (tag == 178) {
         parse_mdSimpleTick:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdsimpletick()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(186)) goto parse_mdUpsDownsAnalysis;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
      case 23: {
        if (tag == 186) {
         parse_mdUpsDownsAnalysis:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdupsdownsanalysis()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(194)) goto parse_mdIndicatorsRanking;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
      case 24: {
        if (tag == 194) {
         parse_mdIndicatorsRanking:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdindicatorsranking()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(202)) goto parse_dynamicPacket;
        break;
      }

      // optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
      case 25: {
        if (tag == 202) {
         parse_dynamicPacket:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_dynamicpacket()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(210)) goto parse_mdVolumeByPrice;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
      case 26: {
        if (tag == 210) {
         parse_mdVolumeByPrice:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdvolumebyprice()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(218)) goto parse_mdFundFlowAnalysis;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
      case 27: {
        if (tag == 218) {
         parse_mdFundFlowAnalysis:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdfundflowanalysis()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(226)) goto parse_mdForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
      case 28: {
        if (tag == 226) {
         parse_mdForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(234)) goto parse_mdSpot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
      case 29: {
        if (tag == 234) {
         parse_mdSpot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdspot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(242)) goto parse_mdRate;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
      case 30: {
        if (tag == 242) {
         parse_mdRate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdrate()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(250)) goto parse_orderbookSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
      case 31: {
        if (tag == 250) {
         parse_orderbookSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_orderbooksnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(258)) goto parse_orderbookSnapshotWithTick;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
      case 32: {
        if (tag == 258) {
         parse_orderbookSnapshotWithTick:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_orderbooksnapshotwithtick()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(266)) goto parse_mdQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
      case 33: {
        if (tag == 266) {
         parse_mdQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(274)) goto parse_mdETFBasicInfo;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
      case 34: {
        if (tag == 274) {
         parse_mdETFBasicInfo:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdetfbasicinfo()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(282)) goto parse_mdFIQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
      case 35: {
        if (tag == 282) {
         parse_mdFIQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdfiquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_mdChipDistribution;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
      case 36: {
        if (tag == 290) {
         parse_mdChipDistribution:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdchipdistribution()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(298)) goto parse_mdWarrant;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
      case 37: {
        if (tag == 298) {
         parse_mdWarrant:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdwarrant()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(306)) goto parse_mdSecurityLending;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
      case 38: {
        if (tag == 306) {
         parse_mdSecurityLending:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdsecuritylending()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(314)) goto parse_mdNews;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
      case 39: {
        if (tag == 314) {
         parse_mdNews:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdnews()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(322)) goto parse_mdStaringResult;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
      case 40: {
        if (tag == 322) {
         parse_mdStaringResult:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdstaringresult()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(330)) goto parse_mdDerivedAnalysis;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
      case 41: {
        if (tag == 330) {
         parse_mdDerivedAnalysis:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdderivedanalysis()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(338)) goto parse_mdQBQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
      case 42: {
        if (tag == 338) {
         parse_mdQBQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdqbquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(346)) goto parse_mdQBTransaction;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
      case 43: {
        if (tag == 346) {
         parse_mdQBTransaction:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdqbtransaction()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(354)) goto parse_mdUSAOrder;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
      case 44: {
        if (tag == 354) {
         parse_mdUSAOrder:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdusaorder()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(362)) goto parse_mdUSATransaction;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
      case 45: {
        if (tag == 362) {
         parse_mdUSATransaction:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdusatransaction()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(370)) goto parse_mdSLOrder;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
      case 46: {
        if (tag == 370) {
         parse_mdSLOrder:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdslorder()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(378)) goto parse_mdSLTransaction;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
      case 47: {
        if (tag == 378) {
         parse_mdSLTransaction:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdsltransaction()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(386)) goto parse_mdHKGreyMarket;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
      case 48: {
        if (tag == 386) {
         parse_mdHKGreyMarket:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdhkgreymarket()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(394)) goto parse_mdSLIndicativeQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
      case 49: {
        if (tag == 394) {
         parse_mdSLIndicativeQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdslindicativequote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(402)) goto parse_mdSLStatistics;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
      case 50: {
        if (tag == 402) {
         parse_mdSLStatistics:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdslstatistics()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_mdUSAQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
      case 51: {
        if (tag == 410) {
         parse_mdUSAQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdusaquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_mdSLEstimation;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
      case 52: {
        if (tag == 418) {
         parse_mdSLEstimation:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdslestimation()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_mdCnexDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
      case 53: {
        if (tag == 426) {
         parse_mdCnexDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcnexdeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_mdCnexQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
      case 54: {
        if (tag == 434) {
         parse_mdCnexQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcnexquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_mdDelaySnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
      case 55: {
        if (tag == 442) {
         parse_mdDelaySnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mddelaysnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_mdHighAccuracyFuture;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
      case 56: {
        if (tag == 450) {
         parse_mdHighAccuracyFuture:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdhighaccuracyfuture()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_mdCfetsForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
      case 57: {
        if (tag == 458) {
         parse_mdCfetsForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_mdCfetsFxSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
      case 58: {
        if (tag == 466) {
         parse_mdCfetsFxSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsfxsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(474)) goto parse_mdCfetsFxQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
      case 59: {
        if (tag == 474) {
         parse_mdCfetsFxQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsfxquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(482)) goto parse_spFuture;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
      case 60: {
        if (tag == 482) {
         parse_spFuture:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spfuture()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(490)) goto parse_mdCfetsBenchmark;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
      case 61: {
        if (tag == 490) {
         parse_mdCfetsBenchmark:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsbenchmark()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(498)) goto parse_mdCfetsBondDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
      case 62: {
        if (tag == 498) {
         parse_mdCfetsBondDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsbonddeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(506)) goto parse_mdCfetsBondSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
      case 63: {
        if (tag == 506) {
         parse_mdCfetsBondSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsbondsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(514)) goto parse_mdCfetsCurrencyDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
      case 64: {
        if (tag == 514) {
         parse_mdCfetsCurrencyDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetscurrencydeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(522)) goto parse_mdCfetsCurrencySnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
      case 65: {
        if (tag == 522) {
         parse_mdCfetsCurrencySnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetscurrencysnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(530)) goto parse_mdCfetsODMSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
      case 66: {
        if (tag == 530) {
         parse_mdCfetsODMSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsodmsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(538)) goto parse_mdCfetsQDMQuote;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
      case 67: {
        if (tag == 538) {
         parse_mdCfetsQDMQuote:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsqdmquote()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(546)) goto parse_mdCfetsRateDeal;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
      case 68: {
        if (tag == 546) {
         parse_mdCfetsRateDeal:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsratedeal()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(554)) goto parse_mdCfetsRateSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
      case 69: {
        if (tag == 554) {
         parse_mdCfetsRateSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsratesnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(562)) goto parse_mdCfetsFxCnyMiddlePrice;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
      case 70: {
        if (tag == 562) {
         parse_mdCfetsFxCnyMiddlePrice:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdcfetsfxcnymiddleprice()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(570)) goto parse_mdIopvSnapshot;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
      case 71: {
        if (tag == 570) {
         parse_mdIopvSnapshot:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdiopvsnapshot()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(578)) goto parse_mdChinaBondBenchmark;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
      case 72: {
        if (tag == 578) {
         parse_mdChinaBondBenchmark:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdchinabondbenchmark()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(586)) goto parse_mdIceTrace;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
      case 73: {
        if (tag == 586) {
         parse_mdIceTrace:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_mdicetrace()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MarketData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MarketData)
  return false;
#undef DO_
}

void MarketData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MarketData)
  // optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
  if (this->marketdatatype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->marketdatatype(), output);
  }

  // optional int64 MessageChannelNumber = 2;
  if (this->messagechannelnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->messagechannelnumber(), output);
  }

  // optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
  if (this->has_mdstock()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->mdstock_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
  if (this->has_mdindex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->mdindex_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
  if (this->has_mdbond()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->mdbond_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
  if (this->has_mdfund()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->mdfund_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
  if (this->has_mdoption()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->mdoption_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
  if (this->has_mdfuture()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      15, *this->mdfuture_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
  if (this->has_mdtransaction()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, *this->mdtransaction_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
  if (this->has_mdorder()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      17, *this->mdorder_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
  if (this->has_mdkline()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      18, *this->mdkline_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
  if (this->has_mdtwap()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      19, *this->mdtwap_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
  if (this->has_mdvwap()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      20, *this->mdvwap_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
  if (this->has_mdconstant()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      21, *this->mdconstant_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
  if (this->has_mdsimpletick()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      22, *this->mdsimpletick_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
  if (this->has_mdupsdownsanalysis()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      23, *this->mdupsdownsanalysis_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
  if (this->has_mdindicatorsranking()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      24, *this->mdindicatorsranking_, output);
  }

  // optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
  if (this->has_dynamicpacket()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      25, *this->dynamicpacket_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
  if (this->has_mdvolumebyprice()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      26, *this->mdvolumebyprice_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
  if (this->has_mdfundflowanalysis()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      27, *this->mdfundflowanalysis_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
  if (this->has_mdforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      28, *this->mdforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
  if (this->has_mdspot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      29, *this->mdspot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
  if (this->has_mdrate()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      30, *this->mdrate_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
  if (this->has_orderbooksnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      31, *this->orderbooksnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
  if (this->has_orderbooksnapshotwithtick()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      32, *this->orderbooksnapshotwithtick_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
  if (this->has_mdquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      33, *this->mdquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
  if (this->has_mdetfbasicinfo()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      34, *this->mdetfbasicinfo_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
  if (this->has_mdfiquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      35, *this->mdfiquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
  if (this->has_mdchipdistribution()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      36, *this->mdchipdistribution_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
  if (this->has_mdwarrant()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      37, *this->mdwarrant_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
  if (this->has_mdsecuritylending()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      38, *this->mdsecuritylending_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
  if (this->has_mdnews()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      39, *this->mdnews_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
  if (this->has_mdstaringresult()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      40, *this->mdstaringresult_, output);
  }

  // optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
  if (this->has_mdderivedanalysis()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      41, *this->mdderivedanalysis_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
  if (this->has_mdqbquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      42, *this->mdqbquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
  if (this->has_mdqbtransaction()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      43, *this->mdqbtransaction_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
  if (this->has_mdusaorder()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      44, *this->mdusaorder_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
  if (this->has_mdusatransaction()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      45, *this->mdusatransaction_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
  if (this->has_mdslorder()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      46, *this->mdslorder_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
  if (this->has_mdsltransaction()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      47, *this->mdsltransaction_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
  if (this->has_mdhkgreymarket()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      48, *this->mdhkgreymarket_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
  if (this->has_mdslindicativequote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      49, *this->mdslindicativequote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
  if (this->has_mdslstatistics()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      50, *this->mdslstatistics_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
  if (this->has_mdusaquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      51, *this->mdusaquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
  if (this->has_mdslestimation()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      52, *this->mdslestimation_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
  if (this->has_mdcnexdeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      53, *this->mdcnexdeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
  if (this->has_mdcnexquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      54, *this->mdcnexquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
  if (this->has_mddelaysnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      55, *this->mddelaysnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
  if (this->has_mdhighaccuracyfuture()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      56, *this->mdhighaccuracyfuture_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
  if (this->has_mdcfetsforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      57, *this->mdcfetsforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
  if (this->has_mdcfetsfxsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      58, *this->mdcfetsfxsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
  if (this->has_mdcfetsfxquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      59, *this->mdcfetsfxquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
  if (this->has_spfuture()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      60, *this->spfuture_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
  if (this->has_mdcfetsbenchmark()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      61, *this->mdcfetsbenchmark_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
  if (this->has_mdcfetsbonddeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      62, *this->mdcfetsbonddeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
  if (this->has_mdcfetsbondsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      63, *this->mdcfetsbondsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
  if (this->has_mdcfetscurrencydeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      64, *this->mdcfetscurrencydeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
  if (this->has_mdcfetscurrencysnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      65, *this->mdcfetscurrencysnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
  if (this->has_mdcfetsodmsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      66, *this->mdcfetsodmsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
  if (this->has_mdcfetsqdmquote()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      67, *this->mdcfetsqdmquote_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
  if (this->has_mdcfetsratedeal()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      68, *this->mdcfetsratedeal_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
  if (this->has_mdcfetsratesnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      69, *this->mdcfetsratesnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
  if (this->has_mdcfetsfxcnymiddleprice()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      70, *this->mdcfetsfxcnymiddleprice_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
  if (this->has_mdiopvsnapshot()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      71, *this->mdiopvsnapshot_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
  if (this->has_mdchinabondbenchmark()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      72, *this->mdchinabondbenchmark_, output);
  }

  // optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
  if (this->has_mdicetrace()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      73, *this->mdicetrace_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MarketData)
}

::google::protobuf::uint8* MarketData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MarketData)
  // optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
  if (this->marketdatatype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->marketdatatype(), target);
  }

  // optional int64 MessageChannelNumber = 2;
  if (this->messagechannelnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->messagechannelnumber(), target);
  }

  // optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
  if (this->has_mdstock()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->mdstock_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
  if (this->has_mdindex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->mdindex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
  if (this->has_mdbond()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->mdbond_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
  if (this->has_mdfund()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->mdfund_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
  if (this->has_mdoption()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->mdoption_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
  if (this->has_mdfuture()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        15, *this->mdfuture_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
  if (this->has_mdtransaction()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        16, *this->mdtransaction_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
  if (this->has_mdorder()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        17, *this->mdorder_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
  if (this->has_mdkline()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        18, *this->mdkline_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
  if (this->has_mdtwap()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        19, *this->mdtwap_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
  if (this->has_mdvwap()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        20, *this->mdvwap_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
  if (this->has_mdconstant()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        21, *this->mdconstant_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
  if (this->has_mdsimpletick()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        22, *this->mdsimpletick_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
  if (this->has_mdupsdownsanalysis()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        23, *this->mdupsdownsanalysis_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
  if (this->has_mdindicatorsranking()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        24, *this->mdindicatorsranking_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
  if (this->has_dynamicpacket()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        25, *this->dynamicpacket_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
  if (this->has_mdvolumebyprice()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        26, *this->mdvolumebyprice_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
  if (this->has_mdfundflowanalysis()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        27, *this->mdfundflowanalysis_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
  if (this->has_mdforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        28, *this->mdforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
  if (this->has_mdspot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        29, *this->mdspot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
  if (this->has_mdrate()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        30, *this->mdrate_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
  if (this->has_orderbooksnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        31, *this->orderbooksnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
  if (this->has_orderbooksnapshotwithtick()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        32, *this->orderbooksnapshotwithtick_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
  if (this->has_mdquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        33, *this->mdquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
  if (this->has_mdetfbasicinfo()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        34, *this->mdetfbasicinfo_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
  if (this->has_mdfiquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        35, *this->mdfiquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
  if (this->has_mdchipdistribution()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        36, *this->mdchipdistribution_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
  if (this->has_mdwarrant()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        37, *this->mdwarrant_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
  if (this->has_mdsecuritylending()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        38, *this->mdsecuritylending_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
  if (this->has_mdnews()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        39, *this->mdnews_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
  if (this->has_mdstaringresult()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        40, *this->mdstaringresult_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
  if (this->has_mdderivedanalysis()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        41, *this->mdderivedanalysis_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
  if (this->has_mdqbquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        42, *this->mdqbquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
  if (this->has_mdqbtransaction()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        43, *this->mdqbtransaction_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
  if (this->has_mdusaorder()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        44, *this->mdusaorder_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
  if (this->has_mdusatransaction()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        45, *this->mdusatransaction_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
  if (this->has_mdslorder()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        46, *this->mdslorder_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
  if (this->has_mdsltransaction()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        47, *this->mdsltransaction_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
  if (this->has_mdhkgreymarket()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        48, *this->mdhkgreymarket_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
  if (this->has_mdslindicativequote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        49, *this->mdslindicativequote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
  if (this->has_mdslstatistics()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        50, *this->mdslstatistics_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
  if (this->has_mdusaquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        51, *this->mdusaquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
  if (this->has_mdslestimation()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        52, *this->mdslestimation_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
  if (this->has_mdcnexdeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        53, *this->mdcnexdeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
  if (this->has_mdcnexquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        54, *this->mdcnexquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
  if (this->has_mddelaysnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        55, *this->mddelaysnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
  if (this->has_mdhighaccuracyfuture()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        56, *this->mdhighaccuracyfuture_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
  if (this->has_mdcfetsforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        57, *this->mdcfetsforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
  if (this->has_mdcfetsfxsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        58, *this->mdcfetsfxsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
  if (this->has_mdcfetsfxquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        59, *this->mdcfetsfxquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
  if (this->has_spfuture()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        60, *this->spfuture_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
  if (this->has_mdcfetsbenchmark()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        61, *this->mdcfetsbenchmark_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
  if (this->has_mdcfetsbonddeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        62, *this->mdcfetsbonddeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
  if (this->has_mdcfetsbondsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        63, *this->mdcfetsbondsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
  if (this->has_mdcfetscurrencydeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        64, *this->mdcfetscurrencydeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
  if (this->has_mdcfetscurrencysnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        65, *this->mdcfetscurrencysnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
  if (this->has_mdcfetsodmsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        66, *this->mdcfetsodmsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
  if (this->has_mdcfetsqdmquote()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        67, *this->mdcfetsqdmquote_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
  if (this->has_mdcfetsratedeal()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        68, *this->mdcfetsratedeal_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
  if (this->has_mdcfetsratesnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        69, *this->mdcfetsratesnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
  if (this->has_mdcfetsfxcnymiddleprice()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        70, *this->mdcfetsfxcnymiddleprice_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
  if (this->has_mdiopvsnapshot()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        71, *this->mdiopvsnapshot_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
  if (this->has_mdchinabondbenchmark()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        72, *this->mdchinabondbenchmark_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
  if (this->has_mdicetrace()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        73, *this->mdicetrace_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MarketData)
  return target;
}

size_t MarketData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MarketData)
  size_t total_size = 0;

  // optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
  if (this->marketdatatype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->marketdatatype());
  }

  // optional int64 MessageChannelNumber = 2;
  if (this->messagechannelnumber() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->messagechannelnumber());
  }

  // optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
  if (this->has_mdstock()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdstock_);
  }

  // optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
  if (this->has_mdindex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdindex_);
  }

  // optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
  if (this->has_mdbond()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdbond_);
  }

  // optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
  if (this->has_mdfund()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdfund_);
  }

  // optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
  if (this->has_mdoption()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdoption_);
  }

  // optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
  if (this->has_mdfuture()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdfuture_);
  }

  // optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
  if (this->has_mdtransaction()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdtransaction_);
  }

  // optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
  if (this->has_mdorder()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdorder_);
  }

  // optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
  if (this->has_mdkline()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdkline_);
  }

  // optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
  if (this->has_mdtwap()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdtwap_);
  }

  // optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
  if (this->has_mdvwap()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdvwap_);
  }

  // optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
  if (this->has_mdconstant()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdconstant_);
  }

  // optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
  if (this->has_mdsimpletick()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdsimpletick_);
  }

  // optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
  if (this->has_mdupsdownsanalysis()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdupsdownsanalysis_);
  }

  // optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
  if (this->has_mdindicatorsranking()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdindicatorsranking_);
  }

  // optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
  if (this->has_dynamicpacket()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->dynamicpacket_);
  }

  // optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
  if (this->has_mdvolumebyprice()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdvolumebyprice_);
  }

  // optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
  if (this->has_mdfundflowanalysis()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdfundflowanalysis_);
  }

  // optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
  if (this->has_mdforex()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdforex_);
  }

  // optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
  if (this->has_mdspot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdspot_);
  }

  // optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
  if (this->has_mdrate()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdrate_);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
  if (this->has_orderbooksnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->orderbooksnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
  if (this->has_orderbooksnapshotwithtick()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->orderbooksnapshotwithtick_);
  }

  // optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
  if (this->has_mdquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
  if (this->has_mdetfbasicinfo()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdetfbasicinfo_);
  }

  // optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
  if (this->has_mdfiquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdfiquote_);
  }

  // optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
  if (this->has_mdchipdistribution()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdchipdistribution_);
  }

  // optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
  if (this->has_mdwarrant()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdwarrant_);
  }

  // optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
  if (this->has_mdsecuritylending()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdsecuritylending_);
  }

  // optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
  if (this->has_mdnews()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdnews_);
  }

  // optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
  if (this->has_mdstaringresult()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdstaringresult_);
  }

  // optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
  if (this->has_mdderivedanalysis()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdderivedanalysis_);
  }

  // optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
  if (this->has_mdqbquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdqbquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
  if (this->has_mdqbtransaction()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdqbtransaction_);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
  if (this->has_mdusaorder()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdusaorder_);
  }

  // optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
  if (this->has_mdusatransaction()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdusatransaction_);
  }

  // optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
  if (this->has_mdslorder()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdslorder_);
  }

  // optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
  if (this->has_mdsltransaction()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdsltransaction_);
  }

  // optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
  if (this->has_mdhkgreymarket()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdhkgreymarket_);
  }

  // optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
  if (this->has_mdslindicativequote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdslindicativequote_);
  }

  // optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
  if (this->has_mdslstatistics()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdslstatistics_);
  }

  // optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
  if (this->has_mdusaquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdusaquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
  if (this->has_mdslestimation()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdslestimation_);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
  if (this->has_mdcnexdeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcnexdeal_);
  }

  // optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
  if (this->has_mdcnexquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcnexquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
  if (this->has_mddelaysnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mddelaysnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
  if (this->has_mdhighaccuracyfuture()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdhighaccuracyfuture_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
  if (this->has_mdcfetsforex()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsforex_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
  if (this->has_mdcfetsfxsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsfxsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
  if (this->has_mdcfetsfxquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsfxquote_);
  }

  // optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
  if (this->has_spfuture()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spfuture_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
  if (this->has_mdcfetsbenchmark()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsbenchmark_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
  if (this->has_mdcfetsbonddeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsbonddeal_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
  if (this->has_mdcfetsbondsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsbondsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
  if (this->has_mdcfetscurrencydeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetscurrencydeal_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
  if (this->has_mdcfetscurrencysnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetscurrencysnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
  if (this->has_mdcfetsodmsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsodmsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
  if (this->has_mdcfetsqdmquote()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsqdmquote_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
  if (this->has_mdcfetsratedeal()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsratedeal_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
  if (this->has_mdcfetsratesnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsratesnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
  if (this->has_mdcfetsfxcnymiddleprice()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdcfetsfxcnymiddleprice_);
  }

  // optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
  if (this->has_mdiopvsnapshot()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdiopvsnapshot_);
  }

  // optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
  if (this->has_mdchinabondbenchmark()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdchinabondbenchmark_);
  }

  // optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
  if (this->has_mdicetrace()) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->mdicetrace_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarketData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MarketData)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MarketData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarketData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MarketData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MarketData)
    UnsafeMergeFrom(*source);
  }
}

void MarketData::MergeFrom(const MarketData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MarketData)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MarketData::UnsafeMergeFrom(const MarketData& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.marketdatatype() != 0) {
    set_marketdatatype(from.marketdatatype());
  }
  if (from.messagechannelnumber() != 0) {
    set_messagechannelnumber(from.messagechannelnumber());
  }
  if (from.has_mdstock()) {
    mutable_mdstock()->::com::htsc::mdc::insight::model::MDStock::MergeFrom(from.mdstock());
  }
  if (from.has_mdindex()) {
    mutable_mdindex()->::com::htsc::mdc::insight::model::MDIndex::MergeFrom(from.mdindex());
  }
  if (from.has_mdbond()) {
    mutable_mdbond()->::com::htsc::mdc::insight::model::MDBond::MergeFrom(from.mdbond());
  }
  if (from.has_mdfund()) {
    mutable_mdfund()->::com::htsc::mdc::insight::model::MDFund::MergeFrom(from.mdfund());
  }
  if (from.has_mdoption()) {
    mutable_mdoption()->::com::htsc::mdc::insight::model::MDOption::MergeFrom(from.mdoption());
  }
  if (from.has_mdfuture()) {
    mutable_mdfuture()->::com::htsc::mdc::insight::model::MDFuture::MergeFrom(from.mdfuture());
  }
  if (from.has_mdtransaction()) {
    mutable_mdtransaction()->::com::htsc::mdc::insight::model::MDTransaction::MergeFrom(from.mdtransaction());
  }
  if (from.has_mdorder()) {
    mutable_mdorder()->::com::htsc::mdc::insight::model::MDOrder::MergeFrom(from.mdorder());
  }
  if (from.has_mdkline()) {
    mutable_mdkline()->::com::htsc::mdc::insight::model::ADKLine::MergeFrom(from.mdkline());
  }
  if (from.has_mdtwap()) {
    mutable_mdtwap()->::com::htsc::mdc::insight::model::ADTwap::MergeFrom(from.mdtwap());
  }
  if (from.has_mdvwap()) {
    mutable_mdvwap()->::com::htsc::mdc::insight::model::ADVwap::MergeFrom(from.mdvwap());
  }
  if (from.has_mdconstant()) {
    mutable_mdconstant()->::com::htsc::mdc::insight::model::MDBasicInfo::MergeFrom(from.mdconstant());
  }
  if (from.has_mdsimpletick()) {
    mutable_mdsimpletick()->::com::htsc::mdc::insight::model::MDSimpleTick::MergeFrom(from.mdsimpletick());
  }
  if (from.has_mdupsdownsanalysis()) {
    mutable_mdupsdownsanalysis()->::com::htsc::mdc::insight::model::ADUpsDownsAnalysis::MergeFrom(from.mdupsdownsanalysis());
  }
  if (from.has_mdindicatorsranking()) {
    mutable_mdindicatorsranking()->::com::htsc::mdc::insight::model::ADIndicatorsRanking::MergeFrom(from.mdindicatorsranking());
  }
  if (from.has_dynamicpacket()) {
    mutable_dynamicpacket()->::com::htsc::mdc::insight::model::DynamicPacket::MergeFrom(from.dynamicpacket());
  }
  if (from.has_mdvolumebyprice()) {
    mutable_mdvolumebyprice()->::com::htsc::mdc::insight::model::ADVolumeByPrice::MergeFrom(from.mdvolumebyprice());
  }
  if (from.has_mdfundflowanalysis()) {
    mutable_mdfundflowanalysis()->::com::htsc::mdc::insight::model::ADFundFlowAnalysis::MergeFrom(from.mdfundflowanalysis());
  }
  if (from.has_mdforex()) {
    mutable_mdforex()->::com::htsc::mdc::insight::model::MDForex::MergeFrom(from.mdforex());
  }
  if (from.has_mdspot()) {
    mutable_mdspot()->::com::htsc::mdc::insight::model::MDSpot::MergeFrom(from.mdspot());
  }
  if (from.has_mdrate()) {
    mutable_mdrate()->::com::htsc::mdc::insight::model::MDRate::MergeFrom(from.mdrate());
  }
  if (from.has_orderbooksnapshot()) {
    mutable_orderbooksnapshot()->::com::htsc::mdc::insight::model::ADOrderbookSnapshot::MergeFrom(from.orderbooksnapshot());
  }
  if (from.has_orderbooksnapshotwithtick()) {
    mutable_orderbooksnapshotwithtick()->::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick::MergeFrom(from.orderbooksnapshotwithtick());
  }
  if (from.has_mdquote()) {
    mutable_mdquote()->::com::htsc::mdc::insight::model::MDQuote::MergeFrom(from.mdquote());
  }
  if (from.has_mdetfbasicinfo()) {
    mutable_mdetfbasicinfo()->::com::htsc::mdc::insight::model::MDETFBasicInfo::MergeFrom(from.mdetfbasicinfo());
  }
  if (from.has_mdfiquote()) {
    mutable_mdfiquote()->::com::htsc::mdc::insight::model::MDFIQuote::MergeFrom(from.mdfiquote());
  }
  if (from.has_mdchipdistribution()) {
    mutable_mdchipdistribution()->::com::htsc::mdc::insight::model::ADChipDistribution::MergeFrom(from.mdchipdistribution());
  }
  if (from.has_mdwarrant()) {
    mutable_mdwarrant()->::com::htsc::mdc::insight::model::MDWarrant::MergeFrom(from.mdwarrant());
  }
  if (from.has_mdsecuritylending()) {
    mutable_mdsecuritylending()->::com::htsc::mdc::insight::model::MDSecurityLending::MergeFrom(from.mdsecuritylending());
  }
  if (from.has_mdnews()) {
    mutable_mdnews()->::com::htsc::mdc::insight::model::ADNews::MergeFrom(from.mdnews());
  }
  if (from.has_mdstaringresult()) {
    mutable_mdstaringresult()->::com::htsc::mdc::insight::model::ADStaringResult::MergeFrom(from.mdstaringresult());
  }
  if (from.has_mdderivedanalysis()) {
    mutable_mdderivedanalysis()->::com::htsc::mdc::insight::model::ADDerivedAnalysis::MergeFrom(from.mdderivedanalysis());
  }
  if (from.has_mdqbquote()) {
    mutable_mdqbquote()->::com::htsc::mdc::insight::model::MDQBQuote::MergeFrom(from.mdqbquote());
  }
  if (from.has_mdqbtransaction()) {
    mutable_mdqbtransaction()->::com::htsc::mdc::insight::model::MDQBTransaction::MergeFrom(from.mdqbtransaction());
  }
  if (from.has_mdusaorder()) {
    mutable_mdusaorder()->::com::htsc::mdc::insight::model::MDUSAOrder::MergeFrom(from.mdusaorder());
  }
  if (from.has_mdusatransaction()) {
    mutable_mdusatransaction()->::com::htsc::mdc::insight::model::MDUSATransaction::MergeFrom(from.mdusatransaction());
  }
  if (from.has_mdslorder()) {
    mutable_mdslorder()->::com::htsc::mdc::insight::model::MDSLOrder::MergeFrom(from.mdslorder());
  }
  if (from.has_mdsltransaction()) {
    mutable_mdsltransaction()->::com::htsc::mdc::insight::model::MDSLTransaction::MergeFrom(from.mdsltransaction());
  }
  if (from.has_mdhkgreymarket()) {
    mutable_mdhkgreymarket()->::com::htsc::mdc::insight::model::MDHKGreyMarket::MergeFrom(from.mdhkgreymarket());
  }
  if (from.has_mdslindicativequote()) {
    mutable_mdslindicativequote()->::com::htsc::mdc::insight::model::MDSLIndicativeQuote::MergeFrom(from.mdslindicativequote());
  }
  if (from.has_mdslstatistics()) {
    mutable_mdslstatistics()->::com::htsc::mdc::insight::model::MDSLStatistics::MergeFrom(from.mdslstatistics());
  }
  if (from.has_mdusaquote()) {
    mutable_mdusaquote()->::com::htsc::mdc::insight::model::MDUSAQuote::MergeFrom(from.mdusaquote());
  }
  if (from.has_mdslestimation()) {
    mutable_mdslestimation()->::com::htsc::mdc::insight::model::MDSLEstimation::MergeFrom(from.mdslestimation());
  }
  if (from.has_mdcnexdeal()) {
    mutable_mdcnexdeal()->::com::htsc::mdc::insight::model::MDCnexDeal::MergeFrom(from.mdcnexdeal());
  }
  if (from.has_mdcnexquote()) {
    mutable_mdcnexquote()->::com::htsc::mdc::insight::model::MDCnexQuote::MergeFrom(from.mdcnexquote());
  }
  if (from.has_mddelaysnapshot()) {
    mutable_mddelaysnapshot()->::com::htsc::mdc::insight::model::MDDelaySnapshot::MergeFrom(from.mddelaysnapshot());
  }
  if (from.has_mdhighaccuracyfuture()) {
    mutable_mdhighaccuracyfuture()->::com::htsc::mdc::insight::model::MDHighAccuracyFuture::MergeFrom(from.mdhighaccuracyfuture());
  }
  if (from.has_mdcfetsforex()) {
    mutable_mdcfetsforex()->::com::htsc::mdc::insight::model::MDCfetsForex::MergeFrom(from.mdcfetsforex());
  }
  if (from.has_mdcfetsfxsnapshot()) {
    mutable_mdcfetsfxsnapshot()->::com::htsc::mdc::insight::model::MDCfetsFxSnapshot::MergeFrom(from.mdcfetsfxsnapshot());
  }
  if (from.has_mdcfetsfxquote()) {
    mutable_mdcfetsfxquote()->::com::htsc::mdc::insight::model::MDCfetsFxQuote::MergeFrom(from.mdcfetsfxquote());
  }
  if (from.has_spfuture()) {
    mutable_spfuture()->::com::htsc::mdc::insight::model::SPFuture::MergeFrom(from.spfuture());
  }
  if (from.has_mdcfetsbenchmark()) {
    mutable_mdcfetsbenchmark()->::com::htsc::mdc::insight::model::MDCfetsBenchmark::MergeFrom(from.mdcfetsbenchmark());
  }
  if (from.has_mdcfetsbonddeal()) {
    mutable_mdcfetsbonddeal()->::com::htsc::mdc::insight::model::MDCfetsBondDeal::MergeFrom(from.mdcfetsbonddeal());
  }
  if (from.has_mdcfetsbondsnapshot()) {
    mutable_mdcfetsbondsnapshot()->::com::htsc::mdc::insight::model::MDCfetsBondSnapshot::MergeFrom(from.mdcfetsbondsnapshot());
  }
  if (from.has_mdcfetscurrencydeal()) {
    mutable_mdcfetscurrencydeal()->::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal::MergeFrom(from.mdcfetscurrencydeal());
  }
  if (from.has_mdcfetscurrencysnapshot()) {
    mutable_mdcfetscurrencysnapshot()->::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot::MergeFrom(from.mdcfetscurrencysnapshot());
  }
  if (from.has_mdcfetsodmsnapshot()) {
    mutable_mdcfetsodmsnapshot()->::com::htsc::mdc::insight::model::MDCfetsODMSnapshot::MergeFrom(from.mdcfetsodmsnapshot());
  }
  if (from.has_mdcfetsqdmquote()) {
    mutable_mdcfetsqdmquote()->::com::htsc::mdc::insight::model::MDCfetsQDMQuote::MergeFrom(from.mdcfetsqdmquote());
  }
  if (from.has_mdcfetsratedeal()) {
    mutable_mdcfetsratedeal()->::com::htsc::mdc::insight::model::MDCfetsRateDeal::MergeFrom(from.mdcfetsratedeal());
  }
  if (from.has_mdcfetsratesnapshot()) {
    mutable_mdcfetsratesnapshot()->::com::htsc::mdc::insight::model::MDCfetsRateSnapshot::MergeFrom(from.mdcfetsratesnapshot());
  }
  if (from.has_mdcfetsfxcnymiddleprice()) {
    mutable_mdcfetsfxcnymiddleprice()->::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice::MergeFrom(from.mdcfetsfxcnymiddleprice());
  }
  if (from.has_mdiopvsnapshot()) {
    mutable_mdiopvsnapshot()->::com::htsc::mdc::insight::model::MDIopvSnapshot::MergeFrom(from.mdiopvsnapshot());
  }
  if (from.has_mdchinabondbenchmark()) {
    mutable_mdchinabondbenchmark()->::com::htsc::mdc::insight::model::MDChinaBondBenchmark::MergeFrom(from.mdchinabondbenchmark());
  }
  if (from.has_mdicetrace()) {
    mutable_mdicetrace()->::com::htsc::mdc::insight::model::MDIceTrace::MergeFrom(from.mdicetrace());
  }
}

void MarketData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MarketData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarketData::CopyFrom(const MarketData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MarketData)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MarketData::IsInitialized() const {

  return true;
}

void MarketData::Swap(MarketData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarketData::InternalSwap(MarketData* other) {
  std::swap(marketdatatype_, other->marketdatatype_);
  std::swap(messagechannelnumber_, other->messagechannelnumber_);
  std::swap(mdstock_, other->mdstock_);
  std::swap(mdindex_, other->mdindex_);
  std::swap(mdbond_, other->mdbond_);
  std::swap(mdfund_, other->mdfund_);
  std::swap(mdoption_, other->mdoption_);
  std::swap(mdfuture_, other->mdfuture_);
  std::swap(mdtransaction_, other->mdtransaction_);
  std::swap(mdorder_, other->mdorder_);
  std::swap(mdkline_, other->mdkline_);
  std::swap(mdtwap_, other->mdtwap_);
  std::swap(mdvwap_, other->mdvwap_);
  std::swap(mdconstant_, other->mdconstant_);
  std::swap(mdsimpletick_, other->mdsimpletick_);
  std::swap(mdupsdownsanalysis_, other->mdupsdownsanalysis_);
  std::swap(mdindicatorsranking_, other->mdindicatorsranking_);
  std::swap(dynamicpacket_, other->dynamicpacket_);
  std::swap(mdvolumebyprice_, other->mdvolumebyprice_);
  std::swap(mdfundflowanalysis_, other->mdfundflowanalysis_);
  std::swap(mdforex_, other->mdforex_);
  std::swap(mdspot_, other->mdspot_);
  std::swap(mdrate_, other->mdrate_);
  std::swap(orderbooksnapshot_, other->orderbooksnapshot_);
  std::swap(orderbooksnapshotwithtick_, other->orderbooksnapshotwithtick_);
  std::swap(mdquote_, other->mdquote_);
  std::swap(mdetfbasicinfo_, other->mdetfbasicinfo_);
  std::swap(mdfiquote_, other->mdfiquote_);
  std::swap(mdchipdistribution_, other->mdchipdistribution_);
  std::swap(mdwarrant_, other->mdwarrant_);
  std::swap(mdsecuritylending_, other->mdsecuritylending_);
  std::swap(mdnews_, other->mdnews_);
  std::swap(mdstaringresult_, other->mdstaringresult_);
  std::swap(mdderivedanalysis_, other->mdderivedanalysis_);
  std::swap(mdqbquote_, other->mdqbquote_);
  std::swap(mdqbtransaction_, other->mdqbtransaction_);
  std::swap(mdusaorder_, other->mdusaorder_);
  std::swap(mdusatransaction_, other->mdusatransaction_);
  std::swap(mdslorder_, other->mdslorder_);
  std::swap(mdsltransaction_, other->mdsltransaction_);
  std::swap(mdhkgreymarket_, other->mdhkgreymarket_);
  std::swap(mdslindicativequote_, other->mdslindicativequote_);
  std::swap(mdslstatistics_, other->mdslstatistics_);
  std::swap(mdusaquote_, other->mdusaquote_);
  std::swap(mdslestimation_, other->mdslestimation_);
  std::swap(mdcnexdeal_, other->mdcnexdeal_);
  std::swap(mdcnexquote_, other->mdcnexquote_);
  std::swap(mddelaysnapshot_, other->mddelaysnapshot_);
  std::swap(mdhighaccuracyfuture_, other->mdhighaccuracyfuture_);
  std::swap(mdcfetsforex_, other->mdcfetsforex_);
  std::swap(mdcfetsfxsnapshot_, other->mdcfetsfxsnapshot_);
  std::swap(mdcfetsfxquote_, other->mdcfetsfxquote_);
  std::swap(spfuture_, other->spfuture_);
  std::swap(mdcfetsbenchmark_, other->mdcfetsbenchmark_);
  std::swap(mdcfetsbonddeal_, other->mdcfetsbonddeal_);
  std::swap(mdcfetsbondsnapshot_, other->mdcfetsbondsnapshot_);
  std::swap(mdcfetscurrencydeal_, other->mdcfetscurrencydeal_);
  std::swap(mdcfetscurrencysnapshot_, other->mdcfetscurrencysnapshot_);
  std::swap(mdcfetsodmsnapshot_, other->mdcfetsodmsnapshot_);
  std::swap(mdcfetsqdmquote_, other->mdcfetsqdmquote_);
  std::swap(mdcfetsratedeal_, other->mdcfetsratedeal_);
  std::swap(mdcfetsratesnapshot_, other->mdcfetsratesnapshot_);
  std::swap(mdcfetsfxcnymiddleprice_, other->mdcfetsfxcnymiddleprice_);
  std::swap(mdiopvsnapshot_, other->mdiopvsnapshot_);
  std::swap(mdchinabondbenchmark_, other->mdchinabondbenchmark_);
  std::swap(mdicetrace_, other->mdicetrace_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarketData::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MarketData_descriptor_;
  metadata.reflection = MarketData_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketData

// optional .com.htsc.mdc.insight.model.EMarketDataType marketDataType = 1;
void MarketData::clear_marketdatatype() {
  marketdatatype_ = 0;
}
::com::htsc::mdc::insight::model::EMarketDataType MarketData::marketdatatype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.marketDataType)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatype_);
}
void MarketData::set_marketdatatype(::com::htsc::mdc::insight::model::EMarketDataType value) {
  
  marketdatatype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketData.marketDataType)
}

// optional int64 MessageChannelNumber = 2;
void MarketData::clear_messagechannelnumber() {
  messagechannelnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MarketData::messagechannelnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.MessageChannelNumber)
  return messagechannelnumber_;
}
void MarketData::set_messagechannelnumber(::google::protobuf::int64 value) {
  
  messagechannelnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketData.MessageChannelNumber)
}

// optional .com.htsc.mdc.insight.model.MDStock mdStock = 10;
bool MarketData::has_mdstock() const {
  return this != internal_default_instance() && mdstock_ != NULL;
}
void MarketData::clear_mdstock() {
  if (GetArenaNoVirtual() == NULL && mdstock_ != NULL) delete mdstock_;
  mdstock_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDStock& MarketData::mdstock() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdStock)
  return mdstock_ != NULL ? *mdstock_
                         : *::com::htsc::mdc::insight::model::MDStock::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDStock* MarketData::mutable_mdstock() {
  
  if (mdstock_ == NULL) {
    mdstock_ = new ::com::htsc::mdc::insight::model::MDStock;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdStock)
  return mdstock_;
}
::com::htsc::mdc::insight::model::MDStock* MarketData::release_mdstock() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdStock)
  
  ::com::htsc::mdc::insight::model::MDStock* temp = mdstock_;
  mdstock_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdstock(::com::htsc::mdc::insight::model::MDStock* mdstock) {
  delete mdstock_;
  mdstock_ = mdstock;
  if (mdstock) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdStock)
}

// optional .com.htsc.mdc.insight.model.MDIndex mdIndex = 11;
bool MarketData::has_mdindex() const {
  return this != internal_default_instance() && mdindex_ != NULL;
}
void MarketData::clear_mdindex() {
  if (GetArenaNoVirtual() == NULL && mdindex_ != NULL) delete mdindex_;
  mdindex_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDIndex& MarketData::mdindex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIndex)
  return mdindex_ != NULL ? *mdindex_
                         : *::com::htsc::mdc::insight::model::MDIndex::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDIndex* MarketData::mutable_mdindex() {
  
  if (mdindex_ == NULL) {
    mdindex_ = new ::com::htsc::mdc::insight::model::MDIndex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIndex)
  return mdindex_;
}
::com::htsc::mdc::insight::model::MDIndex* MarketData::release_mdindex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIndex)
  
  ::com::htsc::mdc::insight::model::MDIndex* temp = mdindex_;
  mdindex_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdindex(::com::htsc::mdc::insight::model::MDIndex* mdindex) {
  delete mdindex_;
  mdindex_ = mdindex;
  if (mdindex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIndex)
}

// optional .com.htsc.mdc.insight.model.MDBond mdBond = 12;
bool MarketData::has_mdbond() const {
  return this != internal_default_instance() && mdbond_ != NULL;
}
void MarketData::clear_mdbond() {
  if (GetArenaNoVirtual() == NULL && mdbond_ != NULL) delete mdbond_;
  mdbond_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDBond& MarketData::mdbond() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdBond)
  return mdbond_ != NULL ? *mdbond_
                         : *::com::htsc::mdc::insight::model::MDBond::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDBond* MarketData::mutable_mdbond() {
  
  if (mdbond_ == NULL) {
    mdbond_ = new ::com::htsc::mdc::insight::model::MDBond;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdBond)
  return mdbond_;
}
::com::htsc::mdc::insight::model::MDBond* MarketData::release_mdbond() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdBond)
  
  ::com::htsc::mdc::insight::model::MDBond* temp = mdbond_;
  mdbond_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdbond(::com::htsc::mdc::insight::model::MDBond* mdbond) {
  delete mdbond_;
  mdbond_ = mdbond;
  if (mdbond) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdBond)
}

// optional .com.htsc.mdc.insight.model.MDFund mdFund = 13;
bool MarketData::has_mdfund() const {
  return this != internal_default_instance() && mdfund_ != NULL;
}
void MarketData::clear_mdfund() {
  if (GetArenaNoVirtual() == NULL && mdfund_ != NULL) delete mdfund_;
  mdfund_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDFund& MarketData::mdfund() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFund)
  return mdfund_ != NULL ? *mdfund_
                         : *::com::htsc::mdc::insight::model::MDFund::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDFund* MarketData::mutable_mdfund() {
  
  if (mdfund_ == NULL) {
    mdfund_ = new ::com::htsc::mdc::insight::model::MDFund;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFund)
  return mdfund_;
}
::com::htsc::mdc::insight::model::MDFund* MarketData::release_mdfund() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFund)
  
  ::com::htsc::mdc::insight::model::MDFund* temp = mdfund_;
  mdfund_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdfund(::com::htsc::mdc::insight::model::MDFund* mdfund) {
  delete mdfund_;
  mdfund_ = mdfund;
  if (mdfund) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFund)
}

// optional .com.htsc.mdc.insight.model.MDOption mdOption = 14;
bool MarketData::has_mdoption() const {
  return this != internal_default_instance() && mdoption_ != NULL;
}
void MarketData::clear_mdoption() {
  if (GetArenaNoVirtual() == NULL && mdoption_ != NULL) delete mdoption_;
  mdoption_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDOption& MarketData::mdoption() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdOption)
  return mdoption_ != NULL ? *mdoption_
                         : *::com::htsc::mdc::insight::model::MDOption::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDOption* MarketData::mutable_mdoption() {
  
  if (mdoption_ == NULL) {
    mdoption_ = new ::com::htsc::mdc::insight::model::MDOption;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdOption)
  return mdoption_;
}
::com::htsc::mdc::insight::model::MDOption* MarketData::release_mdoption() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdOption)
  
  ::com::htsc::mdc::insight::model::MDOption* temp = mdoption_;
  mdoption_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdoption(::com::htsc::mdc::insight::model::MDOption* mdoption) {
  delete mdoption_;
  mdoption_ = mdoption;
  if (mdoption) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdOption)
}

// optional .com.htsc.mdc.insight.model.MDFuture mdFuture = 15;
bool MarketData::has_mdfuture() const {
  return this != internal_default_instance() && mdfuture_ != NULL;
}
void MarketData::clear_mdfuture() {
  if (GetArenaNoVirtual() == NULL && mdfuture_ != NULL) delete mdfuture_;
  mdfuture_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDFuture& MarketData::mdfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFuture)
  return mdfuture_ != NULL ? *mdfuture_
                         : *::com::htsc::mdc::insight::model::MDFuture::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDFuture* MarketData::mutable_mdfuture() {
  
  if (mdfuture_ == NULL) {
    mdfuture_ = new ::com::htsc::mdc::insight::model::MDFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFuture)
  return mdfuture_;
}
::com::htsc::mdc::insight::model::MDFuture* MarketData::release_mdfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFuture)
  
  ::com::htsc::mdc::insight::model::MDFuture* temp = mdfuture_;
  mdfuture_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdfuture(::com::htsc::mdc::insight::model::MDFuture* mdfuture) {
  delete mdfuture_;
  mdfuture_ = mdfuture;
  if (mdfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFuture)
}

// optional .com.htsc.mdc.insight.model.MDTransaction mdTransaction = 16;
bool MarketData::has_mdtransaction() const {
  return this != internal_default_instance() && mdtransaction_ != NULL;
}
void MarketData::clear_mdtransaction() {
  if (GetArenaNoVirtual() == NULL && mdtransaction_ != NULL) delete mdtransaction_;
  mdtransaction_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDTransaction& MarketData::mdtransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  return mdtransaction_ != NULL ? *mdtransaction_
                         : *::com::htsc::mdc::insight::model::MDTransaction::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDTransaction* MarketData::mutable_mdtransaction() {
  
  if (mdtransaction_ == NULL) {
    mdtransaction_ = new ::com::htsc::mdc::insight::model::MDTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  return mdtransaction_;
}
::com::htsc::mdc::insight::model::MDTransaction* MarketData::release_mdtransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdTransaction)
  
  ::com::htsc::mdc::insight::model::MDTransaction* temp = mdtransaction_;
  mdtransaction_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdtransaction(::com::htsc::mdc::insight::model::MDTransaction* mdtransaction) {
  delete mdtransaction_;
  mdtransaction_ = mdtransaction;
  if (mdtransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdTransaction)
}

// optional .com.htsc.mdc.insight.model.MDOrder mdOrder = 17;
bool MarketData::has_mdorder() const {
  return this != internal_default_instance() && mdorder_ != NULL;
}
void MarketData::clear_mdorder() {
  if (GetArenaNoVirtual() == NULL && mdorder_ != NULL) delete mdorder_;
  mdorder_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDOrder& MarketData::mdorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdOrder)
  return mdorder_ != NULL ? *mdorder_
                         : *::com::htsc::mdc::insight::model::MDOrder::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDOrder* MarketData::mutable_mdorder() {
  
  if (mdorder_ == NULL) {
    mdorder_ = new ::com::htsc::mdc::insight::model::MDOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdOrder)
  return mdorder_;
}
::com::htsc::mdc::insight::model::MDOrder* MarketData::release_mdorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdOrder)
  
  ::com::htsc::mdc::insight::model::MDOrder* temp = mdorder_;
  mdorder_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdorder(::com::htsc::mdc::insight::model::MDOrder* mdorder) {
  delete mdorder_;
  mdorder_ = mdorder;
  if (mdorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdOrder)
}

// optional .com.htsc.mdc.insight.model.ADKLine mdKLine = 18;
bool MarketData::has_mdkline() const {
  return this != internal_default_instance() && mdkline_ != NULL;
}
void MarketData::clear_mdkline() {
  if (GetArenaNoVirtual() == NULL && mdkline_ != NULL) delete mdkline_;
  mdkline_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADKLine& MarketData::mdkline() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdKLine)
  return mdkline_ != NULL ? *mdkline_
                         : *::com::htsc::mdc::insight::model::ADKLine::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADKLine* MarketData::mutable_mdkline() {
  
  if (mdkline_ == NULL) {
    mdkline_ = new ::com::htsc::mdc::insight::model::ADKLine;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdKLine)
  return mdkline_;
}
::com::htsc::mdc::insight::model::ADKLine* MarketData::release_mdkline() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdKLine)
  
  ::com::htsc::mdc::insight::model::ADKLine* temp = mdkline_;
  mdkline_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdkline(::com::htsc::mdc::insight::model::ADKLine* mdkline) {
  delete mdkline_;
  mdkline_ = mdkline;
  if (mdkline) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdKLine)
}

// optional .com.htsc.mdc.insight.model.ADTwap mdTwap = 19;
bool MarketData::has_mdtwap() const {
  return this != internal_default_instance() && mdtwap_ != NULL;
}
void MarketData::clear_mdtwap() {
  if (GetArenaNoVirtual() == NULL && mdtwap_ != NULL) delete mdtwap_;
  mdtwap_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADTwap& MarketData::mdtwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdTwap)
  return mdtwap_ != NULL ? *mdtwap_
                         : *::com::htsc::mdc::insight::model::ADTwap::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADTwap* MarketData::mutable_mdtwap() {
  
  if (mdtwap_ == NULL) {
    mdtwap_ = new ::com::htsc::mdc::insight::model::ADTwap;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdTwap)
  return mdtwap_;
}
::com::htsc::mdc::insight::model::ADTwap* MarketData::release_mdtwap() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdTwap)
  
  ::com::htsc::mdc::insight::model::ADTwap* temp = mdtwap_;
  mdtwap_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdtwap(::com::htsc::mdc::insight::model::ADTwap* mdtwap) {
  delete mdtwap_;
  mdtwap_ = mdtwap;
  if (mdtwap) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdTwap)
}

// optional .com.htsc.mdc.insight.model.ADVwap mdVwap = 20;
bool MarketData::has_mdvwap() const {
  return this != internal_default_instance() && mdvwap_ != NULL;
}
void MarketData::clear_mdvwap() {
  if (GetArenaNoVirtual() == NULL && mdvwap_ != NULL) delete mdvwap_;
  mdvwap_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADVwap& MarketData::mdvwap() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdVwap)
  return mdvwap_ != NULL ? *mdvwap_
                         : *::com::htsc::mdc::insight::model::ADVwap::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADVwap* MarketData::mutable_mdvwap() {
  
  if (mdvwap_ == NULL) {
    mdvwap_ = new ::com::htsc::mdc::insight::model::ADVwap;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdVwap)
  return mdvwap_;
}
::com::htsc::mdc::insight::model::ADVwap* MarketData::release_mdvwap() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdVwap)
  
  ::com::htsc::mdc::insight::model::ADVwap* temp = mdvwap_;
  mdvwap_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdvwap(::com::htsc::mdc::insight::model::ADVwap* mdvwap) {
  delete mdvwap_;
  mdvwap_ = mdvwap;
  if (mdvwap) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdVwap)
}

// optional .com.htsc.mdc.insight.model.MDBasicInfo mdConstant = 21;
bool MarketData::has_mdconstant() const {
  return this != internal_default_instance() && mdconstant_ != NULL;
}
void MarketData::clear_mdconstant() {
  if (GetArenaNoVirtual() == NULL && mdconstant_ != NULL) delete mdconstant_;
  mdconstant_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDBasicInfo& MarketData::mdconstant() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdConstant)
  return mdconstant_ != NULL ? *mdconstant_
                         : *::com::htsc::mdc::insight::model::MDBasicInfo::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDBasicInfo* MarketData::mutable_mdconstant() {
  
  if (mdconstant_ == NULL) {
    mdconstant_ = new ::com::htsc::mdc::insight::model::MDBasicInfo;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdConstant)
  return mdconstant_;
}
::com::htsc::mdc::insight::model::MDBasicInfo* MarketData::release_mdconstant() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdConstant)
  
  ::com::htsc::mdc::insight::model::MDBasicInfo* temp = mdconstant_;
  mdconstant_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdconstant(::com::htsc::mdc::insight::model::MDBasicInfo* mdconstant) {
  delete mdconstant_;
  mdconstant_ = mdconstant;
  if (mdconstant) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdConstant)
}

// optional .com.htsc.mdc.insight.model.MDSimpleTick mdSimpleTick = 22;
bool MarketData::has_mdsimpletick() const {
  return this != internal_default_instance() && mdsimpletick_ != NULL;
}
void MarketData::clear_mdsimpletick() {
  if (GetArenaNoVirtual() == NULL && mdsimpletick_ != NULL) delete mdsimpletick_;
  mdsimpletick_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSimpleTick& MarketData::mdsimpletick() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  return mdsimpletick_ != NULL ? *mdsimpletick_
                         : *::com::htsc::mdc::insight::model::MDSimpleTick::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSimpleTick* MarketData::mutable_mdsimpletick() {
  
  if (mdsimpletick_ == NULL) {
    mdsimpletick_ = new ::com::htsc::mdc::insight::model::MDSimpleTick;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  return mdsimpletick_;
}
::com::htsc::mdc::insight::model::MDSimpleTick* MarketData::release_mdsimpletick() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
  
  ::com::htsc::mdc::insight::model::MDSimpleTick* temp = mdsimpletick_;
  mdsimpletick_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdsimpletick(::com::htsc::mdc::insight::model::MDSimpleTick* mdsimpletick) {
  delete mdsimpletick_;
  mdsimpletick_ = mdsimpletick;
  if (mdsimpletick) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSimpleTick)
}

// optional .com.htsc.mdc.insight.model.ADUpsDownsAnalysis mdUpsDownsAnalysis = 23;
bool MarketData::has_mdupsdownsanalysis() const {
  return this != internal_default_instance() && mdupsdownsanalysis_ != NULL;
}
void MarketData::clear_mdupsdownsanalysis() {
  if (GetArenaNoVirtual() == NULL && mdupsdownsanalysis_ != NULL) delete mdupsdownsanalysis_;
  mdupsdownsanalysis_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis& MarketData::mdupsdownsanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  return mdupsdownsanalysis_ != NULL ? *mdupsdownsanalysis_
                         : *::com::htsc::mdc::insight::model::ADUpsDownsAnalysis::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* MarketData::mutable_mdupsdownsanalysis() {
  
  if (mdupsdownsanalysis_ == NULL) {
    mdupsdownsanalysis_ = new ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  return mdupsdownsanalysis_;
}
::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* MarketData::release_mdupsdownsanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
  
  ::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* temp = mdupsdownsanalysis_;
  mdupsdownsanalysis_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdupsdownsanalysis(::com::htsc::mdc::insight::model::ADUpsDownsAnalysis* mdupsdownsanalysis) {
  delete mdupsdownsanalysis_;
  mdupsdownsanalysis_ = mdupsdownsanalysis;
  if (mdupsdownsanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUpsDownsAnalysis)
}

// optional .com.htsc.mdc.insight.model.ADIndicatorsRanking mdIndicatorsRanking = 24;
bool MarketData::has_mdindicatorsranking() const {
  return this != internal_default_instance() && mdindicatorsranking_ != NULL;
}
void MarketData::clear_mdindicatorsranking() {
  if (GetArenaNoVirtual() == NULL && mdindicatorsranking_ != NULL) delete mdindicatorsranking_;
  mdindicatorsranking_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADIndicatorsRanking& MarketData::mdindicatorsranking() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  return mdindicatorsranking_ != NULL ? *mdindicatorsranking_
                         : *::com::htsc::mdc::insight::model::ADIndicatorsRanking::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADIndicatorsRanking* MarketData::mutable_mdindicatorsranking() {
  
  if (mdindicatorsranking_ == NULL) {
    mdindicatorsranking_ = new ::com::htsc::mdc::insight::model::ADIndicatorsRanking;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  return mdindicatorsranking_;
}
::com::htsc::mdc::insight::model::ADIndicatorsRanking* MarketData::release_mdindicatorsranking() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
  
  ::com::htsc::mdc::insight::model::ADIndicatorsRanking* temp = mdindicatorsranking_;
  mdindicatorsranking_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdindicatorsranking(::com::htsc::mdc::insight::model::ADIndicatorsRanking* mdindicatorsranking) {
  delete mdindicatorsranking_;
  mdindicatorsranking_ = mdindicatorsranking;
  if (mdindicatorsranking) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIndicatorsRanking)
}

// optional .com.htsc.mdc.insight.model.DynamicPacket dynamicPacket = 25;
bool MarketData::has_dynamicpacket() const {
  return this != internal_default_instance() && dynamicpacket_ != NULL;
}
void MarketData::clear_dynamicpacket() {
  if (GetArenaNoVirtual() == NULL && dynamicpacket_ != NULL) delete dynamicpacket_;
  dynamicpacket_ = NULL;
}
const ::com::htsc::mdc::insight::model::DynamicPacket& MarketData::dynamicpacket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  return dynamicpacket_ != NULL ? *dynamicpacket_
                         : *::com::htsc::mdc::insight::model::DynamicPacket::internal_default_instance();
}
::com::htsc::mdc::insight::model::DynamicPacket* MarketData::mutable_dynamicpacket() {
  
  if (dynamicpacket_ == NULL) {
    dynamicpacket_ = new ::com::htsc::mdc::insight::model::DynamicPacket;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  return dynamicpacket_;
}
::com::htsc::mdc::insight::model::DynamicPacket* MarketData::release_dynamicpacket() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
  
  ::com::htsc::mdc::insight::model::DynamicPacket* temp = dynamicpacket_;
  dynamicpacket_ = NULL;
  return temp;
}
void MarketData::set_allocated_dynamicpacket(::com::htsc::mdc::insight::model::DynamicPacket* dynamicpacket) {
  delete dynamicpacket_;
  dynamicpacket_ = dynamicpacket;
  if (dynamicpacket) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.dynamicPacket)
}

// optional .com.htsc.mdc.insight.model.ADVolumeByPrice mdVolumeByPrice = 26;
bool MarketData::has_mdvolumebyprice() const {
  return this != internal_default_instance() && mdvolumebyprice_ != NULL;
}
void MarketData::clear_mdvolumebyprice() {
  if (GetArenaNoVirtual() == NULL && mdvolumebyprice_ != NULL) delete mdvolumebyprice_;
  mdvolumebyprice_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADVolumeByPrice& MarketData::mdvolumebyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  return mdvolumebyprice_ != NULL ? *mdvolumebyprice_
                         : *::com::htsc::mdc::insight::model::ADVolumeByPrice::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADVolumeByPrice* MarketData::mutable_mdvolumebyprice() {
  
  if (mdvolumebyprice_ == NULL) {
    mdvolumebyprice_ = new ::com::htsc::mdc::insight::model::ADVolumeByPrice;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  return mdvolumebyprice_;
}
::com::htsc::mdc::insight::model::ADVolumeByPrice* MarketData::release_mdvolumebyprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
  
  ::com::htsc::mdc::insight::model::ADVolumeByPrice* temp = mdvolumebyprice_;
  mdvolumebyprice_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdvolumebyprice(::com::htsc::mdc::insight::model::ADVolumeByPrice* mdvolumebyprice) {
  delete mdvolumebyprice_;
  mdvolumebyprice_ = mdvolumebyprice;
  if (mdvolumebyprice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdVolumeByPrice)
}

// optional .com.htsc.mdc.insight.model.ADFundFlowAnalysis mdFundFlowAnalysis = 27;
bool MarketData::has_mdfundflowanalysis() const {
  return this != internal_default_instance() && mdfundflowanalysis_ != NULL;
}
void MarketData::clear_mdfundflowanalysis() {
  if (GetArenaNoVirtual() == NULL && mdfundflowanalysis_ != NULL) delete mdfundflowanalysis_;
  mdfundflowanalysis_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADFundFlowAnalysis& MarketData::mdfundflowanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  return mdfundflowanalysis_ != NULL ? *mdfundflowanalysis_
                         : *::com::htsc::mdc::insight::model::ADFundFlowAnalysis::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADFundFlowAnalysis* MarketData::mutable_mdfundflowanalysis() {
  
  if (mdfundflowanalysis_ == NULL) {
    mdfundflowanalysis_ = new ::com::htsc::mdc::insight::model::ADFundFlowAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  return mdfundflowanalysis_;
}
::com::htsc::mdc::insight::model::ADFundFlowAnalysis* MarketData::release_mdfundflowanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
  
  ::com::htsc::mdc::insight::model::ADFundFlowAnalysis* temp = mdfundflowanalysis_;
  mdfundflowanalysis_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdfundflowanalysis(::com::htsc::mdc::insight::model::ADFundFlowAnalysis* mdfundflowanalysis) {
  delete mdfundflowanalysis_;
  mdfundflowanalysis_ = mdfundflowanalysis;
  if (mdfundflowanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFundFlowAnalysis)
}

// optional .com.htsc.mdc.insight.model.MDForex mdForex = 28;
bool MarketData::has_mdforex() const {
  return this != internal_default_instance() && mdforex_ != NULL;
}
void MarketData::clear_mdforex() {
  if (GetArenaNoVirtual() == NULL && mdforex_ != NULL) delete mdforex_;
  mdforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDForex& MarketData::mdforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdForex)
  return mdforex_ != NULL ? *mdforex_
                         : *::com::htsc::mdc::insight::model::MDForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDForex* MarketData::mutable_mdforex() {
  
  if (mdforex_ == NULL) {
    mdforex_ = new ::com::htsc::mdc::insight::model::MDForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdForex)
  return mdforex_;
}
::com::htsc::mdc::insight::model::MDForex* MarketData::release_mdforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdForex)
  
  ::com::htsc::mdc::insight::model::MDForex* temp = mdforex_;
  mdforex_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdforex(::com::htsc::mdc::insight::model::MDForex* mdforex) {
  delete mdforex_;
  mdforex_ = mdforex;
  if (mdforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdForex)
}

// optional .com.htsc.mdc.insight.model.MDSpot mdSpot = 29;
bool MarketData::has_mdspot() const {
  return this != internal_default_instance() && mdspot_ != NULL;
}
void MarketData::clear_mdspot() {
  if (GetArenaNoVirtual() == NULL && mdspot_ != NULL) delete mdspot_;
  mdspot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSpot& MarketData::mdspot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSpot)
  return mdspot_ != NULL ? *mdspot_
                         : *::com::htsc::mdc::insight::model::MDSpot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSpot* MarketData::mutable_mdspot() {
  
  if (mdspot_ == NULL) {
    mdspot_ = new ::com::htsc::mdc::insight::model::MDSpot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSpot)
  return mdspot_;
}
::com::htsc::mdc::insight::model::MDSpot* MarketData::release_mdspot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSpot)
  
  ::com::htsc::mdc::insight::model::MDSpot* temp = mdspot_;
  mdspot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdspot(::com::htsc::mdc::insight::model::MDSpot* mdspot) {
  delete mdspot_;
  mdspot_ = mdspot;
  if (mdspot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSpot)
}

// optional .com.htsc.mdc.insight.model.MDRate mdRate = 30;
bool MarketData::has_mdrate() const {
  return this != internal_default_instance() && mdrate_ != NULL;
}
void MarketData::clear_mdrate() {
  if (GetArenaNoVirtual() == NULL && mdrate_ != NULL) delete mdrate_;
  mdrate_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDRate& MarketData::mdrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdRate)
  return mdrate_ != NULL ? *mdrate_
                         : *::com::htsc::mdc::insight::model::MDRate::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDRate* MarketData::mutable_mdrate() {
  
  if (mdrate_ == NULL) {
    mdrate_ = new ::com::htsc::mdc::insight::model::MDRate;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdRate)
  return mdrate_;
}
::com::htsc::mdc::insight::model::MDRate* MarketData::release_mdrate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdRate)
  
  ::com::htsc::mdc::insight::model::MDRate* temp = mdrate_;
  mdrate_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdrate(::com::htsc::mdc::insight::model::MDRate* mdrate) {
  delete mdrate_;
  mdrate_ = mdrate;
  if (mdrate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdRate)
}

// optional .com.htsc.mdc.insight.model.ADOrderbookSnapshot orderbookSnapshot = 31;
bool MarketData::has_orderbooksnapshot() const {
  return this != internal_default_instance() && orderbooksnapshot_ != NULL;
}
void MarketData::clear_orderbooksnapshot() {
  if (GetArenaNoVirtual() == NULL && orderbooksnapshot_ != NULL) delete orderbooksnapshot_;
  orderbooksnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADOrderbookSnapshot& MarketData::orderbooksnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  return orderbooksnapshot_ != NULL ? *orderbooksnapshot_
                         : *::com::htsc::mdc::insight::model::ADOrderbookSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADOrderbookSnapshot* MarketData::mutable_orderbooksnapshot() {
  
  if (orderbooksnapshot_ == NULL) {
    orderbooksnapshot_ = new ::com::htsc::mdc::insight::model::ADOrderbookSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  return orderbooksnapshot_;
}
::com::htsc::mdc::insight::model::ADOrderbookSnapshot* MarketData::release_orderbooksnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
  
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshot* temp = orderbooksnapshot_;
  orderbooksnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_orderbooksnapshot(::com::htsc::mdc::insight::model::ADOrderbookSnapshot* orderbooksnapshot) {
  delete orderbooksnapshot_;
  orderbooksnapshot_ = orderbooksnapshot;
  if (orderbooksnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.orderbookSnapshot)
}

// optional .com.htsc.mdc.insight.model.ADOrderbookSnapshotWithTick orderbookSnapshotWithTick = 32;
bool MarketData::has_orderbooksnapshotwithtick() const {
  return this != internal_default_instance() && orderbooksnapshotwithtick_ != NULL;
}
void MarketData::clear_orderbooksnapshotwithtick() {
  if (GetArenaNoVirtual() == NULL && orderbooksnapshotwithtick_ != NULL) delete orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick& MarketData::orderbooksnapshotwithtick() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  return orderbooksnapshotwithtick_ != NULL ? *orderbooksnapshotwithtick_
                         : *::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* MarketData::mutable_orderbooksnapshotwithtick() {
  
  if (orderbooksnapshotwithtick_ == NULL) {
    orderbooksnapshotwithtick_ = new ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  return orderbooksnapshotwithtick_;
}
::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* MarketData::release_orderbooksnapshotwithtick() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
  
  ::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* temp = orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = NULL;
  return temp;
}
void MarketData::set_allocated_orderbooksnapshotwithtick(::com::htsc::mdc::insight::model::ADOrderbookSnapshotWithTick* orderbooksnapshotwithtick) {
  delete orderbooksnapshotwithtick_;
  orderbooksnapshotwithtick_ = orderbooksnapshotwithtick;
  if (orderbooksnapshotwithtick) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.orderbookSnapshotWithTick)
}

// optional .com.htsc.mdc.insight.model.MDQuote mdQuote = 33;
bool MarketData::has_mdquote() const {
  return this != internal_default_instance() && mdquote_ != NULL;
}
void MarketData::clear_mdquote() {
  if (GetArenaNoVirtual() == NULL && mdquote_ != NULL) delete mdquote_;
  mdquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDQuote& MarketData::mdquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQuote)
  return mdquote_ != NULL ? *mdquote_
                         : *::com::htsc::mdc::insight::model::MDQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDQuote* MarketData::mutable_mdquote() {
  
  if (mdquote_ == NULL) {
    mdquote_ = new ::com::htsc::mdc::insight::model::MDQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQuote)
  return mdquote_;
}
::com::htsc::mdc::insight::model::MDQuote* MarketData::release_mdquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQuote)
  
  ::com::htsc::mdc::insight::model::MDQuote* temp = mdquote_;
  mdquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdquote(::com::htsc::mdc::insight::model::MDQuote* mdquote) {
  delete mdquote_;
  mdquote_ = mdquote;
  if (mdquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQuote)
}

// optional .com.htsc.mdc.insight.model.MDETFBasicInfo mdETFBasicInfo = 34;
bool MarketData::has_mdetfbasicinfo() const {
  return this != internal_default_instance() && mdetfbasicinfo_ != NULL;
}
void MarketData::clear_mdetfbasicinfo() {
  if (GetArenaNoVirtual() == NULL && mdetfbasicinfo_ != NULL) delete mdetfbasicinfo_;
  mdetfbasicinfo_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDETFBasicInfo& MarketData::mdetfbasicinfo() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  return mdetfbasicinfo_ != NULL ? *mdetfbasicinfo_
                         : *::com::htsc::mdc::insight::model::MDETFBasicInfo::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDETFBasicInfo* MarketData::mutable_mdetfbasicinfo() {
  
  if (mdetfbasicinfo_ == NULL) {
    mdetfbasicinfo_ = new ::com::htsc::mdc::insight::model::MDETFBasicInfo;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  return mdetfbasicinfo_;
}
::com::htsc::mdc::insight::model::MDETFBasicInfo* MarketData::release_mdetfbasicinfo() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
  
  ::com::htsc::mdc::insight::model::MDETFBasicInfo* temp = mdetfbasicinfo_;
  mdetfbasicinfo_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdetfbasicinfo(::com::htsc::mdc::insight::model::MDETFBasicInfo* mdetfbasicinfo) {
  delete mdetfbasicinfo_;
  mdetfbasicinfo_ = mdetfbasicinfo;
  if (mdetfbasicinfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdETFBasicInfo)
}

// optional .com.htsc.mdc.insight.model.MDFIQuote mdFIQuote = 35;
bool MarketData::has_mdfiquote() const {
  return this != internal_default_instance() && mdfiquote_ != NULL;
}
void MarketData::clear_mdfiquote() {
  if (GetArenaNoVirtual() == NULL && mdfiquote_ != NULL) delete mdfiquote_;
  mdfiquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDFIQuote& MarketData::mdfiquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  return mdfiquote_ != NULL ? *mdfiquote_
                         : *::com::htsc::mdc::insight::model::MDFIQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDFIQuote* MarketData::mutable_mdfiquote() {
  
  if (mdfiquote_ == NULL) {
    mdfiquote_ = new ::com::htsc::mdc::insight::model::MDFIQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  return mdfiquote_;
}
::com::htsc::mdc::insight::model::MDFIQuote* MarketData::release_mdfiquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
  
  ::com::htsc::mdc::insight::model::MDFIQuote* temp = mdfiquote_;
  mdfiquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdfiquote(::com::htsc::mdc::insight::model::MDFIQuote* mdfiquote) {
  delete mdfiquote_;
  mdfiquote_ = mdfiquote;
  if (mdfiquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdFIQuote)
}

// optional .com.htsc.mdc.insight.model.ADChipDistribution mdChipDistribution = 36;
bool MarketData::has_mdchipdistribution() const {
  return this != internal_default_instance() && mdchipdistribution_ != NULL;
}
void MarketData::clear_mdchipdistribution() {
  if (GetArenaNoVirtual() == NULL && mdchipdistribution_ != NULL) delete mdchipdistribution_;
  mdchipdistribution_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADChipDistribution& MarketData::mdchipdistribution() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  return mdchipdistribution_ != NULL ? *mdchipdistribution_
                         : *::com::htsc::mdc::insight::model::ADChipDistribution::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADChipDistribution* MarketData::mutable_mdchipdistribution() {
  
  if (mdchipdistribution_ == NULL) {
    mdchipdistribution_ = new ::com::htsc::mdc::insight::model::ADChipDistribution;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  return mdchipdistribution_;
}
::com::htsc::mdc::insight::model::ADChipDistribution* MarketData::release_mdchipdistribution() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
  
  ::com::htsc::mdc::insight::model::ADChipDistribution* temp = mdchipdistribution_;
  mdchipdistribution_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdchipdistribution(::com::htsc::mdc::insight::model::ADChipDistribution* mdchipdistribution) {
  delete mdchipdistribution_;
  mdchipdistribution_ = mdchipdistribution;
  if (mdchipdistribution) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdChipDistribution)
}

// optional .com.htsc.mdc.insight.model.MDWarrant mdWarrant = 37;
bool MarketData::has_mdwarrant() const {
  return this != internal_default_instance() && mdwarrant_ != NULL;
}
void MarketData::clear_mdwarrant() {
  if (GetArenaNoVirtual() == NULL && mdwarrant_ != NULL) delete mdwarrant_;
  mdwarrant_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDWarrant& MarketData::mdwarrant() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  return mdwarrant_ != NULL ? *mdwarrant_
                         : *::com::htsc::mdc::insight::model::MDWarrant::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDWarrant* MarketData::mutable_mdwarrant() {
  
  if (mdwarrant_ == NULL) {
    mdwarrant_ = new ::com::htsc::mdc::insight::model::MDWarrant;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  return mdwarrant_;
}
::com::htsc::mdc::insight::model::MDWarrant* MarketData::release_mdwarrant() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdWarrant)
  
  ::com::htsc::mdc::insight::model::MDWarrant* temp = mdwarrant_;
  mdwarrant_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdwarrant(::com::htsc::mdc::insight::model::MDWarrant* mdwarrant) {
  delete mdwarrant_;
  mdwarrant_ = mdwarrant;
  if (mdwarrant) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdWarrant)
}

// optional .com.htsc.mdc.insight.model.MDSecurityLending mdSecurityLending = 38;
bool MarketData::has_mdsecuritylending() const {
  return this != internal_default_instance() && mdsecuritylending_ != NULL;
}
void MarketData::clear_mdsecuritylending() {
  if (GetArenaNoVirtual() == NULL && mdsecuritylending_ != NULL) delete mdsecuritylending_;
  mdsecuritylending_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSecurityLending& MarketData::mdsecuritylending() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  return mdsecuritylending_ != NULL ? *mdsecuritylending_
                         : *::com::htsc::mdc::insight::model::MDSecurityLending::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSecurityLending* MarketData::mutable_mdsecuritylending() {
  
  if (mdsecuritylending_ == NULL) {
    mdsecuritylending_ = new ::com::htsc::mdc::insight::model::MDSecurityLending;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  return mdsecuritylending_;
}
::com::htsc::mdc::insight::model::MDSecurityLending* MarketData::release_mdsecuritylending() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
  
  ::com::htsc::mdc::insight::model::MDSecurityLending* temp = mdsecuritylending_;
  mdsecuritylending_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdsecuritylending(::com::htsc::mdc::insight::model::MDSecurityLending* mdsecuritylending) {
  delete mdsecuritylending_;
  mdsecuritylending_ = mdsecuritylending;
  if (mdsecuritylending) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSecurityLending)
}

// optional .com.htsc.mdc.insight.model.ADNews mdNews = 39;
bool MarketData::has_mdnews() const {
  return this != internal_default_instance() && mdnews_ != NULL;
}
void MarketData::clear_mdnews() {
  if (GetArenaNoVirtual() == NULL && mdnews_ != NULL) delete mdnews_;
  mdnews_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADNews& MarketData::mdnews() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdNews)
  return mdnews_ != NULL ? *mdnews_
                         : *::com::htsc::mdc::insight::model::ADNews::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADNews* MarketData::mutable_mdnews() {
  
  if (mdnews_ == NULL) {
    mdnews_ = new ::com::htsc::mdc::insight::model::ADNews;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdNews)
  return mdnews_;
}
::com::htsc::mdc::insight::model::ADNews* MarketData::release_mdnews() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdNews)
  
  ::com::htsc::mdc::insight::model::ADNews* temp = mdnews_;
  mdnews_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdnews(::com::htsc::mdc::insight::model::ADNews* mdnews) {
  delete mdnews_;
  mdnews_ = mdnews;
  if (mdnews) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdNews)
}

// optional .com.htsc.mdc.insight.model.ADStaringResult mdStaringResult = 40;
bool MarketData::has_mdstaringresult() const {
  return this != internal_default_instance() && mdstaringresult_ != NULL;
}
void MarketData::clear_mdstaringresult() {
  if (GetArenaNoVirtual() == NULL && mdstaringresult_ != NULL) delete mdstaringresult_;
  mdstaringresult_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADStaringResult& MarketData::mdstaringresult() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  return mdstaringresult_ != NULL ? *mdstaringresult_
                         : *::com::htsc::mdc::insight::model::ADStaringResult::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADStaringResult* MarketData::mutable_mdstaringresult() {
  
  if (mdstaringresult_ == NULL) {
    mdstaringresult_ = new ::com::htsc::mdc::insight::model::ADStaringResult;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  return mdstaringresult_;
}
::com::htsc::mdc::insight::model::ADStaringResult* MarketData::release_mdstaringresult() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
  
  ::com::htsc::mdc::insight::model::ADStaringResult* temp = mdstaringresult_;
  mdstaringresult_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdstaringresult(::com::htsc::mdc::insight::model::ADStaringResult* mdstaringresult) {
  delete mdstaringresult_;
  mdstaringresult_ = mdstaringresult;
  if (mdstaringresult) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdStaringResult)
}

// optional .com.htsc.mdc.insight.model.ADDerivedAnalysis mdDerivedAnalysis = 41;
bool MarketData::has_mdderivedanalysis() const {
  return this != internal_default_instance() && mdderivedanalysis_ != NULL;
}
void MarketData::clear_mdderivedanalysis() {
  if (GetArenaNoVirtual() == NULL && mdderivedanalysis_ != NULL) delete mdderivedanalysis_;
  mdderivedanalysis_ = NULL;
}
const ::com::htsc::mdc::insight::model::ADDerivedAnalysis& MarketData::mdderivedanalysis() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  return mdderivedanalysis_ != NULL ? *mdderivedanalysis_
                         : *::com::htsc::mdc::insight::model::ADDerivedAnalysis::internal_default_instance();
}
::com::htsc::mdc::insight::model::ADDerivedAnalysis* MarketData::mutable_mdderivedanalysis() {
  
  if (mdderivedanalysis_ == NULL) {
    mdderivedanalysis_ = new ::com::htsc::mdc::insight::model::ADDerivedAnalysis;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  return mdderivedanalysis_;
}
::com::htsc::mdc::insight::model::ADDerivedAnalysis* MarketData::release_mdderivedanalysis() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
  
  ::com::htsc::mdc::insight::model::ADDerivedAnalysis* temp = mdderivedanalysis_;
  mdderivedanalysis_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdderivedanalysis(::com::htsc::mdc::insight::model::ADDerivedAnalysis* mdderivedanalysis) {
  delete mdderivedanalysis_;
  mdderivedanalysis_ = mdderivedanalysis;
  if (mdderivedanalysis) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdDerivedAnalysis)
}

// optional .com.htsc.mdc.insight.model.MDQBQuote mdQBQuote = 42;
bool MarketData::has_mdqbquote() const {
  return this != internal_default_instance() && mdqbquote_ != NULL;
}
void MarketData::clear_mdqbquote() {
  if (GetArenaNoVirtual() == NULL && mdqbquote_ != NULL) delete mdqbquote_;
  mdqbquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDQBQuote& MarketData::mdqbquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  return mdqbquote_ != NULL ? *mdqbquote_
                         : *::com::htsc::mdc::insight::model::MDQBQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDQBQuote* MarketData::mutable_mdqbquote() {
  
  if (mdqbquote_ == NULL) {
    mdqbquote_ = new ::com::htsc::mdc::insight::model::MDQBQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  return mdqbquote_;
}
::com::htsc::mdc::insight::model::MDQBQuote* MarketData::release_mdqbquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
  
  ::com::htsc::mdc::insight::model::MDQBQuote* temp = mdqbquote_;
  mdqbquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdqbquote(::com::htsc::mdc::insight::model::MDQBQuote* mdqbquote) {
  delete mdqbquote_;
  mdqbquote_ = mdqbquote;
  if (mdqbquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQBQuote)
}

// optional .com.htsc.mdc.insight.model.MDQBTransaction mdQBTransaction = 43;
bool MarketData::has_mdqbtransaction() const {
  return this != internal_default_instance() && mdqbtransaction_ != NULL;
}
void MarketData::clear_mdqbtransaction() {
  if (GetArenaNoVirtual() == NULL && mdqbtransaction_ != NULL) delete mdqbtransaction_;
  mdqbtransaction_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDQBTransaction& MarketData::mdqbtransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  return mdqbtransaction_ != NULL ? *mdqbtransaction_
                         : *::com::htsc::mdc::insight::model::MDQBTransaction::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDQBTransaction* MarketData::mutable_mdqbtransaction() {
  
  if (mdqbtransaction_ == NULL) {
    mdqbtransaction_ = new ::com::htsc::mdc::insight::model::MDQBTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  return mdqbtransaction_;
}
::com::htsc::mdc::insight::model::MDQBTransaction* MarketData::release_mdqbtransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
  
  ::com::htsc::mdc::insight::model::MDQBTransaction* temp = mdqbtransaction_;
  mdqbtransaction_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdqbtransaction(::com::htsc::mdc::insight::model::MDQBTransaction* mdqbtransaction) {
  delete mdqbtransaction_;
  mdqbtransaction_ = mdqbtransaction;
  if (mdqbtransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdQBTransaction)
}

// optional .com.htsc.mdc.insight.model.MDUSAOrder mdUSAOrder = 44;
bool MarketData::has_mdusaorder() const {
  return this != internal_default_instance() && mdusaorder_ != NULL;
}
void MarketData::clear_mdusaorder() {
  if (GetArenaNoVirtual() == NULL && mdusaorder_ != NULL) delete mdusaorder_;
  mdusaorder_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDUSAOrder& MarketData::mdusaorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  return mdusaorder_ != NULL ? *mdusaorder_
                         : *::com::htsc::mdc::insight::model::MDUSAOrder::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDUSAOrder* MarketData::mutable_mdusaorder() {
  
  if (mdusaorder_ == NULL) {
    mdusaorder_ = new ::com::htsc::mdc::insight::model::MDUSAOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  return mdusaorder_;
}
::com::htsc::mdc::insight::model::MDUSAOrder* MarketData::release_mdusaorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
  
  ::com::htsc::mdc::insight::model::MDUSAOrder* temp = mdusaorder_;
  mdusaorder_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdusaorder(::com::htsc::mdc::insight::model::MDUSAOrder* mdusaorder) {
  delete mdusaorder_;
  mdusaorder_ = mdusaorder;
  if (mdusaorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSAOrder)
}

// optional .com.htsc.mdc.insight.model.MDUSATransaction mdUSATransaction = 45;
bool MarketData::has_mdusatransaction() const {
  return this != internal_default_instance() && mdusatransaction_ != NULL;
}
void MarketData::clear_mdusatransaction() {
  if (GetArenaNoVirtual() == NULL && mdusatransaction_ != NULL) delete mdusatransaction_;
  mdusatransaction_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDUSATransaction& MarketData::mdusatransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  return mdusatransaction_ != NULL ? *mdusatransaction_
                         : *::com::htsc::mdc::insight::model::MDUSATransaction::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDUSATransaction* MarketData::mutable_mdusatransaction() {
  
  if (mdusatransaction_ == NULL) {
    mdusatransaction_ = new ::com::htsc::mdc::insight::model::MDUSATransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  return mdusatransaction_;
}
::com::htsc::mdc::insight::model::MDUSATransaction* MarketData::release_mdusatransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
  
  ::com::htsc::mdc::insight::model::MDUSATransaction* temp = mdusatransaction_;
  mdusatransaction_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdusatransaction(::com::htsc::mdc::insight::model::MDUSATransaction* mdusatransaction) {
  delete mdusatransaction_;
  mdusatransaction_ = mdusatransaction;
  if (mdusatransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSATransaction)
}

// optional .com.htsc.mdc.insight.model.MDSLOrder mdSLOrder = 46;
bool MarketData::has_mdslorder() const {
  return this != internal_default_instance() && mdslorder_ != NULL;
}
void MarketData::clear_mdslorder() {
  if (GetArenaNoVirtual() == NULL && mdslorder_ != NULL) delete mdslorder_;
  mdslorder_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSLOrder& MarketData::mdslorder() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  return mdslorder_ != NULL ? *mdslorder_
                         : *::com::htsc::mdc::insight::model::MDSLOrder::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSLOrder* MarketData::mutable_mdslorder() {
  
  if (mdslorder_ == NULL) {
    mdslorder_ = new ::com::htsc::mdc::insight::model::MDSLOrder;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  return mdslorder_;
}
::com::htsc::mdc::insight::model::MDSLOrder* MarketData::release_mdslorder() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
  
  ::com::htsc::mdc::insight::model::MDSLOrder* temp = mdslorder_;
  mdslorder_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdslorder(::com::htsc::mdc::insight::model::MDSLOrder* mdslorder) {
  delete mdslorder_;
  mdslorder_ = mdslorder;
  if (mdslorder) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLOrder)
}

// optional .com.htsc.mdc.insight.model.MDSLTransaction mdSLTransaction = 47;
bool MarketData::has_mdsltransaction() const {
  return this != internal_default_instance() && mdsltransaction_ != NULL;
}
void MarketData::clear_mdsltransaction() {
  if (GetArenaNoVirtual() == NULL && mdsltransaction_ != NULL) delete mdsltransaction_;
  mdsltransaction_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSLTransaction& MarketData::mdsltransaction() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  return mdsltransaction_ != NULL ? *mdsltransaction_
                         : *::com::htsc::mdc::insight::model::MDSLTransaction::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSLTransaction* MarketData::mutable_mdsltransaction() {
  
  if (mdsltransaction_ == NULL) {
    mdsltransaction_ = new ::com::htsc::mdc::insight::model::MDSLTransaction;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  return mdsltransaction_;
}
::com::htsc::mdc::insight::model::MDSLTransaction* MarketData::release_mdsltransaction() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
  
  ::com::htsc::mdc::insight::model::MDSLTransaction* temp = mdsltransaction_;
  mdsltransaction_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdsltransaction(::com::htsc::mdc::insight::model::MDSLTransaction* mdsltransaction) {
  delete mdsltransaction_;
  mdsltransaction_ = mdsltransaction;
  if (mdsltransaction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLTransaction)
}

// optional .com.htsc.mdc.insight.model.MDHKGreyMarket mdHKGreyMarket = 48;
bool MarketData::has_mdhkgreymarket() const {
  return this != internal_default_instance() && mdhkgreymarket_ != NULL;
}
void MarketData::clear_mdhkgreymarket() {
  if (GetArenaNoVirtual() == NULL && mdhkgreymarket_ != NULL) delete mdhkgreymarket_;
  mdhkgreymarket_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDHKGreyMarket& MarketData::mdhkgreymarket() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  return mdhkgreymarket_ != NULL ? *mdhkgreymarket_
                         : *::com::htsc::mdc::insight::model::MDHKGreyMarket::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDHKGreyMarket* MarketData::mutable_mdhkgreymarket() {
  
  if (mdhkgreymarket_ == NULL) {
    mdhkgreymarket_ = new ::com::htsc::mdc::insight::model::MDHKGreyMarket;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  return mdhkgreymarket_;
}
::com::htsc::mdc::insight::model::MDHKGreyMarket* MarketData::release_mdhkgreymarket() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
  
  ::com::htsc::mdc::insight::model::MDHKGreyMarket* temp = mdhkgreymarket_;
  mdhkgreymarket_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdhkgreymarket(::com::htsc::mdc::insight::model::MDHKGreyMarket* mdhkgreymarket) {
  delete mdhkgreymarket_;
  mdhkgreymarket_ = mdhkgreymarket;
  if (mdhkgreymarket) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdHKGreyMarket)
}

// optional .com.htsc.mdc.insight.model.MDSLIndicativeQuote mdSLIndicativeQuote = 49;
bool MarketData::has_mdslindicativequote() const {
  return this != internal_default_instance() && mdslindicativequote_ != NULL;
}
void MarketData::clear_mdslindicativequote() {
  if (GetArenaNoVirtual() == NULL && mdslindicativequote_ != NULL) delete mdslindicativequote_;
  mdslindicativequote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSLIndicativeQuote& MarketData::mdslindicativequote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  return mdslindicativequote_ != NULL ? *mdslindicativequote_
                         : *::com::htsc::mdc::insight::model::MDSLIndicativeQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSLIndicativeQuote* MarketData::mutable_mdslindicativequote() {
  
  if (mdslindicativequote_ == NULL) {
    mdslindicativequote_ = new ::com::htsc::mdc::insight::model::MDSLIndicativeQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  return mdslindicativequote_;
}
::com::htsc::mdc::insight::model::MDSLIndicativeQuote* MarketData::release_mdslindicativequote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
  
  ::com::htsc::mdc::insight::model::MDSLIndicativeQuote* temp = mdslindicativequote_;
  mdslindicativequote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdslindicativequote(::com::htsc::mdc::insight::model::MDSLIndicativeQuote* mdslindicativequote) {
  delete mdslindicativequote_;
  mdslindicativequote_ = mdslindicativequote;
  if (mdslindicativequote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLIndicativeQuote)
}

// optional .com.htsc.mdc.insight.model.MDSLStatistics mdSLStatistics = 50;
bool MarketData::has_mdslstatistics() const {
  return this != internal_default_instance() && mdslstatistics_ != NULL;
}
void MarketData::clear_mdslstatistics() {
  if (GetArenaNoVirtual() == NULL && mdslstatistics_ != NULL) delete mdslstatistics_;
  mdslstatistics_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSLStatistics& MarketData::mdslstatistics() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  return mdslstatistics_ != NULL ? *mdslstatistics_
                         : *::com::htsc::mdc::insight::model::MDSLStatistics::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSLStatistics* MarketData::mutable_mdslstatistics() {
  
  if (mdslstatistics_ == NULL) {
    mdslstatistics_ = new ::com::htsc::mdc::insight::model::MDSLStatistics;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  return mdslstatistics_;
}
::com::htsc::mdc::insight::model::MDSLStatistics* MarketData::release_mdslstatistics() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
  
  ::com::htsc::mdc::insight::model::MDSLStatistics* temp = mdslstatistics_;
  mdslstatistics_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdslstatistics(::com::htsc::mdc::insight::model::MDSLStatistics* mdslstatistics) {
  delete mdslstatistics_;
  mdslstatistics_ = mdslstatistics;
  if (mdslstatistics) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLStatistics)
}

// optional .com.htsc.mdc.insight.model.MDUSAQuote mdUSAQuote = 51;
bool MarketData::has_mdusaquote() const {
  return this != internal_default_instance() && mdusaquote_ != NULL;
}
void MarketData::clear_mdusaquote() {
  if (GetArenaNoVirtual() == NULL && mdusaquote_ != NULL) delete mdusaquote_;
  mdusaquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDUSAQuote& MarketData::mdusaquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  return mdusaquote_ != NULL ? *mdusaquote_
                         : *::com::htsc::mdc::insight::model::MDUSAQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDUSAQuote* MarketData::mutable_mdusaquote() {
  
  if (mdusaquote_ == NULL) {
    mdusaquote_ = new ::com::htsc::mdc::insight::model::MDUSAQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  return mdusaquote_;
}
::com::htsc::mdc::insight::model::MDUSAQuote* MarketData::release_mdusaquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
  
  ::com::htsc::mdc::insight::model::MDUSAQuote* temp = mdusaquote_;
  mdusaquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdusaquote(::com::htsc::mdc::insight::model::MDUSAQuote* mdusaquote) {
  delete mdusaquote_;
  mdusaquote_ = mdusaquote;
  if (mdusaquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdUSAQuote)
}

// optional .com.htsc.mdc.insight.model.MDSLEstimation mdSLEstimation = 52;
bool MarketData::has_mdslestimation() const {
  return this != internal_default_instance() && mdslestimation_ != NULL;
}
void MarketData::clear_mdslestimation() {
  if (GetArenaNoVirtual() == NULL && mdslestimation_ != NULL) delete mdslestimation_;
  mdslestimation_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDSLEstimation& MarketData::mdslestimation() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  return mdslestimation_ != NULL ? *mdslestimation_
                         : *::com::htsc::mdc::insight::model::MDSLEstimation::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDSLEstimation* MarketData::mutable_mdslestimation() {
  
  if (mdslestimation_ == NULL) {
    mdslestimation_ = new ::com::htsc::mdc::insight::model::MDSLEstimation;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  return mdslestimation_;
}
::com::htsc::mdc::insight::model::MDSLEstimation* MarketData::release_mdslestimation() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
  
  ::com::htsc::mdc::insight::model::MDSLEstimation* temp = mdslestimation_;
  mdslestimation_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdslestimation(::com::htsc::mdc::insight::model::MDSLEstimation* mdslestimation) {
  delete mdslestimation_;
  mdslestimation_ = mdslestimation;
  if (mdslestimation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdSLEstimation)
}

// optional .com.htsc.mdc.insight.model.MDCnexDeal mdCnexDeal = 53;
bool MarketData::has_mdcnexdeal() const {
  return this != internal_default_instance() && mdcnexdeal_ != NULL;
}
void MarketData::clear_mdcnexdeal() {
  if (GetArenaNoVirtual() == NULL && mdcnexdeal_ != NULL) delete mdcnexdeal_;
  mdcnexdeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCnexDeal& MarketData::mdcnexdeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  return mdcnexdeal_ != NULL ? *mdcnexdeal_
                         : *::com::htsc::mdc::insight::model::MDCnexDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCnexDeal* MarketData::mutable_mdcnexdeal() {
  
  if (mdcnexdeal_ == NULL) {
    mdcnexdeal_ = new ::com::htsc::mdc::insight::model::MDCnexDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  return mdcnexdeal_;
}
::com::htsc::mdc::insight::model::MDCnexDeal* MarketData::release_mdcnexdeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
  
  ::com::htsc::mdc::insight::model::MDCnexDeal* temp = mdcnexdeal_;
  mdcnexdeal_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcnexdeal(::com::htsc::mdc::insight::model::MDCnexDeal* mdcnexdeal) {
  delete mdcnexdeal_;
  mdcnexdeal_ = mdcnexdeal;
  if (mdcnexdeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCnexDeal)
}

// optional .com.htsc.mdc.insight.model.MDCnexQuote mdCnexQuote = 54;
bool MarketData::has_mdcnexquote() const {
  return this != internal_default_instance() && mdcnexquote_ != NULL;
}
void MarketData::clear_mdcnexquote() {
  if (GetArenaNoVirtual() == NULL && mdcnexquote_ != NULL) delete mdcnexquote_;
  mdcnexquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCnexQuote& MarketData::mdcnexquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  return mdcnexquote_ != NULL ? *mdcnexquote_
                         : *::com::htsc::mdc::insight::model::MDCnexQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCnexQuote* MarketData::mutable_mdcnexquote() {
  
  if (mdcnexquote_ == NULL) {
    mdcnexquote_ = new ::com::htsc::mdc::insight::model::MDCnexQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  return mdcnexquote_;
}
::com::htsc::mdc::insight::model::MDCnexQuote* MarketData::release_mdcnexquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
  
  ::com::htsc::mdc::insight::model::MDCnexQuote* temp = mdcnexquote_;
  mdcnexquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcnexquote(::com::htsc::mdc::insight::model::MDCnexQuote* mdcnexquote) {
  delete mdcnexquote_;
  mdcnexquote_ = mdcnexquote;
  if (mdcnexquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCnexQuote)
}

// optional .com.htsc.mdc.insight.model.MDDelaySnapshot mdDelaySnapshot = 55;
bool MarketData::has_mddelaysnapshot() const {
  return this != internal_default_instance() && mddelaysnapshot_ != NULL;
}
void MarketData::clear_mddelaysnapshot() {
  if (GetArenaNoVirtual() == NULL && mddelaysnapshot_ != NULL) delete mddelaysnapshot_;
  mddelaysnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDDelaySnapshot& MarketData::mddelaysnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  return mddelaysnapshot_ != NULL ? *mddelaysnapshot_
                         : *::com::htsc::mdc::insight::model::MDDelaySnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDDelaySnapshot* MarketData::mutable_mddelaysnapshot() {
  
  if (mddelaysnapshot_ == NULL) {
    mddelaysnapshot_ = new ::com::htsc::mdc::insight::model::MDDelaySnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  return mddelaysnapshot_;
}
::com::htsc::mdc::insight::model::MDDelaySnapshot* MarketData::release_mddelaysnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
  
  ::com::htsc::mdc::insight::model::MDDelaySnapshot* temp = mddelaysnapshot_;
  mddelaysnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mddelaysnapshot(::com::htsc::mdc::insight::model::MDDelaySnapshot* mddelaysnapshot) {
  delete mddelaysnapshot_;
  mddelaysnapshot_ = mddelaysnapshot;
  if (mddelaysnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdDelaySnapshot)
}

// optional .com.htsc.mdc.insight.model.MDHighAccuracyFuture mdHighAccuracyFuture = 56;
bool MarketData::has_mdhighaccuracyfuture() const {
  return this != internal_default_instance() && mdhighaccuracyfuture_ != NULL;
}
void MarketData::clear_mdhighaccuracyfuture() {
  if (GetArenaNoVirtual() == NULL && mdhighaccuracyfuture_ != NULL) delete mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDHighAccuracyFuture& MarketData::mdhighaccuracyfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  return mdhighaccuracyfuture_ != NULL ? *mdhighaccuracyfuture_
                         : *::com::htsc::mdc::insight::model::MDHighAccuracyFuture::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDHighAccuracyFuture* MarketData::mutable_mdhighaccuracyfuture() {
  
  if (mdhighaccuracyfuture_ == NULL) {
    mdhighaccuracyfuture_ = new ::com::htsc::mdc::insight::model::MDHighAccuracyFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  return mdhighaccuracyfuture_;
}
::com::htsc::mdc::insight::model::MDHighAccuracyFuture* MarketData::release_mdhighaccuracyfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
  
  ::com::htsc::mdc::insight::model::MDHighAccuracyFuture* temp = mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdhighaccuracyfuture(::com::htsc::mdc::insight::model::MDHighAccuracyFuture* mdhighaccuracyfuture) {
  delete mdhighaccuracyfuture_;
  mdhighaccuracyfuture_ = mdhighaccuracyfuture;
  if (mdhighaccuracyfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdHighAccuracyFuture)
}

// optional .com.htsc.mdc.insight.model.MDCfetsForex mdCfetsForex = 57;
bool MarketData::has_mdcfetsforex() const {
  return this != internal_default_instance() && mdcfetsforex_ != NULL;
}
void MarketData::clear_mdcfetsforex() {
  if (GetArenaNoVirtual() == NULL && mdcfetsforex_ != NULL) delete mdcfetsforex_;
  mdcfetsforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsForex& MarketData::mdcfetsforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  return mdcfetsforex_ != NULL ? *mdcfetsforex_
                         : *::com::htsc::mdc::insight::model::MDCfetsForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsForex* MarketData::mutable_mdcfetsforex() {
  
  if (mdcfetsforex_ == NULL) {
    mdcfetsforex_ = new ::com::htsc::mdc::insight::model::MDCfetsForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  return mdcfetsforex_;
}
::com::htsc::mdc::insight::model::MDCfetsForex* MarketData::release_mdcfetsforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
  
  ::com::htsc::mdc::insight::model::MDCfetsForex* temp = mdcfetsforex_;
  mdcfetsforex_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsforex(::com::htsc::mdc::insight::model::MDCfetsForex* mdcfetsforex) {
  delete mdcfetsforex_;
  mdcfetsforex_ = mdcfetsforex;
  if (mdcfetsforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsForex)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxSnapshot mdCfetsFxSnapshot = 58;
bool MarketData::has_mdcfetsfxsnapshot() const {
  return this != internal_default_instance() && mdcfetsfxsnapshot_ != NULL;
}
void MarketData::clear_mdcfetsfxsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxsnapshot_ != NULL) delete mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot& MarketData::mdcfetsfxsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  return mdcfetsfxsnapshot_ != NULL ? *mdcfetsfxsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* MarketData::mutable_mdcfetsfxsnapshot() {
  
  if (mdcfetsfxsnapshot_ == NULL) {
    mdcfetsfxsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  return mdcfetsfxsnapshot_;
}
::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* MarketData::release_mdcfetsfxsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* temp = mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsfxsnapshot(::com::htsc::mdc::insight::model::MDCfetsFxSnapshot* mdcfetsfxsnapshot) {
  delete mdcfetsfxsnapshot_;
  mdcfetsfxsnapshot_ = mdcfetsfxsnapshot;
  if (mdcfetsfxsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxQuote mdCfetsFxQuote = 59;
bool MarketData::has_mdcfetsfxquote() const {
  return this != internal_default_instance() && mdcfetsfxquote_ != NULL;
}
void MarketData::clear_mdcfetsfxquote() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxquote_ != NULL) delete mdcfetsfxquote_;
  mdcfetsfxquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsFxQuote& MarketData::mdcfetsfxquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  return mdcfetsfxquote_ != NULL ? *mdcfetsfxquote_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsFxQuote* MarketData::mutable_mdcfetsfxquote() {
  
  if (mdcfetsfxquote_ == NULL) {
    mdcfetsfxquote_ = new ::com::htsc::mdc::insight::model::MDCfetsFxQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  return mdcfetsfxquote_;
}
::com::htsc::mdc::insight::model::MDCfetsFxQuote* MarketData::release_mdcfetsfxquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxQuote* temp = mdcfetsfxquote_;
  mdcfetsfxquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsfxquote(::com::htsc::mdc::insight::model::MDCfetsFxQuote* mdcfetsfxquote) {
  delete mdcfetsfxquote_;
  mdcfetsfxquote_ = mdcfetsfxquote;
  if (mdcfetsfxquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxQuote)
}

// optional .com.htsc.mdc.insight.model.SPFuture spFuture = 60;
bool MarketData::has_spfuture() const {
  return this != internal_default_instance() && spfuture_ != NULL;
}
void MarketData::clear_spfuture() {
  if (GetArenaNoVirtual() == NULL && spfuture_ != NULL) delete spfuture_;
  spfuture_ = NULL;
}
const ::com::htsc::mdc::insight::model::SPFuture& MarketData::spfuture() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.spFuture)
  return spfuture_ != NULL ? *spfuture_
                         : *::com::htsc::mdc::insight::model::SPFuture::internal_default_instance();
}
::com::htsc::mdc::insight::model::SPFuture* MarketData::mutable_spfuture() {
  
  if (spfuture_ == NULL) {
    spfuture_ = new ::com::htsc::mdc::insight::model::SPFuture;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.spFuture)
  return spfuture_;
}
::com::htsc::mdc::insight::model::SPFuture* MarketData::release_spfuture() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.spFuture)
  
  ::com::htsc::mdc::insight::model::SPFuture* temp = spfuture_;
  spfuture_ = NULL;
  return temp;
}
void MarketData::set_allocated_spfuture(::com::htsc::mdc::insight::model::SPFuture* spfuture) {
  delete spfuture_;
  spfuture_ = spfuture;
  if (spfuture) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.spFuture)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBenchmark mdCfetsBenchmark = 61;
bool MarketData::has_mdcfetsbenchmark() const {
  return this != internal_default_instance() && mdcfetsbenchmark_ != NULL;
}
void MarketData::clear_mdcfetsbenchmark() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbenchmark_ != NULL) delete mdcfetsbenchmark_;
  mdcfetsbenchmark_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsBenchmark& MarketData::mdcfetsbenchmark() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  return mdcfetsbenchmark_ != NULL ? *mdcfetsbenchmark_
                         : *::com::htsc::mdc::insight::model::MDCfetsBenchmark::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsBenchmark* MarketData::mutable_mdcfetsbenchmark() {
  
  if (mdcfetsbenchmark_ == NULL) {
    mdcfetsbenchmark_ = new ::com::htsc::mdc::insight::model::MDCfetsBenchmark;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  return mdcfetsbenchmark_;
}
::com::htsc::mdc::insight::model::MDCfetsBenchmark* MarketData::release_mdcfetsbenchmark() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
  
  ::com::htsc::mdc::insight::model::MDCfetsBenchmark* temp = mdcfetsbenchmark_;
  mdcfetsbenchmark_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsbenchmark(::com::htsc::mdc::insight::model::MDCfetsBenchmark* mdcfetsbenchmark) {
  delete mdcfetsbenchmark_;
  mdcfetsbenchmark_ = mdcfetsbenchmark;
  if (mdcfetsbenchmark) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBenchmark)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBondDeal mdCfetsBondDeal = 62;
bool MarketData::has_mdcfetsbonddeal() const {
  return this != internal_default_instance() && mdcfetsbonddeal_ != NULL;
}
void MarketData::clear_mdcfetsbonddeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbonddeal_ != NULL) delete mdcfetsbonddeal_;
  mdcfetsbonddeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsBondDeal& MarketData::mdcfetsbonddeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  return mdcfetsbonddeal_ != NULL ? *mdcfetsbonddeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsBondDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsBondDeal* MarketData::mutable_mdcfetsbonddeal() {
  
  if (mdcfetsbonddeal_ == NULL) {
    mdcfetsbonddeal_ = new ::com::htsc::mdc::insight::model::MDCfetsBondDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  return mdcfetsbonddeal_;
}
::com::htsc::mdc::insight::model::MDCfetsBondDeal* MarketData::release_mdcfetsbonddeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsBondDeal* temp = mdcfetsbonddeal_;
  mdcfetsbonddeal_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsbonddeal(::com::htsc::mdc::insight::model::MDCfetsBondDeal* mdcfetsbonddeal) {
  delete mdcfetsbonddeal_;
  mdcfetsbonddeal_ = mdcfetsbonddeal;
  if (mdcfetsbonddeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBondDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsBondSnapshot mdCfetsBondSnapshot = 63;
bool MarketData::has_mdcfetsbondsnapshot() const {
  return this != internal_default_instance() && mdcfetsbondsnapshot_ != NULL;
}
void MarketData::clear_mdcfetsbondsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsbondsnapshot_ != NULL) delete mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot& MarketData::mdcfetsbondsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  return mdcfetsbondsnapshot_ != NULL ? *mdcfetsbondsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsBondSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* MarketData::mutable_mdcfetsbondsnapshot() {
  
  if (mdcfetsbondsnapshot_ == NULL) {
    mdcfetsbondsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  return mdcfetsbondsnapshot_;
}
::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* MarketData::release_mdcfetsbondsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* temp = mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsbondsnapshot(::com::htsc::mdc::insight::model::MDCfetsBondSnapshot* mdcfetsbondsnapshot) {
  delete mdcfetsbondsnapshot_;
  mdcfetsbondsnapshot_ = mdcfetsbondsnapshot;
  if (mdcfetsbondsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsBondSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsCurrencyDeal mdCfetsCurrencyDeal = 64;
bool MarketData::has_mdcfetscurrencydeal() const {
  return this != internal_default_instance() && mdcfetscurrencydeal_ != NULL;
}
void MarketData::clear_mdcfetscurrencydeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencydeal_ != NULL) delete mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal& MarketData::mdcfetscurrencydeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  return mdcfetscurrencydeal_ != NULL ? *mdcfetscurrencydeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* MarketData::mutable_mdcfetscurrencydeal() {
  
  if (mdcfetscurrencydeal_ == NULL) {
    mdcfetscurrencydeal_ = new ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  return mdcfetscurrencydeal_;
}
::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* MarketData::release_mdcfetscurrencydeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* temp = mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetscurrencydeal(::com::htsc::mdc::insight::model::MDCfetsCurrencyDeal* mdcfetscurrencydeal) {
  delete mdcfetscurrencydeal_;
  mdcfetscurrencydeal_ = mdcfetscurrencydeal;
  if (mdcfetscurrencydeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencyDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsCurrencySnapshot mdCfetsCurrencySnapshot = 65;
bool MarketData::has_mdcfetscurrencysnapshot() const {
  return this != internal_default_instance() && mdcfetscurrencysnapshot_ != NULL;
}
void MarketData::clear_mdcfetscurrencysnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetscurrencysnapshot_ != NULL) delete mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot& MarketData::mdcfetscurrencysnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  return mdcfetscurrencysnapshot_ != NULL ? *mdcfetscurrencysnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* MarketData::mutable_mdcfetscurrencysnapshot() {
  
  if (mdcfetscurrencysnapshot_ == NULL) {
    mdcfetscurrencysnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  return mdcfetscurrencysnapshot_;
}
::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* MarketData::release_mdcfetscurrencysnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* temp = mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetscurrencysnapshot(::com::htsc::mdc::insight::model::MDCfetsCurrencySnapshot* mdcfetscurrencysnapshot) {
  delete mdcfetscurrencysnapshot_;
  mdcfetscurrencysnapshot_ = mdcfetscurrencysnapshot;
  if (mdcfetscurrencysnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsCurrencySnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsODMSnapshot mdCfetsODMSnapshot = 66;
bool MarketData::has_mdcfetsodmsnapshot() const {
  return this != internal_default_instance() && mdcfetsodmsnapshot_ != NULL;
}
void MarketData::clear_mdcfetsodmsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsodmsnapshot_ != NULL) delete mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot& MarketData::mdcfetsodmsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  return mdcfetsodmsnapshot_ != NULL ? *mdcfetsodmsnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsODMSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* MarketData::mutable_mdcfetsodmsnapshot() {
  
  if (mdcfetsodmsnapshot_ == NULL) {
    mdcfetsodmsnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  return mdcfetsodmsnapshot_;
}
::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* MarketData::release_mdcfetsodmsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* temp = mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsodmsnapshot(::com::htsc::mdc::insight::model::MDCfetsODMSnapshot* mdcfetsodmsnapshot) {
  delete mdcfetsodmsnapshot_;
  mdcfetsodmsnapshot_ = mdcfetsodmsnapshot;
  if (mdcfetsodmsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsODMSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsQDMQuote mdCfetsQDMQuote = 67;
bool MarketData::has_mdcfetsqdmquote() const {
  return this != internal_default_instance() && mdcfetsqdmquote_ != NULL;
}
void MarketData::clear_mdcfetsqdmquote() {
  if (GetArenaNoVirtual() == NULL && mdcfetsqdmquote_ != NULL) delete mdcfetsqdmquote_;
  mdcfetsqdmquote_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsQDMQuote& MarketData::mdcfetsqdmquote() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  return mdcfetsqdmquote_ != NULL ? *mdcfetsqdmquote_
                         : *::com::htsc::mdc::insight::model::MDCfetsQDMQuote::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsQDMQuote* MarketData::mutable_mdcfetsqdmquote() {
  
  if (mdcfetsqdmquote_ == NULL) {
    mdcfetsqdmquote_ = new ::com::htsc::mdc::insight::model::MDCfetsQDMQuote;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  return mdcfetsqdmquote_;
}
::com::htsc::mdc::insight::model::MDCfetsQDMQuote* MarketData::release_mdcfetsqdmquote() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
  
  ::com::htsc::mdc::insight::model::MDCfetsQDMQuote* temp = mdcfetsqdmquote_;
  mdcfetsqdmquote_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsqdmquote(::com::htsc::mdc::insight::model::MDCfetsQDMQuote* mdcfetsqdmquote) {
  delete mdcfetsqdmquote_;
  mdcfetsqdmquote_ = mdcfetsqdmquote;
  if (mdcfetsqdmquote) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsQDMQuote)
}

// optional .com.htsc.mdc.insight.model.MDCfetsRateDeal mdCfetsRateDeal = 68;
bool MarketData::has_mdcfetsratedeal() const {
  return this != internal_default_instance() && mdcfetsratedeal_ != NULL;
}
void MarketData::clear_mdcfetsratedeal() {
  if (GetArenaNoVirtual() == NULL && mdcfetsratedeal_ != NULL) delete mdcfetsratedeal_;
  mdcfetsratedeal_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsRateDeal& MarketData::mdcfetsratedeal() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  return mdcfetsratedeal_ != NULL ? *mdcfetsratedeal_
                         : *::com::htsc::mdc::insight::model::MDCfetsRateDeal::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsRateDeal* MarketData::mutable_mdcfetsratedeal() {
  
  if (mdcfetsratedeal_ == NULL) {
    mdcfetsratedeal_ = new ::com::htsc::mdc::insight::model::MDCfetsRateDeal;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  return mdcfetsratedeal_;
}
::com::htsc::mdc::insight::model::MDCfetsRateDeal* MarketData::release_mdcfetsratedeal() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
  
  ::com::htsc::mdc::insight::model::MDCfetsRateDeal* temp = mdcfetsratedeal_;
  mdcfetsratedeal_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsratedeal(::com::htsc::mdc::insight::model::MDCfetsRateDeal* mdcfetsratedeal) {
  delete mdcfetsratedeal_;
  mdcfetsratedeal_ = mdcfetsratedeal;
  if (mdcfetsratedeal) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsRateDeal)
}

// optional .com.htsc.mdc.insight.model.MDCfetsRateSnapshot mdCfetsRateSnapshot = 69;
bool MarketData::has_mdcfetsratesnapshot() const {
  return this != internal_default_instance() && mdcfetsratesnapshot_ != NULL;
}
void MarketData::clear_mdcfetsratesnapshot() {
  if (GetArenaNoVirtual() == NULL && mdcfetsratesnapshot_ != NULL) delete mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot& MarketData::mdcfetsratesnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  return mdcfetsratesnapshot_ != NULL ? *mdcfetsratesnapshot_
                         : *::com::htsc::mdc::insight::model::MDCfetsRateSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* MarketData::mutable_mdcfetsratesnapshot() {
  
  if (mdcfetsratesnapshot_ == NULL) {
    mdcfetsratesnapshot_ = new ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  return mdcfetsratesnapshot_;
}
::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* MarketData::release_mdcfetsratesnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
  
  ::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* temp = mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsratesnapshot(::com::htsc::mdc::insight::model::MDCfetsRateSnapshot* mdcfetsratesnapshot) {
  delete mdcfetsratesnapshot_;
  mdcfetsratesnapshot_ = mdcfetsratesnapshot;
  if (mdcfetsratesnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsRateSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDCfetsFxCnyMiddlePrice mdCfetsFxCnyMiddlePrice = 70;
bool MarketData::has_mdcfetsfxcnymiddleprice() const {
  return this != internal_default_instance() && mdcfetsfxcnymiddleprice_ != NULL;
}
void MarketData::clear_mdcfetsfxcnymiddleprice() {
  if (GetArenaNoVirtual() == NULL && mdcfetsfxcnymiddleprice_ != NULL) delete mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice& MarketData::mdcfetsfxcnymiddleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  return mdcfetsfxcnymiddleprice_ != NULL ? *mdcfetsfxcnymiddleprice_
                         : *::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* MarketData::mutable_mdcfetsfxcnymiddleprice() {
  
  if (mdcfetsfxcnymiddleprice_ == NULL) {
    mdcfetsfxcnymiddleprice_ = new ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  return mdcfetsfxcnymiddleprice_;
}
::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* MarketData::release_mdcfetsfxcnymiddleprice() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
  
  ::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* temp = mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdcfetsfxcnymiddleprice(::com::htsc::mdc::insight::model::MDCfetsFxCnyMiddlePrice* mdcfetsfxcnymiddleprice) {
  delete mdcfetsfxcnymiddleprice_;
  mdcfetsfxcnymiddleprice_ = mdcfetsfxcnymiddleprice;
  if (mdcfetsfxcnymiddleprice) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdCfetsFxCnyMiddlePrice)
}

// optional .com.htsc.mdc.insight.model.MDIopvSnapshot mdIopvSnapshot = 71;
bool MarketData::has_mdiopvsnapshot() const {
  return this != internal_default_instance() && mdiopvsnapshot_ != NULL;
}
void MarketData::clear_mdiopvsnapshot() {
  if (GetArenaNoVirtual() == NULL && mdiopvsnapshot_ != NULL) delete mdiopvsnapshot_;
  mdiopvsnapshot_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDIopvSnapshot& MarketData::mdiopvsnapshot() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  return mdiopvsnapshot_ != NULL ? *mdiopvsnapshot_
                         : *::com::htsc::mdc::insight::model::MDIopvSnapshot::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDIopvSnapshot* MarketData::mutable_mdiopvsnapshot() {
  
  if (mdiopvsnapshot_ == NULL) {
    mdiopvsnapshot_ = new ::com::htsc::mdc::insight::model::MDIopvSnapshot;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  return mdiopvsnapshot_;
}
::com::htsc::mdc::insight::model::MDIopvSnapshot* MarketData::release_mdiopvsnapshot() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
  
  ::com::htsc::mdc::insight::model::MDIopvSnapshot* temp = mdiopvsnapshot_;
  mdiopvsnapshot_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdiopvsnapshot(::com::htsc::mdc::insight::model::MDIopvSnapshot* mdiopvsnapshot) {
  delete mdiopvsnapshot_;
  mdiopvsnapshot_ = mdiopvsnapshot;
  if (mdiopvsnapshot) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIopvSnapshot)
}

// optional .com.htsc.mdc.insight.model.MDChinaBondBenchmark mdChinaBondBenchmark = 72;
bool MarketData::has_mdchinabondbenchmark() const {
  return this != internal_default_instance() && mdchinabondbenchmark_ != NULL;
}
void MarketData::clear_mdchinabondbenchmark() {
  if (GetArenaNoVirtual() == NULL && mdchinabondbenchmark_ != NULL) delete mdchinabondbenchmark_;
  mdchinabondbenchmark_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDChinaBondBenchmark& MarketData::mdchinabondbenchmark() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  return mdchinabondbenchmark_ != NULL ? *mdchinabondbenchmark_
                         : *::com::htsc::mdc::insight::model::MDChinaBondBenchmark::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDChinaBondBenchmark* MarketData::mutable_mdchinabondbenchmark() {
  
  if (mdchinabondbenchmark_ == NULL) {
    mdchinabondbenchmark_ = new ::com::htsc::mdc::insight::model::MDChinaBondBenchmark;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  return mdchinabondbenchmark_;
}
::com::htsc::mdc::insight::model::MDChinaBondBenchmark* MarketData::release_mdchinabondbenchmark() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
  
  ::com::htsc::mdc::insight::model::MDChinaBondBenchmark* temp = mdchinabondbenchmark_;
  mdchinabondbenchmark_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdchinabondbenchmark(::com::htsc::mdc::insight::model::MDChinaBondBenchmark* mdchinabondbenchmark) {
  delete mdchinabondbenchmark_;
  mdchinabondbenchmark_ = mdchinabondbenchmark;
  if (mdchinabondbenchmark) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdChinaBondBenchmark)
}

// optional .com.htsc.mdc.insight.model.MDIceTrace mdIceTrace = 73;
bool MarketData::has_mdicetrace() const {
  return this != internal_default_instance() && mdicetrace_ != NULL;
}
void MarketData::clear_mdicetrace() {
  if (GetArenaNoVirtual() == NULL && mdicetrace_ != NULL) delete mdicetrace_;
  mdicetrace_ = NULL;
}
const ::com::htsc::mdc::insight::model::MDIceTrace& MarketData::mdicetrace() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  return mdicetrace_ != NULL ? *mdicetrace_
                         : *::com::htsc::mdc::insight::model::MDIceTrace::internal_default_instance();
}
::com::htsc::mdc::insight::model::MDIceTrace* MarketData::mutable_mdicetrace() {
  
  if (mdicetrace_ == NULL) {
    mdicetrace_ = new ::com::htsc::mdc::insight::model::MDIceTrace;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  return mdicetrace_;
}
::com::htsc::mdc::insight::model::MDIceTrace* MarketData::release_mdicetrace() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
  
  ::com::htsc::mdc::insight::model::MDIceTrace* temp = mdicetrace_;
  mdicetrace_ = NULL;
  return temp;
}
void MarketData::set_allocated_mdicetrace(::com::htsc::mdc::insight::model::MDIceTrace* mdicetrace) {
  delete mdicetrace_;
  mdicetrace_ = mdicetrace;
  if (mdicetrace) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketData.mdIceTrace)
}

inline const MarketData* MarketData::internal_default_instance() {
  return &MarketData_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MarketDataStream::kIsCompressedFieldNumber;
const int MarketDataStream::kOriginalLengthFieldNumber;
const int MarketDataStream::kCompressedDataFieldNumber;
const int MarketDataStream::kMarketDataListFieldNumber;
const int MarketDataStream::kTotalNumberFieldNumber;
const int MarketDataStream::kSerialFieldNumber;
const int MarketDataStream::kIsFinishedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarketDataStream::MarketDataStream()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MarketData_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MarketDataStream)
}

void MarketDataStream::InitAsDefaultInstance() {
  marketdatalist_ = const_cast< ::com::htsc::mdc::insight::model::MarketDataList*>(
      ::com::htsc::mdc::insight::model::MarketDataList::internal_default_instance());
}

MarketDataStream::MarketDataStream(const MarketDataStream& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MarketDataStream)
}

void MarketDataStream::SharedCtor() {
  compresseddata_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketdatalist_ = NULL;
  ::memset(&originallength_, 0, reinterpret_cast<char*>(&serial_) -
    reinterpret_cast<char*>(&originallength_) + sizeof(serial_));
  _cached_size_ = 0;
}

MarketDataStream::~MarketDataStream() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MarketDataStream)
  SharedDtor();
}

void MarketDataStream::SharedDtor() {
  compresseddata_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MarketDataStream_default_instance_.get()) {
    delete marketdatalist_;
  }
}

void MarketDataStream::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarketDataStream::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MarketDataStream_descriptor_;
}

const MarketDataStream& MarketDataStream::default_instance() {
  protobuf_InitDefaults_MarketData_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MarketDataStream> MarketDataStream_default_instance_;

MarketDataStream* MarketDataStream::New(::google::protobuf::Arena* arena) const {
  MarketDataStream* n = new MarketDataStream;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarketDataStream::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MarketDataStream)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MarketDataStream, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MarketDataStream*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(originallength_, serial_);
  compresseddata_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && marketdatalist_ != NULL) delete marketdatalist_;
  marketdatalist_ = NULL;

#undef ZR_HELPER_
#undef ZR_

}

bool MarketDataStream::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MarketDataStream)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool isCompressed = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &iscompressed_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_originalLength;
        break;
      }

      // optional int32 originalLength = 2;
      case 2: {
        if (tag == 16) {
         parse_originalLength:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &originallength_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_compressedData;
        break;
      }

      // optional bytes compressedData = 3;
      case 3: {
        if (tag == 26) {
         parse_compressedData:
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_compresseddata()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_marketDataList;
        break;
      }

      // optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
      case 4: {
        if (tag == 34) {
         parse_marketDataList:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_marketdatalist()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_totalNumber;
        break;
      }

      // optional int32 totalNumber = 5;
      case 5: {
        if (tag == 40) {
         parse_totalNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &totalnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_serial;
        break;
      }

      // optional int32 serial = 6;
      case 6: {
        if (tag == 48) {
         parse_serial:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &serial_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_isFinished;
        break;
      }

      // optional bool isFinished = 7;
      case 7: {
        if (tag == 56) {
         parse_isFinished:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &isfinished_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MarketDataStream)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MarketDataStream)
  return false;
#undef DO_
}

void MarketDataStream::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MarketDataStream)
  // optional bool isCompressed = 1;
  if (this->iscompressed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->iscompressed(), output);
  }

  // optional int32 originalLength = 2;
  if (this->originallength() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->originallength(), output);
  }

  // optional bytes compressedData = 3;
  if (this->compresseddata().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      3, this->compresseddata(), output);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
  if (this->has_marketdatalist()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->marketdatalist_, output);
  }

  // optional int32 totalNumber = 5;
  if (this->totalnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->totalnumber(), output);
  }

  // optional int32 serial = 6;
  if (this->serial() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->serial(), output);
  }

  // optional bool isFinished = 7;
  if (this->isfinished() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->isfinished(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MarketDataStream)
}

::google::protobuf::uint8* MarketDataStream::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MarketDataStream)
  // optional bool isCompressed = 1;
  if (this->iscompressed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->iscompressed(), target);
  }

  // optional int32 originalLength = 2;
  if (this->originallength() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->originallength(), target);
  }

  // optional bytes compressedData = 3;
  if (this->compresseddata().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        3, this->compresseddata(), target);
  }

  // optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
  if (this->has_marketdatalist()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->marketdatalist_, false, target);
  }

  // optional int32 totalNumber = 5;
  if (this->totalnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->totalnumber(), target);
  }

  // optional int32 serial = 6;
  if (this->serial() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->serial(), target);
  }

  // optional bool isFinished = 7;
  if (this->isfinished() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->isfinished(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MarketDataStream)
  return target;
}

size_t MarketDataStream::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MarketDataStream)
  size_t total_size = 0;

  // optional bool isCompressed = 1;
  if (this->iscompressed() != 0) {
    total_size += 1 + 1;
  }

  // optional int32 originalLength = 2;
  if (this->originallength() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->originallength());
  }

  // optional bytes compressedData = 3;
  if (this->compresseddata().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->compresseddata());
  }

  // optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
  if (this->has_marketdatalist()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->marketdatalist_);
  }

  // optional int32 totalNumber = 5;
  if (this->totalnumber() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->totalnumber());
  }

  // optional int32 serial = 6;
  if (this->serial() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->serial());
  }

  // optional bool isFinished = 7;
  if (this->isfinished() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarketDataStream::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MarketDataStream)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MarketDataStream* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarketDataStream>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MarketDataStream)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MarketDataStream)
    UnsafeMergeFrom(*source);
  }
}

void MarketDataStream::MergeFrom(const MarketDataStream& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MarketDataStream)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MarketDataStream::UnsafeMergeFrom(const MarketDataStream& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.iscompressed() != 0) {
    set_iscompressed(from.iscompressed());
  }
  if (from.originallength() != 0) {
    set_originallength(from.originallength());
  }
  if (from.compresseddata().size() > 0) {

    compresseddata_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.compresseddata_);
  }
  if (from.has_marketdatalist()) {
    mutable_marketdatalist()->::com::htsc::mdc::insight::model::MarketDataList::MergeFrom(from.marketdatalist());
  }
  if (from.totalnumber() != 0) {
    set_totalnumber(from.totalnumber());
  }
  if (from.serial() != 0) {
    set_serial(from.serial());
  }
  if (from.isfinished() != 0) {
    set_isfinished(from.isfinished());
  }
}

void MarketDataStream::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MarketDataStream)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarketDataStream::CopyFrom(const MarketDataStream& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MarketDataStream)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MarketDataStream::IsInitialized() const {

  return true;
}

void MarketDataStream::Swap(MarketDataStream* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarketDataStream::InternalSwap(MarketDataStream* other) {
  std::swap(iscompressed_, other->iscompressed_);
  std::swap(originallength_, other->originallength_);
  compresseddata_.Swap(&other->compresseddata_);
  std::swap(marketdatalist_, other->marketdatalist_);
  std::swap(totalnumber_, other->totalnumber_);
  std::swap(serial_, other->serial_);
  std::swap(isfinished_, other->isfinished_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarketDataStream::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MarketDataStream_descriptor_;
  metadata.reflection = MarketDataStream_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketDataStream

// optional bool isCompressed = 1;
void MarketDataStream::clear_iscompressed() {
  iscompressed_ = false;
}
bool MarketDataStream::iscompressed() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.isCompressed)
  return iscompressed_;
}
void MarketDataStream::set_iscompressed(bool value) {
  
  iscompressed_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.isCompressed)
}

// optional int32 originalLength = 2;
void MarketDataStream::clear_originallength() {
  originallength_ = 0;
}
::google::protobuf::int32 MarketDataStream::originallength() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.originalLength)
  return originallength_;
}
void MarketDataStream::set_originallength(::google::protobuf::int32 value) {
  
  originallength_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.originalLength)
}

// optional bytes compressedData = 3;
void MarketDataStream::clear_compresseddata() {
  compresseddata_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MarketDataStream::compresseddata() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  return compresseddata_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketDataStream::set_compresseddata(const ::std::string& value) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
void MarketDataStream::set_compresseddata(const char* value) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
void MarketDataStream::set_compresseddata(const void* value, size_t size) {
  
  compresseddata_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}
::std::string* MarketDataStream::mutable_compresseddata() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  return compresseddata_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MarketDataStream::release_compresseddata() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
  
  return compresseddata_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MarketDataStream::set_allocated_compresseddata(::std::string* compresseddata) {
  if (compresseddata != NULL) {
    
  } else {
    
  }
  compresseddata_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), compresseddata);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketDataStream.compressedData)
}

// optional .com.htsc.mdc.insight.model.MarketDataList marketDataList = 4;
bool MarketDataStream::has_marketdatalist() const {
  return this != internal_default_instance() && marketdatalist_ != NULL;
}
void MarketDataStream::clear_marketdatalist() {
  if (GetArenaNoVirtual() == NULL && marketdatalist_ != NULL) delete marketdatalist_;
  marketdatalist_ = NULL;
}
const ::com::htsc::mdc::insight::model::MarketDataList& MarketDataStream::marketdatalist() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  return marketdatalist_ != NULL ? *marketdatalist_
                         : *::com::htsc::mdc::insight::model::MarketDataList::internal_default_instance();
}
::com::htsc::mdc::insight::model::MarketDataList* MarketDataStream::mutable_marketdatalist() {
  
  if (marketdatalist_ == NULL) {
    marketdatalist_ = new ::com::htsc::mdc::insight::model::MarketDataList;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  return marketdatalist_;
}
::com::htsc::mdc::insight::model::MarketDataList* MarketDataStream::release_marketdatalist() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
  
  ::com::htsc::mdc::insight::model::MarketDataList* temp = marketdatalist_;
  marketdatalist_ = NULL;
  return temp;
}
void MarketDataStream::set_allocated_marketdatalist(::com::htsc::mdc::insight::model::MarketDataList* marketdatalist) {
  delete marketdatalist_;
  marketdatalist_ = marketdatalist;
  if (marketdatalist) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MarketDataStream.marketDataList)
}

// optional int32 totalNumber = 5;
void MarketDataStream::clear_totalnumber() {
  totalnumber_ = 0;
}
::google::protobuf::int32 MarketDataStream::totalnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.totalNumber)
  return totalnumber_;
}
void MarketDataStream::set_totalnumber(::google::protobuf::int32 value) {
  
  totalnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.totalNumber)
}

// optional int32 serial = 6;
void MarketDataStream::clear_serial() {
  serial_ = 0;
}
::google::protobuf::int32 MarketDataStream::serial() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.serial)
  return serial_;
}
void MarketDataStream::set_serial(::google::protobuf::int32 value) {
  
  serial_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.serial)
}

// optional bool isFinished = 7;
void MarketDataStream::clear_isfinished() {
  isfinished_ = false;
}
bool MarketDataStream::isfinished() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataStream.isFinished)
  return isfinished_;
}
void MarketDataStream::set_isfinished(bool value) {
  
  isfinished_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MarketDataStream.isFinished)
}

inline const MarketDataStream* MarketDataStream::internal_default_instance() {
  return &MarketDataStream_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MarketDataList::kMarketDatasFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MarketDataList::MarketDataList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MarketData_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MarketDataList)
}

void MarketDataList::InitAsDefaultInstance() {
}

MarketDataList::MarketDataList(const MarketDataList& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MarketDataList)
}

void MarketDataList::SharedCtor() {
  _cached_size_ = 0;
}

MarketDataList::~MarketDataList() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MarketDataList)
  SharedDtor();
}

void MarketDataList::SharedDtor() {
}

void MarketDataList::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MarketDataList::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MarketDataList_descriptor_;
}

const MarketDataList& MarketDataList::default_instance() {
  protobuf_InitDefaults_MarketData_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MarketDataList> MarketDataList_default_instance_;

MarketDataList* MarketDataList::New(::google::protobuf::Arena* arena) const {
  MarketDataList* n = new MarketDataList;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MarketDataList::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MarketDataList)
  marketdatas_.Clear();
}

bool MarketDataList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MarketDataList)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_marketDatas:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_marketdatas()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_marketDatas;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MarketDataList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MarketDataList)
  return false;
#undef DO_
}

void MarketDataList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MarketDataList)
  // repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
  for (unsigned int i = 0, n = this->marketdatas_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->marketdatas(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MarketDataList)
}

::google::protobuf::uint8* MarketDataList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MarketDataList)
  // repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
  for (unsigned int i = 0, n = this->marketdatas_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->marketdatas(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MarketDataList)
  return target;
}

size_t MarketDataList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MarketDataList)
  size_t total_size = 0;

  // repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
  {
    unsigned int count = this->marketdatas_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->marketdatas(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MarketDataList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MarketDataList)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MarketDataList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MarketDataList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MarketDataList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MarketDataList)
    UnsafeMergeFrom(*source);
  }
}

void MarketDataList::MergeFrom(const MarketDataList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MarketDataList)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MarketDataList::UnsafeMergeFrom(const MarketDataList& from) {
  GOOGLE_DCHECK(&from != this);
  marketdatas_.MergeFrom(from.marketdatas_);
}

void MarketDataList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MarketDataList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MarketDataList::CopyFrom(const MarketDataList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MarketDataList)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MarketDataList::IsInitialized() const {

  return true;
}

void MarketDataList::Swap(MarketDataList* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MarketDataList::InternalSwap(MarketDataList* other) {
  marketdatas_.UnsafeArenaSwap(&other->marketdatas_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MarketDataList::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MarketDataList_descriptor_;
  metadata.reflection = MarketDataList_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MarketDataList

// repeated .com.htsc.mdc.insight.model.MarketData marketDatas = 1;
int MarketDataList::marketdatas_size() const {
  return marketdatas_.size();
}
void MarketDataList::clear_marketdatas() {
  marketdatas_.Clear();
}
const ::com::htsc::mdc::insight::model::MarketData& MarketDataList::marketdatas(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Get(index);
}
::com::htsc::mdc::insight::model::MarketData* MarketDataList::mutable_marketdatas(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Mutable(index);
}
::com::htsc::mdc::insight::model::MarketData* MarketDataList::add_marketdatas() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >*
MarketDataList::mutable_marketdatas() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return &marketdatas_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::MarketData >&
MarketDataList::marketdatas() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MarketDataList.marketDatas)
  return marketdatas_;
}

inline const MarketDataList* MarketDataList::internal_default_instance() {
  return &MarketDataList_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
