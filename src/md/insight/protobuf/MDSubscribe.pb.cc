// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSubscribe.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSubscribe.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSubscribeRequest_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSubscribeRequest_reflection_ = NULL;
const ::google::protobuf::Descriptor* SubscribeAll_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SubscribeAll_reflection_ = NULL;
const ::google::protobuf::Descriptor* SubscribeByID_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SubscribeByID_reflection_ = NULL;
const ::google::protobuf::Descriptor* SubscribeByIDDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SubscribeByIDDetail_reflection_ = NULL;
const ::google::protobuf::Descriptor* SubscribeBySourceType_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SubscribeBySourceType_reflection_ = NULL;
const ::google::protobuf::Descriptor* SubscribeBySourceTypeDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SubscribeBySourceTypeDetail_reflection_ = NULL;
const ::google::protobuf::Descriptor* MDSubscribeResponse_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSubscribeResponse_reflection_ = NULL;
const ::google::protobuf::EnumDescriptor* ESubscribeActionType_descriptor_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSubscribe_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSubscribe_2eproto() {
  protobuf_AddDesc_MDSubscribe_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSubscribe.proto");
  GOOGLE_CHECK(file != NULL);
  MDSubscribeRequest_descriptor_ = file->message_type(0);
  static const int MDSubscribeRequest_offsets_[4] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeRequest, subscribeactiontype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeRequest, subscribeall_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeRequest, subscribebysourcetype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeRequest, subscribebyid_),
  };
  MDSubscribeRequest_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSubscribeRequest_descriptor_,
      MDSubscribeRequest::internal_default_instance(),
      MDSubscribeRequest_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSubscribeRequest),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeRequest, _internal_metadata_));
  SubscribeAll_descriptor_ = file->message_type(1);
  static const int SubscribeAll_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeAll, marketdatatypes_),
  };
  SubscribeAll_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SubscribeAll_descriptor_,
      SubscribeAll::internal_default_instance(),
      SubscribeAll_offsets_,
      -1,
      -1,
      -1,
      sizeof(SubscribeAll),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeAll, _internal_metadata_));
  SubscribeByID_descriptor_ = file->message_type(2);
  static const int SubscribeByID_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByID, subscribebyiddetails_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByID, globalmarketdatatypes_),
  };
  SubscribeByID_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SubscribeByID_descriptor_,
      SubscribeByID::internal_default_instance(),
      SubscribeByID_offsets_,
      -1,
      -1,
      -1,
      sizeof(SubscribeByID),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByID, _internal_metadata_));
  SubscribeByIDDetail_descriptor_ = file->message_type(3);
  static const int SubscribeByIDDetail_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByIDDetail, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByIDDetail, marketdatatypes_),
  };
  SubscribeByIDDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SubscribeByIDDetail_descriptor_,
      SubscribeByIDDetail::internal_default_instance(),
      SubscribeByIDDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(SubscribeByIDDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeByIDDetail, _internal_metadata_));
  SubscribeBySourceType_descriptor_ = file->message_type(4);
  static const int SubscribeBySourceType_offsets_[1] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeBySourceType, subscribebysourcetypedetail_),
  };
  SubscribeBySourceType_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SubscribeBySourceType_descriptor_,
      SubscribeBySourceType::internal_default_instance(),
      SubscribeBySourceType_offsets_,
      -1,
      -1,
      -1,
      sizeof(SubscribeBySourceType),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeBySourceType, _internal_metadata_));
  SubscribeBySourceTypeDetail_descriptor_ = file->message_type(5);
  static const int SubscribeBySourceTypeDetail_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeBySourceTypeDetail, securitysourcetypes_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeBySourceTypeDetail, marketdatatypes_),
  };
  SubscribeBySourceTypeDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SubscribeBySourceTypeDetail_descriptor_,
      SubscribeBySourceTypeDetail::internal_default_instance(),
      SubscribeBySourceTypeDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(SubscribeBySourceTypeDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SubscribeBySourceTypeDetail, _internal_metadata_));
  MDSubscribeResponse_descriptor_ = file->message_type(6);
  static const int MDSubscribeResponse_offsets_[2] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeResponse, issuccess_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeResponse, errorcontext_),
  };
  MDSubscribeResponse_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSubscribeResponse_descriptor_,
      MDSubscribeResponse::internal_default_instance(),
      MDSubscribeResponse_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSubscribeResponse),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSubscribeResponse, _internal_metadata_));
  ESubscribeActionType_descriptor_ = file->enum_type(0);
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSubscribe_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSubscribeRequest_descriptor_, MDSubscribeRequest::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SubscribeAll_descriptor_, SubscribeAll::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SubscribeByID_descriptor_, SubscribeByID::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SubscribeByIDDetail_descriptor_, SubscribeByIDDetail::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SubscribeBySourceType_descriptor_, SubscribeBySourceType::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SubscribeBySourceTypeDetail_descriptor_, SubscribeBySourceTypeDetail::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSubscribeResponse_descriptor_, MDSubscribeResponse::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSubscribe_2eproto() {
  MDSubscribeRequest_default_instance_.Shutdown();
  delete MDSubscribeRequest_reflection_;
  SubscribeAll_default_instance_.Shutdown();
  delete SubscribeAll_reflection_;
  SubscribeByID_default_instance_.Shutdown();
  delete SubscribeByID_reflection_;
  SubscribeByIDDetail_default_instance_.Shutdown();
  delete SubscribeByIDDetail_reflection_;
  SubscribeBySourceType_default_instance_.Shutdown();
  delete SubscribeBySourceType_reflection_;
  SubscribeBySourceTypeDetail_default_instance_.Shutdown();
  delete SubscribeBySourceTypeDetail_reflection_;
  MDSubscribeResponse_default_instance_.Shutdown();
  delete MDSubscribeResponse_reflection_;
}

void protobuf_InitDefaults_MDSubscribe_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_SecuritySourceType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_EMarketDataType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_InitDefaults_InsightErrorContext_2eproto();
  MDSubscribeRequest_default_instance_.DefaultConstruct();
  SubscribeAll_default_instance_.DefaultConstruct();
  SubscribeByID_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SubscribeByIDDetail_default_instance_.DefaultConstruct();
  SubscribeBySourceType_default_instance_.DefaultConstruct();
  SubscribeBySourceTypeDetail_default_instance_.DefaultConstruct();
  MDSubscribeResponse_default_instance_.DefaultConstruct();
  MDSubscribeRequest_default_instance_.get_mutable()->InitAsDefaultInstance();
  SubscribeAll_default_instance_.get_mutable()->InitAsDefaultInstance();
  SubscribeByID_default_instance_.get_mutable()->InitAsDefaultInstance();
  SubscribeByIDDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
  SubscribeBySourceType_default_instance_.get_mutable()->InitAsDefaultInstance();
  SubscribeBySourceTypeDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
  MDSubscribeResponse_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSubscribe_2eproto_once_);
void protobuf_InitDefaults_MDSubscribe_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSubscribe_2eproto_once_,
                 &protobuf_InitDefaults_MDSubscribe_2eproto_impl);
}
void protobuf_AddDesc_MDSubscribe_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSubscribe_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\021MDSubscribe.proto\022\032com.htsc.mdc.insigh"
    "t.model\032\030SecuritySourceType.proto\032\025EMark"
    "etDataType.proto\032\031InsightErrorContext.pr"
    "oto\"\267\002\n\022MDSubscribeRequest\022M\n\023subscribeA"
    "ctionType\030\001 \001(\01620.com.htsc.mdc.insight.m"
    "odel.ESubscribeActionType\022>\n\014subscribeAl"
    "l\030\002 \001(\0132(.com.htsc.mdc.insight.model.Sub"
    "scribeAll\022P\n\025subscribeBySourceType\030\003 \001(\013"
    "21.com.htsc.mdc.insight.model.SubscribeB"
    "ySourceType\022@\n\rsubscribeByID\030\004 \001(\0132).com"
    ".htsc.mdc.insight.model.SubscribeByID\"T\n"
    "\014SubscribeAll\022D\n\017marketDataTypes\030\001 \003(\0162+"
    ".com.htsc.mdc.insight.model.EMarketDataT"
    "ype\"\252\001\n\rSubscribeByID\022M\n\024subscribeByIDDe"
    "tails\030\001 \003(\0132/.com.htsc.mdc.insight.model"
    ".SubscribeByIDDetail\022J\n\025globalMarketData"
    "Types\030\002 \003(\0162+.com.htsc.mdc.insight.model"
    ".EMarketDataType\"s\n\023SubscribeByIDDetail\022"
    "\026\n\016htscSecurityID\030\001 \001(\t\022D\n\017marketDataTyp"
    "es\030\002 \003(\0162+.com.htsc.mdc.insight.model.EM"
    "arketDataType\"u\n\025SubscribeBySourceType\022\\"
    "\n\033subscribeBySourceTypeDetail\030\001 \003(\01327.co"
    "m.htsc.mdc.insight.model.SubscribeBySour"
    "ceTypeDetail\"\260\001\n\033SubscribeBySourceTypeDe"
    "tail\022K\n\023securitySourceTypes\030\001 \001(\0132..com."
    "htsc.mdc.insight.model.SecuritySourceTyp"
    "e\022D\n\017marketDataTypes\030\002 \003(\0162+.com.htsc.md"
    "c.insight.model.EMarketDataType\"o\n\023MDSub"
    "scribeResponse\022\021\n\tisSuccess\030\001 \001(\010\022E\n\014err"
    "orContext\030\002 \001(\0132/.com.htsc.mdc.insight.m"
    "odel.InsightErrorContext*G\n\024ESubscribeAc"
    "tionType\022\014\n\010COVERAGE\020\000\022\007\n\003ADD\020\001\022\014\n\010DECRE"
    "ASE\020\002\022\n\n\006CANCEL\020\003B4\n\032com.htsc.mdc.insigh"
    "t.modelB\021MDSubscribeProtosH\001\240\001\001b\006proto3", 1359);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSubscribe.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_SecuritySourceType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_EMarketDataType_2eproto();
  ::com::htsc::mdc::insight::model::protobuf_AddDesc_InsightErrorContext_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSubscribe_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSubscribe_2eproto_once_);
void protobuf_AddDesc_MDSubscribe_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSubscribe_2eproto_once_,
                 &protobuf_AddDesc_MDSubscribe_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSubscribe_2eproto {
  StaticDescriptorInitializer_MDSubscribe_2eproto() {
    protobuf_AddDesc_MDSubscribe_2eproto();
  }
} static_descriptor_initializer_MDSubscribe_2eproto_;
const ::google::protobuf::EnumDescriptor* ESubscribeActionType_descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ESubscribeActionType_descriptor_;
}
bool ESubscribeActionType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSubscribeRequest::kSubscribeActionTypeFieldNumber;
const int MDSubscribeRequest::kSubscribeAllFieldNumber;
const int MDSubscribeRequest::kSubscribeBySourceTypeFieldNumber;
const int MDSubscribeRequest::kSubscribeByIDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSubscribeRequest::MDSubscribeRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSubscribeRequest)
}

void MDSubscribeRequest::InitAsDefaultInstance() {
  subscribeall_ = const_cast< ::com::htsc::mdc::insight::model::SubscribeAll*>(
      ::com::htsc::mdc::insight::model::SubscribeAll::internal_default_instance());
  subscribebysourcetype_ = const_cast< ::com::htsc::mdc::insight::model::SubscribeBySourceType*>(
      ::com::htsc::mdc::insight::model::SubscribeBySourceType::internal_default_instance());
  subscribebyid_ = const_cast< ::com::htsc::mdc::insight::model::SubscribeByID*>(
      ::com::htsc::mdc::insight::model::SubscribeByID::internal_default_instance());
}

MDSubscribeRequest::MDSubscribeRequest(const MDSubscribeRequest& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSubscribeRequest)
}

void MDSubscribeRequest::SharedCtor() {
  subscribeall_ = NULL;
  subscribebysourcetype_ = NULL;
  subscribebyid_ = NULL;
  subscribeactiontype_ = 0;
  _cached_size_ = 0;
}

MDSubscribeRequest::~MDSubscribeRequest() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSubscribeRequest)
  SharedDtor();
}

void MDSubscribeRequest::SharedDtor() {
  if (this != &MDSubscribeRequest_default_instance_.get()) {
    delete subscribeall_;
    delete subscribebysourcetype_;
    delete subscribebyid_;
  }
}

void MDSubscribeRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSubscribeRequest::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSubscribeRequest_descriptor_;
}

const MDSubscribeRequest& MDSubscribeRequest::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSubscribeRequest> MDSubscribeRequest_default_instance_;

MDSubscribeRequest* MDSubscribeRequest::New(::google::protobuf::Arena* arena) const {
  MDSubscribeRequest* n = new MDSubscribeRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSubscribeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  subscribeactiontype_ = 0;
  if (GetArenaNoVirtual() == NULL && subscribeall_ != NULL) delete subscribeall_;
  subscribeall_ = NULL;
  if (GetArenaNoVirtual() == NULL && subscribebysourcetype_ != NULL) delete subscribebysourcetype_;
  subscribebysourcetype_ = NULL;
  if (GetArenaNoVirtual() == NULL && subscribebyid_ != NULL) delete subscribebyid_;
  subscribebyid_ = NULL;
}

bool MDSubscribeRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
      case 1: {
        if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_subscribeactiontype(static_cast< ::com::htsc::mdc::insight::model::ESubscribeActionType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_subscribeAll;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
      case 2: {
        if (tag == 18) {
         parse_subscribeAll:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_subscribeall()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_subscribeBySourceType;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
      case 3: {
        if (tag == 26) {
         parse_subscribeBySourceType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_subscribebysourcetype()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_subscribeByID;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
      case 4: {
        if (tag == 34) {
         parse_subscribeByID:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_subscribebyid()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSubscribeRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSubscribeRequest)
  return false;
#undef DO_
}

void MDSubscribeRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  // optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
  if (this->subscribeactiontype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->subscribeactiontype(), output);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
  if (this->has_subscribeall()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->subscribeall_, output);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
  if (this->has_subscribebysourcetype()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->subscribebysourcetype_, output);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
  if (this->has_subscribebyid()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->subscribebyid_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSubscribeRequest)
}

::google::protobuf::uint8* MDSubscribeRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  // optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
  if (this->subscribeactiontype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->subscribeactiontype(), target);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
  if (this->has_subscribeall()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->subscribeall_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
  if (this->has_subscribebysourcetype()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        3, *this->subscribebysourcetype_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
  if (this->has_subscribebyid()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        4, *this->subscribebyid_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSubscribeRequest)
  return target;
}

size_t MDSubscribeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  size_t total_size = 0;

  // optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
  if (this->subscribeactiontype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->subscribeactiontype());
  }

  // optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
  if (this->has_subscribeall()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->subscribeall_);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
  if (this->has_subscribebysourcetype()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->subscribebysourcetype_);
  }

  // optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
  if (this->has_subscribebyid()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->subscribebyid_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSubscribeRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSubscribeRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSubscribeRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSubscribeRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSubscribeRequest)
    UnsafeMergeFrom(*source);
  }
}

void MDSubscribeRequest::MergeFrom(const MDSubscribeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSubscribeRequest::UnsafeMergeFrom(const MDSubscribeRequest& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.subscribeactiontype() != 0) {
    set_subscribeactiontype(from.subscribeactiontype());
  }
  if (from.has_subscribeall()) {
    mutable_subscribeall()->::com::htsc::mdc::insight::model::SubscribeAll::MergeFrom(from.subscribeall());
  }
  if (from.has_subscribebysourcetype()) {
    mutable_subscribebysourcetype()->::com::htsc::mdc::insight::model::SubscribeBySourceType::MergeFrom(from.subscribebysourcetype());
  }
  if (from.has_subscribebyid()) {
    mutable_subscribebyid()->::com::htsc::mdc::insight::model::SubscribeByID::MergeFrom(from.subscribebyid());
  }
}

void MDSubscribeRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSubscribeRequest::CopyFrom(const MDSubscribeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSubscribeRequest)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSubscribeRequest::IsInitialized() const {

  return true;
}

void MDSubscribeRequest::Swap(MDSubscribeRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSubscribeRequest::InternalSwap(MDSubscribeRequest* other) {
  std::swap(subscribeactiontype_, other->subscribeactiontype_);
  std::swap(subscribeall_, other->subscribeall_);
  std::swap(subscribebysourcetype_, other->subscribebysourcetype_);
  std::swap(subscribebyid_, other->subscribebyid_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSubscribeRequest::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSubscribeRequest_descriptor_;
  metadata.reflection = MDSubscribeRequest_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSubscribeRequest

// optional .com.htsc.mdc.insight.model.ESubscribeActionType subscribeActionType = 1;
void MDSubscribeRequest::clear_subscribeactiontype() {
  subscribeactiontype_ = 0;
}
::com::htsc::mdc::insight::model::ESubscribeActionType MDSubscribeRequest::subscribeactiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeActionType)
  return static_cast< ::com::htsc::mdc::insight::model::ESubscribeActionType >(subscribeactiontype_);
}
void MDSubscribeRequest::set_subscribeactiontype(::com::htsc::mdc::insight::model::ESubscribeActionType value) {
  
  subscribeactiontype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeActionType)
}

// optional .com.htsc.mdc.insight.model.SubscribeAll subscribeAll = 2;
bool MDSubscribeRequest::has_subscribeall() const {
  return this != internal_default_instance() && subscribeall_ != NULL;
}
void MDSubscribeRequest::clear_subscribeall() {
  if (GetArenaNoVirtual() == NULL && subscribeall_ != NULL) delete subscribeall_;
  subscribeall_ = NULL;
}
const ::com::htsc::mdc::insight::model::SubscribeAll& MDSubscribeRequest::subscribeall() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  return subscribeall_ != NULL ? *subscribeall_
                         : *::com::htsc::mdc::insight::model::SubscribeAll::internal_default_instance();
}
::com::htsc::mdc::insight::model::SubscribeAll* MDSubscribeRequest::mutable_subscribeall() {
  
  if (subscribeall_ == NULL) {
    subscribeall_ = new ::com::htsc::mdc::insight::model::SubscribeAll;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  return subscribeall_;
}
::com::htsc::mdc::insight::model::SubscribeAll* MDSubscribeRequest::release_subscribeall() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
  
  ::com::htsc::mdc::insight::model::SubscribeAll* temp = subscribeall_;
  subscribeall_ = NULL;
  return temp;
}
void MDSubscribeRequest::set_allocated_subscribeall(::com::htsc::mdc::insight::model::SubscribeAll* subscribeall) {
  delete subscribeall_;
  subscribeall_ = subscribeall;
  if (subscribeall) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeAll)
}

// optional .com.htsc.mdc.insight.model.SubscribeBySourceType subscribeBySourceType = 3;
bool MDSubscribeRequest::has_subscribebysourcetype() const {
  return this != internal_default_instance() && subscribebysourcetype_ != NULL;
}
void MDSubscribeRequest::clear_subscribebysourcetype() {
  if (GetArenaNoVirtual() == NULL && subscribebysourcetype_ != NULL) delete subscribebysourcetype_;
  subscribebysourcetype_ = NULL;
}
const ::com::htsc::mdc::insight::model::SubscribeBySourceType& MDSubscribeRequest::subscribebysourcetype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  return subscribebysourcetype_ != NULL ? *subscribebysourcetype_
                         : *::com::htsc::mdc::insight::model::SubscribeBySourceType::internal_default_instance();
}
::com::htsc::mdc::insight::model::SubscribeBySourceType* MDSubscribeRequest::mutable_subscribebysourcetype() {
  
  if (subscribebysourcetype_ == NULL) {
    subscribebysourcetype_ = new ::com::htsc::mdc::insight::model::SubscribeBySourceType;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  return subscribebysourcetype_;
}
::com::htsc::mdc::insight::model::SubscribeBySourceType* MDSubscribeRequest::release_subscribebysourcetype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
  
  ::com::htsc::mdc::insight::model::SubscribeBySourceType* temp = subscribebysourcetype_;
  subscribebysourcetype_ = NULL;
  return temp;
}
void MDSubscribeRequest::set_allocated_subscribebysourcetype(::com::htsc::mdc::insight::model::SubscribeBySourceType* subscribebysourcetype) {
  delete subscribebysourcetype_;
  subscribebysourcetype_ = subscribebysourcetype;
  if (subscribebysourcetype) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeBySourceType)
}

// optional .com.htsc.mdc.insight.model.SubscribeByID subscribeByID = 4;
bool MDSubscribeRequest::has_subscribebyid() const {
  return this != internal_default_instance() && subscribebyid_ != NULL;
}
void MDSubscribeRequest::clear_subscribebyid() {
  if (GetArenaNoVirtual() == NULL && subscribebyid_ != NULL) delete subscribebyid_;
  subscribebyid_ = NULL;
}
const ::com::htsc::mdc::insight::model::SubscribeByID& MDSubscribeRequest::subscribebyid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  return subscribebyid_ != NULL ? *subscribebyid_
                         : *::com::htsc::mdc::insight::model::SubscribeByID::internal_default_instance();
}
::com::htsc::mdc::insight::model::SubscribeByID* MDSubscribeRequest::mutable_subscribebyid() {
  
  if (subscribebyid_ == NULL) {
    subscribebyid_ = new ::com::htsc::mdc::insight::model::SubscribeByID;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  return subscribebyid_;
}
::com::htsc::mdc::insight::model::SubscribeByID* MDSubscribeRequest::release_subscribebyid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
  
  ::com::htsc::mdc::insight::model::SubscribeByID* temp = subscribebyid_;
  subscribebyid_ = NULL;
  return temp;
}
void MDSubscribeRequest::set_allocated_subscribebyid(::com::htsc::mdc::insight::model::SubscribeByID* subscribebyid) {
  delete subscribebyid_;
  subscribebyid_ = subscribebyid;
  if (subscribebyid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeRequest.subscribeByID)
}

inline const MDSubscribeRequest* MDSubscribeRequest::internal_default_instance() {
  return &MDSubscribeRequest_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SubscribeAll::kMarketDataTypesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SubscribeAll::SubscribeAll()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SubscribeAll)
}

void SubscribeAll::InitAsDefaultInstance() {
}

SubscribeAll::SubscribeAll(const SubscribeAll& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SubscribeAll)
}

void SubscribeAll::SharedCtor() {
  _cached_size_ = 0;
}

SubscribeAll::~SubscribeAll() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SubscribeAll)
  SharedDtor();
}

void SubscribeAll::SharedDtor() {
}

void SubscribeAll::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SubscribeAll::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SubscribeAll_descriptor_;
}

const SubscribeAll& SubscribeAll::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SubscribeAll> SubscribeAll_default_instance_;

SubscribeAll* SubscribeAll::New(::google::protobuf::Arena* arena) const {
  SubscribeAll* n = new SubscribeAll;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SubscribeAll::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SubscribeAll)
  marketdatatypes_.Clear();
}

bool SubscribeAll::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SubscribeAll)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
      case 1: {
        if (tag == 10) {
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 8) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SubscribeAll)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SubscribeAll)
  return false;
#undef DO_
}

void SubscribeAll::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SubscribeAll)
  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
  if (this->marketdatatypes_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_marketdatatypes_cached_byte_size_);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->marketdatatypes(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SubscribeAll)
}

::google::protobuf::uint8* SubscribeAll::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SubscribeAll)
  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
  if (this->marketdatatypes_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _marketdatatypes_cached_byte_size_, target);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->marketdatatypes(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SubscribeAll)
  return target;
}

size_t SubscribeAll::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SubscribeAll)
  size_t total_size = 0;

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
  {
    size_t data_size = 0;
    unsigned int count = this->marketdatatypes_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->marketdatatypes(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _marketdatatypes_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SubscribeAll::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SubscribeAll)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SubscribeAll* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SubscribeAll>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SubscribeAll)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SubscribeAll)
    UnsafeMergeFrom(*source);
  }
}

void SubscribeAll::MergeFrom(const SubscribeAll& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SubscribeAll)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SubscribeAll::UnsafeMergeFrom(const SubscribeAll& from) {
  GOOGLE_DCHECK(&from != this);
  marketdatatypes_.UnsafeMergeFrom(from.marketdatatypes_);
}

void SubscribeAll::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SubscribeAll)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubscribeAll::CopyFrom(const SubscribeAll& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SubscribeAll)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SubscribeAll::IsInitialized() const {

  return true;
}

void SubscribeAll::Swap(SubscribeAll* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SubscribeAll::InternalSwap(SubscribeAll* other) {
  marketdatatypes_.UnsafeArenaSwap(&other->marketdatatypes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SubscribeAll::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SubscribeAll_descriptor_;
  metadata.reflection = SubscribeAll_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SubscribeAll

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 1;
int SubscribeAll::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
void SubscribeAll::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
::com::htsc::mdc::insight::model::EMarketDataType SubscribeAll::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
void SubscribeAll::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
}
void SubscribeAll::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
}
const ::google::protobuf::RepeatedField<int>&
SubscribeAll::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return marketdatatypes_;
}
::google::protobuf::RepeatedField<int>*
SubscribeAll::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeAll.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeAll* SubscribeAll::internal_default_instance() {
  return &SubscribeAll_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SubscribeByID::kSubscribeByIDDetailsFieldNumber;
const int SubscribeByID::kGlobalMarketDataTypesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SubscribeByID::SubscribeByID()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SubscribeByID)
}

void SubscribeByID::InitAsDefaultInstance() {
}

SubscribeByID::SubscribeByID(const SubscribeByID& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SubscribeByID)
}

void SubscribeByID::SharedCtor() {
  _cached_size_ = 0;
}

SubscribeByID::~SubscribeByID() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SubscribeByID)
  SharedDtor();
}

void SubscribeByID::SharedDtor() {
}

void SubscribeByID::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SubscribeByID::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SubscribeByID_descriptor_;
}

const SubscribeByID& SubscribeByID::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SubscribeByID> SubscribeByID_default_instance_;

SubscribeByID* SubscribeByID::New(::google::protobuf::Arena* arena) const {
  SubscribeByID* n = new SubscribeByID;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SubscribeByID::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SubscribeByID)
  subscribebyiddetails_.Clear();
  globalmarketdatatypes_.Clear();
}

bool SubscribeByID::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SubscribeByID)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_subscribeByIDDetails:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_subscribebyiddetails()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_subscribeByIDDetails;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(18)) goto parse_globalMarketDataTypes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
      case 2: {
        if (tag == 18) {
         parse_globalMarketDataTypes:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_globalmarketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 16) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_globalmarketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SubscribeByID)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SubscribeByID)
  return false;
#undef DO_
}

void SubscribeByID::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SubscribeByID)
  // repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
  for (unsigned int i = 0, n = this->subscribebyiddetails_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->subscribebyiddetails(i), output);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
  if (this->globalmarketdatatypes_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_globalmarketdatatypes_cached_byte_size_);
  }
  for (int i = 0; i < this->globalmarketdatatypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->globalmarketdatatypes(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SubscribeByID)
}

::google::protobuf::uint8* SubscribeByID::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SubscribeByID)
  // repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
  for (unsigned int i = 0, n = this->subscribebyiddetails_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->subscribebyiddetails(i), false, target);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
  if (this->globalmarketdatatypes_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _globalmarketdatatypes_cached_byte_size_, target);
  }
  for (int i = 0; i < this->globalmarketdatatypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->globalmarketdatatypes(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SubscribeByID)
  return target;
}

size_t SubscribeByID::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SubscribeByID)
  size_t total_size = 0;

  // repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
  {
    unsigned int count = this->subscribebyiddetails_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->subscribebyiddetails(i));
    }
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->globalmarketdatatypes_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->globalmarketdatatypes(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _globalmarketdatatypes_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SubscribeByID::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SubscribeByID)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SubscribeByID* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SubscribeByID>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SubscribeByID)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SubscribeByID)
    UnsafeMergeFrom(*source);
  }
}

void SubscribeByID::MergeFrom(const SubscribeByID& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SubscribeByID)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SubscribeByID::UnsafeMergeFrom(const SubscribeByID& from) {
  GOOGLE_DCHECK(&from != this);
  subscribebyiddetails_.MergeFrom(from.subscribebyiddetails_);
  globalmarketdatatypes_.UnsafeMergeFrom(from.globalmarketdatatypes_);
}

void SubscribeByID::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SubscribeByID)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubscribeByID::CopyFrom(const SubscribeByID& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SubscribeByID)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SubscribeByID::IsInitialized() const {

  return true;
}

void SubscribeByID::Swap(SubscribeByID* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SubscribeByID::InternalSwap(SubscribeByID* other) {
  subscribebyiddetails_.UnsafeArenaSwap(&other->subscribebyiddetails_);
  globalmarketdatatypes_.UnsafeArenaSwap(&other->globalmarketdatatypes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SubscribeByID::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SubscribeByID_descriptor_;
  metadata.reflection = SubscribeByID_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SubscribeByID

// repeated .com.htsc.mdc.insight.model.SubscribeByIDDetail subscribeByIDDetails = 1;
int SubscribeByID::subscribebyiddetails_size() const {
  return subscribebyiddetails_.size();
}
void SubscribeByID::clear_subscribebyiddetails() {
  subscribebyiddetails_.Clear();
}
const ::com::htsc::mdc::insight::model::SubscribeByIDDetail& SubscribeByID::subscribebyiddetails(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Get(index);
}
::com::htsc::mdc::insight::model::SubscribeByIDDetail* SubscribeByID::mutable_subscribebyiddetails(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Mutable(index);
}
::com::htsc::mdc::insight::model::SubscribeByIDDetail* SubscribeByID::add_subscribebyiddetails() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >*
SubscribeByID::mutable_subscribebyiddetails() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return &subscribebyiddetails_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeByIDDetail >&
SubscribeByID::subscribebyiddetails() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByID.subscribeByIDDetails)
  return subscribebyiddetails_;
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType globalMarketDataTypes = 2;
int SubscribeByID::globalmarketdatatypes_size() const {
  return globalmarketdatatypes_.size();
}
void SubscribeByID::clear_globalmarketdatatypes() {
  globalmarketdatatypes_.Clear();
}
::com::htsc::mdc::insight::model::EMarketDataType SubscribeByID::globalmarketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(globalmarketdatatypes_.Get(index));
}
void SubscribeByID::set_globalmarketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  globalmarketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
}
void SubscribeByID::add_globalmarketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  globalmarketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
}
const ::google::protobuf::RepeatedField<int>&
SubscribeByID::globalmarketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return globalmarketdatatypes_;
}
::google::protobuf::RepeatedField<int>*
SubscribeByID::mutable_globalmarketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByID.globalMarketDataTypes)
  return &globalmarketdatatypes_;
}

inline const SubscribeByID* SubscribeByID::internal_default_instance() {
  return &SubscribeByID_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SubscribeByIDDetail::kHtscSecurityIDFieldNumber;
const int SubscribeByIDDetail::kMarketDataTypesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SubscribeByIDDetail::SubscribeByIDDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SubscribeByIDDetail)
}

void SubscribeByIDDetail::InitAsDefaultInstance() {
}

SubscribeByIDDetail::SubscribeByIDDetail(const SubscribeByIDDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SubscribeByIDDetail)
}

void SubscribeByIDDetail::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

SubscribeByIDDetail::~SubscribeByIDDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  SharedDtor();
}

void SubscribeByIDDetail::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SubscribeByIDDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SubscribeByIDDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SubscribeByIDDetail_descriptor_;
}

const SubscribeByIDDetail& SubscribeByIDDetail::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SubscribeByIDDetail> SubscribeByIDDetail_default_instance_;

SubscribeByIDDetail* SubscribeByIDDetail::New(::google::protobuf::Arena* arena) const {
  SubscribeByIDDetail* n = new SubscribeByIDDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SubscribeByIDDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketdatatypes_.Clear();
}

bool SubscribeByIDDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string htscSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_marketDataTypes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
      case 2: {
        if (tag == 18) {
         parse_marketDataTypes:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 16) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  return false;
#undef DO_
}

void SubscribeByIDDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  // optional string htscSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  if (this->marketdatatypes_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_marketdatatypes_cached_byte_size_);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->marketdatatypes(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SubscribeByIDDetail)
}

::google::protobuf::uint8* SubscribeByIDDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  // optional string htscSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  if (this->marketdatatypes_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _marketdatatypes_cached_byte_size_, target);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->marketdatatypes(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  return target;
}

size_t SubscribeByIDDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  size_t total_size = 0;

  // optional string htscSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->marketdatatypes_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->marketdatatypes(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _marketdatatypes_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SubscribeByIDDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SubscribeByIDDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SubscribeByIDDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SubscribeByIDDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SubscribeByIDDetail)
    UnsafeMergeFrom(*source);
  }
}

void SubscribeByIDDetail::MergeFrom(const SubscribeByIDDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SubscribeByIDDetail::UnsafeMergeFrom(const SubscribeByIDDetail& from) {
  GOOGLE_DCHECK(&from != this);
  marketdatatypes_.UnsafeMergeFrom(from.marketdatatypes_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
}

void SubscribeByIDDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubscribeByIDDetail::CopyFrom(const SubscribeByIDDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SubscribeByIDDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SubscribeByIDDetail::IsInitialized() const {

  return true;
}

void SubscribeByIDDetail::Swap(SubscribeByIDDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SubscribeByIDDetail::InternalSwap(SubscribeByIDDetail* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  marketdatatypes_.UnsafeArenaSwap(&other->marketdatatypes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SubscribeByIDDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SubscribeByIDDetail_descriptor_;
  metadata.reflection = SubscribeByIDDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SubscribeByIDDetail

// optional string htscSecurityID = 1;
void SubscribeByIDDetail::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SubscribeByIDDetail::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SubscribeByIDDetail::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
void SubscribeByIDDetail::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
void SubscribeByIDDetail::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}
::std::string* SubscribeByIDDetail::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SubscribeByIDDetail::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SubscribeByIDDetail::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SubscribeByIDDetail.htscSecurityID)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
int SubscribeByIDDetail::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
void SubscribeByIDDetail::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
::com::htsc::mdc::insight::model::EMarketDataType SubscribeByIDDetail::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
void SubscribeByIDDetail::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
}
void SubscribeByIDDetail::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
}
const ::google::protobuf::RepeatedField<int>&
SubscribeByIDDetail::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return marketdatatypes_;
}
::google::protobuf::RepeatedField<int>*
SubscribeByIDDetail::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeByIDDetail.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeByIDDetail* SubscribeByIDDetail::internal_default_instance() {
  return &SubscribeByIDDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SubscribeBySourceType::kSubscribeBySourceTypeDetailFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SubscribeBySourceType::SubscribeBySourceType()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SubscribeBySourceType)
}

void SubscribeBySourceType::InitAsDefaultInstance() {
}

SubscribeBySourceType::SubscribeBySourceType(const SubscribeBySourceType& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SubscribeBySourceType)
}

void SubscribeBySourceType::SharedCtor() {
  _cached_size_ = 0;
}

SubscribeBySourceType::~SubscribeBySourceType() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SubscribeBySourceType)
  SharedDtor();
}

void SubscribeBySourceType::SharedDtor() {
}

void SubscribeBySourceType::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SubscribeBySourceType::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SubscribeBySourceType_descriptor_;
}

const SubscribeBySourceType& SubscribeBySourceType::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SubscribeBySourceType> SubscribeBySourceType_default_instance_;

SubscribeBySourceType* SubscribeBySourceType::New(::google::protobuf::Arena* arena) const {
  SubscribeBySourceType* n = new SubscribeBySourceType;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SubscribeBySourceType::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  subscribebysourcetypedetail_.Clear();
}

bool SubscribeBySourceType::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
      case 1: {
        if (tag == 10) {
          DO_(input->IncrementRecursionDepth());
         parse_loop_subscribeBySourceTypeDetail:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_subscribebysourcetypedetail()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(10)) goto parse_loop_subscribeBySourceTypeDetail;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SubscribeBySourceType)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SubscribeBySourceType)
  return false;
#undef DO_
}

void SubscribeBySourceType::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  // repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
  for (unsigned int i = 0, n = this->subscribebysourcetypedetail_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->subscribebysourcetypedetail(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SubscribeBySourceType)
}

::google::protobuf::uint8* SubscribeBySourceType::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  // repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
  for (unsigned int i = 0, n = this->subscribebysourcetypedetail_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, this->subscribebysourcetypedetail(i), false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SubscribeBySourceType)
  return target;
}

size_t SubscribeBySourceType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  size_t total_size = 0;

  // repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
  {
    unsigned int count = this->subscribebysourcetypedetail_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->subscribebysourcetypedetail(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SubscribeBySourceType::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SubscribeBySourceType* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SubscribeBySourceType>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SubscribeBySourceType)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SubscribeBySourceType)
    UnsafeMergeFrom(*source);
  }
}

void SubscribeBySourceType::MergeFrom(const SubscribeBySourceType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SubscribeBySourceType::UnsafeMergeFrom(const SubscribeBySourceType& from) {
  GOOGLE_DCHECK(&from != this);
  subscribebysourcetypedetail_.MergeFrom(from.subscribebysourcetypedetail_);
}

void SubscribeBySourceType::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubscribeBySourceType::CopyFrom(const SubscribeBySourceType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SubscribeBySourceType)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SubscribeBySourceType::IsInitialized() const {

  return true;
}

void SubscribeBySourceType::Swap(SubscribeBySourceType* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SubscribeBySourceType::InternalSwap(SubscribeBySourceType* other) {
  subscribebysourcetypedetail_.UnsafeArenaSwap(&other->subscribebysourcetypedetail_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SubscribeBySourceType::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SubscribeBySourceType_descriptor_;
  metadata.reflection = SubscribeBySourceType_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SubscribeBySourceType

// repeated .com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail subscribeBySourceTypeDetail = 1;
int SubscribeBySourceType::subscribebysourcetypedetail_size() const {
  return subscribebysourcetypedetail_.size();
}
void SubscribeBySourceType::clear_subscribebysourcetypedetail() {
  subscribebysourcetypedetail_.Clear();
}
const ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail& SubscribeBySourceType::subscribebysourcetypedetail(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Get(index);
}
::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* SubscribeBySourceType::mutable_subscribebysourcetypedetail(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Mutable(index);
}
::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail* SubscribeBySourceType::add_subscribebysourcetypedetail() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >*
SubscribeBySourceType::mutable_subscribebysourcetypedetail() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return &subscribebysourcetypedetail_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::SubscribeBySourceTypeDetail >&
SubscribeBySourceType::subscribebysourcetypedetail() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeBySourceType.subscribeBySourceTypeDetail)
  return subscribebysourcetypedetail_;
}

inline const SubscribeBySourceType* SubscribeBySourceType::internal_default_instance() {
  return &SubscribeBySourceType_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SubscribeBySourceTypeDetail::kSecuritySourceTypesFieldNumber;
const int SubscribeBySourceTypeDetail::kMarketDataTypesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SubscribeBySourceTypeDetail::SubscribeBySourceTypeDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
}

void SubscribeBySourceTypeDetail::InitAsDefaultInstance() {
  securitysourcetypes_ = const_cast< ::com::htsc::mdc::insight::model::SecuritySourceType*>(
      ::com::htsc::mdc::insight::model::SecuritySourceType::internal_default_instance());
}

SubscribeBySourceTypeDetail::SubscribeBySourceTypeDetail(const SubscribeBySourceTypeDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
}

void SubscribeBySourceTypeDetail::SharedCtor() {
  securitysourcetypes_ = NULL;
  _cached_size_ = 0;
}

SubscribeBySourceTypeDetail::~SubscribeBySourceTypeDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  SharedDtor();
}

void SubscribeBySourceTypeDetail::SharedDtor() {
  if (this != &SubscribeBySourceTypeDetail_default_instance_.get()) {
    delete securitysourcetypes_;
  }
}

void SubscribeBySourceTypeDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SubscribeBySourceTypeDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SubscribeBySourceTypeDetail_descriptor_;
}

const SubscribeBySourceTypeDetail& SubscribeBySourceTypeDetail::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SubscribeBySourceTypeDetail> SubscribeBySourceTypeDetail_default_instance_;

SubscribeBySourceTypeDetail* SubscribeBySourceTypeDetail::New(::google::protobuf::Arena* arena) const {
  SubscribeBySourceTypeDetail* n = new SubscribeBySourceTypeDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SubscribeBySourceTypeDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  if (GetArenaNoVirtual() == NULL && securitysourcetypes_ != NULL) delete securitysourcetypes_;
  securitysourcetypes_ = NULL;
  marketdatatypes_.Clear();
}

bool SubscribeBySourceTypeDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_securitysourcetypes()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_marketDataTypes;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
      case 2: {
        if (tag == 18) {
         parse_marketDataTypes:
          ::google::protobuf::uint32 length;
          DO_(input->ReadVarint32(&length));
          ::google::protobuf::io::CodedInputStream::Limit limit = input->PushLimit(length);
          while (input->BytesUntilLimit() > 0) {
            int value;
            DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
            add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
          }
          input->PopLimit(limit);
        } else if (tag == 16) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          add_marketdatatypes(static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  return false;
#undef DO_
}

void SubscribeBySourceTypeDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  // optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
  if (this->has_securitysourcetypes()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->securitysourcetypes_, output);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  if (this->marketdatatypes_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      output);
    output->WriteVarint32(_marketdatatypes_cached_byte_size_);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteEnumNoTag(
      this->marketdatatypes(i), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
}

::google::protobuf::uint8* SubscribeBySourceTypeDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  // optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
  if (this->has_securitysourcetypes()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        1, *this->securitysourcetypes_, false, target);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  if (this->marketdatatypes_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(    _marketdatatypes_cached_byte_size_, target);
  }
  for (int i = 0; i < this->marketdatatypes_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumNoTagToArray(
      this->marketdatatypes(i), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  return target;
}

size_t SubscribeBySourceTypeDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  size_t total_size = 0;

  // optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
  if (this->has_securitysourcetypes()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->securitysourcetypes_);
  }

  // repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
  {
    size_t data_size = 0;
    unsigned int count = this->marketdatatypes_size();for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::EnumSize(
        this->marketdatatypes(i));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _marketdatatypes_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SubscribeBySourceTypeDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SubscribeBySourceTypeDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SubscribeBySourceTypeDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
    UnsafeMergeFrom(*source);
  }
}

void SubscribeBySourceTypeDetail::MergeFrom(const SubscribeBySourceTypeDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SubscribeBySourceTypeDetail::UnsafeMergeFrom(const SubscribeBySourceTypeDetail& from) {
  GOOGLE_DCHECK(&from != this);
  marketdatatypes_.UnsafeMergeFrom(from.marketdatatypes_);
  if (from.has_securitysourcetypes()) {
    mutable_securitysourcetypes()->::com::htsc::mdc::insight::model::SecuritySourceType::MergeFrom(from.securitysourcetypes());
  }
}

void SubscribeBySourceTypeDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubscribeBySourceTypeDetail::CopyFrom(const SubscribeBySourceTypeDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SubscribeBySourceTypeDetail::IsInitialized() const {

  return true;
}

void SubscribeBySourceTypeDetail::Swap(SubscribeBySourceTypeDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SubscribeBySourceTypeDetail::InternalSwap(SubscribeBySourceTypeDetail* other) {
  std::swap(securitysourcetypes_, other->securitysourcetypes_);
  marketdatatypes_.UnsafeArenaSwap(&other->marketdatatypes_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SubscribeBySourceTypeDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SubscribeBySourceTypeDetail_descriptor_;
  metadata.reflection = SubscribeBySourceTypeDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SubscribeBySourceTypeDetail

// optional .com.htsc.mdc.insight.model.SecuritySourceType securitySourceTypes = 1;
bool SubscribeBySourceTypeDetail::has_securitysourcetypes() const {
  return this != internal_default_instance() && securitysourcetypes_ != NULL;
}
void SubscribeBySourceTypeDetail::clear_securitysourcetypes() {
  if (GetArenaNoVirtual() == NULL && securitysourcetypes_ != NULL) delete securitysourcetypes_;
  securitysourcetypes_ = NULL;
}
const ::com::htsc::mdc::insight::model::SecuritySourceType& SubscribeBySourceTypeDetail::securitysourcetypes() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  return securitysourcetypes_ != NULL ? *securitysourcetypes_
                         : *::com::htsc::mdc::insight::model::SecuritySourceType::internal_default_instance();
}
::com::htsc::mdc::insight::model::SecuritySourceType* SubscribeBySourceTypeDetail::mutable_securitysourcetypes() {
  
  if (securitysourcetypes_ == NULL) {
    securitysourcetypes_ = new ::com::htsc::mdc::insight::model::SecuritySourceType;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  return securitysourcetypes_;
}
::com::htsc::mdc::insight::model::SecuritySourceType* SubscribeBySourceTypeDetail::release_securitysourcetypes() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
  
  ::com::htsc::mdc::insight::model::SecuritySourceType* temp = securitysourcetypes_;
  securitysourcetypes_ = NULL;
  return temp;
}
void SubscribeBySourceTypeDetail::set_allocated_securitysourcetypes(::com::htsc::mdc::insight::model::SecuritySourceType* securitysourcetypes) {
  delete securitysourcetypes_;
  securitysourcetypes_ = securitysourcetypes;
  if (securitysourcetypes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.securitySourceTypes)
}

// repeated .com.htsc.mdc.insight.model.EMarketDataType marketDataTypes = 2;
int SubscribeBySourceTypeDetail::marketdatatypes_size() const {
  return marketdatatypes_.size();
}
void SubscribeBySourceTypeDetail::clear_marketdatatypes() {
  marketdatatypes_.Clear();
}
::com::htsc::mdc::insight::model::EMarketDataType SubscribeBySourceTypeDetail::marketdatatypes(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return static_cast< ::com::htsc::mdc::insight::model::EMarketDataType >(marketdatatypes_.Get(index));
}
void SubscribeBySourceTypeDetail::set_marketdatatypes(int index, ::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
}
void SubscribeBySourceTypeDetail::add_marketdatatypes(::com::htsc::mdc::insight::model::EMarketDataType value) {
  marketdatatypes_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
}
const ::google::protobuf::RepeatedField<int>&
SubscribeBySourceTypeDetail::marketdatatypes() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return marketdatatypes_;
}
::google::protobuf::RepeatedField<int>*
SubscribeBySourceTypeDetail::mutable_marketdatatypes() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.SubscribeBySourceTypeDetail.marketDataTypes)
  return &marketdatatypes_;
}

inline const SubscribeBySourceTypeDetail* SubscribeBySourceTypeDetail::internal_default_instance() {
  return &SubscribeBySourceTypeDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSubscribeResponse::kIsSuccessFieldNumber;
const int MDSubscribeResponse::kErrorContextFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSubscribeResponse::MDSubscribeResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSubscribe_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSubscribeResponse)
}

void MDSubscribeResponse::InitAsDefaultInstance() {
  errorcontext_ = const_cast< ::com::htsc::mdc::insight::model::InsightErrorContext*>(
      ::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance());
}

MDSubscribeResponse::MDSubscribeResponse(const MDSubscribeResponse& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSubscribeResponse)
}

void MDSubscribeResponse::SharedCtor() {
  errorcontext_ = NULL;
  issuccess_ = false;
  _cached_size_ = 0;
}

MDSubscribeResponse::~MDSubscribeResponse() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSubscribeResponse)
  SharedDtor();
}

void MDSubscribeResponse::SharedDtor() {
  if (this != &MDSubscribeResponse_default_instance_.get()) {
    delete errorcontext_;
  }
}

void MDSubscribeResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSubscribeResponse::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSubscribeResponse_descriptor_;
}

const MDSubscribeResponse& MDSubscribeResponse::default_instance() {
  protobuf_InitDefaults_MDSubscribe_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSubscribeResponse> MDSubscribeResponse_default_instance_;

MDSubscribeResponse* MDSubscribeResponse::New(::google::protobuf::Arena* arena) const {
  MDSubscribeResponse* n = new MDSubscribeResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSubscribeResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  issuccess_ = false;
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}

bool MDSubscribeResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional bool isSuccess = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &issuccess_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_errorContext;
        break;
      }

      // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
      case 2: {
        if (tag == 18) {
         parse_errorContext:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_errorcontext()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSubscribeResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSubscribeResponse)
  return false;
#undef DO_
}

void MDSubscribeResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->issuccess(), output);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->errorcontext_, output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSubscribeResponse)
}

::google::protobuf::uint8* MDSubscribeResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->issuccess(), target);
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        2, *this->errorcontext_, false, target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSubscribeResponse)
  return target;
}

size_t MDSubscribeResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  size_t total_size = 0;

  // optional bool isSuccess = 1;
  if (this->issuccess() != 0) {
    total_size += 1 + 1;
  }

  // optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
  if (this->has_errorcontext()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->errorcontext_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSubscribeResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSubscribeResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSubscribeResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSubscribeResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSubscribeResponse)
    UnsafeMergeFrom(*source);
  }
}

void MDSubscribeResponse::MergeFrom(const MDSubscribeResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSubscribeResponse::UnsafeMergeFrom(const MDSubscribeResponse& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.issuccess() != 0) {
    set_issuccess(from.issuccess());
  }
  if (from.has_errorcontext()) {
    mutable_errorcontext()->::com::htsc::mdc::insight::model::InsightErrorContext::MergeFrom(from.errorcontext());
  }
}

void MDSubscribeResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSubscribeResponse::CopyFrom(const MDSubscribeResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSubscribeResponse)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSubscribeResponse::IsInitialized() const {

  return true;
}

void MDSubscribeResponse::Swap(MDSubscribeResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSubscribeResponse::InternalSwap(MDSubscribeResponse* other) {
  std::swap(issuccess_, other->issuccess_);
  std::swap(errorcontext_, other->errorcontext_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSubscribeResponse::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSubscribeResponse_descriptor_;
  metadata.reflection = MDSubscribeResponse_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSubscribeResponse

// optional bool isSuccess = 1;
void MDSubscribeResponse::clear_issuccess() {
  issuccess_ = false;
}
bool MDSubscribeResponse::issuccess() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeResponse.isSuccess)
  return issuccess_;
}
void MDSubscribeResponse::set_issuccess(bool value) {
  
  issuccess_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSubscribeResponse.isSuccess)
}

// optional .com.htsc.mdc.insight.model.InsightErrorContext errorContext = 2;
bool MDSubscribeResponse::has_errorcontext() const {
  return this != internal_default_instance() && errorcontext_ != NULL;
}
void MDSubscribeResponse::clear_errorcontext() {
  if (GetArenaNoVirtual() == NULL && errorcontext_ != NULL) delete errorcontext_;
  errorcontext_ = NULL;
}
const ::com::htsc::mdc::insight::model::InsightErrorContext& MDSubscribeResponse::errorcontext() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  return errorcontext_ != NULL ? *errorcontext_
                         : *::com::htsc::mdc::insight::model::InsightErrorContext::internal_default_instance();
}
::com::htsc::mdc::insight::model::InsightErrorContext* MDSubscribeResponse::mutable_errorcontext() {
  
  if (errorcontext_ == NULL) {
    errorcontext_ = new ::com::htsc::mdc::insight::model::InsightErrorContext;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  return errorcontext_;
}
::com::htsc::mdc::insight::model::InsightErrorContext* MDSubscribeResponse::release_errorcontext() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
  
  ::com::htsc::mdc::insight::model::InsightErrorContext* temp = errorcontext_;
  errorcontext_ = NULL;
  return temp;
}
void MDSubscribeResponse::set_allocated_errorcontext(::com::htsc::mdc::insight::model::InsightErrorContext* errorcontext) {
  delete errorcontext_;
  errorcontext_ = errorcontext;
  if (errorcontext) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSubscribeResponse.errorContext)
}

inline const MDSubscribeResponse* MDSubscribeResponse::internal_default_instance() {
  return &MDSubscribeResponse_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
