// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDSpot.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDSpot.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDSpot_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDSpot_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDSpot_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDSpot_2eproto() {
  protobuf_AddDesc_MDSpot_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDSpot.proto");
  GOOGLE_CHECK(file != NULL);
  MDSpot_descriptor_ = file->message_type(0);
  static const int MDSpot_offsets_[47] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, totalweighttrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, tradingdate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, preopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, presettleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, openinterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, settleprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, initopeninterest_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, interestchg_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, averagepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, lifehighpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, lifelowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buyimplyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellimplyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, commoditycontractnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, datamultiplepowerof10_),
  };
  MDSpot_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDSpot_descriptor_,
      MDSpot::internal_default_instance(),
      MDSpot_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDSpot),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDSpot, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDSpot_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDSpot_descriptor_, MDSpot::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDSpot_2eproto() {
  MDSpot_default_instance_.Shutdown();
  delete MDSpot_reflection_;
}

void protobuf_InitDefaults_MDSpot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDSpot_default_instance_.DefaultConstruct();
  MDSpot_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDSpot_2eproto_once_);
void protobuf_InitDefaults_MDSpot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDSpot_2eproto_once_,
                 &protobuf_InitDefaults_MDSpot_2eproto_impl);
}
void protobuf_AddDesc_MDSpot_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDSpot_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\014MDSpot.proto\022\032com.htsc.mdc.insight.mod"
    "el\032\027ESecurityIDSource.proto\032\023ESecurityTy"
    "pe.proto\"\350\010\n\006MDSpot\022\026\n\016HTSCSecurityID\030\001 "
    "\001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rD"
    "ataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode\030\005"
    " \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.hts"
    "c.mdc.model.ESecurityIDSource\0227\n\014securit"
    "yType\030\007 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022\n\n"
    "PreClosePx\030\n \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022\030\n\020T"
    "otalVolumeTrade\030\014 \001(\003\022\027\n\017TotalValueTrade"
    "\030\r \001(\003\022\030\n\020TotalWeightTrade\030\016 \001(\003\022\016\n\006Last"
    "Px\030\017 \001(\003\022\016\n\006OpenPx\030\020 \001(\003\022\017\n\007ClosePx\030\021 \001("
    "\003\022\016\n\006HighPx\030\022 \001(\003\022\r\n\005LowPx\030\023 \001(\003\022\023\n\013Trad"
    "ingDate\030\024 \001(\005\022\027\n\017PreOpenInterest\030\025 \001(\003\022\026"
    "\n\016PreSettlePrice\030\026 \001(\003\022\024\n\014OpenInterest\030\027"
    " \001(\003\022\023\n\013SettlePrice\030\030 \001(\003\022\030\n\020InitOpenInt"
    "erest\030\031 \001(\003\022\023\n\013InterestChg\030\032 \001(\003\022\021\n\tAver"
    "agePx\030\033 \001(\003\022\022\n\nLifeHighPx\030\034 \001(\003\022\021\n\tLifeL"
    "owPx\030\035 \001(\003\022\r\n\005BuyPx\030\036 \001(\003\022\016\n\006BuyQty\030\037 \001("
    "\003\022\023\n\013BuyImplyQty\030  \001(\003\022\016\n\006SellPx\030! \001(\003\022\017"
    "\n\007SellQty\030\" \001(\003\022\024\n\014SellImplyQty\030# \001(\003\022\037\n"
    "\027CommodityContractNumber\030$ \001(\t\022\024\n\014Exchan"
    "geDate\030% \001(\005\022\024\n\014ExchangeTime\030& \001(\005\022\031\n\rBu"
    "yPriceQueue\0303 \003(\003B\002\020\001\022\034\n\020BuyOrderQtyQueu"
    "e\0304 \003(\003B\002\020\001\022\032\n\016SellPriceQueue\0305 \003(\003B\002\020\001\022"
    "\035\n\021SellOrderQtyQueue\0306 \003(\003B\002\020\001\022\031\n\rBuyOrd"
    "erQueue\0307 \003(\003B\002\020\001\022\032\n\016SellOrderQueue\0308 \003("
    "\003B\002\020\001\022\035\n\021BuyNumOrdersQueue\0309 \003(\003B\002\020\001\022\036\n\022"
    "SellNumOrdersQueue\030: \003(\003B\002\020\001\022\035\n\025DataMult"
    "iplePowerOf10\030; \001(\005B/\n\032com.htsc.mdc.insi"
    "ght.modelB\014MDSpotProtosH\001\240\001\001b\006proto3", 1276);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDSpot.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDSpot_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDSpot_2eproto_once_);
void protobuf_AddDesc_MDSpot_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDSpot_2eproto_once_,
                 &protobuf_AddDesc_MDSpot_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDSpot_2eproto {
  StaticDescriptorInitializer_MDSpot_2eproto() {
    protobuf_AddDesc_MDSpot_2eproto();
  }
} static_descriptor_initializer_MDSpot_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDSpot::kHTSCSecurityIDFieldNumber;
const int MDSpot::kMDDateFieldNumber;
const int MDSpot::kMDTimeFieldNumber;
const int MDSpot::kDataTimestampFieldNumber;
const int MDSpot::kTradingPhaseCodeFieldNumber;
const int MDSpot::kSecurityIDSourceFieldNumber;
const int MDSpot::kSecurityTypeFieldNumber;
const int MDSpot::kMaxPxFieldNumber;
const int MDSpot::kMinPxFieldNumber;
const int MDSpot::kPreClosePxFieldNumber;
const int MDSpot::kNumTradesFieldNumber;
const int MDSpot::kTotalVolumeTradeFieldNumber;
const int MDSpot::kTotalValueTradeFieldNumber;
const int MDSpot::kTotalWeightTradeFieldNumber;
const int MDSpot::kLastPxFieldNumber;
const int MDSpot::kOpenPxFieldNumber;
const int MDSpot::kClosePxFieldNumber;
const int MDSpot::kHighPxFieldNumber;
const int MDSpot::kLowPxFieldNumber;
const int MDSpot::kTradingDateFieldNumber;
const int MDSpot::kPreOpenInterestFieldNumber;
const int MDSpot::kPreSettlePriceFieldNumber;
const int MDSpot::kOpenInterestFieldNumber;
const int MDSpot::kSettlePriceFieldNumber;
const int MDSpot::kInitOpenInterestFieldNumber;
const int MDSpot::kInterestChgFieldNumber;
const int MDSpot::kAveragePxFieldNumber;
const int MDSpot::kLifeHighPxFieldNumber;
const int MDSpot::kLifeLowPxFieldNumber;
const int MDSpot::kBuyPxFieldNumber;
const int MDSpot::kBuyQtyFieldNumber;
const int MDSpot::kBuyImplyQtyFieldNumber;
const int MDSpot::kSellPxFieldNumber;
const int MDSpot::kSellQtyFieldNumber;
const int MDSpot::kSellImplyQtyFieldNumber;
const int MDSpot::kCommodityContractNumberFieldNumber;
const int MDSpot::kExchangeDateFieldNumber;
const int MDSpot::kExchangeTimeFieldNumber;
const int MDSpot::kBuyPriceQueueFieldNumber;
const int MDSpot::kBuyOrderQtyQueueFieldNumber;
const int MDSpot::kSellPriceQueueFieldNumber;
const int MDSpot::kSellOrderQtyQueueFieldNumber;
const int MDSpot::kBuyOrderQueueFieldNumber;
const int MDSpot::kSellOrderQueueFieldNumber;
const int MDSpot::kBuyNumOrdersQueueFieldNumber;
const int MDSpot::kSellNumOrdersQueueFieldNumber;
const int MDSpot::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDSpot::MDSpot()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDSpot_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDSpot)
}

void MDSpot::InitAsDefaultInstance() {
}

MDSpot::MDSpot(const MDSpot& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDSpot)
}

void MDSpot::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDSpot::~MDSpot() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDSpot)
  SharedDtor();
}

void MDSpot::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  commoditycontractnumber_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDSpot::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDSpot::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDSpot_descriptor_;
}

const MDSpot& MDSpot::default_instance() {
  protobuf_InitDefaults_MDSpot_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDSpot> MDSpot_default_instance_;

MDSpot* MDSpot::New(::google::protobuf::Arena* arena) const {
  MDSpot* n = new MDSpot;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDSpot::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDSpot)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDSpot, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDSpot*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, openpx_);
  ZR_(closepx_, settleprice_);
  tradingdate_ = 0;
  ZR_(initopeninterest_, lifehighpx_);
  ZR_(lifelowpx_, buyimplyqty_);
  ZR_(sellpx_, exchangetime_);
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  exchangedate_ = 0;
  datamultiplepowerof10_ = 0;

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
}

bool MDSpot::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDSpot)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_TotalWeightTrade;
        break;
      }

      // optional int64 TotalWeightTrade = 14;
      case 14: {
        if (tag == 112) {
         parse_TotalWeightTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalweighttrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 15;
      case 15: {
        if (tag == 120) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 16;
      case 16: {
        if (tag == 128) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 17;
      case 17: {
        if (tag == 136) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 18;
      case 18: {
        if (tag == 144) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 19;
      case 19: {
        if (tag == 152) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_TradingDate;
        break;
      }

      // optional int32 TradingDate = 20;
      case 20: {
        if (tag == 160) {
         parse_TradingDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &tradingdate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_PreOpenInterest;
        break;
      }

      // optional int64 PreOpenInterest = 21;
      case 21: {
        if (tag == 168) {
         parse_PreOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_PreSettlePrice;
        break;
      }

      // optional int64 PreSettlePrice = 22;
      case 22: {
        if (tag == 176) {
         parse_PreSettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &presettleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_OpenInterest;
        break;
      }

      // optional int64 OpenInterest = 23;
      case 23: {
        if (tag == 184) {
         parse_OpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openinterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_SettlePrice;
        break;
      }

      // optional int64 SettlePrice = 24;
      case 24: {
        if (tag == 192) {
         parse_SettlePrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &settleprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_InitOpenInterest;
        break;
      }

      // optional int64 InitOpenInterest = 25;
      case 25: {
        if (tag == 200) {
         parse_InitOpenInterest:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &initopeninterest_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_InterestChg;
        break;
      }

      // optional int64 InterestChg = 26;
      case 26: {
        if (tag == 208) {
         parse_InterestChg:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &interestchg_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_AveragePx;
        break;
      }

      // optional int64 AveragePx = 27;
      case 27: {
        if (tag == 216) {
         parse_AveragePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &averagepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_LifeHighPx;
        break;
      }

      // optional int64 LifeHighPx = 28;
      case 28: {
        if (tag == 224) {
         parse_LifeHighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lifehighpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_LifeLowPx;
        break;
      }

      // optional int64 LifeLowPx = 29;
      case 29: {
        if (tag == 232) {
         parse_LifeLowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lifelowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_BuyPx;
        break;
      }

      // optional int64 BuyPx = 30;
      case 30: {
        if (tag == 240) {
         parse_BuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_BuyQty;
        break;
      }

      // optional int64 BuyQty = 31;
      case 31: {
        if (tag == 248) {
         parse_BuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_BuyImplyQty;
        break;
      }

      // optional int64 BuyImplyQty = 32;
      case 32: {
        if (tag == 256) {
         parse_BuyImplyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyimplyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_SellPx;
        break;
      }

      // optional int64 SellPx = 33;
      case 33: {
        if (tag == 264) {
         parse_SellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_SellQty;
        break;
      }

      // optional int64 SellQty = 34;
      case 34: {
        if (tag == 272) {
         parse_SellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_SellImplyQty;
        break;
      }

      // optional int64 SellImplyQty = 35;
      case 35: {
        if (tag == 280) {
         parse_SellImplyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellimplyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(290)) goto parse_CommodityContractNumber;
        break;
      }

      // optional string CommodityContractNumber = 36;
      case 36: {
        if (tag == 290) {
         parse_CommodityContractNumber:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_commoditycontractnumber()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 37;
      case 37: {
        if (tag == 296) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 38;
      case 38: {
        if (tag == 304) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 59;
      case 59: {
        if (tag == 472) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDSpot)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDSpot)
  return false;
#undef DO_
}

void MDSpot::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDSpot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 TotalWeightTrade = 14;
  if (this->totalweighttrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->totalweighttrade(), output);
  }

  // optional int64 LastPx = 15;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->lastpx(), output);
  }

  // optional int64 OpenPx = 16;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->openpx(), output);
  }

  // optional int64 ClosePx = 17;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->closepx(), output);
  }

  // optional int64 HighPx = 18;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->highpx(), output);
  }

  // optional int64 LowPx = 19;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->lowpx(), output);
  }

  // optional int32 TradingDate = 20;
  if (this->tradingdate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->tradingdate(), output);
  }

  // optional int64 PreOpenInterest = 21;
  if (this->preopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->preopeninterest(), output);
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->presettleprice(), output);
  }

  // optional int64 OpenInterest = 23;
  if (this->openinterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->openinterest(), output);
  }

  // optional int64 SettlePrice = 24;
  if (this->settleprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->settleprice(), output);
  }

  // optional int64 InitOpenInterest = 25;
  if (this->initopeninterest() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->initopeninterest(), output);
  }

  // optional int64 InterestChg = 26;
  if (this->interestchg() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->interestchg(), output);
  }

  // optional int64 AveragePx = 27;
  if (this->averagepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->averagepx(), output);
  }

  // optional int64 LifeHighPx = 28;
  if (this->lifehighpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->lifehighpx(), output);
  }

  // optional int64 LifeLowPx = 29;
  if (this->lifelowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->lifelowpx(), output);
  }

  // optional int64 BuyPx = 30;
  if (this->buypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->buypx(), output);
  }

  // optional int64 BuyQty = 31;
  if (this->buyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->buyqty(), output);
  }

  // optional int64 BuyImplyQty = 32;
  if (this->buyimplyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->buyimplyqty(), output);
  }

  // optional int64 SellPx = 33;
  if (this->sellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->sellpx(), output);
  }

  // optional int64 SellQty = 34;
  if (this->sellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->sellqty(), output);
  }

  // optional int64 SellImplyQty = 35;
  if (this->sellimplyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(35, this->sellimplyqty(), output);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      36, this->commoditycontractnumber(), output);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(37, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(38, this->exchangetime(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(59, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDSpot)
}

::google::protobuf::uint8* MDSpot::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDSpot)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 TotalWeightTrade = 14;
  if (this->totalweighttrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->totalweighttrade(), target);
  }

  // optional int64 LastPx = 15;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->lastpx(), target);
  }

  // optional int64 OpenPx = 16;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->openpx(), target);
  }

  // optional int64 ClosePx = 17;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->closepx(), target);
  }

  // optional int64 HighPx = 18;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->highpx(), target);
  }

  // optional int64 LowPx = 19;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->lowpx(), target);
  }

  // optional int32 TradingDate = 20;
  if (this->tradingdate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->tradingdate(), target);
  }

  // optional int64 PreOpenInterest = 21;
  if (this->preopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->preopeninterest(), target);
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->presettleprice(), target);
  }

  // optional int64 OpenInterest = 23;
  if (this->openinterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->openinterest(), target);
  }

  // optional int64 SettlePrice = 24;
  if (this->settleprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->settleprice(), target);
  }

  // optional int64 InitOpenInterest = 25;
  if (this->initopeninterest() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->initopeninterest(), target);
  }

  // optional int64 InterestChg = 26;
  if (this->interestchg() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->interestchg(), target);
  }

  // optional int64 AveragePx = 27;
  if (this->averagepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->averagepx(), target);
  }

  // optional int64 LifeHighPx = 28;
  if (this->lifehighpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->lifehighpx(), target);
  }

  // optional int64 LifeLowPx = 29;
  if (this->lifelowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->lifelowpx(), target);
  }

  // optional int64 BuyPx = 30;
  if (this->buypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->buypx(), target);
  }

  // optional int64 BuyQty = 31;
  if (this->buyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->buyqty(), target);
  }

  // optional int64 BuyImplyQty = 32;
  if (this->buyimplyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->buyimplyqty(), target);
  }

  // optional int64 SellPx = 33;
  if (this->sellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->sellpx(), target);
  }

  // optional int64 SellQty = 34;
  if (this->sellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->sellqty(), target);
  }

  // optional int64 SellImplyQty = 35;
  if (this->sellimplyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(35, this->sellimplyqty(), target);
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->commoditycontractnumber().data(), this->commoditycontractnumber().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        36, this->commoditycontractnumber(), target);
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(37, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(38, this->exchangetime(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(59, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDSpot)
  return target;
}

size_t MDSpot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDSpot)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 TotalWeightTrade = 14;
  if (this->totalweighttrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalweighttrade());
  }

  // optional int64 LastPx = 15;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 16;
  if (this->openpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 17;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 18;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 19;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int32 TradingDate = 20;
  if (this->tradingdate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->tradingdate());
  }

  // optional int64 PreOpenInterest = 21;
  if (this->preopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preopeninterest());
  }

  // optional int64 PreSettlePrice = 22;
  if (this->presettleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->presettleprice());
  }

  // optional int64 OpenInterest = 23;
  if (this->openinterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openinterest());
  }

  // optional int64 SettlePrice = 24;
  if (this->settleprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->settleprice());
  }

  // optional int64 InitOpenInterest = 25;
  if (this->initopeninterest() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->initopeninterest());
  }

  // optional int64 InterestChg = 26;
  if (this->interestchg() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->interestchg());
  }

  // optional int64 AveragePx = 27;
  if (this->averagepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->averagepx());
  }

  // optional int64 LifeHighPx = 28;
  if (this->lifehighpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lifehighpx());
  }

  // optional int64 LifeLowPx = 29;
  if (this->lifelowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lifelowpx());
  }

  // optional int64 BuyPx = 30;
  if (this->buypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buypx());
  }

  // optional int64 BuyQty = 31;
  if (this->buyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyqty());
  }

  // optional int64 BuyImplyQty = 32;
  if (this->buyimplyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyimplyqty());
  }

  // optional int64 SellPx = 33;
  if (this->sellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellpx());
  }

  // optional int64 SellQty = 34;
  if (this->sellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellqty());
  }

  // optional int64 SellImplyQty = 35;
  if (this->sellimplyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellimplyqty());
  }

  // optional string CommodityContractNumber = 36;
  if (this->commoditycontractnumber().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->commoditycontractnumber());
  }

  // optional int32 ExchangeDate = 37;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 38;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 59;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDSpot::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDSpot)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDSpot* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDSpot>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDSpot)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDSpot)
    UnsafeMergeFrom(*source);
  }
}

void MDSpot::MergeFrom(const MDSpot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDSpot)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDSpot::UnsafeMergeFrom(const MDSpot& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.totalweighttrade() != 0) {
    set_totalweighttrade(from.totalweighttrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.tradingdate() != 0) {
    set_tradingdate(from.tradingdate());
  }
  if (from.preopeninterest() != 0) {
    set_preopeninterest(from.preopeninterest());
  }
  if (from.presettleprice() != 0) {
    set_presettleprice(from.presettleprice());
  }
  if (from.openinterest() != 0) {
    set_openinterest(from.openinterest());
  }
  if (from.settleprice() != 0) {
    set_settleprice(from.settleprice());
  }
  if (from.initopeninterest() != 0) {
    set_initopeninterest(from.initopeninterest());
  }
  if (from.interestchg() != 0) {
    set_interestchg(from.interestchg());
  }
  if (from.averagepx() != 0) {
    set_averagepx(from.averagepx());
  }
  if (from.lifehighpx() != 0) {
    set_lifehighpx(from.lifehighpx());
  }
  if (from.lifelowpx() != 0) {
    set_lifelowpx(from.lifelowpx());
  }
  if (from.buypx() != 0) {
    set_buypx(from.buypx());
  }
  if (from.buyqty() != 0) {
    set_buyqty(from.buyqty());
  }
  if (from.buyimplyqty() != 0) {
    set_buyimplyqty(from.buyimplyqty());
  }
  if (from.sellpx() != 0) {
    set_sellpx(from.sellpx());
  }
  if (from.sellqty() != 0) {
    set_sellqty(from.sellqty());
  }
  if (from.sellimplyqty() != 0) {
    set_sellimplyqty(from.sellimplyqty());
  }
  if (from.commoditycontractnumber().size() > 0) {

    commoditycontractnumber_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.commoditycontractnumber_);
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void MDSpot::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDSpot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDSpot::CopyFrom(const MDSpot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDSpot)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDSpot::IsInitialized() const {

  return true;
}

void MDSpot::Swap(MDSpot* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDSpot::InternalSwap(MDSpot* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(totalweighttrade_, other->totalweighttrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(tradingdate_, other->tradingdate_);
  std::swap(preopeninterest_, other->preopeninterest_);
  std::swap(presettleprice_, other->presettleprice_);
  std::swap(openinterest_, other->openinterest_);
  std::swap(settleprice_, other->settleprice_);
  std::swap(initopeninterest_, other->initopeninterest_);
  std::swap(interestchg_, other->interestchg_);
  std::swap(averagepx_, other->averagepx_);
  std::swap(lifehighpx_, other->lifehighpx_);
  std::swap(lifelowpx_, other->lifelowpx_);
  std::swap(buypx_, other->buypx_);
  std::swap(buyqty_, other->buyqty_);
  std::swap(buyimplyqty_, other->buyimplyqty_);
  std::swap(sellpx_, other->sellpx_);
  std::swap(sellqty_, other->sellqty_);
  std::swap(sellimplyqty_, other->sellimplyqty_);
  commoditycontractnumber_.Swap(&other->commoditycontractnumber_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDSpot::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDSpot_descriptor_;
  metadata.reflection = MDSpot_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDSpot

// optional string HTSCSecurityID = 1;
void MDSpot::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSpot::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
void MDSpot::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
void MDSpot::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}
::std::string* MDSpot::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSpot::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDSpot::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDSpot::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MDDate)
  return mddate_;
}
void MDSpot::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MDDate)
}

// optional int32 MDTime = 3;
void MDSpot::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDSpot::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MDTime)
  return mdtime_;
}
void MDSpot::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDSpot::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.DataTimestamp)
  return datatimestamp_;
}
void MDSpot::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDSpot::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSpot::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
void MDSpot::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
void MDSpot::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}
::std::string* MDSpot::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSpot::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDSpot::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDSpot::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDSpot::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDSpot::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDSpot::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDSpot::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.securityType)
}

// optional int64 MaxPx = 8;
void MDSpot::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MaxPx)
  return maxpx_;
}
void MDSpot::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MaxPx)
}

// optional int64 MinPx = 9;
void MDSpot::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.MinPx)
  return minpx_;
}
void MDSpot::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.MinPx)
}

// optional int64 PreClosePx = 10;
void MDSpot::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreClosePx)
  return preclosepx_;
}
void MDSpot::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDSpot::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.NumTrades)
  return numtrades_;
}
void MDSpot::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDSpot::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDSpot::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDSpot::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalValueTrade)
  return totalvaluetrade_;
}
void MDSpot::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalValueTrade)
}

// optional int64 TotalWeightTrade = 14;
void MDSpot::clear_totalweighttrade() {
  totalweighttrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::totalweighttrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TotalWeightTrade)
  return totalweighttrade_;
}
void MDSpot::set_totalweighttrade(::google::protobuf::int64 value) {
  
  totalweighttrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TotalWeightTrade)
}

// optional int64 LastPx = 15;
void MDSpot::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LastPx)
  return lastpx_;
}
void MDSpot::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LastPx)
}

// optional int64 OpenPx = 16;
void MDSpot::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.OpenPx)
  return openpx_;
}
void MDSpot::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.OpenPx)
}

// optional int64 ClosePx = 17;
void MDSpot::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ClosePx)
  return closepx_;
}
void MDSpot::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ClosePx)
}

// optional int64 HighPx = 18;
void MDSpot::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.HighPx)
  return highpx_;
}
void MDSpot::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.HighPx)
}

// optional int64 LowPx = 19;
void MDSpot::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LowPx)
  return lowpx_;
}
void MDSpot::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LowPx)
}

// optional int32 TradingDate = 20;
void MDSpot::clear_tradingdate() {
  tradingdate_ = 0;
}
::google::protobuf::int32 MDSpot::tradingdate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.TradingDate)
  return tradingdate_;
}
void MDSpot::set_tradingdate(::google::protobuf::int32 value) {
  
  tradingdate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.TradingDate)
}

// optional int64 PreOpenInterest = 21;
void MDSpot::clear_preopeninterest() {
  preopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::preopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreOpenInterest)
  return preopeninterest_;
}
void MDSpot::set_preopeninterest(::google::protobuf::int64 value) {
  
  preopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreOpenInterest)
}

// optional int64 PreSettlePrice = 22;
void MDSpot::clear_presettleprice() {
  presettleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::presettleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.PreSettlePrice)
  return presettleprice_;
}
void MDSpot::set_presettleprice(::google::protobuf::int64 value) {
  
  presettleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.PreSettlePrice)
}

// optional int64 OpenInterest = 23;
void MDSpot::clear_openinterest() {
  openinterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::openinterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.OpenInterest)
  return openinterest_;
}
void MDSpot::set_openinterest(::google::protobuf::int64 value) {
  
  openinterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.OpenInterest)
}

// optional int64 SettlePrice = 24;
void MDSpot::clear_settleprice() {
  settleprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::settleprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SettlePrice)
  return settleprice_;
}
void MDSpot::set_settleprice(::google::protobuf::int64 value) {
  
  settleprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SettlePrice)
}

// optional int64 InitOpenInterest = 25;
void MDSpot::clear_initopeninterest() {
  initopeninterest_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::initopeninterest() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.InitOpenInterest)
  return initopeninterest_;
}
void MDSpot::set_initopeninterest(::google::protobuf::int64 value) {
  
  initopeninterest_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.InitOpenInterest)
}

// optional int64 InterestChg = 26;
void MDSpot::clear_interestchg() {
  interestchg_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::interestchg() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.InterestChg)
  return interestchg_;
}
void MDSpot::set_interestchg(::google::protobuf::int64 value) {
  
  interestchg_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.InterestChg)
}

// optional int64 AveragePx = 27;
void MDSpot::clear_averagepx() {
  averagepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::averagepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.AveragePx)
  return averagepx_;
}
void MDSpot::set_averagepx(::google::protobuf::int64 value) {
  
  averagepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.AveragePx)
}

// optional int64 LifeHighPx = 28;
void MDSpot::clear_lifehighpx() {
  lifehighpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::lifehighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LifeHighPx)
  return lifehighpx_;
}
void MDSpot::set_lifehighpx(::google::protobuf::int64 value) {
  
  lifehighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LifeHighPx)
}

// optional int64 LifeLowPx = 29;
void MDSpot::clear_lifelowpx() {
  lifelowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::lifelowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.LifeLowPx)
  return lifelowpx_;
}
void MDSpot::set_lifelowpx(::google::protobuf::int64 value) {
  
  lifelowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.LifeLowPx)
}

// optional int64 BuyPx = 30;
void MDSpot::clear_buypx() {
  buypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::buypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyPx)
  return buypx_;
}
void MDSpot::set_buypx(::google::protobuf::int64 value) {
  
  buypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyPx)
}

// optional int64 BuyQty = 31;
void MDSpot::clear_buyqty() {
  buyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::buyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyQty)
  return buyqty_;
}
void MDSpot::set_buyqty(::google::protobuf::int64 value) {
  
  buyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyQty)
}

// optional int64 BuyImplyQty = 32;
void MDSpot::clear_buyimplyqty() {
  buyimplyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::buyimplyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyImplyQty)
  return buyimplyqty_;
}
void MDSpot::set_buyimplyqty(::google::protobuf::int64 value) {
  
  buyimplyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyImplyQty)
}

// optional int64 SellPx = 33;
void MDSpot::clear_sellpx() {
  sellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::sellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellPx)
  return sellpx_;
}
void MDSpot::set_sellpx(::google::protobuf::int64 value) {
  
  sellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellPx)
}

// optional int64 SellQty = 34;
void MDSpot::clear_sellqty() {
  sellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::sellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellQty)
  return sellqty_;
}
void MDSpot::set_sellqty(::google::protobuf::int64 value) {
  
  sellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellQty)
}

// optional int64 SellImplyQty = 35;
void MDSpot::clear_sellimplyqty() {
  sellimplyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDSpot::sellimplyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellImplyQty)
  return sellimplyqty_;
}
void MDSpot::set_sellimplyqty(::google::protobuf::int64 value) {
  
  sellimplyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellImplyQty)
}

// optional string CommodityContractNumber = 36;
void MDSpot::clear_commoditycontractnumber() {
  commoditycontractnumber_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDSpot::commoditycontractnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  return commoditycontractnumber_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_commoditycontractnumber(const ::std::string& value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
void MDSpot::set_commoditycontractnumber(const char* value) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
void MDSpot::set_commoditycontractnumber(const char* value, size_t size) {
  
  commoditycontractnumber_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}
::std::string* MDSpot::mutable_commoditycontractnumber() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  return commoditycontractnumber_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDSpot::release_commoditycontractnumber() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
  
  return commoditycontractnumber_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDSpot::set_allocated_commoditycontractnumber(::std::string* commoditycontractnumber) {
  if (commoditycontractnumber != NULL) {
    
  } else {
    
  }
  commoditycontractnumber_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), commoditycontractnumber);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDSpot.CommodityContractNumber)
}

// optional int32 ExchangeDate = 37;
void MDSpot::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDSpot::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ExchangeDate)
  return exchangedate_;
}
void MDSpot::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ExchangeDate)
}

// optional int32 ExchangeTime = 38;
void MDSpot::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDSpot::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.ExchangeTime)
  return exchangetime_;
}
void MDSpot::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.ExchangeTime)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDSpot::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDSpot::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDSpot::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDSpot::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
}
void MDSpot::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDSpot::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDSpot::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDSpot::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDSpot::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
}
void MDSpot::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDSpot::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDSpot::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDSpot::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDSpot::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
}
void MDSpot::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDSpot::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDSpot::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDSpot::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDSpot::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
}
void MDSpot::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDSpot::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDSpot::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDSpot::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDSpot::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
}
void MDSpot::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDSpot::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDSpot::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDSpot::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDSpot::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
}
void MDSpot::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDSpot::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDSpot::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDSpot::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDSpot::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
}
void MDSpot::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDSpot::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDSpot::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDSpot::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDSpot::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
}
void MDSpot::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDSpot::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDSpot::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDSpot.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int32 DataMultiplePowerOf10 = 59;
void MDSpot::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDSpot::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDSpot.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDSpot::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDSpot.DataMultiplePowerOf10)
}

inline const MDSpot* MDSpot::internal_default_instance() {
  return &MDSpot_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
