// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDCfetsForex.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDCfetsForex.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDCfetsForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDCfetsForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* SpotForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SpotForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* ForwardForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ForwardForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* NonDeliverableForwardsForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  NonDeliverableForwardsForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* SwapForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SwapForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* OptionForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  OptionForex_reflection_ = NULL;
const ::google::protobuf::Descriptor* SpotClosePriceForex_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  SpotClosePriceForex_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDCfetsForex_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDCfetsForex_2eproto() {
  protobuf_AddDesc_MDCfetsForex_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDCfetsForex.proto");
  GOOGLE_CHECK(file != NULL);
  MDCfetsForex_descriptor_ = file->message_type(0);
  static const int MDCfetsForex_offsets_[16] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, securitysubtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, forextype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, spotforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, forwardforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, nondeliverableforwardsforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, swapforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, optionforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, spotclosepriceforex_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, transacttime_),
  };
  MDCfetsForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDCfetsForex_descriptor_,
      MDCfetsForex::internal_default_instance(),
      MDCfetsForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDCfetsForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDCfetsForex, _internal_metadata_));
  SpotForex_descriptor_ = file->message_type(1);
  static const int SpotForex_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, valuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, netbasischange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, percentagechange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, buydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, buytime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, selldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, selltime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, lastbuyrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, lastsellrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, lastbuyallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, lastsellallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, historycloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, amountlevelrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, amountlevelallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, rateside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, allinside_),
  };
  SpotForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SpotForex_descriptor_,
      SpotForex::internal_default_instance(),
      SpotForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(SpotForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotForex, _internal_metadata_));
  ForwardForex_descriptor_ = file->message_type(2);
  static const int ForwardForex_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, valuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, netbasischange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, percentagechange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, buydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, buytime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, selldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, selltime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, lastbuyrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, lastsellrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, lastbuyallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, lastsellallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, historycloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, amountlevelrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, amountlevelallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, rateside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, allinside_),
  };
  ForwardForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ForwardForex_descriptor_,
      ForwardForex::internal_default_instance(),
      ForwardForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(ForwardForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ForwardForex, _internal_metadata_));
  NonDeliverableForwardsForex_descriptor_ = file->message_type(3);
  static const int NonDeliverableForwardsForex_offsets_[20] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, valuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, netbasischange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, percentagechange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, buydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, buytime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, selldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, selltime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, lastbuyrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, lastsellrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, lastbuyallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, lastsellallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, historycloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, amountlevelrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, amountlevelallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, rateside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, allinside_),
  };
  NonDeliverableForwardsForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      NonDeliverableForwardsForex_descriptor_,
      NonDeliverableForwardsForex::internal_default_instance(),
      NonDeliverableForwardsForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(NonDeliverableForwardsForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(NonDeliverableForwardsForex, _internal_metadata_));
  SwapForex_descriptor_ = file->message_type(4);
  static const int SwapForex_offsets_[21] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, valuedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, netbasischange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, percentagechange_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, buydate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, buytime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, selldate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, selltime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, lastbuyrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, lastsellrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, lastbuyallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, lastsellallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, highrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, lowrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, openrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, historycloserate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, closerate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, amountlevelrate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, amountlevelallin_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, rateside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, allinside_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, legsign_),
  };
  SwapForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SwapForex_descriptor_,
      SwapForex::internal_default_instance(),
      SwapForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(SwapForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SwapForex, _internal_metadata_));
  OptionForex_descriptor_ = file->message_type(5);
  static const int OptionForex_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, fxterm_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, premium_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, volatility_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, volume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, tradedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, tradetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, premiumtype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, optiontype_),
  };
  OptionForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      OptionForex_descriptor_,
      OptionForex::internal_default_instance(),
      OptionForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(OptionForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(OptionForex, _internal_metadata_));
  SpotClosePriceForex_descriptor_ = file->message_type(6);
  static const int SpotClosePriceForex_offsets_[3] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceForex, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceForex, updatedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceForex, updatetime_),
  };
  SpotClosePriceForex_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      SpotClosePriceForex_descriptor_,
      SpotClosePriceForex::internal_default_instance(),
      SpotClosePriceForex_offsets_,
      -1,
      -1,
      -1,
      sizeof(SpotClosePriceForex),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(SpotClosePriceForex, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDCfetsForex_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDCfetsForex_descriptor_, MDCfetsForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SpotForex_descriptor_, SpotForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ForwardForex_descriptor_, ForwardForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      NonDeliverableForwardsForex_descriptor_, NonDeliverableForwardsForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SwapForex_descriptor_, SwapForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      OptionForex_descriptor_, OptionForex::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      SpotClosePriceForex_descriptor_, SpotClosePriceForex::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDCfetsForex_2eproto() {
  MDCfetsForex_default_instance_.Shutdown();
  delete MDCfetsForex_reflection_;
  SpotForex_default_instance_.Shutdown();
  delete SpotForex_reflection_;
  ForwardForex_default_instance_.Shutdown();
  delete ForwardForex_reflection_;
  NonDeliverableForwardsForex_default_instance_.Shutdown();
  delete NonDeliverableForwardsForex_reflection_;
  SwapForex_default_instance_.Shutdown();
  delete SwapForex_reflection_;
  OptionForex_default_instance_.Shutdown();
  delete OptionForex_reflection_;
  SpotClosePriceForex_default_instance_.Shutdown();
  delete SpotClosePriceForex_reflection_;
}

void protobuf_InitDefaults_MDCfetsForex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDCfetsForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SpotForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  ForwardForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  NonDeliverableForwardsForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SwapForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  OptionForex_default_instance_.DefaultConstruct();
  ::google::protobuf::internal::GetEmptyString();
  SpotClosePriceForex_default_instance_.DefaultConstruct();
  MDCfetsForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  SpotForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  ForwardForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  NonDeliverableForwardsForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  SwapForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  OptionForex_default_instance_.get_mutable()->InitAsDefaultInstance();
  SpotClosePriceForex_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDCfetsForex_2eproto_once_);
void protobuf_InitDefaults_MDCfetsForex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDCfetsForex_2eproto_once_,
                 &protobuf_InitDefaults_MDCfetsForex_2eproto_impl);
}
void protobuf_AddDesc_MDCfetsForex_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDCfetsForex_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\022MDCfetsForex.proto\022\032com.htsc.mdc.insig"
    "ht.model\032\027ESecurityIDSource.proto\032\023ESecu"
    "rityType.proto\"\326\005\n\014MDCfetsForex\022\026\n\016HTSCS"
    "ecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTim"
    "e\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020securi"
    "tyIDSource\030\005 \001(\0162%.com.htsc.mdc.model.ES"
    "ecurityIDSource\0227\n\014securityType\030\006 \001(\0162!."
    "com.htsc.mdc.model.ESecurityType\022\027\n\017Secu"
    "ritySubType\030\007 \001(\t\022\021\n\tForexType\030\010 \001(\005\0228\n\t"
    "spotForex\030\t \001(\0132%.com.htsc.mdc.insight.m"
    "odel.SpotForex\022>\n\014forwardForex\030\n \001(\0132(.c"
    "om.htsc.mdc.insight.model.ForwardForex\022\\"
    "\n\033nonDeliverableForwardsForex\030\013 \001(\01327.co"
    "m.htsc.mdc.insight.model.NonDeliverableF"
    "orwardsForex\0228\n\tswapForex\030\014 \001(\0132%.com.ht"
    "sc.mdc.insight.model.SwapForex\022<\n\013option"
    "Forex\030\r \001(\0132\'.com.htsc.mdc.insight.model"
    ".OptionForex\022L\n\023spotClosePriceForex\030\016 \001("
    "\0132/.com.htsc.mdc.insight.model.SpotClose"
    "PriceForex\022\035\n\025DataMultiplePowerOf10\030\017 \001("
    "\005\022\024\n\014TransactTime\030\020 \001(\t\"\250\003\n\tSpotForex\022\021\n"
    "\tValueDate\030\001 \001(\t\022\026\n\016NetBasisChange\030\002 \001(\003"
    "\022\030\n\020PercentageChange\030\003 \001(\003\022\017\n\007BuyDate\030\004 "
    "\001(\t\022\017\n\007BuyTime\030\005 \001(\t\022\020\n\010SellDate\030\006 \001(\t\022\020"
    "\n\010SellTime\030\007 \001(\t\022\023\n\013LastBuyRate\030\010 \001(\003\022\024\n"
    "\014LastSellRate\030\t \001(\003\022\024\n\014LastBuyAllin\030\n \001("
    "\003\022\025\n\rLastSellAllin\030\013 \001(\003\022\020\n\010HighRate\030\014 \001"
    "(\003\022\017\n\007LowRate\030\r \001(\003\022\020\n\010OpenRate\030\016 \001(\003\022\030\n"
    "\020HistoryCloseRate\030\017 \001(\003\022\021\n\tCloseRate\030\020 \001"
    "(\003\022\027\n\017AmountLevelRate\030\021 \001(\005\022\030\n\020AmountLev"
    "elAllin\030\022 \001(\005\022\020\n\010RateSide\030\023 \001(\005\022\021\n\tAllin"
    "Side\030\024 \001(\005\"\253\003\n\014ForwardForex\022\021\n\tValueDate"
    "\030\001 \001(\t\022\026\n\016NetBasisChange\030\002 \001(\003\022\030\n\020Percen"
    "tageChange\030\003 \001(\003\022\017\n\007BuyDate\030\004 \001(\t\022\017\n\007Buy"
    "Time\030\005 \001(\t\022\020\n\010SellDate\030\006 \001(\t\022\020\n\010SellTime"
    "\030\007 \001(\t\022\023\n\013LastBuyRate\030\010 \001(\003\022\024\n\014LastSellR"
    "ate\030\t \001(\003\022\024\n\014LastBuyAllin\030\n \001(\003\022\025\n\rLastS"
    "ellAllin\030\013 \001(\003\022\020\n\010HighRate\030\014 \001(\003\022\017\n\007LowR"
    "ate\030\r \001(\003\022\020\n\010OpenRate\030\016 \001(\003\022\030\n\020HistoryCl"
    "oseRate\030\017 \001(\003\022\021\n\tCloseRate\030\020 \001(\003\022\027\n\017Amou"
    "ntLevelRate\030\021 \001(\005\022\030\n\020AmountLevelAllin\030\022 "
    "\001(\005\022\020\n\010RateSide\030\023 \001(\005\022\021\n\tAllinSide\030\024 \001(\005"
    "\"\272\003\n\033NonDeliverableForwardsForex\022\021\n\tValu"
    "eDate\030\001 \001(\t\022\026\n\016NetBasisChange\030\002 \001(\003\022\030\n\020P"
    "ercentageChange\030\003 \001(\003\022\017\n\007BuyDate\030\004 \001(\t\022\017"
    "\n\007BuyTime\030\005 \001(\t\022\020\n\010SellDate\030\006 \001(\t\022\020\n\010Sel"
    "lTime\030\007 \001(\t\022\023\n\013LastBuyRate\030\010 \001(\003\022\024\n\014Last"
    "SellRate\030\t \001(\003\022\024\n\014LastBuyAllin\030\n \001(\003\022\025\n\r"
    "LastSellAllin\030\013 \001(\003\022\020\n\010HighRate\030\014 \001(\003\022\017\n"
    "\007LowRate\030\r \001(\003\022\020\n\010OpenRate\030\016 \001(\003\022\030\n\020Hist"
    "oryCloseRate\030\017 \001(\003\022\021\n\tCloseRate\030\020 \001(\003\022\027\n"
    "\017AmountLevelRate\030\021 \001(\005\022\030\n\020AmountLevelAll"
    "in\030\022 \001(\005\022\020\n\010RateSide\030\023 \001(\005\022\021\n\tAllinSide\030"
    "\024 \001(\005\"\271\003\n\tSwapForex\022\021\n\tValueDate\030\001 \001(\t\022\026"
    "\n\016NetBasisChange\030\002 \001(\003\022\030\n\020PercentageChan"
    "ge\030\003 \001(\003\022\017\n\007BuyDate\030\004 \001(\t\022\017\n\007BuyTime\030\005 \001"
    "(\t\022\020\n\010SellDate\030\006 \001(\t\022\020\n\010SellTime\030\007 \001(\t\022\023"
    "\n\013LastBuyRate\030\010 \001(\003\022\024\n\014LastSellRate\030\t \001("
    "\003\022\024\n\014LastBuyAllin\030\n \001(\003\022\025\n\rLastSellAllin"
    "\030\013 \001(\003\022\020\n\010HighRate\030\014 \001(\003\022\017\n\007LowRate\030\r \001("
    "\003\022\020\n\010OpenRate\030\016 \001(\003\022\030\n\020HistoryCloseRate\030"
    "\017 \001(\003\022\021\n\tCloseRate\030\020 \001(\003\022\027\n\017AmountLevelR"
    "ate\030\021 \001(\005\022\030\n\020AmountLevelAllin\030\022 \001(\005\022\020\n\010R"
    "ateSide\030\023 \001(\005\022\021\n\tAllinSide\030\024 \001(\005\022\017\n\007LegS"
    "ign\030\025 \001(\t\"\241\001\n\013OptionForex\022\016\n\006FxTerm\030\001 \001("
    "\t\022\017\n\007Premium\030\002 \001(\003\022\022\n\nVolatility\030\003 \001(\003\022\016"
    "\n\006Volume\030\004 \001(\003\022\021\n\tTradeDate\030\005 \001(\t\022\021\n\tTra"
    "deTime\030\006 \001(\t\022\023\n\013PremiumType\030\007 \001(\005\022\022\n\nOpt"
    "ionType\030\010 \001(\t\"N\n\023SpotClosePriceForex\022\017\n\007"
    "ClosePx\030\001 \001(\003\022\022\n\nUpdateDate\030\002 \001(\t\022\022\n\nUpd"
    "ateTime\030\003 \001(\tB5\n\032com.htsc.mdc.insight.mo"
    "delB\022MDCfetsForexProtosH\001\240\001\001b\006proto3", 2876);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDCfetsForex.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDCfetsForex_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDCfetsForex_2eproto_once_);
void protobuf_AddDesc_MDCfetsForex_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDCfetsForex_2eproto_once_,
                 &protobuf_AddDesc_MDCfetsForex_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDCfetsForex_2eproto {
  StaticDescriptorInitializer_MDCfetsForex_2eproto() {
    protobuf_AddDesc_MDCfetsForex_2eproto();
  }
} static_descriptor_initializer_MDCfetsForex_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDCfetsForex::kHTSCSecurityIDFieldNumber;
const int MDCfetsForex::kMDDateFieldNumber;
const int MDCfetsForex::kMDTimeFieldNumber;
const int MDCfetsForex::kDataTimestampFieldNumber;
const int MDCfetsForex::kSecurityIDSourceFieldNumber;
const int MDCfetsForex::kSecurityTypeFieldNumber;
const int MDCfetsForex::kSecuritySubTypeFieldNumber;
const int MDCfetsForex::kForexTypeFieldNumber;
const int MDCfetsForex::kSpotForexFieldNumber;
const int MDCfetsForex::kForwardForexFieldNumber;
const int MDCfetsForex::kNonDeliverableForwardsForexFieldNumber;
const int MDCfetsForex::kSwapForexFieldNumber;
const int MDCfetsForex::kOptionForexFieldNumber;
const int MDCfetsForex::kSpotClosePriceForexFieldNumber;
const int MDCfetsForex::kDataMultiplePowerOf10FieldNumber;
const int MDCfetsForex::kTransactTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDCfetsForex::MDCfetsForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDCfetsForex)
}

void MDCfetsForex::InitAsDefaultInstance() {
  spotforex_ = const_cast< ::com::htsc::mdc::insight::model::SpotForex*>(
      ::com::htsc::mdc::insight::model::SpotForex::internal_default_instance());
  forwardforex_ = const_cast< ::com::htsc::mdc::insight::model::ForwardForex*>(
      ::com::htsc::mdc::insight::model::ForwardForex::internal_default_instance());
  nondeliverableforwardsforex_ = const_cast< ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex*>(
      ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex::internal_default_instance());
  swapforex_ = const_cast< ::com::htsc::mdc::insight::model::SwapForex*>(
      ::com::htsc::mdc::insight::model::SwapForex::internal_default_instance());
  optionforex_ = const_cast< ::com::htsc::mdc::insight::model::OptionForex*>(
      ::com::htsc::mdc::insight::model::OptionForex::internal_default_instance());
  spotclosepriceforex_ = const_cast< ::com::htsc::mdc::insight::model::SpotClosePriceForex*>(
      ::com::htsc::mdc::insight::model::SpotClosePriceForex::internal_default_instance());
}

MDCfetsForex::MDCfetsForex(const MDCfetsForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDCfetsForex)
}

void MDCfetsForex::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  spotforex_ = NULL;
  forwardforex_ = NULL;
  nondeliverableforwardsforex_ = NULL;
  swapforex_ = NULL;
  optionforex_ = NULL;
  spotclosepriceforex_ = NULL;
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

MDCfetsForex::~MDCfetsForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDCfetsForex)
  SharedDtor();
}

void MDCfetsForex::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  transacttime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != &MDCfetsForex_default_instance_.get()) {
    delete spotforex_;
    delete forwardforex_;
    delete nondeliverableforwardsforex_;
    delete swapforex_;
    delete optionforex_;
    delete spotclosepriceforex_;
  }
}

void MDCfetsForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDCfetsForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDCfetsForex_descriptor_;
}

const MDCfetsForex& MDCfetsForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDCfetsForex> MDCfetsForex_default_instance_;

MDCfetsForex* MDCfetsForex::New(::google::protobuf::Arena* arena) const {
  MDCfetsForex* n = new MDCfetsForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDCfetsForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDCfetsForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDCfetsForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDCfetsForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, forextype_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && spotforex_ != NULL) delete spotforex_;
  spotforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && forwardforex_ != NULL) delete forwardforex_;
  forwardforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsforex_ != NULL) delete nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && swapforex_ != NULL) delete swapforex_;
  swapforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && optionforex_ != NULL) delete optionforex_;
  optionforex_ = NULL;
  if (GetArenaNoVirtual() == NULL && spotclosepriceforex_ != NULL) delete spotclosepriceforex_;
  spotclosepriceforex_ = NULL;
  datamultiplepowerof10_ = 0;
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool MDCfetsForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDCfetsForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SecuritySubType;
        break;
      }

      // optional string SecuritySubType = 7;
      case 7: {
        if (tag == 58) {
         parse_SecuritySubType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_securitysubtype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->securitysubtype().data(), this->securitysubtype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_ForexType;
        break;
      }

      // optional int32 ForexType = 8;
      case 8: {
        if (tag == 64) {
         parse_ForexType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &forextype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(74)) goto parse_spotForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
      case 9: {
        if (tag == 74) {
         parse_spotForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spotforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(82)) goto parse_forwardForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
      case 10: {
        if (tag == 82) {
         parse_forwardForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_forwardforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(90)) goto parse_nonDeliverableForwardsForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
      case 11: {
        if (tag == 90) {
         parse_nonDeliverableForwardsForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_nondeliverableforwardsforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(98)) goto parse_swapForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
      case 12: {
        if (tag == 98) {
         parse_swapForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_swapforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(106)) goto parse_optionForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
      case 13: {
        if (tag == 106) {
         parse_optionForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_optionforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(114)) goto parse_spotClosePriceForex;
        break;
      }

      // optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
      case 14: {
        if (tag == 114) {
         parse_spotClosePriceForex:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtual(
               input, mutable_spotclosepriceforex()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 15;
      case 15: {
        if (tag == 120) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(130)) goto parse_TransactTime;
        break;
      }

      // optional string TransactTime = 16;
      case 16: {
        if (tag == 130) {
         parse_TransactTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_transacttime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->transacttime().data(), this->transacttime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDCfetsForex.TransactTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDCfetsForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDCfetsForex)
  return false;
#undef DO_
}

void MDCfetsForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDCfetsForex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->securitysubtype(), output);
  }

  // optional int32 ForexType = 8;
  if (this->forextype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->forextype(), output);
  }

  // optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
  if (this->has_spotforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->spotforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
  if (this->has_forwardforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->forwardforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
  if (this->has_nondeliverableforwardsforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->nondeliverableforwardsforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
  if (this->has_swapforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->swapforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
  if (this->has_optionforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      13, *this->optionforex_, output);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
  if (this->has_spotclosepriceforex()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->spotclosepriceforex_, output);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->datamultiplepowerof10(), output);
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.TransactTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      16, this->transacttime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDCfetsForex)
}

::google::protobuf::uint8* MDCfetsForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDCfetsForex)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->securitysubtype().data(), this->securitysubtype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->securitysubtype(), target);
  }

  // optional int32 ForexType = 8;
  if (this->forextype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->forextype(), target);
  }

  // optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
  if (this->has_spotforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        9, *this->spotforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
  if (this->has_forwardforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        10, *this->forwardforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
  if (this->has_nondeliverableforwardsforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        11, *this->nondeliverableforwardsforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
  if (this->has_swapforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        12, *this->swapforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
  if (this->has_optionforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        13, *this->optionforex_, false, target);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
  if (this->has_spotclosepriceforex()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        14, *this->spotclosepriceforex_, false, target);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->datamultiplepowerof10(), target);
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->transacttime().data(), this->transacttime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDCfetsForex.TransactTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        16, this->transacttime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDCfetsForex)
  return target;
}

size_t MDCfetsForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDCfetsForex)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional string SecuritySubType = 7;
  if (this->securitysubtype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->securitysubtype());
  }

  // optional int32 ForexType = 8;
  if (this->forextype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->forextype());
  }

  // optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
  if (this->has_spotforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spotforex_);
  }

  // optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
  if (this->has_forwardforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->forwardforex_);
  }

  // optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
  if (this->has_nondeliverableforwardsforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->nondeliverableforwardsforex_);
  }

  // optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
  if (this->has_swapforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->swapforex_);
  }

  // optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
  if (this->has_optionforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->optionforex_);
  }

  // optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
  if (this->has_spotclosepriceforex()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
        *this->spotclosepriceforex_);
  }

  // optional int32 DataMultiplePowerOf10 = 15;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional string TransactTime = 16;
  if (this->transacttime().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->transacttime());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDCfetsForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDCfetsForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDCfetsForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDCfetsForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDCfetsForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDCfetsForex)
    UnsafeMergeFrom(*source);
  }
}

void MDCfetsForex::MergeFrom(const MDCfetsForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDCfetsForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDCfetsForex::UnsafeMergeFrom(const MDCfetsForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.securitysubtype().size() > 0) {

    securitysubtype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.securitysubtype_);
  }
  if (from.forextype() != 0) {
    set_forextype(from.forextype());
  }
  if (from.has_spotforex()) {
    mutable_spotforex()->::com::htsc::mdc::insight::model::SpotForex::MergeFrom(from.spotforex());
  }
  if (from.has_forwardforex()) {
    mutable_forwardforex()->::com::htsc::mdc::insight::model::ForwardForex::MergeFrom(from.forwardforex());
  }
  if (from.has_nondeliverableforwardsforex()) {
    mutable_nondeliverableforwardsforex()->::com::htsc::mdc::insight::model::NonDeliverableForwardsForex::MergeFrom(from.nondeliverableforwardsforex());
  }
  if (from.has_swapforex()) {
    mutable_swapforex()->::com::htsc::mdc::insight::model::SwapForex::MergeFrom(from.swapforex());
  }
  if (from.has_optionforex()) {
    mutable_optionforex()->::com::htsc::mdc::insight::model::OptionForex::MergeFrom(from.optionforex());
  }
  if (from.has_spotclosepriceforex()) {
    mutable_spotclosepriceforex()->::com::htsc::mdc::insight::model::SpotClosePriceForex::MergeFrom(from.spotclosepriceforex());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.transacttime().size() > 0) {

    transacttime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.transacttime_);
  }
}

void MDCfetsForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDCfetsForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDCfetsForex::CopyFrom(const MDCfetsForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDCfetsForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDCfetsForex::IsInitialized() const {

  return true;
}

void MDCfetsForex::Swap(MDCfetsForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDCfetsForex::InternalSwap(MDCfetsForex* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  securitysubtype_.Swap(&other->securitysubtype_);
  std::swap(forextype_, other->forextype_);
  std::swap(spotforex_, other->spotforex_);
  std::swap(forwardforex_, other->forwardforex_);
  std::swap(nondeliverableforwardsforex_, other->nondeliverableforwardsforex_);
  std::swap(swapforex_, other->swapforex_);
  std::swap(optionforex_, other->optionforex_);
  std::swap(spotclosepriceforex_, other->spotclosepriceforex_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  transacttime_.Swap(&other->transacttime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDCfetsForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDCfetsForex_descriptor_;
  metadata.reflection = MDCfetsForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDCfetsForex

// optional string HTSCSecurityID = 1;
void MDCfetsForex::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsForex::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
void MDCfetsForex::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
void MDCfetsForex::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}
::std::string* MDCfetsForex::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsForex::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDCfetsForex::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDCfetsForex::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.MDDate)
  return mddate_;
}
void MDCfetsForex::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.MDDate)
}

// optional int32 MDTime = 3;
void MDCfetsForex::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDCfetsForex::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.MDTime)
  return mdtime_;
}
void MDCfetsForex::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDCfetsForex::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDCfetsForex::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.DataTimestamp)
  return datatimestamp_;
}
void MDCfetsForex::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void MDCfetsForex::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDCfetsForex::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDCfetsForex::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void MDCfetsForex::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDCfetsForex::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDCfetsForex::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.securityType)
}

// optional string SecuritySubType = 7;
void MDCfetsForex::clear_securitysubtype() {
  securitysubtype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsForex::securitysubtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  return securitysubtype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_securitysubtype(const ::std::string& value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
void MDCfetsForex::set_securitysubtype(const char* value) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
void MDCfetsForex::set_securitysubtype(const char* value, size_t size) {
  
  securitysubtype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}
::std::string* MDCfetsForex::mutable_securitysubtype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  return securitysubtype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsForex::release_securitysubtype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
  
  return securitysubtype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_allocated_securitysubtype(::std::string* securitysubtype) {
  if (securitysubtype != NULL) {
    
  } else {
    
  }
  securitysubtype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), securitysubtype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.SecuritySubType)
}

// optional int32 ForexType = 8;
void MDCfetsForex::clear_forextype() {
  forextype_ = 0;
}
::google::protobuf::int32 MDCfetsForex::forextype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.ForexType)
  return forextype_;
}
void MDCfetsForex::set_forextype(::google::protobuf::int32 value) {
  
  forextype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.ForexType)
}

// optional .com.htsc.mdc.insight.model.SpotForex spotForex = 9;
bool MDCfetsForex::has_spotforex() const {
  return this != internal_default_instance() && spotforex_ != NULL;
}
void MDCfetsForex::clear_spotforex() {
  if (GetArenaNoVirtual() == NULL && spotforex_ != NULL) delete spotforex_;
  spotforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::SpotForex& MDCfetsForex::spotforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  return spotforex_ != NULL ? *spotforex_
                         : *::com::htsc::mdc::insight::model::SpotForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::SpotForex* MDCfetsForex::mutable_spotforex() {
  
  if (spotforex_ == NULL) {
    spotforex_ = new ::com::htsc::mdc::insight::model::SpotForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  return spotforex_;
}
::com::htsc::mdc::insight::model::SpotForex* MDCfetsForex::release_spotforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
  
  ::com::htsc::mdc::insight::model::SpotForex* temp = spotforex_;
  spotforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_spotforex(::com::htsc::mdc::insight::model::SpotForex* spotforex) {
  delete spotforex_;
  spotforex_ = spotforex;
  if (spotforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.spotForex)
}

// optional .com.htsc.mdc.insight.model.ForwardForex forwardForex = 10;
bool MDCfetsForex::has_forwardforex() const {
  return this != internal_default_instance() && forwardforex_ != NULL;
}
void MDCfetsForex::clear_forwardforex() {
  if (GetArenaNoVirtual() == NULL && forwardforex_ != NULL) delete forwardforex_;
  forwardforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::ForwardForex& MDCfetsForex::forwardforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  return forwardforex_ != NULL ? *forwardforex_
                         : *::com::htsc::mdc::insight::model::ForwardForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::ForwardForex* MDCfetsForex::mutable_forwardforex() {
  
  if (forwardforex_ == NULL) {
    forwardforex_ = new ::com::htsc::mdc::insight::model::ForwardForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  return forwardforex_;
}
::com::htsc::mdc::insight::model::ForwardForex* MDCfetsForex::release_forwardforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
  
  ::com::htsc::mdc::insight::model::ForwardForex* temp = forwardforex_;
  forwardforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_forwardforex(::com::htsc::mdc::insight::model::ForwardForex* forwardforex) {
  delete forwardforex_;
  forwardforex_ = forwardforex;
  if (forwardforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.forwardForex)
}

// optional .com.htsc.mdc.insight.model.NonDeliverableForwardsForex nonDeliverableForwardsForex = 11;
bool MDCfetsForex::has_nondeliverableforwardsforex() const {
  return this != internal_default_instance() && nondeliverableforwardsforex_ != NULL;
}
void MDCfetsForex::clear_nondeliverableforwardsforex() {
  if (GetArenaNoVirtual() == NULL && nondeliverableforwardsforex_ != NULL) delete nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex& MDCfetsForex::nondeliverableforwardsforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  return nondeliverableforwardsforex_ != NULL ? *nondeliverableforwardsforex_
                         : *::com::htsc::mdc::insight::model::NonDeliverableForwardsForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* MDCfetsForex::mutable_nondeliverableforwardsforex() {
  
  if (nondeliverableforwardsforex_ == NULL) {
    nondeliverableforwardsforex_ = new ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  return nondeliverableforwardsforex_;
}
::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* MDCfetsForex::release_nondeliverableforwardsforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
  
  ::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* temp = nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_nondeliverableforwardsforex(::com::htsc::mdc::insight::model::NonDeliverableForwardsForex* nondeliverableforwardsforex) {
  delete nondeliverableforwardsforex_;
  nondeliverableforwardsforex_ = nondeliverableforwardsforex;
  if (nondeliverableforwardsforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.nonDeliverableForwardsForex)
}

// optional .com.htsc.mdc.insight.model.SwapForex swapForex = 12;
bool MDCfetsForex::has_swapforex() const {
  return this != internal_default_instance() && swapforex_ != NULL;
}
void MDCfetsForex::clear_swapforex() {
  if (GetArenaNoVirtual() == NULL && swapforex_ != NULL) delete swapforex_;
  swapforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::SwapForex& MDCfetsForex::swapforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  return swapforex_ != NULL ? *swapforex_
                         : *::com::htsc::mdc::insight::model::SwapForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::SwapForex* MDCfetsForex::mutable_swapforex() {
  
  if (swapforex_ == NULL) {
    swapforex_ = new ::com::htsc::mdc::insight::model::SwapForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  return swapforex_;
}
::com::htsc::mdc::insight::model::SwapForex* MDCfetsForex::release_swapforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
  
  ::com::htsc::mdc::insight::model::SwapForex* temp = swapforex_;
  swapforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_swapforex(::com::htsc::mdc::insight::model::SwapForex* swapforex) {
  delete swapforex_;
  swapforex_ = swapforex;
  if (swapforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.swapForex)
}

// optional .com.htsc.mdc.insight.model.OptionForex optionForex = 13;
bool MDCfetsForex::has_optionforex() const {
  return this != internal_default_instance() && optionforex_ != NULL;
}
void MDCfetsForex::clear_optionforex() {
  if (GetArenaNoVirtual() == NULL && optionforex_ != NULL) delete optionforex_;
  optionforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::OptionForex& MDCfetsForex::optionforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  return optionforex_ != NULL ? *optionforex_
                         : *::com::htsc::mdc::insight::model::OptionForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::OptionForex* MDCfetsForex::mutable_optionforex() {
  
  if (optionforex_ == NULL) {
    optionforex_ = new ::com::htsc::mdc::insight::model::OptionForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  return optionforex_;
}
::com::htsc::mdc::insight::model::OptionForex* MDCfetsForex::release_optionforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
  
  ::com::htsc::mdc::insight::model::OptionForex* temp = optionforex_;
  optionforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_optionforex(::com::htsc::mdc::insight::model::OptionForex* optionforex) {
  delete optionforex_;
  optionforex_ = optionforex;
  if (optionforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.optionForex)
}

// optional .com.htsc.mdc.insight.model.SpotClosePriceForex spotClosePriceForex = 14;
bool MDCfetsForex::has_spotclosepriceforex() const {
  return this != internal_default_instance() && spotclosepriceforex_ != NULL;
}
void MDCfetsForex::clear_spotclosepriceforex() {
  if (GetArenaNoVirtual() == NULL && spotclosepriceforex_ != NULL) delete spotclosepriceforex_;
  spotclosepriceforex_ = NULL;
}
const ::com::htsc::mdc::insight::model::SpotClosePriceForex& MDCfetsForex::spotclosepriceforex() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  return spotclosepriceforex_ != NULL ? *spotclosepriceforex_
                         : *::com::htsc::mdc::insight::model::SpotClosePriceForex::internal_default_instance();
}
::com::htsc::mdc::insight::model::SpotClosePriceForex* MDCfetsForex::mutable_spotclosepriceforex() {
  
  if (spotclosepriceforex_ == NULL) {
    spotclosepriceforex_ = new ::com::htsc::mdc::insight::model::SpotClosePriceForex;
  }
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  return spotclosepriceforex_;
}
::com::htsc::mdc::insight::model::SpotClosePriceForex* MDCfetsForex::release_spotclosepriceforex() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
  
  ::com::htsc::mdc::insight::model::SpotClosePriceForex* temp = spotclosepriceforex_;
  spotclosepriceforex_ = NULL;
  return temp;
}
void MDCfetsForex::set_allocated_spotclosepriceforex(::com::htsc::mdc::insight::model::SpotClosePriceForex* spotclosepriceforex) {
  delete spotclosepriceforex_;
  spotclosepriceforex_ = spotclosepriceforex;
  if (spotclosepriceforex) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.spotClosePriceForex)
}

// optional int32 DataMultiplePowerOf10 = 15;
void MDCfetsForex::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDCfetsForex::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDCfetsForex::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.DataMultiplePowerOf10)
}

// optional string TransactTime = 16;
void MDCfetsForex::clear_transacttime() {
  transacttime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDCfetsForex::transacttime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  return transacttime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_transacttime(const ::std::string& value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
void MDCfetsForex::set_transacttime(const char* value) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
void MDCfetsForex::set_transacttime(const char* value, size_t size) {
  
  transacttime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}
::std::string* MDCfetsForex::mutable_transacttime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  return transacttime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDCfetsForex::release_transacttime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
  
  return transacttime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDCfetsForex::set_allocated_transacttime(::std::string* transacttime) {
  if (transacttime != NULL) {
    
  } else {
    
  }
  transacttime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), transacttime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDCfetsForex.TransactTime)
}

inline const MDCfetsForex* MDCfetsForex::internal_default_instance() {
  return &MDCfetsForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SpotForex::kValueDateFieldNumber;
const int SpotForex::kNetBasisChangeFieldNumber;
const int SpotForex::kPercentageChangeFieldNumber;
const int SpotForex::kBuyDateFieldNumber;
const int SpotForex::kBuyTimeFieldNumber;
const int SpotForex::kSellDateFieldNumber;
const int SpotForex::kSellTimeFieldNumber;
const int SpotForex::kLastBuyRateFieldNumber;
const int SpotForex::kLastSellRateFieldNumber;
const int SpotForex::kLastBuyAllinFieldNumber;
const int SpotForex::kLastSellAllinFieldNumber;
const int SpotForex::kHighRateFieldNumber;
const int SpotForex::kLowRateFieldNumber;
const int SpotForex::kOpenRateFieldNumber;
const int SpotForex::kHistoryCloseRateFieldNumber;
const int SpotForex::kCloseRateFieldNumber;
const int SpotForex::kAmountLevelRateFieldNumber;
const int SpotForex::kAmountLevelAllinFieldNumber;
const int SpotForex::kRateSideFieldNumber;
const int SpotForex::kAllinSideFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SpotForex::SpotForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SpotForex)
}

void SpotForex::InitAsDefaultInstance() {
}

SpotForex::SpotForex(const SpotForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SpotForex)
}

void SpotForex::SharedCtor() {
  valuedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&netbasischange_, 0, reinterpret_cast<char*>(&allinside_) -
    reinterpret_cast<char*>(&netbasischange_) + sizeof(allinside_));
  _cached_size_ = 0;
}

SpotForex::~SpotForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SpotForex)
  SharedDtor();
}

void SpotForex::SharedDtor() {
  valuedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SpotForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SpotForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SpotForex_descriptor_;
}

const SpotForex& SpotForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SpotForex> SpotForex_default_instance_;

SpotForex* SpotForex::New(::google::protobuf::Arena* arena) const {
  SpotForex* n = new SpotForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SpotForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SpotForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(SpotForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<SpotForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(netbasischange_, lastbuyrate_);
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lastsellrate_, closerate_);
  ZR_(amountlevelrate_, allinside_);

#undef ZR_HELPER_
#undef ZR_

}

bool SpotForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SpotForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ValueDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuedate().data(), this->valuedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotForex.ValueDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_NetBasisChange;
        break;
      }

      // optional int64 NetBasisChange = 2;
      case 2: {
        if (tag == 16) {
         parse_NetBasisChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &netbasischange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PercentageChange;
        break;
      }

      // optional int64 PercentageChange = 3;
      case 3: {
        if (tag == 24) {
         parse_PercentageChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &percentagechange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BuyDate;
        break;
      }

      // optional string BuyDate = 4;
      case 4: {
        if (tag == 34) {
         parse_BuyDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buydate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buydate().data(), this->buydate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotForex.BuyDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_BuyTime;
        break;
      }

      // optional string BuyTime = 5;
      case 5: {
        if (tag == 42) {
         parse_BuyTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buytime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buytime().data(), this->buytime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotForex.BuyTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_SellDate;
        break;
      }

      // optional string SellDate = 6;
      case 6: {
        if (tag == 50) {
         parse_SellDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selldate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selldate().data(), this->selldate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotForex.SellDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SellTime;
        break;
      }

      // optional string SellTime = 7;
      case 7: {
        if (tag == 58) {
         parse_SellTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selltime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selltime().data(), this->selltime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotForex.SellTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastBuyRate;
        break;
      }

      // optional int64 LastBuyRate = 8;
      case 8: {
        if (tag == 64) {
         parse_LastBuyRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_LastSellRate;
        break;
      }

      // optional int64 LastSellRate = 9;
      case 9: {
        if (tag == 72) {
         parse_LastSellRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LastBuyAllin;
        break;
      }

      // optional int64 LastBuyAllin = 10;
      case 10: {
        if (tag == 80) {
         parse_LastBuyAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LastSellAllin;
        break;
      }

      // optional int64 LastSellAllin = 11;
      case 11: {
        if (tag == 88) {
         parse_LastSellAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HistoryCloseRate;
        break;
      }

      // optional int64 HistoryCloseRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HistoryCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historycloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 16;
      case 16: {
        if (tag == 128) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AmountLevelRate;
        break;
      }

      // optional int32 AmountLevelRate = 17;
      case 17: {
        if (tag == 136) {
         parse_AmountLevelRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_AmountLevelAllin;
        break;
      }

      // optional int32 AmountLevelAllin = 18;
      case 18: {
        if (tag == 144) {
         parse_AmountLevelAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_RateSide;
        break;
      }

      // optional int32 RateSide = 19;
      case 19: {
        if (tag == 152) {
         parse_RateSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AllinSide;
        break;
      }

      // optional int32 AllinSide = 20;
      case 20: {
        if (tag == 160) {
         parse_AllinSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &allinside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SpotForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SpotForex)
  return false;
#undef DO_
}

void SpotForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SpotForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.ValueDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valuedate(), output);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->netbasischange(), output);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->percentagechange(), output);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.BuyDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->buydate(), output);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.BuyTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->buytime(), output);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.SellDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->selldate(), output);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.SellTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->selltime(), output);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastbuyrate(), output);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->lastsellrate(), output);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastbuyallin(), output);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lastsellallin(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openrate(), output);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->historycloserate(), output);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closerate(), output);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->amountlevelrate(), output);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->amountlevelallin(), output);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->rateside(), output);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->allinside(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SpotForex)
}

::google::protobuf::uint8* SpotForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SpotForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.ValueDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valuedate(), target);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->netbasischange(), target);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->percentagechange(), target);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.BuyDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->buydate(), target);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.BuyTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->buytime(), target);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.SellDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->selldate(), target);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotForex.SellTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->selltime(), target);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastbuyrate(), target);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->lastsellrate(), target);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastbuyallin(), target);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lastsellallin(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openrate(), target);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->historycloserate(), target);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closerate(), target);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->amountlevelrate(), target);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->amountlevelallin(), target);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->rateside(), target);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->allinside(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SpotForex)
  return target;
}

size_t SpotForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SpotForex)
  size_t total_size = 0;

  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuedate());
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->netbasischange());
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->percentagechange());
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buydate());
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buytime());
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selldate());
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selltime());
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyrate());
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellrate());
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyallin());
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellallin());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historycloserate());
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelrate());
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelallin());
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateside());
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->allinside());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SpotForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SpotForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SpotForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SpotForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SpotForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SpotForex)
    UnsafeMergeFrom(*source);
  }
}

void SpotForex::MergeFrom(const SpotForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SpotForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SpotForex::UnsafeMergeFrom(const SpotForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.valuedate().size() > 0) {

    valuedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuedate_);
  }
  if (from.netbasischange() != 0) {
    set_netbasischange(from.netbasischange());
  }
  if (from.percentagechange() != 0) {
    set_percentagechange(from.percentagechange());
  }
  if (from.buydate().size() > 0) {

    buydate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buydate_);
  }
  if (from.buytime().size() > 0) {

    buytime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buytime_);
  }
  if (from.selldate().size() > 0) {

    selldate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selldate_);
  }
  if (from.selltime().size() > 0) {

    selltime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selltime_);
  }
  if (from.lastbuyrate() != 0) {
    set_lastbuyrate(from.lastbuyrate());
  }
  if (from.lastsellrate() != 0) {
    set_lastsellrate(from.lastsellrate());
  }
  if (from.lastbuyallin() != 0) {
    set_lastbuyallin(from.lastbuyallin());
  }
  if (from.lastsellallin() != 0) {
    set_lastsellallin(from.lastsellallin());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.historycloserate() != 0) {
    set_historycloserate(from.historycloserate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.amountlevelrate() != 0) {
    set_amountlevelrate(from.amountlevelrate());
  }
  if (from.amountlevelallin() != 0) {
    set_amountlevelallin(from.amountlevelallin());
  }
  if (from.rateside() != 0) {
    set_rateside(from.rateside());
  }
  if (from.allinside() != 0) {
    set_allinside(from.allinside());
  }
}

void SpotForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SpotForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SpotForex::CopyFrom(const SpotForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SpotForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SpotForex::IsInitialized() const {

  return true;
}

void SpotForex::Swap(SpotForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SpotForex::InternalSwap(SpotForex* other) {
  valuedate_.Swap(&other->valuedate_);
  std::swap(netbasischange_, other->netbasischange_);
  std::swap(percentagechange_, other->percentagechange_);
  buydate_.Swap(&other->buydate_);
  buytime_.Swap(&other->buytime_);
  selldate_.Swap(&other->selldate_);
  selltime_.Swap(&other->selltime_);
  std::swap(lastbuyrate_, other->lastbuyrate_);
  std::swap(lastsellrate_, other->lastsellrate_);
  std::swap(lastbuyallin_, other->lastbuyallin_);
  std::swap(lastsellallin_, other->lastsellallin_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(historycloserate_, other->historycloserate_);
  std::swap(closerate_, other->closerate_);
  std::swap(amountlevelrate_, other->amountlevelrate_);
  std::swap(amountlevelallin_, other->amountlevelallin_);
  std::swap(rateside_, other->rateside_);
  std::swap(allinside_, other->allinside_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SpotForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SpotForex_descriptor_;
  metadata.reflection = SpotForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SpotForex

// optional string ValueDate = 1;
void SpotForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
void SpotForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
void SpotForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}
::std::string* SpotForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
void SpotForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.NetBasisChange)
  return netbasischange_;
}
void SpotForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
void SpotForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.PercentageChange)
  return percentagechange_;
}
void SpotForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.PercentageChange)
}

// optional string BuyDate = 4;
void SpotForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
void SpotForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
void SpotForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}
::std::string* SpotForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.BuyDate)
}

// optional string BuyTime = 5;
void SpotForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
void SpotForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
void SpotForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}
::std::string* SpotForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.BuyTime)
}

// optional string SellDate = 6;
void SpotForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
void SpotForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
void SpotForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.SellDate)
}
::std::string* SpotForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.SellDate)
}

// optional string SellTime = 7;
void SpotForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
void SpotForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
void SpotForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotForex.SellTime)
}
::std::string* SpotForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotForex.SellTime)
}

// optional int64 LastBuyRate = 8;
void SpotForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastBuyRate)
  return lastbuyrate_;
}
void SpotForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
void SpotForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastSellRate)
  return lastsellrate_;
}
void SpotForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
void SpotForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastBuyAllin)
  return lastbuyallin_;
}
void SpotForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
void SpotForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LastSellAllin)
  return lastsellallin_;
}
void SpotForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LastSellAllin)
}

// optional int64 HighRate = 12;
void SpotForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.HighRate)
  return highrate_;
}
void SpotForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.HighRate)
}

// optional int64 LowRate = 13;
void SpotForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.LowRate)
  return lowrate_;
}
void SpotForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.LowRate)
}

// optional int64 OpenRate = 14;
void SpotForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.OpenRate)
  return openrate_;
}
void SpotForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
void SpotForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.HistoryCloseRate)
  return historycloserate_;
}
void SpotForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
void SpotForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.CloseRate)
  return closerate_;
}
void SpotForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
void SpotForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
::google::protobuf::int32 SpotForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AmountLevelRate)
  return amountlevelrate_;
}
void SpotForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
void SpotForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
::google::protobuf::int32 SpotForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AmountLevelAllin)
  return amountlevelallin_;
}
void SpotForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
void SpotForex::clear_rateside() {
  rateside_ = 0;
}
::google::protobuf::int32 SpotForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.RateSide)
  return rateside_;
}
void SpotForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.RateSide)
}

// optional int32 AllinSide = 20;
void SpotForex::clear_allinside() {
  allinside_ = 0;
}
::google::protobuf::int32 SpotForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotForex.AllinSide)
  return allinside_;
}
void SpotForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotForex.AllinSide)
}

inline const SpotForex* SpotForex::internal_default_instance() {
  return &SpotForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ForwardForex::kValueDateFieldNumber;
const int ForwardForex::kNetBasisChangeFieldNumber;
const int ForwardForex::kPercentageChangeFieldNumber;
const int ForwardForex::kBuyDateFieldNumber;
const int ForwardForex::kBuyTimeFieldNumber;
const int ForwardForex::kSellDateFieldNumber;
const int ForwardForex::kSellTimeFieldNumber;
const int ForwardForex::kLastBuyRateFieldNumber;
const int ForwardForex::kLastSellRateFieldNumber;
const int ForwardForex::kLastBuyAllinFieldNumber;
const int ForwardForex::kLastSellAllinFieldNumber;
const int ForwardForex::kHighRateFieldNumber;
const int ForwardForex::kLowRateFieldNumber;
const int ForwardForex::kOpenRateFieldNumber;
const int ForwardForex::kHistoryCloseRateFieldNumber;
const int ForwardForex::kCloseRateFieldNumber;
const int ForwardForex::kAmountLevelRateFieldNumber;
const int ForwardForex::kAmountLevelAllinFieldNumber;
const int ForwardForex::kRateSideFieldNumber;
const int ForwardForex::kAllinSideFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ForwardForex::ForwardForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ForwardForex)
}

void ForwardForex::InitAsDefaultInstance() {
}

ForwardForex::ForwardForex(const ForwardForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ForwardForex)
}

void ForwardForex::SharedCtor() {
  valuedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&netbasischange_, 0, reinterpret_cast<char*>(&allinside_) -
    reinterpret_cast<char*>(&netbasischange_) + sizeof(allinside_));
  _cached_size_ = 0;
}

ForwardForex::~ForwardForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ForwardForex)
  SharedDtor();
}

void ForwardForex::SharedDtor() {
  valuedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ForwardForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ForwardForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ForwardForex_descriptor_;
}

const ForwardForex& ForwardForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ForwardForex> ForwardForex_default_instance_;

ForwardForex* ForwardForex::New(::google::protobuf::Arena* arena) const {
  ForwardForex* n = new ForwardForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ForwardForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ForwardForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ForwardForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ForwardForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(netbasischange_, lastbuyrate_);
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lastsellrate_, closerate_);
  ZR_(amountlevelrate_, allinside_);

#undef ZR_HELPER_
#undef ZR_

}

bool ForwardForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ForwardForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ValueDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuedate().data(), this->valuedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForwardForex.ValueDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_NetBasisChange;
        break;
      }

      // optional int64 NetBasisChange = 2;
      case 2: {
        if (tag == 16) {
         parse_NetBasisChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &netbasischange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PercentageChange;
        break;
      }

      // optional int64 PercentageChange = 3;
      case 3: {
        if (tag == 24) {
         parse_PercentageChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &percentagechange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BuyDate;
        break;
      }

      // optional string BuyDate = 4;
      case 4: {
        if (tag == 34) {
         parse_BuyDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buydate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buydate().data(), this->buydate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForwardForex.BuyDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_BuyTime;
        break;
      }

      // optional string BuyTime = 5;
      case 5: {
        if (tag == 42) {
         parse_BuyTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buytime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buytime().data(), this->buytime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForwardForex.BuyTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_SellDate;
        break;
      }

      // optional string SellDate = 6;
      case 6: {
        if (tag == 50) {
         parse_SellDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selldate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selldate().data(), this->selldate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForwardForex.SellDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SellTime;
        break;
      }

      // optional string SellTime = 7;
      case 7: {
        if (tag == 58) {
         parse_SellTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selltime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selltime().data(), this->selltime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ForwardForex.SellTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastBuyRate;
        break;
      }

      // optional int64 LastBuyRate = 8;
      case 8: {
        if (tag == 64) {
         parse_LastBuyRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_LastSellRate;
        break;
      }

      // optional int64 LastSellRate = 9;
      case 9: {
        if (tag == 72) {
         parse_LastSellRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LastBuyAllin;
        break;
      }

      // optional int64 LastBuyAllin = 10;
      case 10: {
        if (tag == 80) {
         parse_LastBuyAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LastSellAllin;
        break;
      }

      // optional int64 LastSellAllin = 11;
      case 11: {
        if (tag == 88) {
         parse_LastSellAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HistoryCloseRate;
        break;
      }

      // optional int64 HistoryCloseRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HistoryCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historycloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 16;
      case 16: {
        if (tag == 128) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AmountLevelRate;
        break;
      }

      // optional int32 AmountLevelRate = 17;
      case 17: {
        if (tag == 136) {
         parse_AmountLevelRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_AmountLevelAllin;
        break;
      }

      // optional int32 AmountLevelAllin = 18;
      case 18: {
        if (tag == 144) {
         parse_AmountLevelAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_RateSide;
        break;
      }

      // optional int32 RateSide = 19;
      case 19: {
        if (tag == 152) {
         parse_RateSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AllinSide;
        break;
      }

      // optional int32 AllinSide = 20;
      case 20: {
        if (tag == 160) {
         parse_AllinSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &allinside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ForwardForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ForwardForex)
  return false;
#undef DO_
}

void ForwardForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ForwardForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.ValueDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valuedate(), output);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->netbasischange(), output);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->percentagechange(), output);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.BuyDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->buydate(), output);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.BuyTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->buytime(), output);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.SellDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->selldate(), output);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.SellTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->selltime(), output);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastbuyrate(), output);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->lastsellrate(), output);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastbuyallin(), output);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lastsellallin(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openrate(), output);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->historycloserate(), output);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closerate(), output);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->amountlevelrate(), output);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->amountlevelallin(), output);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->rateside(), output);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->allinside(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ForwardForex)
}

::google::protobuf::uint8* ForwardForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ForwardForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.ValueDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valuedate(), target);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->netbasischange(), target);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->percentagechange(), target);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.BuyDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->buydate(), target);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.BuyTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->buytime(), target);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.SellDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->selldate(), target);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ForwardForex.SellTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->selltime(), target);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastbuyrate(), target);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->lastsellrate(), target);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastbuyallin(), target);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lastsellallin(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openrate(), target);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->historycloserate(), target);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closerate(), target);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->amountlevelrate(), target);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->amountlevelallin(), target);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->rateside(), target);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->allinside(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ForwardForex)
  return target;
}

size_t ForwardForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ForwardForex)
  size_t total_size = 0;

  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuedate());
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->netbasischange());
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->percentagechange());
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buydate());
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buytime());
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selldate());
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selltime());
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyrate());
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellrate());
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyallin());
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellallin());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historycloserate());
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelrate());
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelallin());
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateside());
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->allinside());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ForwardForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ForwardForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ForwardForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ForwardForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ForwardForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ForwardForex)
    UnsafeMergeFrom(*source);
  }
}

void ForwardForex::MergeFrom(const ForwardForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ForwardForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ForwardForex::UnsafeMergeFrom(const ForwardForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.valuedate().size() > 0) {

    valuedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuedate_);
  }
  if (from.netbasischange() != 0) {
    set_netbasischange(from.netbasischange());
  }
  if (from.percentagechange() != 0) {
    set_percentagechange(from.percentagechange());
  }
  if (from.buydate().size() > 0) {

    buydate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buydate_);
  }
  if (from.buytime().size() > 0) {

    buytime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buytime_);
  }
  if (from.selldate().size() > 0) {

    selldate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selldate_);
  }
  if (from.selltime().size() > 0) {

    selltime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selltime_);
  }
  if (from.lastbuyrate() != 0) {
    set_lastbuyrate(from.lastbuyrate());
  }
  if (from.lastsellrate() != 0) {
    set_lastsellrate(from.lastsellrate());
  }
  if (from.lastbuyallin() != 0) {
    set_lastbuyallin(from.lastbuyallin());
  }
  if (from.lastsellallin() != 0) {
    set_lastsellallin(from.lastsellallin());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.historycloserate() != 0) {
    set_historycloserate(from.historycloserate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.amountlevelrate() != 0) {
    set_amountlevelrate(from.amountlevelrate());
  }
  if (from.amountlevelallin() != 0) {
    set_amountlevelallin(from.amountlevelallin());
  }
  if (from.rateside() != 0) {
    set_rateside(from.rateside());
  }
  if (from.allinside() != 0) {
    set_allinside(from.allinside());
  }
}

void ForwardForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ForwardForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ForwardForex::CopyFrom(const ForwardForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ForwardForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ForwardForex::IsInitialized() const {

  return true;
}

void ForwardForex::Swap(ForwardForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ForwardForex::InternalSwap(ForwardForex* other) {
  valuedate_.Swap(&other->valuedate_);
  std::swap(netbasischange_, other->netbasischange_);
  std::swap(percentagechange_, other->percentagechange_);
  buydate_.Swap(&other->buydate_);
  buytime_.Swap(&other->buytime_);
  selldate_.Swap(&other->selldate_);
  selltime_.Swap(&other->selltime_);
  std::swap(lastbuyrate_, other->lastbuyrate_);
  std::swap(lastsellrate_, other->lastsellrate_);
  std::swap(lastbuyallin_, other->lastbuyallin_);
  std::swap(lastsellallin_, other->lastsellallin_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(historycloserate_, other->historycloserate_);
  std::swap(closerate_, other->closerate_);
  std::swap(amountlevelrate_, other->amountlevelrate_);
  std::swap(amountlevelallin_, other->amountlevelallin_);
  std::swap(rateside_, other->rateside_);
  std::swap(allinside_, other->allinside_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ForwardForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ForwardForex_descriptor_;
  metadata.reflection = ForwardForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ForwardForex

// optional string ValueDate = 1;
void ForwardForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForwardForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
void ForwardForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
void ForwardForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}
::std::string* ForwardForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForwardForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
void ForwardForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.NetBasisChange)
  return netbasischange_;
}
void ForwardForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
void ForwardForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.PercentageChange)
  return percentagechange_;
}
void ForwardForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.PercentageChange)
}

// optional string BuyDate = 4;
void ForwardForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForwardForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
void ForwardForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
void ForwardForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}
::std::string* ForwardForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForwardForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.BuyDate)
}

// optional string BuyTime = 5;
void ForwardForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForwardForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
void ForwardForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
void ForwardForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}
::std::string* ForwardForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForwardForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.BuyTime)
}

// optional string SellDate = 6;
void ForwardForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForwardForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
void ForwardForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
void ForwardForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}
::std::string* ForwardForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForwardForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.SellDate)
}

// optional string SellTime = 7;
void ForwardForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ForwardForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
void ForwardForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
void ForwardForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}
::std::string* ForwardForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ForwardForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ForwardForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ForwardForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ForwardForex.SellTime)
}

// optional int64 LastBuyRate = 8;
void ForwardForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastBuyRate)
  return lastbuyrate_;
}
void ForwardForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
void ForwardForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastSellRate)
  return lastsellrate_;
}
void ForwardForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
void ForwardForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastBuyAllin)
  return lastbuyallin_;
}
void ForwardForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
void ForwardForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LastSellAllin)
  return lastsellallin_;
}
void ForwardForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LastSellAllin)
}

// optional int64 HighRate = 12;
void ForwardForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.HighRate)
  return highrate_;
}
void ForwardForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.HighRate)
}

// optional int64 LowRate = 13;
void ForwardForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.LowRate)
  return lowrate_;
}
void ForwardForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.LowRate)
}

// optional int64 OpenRate = 14;
void ForwardForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.OpenRate)
  return openrate_;
}
void ForwardForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
void ForwardForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.HistoryCloseRate)
  return historycloserate_;
}
void ForwardForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
void ForwardForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ForwardForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.CloseRate)
  return closerate_;
}
void ForwardForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
void ForwardForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
::google::protobuf::int32 ForwardForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AmountLevelRate)
  return amountlevelrate_;
}
void ForwardForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
void ForwardForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
::google::protobuf::int32 ForwardForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AmountLevelAllin)
  return amountlevelallin_;
}
void ForwardForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
void ForwardForex::clear_rateside() {
  rateside_ = 0;
}
::google::protobuf::int32 ForwardForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.RateSide)
  return rateside_;
}
void ForwardForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.RateSide)
}

// optional int32 AllinSide = 20;
void ForwardForex::clear_allinside() {
  allinside_ = 0;
}
::google::protobuf::int32 ForwardForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ForwardForex.AllinSide)
  return allinside_;
}
void ForwardForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ForwardForex.AllinSide)
}

inline const ForwardForex* ForwardForex::internal_default_instance() {
  return &ForwardForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int NonDeliverableForwardsForex::kValueDateFieldNumber;
const int NonDeliverableForwardsForex::kNetBasisChangeFieldNumber;
const int NonDeliverableForwardsForex::kPercentageChangeFieldNumber;
const int NonDeliverableForwardsForex::kBuyDateFieldNumber;
const int NonDeliverableForwardsForex::kBuyTimeFieldNumber;
const int NonDeliverableForwardsForex::kSellDateFieldNumber;
const int NonDeliverableForwardsForex::kSellTimeFieldNumber;
const int NonDeliverableForwardsForex::kLastBuyRateFieldNumber;
const int NonDeliverableForwardsForex::kLastSellRateFieldNumber;
const int NonDeliverableForwardsForex::kLastBuyAllinFieldNumber;
const int NonDeliverableForwardsForex::kLastSellAllinFieldNumber;
const int NonDeliverableForwardsForex::kHighRateFieldNumber;
const int NonDeliverableForwardsForex::kLowRateFieldNumber;
const int NonDeliverableForwardsForex::kOpenRateFieldNumber;
const int NonDeliverableForwardsForex::kHistoryCloseRateFieldNumber;
const int NonDeliverableForwardsForex::kCloseRateFieldNumber;
const int NonDeliverableForwardsForex::kAmountLevelRateFieldNumber;
const int NonDeliverableForwardsForex::kAmountLevelAllinFieldNumber;
const int NonDeliverableForwardsForex::kRateSideFieldNumber;
const int NonDeliverableForwardsForex::kAllinSideFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

NonDeliverableForwardsForex::NonDeliverableForwardsForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
}

void NonDeliverableForwardsForex::InitAsDefaultInstance() {
}

NonDeliverableForwardsForex::NonDeliverableForwardsForex(const NonDeliverableForwardsForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
}

void NonDeliverableForwardsForex::SharedCtor() {
  valuedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&netbasischange_, 0, reinterpret_cast<char*>(&allinside_) -
    reinterpret_cast<char*>(&netbasischange_) + sizeof(allinside_));
  _cached_size_ = 0;
}

NonDeliverableForwardsForex::~NonDeliverableForwardsForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  SharedDtor();
}

void NonDeliverableForwardsForex::SharedDtor() {
  valuedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void NonDeliverableForwardsForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* NonDeliverableForwardsForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return NonDeliverableForwardsForex_descriptor_;
}

const NonDeliverableForwardsForex& NonDeliverableForwardsForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<NonDeliverableForwardsForex> NonDeliverableForwardsForex_default_instance_;

NonDeliverableForwardsForex* NonDeliverableForwardsForex::New(::google::protobuf::Arena* arena) const {
  NonDeliverableForwardsForex* n = new NonDeliverableForwardsForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void NonDeliverableForwardsForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(NonDeliverableForwardsForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<NonDeliverableForwardsForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(netbasischange_, lastbuyrate_);
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lastsellrate_, closerate_);
  ZR_(amountlevelrate_, allinside_);

#undef ZR_HELPER_
#undef ZR_

}

bool NonDeliverableForwardsForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ValueDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuedate().data(), this->valuedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_NetBasisChange;
        break;
      }

      // optional int64 NetBasisChange = 2;
      case 2: {
        if (tag == 16) {
         parse_NetBasisChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &netbasischange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PercentageChange;
        break;
      }

      // optional int64 PercentageChange = 3;
      case 3: {
        if (tag == 24) {
         parse_PercentageChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &percentagechange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BuyDate;
        break;
      }

      // optional string BuyDate = 4;
      case 4: {
        if (tag == 34) {
         parse_BuyDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buydate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buydate().data(), this->buydate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_BuyTime;
        break;
      }

      // optional string BuyTime = 5;
      case 5: {
        if (tag == 42) {
         parse_BuyTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buytime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buytime().data(), this->buytime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_SellDate;
        break;
      }

      // optional string SellDate = 6;
      case 6: {
        if (tag == 50) {
         parse_SellDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selldate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selldate().data(), this->selldate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SellTime;
        break;
      }

      // optional string SellTime = 7;
      case 7: {
        if (tag == 58) {
         parse_SellTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selltime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selltime().data(), this->selltime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastBuyRate;
        break;
      }

      // optional int64 LastBuyRate = 8;
      case 8: {
        if (tag == 64) {
         parse_LastBuyRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_LastSellRate;
        break;
      }

      // optional int64 LastSellRate = 9;
      case 9: {
        if (tag == 72) {
         parse_LastSellRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LastBuyAllin;
        break;
      }

      // optional int64 LastBuyAllin = 10;
      case 10: {
        if (tag == 80) {
         parse_LastBuyAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LastSellAllin;
        break;
      }

      // optional int64 LastSellAllin = 11;
      case 11: {
        if (tag == 88) {
         parse_LastSellAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HistoryCloseRate;
        break;
      }

      // optional int64 HistoryCloseRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HistoryCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historycloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 16;
      case 16: {
        if (tag == 128) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AmountLevelRate;
        break;
      }

      // optional int32 AmountLevelRate = 17;
      case 17: {
        if (tag == 136) {
         parse_AmountLevelRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_AmountLevelAllin;
        break;
      }

      // optional int32 AmountLevelAllin = 18;
      case 18: {
        if (tag == 144) {
         parse_AmountLevelAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_RateSide;
        break;
      }

      // optional int32 RateSide = 19;
      case 19: {
        if (tag == 152) {
         parse_RateSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AllinSide;
        break;
      }

      // optional int32 AllinSide = 20;
      case 20: {
        if (tag == 160) {
         parse_AllinSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &allinside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  return false;
#undef DO_
}

void NonDeliverableForwardsForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valuedate(), output);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->netbasischange(), output);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->percentagechange(), output);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->buydate(), output);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->buytime(), output);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->selldate(), output);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->selltime(), output);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastbuyrate(), output);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->lastsellrate(), output);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastbuyallin(), output);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lastsellallin(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openrate(), output);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->historycloserate(), output);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closerate(), output);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->amountlevelrate(), output);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->amountlevelallin(), output);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->rateside(), output);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->allinside(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
}

::google::protobuf::uint8* NonDeliverableForwardsForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valuedate(), target);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->netbasischange(), target);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->percentagechange(), target);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->buydate(), target);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->buytime(), target);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->selldate(), target);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->selltime(), target);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastbuyrate(), target);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->lastsellrate(), target);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastbuyallin(), target);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lastsellallin(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openrate(), target);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->historycloserate(), target);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closerate(), target);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->amountlevelrate(), target);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->amountlevelallin(), target);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->rateside(), target);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->allinside(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  return target;
}

size_t NonDeliverableForwardsForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  size_t total_size = 0;

  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuedate());
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->netbasischange());
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->percentagechange());
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buydate());
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buytime());
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selldate());
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selltime());
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyrate());
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellrate());
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyallin());
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellallin());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historycloserate());
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelrate());
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelallin());
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateside());
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->allinside());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void NonDeliverableForwardsForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const NonDeliverableForwardsForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const NonDeliverableForwardsForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
    UnsafeMergeFrom(*source);
  }
}

void NonDeliverableForwardsForex::MergeFrom(const NonDeliverableForwardsForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void NonDeliverableForwardsForex::UnsafeMergeFrom(const NonDeliverableForwardsForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.valuedate().size() > 0) {

    valuedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuedate_);
  }
  if (from.netbasischange() != 0) {
    set_netbasischange(from.netbasischange());
  }
  if (from.percentagechange() != 0) {
    set_percentagechange(from.percentagechange());
  }
  if (from.buydate().size() > 0) {

    buydate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buydate_);
  }
  if (from.buytime().size() > 0) {

    buytime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buytime_);
  }
  if (from.selldate().size() > 0) {

    selldate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selldate_);
  }
  if (from.selltime().size() > 0) {

    selltime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selltime_);
  }
  if (from.lastbuyrate() != 0) {
    set_lastbuyrate(from.lastbuyrate());
  }
  if (from.lastsellrate() != 0) {
    set_lastsellrate(from.lastsellrate());
  }
  if (from.lastbuyallin() != 0) {
    set_lastbuyallin(from.lastbuyallin());
  }
  if (from.lastsellallin() != 0) {
    set_lastsellallin(from.lastsellallin());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.historycloserate() != 0) {
    set_historycloserate(from.historycloserate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.amountlevelrate() != 0) {
    set_amountlevelrate(from.amountlevelrate());
  }
  if (from.amountlevelallin() != 0) {
    set_amountlevelallin(from.amountlevelallin());
  }
  if (from.rateside() != 0) {
    set_rateside(from.rateside());
  }
  if (from.allinside() != 0) {
    set_allinside(from.allinside());
  }
}

void NonDeliverableForwardsForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NonDeliverableForwardsForex::CopyFrom(const NonDeliverableForwardsForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.NonDeliverableForwardsForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool NonDeliverableForwardsForex::IsInitialized() const {

  return true;
}

void NonDeliverableForwardsForex::Swap(NonDeliverableForwardsForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void NonDeliverableForwardsForex::InternalSwap(NonDeliverableForwardsForex* other) {
  valuedate_.Swap(&other->valuedate_);
  std::swap(netbasischange_, other->netbasischange_);
  std::swap(percentagechange_, other->percentagechange_);
  buydate_.Swap(&other->buydate_);
  buytime_.Swap(&other->buytime_);
  selldate_.Swap(&other->selldate_);
  selltime_.Swap(&other->selltime_);
  std::swap(lastbuyrate_, other->lastbuyrate_);
  std::swap(lastsellrate_, other->lastsellrate_);
  std::swap(lastbuyallin_, other->lastbuyallin_);
  std::swap(lastsellallin_, other->lastsellallin_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(historycloserate_, other->historycloserate_);
  std::swap(closerate_, other->closerate_);
  std::swap(amountlevelrate_, other->amountlevelrate_);
  std::swap(amountlevelallin_, other->amountlevelallin_);
  std::swap(rateside_, other->rateside_);
  std::swap(allinside_, other->allinside_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata NonDeliverableForwardsForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = NonDeliverableForwardsForex_descriptor_;
  metadata.reflection = NonDeliverableForwardsForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// NonDeliverableForwardsForex

// optional string ValueDate = 1;
void NonDeliverableForwardsForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NonDeliverableForwardsForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
void NonDeliverableForwardsForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
void NonDeliverableForwardsForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}
::std::string* NonDeliverableForwardsForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NonDeliverableForwardsForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
void NonDeliverableForwardsForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.NetBasisChange)
  return netbasischange_;
}
void NonDeliverableForwardsForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
void NonDeliverableForwardsForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.PercentageChange)
  return percentagechange_;
}
void NonDeliverableForwardsForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.PercentageChange)
}

// optional string BuyDate = 4;
void NonDeliverableForwardsForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NonDeliverableForwardsForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
void NonDeliverableForwardsForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
void NonDeliverableForwardsForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}
::std::string* NonDeliverableForwardsForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NonDeliverableForwardsForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyDate)
}

// optional string BuyTime = 5;
void NonDeliverableForwardsForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NonDeliverableForwardsForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
void NonDeliverableForwardsForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
void NonDeliverableForwardsForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}
::std::string* NonDeliverableForwardsForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NonDeliverableForwardsForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.BuyTime)
}

// optional string SellDate = 6;
void NonDeliverableForwardsForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NonDeliverableForwardsForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
void NonDeliverableForwardsForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
void NonDeliverableForwardsForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}
::std::string* NonDeliverableForwardsForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NonDeliverableForwardsForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellDate)
}

// optional string SellTime = 7;
void NonDeliverableForwardsForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& NonDeliverableForwardsForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
void NonDeliverableForwardsForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
void NonDeliverableForwardsForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}
::std::string* NonDeliverableForwardsForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* NonDeliverableForwardsForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void NonDeliverableForwardsForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.SellTime)
}

// optional int64 LastBuyRate = 8;
void NonDeliverableForwardsForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyRate)
  return lastbuyrate_;
}
void NonDeliverableForwardsForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
void NonDeliverableForwardsForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellRate)
  return lastsellrate_;
}
void NonDeliverableForwardsForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
void NonDeliverableForwardsForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyAllin)
  return lastbuyallin_;
}
void NonDeliverableForwardsForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
void NonDeliverableForwardsForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellAllin)
  return lastsellallin_;
}
void NonDeliverableForwardsForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LastSellAllin)
}

// optional int64 HighRate = 12;
void NonDeliverableForwardsForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HighRate)
  return highrate_;
}
void NonDeliverableForwardsForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HighRate)
}

// optional int64 LowRate = 13;
void NonDeliverableForwardsForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LowRate)
  return lowrate_;
}
void NonDeliverableForwardsForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.LowRate)
}

// optional int64 OpenRate = 14;
void NonDeliverableForwardsForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.OpenRate)
  return openrate_;
}
void NonDeliverableForwardsForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
void NonDeliverableForwardsForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HistoryCloseRate)
  return historycloserate_;
}
void NonDeliverableForwardsForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
void NonDeliverableForwardsForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 NonDeliverableForwardsForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.CloseRate)
  return closerate_;
}
void NonDeliverableForwardsForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
void NonDeliverableForwardsForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
::google::protobuf::int32 NonDeliverableForwardsForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelRate)
  return amountlevelrate_;
}
void NonDeliverableForwardsForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
void NonDeliverableForwardsForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
::google::protobuf::int32 NonDeliverableForwardsForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelAllin)
  return amountlevelallin_;
}
void NonDeliverableForwardsForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
void NonDeliverableForwardsForex::clear_rateside() {
  rateside_ = 0;
}
::google::protobuf::int32 NonDeliverableForwardsForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.RateSide)
  return rateside_;
}
void NonDeliverableForwardsForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.RateSide)
}

// optional int32 AllinSide = 20;
void NonDeliverableForwardsForex::clear_allinside() {
  allinside_ = 0;
}
::google::protobuf::int32 NonDeliverableForwardsForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AllinSide)
  return allinside_;
}
void NonDeliverableForwardsForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.NonDeliverableForwardsForex.AllinSide)
}

inline const NonDeliverableForwardsForex* NonDeliverableForwardsForex::internal_default_instance() {
  return &NonDeliverableForwardsForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SwapForex::kValueDateFieldNumber;
const int SwapForex::kNetBasisChangeFieldNumber;
const int SwapForex::kPercentageChangeFieldNumber;
const int SwapForex::kBuyDateFieldNumber;
const int SwapForex::kBuyTimeFieldNumber;
const int SwapForex::kSellDateFieldNumber;
const int SwapForex::kSellTimeFieldNumber;
const int SwapForex::kLastBuyRateFieldNumber;
const int SwapForex::kLastSellRateFieldNumber;
const int SwapForex::kLastBuyAllinFieldNumber;
const int SwapForex::kLastSellAllinFieldNumber;
const int SwapForex::kHighRateFieldNumber;
const int SwapForex::kLowRateFieldNumber;
const int SwapForex::kOpenRateFieldNumber;
const int SwapForex::kHistoryCloseRateFieldNumber;
const int SwapForex::kCloseRateFieldNumber;
const int SwapForex::kAmountLevelRateFieldNumber;
const int SwapForex::kAmountLevelAllinFieldNumber;
const int SwapForex::kRateSideFieldNumber;
const int SwapForex::kAllinSideFieldNumber;
const int SwapForex::kLegSignFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SwapForex::SwapForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SwapForex)
}

void SwapForex::InitAsDefaultInstance() {
}

SwapForex::SwapForex(const SwapForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SwapForex)
}

void SwapForex::SharedCtor() {
  valuedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&netbasischange_, 0, reinterpret_cast<char*>(&allinside_) -
    reinterpret_cast<char*>(&netbasischange_) + sizeof(allinside_));
  _cached_size_ = 0;
}

SwapForex::~SwapForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SwapForex)
  SharedDtor();
}

void SwapForex::SharedDtor() {
  valuedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  legsign_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SwapForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SwapForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SwapForex_descriptor_;
}

const SwapForex& SwapForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SwapForex> SwapForex_default_instance_;

SwapForex* SwapForex::New(::google::protobuf::Arena* arena) const {
  SwapForex* n = new SwapForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SwapForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SwapForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(SwapForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<SwapForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(netbasischange_, lastbuyrate_);
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(lastsellrate_, closerate_);
  ZR_(amountlevelrate_, allinside_);
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool SwapForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SwapForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string ValueDate = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valuedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valuedate().data(), this->valuedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.ValueDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_NetBasisChange;
        break;
      }

      // optional int64 NetBasisChange = 2;
      case 2: {
        if (tag == 16) {
         parse_NetBasisChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &netbasischange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_PercentageChange;
        break;
      }

      // optional int64 PercentageChange = 3;
      case 3: {
        if (tag == 24) {
         parse_PercentageChange:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &percentagechange_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(34)) goto parse_BuyDate;
        break;
      }

      // optional string BuyDate = 4;
      case 4: {
        if (tag == 34) {
         parse_BuyDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buydate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buydate().data(), this->buydate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.BuyDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_BuyTime;
        break;
      }

      // optional string BuyTime = 5;
      case 5: {
        if (tag == 42) {
         parse_BuyTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_buytime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->buytime().data(), this->buytime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.BuyTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_SellDate;
        break;
      }

      // optional string SellDate = 6;
      case 6: {
        if (tag == 50) {
         parse_SellDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selldate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selldate().data(), this->selldate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.SellDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(58)) goto parse_SellTime;
        break;
      }

      // optional string SellTime = 7;
      case 7: {
        if (tag == 58) {
         parse_SellTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_selltime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->selltime().data(), this->selltime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.SellTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_LastBuyRate;
        break;
      }

      // optional int64 LastBuyRate = 8;
      case 8: {
        if (tag == 64) {
         parse_LastBuyRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_LastSellRate;
        break;
      }

      // optional int64 LastSellRate = 9;
      case 9: {
        if (tag == 72) {
         parse_LastSellRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_LastBuyAllin;
        break;
      }

      // optional int64 LastBuyAllin = 10;
      case 10: {
        if (tag == 80) {
         parse_LastBuyAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastbuyallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_LastSellAllin;
        break;
      }

      // optional int64 LastSellAllin = 11;
      case 11: {
        if (tag == 88) {
         parse_LastSellAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastsellallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_HighRate;
        break;
      }

      // optional int64 HighRate = 12;
      case 12: {
        if (tag == 96) {
         parse_HighRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_LowRate;
        break;
      }

      // optional int64 LowRate = 13;
      case 13: {
        if (tag == 104) {
         parse_LowRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_OpenRate;
        break;
      }

      // optional int64 OpenRate = 14;
      case 14: {
        if (tag == 112) {
         parse_OpenRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_HistoryCloseRate;
        break;
      }

      // optional int64 HistoryCloseRate = 15;
      case 15: {
        if (tag == 120) {
         parse_HistoryCloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &historycloserate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_CloseRate;
        break;
      }

      // optional int64 CloseRate = 16;
      case 16: {
        if (tag == 128) {
         parse_CloseRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closerate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_AmountLevelRate;
        break;
      }

      // optional int32 AmountLevelRate = 17;
      case 17: {
        if (tag == 136) {
         parse_AmountLevelRate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelrate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_AmountLevelAllin;
        break;
      }

      // optional int32 AmountLevelAllin = 18;
      case 18: {
        if (tag == 144) {
         parse_AmountLevelAllin:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &amountlevelallin_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_RateSide;
        break;
      }

      // optional int32 RateSide = 19;
      case 19: {
        if (tag == 152) {
         parse_RateSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &rateside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_AllinSide;
        break;
      }

      // optional int32 AllinSide = 20;
      case 20: {
        if (tag == 160) {
         parse_AllinSide:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &allinside_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(170)) goto parse_LegSign;
        break;
      }

      // optional string LegSign = 21;
      case 21: {
        if (tag == 170) {
         parse_LegSign:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_legsign()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->legsign().data(), this->legsign().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SwapForex.LegSign"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SwapForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SwapForex)
  return false;
#undef DO_
}

void SwapForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SwapForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.ValueDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valuedate(), output);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->netbasischange(), output);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->percentagechange(), output);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.BuyDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->buydate(), output);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.BuyTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->buytime(), output);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.SellDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->selldate(), output);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.SellTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->selltime(), output);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->lastbuyrate(), output);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->lastsellrate(), output);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->lastbuyallin(), output);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->lastsellallin(), output);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->highrate(), output);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->lowrate(), output);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->openrate(), output);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->historycloserate(), output);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closerate(), output);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->amountlevelrate(), output);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->amountlevelallin(), output);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(19, this->rateside(), output);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(20, this->allinside(), output);
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.LegSign");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      21, this->legsign(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SwapForex)
}

::google::protobuf::uint8* SwapForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SwapForex)
  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valuedate().data(), this->valuedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.ValueDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valuedate(), target);
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->netbasischange(), target);
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->percentagechange(), target);
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buydate().data(), this->buydate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.BuyDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->buydate(), target);
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->buytime().data(), this->buytime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.BuyTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->buytime(), target);
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selldate().data(), this->selldate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.SellDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->selldate(), target);
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->selltime().data(), this->selltime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.SellTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->selltime(), target);
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->lastbuyrate(), target);
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->lastsellrate(), target);
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->lastbuyallin(), target);
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->lastsellallin(), target);
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->highrate(), target);
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->lowrate(), target);
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->openrate(), target);
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->historycloserate(), target);
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closerate(), target);
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->amountlevelrate(), target);
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->amountlevelallin(), target);
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(19, this->rateside(), target);
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(20, this->allinside(), target);
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->legsign().data(), this->legsign().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SwapForex.LegSign");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        21, this->legsign(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SwapForex)
  return target;
}

size_t SwapForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SwapForex)
  size_t total_size = 0;

  // optional string ValueDate = 1;
  if (this->valuedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valuedate());
  }

  // optional int64 NetBasisChange = 2;
  if (this->netbasischange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->netbasischange());
  }

  // optional int64 PercentageChange = 3;
  if (this->percentagechange() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->percentagechange());
  }

  // optional string BuyDate = 4;
  if (this->buydate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buydate());
  }

  // optional string BuyTime = 5;
  if (this->buytime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->buytime());
  }

  // optional string SellDate = 6;
  if (this->selldate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selldate());
  }

  // optional string SellTime = 7;
  if (this->selltime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->selltime());
  }

  // optional int64 LastBuyRate = 8;
  if (this->lastbuyrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyrate());
  }

  // optional int64 LastSellRate = 9;
  if (this->lastsellrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellrate());
  }

  // optional int64 LastBuyAllin = 10;
  if (this->lastbuyallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastbuyallin());
  }

  // optional int64 LastSellAllin = 11;
  if (this->lastsellallin() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastsellallin());
  }

  // optional int64 HighRate = 12;
  if (this->highrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highrate());
  }

  // optional int64 LowRate = 13;
  if (this->lowrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowrate());
  }

  // optional int64 OpenRate = 14;
  if (this->openrate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openrate());
  }

  // optional int64 HistoryCloseRate = 15;
  if (this->historycloserate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->historycloserate());
  }

  // optional int64 CloseRate = 16;
  if (this->closerate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closerate());
  }

  // optional int32 AmountLevelRate = 17;
  if (this->amountlevelrate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelrate());
  }

  // optional int32 AmountLevelAllin = 18;
  if (this->amountlevelallin() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->amountlevelallin());
  }

  // optional int32 RateSide = 19;
  if (this->rateside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->rateside());
  }

  // optional int32 AllinSide = 20;
  if (this->allinside() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->allinside());
  }

  // optional string LegSign = 21;
  if (this->legsign().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->legsign());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SwapForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SwapForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SwapForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SwapForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SwapForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SwapForex)
    UnsafeMergeFrom(*source);
  }
}

void SwapForex::MergeFrom(const SwapForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SwapForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SwapForex::UnsafeMergeFrom(const SwapForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.valuedate().size() > 0) {

    valuedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valuedate_);
  }
  if (from.netbasischange() != 0) {
    set_netbasischange(from.netbasischange());
  }
  if (from.percentagechange() != 0) {
    set_percentagechange(from.percentagechange());
  }
  if (from.buydate().size() > 0) {

    buydate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buydate_);
  }
  if (from.buytime().size() > 0) {

    buytime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.buytime_);
  }
  if (from.selldate().size() > 0) {

    selldate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selldate_);
  }
  if (from.selltime().size() > 0) {

    selltime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.selltime_);
  }
  if (from.lastbuyrate() != 0) {
    set_lastbuyrate(from.lastbuyrate());
  }
  if (from.lastsellrate() != 0) {
    set_lastsellrate(from.lastsellrate());
  }
  if (from.lastbuyallin() != 0) {
    set_lastbuyallin(from.lastbuyallin());
  }
  if (from.lastsellallin() != 0) {
    set_lastsellallin(from.lastsellallin());
  }
  if (from.highrate() != 0) {
    set_highrate(from.highrate());
  }
  if (from.lowrate() != 0) {
    set_lowrate(from.lowrate());
  }
  if (from.openrate() != 0) {
    set_openrate(from.openrate());
  }
  if (from.historycloserate() != 0) {
    set_historycloserate(from.historycloserate());
  }
  if (from.closerate() != 0) {
    set_closerate(from.closerate());
  }
  if (from.amountlevelrate() != 0) {
    set_amountlevelrate(from.amountlevelrate());
  }
  if (from.amountlevelallin() != 0) {
    set_amountlevelallin(from.amountlevelallin());
  }
  if (from.rateside() != 0) {
    set_rateside(from.rateside());
  }
  if (from.allinside() != 0) {
    set_allinside(from.allinside());
  }
  if (from.legsign().size() > 0) {

    legsign_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.legsign_);
  }
}

void SwapForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SwapForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SwapForex::CopyFrom(const SwapForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SwapForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SwapForex::IsInitialized() const {

  return true;
}

void SwapForex::Swap(SwapForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SwapForex::InternalSwap(SwapForex* other) {
  valuedate_.Swap(&other->valuedate_);
  std::swap(netbasischange_, other->netbasischange_);
  std::swap(percentagechange_, other->percentagechange_);
  buydate_.Swap(&other->buydate_);
  buytime_.Swap(&other->buytime_);
  selldate_.Swap(&other->selldate_);
  selltime_.Swap(&other->selltime_);
  std::swap(lastbuyrate_, other->lastbuyrate_);
  std::swap(lastsellrate_, other->lastsellrate_);
  std::swap(lastbuyallin_, other->lastbuyallin_);
  std::swap(lastsellallin_, other->lastsellallin_);
  std::swap(highrate_, other->highrate_);
  std::swap(lowrate_, other->lowrate_);
  std::swap(openrate_, other->openrate_);
  std::swap(historycloserate_, other->historycloserate_);
  std::swap(closerate_, other->closerate_);
  std::swap(amountlevelrate_, other->amountlevelrate_);
  std::swap(amountlevelallin_, other->amountlevelallin_);
  std::swap(rateside_, other->rateside_);
  std::swap(allinside_, other->allinside_);
  legsign_.Swap(&other->legsign_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SwapForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SwapForex_descriptor_;
  metadata.reflection = SwapForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SwapForex

// optional string ValueDate = 1;
void SwapForex::clear_valuedate() {
  valuedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::valuedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  return valuedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_valuedate(const ::std::string& value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
void SwapForex::set_valuedate(const char* value) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
void SwapForex::set_valuedate(const char* value, size_t size) {
  
  valuedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}
::std::string* SwapForex::mutable_valuedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  return valuedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_valuedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.ValueDate)
  
  return valuedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_valuedate(::std::string* valuedate) {
  if (valuedate != NULL) {
    
  } else {
    
  }
  valuedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valuedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.ValueDate)
}

// optional int64 NetBasisChange = 2;
void SwapForex::clear_netbasischange() {
  netbasischange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::netbasischange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.NetBasisChange)
  return netbasischange_;
}
void SwapForex::set_netbasischange(::google::protobuf::int64 value) {
  
  netbasischange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.NetBasisChange)
}

// optional int64 PercentageChange = 3;
void SwapForex::clear_percentagechange() {
  percentagechange_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::percentagechange() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.PercentageChange)
  return percentagechange_;
}
void SwapForex::set_percentagechange(::google::protobuf::int64 value) {
  
  percentagechange_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.PercentageChange)
}

// optional string BuyDate = 4;
void SwapForex::clear_buydate() {
  buydate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::buydate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  return buydate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_buydate(const ::std::string& value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
void SwapForex::set_buydate(const char* value) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
void SwapForex::set_buydate(const char* value, size_t size) {
  
  buydate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}
::std::string* SwapForex::mutable_buydate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  return buydate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_buydate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.BuyDate)
  
  return buydate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_buydate(::std::string* buydate) {
  if (buydate != NULL) {
    
  } else {
    
  }
  buydate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buydate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.BuyDate)
}

// optional string BuyTime = 5;
void SwapForex::clear_buytime() {
  buytime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::buytime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  return buytime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_buytime(const ::std::string& value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
void SwapForex::set_buytime(const char* value) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
void SwapForex::set_buytime(const char* value, size_t size) {
  
  buytime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}
::std::string* SwapForex::mutable_buytime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  return buytime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_buytime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.BuyTime)
  
  return buytime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_buytime(::std::string* buytime) {
  if (buytime != NULL) {
    
  } else {
    
  }
  buytime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), buytime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.BuyTime)
}

// optional string SellDate = 6;
void SwapForex::clear_selldate() {
  selldate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::selldate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.SellDate)
  return selldate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_selldate(const ::std::string& value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
void SwapForex::set_selldate(const char* value) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
void SwapForex::set_selldate(const char* value, size_t size) {
  
  selldate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.SellDate)
}
::std::string* SwapForex::mutable_selldate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.SellDate)
  return selldate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_selldate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.SellDate)
  
  return selldate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_selldate(::std::string* selldate) {
  if (selldate != NULL) {
    
  } else {
    
  }
  selldate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selldate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.SellDate)
}

// optional string SellTime = 7;
void SwapForex::clear_selltime() {
  selltime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::selltime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.SellTime)
  return selltime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_selltime(const ::std::string& value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
void SwapForex::set_selltime(const char* value) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
void SwapForex::set_selltime(const char* value, size_t size) {
  
  selltime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.SellTime)
}
::std::string* SwapForex::mutable_selltime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.SellTime)
  return selltime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_selltime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.SellTime)
  
  return selltime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_selltime(::std::string* selltime) {
  if (selltime != NULL) {
    
  } else {
    
  }
  selltime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), selltime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.SellTime)
}

// optional int64 LastBuyRate = 8;
void SwapForex::clear_lastbuyrate() {
  lastbuyrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::lastbuyrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastBuyRate)
  return lastbuyrate_;
}
void SwapForex::set_lastbuyrate(::google::protobuf::int64 value) {
  
  lastbuyrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastBuyRate)
}

// optional int64 LastSellRate = 9;
void SwapForex::clear_lastsellrate() {
  lastsellrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::lastsellrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastSellRate)
  return lastsellrate_;
}
void SwapForex::set_lastsellrate(::google::protobuf::int64 value) {
  
  lastsellrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastSellRate)
}

// optional int64 LastBuyAllin = 10;
void SwapForex::clear_lastbuyallin() {
  lastbuyallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::lastbuyallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastBuyAllin)
  return lastbuyallin_;
}
void SwapForex::set_lastbuyallin(::google::protobuf::int64 value) {
  
  lastbuyallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastBuyAllin)
}

// optional int64 LastSellAllin = 11;
void SwapForex::clear_lastsellallin() {
  lastsellallin_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::lastsellallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LastSellAllin)
  return lastsellallin_;
}
void SwapForex::set_lastsellallin(::google::protobuf::int64 value) {
  
  lastsellallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LastSellAllin)
}

// optional int64 HighRate = 12;
void SwapForex::clear_highrate() {
  highrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::highrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.HighRate)
  return highrate_;
}
void SwapForex::set_highrate(::google::protobuf::int64 value) {
  
  highrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.HighRate)
}

// optional int64 LowRate = 13;
void SwapForex::clear_lowrate() {
  lowrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::lowrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LowRate)
  return lowrate_;
}
void SwapForex::set_lowrate(::google::protobuf::int64 value) {
  
  lowrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LowRate)
}

// optional int64 OpenRate = 14;
void SwapForex::clear_openrate() {
  openrate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::openrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.OpenRate)
  return openrate_;
}
void SwapForex::set_openrate(::google::protobuf::int64 value) {
  
  openrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.OpenRate)
}

// optional int64 HistoryCloseRate = 15;
void SwapForex::clear_historycloserate() {
  historycloserate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::historycloserate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.HistoryCloseRate)
  return historycloserate_;
}
void SwapForex::set_historycloserate(::google::protobuf::int64 value) {
  
  historycloserate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.HistoryCloseRate)
}

// optional int64 CloseRate = 16;
void SwapForex::clear_closerate() {
  closerate_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SwapForex::closerate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.CloseRate)
  return closerate_;
}
void SwapForex::set_closerate(::google::protobuf::int64 value) {
  
  closerate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.CloseRate)
}

// optional int32 AmountLevelRate = 17;
void SwapForex::clear_amountlevelrate() {
  amountlevelrate_ = 0;
}
::google::protobuf::int32 SwapForex::amountlevelrate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AmountLevelRate)
  return amountlevelrate_;
}
void SwapForex::set_amountlevelrate(::google::protobuf::int32 value) {
  
  amountlevelrate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AmountLevelRate)
}

// optional int32 AmountLevelAllin = 18;
void SwapForex::clear_amountlevelallin() {
  amountlevelallin_ = 0;
}
::google::protobuf::int32 SwapForex::amountlevelallin() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AmountLevelAllin)
  return amountlevelallin_;
}
void SwapForex::set_amountlevelallin(::google::protobuf::int32 value) {
  
  amountlevelallin_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AmountLevelAllin)
}

// optional int32 RateSide = 19;
void SwapForex::clear_rateside() {
  rateside_ = 0;
}
::google::protobuf::int32 SwapForex::rateside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.RateSide)
  return rateside_;
}
void SwapForex::set_rateside(::google::protobuf::int32 value) {
  
  rateside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.RateSide)
}

// optional int32 AllinSide = 20;
void SwapForex::clear_allinside() {
  allinside_ = 0;
}
::google::protobuf::int32 SwapForex::allinside() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.AllinSide)
  return allinside_;
}
void SwapForex::set_allinside(::google::protobuf::int32 value) {
  
  allinside_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.AllinSide)
}

// optional string LegSign = 21;
void SwapForex::clear_legsign() {
  legsign_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SwapForex::legsign() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SwapForex.LegSign)
  return legsign_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_legsign(const ::std::string& value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
void SwapForex::set_legsign(const char* value) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
void SwapForex::set_legsign(const char* value, size_t size) {
  
  legsign_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SwapForex.LegSign)
}
::std::string* SwapForex::mutable_legsign() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SwapForex.LegSign)
  return legsign_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SwapForex::release_legsign() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SwapForex.LegSign)
  
  return legsign_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SwapForex::set_allocated_legsign(::std::string* legsign) {
  if (legsign != NULL) {
    
  } else {
    
  }
  legsign_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), legsign);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SwapForex.LegSign)
}

inline const SwapForex* SwapForex::internal_default_instance() {
  return &SwapForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int OptionForex::kFxTermFieldNumber;
const int OptionForex::kPremiumFieldNumber;
const int OptionForex::kVolatilityFieldNumber;
const int OptionForex::kVolumeFieldNumber;
const int OptionForex::kTradeDateFieldNumber;
const int OptionForex::kTradeTimeFieldNumber;
const int OptionForex::kPremiumTypeFieldNumber;
const int OptionForex::kOptionTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

OptionForex::OptionForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.OptionForex)
}

void OptionForex::InitAsDefaultInstance() {
}

OptionForex::OptionForex(const OptionForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.OptionForex)
}

void OptionForex::SharedCtor() {
  fxterm_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&premium_, 0, reinterpret_cast<char*>(&premiumtype_) -
    reinterpret_cast<char*>(&premium_) + sizeof(premiumtype_));
  _cached_size_ = 0;
}

OptionForex::~OptionForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.OptionForex)
  SharedDtor();
}

void OptionForex::SharedDtor() {
  fxterm_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void OptionForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* OptionForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return OptionForex_descriptor_;
}

const OptionForex& OptionForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<OptionForex> OptionForex_default_instance_;

OptionForex* OptionForex::New(::google::protobuf::Arena* arena) const {
  OptionForex* n = new OptionForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void OptionForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.OptionForex)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(OptionForex, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<OptionForex*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(premium_, premiumtype_);
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());

#undef ZR_HELPER_
#undef ZR_

}

bool OptionForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.OptionForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string FxTerm = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_fxterm()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->fxterm().data(), this->fxterm().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionForex.FxTerm"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_Premium;
        break;
      }

      // optional int64 Premium = 2;
      case 2: {
        if (tag == 16) {
         parse_Premium:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premium_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_Volatility;
        break;
      }

      // optional int64 Volatility = 3;
      case 3: {
        if (tag == 24) {
         parse_Volatility:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volatility_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_Volume;
        break;
      }

      // optional int64 Volume = 4;
      case 4: {
        if (tag == 32) {
         parse_Volume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradeDate;
        break;
      }

      // optional string TradeDate = 5;
      case 5: {
        if (tag == 42) {
         parse_TradeDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradedate().data(), this->tradedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionForex.TradeDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(50)) goto parse_TradeTime;
        break;
      }

      // optional string TradeTime = 6;
      case 6: {
        if (tag == 50) {
         parse_TradeTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradetime().data(), this->tradetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionForex.TradeTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_PremiumType;
        break;
      }

      // optional int32 PremiumType = 7;
      case 7: {
        if (tag == 56) {
         parse_PremiumType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &premiumtype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_OptionType;
        break;
      }

      // optional string OptionType = 8;
      case 8: {
        if (tag == 66) {
         parse_OptionType:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_optiontype()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->optiontype().data(), this->optiontype().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.OptionForex.OptionType"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.OptionForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.OptionForex)
  return false;
#undef DO_
}

void OptionForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.OptionForex)
  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fxterm().data(), this->fxterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.FxTerm");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->fxterm(), output);
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->premium(), output);
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->volatility(), output);
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->volume(), output);
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.TradeDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradedate(), output);
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.TradeTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->tradetime(), output);
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->premiumtype(), output);
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiontype().data(), this->optiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.OptionType");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->optiontype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.OptionForex)
}

::google::protobuf::uint8* OptionForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.OptionForex)
  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->fxterm().data(), this->fxterm().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.FxTerm");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->fxterm(), target);
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->premium(), target);
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->volatility(), target);
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->volume(), target);
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradedate().data(), this->tradedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.TradeDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradedate(), target);
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradetime().data(), this->tradetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.TradeTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->tradetime(), target);
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->premiumtype(), target);
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->optiontype().data(), this->optiontype().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.OptionForex.OptionType");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->optiontype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.OptionForex)
  return target;
}

size_t OptionForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.OptionForex)
  size_t total_size = 0;

  // optional string FxTerm = 1;
  if (this->fxterm().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->fxterm());
  }

  // optional int64 Premium = 2;
  if (this->premium() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premium());
  }

  // optional int64 Volatility = 3;
  if (this->volatility() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volatility());
  }

  // optional int64 Volume = 4;
  if (this->volume() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volume());
  }

  // optional string TradeDate = 5;
  if (this->tradedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradedate());
  }

  // optional string TradeTime = 6;
  if (this->tradetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradetime());
  }

  // optional int32 PremiumType = 7;
  if (this->premiumtype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->premiumtype());
  }

  // optional string OptionType = 8;
  if (this->optiontype().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->optiontype());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void OptionForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.OptionForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const OptionForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const OptionForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.OptionForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.OptionForex)
    UnsafeMergeFrom(*source);
  }
}

void OptionForex::MergeFrom(const OptionForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.OptionForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void OptionForex::UnsafeMergeFrom(const OptionForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.fxterm().size() > 0) {

    fxterm_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.fxterm_);
  }
  if (from.premium() != 0) {
    set_premium(from.premium());
  }
  if (from.volatility() != 0) {
    set_volatility(from.volatility());
  }
  if (from.volume() != 0) {
    set_volume(from.volume());
  }
  if (from.tradedate().size() > 0) {

    tradedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradedate_);
  }
  if (from.tradetime().size() > 0) {

    tradetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradetime_);
  }
  if (from.premiumtype() != 0) {
    set_premiumtype(from.premiumtype());
  }
  if (from.optiontype().size() > 0) {

    optiontype_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.optiontype_);
  }
}

void OptionForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.OptionForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void OptionForex::CopyFrom(const OptionForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.OptionForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool OptionForex::IsInitialized() const {

  return true;
}

void OptionForex::Swap(OptionForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void OptionForex::InternalSwap(OptionForex* other) {
  fxterm_.Swap(&other->fxterm_);
  std::swap(premium_, other->premium_);
  std::swap(volatility_, other->volatility_);
  std::swap(volume_, other->volume_);
  tradedate_.Swap(&other->tradedate_);
  tradetime_.Swap(&other->tradetime_);
  std::swap(premiumtype_, other->premiumtype_);
  optiontype_.Swap(&other->optiontype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata OptionForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = OptionForex_descriptor_;
  metadata.reflection = OptionForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// OptionForex

// optional string FxTerm = 1;
void OptionForex::clear_fxterm() {
  fxterm_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionForex::fxterm() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  return fxterm_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_fxterm(const ::std::string& value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
void OptionForex::set_fxterm(const char* value) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
void OptionForex::set_fxterm(const char* value, size_t size) {
  
  fxterm_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}
::std::string* OptionForex::mutable_fxterm() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  return fxterm_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionForex::release_fxterm() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.FxTerm)
  
  return fxterm_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_allocated_fxterm(::std::string* fxterm) {
  if (fxterm != NULL) {
    
  } else {
    
  }
  fxterm_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), fxterm);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.FxTerm)
}

// optional int64 Premium = 2;
void OptionForex::clear_premium() {
  premium_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionForex::premium() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Premium)
  return premium_;
}
void OptionForex::set_premium(::google::protobuf::int64 value) {
  
  premium_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Premium)
}

// optional int64 Volatility = 3;
void OptionForex::clear_volatility() {
  volatility_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionForex::volatility() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Volatility)
  return volatility_;
}
void OptionForex::set_volatility(::google::protobuf::int64 value) {
  
  volatility_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Volatility)
}

// optional int64 Volume = 4;
void OptionForex::clear_volume() {
  volume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 OptionForex::volume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.Volume)
  return volume_;
}
void OptionForex::set_volume(::google::protobuf::int64 value) {
  
  volume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.Volume)
}

// optional string TradeDate = 5;
void OptionForex::clear_tradedate() {
  tradedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionForex::tradedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  return tradedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_tradedate(const ::std::string& value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
void OptionForex::set_tradedate(const char* value) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
void OptionForex::set_tradedate(const char* value, size_t size) {
  
  tradedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}
::std::string* OptionForex::mutable_tradedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  return tradedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionForex::release_tradedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.TradeDate)
  
  return tradedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_allocated_tradedate(::std::string* tradedate) {
  if (tradedate != NULL) {
    
  } else {
    
  }
  tradedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.TradeDate)
}

// optional string TradeTime = 6;
void OptionForex::clear_tradetime() {
  tradetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionForex::tradetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  return tradetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_tradetime(const ::std::string& value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
void OptionForex::set_tradetime(const char* value) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
void OptionForex::set_tradetime(const char* value, size_t size) {
  
  tradetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}
::std::string* OptionForex::mutable_tradetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  return tradetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionForex::release_tradetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.TradeTime)
  
  return tradetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_allocated_tradetime(::std::string* tradetime) {
  if (tradetime != NULL) {
    
  } else {
    
  }
  tradetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.TradeTime)
}

// optional int32 PremiumType = 7;
void OptionForex::clear_premiumtype() {
  premiumtype_ = 0;
}
::google::protobuf::int32 OptionForex::premiumtype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.PremiumType)
  return premiumtype_;
}
void OptionForex::set_premiumtype(::google::protobuf::int32 value) {
  
  premiumtype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.PremiumType)
}

// optional string OptionType = 8;
void OptionForex::clear_optiontype() {
  optiontype_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& OptionForex::optiontype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.OptionForex.OptionType)
  return optiontype_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_optiontype(const ::std::string& value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
void OptionForex::set_optiontype(const char* value) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
void OptionForex::set_optiontype(const char* value, size_t size) {
  
  optiontype_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.OptionForex.OptionType)
}
::std::string* OptionForex::mutable_optiontype() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.OptionForex.OptionType)
  return optiontype_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* OptionForex::release_optiontype() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.OptionForex.OptionType)
  
  return optiontype_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void OptionForex::set_allocated_optiontype(::std::string* optiontype) {
  if (optiontype != NULL) {
    
  } else {
    
  }
  optiontype_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), optiontype);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.OptionForex.OptionType)
}

inline const OptionForex* OptionForex::internal_default_instance() {
  return &OptionForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SpotClosePriceForex::kClosePxFieldNumber;
const int SpotClosePriceForex::kUpdateDateFieldNumber;
const int SpotClosePriceForex::kUpdateTimeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SpotClosePriceForex::SpotClosePriceForex()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDCfetsForex_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.SpotClosePriceForex)
}

void SpotClosePriceForex::InitAsDefaultInstance() {
}

SpotClosePriceForex::SpotClosePriceForex(const SpotClosePriceForex& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.SpotClosePriceForex)
}

void SpotClosePriceForex::SharedCtor() {
  updatedate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  closepx_ = GOOGLE_LONGLONG(0);
  _cached_size_ = 0;
}

SpotClosePriceForex::~SpotClosePriceForex() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.SpotClosePriceForex)
  SharedDtor();
}

void SpotClosePriceForex::SharedDtor() {
  updatedate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SpotClosePriceForex::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SpotClosePriceForex::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return SpotClosePriceForex_descriptor_;
}

const SpotClosePriceForex& SpotClosePriceForex::default_instance() {
  protobuf_InitDefaults_MDCfetsForex_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<SpotClosePriceForex> SpotClosePriceForex_default_instance_;

SpotClosePriceForex* SpotClosePriceForex::New(::google::protobuf::Arena* arena) const {
  SpotClosePriceForex* n = new SpotClosePriceForex;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SpotClosePriceForex::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  closepx_ = GOOGLE_LONGLONG(0);
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

bool SpotClosePriceForex::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 ClosePx = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(18)) goto parse_UpdateDate;
        break;
      }

      // optional string UpdateDate = 2;
      case 2: {
        if (tag == 18) {
         parse_UpdateDate:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_updatedate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->updatedate().data(), this->updatedate().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(26)) goto parse_UpdateTime;
        break;
      }

      // optional string UpdateTime = 3;
      case 3: {
        if (tag == 26) {
         parse_UpdateTime:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_updatetime()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->updatetime().data(), this->updatetime().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.SpotClosePriceForex)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.SpotClosePriceForex)
  return false;
#undef DO_
}

void SpotClosePriceForex::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->closepx(), output);
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatedate().data(), this->updatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->updatedate(), output);
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatetime().data(), this->updatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->updatetime(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.SpotClosePriceForex)
}

::google::protobuf::uint8* SpotClosePriceForex::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->closepx(), target);
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatedate().data(), this->updatedate().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->updatedate(), target);
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->updatetime().data(), this->updatetime().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->updatetime(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.SpotClosePriceForex)
  return target;
}

size_t SpotClosePriceForex::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  size_t total_size = 0;

  // optional int64 ClosePx = 1;
  if (this->closepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional string UpdateDate = 2;
  if (this->updatedate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->updatedate());
  }

  // optional string UpdateTime = 3;
  if (this->updatetime().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->updatetime());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SpotClosePriceForex::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const SpotClosePriceForex* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SpotClosePriceForex>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.SpotClosePriceForex)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.SpotClosePriceForex)
    UnsafeMergeFrom(*source);
  }
}

void SpotClosePriceForex::MergeFrom(const SpotClosePriceForex& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void SpotClosePriceForex::UnsafeMergeFrom(const SpotClosePriceForex& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.updatedate().size() > 0) {

    updatedate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.updatedate_);
  }
  if (from.updatetime().size() > 0) {

    updatetime_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.updatetime_);
  }
}

void SpotClosePriceForex::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SpotClosePriceForex::CopyFrom(const SpotClosePriceForex& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.SpotClosePriceForex)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool SpotClosePriceForex::IsInitialized() const {

  return true;
}

void SpotClosePriceForex::Swap(SpotClosePriceForex* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SpotClosePriceForex::InternalSwap(SpotClosePriceForex* other) {
  std::swap(closepx_, other->closepx_);
  updatedate_.Swap(&other->updatedate_);
  updatetime_.Swap(&other->updatetime_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SpotClosePriceForex::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = SpotClosePriceForex_descriptor_;
  metadata.reflection = SpotClosePriceForex_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// SpotClosePriceForex

// optional int64 ClosePx = 1;
void SpotClosePriceForex::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 SpotClosePriceForex::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.ClosePx)
  return closepx_;
}
void SpotClosePriceForex::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.ClosePx)
}

// optional string UpdateDate = 2;
void SpotClosePriceForex::clear_updatedate() {
  updatedate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotClosePriceForex::updatedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  return updatedate_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceForex::set_updatedate(const ::std::string& value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
void SpotClosePriceForex::set_updatedate(const char* value) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
void SpotClosePriceForex::set_updatedate(const char* value, size_t size) {
  
  updatedate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}
::std::string* SpotClosePriceForex::mutable_updatedate() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  return updatedate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotClosePriceForex::release_updatedate() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
  
  return updatedate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceForex::set_allocated_updatedate(::std::string* updatedate) {
  if (updatedate != NULL) {
    
  } else {
    
  }
  updatedate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatedate);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateDate)
}

// optional string UpdateTime = 3;
void SpotClosePriceForex::clear_updatetime() {
  updatetime_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& SpotClosePriceForex::updatetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  return updatetime_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceForex::set_updatetime(const ::std::string& value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
void SpotClosePriceForex::set_updatetime(const char* value) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
void SpotClosePriceForex::set_updatetime(const char* value, size_t size) {
  
  updatetime_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}
::std::string* SpotClosePriceForex::mutable_updatetime() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  return updatetime_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* SpotClosePriceForex::release_updatetime() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
  
  return updatetime_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void SpotClosePriceForex::set_allocated_updatetime(::std::string* updatetime) {
  if (updatetime != NULL) {
    
  } else {
    
  }
  updatetime_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), updatetime);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.SpotClosePriceForex.UpdateTime)
}

inline const SpotClosePriceForex* SpotClosePriceForex::internal_default_instance() {
  return &SpotClosePriceForex_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
