// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ADVolumeByPrice.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "ADVolumeByPrice.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* ADVolumeByPrice_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADVolumeByPrice_reflection_ = NULL;
const ::google::protobuf::Descriptor* ADVolumeByPriceDetail_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  ADVolumeByPriceDetail_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_ADVolumeByPrice_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_ADVolumeByPrice_2eproto() {
  protobuf_AddDesc_ADVolumeByPrice_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "ADVolumeByPrice.proto");
  GOOGLE_CHECK(file != NULL);
  ADVolumeByPrice_descriptor_ = file->message_type(0);
  static const int ADVolumeByPrice_offsets_[11] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, details_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, datamultiplepowerof10_),
  };
  ADVolumeByPrice_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADVolumeByPrice_descriptor_,
      ADVolumeByPrice::internal_default_instance(),
      ADVolumeByPrice_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADVolumeByPrice),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPrice, _internal_metadata_));
  ADVolumeByPriceDetail_descriptor_ = file->message_type(1);
  static const int ADVolumeByPriceDetail_offsets_[8] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, tradeprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, totalqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, buyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, sellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, totalnumbers_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, buynumbers_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, sellnumbers_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, volumepernumber_),
  };
  ADVolumeByPriceDetail_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      ADVolumeByPriceDetail_descriptor_,
      ADVolumeByPriceDetail::internal_default_instance(),
      ADVolumeByPriceDetail_offsets_,
      -1,
      -1,
      -1,
      sizeof(ADVolumeByPriceDetail),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(ADVolumeByPriceDetail, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_ADVolumeByPrice_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADVolumeByPrice_descriptor_, ADVolumeByPrice::internal_default_instance());
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      ADVolumeByPriceDetail_descriptor_, ADVolumeByPriceDetail::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_ADVolumeByPrice_2eproto() {
  ADVolumeByPrice_default_instance_.Shutdown();
  delete ADVolumeByPrice_reflection_;
  ADVolumeByPriceDetail_default_instance_.Shutdown();
  delete ADVolumeByPriceDetail_reflection_;
}

void protobuf_InitDefaults_ADVolumeByPrice_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  ADVolumeByPrice_default_instance_.DefaultConstruct();
  ADVolumeByPriceDetail_default_instance_.DefaultConstruct();
  ADVolumeByPrice_default_instance_.get_mutable()->InitAsDefaultInstance();
  ADVolumeByPriceDetail_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_ADVolumeByPrice_2eproto_once_);
void protobuf_InitDefaults_ADVolumeByPrice_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_ADVolumeByPrice_2eproto_once_,
                 &protobuf_InitDefaults_ADVolumeByPrice_2eproto_impl);
}
void protobuf_AddDesc_ADVolumeByPrice_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\025ADVolumeByPrice.proto\022\032com.htsc.mdc.in"
    "sight.model\032\023ESecurityType.proto\032\027ESecur"
    "ityIDSource.proto\"\203\003\n\017ADVolumeByPrice\022\026\n"
    "\016HTSCSecurityID\030\001 \001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n"
    "\006MDTime\030\003 \001(\005\022\025\n\rDataTimestamp\030\004 \001(\003\022\?\n\020"
    "securityIDSource\030\005 \001(\0162%.com.htsc.mdc.mo"
    "del.ESecurityIDSource\0227\n\014securityType\030\006 "
    "\001(\0162!.com.htsc.mdc.model.ESecurityType\022\030"
    "\n\020TotalVolumeTrade\030\007 \001(\003\022B\n\007Details\030\010 \003("
    "\01321.com.htsc.mdc.insight.model.ADVolumeB"
    "yPriceDetail\022\024\n\014ExchangeDate\030\t \001(\005\022\024\n\014Ex"
    "changeTime\030\n \001(\005\022\035\n\025DataMultiplePowerOf1"
    "0\030\013 \001(\005\"\266\001\n\025ADVolumeByPriceDetail\022\022\n\nTra"
    "dePrice\030\001 \001(\003\022\020\n\010TotalQty\030\002 \001(\003\022\016\n\006BuyQt"
    "y\030\003 \001(\003\022\017\n\007SellQty\030\004 \001(\003\022\024\n\014TotalNumbers"
    "\030\005 \001(\003\022\022\n\nBuyNumbers\030\006 \001(\003\022\023\n\013SellNumber"
    "s\030\007 \001(\003\022\027\n\017VolumePerNumber\030\010 \001(\003B8\n\032com."
    "htsc.mdc.insight.modelB\025ADVolumeByPriceP"
    "rotosH\001\240\001\001b\006proto3", 738);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "ADVolumeByPrice.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_ADVolumeByPrice_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_ADVolumeByPrice_2eproto_once_);
void protobuf_AddDesc_ADVolumeByPrice_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_ADVolumeByPrice_2eproto_once_,
                 &protobuf_AddDesc_ADVolumeByPrice_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_ADVolumeByPrice_2eproto {
  StaticDescriptorInitializer_ADVolumeByPrice_2eproto() {
    protobuf_AddDesc_ADVolumeByPrice_2eproto();
  }
} static_descriptor_initializer_ADVolumeByPrice_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADVolumeByPrice::kHTSCSecurityIDFieldNumber;
const int ADVolumeByPrice::kMDDateFieldNumber;
const int ADVolumeByPrice::kMDTimeFieldNumber;
const int ADVolumeByPrice::kDataTimestampFieldNumber;
const int ADVolumeByPrice::kSecurityIDSourceFieldNumber;
const int ADVolumeByPrice::kSecurityTypeFieldNumber;
const int ADVolumeByPrice::kTotalVolumeTradeFieldNumber;
const int ADVolumeByPrice::kDetailsFieldNumber;
const int ADVolumeByPrice::kExchangeDateFieldNumber;
const int ADVolumeByPrice::kExchangeTimeFieldNumber;
const int ADVolumeByPrice::kDataMultiplePowerOf10FieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADVolumeByPrice::ADVolumeByPrice()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADVolumeByPrice)
}

void ADVolumeByPrice::InitAsDefaultInstance() {
}

ADVolumeByPrice::ADVolumeByPrice(const ADVolumeByPrice& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADVolumeByPrice)
}

void ADVolumeByPrice::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&datamultiplepowerof10_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(datamultiplepowerof10_));
  _cached_size_ = 0;
}

ADVolumeByPrice::~ADVolumeByPrice() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADVolumeByPrice)
  SharedDtor();
}

void ADVolumeByPrice::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ADVolumeByPrice::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADVolumeByPrice::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADVolumeByPrice_descriptor_;
}

const ADVolumeByPrice& ADVolumeByPrice::default_instance() {
  protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADVolumeByPrice> ADVolumeByPrice_default_instance_;

ADVolumeByPrice* ADVolumeByPrice::New(::google::protobuf::Arena* arena) const {
  ADVolumeByPrice* n = new ADVolumeByPrice;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADVolumeByPrice::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADVolumeByPrice, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADVolumeByPrice*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, totalvolumetrade_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(exchangedate_, datamultiplepowerof10_);

#undef ZR_HELPER_
#undef ZR_

  details_.Clear();
}

bool ADVolumeByPrice::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
      case 5: {
        if (tag == 40) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
      case 6: {
        if (tag == 48) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 7;
      case 7: {
        if (tag == 56) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_Details;
        break;
      }

      // repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
      case 8: {
        if (tag == 66) {
         parse_Details:
          DO_(input->IncrementRecursionDepth());
         parse_loop_Details:
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessageNoVirtualNoRecursionDepth(
                input, add_details()));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(66)) goto parse_loop_Details;
        input->UnsafeDecrementRecursionDepth();
        if (input->ExpectTag(72)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 9;
      case 9: {
        if (tag == 72) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 10;
      case 10: {
        if (tag == 80) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 11;
      case 11: {
        if (tag == 88) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADVolumeByPrice)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADVolumeByPrice)
  return false;
#undef DO_
}

void ADVolumeByPrice::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securitytype(), output);
  }

  // optional int64 TotalVolumeTrade = 7;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->totalvolumetrade(), output);
  }

  // repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
  for (unsigned int i = 0, n = this->details_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->details(i), output);
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(9, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(10, this->exchangetime(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(11, this->datamultiplepowerof10(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADVolumeByPrice)
}

::google::protobuf::uint8* ADVolumeByPrice::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securitytype(), target);
  }

  // optional int64 TotalVolumeTrade = 7;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->totalvolumetrade(), target);
  }

  // repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
  for (unsigned int i = 0, n = this->details_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageNoVirtualToArray(
        8, this->details(i), false, target);
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(9, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(10, this->exchangetime(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(11, this->datamultiplepowerof10(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADVolumeByPrice)
  return target;
}

size_t ADVolumeByPrice::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 6;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 TotalVolumeTrade = 7;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int32 ExchangeDate = 9;
  if (this->exchangedate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 10;
  if (this->exchangetime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 DataMultiplePowerOf10 = 11;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
  {
    unsigned int count = this->details_size();
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSizeNoVirtual(
          this->details(i));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADVolumeByPrice::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADVolumeByPrice* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADVolumeByPrice>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADVolumeByPrice)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADVolumeByPrice)
    UnsafeMergeFrom(*source);
  }
}

void ADVolumeByPrice::MergeFrom(const ADVolumeByPrice& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADVolumeByPrice::UnsafeMergeFrom(const ADVolumeByPrice& from) {
  GOOGLE_DCHECK(&from != this);
  details_.MergeFrom(from.details_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
}

void ADVolumeByPrice::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADVolumeByPrice::CopyFrom(const ADVolumeByPrice& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADVolumeByPrice)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADVolumeByPrice::IsInitialized() const {

  return true;
}

void ADVolumeByPrice::Swap(ADVolumeByPrice* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADVolumeByPrice::InternalSwap(ADVolumeByPrice* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  details_.UnsafeArenaSwap(&other->details_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADVolumeByPrice::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADVolumeByPrice_descriptor_;
  metadata.reflection = ADVolumeByPrice_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADVolumeByPrice

// optional string HTSCSecurityID = 1;
void ADVolumeByPrice::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& ADVolumeByPrice::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADVolumeByPrice::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
void ADVolumeByPrice::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
void ADVolumeByPrice::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}
::std::string* ADVolumeByPrice::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* ADVolumeByPrice::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ADVolumeByPrice::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.ADVolumeByPrice.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void ADVolumeByPrice::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 ADVolumeByPrice::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.MDDate)
  return mddate_;
}
void ADVolumeByPrice::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.MDDate)
}

// optional int32 MDTime = 3;
void ADVolumeByPrice::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 ADVolumeByPrice::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.MDTime)
  return mdtime_;
}
void ADVolumeByPrice::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.MDTime)
}

// optional int64 DataTimestamp = 4;
void ADVolumeByPrice::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPrice::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.DataTimestamp)
  return datatimestamp_;
}
void ADVolumeByPrice::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.DataTimestamp)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
void ADVolumeByPrice::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource ADVolumeByPrice::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void ADVolumeByPrice::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 6;
void ADVolumeByPrice::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType ADVolumeByPrice::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void ADVolumeByPrice::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.securityType)
}

// optional int64 TotalVolumeTrade = 7;
void ADVolumeByPrice::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPrice::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.TotalVolumeTrade)
  return totalvolumetrade_;
}
void ADVolumeByPrice::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.TotalVolumeTrade)
}

// repeated .com.htsc.mdc.insight.model.ADVolumeByPriceDetail Details = 8;
int ADVolumeByPrice::details_size() const {
  return details_.size();
}
void ADVolumeByPrice::clear_details() {
  details_.Clear();
}
const ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail& ADVolumeByPrice::details(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Get(index);
}
::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* ADVolumeByPrice::mutable_details(int index) {
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Mutable(index);
}
::com::htsc::mdc::insight::model::ADVolumeByPriceDetail* ADVolumeByPrice::add_details() {
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_.Add();
}
::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >*
ADVolumeByPrice::mutable_details() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return &details_;
}
const ::google::protobuf::RepeatedPtrField< ::com::htsc::mdc::insight::model::ADVolumeByPriceDetail >&
ADVolumeByPrice::details() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.ADVolumeByPrice.Details)
  return details_;
}

// optional int32 ExchangeDate = 9;
void ADVolumeByPrice::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 ADVolumeByPrice::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeDate)
  return exchangedate_;
}
void ADVolumeByPrice::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeDate)
}

// optional int32 ExchangeTime = 10;
void ADVolumeByPrice::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 ADVolumeByPrice::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeTime)
  return exchangetime_;
}
void ADVolumeByPrice::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.ExchangeTime)
}

// optional int32 DataMultiplePowerOf10 = 11;
void ADVolumeByPrice::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 ADVolumeByPrice::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPrice.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void ADVolumeByPrice::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPrice.DataMultiplePowerOf10)
}

inline const ADVolumeByPrice* ADVolumeByPrice::internal_default_instance() {
  return &ADVolumeByPrice_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ADVolumeByPriceDetail::kTradePriceFieldNumber;
const int ADVolumeByPriceDetail::kTotalQtyFieldNumber;
const int ADVolumeByPriceDetail::kBuyQtyFieldNumber;
const int ADVolumeByPriceDetail::kSellQtyFieldNumber;
const int ADVolumeByPriceDetail::kTotalNumbersFieldNumber;
const int ADVolumeByPriceDetail::kBuyNumbersFieldNumber;
const int ADVolumeByPriceDetail::kSellNumbersFieldNumber;
const int ADVolumeByPriceDetail::kVolumePerNumberFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ADVolumeByPriceDetail::ADVolumeByPriceDetail()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
}

void ADVolumeByPriceDetail::InitAsDefaultInstance() {
}

ADVolumeByPriceDetail::ADVolumeByPriceDetail(const ADVolumeByPriceDetail& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
}

void ADVolumeByPriceDetail::SharedCtor() {
  ::memset(&tradeprice_, 0, reinterpret_cast<char*>(&volumepernumber_) -
    reinterpret_cast<char*>(&tradeprice_) + sizeof(volumepernumber_));
  _cached_size_ = 0;
}

ADVolumeByPriceDetail::~ADVolumeByPriceDetail() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  SharedDtor();
}

void ADVolumeByPriceDetail::SharedDtor() {
}

void ADVolumeByPriceDetail::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ADVolumeByPriceDetail::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return ADVolumeByPriceDetail_descriptor_;
}

const ADVolumeByPriceDetail& ADVolumeByPriceDetail::default_instance() {
  protobuf_InitDefaults_ADVolumeByPrice_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<ADVolumeByPriceDetail> ADVolumeByPriceDetail_default_instance_;

ADVolumeByPriceDetail* ADVolumeByPriceDetail::New(::google::protobuf::Arena* arena) const {
  ADVolumeByPriceDetail* n = new ADVolumeByPriceDetail;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ADVolumeByPriceDetail::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(ADVolumeByPriceDetail, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<ADVolumeByPriceDetail*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(tradeprice_, volumepernumber_);

#undef ZR_HELPER_
#undef ZR_

}

bool ADVolumeByPriceDetail::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(127);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional int64 TradePrice = 1;
      case 1: {
        if (tag == 8) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &tradeprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_TotalQty;
        break;
      }

      // optional int64 TotalQty = 2;
      case 2: {
        if (tag == 16) {
         parse_TotalQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_BuyQty;
        break;
      }

      // optional int64 BuyQty = 3;
      case 3: {
        if (tag == 24) {
         parse_BuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_SellQty;
        break;
      }

      // optional int64 SellQty = 4;
      case 4: {
        if (tag == 32) {
         parse_SellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(40)) goto parse_TotalNumbers;
        break;
      }

      // optional int64 TotalNumbers = 5;
      case 5: {
        if (tag == 40) {
         parse_TotalNumbers:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalnumbers_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_BuyNumbers;
        break;
      }

      // optional int64 BuyNumbers = 6;
      case 6: {
        if (tag == 48) {
         parse_BuyNumbers:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buynumbers_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_SellNumbers;
        break;
      }

      // optional int64 SellNumbers = 7;
      case 7: {
        if (tag == 56) {
         parse_SellNumbers:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &sellnumbers_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_VolumePerNumber;
        break;
      }

      // optional int64 VolumePerNumber = 8;
      case 8: {
        if (tag == 64) {
         parse_VolumePerNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &volumepernumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  return false;
#undef DO_
}

void ADVolumeByPriceDetail::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  // optional int64 TradePrice = 1;
  if (this->tradeprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->tradeprice(), output);
  }

  // optional int64 TotalQty = 2;
  if (this->totalqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->totalqty(), output);
  }

  // optional int64 BuyQty = 3;
  if (this->buyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->buyqty(), output);
  }

  // optional int64 SellQty = 4;
  if (this->sellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->sellqty(), output);
  }

  // optional int64 TotalNumbers = 5;
  if (this->totalnumbers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(5, this->totalnumbers(), output);
  }

  // optional int64 BuyNumbers = 6;
  if (this->buynumbers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->buynumbers(), output);
  }

  // optional int64 SellNumbers = 7;
  if (this->sellnumbers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->sellnumbers(), output);
  }

  // optional int64 VolumePerNumber = 8;
  if (this->volumepernumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->volumepernumber(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
}

::google::protobuf::uint8* ADVolumeByPriceDetail::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  // optional int64 TradePrice = 1;
  if (this->tradeprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->tradeprice(), target);
  }

  // optional int64 TotalQty = 2;
  if (this->totalqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->totalqty(), target);
  }

  // optional int64 BuyQty = 3;
  if (this->buyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->buyqty(), target);
  }

  // optional int64 SellQty = 4;
  if (this->sellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->sellqty(), target);
  }

  // optional int64 TotalNumbers = 5;
  if (this->totalnumbers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(5, this->totalnumbers(), target);
  }

  // optional int64 BuyNumbers = 6;
  if (this->buynumbers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->buynumbers(), target);
  }

  // optional int64 SellNumbers = 7;
  if (this->sellnumbers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->sellnumbers(), target);
  }

  // optional int64 VolumePerNumber = 8;
  if (this->volumepernumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->volumepernumber(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  return target;
}

size_t ADVolumeByPriceDetail::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  size_t total_size = 0;

  // optional int64 TradePrice = 1;
  if (this->tradeprice() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->tradeprice());
  }

  // optional int64 TotalQty = 2;
  if (this->totalqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalqty());
  }

  // optional int64 BuyQty = 3;
  if (this->buyqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buyqty());
  }

  // optional int64 SellQty = 4;
  if (this->sellqty() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellqty());
  }

  // optional int64 TotalNumbers = 5;
  if (this->totalnumbers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalnumbers());
  }

  // optional int64 BuyNumbers = 6;
  if (this->buynumbers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buynumbers());
  }

  // optional int64 SellNumbers = 7;
  if (this->sellnumbers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->sellnumbers());
  }

  // optional int64 VolumePerNumber = 8;
  if (this->volumepernumber() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->volumepernumber());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ADVolumeByPriceDetail::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const ADVolumeByPriceDetail* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ADVolumeByPriceDetail>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
    UnsafeMergeFrom(*source);
  }
}

void ADVolumeByPriceDetail::MergeFrom(const ADVolumeByPriceDetail& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void ADVolumeByPriceDetail::UnsafeMergeFrom(const ADVolumeByPriceDetail& from) {
  GOOGLE_DCHECK(&from != this);
  if (from.tradeprice() != 0) {
    set_tradeprice(from.tradeprice());
  }
  if (from.totalqty() != 0) {
    set_totalqty(from.totalqty());
  }
  if (from.buyqty() != 0) {
    set_buyqty(from.buyqty());
  }
  if (from.sellqty() != 0) {
    set_sellqty(from.sellqty());
  }
  if (from.totalnumbers() != 0) {
    set_totalnumbers(from.totalnumbers());
  }
  if (from.buynumbers() != 0) {
    set_buynumbers(from.buynumbers());
  }
  if (from.sellnumbers() != 0) {
    set_sellnumbers(from.sellnumbers());
  }
  if (from.volumepernumber() != 0) {
    set_volumepernumber(from.volumepernumber());
  }
}

void ADVolumeByPriceDetail::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ADVolumeByPriceDetail::CopyFrom(const ADVolumeByPriceDetail& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.ADVolumeByPriceDetail)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool ADVolumeByPriceDetail::IsInitialized() const {

  return true;
}

void ADVolumeByPriceDetail::Swap(ADVolumeByPriceDetail* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ADVolumeByPriceDetail::InternalSwap(ADVolumeByPriceDetail* other) {
  std::swap(tradeprice_, other->tradeprice_);
  std::swap(totalqty_, other->totalqty_);
  std::swap(buyqty_, other->buyqty_);
  std::swap(sellqty_, other->sellqty_);
  std::swap(totalnumbers_, other->totalnumbers_);
  std::swap(buynumbers_, other->buynumbers_);
  std::swap(sellnumbers_, other->sellnumbers_);
  std::swap(volumepernumber_, other->volumepernumber_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ADVolumeByPriceDetail::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = ADVolumeByPriceDetail_descriptor_;
  metadata.reflection = ADVolumeByPriceDetail_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// ADVolumeByPriceDetail

// optional int64 TradePrice = 1;
void ADVolumeByPriceDetail::clear_tradeprice() {
  tradeprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::tradeprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TradePrice)
  return tradeprice_;
}
void ADVolumeByPriceDetail::set_tradeprice(::google::protobuf::int64 value) {
  
  tradeprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TradePrice)
}

// optional int64 TotalQty = 2;
void ADVolumeByPriceDetail::clear_totalqty() {
  totalqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::totalqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalQty)
  return totalqty_;
}
void ADVolumeByPriceDetail::set_totalqty(::google::protobuf::int64 value) {
  
  totalqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalQty)
}

// optional int64 BuyQty = 3;
void ADVolumeByPriceDetail::clear_buyqty() {
  buyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::buyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyQty)
  return buyqty_;
}
void ADVolumeByPriceDetail::set_buyqty(::google::protobuf::int64 value) {
  
  buyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyQty)
}

// optional int64 SellQty = 4;
void ADVolumeByPriceDetail::clear_sellqty() {
  sellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::sellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellQty)
  return sellqty_;
}
void ADVolumeByPriceDetail::set_sellqty(::google::protobuf::int64 value) {
  
  sellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellQty)
}

// optional int64 TotalNumbers = 5;
void ADVolumeByPriceDetail::clear_totalnumbers() {
  totalnumbers_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::totalnumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalNumbers)
  return totalnumbers_;
}
void ADVolumeByPriceDetail::set_totalnumbers(::google::protobuf::int64 value) {
  
  totalnumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.TotalNumbers)
}

// optional int64 BuyNumbers = 6;
void ADVolumeByPriceDetail::clear_buynumbers() {
  buynumbers_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::buynumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyNumbers)
  return buynumbers_;
}
void ADVolumeByPriceDetail::set_buynumbers(::google::protobuf::int64 value) {
  
  buynumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.BuyNumbers)
}

// optional int64 SellNumbers = 7;
void ADVolumeByPriceDetail::clear_sellnumbers() {
  sellnumbers_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::sellnumbers() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellNumbers)
  return sellnumbers_;
}
void ADVolumeByPriceDetail::set_sellnumbers(::google::protobuf::int64 value) {
  
  sellnumbers_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.SellNumbers)
}

// optional int64 VolumePerNumber = 8;
void ADVolumeByPriceDetail::clear_volumepernumber() {
  volumepernumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 ADVolumeByPriceDetail::volumepernumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.VolumePerNumber)
  return volumepernumber_;
}
void ADVolumeByPriceDetail::set_volumepernumber(::google::protobuf::int64 value) {
  
  volumepernumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.ADVolumeByPriceDetail.VolumePerNumber)
}

inline const ADVolumeByPriceDetail* ADVolumeByPriceDetail::internal_default_instance() {
  return &ADVolumeByPriceDetail_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
