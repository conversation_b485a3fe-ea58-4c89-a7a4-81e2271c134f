// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MDFund.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include "MDFund.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace com {
namespace htsc {
namespace mdc {
namespace insight {
namespace model {

namespace {

const ::google::protobuf::Descriptor* MDFund_descriptor_ = NULL;
const ::google::protobuf::internal::GeneratedMessageReflection*
  MDFund_reflection_ = NULL;

}  // namespace


void protobuf_AssignDesc_MDFund_2eproto() GOOGLE_ATTRIBUTE_COLD;
void protobuf_AssignDesc_MDFund_2eproto() {
  protobuf_AddDesc_MDFund_2eproto();
  const ::google::protobuf::FileDescriptor* file =
    ::google::protobuf::DescriptorPool::generated_pool()->FindFileByName(
      "MDFund.proto");
  GOOGLE_CHECK(file != NULL);
  MDFund_descriptor_ = file->message_type(0);
  static const int MDFund_offsets_[90] = {
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, htscsecurityid_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, mddate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, mdtime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, datatimestamp_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, tradingphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, securityidsource_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, securitytype_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, maxpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, minpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, preclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, numtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, lastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, openpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, closepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, highpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, lowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, diffpx1_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, diffpx2_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalbuyqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalsellqty_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, weightedavgbuypx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, weightedavgsellpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawbuyamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawbuymoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawsellamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, withdrawsellmoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalbuynumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, totalsellnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, buytrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, selltrademaxduration_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, numbuyorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, numsellorders_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, iopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, preiopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, purchasenumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, purchaseamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, purchasemoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, redemptionnumber_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, redemptionamount_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, redemptionmoney_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, exchangedate_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, exchangetime_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, channelno_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, buypricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, buyorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, sellpricequeue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, sellorderqtyqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, buyorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, sellorderqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, buynumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, sellnumordersqueue_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, norminalpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, shortsellsharestraded_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, shortsellturnover_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, premarketlastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, premarkettotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, premarkettotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, premarkethighpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, premarketlowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, afterhourslastpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, afterhourstotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, afterhourstotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, afterhourshighpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, afterhourslowpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, marketphasecode_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, usconsolidatevolume_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, uscompositeclosepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, tradinghaltreason_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, otctotalvolumetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, otctotalvaluetrade_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, otcnumtrades_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, datamultiplepowerof10_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, weightedavgpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, precloseweightedavgpx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, bestbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, qtyatbestbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, bestsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, qtyatbestsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, highaccuracyiopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, highaccuracypreiopv_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, referencepx_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, maxbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, minbuyprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, maxsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, minsellprice_),
    GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, delaytype_),
  };
  MDFund_reflection_ =
    ::google::protobuf::internal::GeneratedMessageReflection::NewGeneratedMessageReflection(
      MDFund_descriptor_,
      MDFund::internal_default_instance(),
      MDFund_offsets_,
      -1,
      -1,
      -1,
      sizeof(MDFund),
      GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(MDFund, _internal_metadata_));
}

namespace {

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AssignDescriptors_once_);
void protobuf_AssignDescriptorsOnce() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AssignDescriptors_once_,
                 &protobuf_AssignDesc_MDFund_2eproto);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedMessage(
      MDFund_descriptor_, MDFund::internal_default_instance());
}

}  // namespace

void protobuf_ShutdownFile_MDFund_2eproto() {
  MDFund_default_instance_.Shutdown();
  delete MDFund_reflection_;
}

void protobuf_InitDefaults_MDFund_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_InitDefaults_ESecurityType_2eproto();
  ::google::protobuf::internal::GetEmptyString();
  MDFund_default_instance_.DefaultConstruct();
  MDFund_default_instance_.get_mutable()->InitAsDefaultInstance();
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_InitDefaults_MDFund_2eproto_once_);
void protobuf_InitDefaults_MDFund_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_InitDefaults_MDFund_2eproto_once_,
                 &protobuf_InitDefaults_MDFund_2eproto_impl);
}
void protobuf_AddDesc_MDFund_2eproto_impl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  protobuf_InitDefaults_MDFund_2eproto();
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
    "\n\014MDFund.proto\022\032com.htsc.mdc.insight.mod"
    "el\032\027ESecurityIDSource.proto\032\023ESecurityTy"
    "pe.proto\"\350\021\n\006MDFund\022\026\n\016HTSCSecurityID\030\001 "
    "\001(\t\022\016\n\006MDDate\030\002 \001(\005\022\016\n\006MDTime\030\003 \001(\005\022\025\n\rD"
    "ataTimestamp\030\004 \001(\003\022\030\n\020TradingPhaseCode\030\005"
    " \001(\t\022\?\n\020securityIDSource\030\006 \001(\0162%.com.hts"
    "c.mdc.model.ESecurityIDSource\0227\n\014securit"
    "yType\030\007 \001(\0162!.com.htsc.mdc.model.ESecuri"
    "tyType\022\r\n\005MaxPx\030\010 \001(\003\022\r\n\005MinPx\030\t \001(\003\022\022\n\n"
    "PreClosePx\030\n \001(\003\022\021\n\tNumTrades\030\013 \001(\003\022\030\n\020T"
    "otalVolumeTrade\030\014 \001(\003\022\027\n\017TotalValueTrade"
    "\030\r \001(\003\022\016\n\006LastPx\030\016 \001(\003\022\016\n\006OpenPx\030\017 \001(\003\022\017"
    "\n\007ClosePx\030\020 \001(\003\022\016\n\006HighPx\030\021 \001(\003\022\r\n\005LowPx"
    "\030\022 \001(\003\022\017\n\007DiffPx1\030\023 \001(\003\022\017\n\007DiffPx2\030\024 \001(\003"
    "\022\023\n\013TotalBuyQty\030\025 \001(\003\022\024\n\014TotalSellQty\030\026 "
    "\001(\003\022\030\n\020WeightedAvgBuyPx\030\027 \001(\003\022\031\n\021Weighte"
    "dAvgSellPx\030\030 \001(\003\022\031\n\021WithdrawBuyNumber\030\031 "
    "\001(\003\022\031\n\021WithdrawBuyAmount\030\032 \001(\003\022\030\n\020Withdr"
    "awBuyMoney\030\033 \001(\003\022\032\n\022WithdrawSellNumber\030\034"
    " \001(\003\022\032\n\022WithdrawSellAmount\030\035 \001(\003\022\031\n\021With"
    "drawSellMoney\030\036 \001(\003\022\026\n\016TotalBuyNumber\030\037 "
    "\001(\003\022\027\n\017TotalSellNumber\030  \001(\003\022\033\n\023BuyTrade"
    "MaxDuration\030! \001(\003\022\034\n\024SellTradeMaxDuratio"
    "n\030\" \001(\003\022\024\n\014NumBuyOrders\030# \001(\005\022\025\n\rNumSell"
    "Orders\030$ \001(\005\022\014\n\004IOPV\030% \001(\003\022\017\n\007PreIOPV\030& "
    "\001(\003\022\026\n\016PurchaseNumber\030\' \001(\003\022\026\n\016PurchaseA"
    "mount\030( \001(\003\022\025\n\rPurchaseMoney\030) \001(\003\022\030\n\020Re"
    "demptionNumber\030* \001(\003\022\030\n\020RedemptionAmount"
    "\030+ \001(\003\022\027\n\017RedemptionMoney\030, \001(\003\022\024\n\014Excha"
    "ngeDate\030- \001(\005\022\024\n\014ExchangeTime\030. \001(\005\022\021\n\tC"
    "hannelNo\0302 \001(\005\022\031\n\rBuyPriceQueue\0303 \003(\003B\002\020"
    "\001\022\034\n\020BuyOrderQtyQueue\0304 \003(\003B\002\020\001\022\032\n\016SellP"
    "riceQueue\0305 \003(\003B\002\020\001\022\035\n\021SellOrderQtyQueue"
    "\0306 \003(\003B\002\020\001\022\031\n\rBuyOrderQueue\0307 \003(\003B\002\020\001\022\032\n"
    "\016SellOrderQueue\0308 \003(\003B\002\020\001\022\035\n\021BuyNumOrder"
    "sQueue\0309 \003(\003B\002\020\001\022\036\n\022SellNumOrdersQueue\030:"
    " \003(\003B\002\020\001\022\022\n\nNorminalPx\030; \001(\003\022\035\n\025ShortSel"
    "lSharesTraded\030< \001(\003\022\031\n\021ShortSellTurnover"
    "\030= \001(\003\022\027\n\017PreMarketLastPx\030> \001(\003\022!\n\031PreMa"
    "rketTotalVolumeTrade\030\? \001(\003\022 \n\030PreMarketT"
    "otalValueTrade\030@ \001(\003\022\027\n\017PreMarketHighPx\030"
    "A \001(\003\022\026\n\016PreMarketLowPx\030B \001(\003\022\030\n\020AfterHo"
    "ursLastPx\030C \001(\003\022\"\n\032AfterHoursTotalVolume"
    "Trade\030D \001(\003\022!\n\031AfterHoursTotalValueTrade"
    "\030E \001(\003\022\030\n\020AfterHoursHighPx\030F \001(\003\022\027\n\017Afte"
    "rHoursLowPx\030G \001(\003\022\027\n\017MarketPhaseCode\030H \001"
    "(\t\022\033\n\023USConsolidateVolume\030I \001(\003\022\032\n\022USCom"
    "positeClosePx\030J \001(\003\022\031\n\021TradingHaltReason"
    "\030K \001(\t\022\033\n\023OtcTotalVolumeTrade\030L \001(\003\022\032\n\022O"
    "tcTotalValueTrade\030M \001(\003\022\024\n\014OtcNumTrades\030"
    "N \001(\003\022\035\n\025DataMultiplePowerOf10\030O \001(\005\022\025\n\r"
    "WeightedAvgPx\030P \001(\003\022\035\n\025PreCloseWeightedA"
    "vgPx\030Q \001(\003\022\024\n\014BestBuyPrice\030R \001(\003\022\031\n\021QtyA"
    "tBestBuyPrice\030S \001(\003\022\025\n\rBestSellPrice\030T \001"
    "(\003\022\032\n\022QtyAtBestSellPrice\030U \001(\003\022\030\n\020HighAc"
    "curacyIOPV\030V \001(\003\022\033\n\023HighAccuracyPreIOPV\030"
    "W \001(\003\022\023\n\013ReferencePx\030X \001(\003\022\023\n\013MaxBuyPric"
    "e\030Y \001(\003\022\023\n\013MinBuyPrice\030Z \001(\003\022\024\n\014MaxSellP"
    "rice\030[ \001(\003\022\024\n\014MinSellPrice\030\\ \001(\003\022\021\n\tDela"
    "yType\030e \001(\005B/\n\032com.htsc.mdc.insight.mode"
    "lB\014MDFundProtosH\001\240\001\001b\006proto3", 2428);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "MDFund.proto", &protobuf_RegisterTypes);
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityIDSource_2eproto();
  ::com::htsc::mdc::model::protobuf_AddDesc_ESecurityType_2eproto();
  ::google::protobuf::internal::OnShutdown(&protobuf_ShutdownFile_MDFund_2eproto);
}

GOOGLE_PROTOBUF_DECLARE_ONCE(protobuf_AddDesc_MDFund_2eproto_once_);
void protobuf_AddDesc_MDFund_2eproto() {
  ::google::protobuf::GoogleOnceInit(&protobuf_AddDesc_MDFund_2eproto_once_,
                 &protobuf_AddDesc_MDFund_2eproto_impl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer_MDFund_2eproto {
  StaticDescriptorInitializer_MDFund_2eproto() {
    protobuf_AddDesc_MDFund_2eproto();
  }
} static_descriptor_initializer_MDFund_2eproto_;

namespace {

static void MergeFromFail(int line) GOOGLE_ATTRIBUTE_COLD GOOGLE_ATTRIBUTE_NORETURN;
static void MergeFromFail(int line) {
  ::google::protobuf::internal::MergeFromFail(__FILE__, line);
}

}  // namespace


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int MDFund::kHTSCSecurityIDFieldNumber;
const int MDFund::kMDDateFieldNumber;
const int MDFund::kMDTimeFieldNumber;
const int MDFund::kDataTimestampFieldNumber;
const int MDFund::kTradingPhaseCodeFieldNumber;
const int MDFund::kSecurityIDSourceFieldNumber;
const int MDFund::kSecurityTypeFieldNumber;
const int MDFund::kMaxPxFieldNumber;
const int MDFund::kMinPxFieldNumber;
const int MDFund::kPreClosePxFieldNumber;
const int MDFund::kNumTradesFieldNumber;
const int MDFund::kTotalVolumeTradeFieldNumber;
const int MDFund::kTotalValueTradeFieldNumber;
const int MDFund::kLastPxFieldNumber;
const int MDFund::kOpenPxFieldNumber;
const int MDFund::kClosePxFieldNumber;
const int MDFund::kHighPxFieldNumber;
const int MDFund::kLowPxFieldNumber;
const int MDFund::kDiffPx1FieldNumber;
const int MDFund::kDiffPx2FieldNumber;
const int MDFund::kTotalBuyQtyFieldNumber;
const int MDFund::kTotalSellQtyFieldNumber;
const int MDFund::kWeightedAvgBuyPxFieldNumber;
const int MDFund::kWeightedAvgSellPxFieldNumber;
const int MDFund::kWithdrawBuyNumberFieldNumber;
const int MDFund::kWithdrawBuyAmountFieldNumber;
const int MDFund::kWithdrawBuyMoneyFieldNumber;
const int MDFund::kWithdrawSellNumberFieldNumber;
const int MDFund::kWithdrawSellAmountFieldNumber;
const int MDFund::kWithdrawSellMoneyFieldNumber;
const int MDFund::kTotalBuyNumberFieldNumber;
const int MDFund::kTotalSellNumberFieldNumber;
const int MDFund::kBuyTradeMaxDurationFieldNumber;
const int MDFund::kSellTradeMaxDurationFieldNumber;
const int MDFund::kNumBuyOrdersFieldNumber;
const int MDFund::kNumSellOrdersFieldNumber;
const int MDFund::kIOPVFieldNumber;
const int MDFund::kPreIOPVFieldNumber;
const int MDFund::kPurchaseNumberFieldNumber;
const int MDFund::kPurchaseAmountFieldNumber;
const int MDFund::kPurchaseMoneyFieldNumber;
const int MDFund::kRedemptionNumberFieldNumber;
const int MDFund::kRedemptionAmountFieldNumber;
const int MDFund::kRedemptionMoneyFieldNumber;
const int MDFund::kExchangeDateFieldNumber;
const int MDFund::kExchangeTimeFieldNumber;
const int MDFund::kChannelNoFieldNumber;
const int MDFund::kBuyPriceQueueFieldNumber;
const int MDFund::kBuyOrderQtyQueueFieldNumber;
const int MDFund::kSellPriceQueueFieldNumber;
const int MDFund::kSellOrderQtyQueueFieldNumber;
const int MDFund::kBuyOrderQueueFieldNumber;
const int MDFund::kSellOrderQueueFieldNumber;
const int MDFund::kBuyNumOrdersQueueFieldNumber;
const int MDFund::kSellNumOrdersQueueFieldNumber;
const int MDFund::kNorminalPxFieldNumber;
const int MDFund::kShortSellSharesTradedFieldNumber;
const int MDFund::kShortSellTurnoverFieldNumber;
const int MDFund::kPreMarketLastPxFieldNumber;
const int MDFund::kPreMarketTotalVolumeTradeFieldNumber;
const int MDFund::kPreMarketTotalValueTradeFieldNumber;
const int MDFund::kPreMarketHighPxFieldNumber;
const int MDFund::kPreMarketLowPxFieldNumber;
const int MDFund::kAfterHoursLastPxFieldNumber;
const int MDFund::kAfterHoursTotalVolumeTradeFieldNumber;
const int MDFund::kAfterHoursTotalValueTradeFieldNumber;
const int MDFund::kAfterHoursHighPxFieldNumber;
const int MDFund::kAfterHoursLowPxFieldNumber;
const int MDFund::kMarketPhaseCodeFieldNumber;
const int MDFund::kUSConsolidateVolumeFieldNumber;
const int MDFund::kUSCompositeClosePxFieldNumber;
const int MDFund::kTradingHaltReasonFieldNumber;
const int MDFund::kOtcTotalVolumeTradeFieldNumber;
const int MDFund::kOtcTotalValueTradeFieldNumber;
const int MDFund::kOtcNumTradesFieldNumber;
const int MDFund::kDataMultiplePowerOf10FieldNumber;
const int MDFund::kWeightedAvgPxFieldNumber;
const int MDFund::kPreCloseWeightedAvgPxFieldNumber;
const int MDFund::kBestBuyPriceFieldNumber;
const int MDFund::kQtyAtBestBuyPriceFieldNumber;
const int MDFund::kBestSellPriceFieldNumber;
const int MDFund::kQtyAtBestSellPriceFieldNumber;
const int MDFund::kHighAccuracyIOPVFieldNumber;
const int MDFund::kHighAccuracyPreIOPVFieldNumber;
const int MDFund::kReferencePxFieldNumber;
const int MDFund::kMaxBuyPriceFieldNumber;
const int MDFund::kMinBuyPriceFieldNumber;
const int MDFund::kMaxSellPriceFieldNumber;
const int MDFund::kMinSellPriceFieldNumber;
const int MDFund::kDelayTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

MDFund::MDFund()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (this != internal_default_instance()) protobuf_InitDefaults_MDFund_2eproto();
  SharedCtor();
  // @@protoc_insertion_point(constructor:com.htsc.mdc.insight.model.MDFund)
}

void MDFund::InitAsDefaultInstance() {
}

MDFund::MDFund(const MDFund& from)
  : ::google::protobuf::Message(),
    _internal_metadata_(NULL) {
  SharedCtor();
  UnsafeMergeFrom(from);
  // @@protoc_insertion_point(copy_constructor:com.htsc.mdc.insight.model.MDFund)
}

void MDFund::SharedCtor() {
  htscsecurityid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketphasecode_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradinghaltreason_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&mddate_, 0, reinterpret_cast<char*>(&delaytype_) -
    reinterpret_cast<char*>(&mddate_) + sizeof(delaytype_));
  _cached_size_ = 0;
}

MDFund::~MDFund() {
  // @@protoc_insertion_point(destructor:com.htsc.mdc.insight.model.MDFund)
  SharedDtor();
}

void MDFund::SharedDtor() {
  htscsecurityid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  marketphasecode_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradinghaltreason_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void MDFund::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* MDFund::descriptor() {
  protobuf_AssignDescriptorsOnce();
  return MDFund_descriptor_;
}

const MDFund& MDFund::default_instance() {
  protobuf_InitDefaults_MDFund_2eproto();
  return *internal_default_instance();
}

::google::protobuf::internal::ExplicitlyConstructed<MDFund> MDFund_default_instance_;

MDFund* MDFund::New(::google::protobuf::Arena* arena) const {
  MDFund* n = new MDFund;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void MDFund::Clear() {
// @@protoc_insertion_point(message_clear_start:com.htsc.mdc.insight.model.MDFund)
#if defined(__clang__)
#define ZR_HELPER_(f) \
  _Pragma("clang diagnostic push") \
  _Pragma("clang diagnostic ignored \"-Winvalid-offsetof\"") \
  __builtin_offsetof(MDFund, f) \
  _Pragma("clang diagnostic pop")
#else
#define ZR_HELPER_(f) reinterpret_cast<char*>(\
  &reinterpret_cast<MDFund*>(16)->f)
#endif

#define ZR_(first, last) do {\
  ::memset(&(first), 0,\
           ZR_HELPER_(last) - ZR_HELPER_(first) + sizeof(last));\
} while (0)

  ZR_(mddate_, maxpx_);
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(minpx_, closepx_);
  ZR_(highpx_, weightedavgsellpx_);
  ZR_(withdrawbuynumber_, totalsellnumber_);
  ZR_(buytrademaxduration_, purchaseamount_);
  ZR_(purchasemoney_, exchangetime_);
  channelno_ = 0;
  norminalpx_ = GOOGLE_LONGLONG(0);
  ZR_(shortsellsharestraded_, premarkettotalvaluetrade_);
  ZR_(premarkethighpx_, afterhourslastpx_);
  ZR_(afterhourstotalvolumetrade_, uscompositeclosepx_);
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ZR_(otctotalvolumetrade_, qtyatbestbuyprice_);
  datamultiplepowerof10_ = 0;
  ZR_(bestsellprice_, maxsellprice_);
  ZR_(minsellprice_, delaytype_);

#undef ZR_HELPER_
#undef ZR_

  buypricequeue_.Clear();
  buyorderqtyqueue_.Clear();
  sellpricequeue_.Clear();
  sellorderqtyqueue_.Clear();
  buyorderqueue_.Clear();
  sellorderqueue_.Clear();
  buynumordersqueue_.Clear();
  sellnumordersqueue_.Clear();
}

bool MDFund::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:com.htsc.mdc.insight.model.MDFund)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoff(16383);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional string HTSCSecurityID = 1;
      case 1: {
        if (tag == 10) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_htscsecurityid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->htscsecurityid().data(), this->htscsecurityid().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFund.HTSCSecurityID"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(16)) goto parse_MDDate;
        break;
      }

      // optional int32 MDDate = 2;
      case 2: {
        if (tag == 16) {
         parse_MDDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mddate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(24)) goto parse_MDTime;
        break;
      }

      // optional int32 MDTime = 3;
      case 3: {
        if (tag == 24) {
         parse_MDTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &mdtime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(32)) goto parse_DataTimestamp;
        break;
      }

      // optional int64 DataTimestamp = 4;
      case 4: {
        if (tag == 32) {
         parse_DataTimestamp:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &datatimestamp_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(42)) goto parse_TradingPhaseCode;
        break;
      }

      // optional string TradingPhaseCode = 5;
      case 5: {
        if (tag == 42) {
         parse_TradingPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradingphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradingphasecode().data(), this->tradingphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFund.TradingPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(48)) goto parse_securityIDSource;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
      case 6: {
        if (tag == 48) {
         parse_securityIDSource:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securityidsource(static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(56)) goto parse_securityType;
        break;
      }

      // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
      case 7: {
        if (tag == 56) {
         parse_securityType:
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_securitytype(static_cast< ::com::htsc::mdc::model::ESecurityType >(value));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(64)) goto parse_MaxPx;
        break;
      }

      // optional int64 MaxPx = 8;
      case 8: {
        if (tag == 64) {
         parse_MaxPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(72)) goto parse_MinPx;
        break;
      }

      // optional int64 MinPx = 9;
      case 9: {
        if (tag == 72) {
         parse_MinPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(80)) goto parse_PreClosePx;
        break;
      }

      // optional int64 PreClosePx = 10;
      case 10: {
        if (tag == 80) {
         parse_PreClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(88)) goto parse_NumTrades;
        break;
      }

      // optional int64 NumTrades = 11;
      case 11: {
        if (tag == 88) {
         parse_NumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &numtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(96)) goto parse_TotalVolumeTrade;
        break;
      }

      // optional int64 TotalVolumeTrade = 12;
      case 12: {
        if (tag == 96) {
         parse_TotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(104)) goto parse_TotalValueTrade;
        break;
      }

      // optional int64 TotalValueTrade = 13;
      case 13: {
        if (tag == 104) {
         parse_TotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(112)) goto parse_LastPx;
        break;
      }

      // optional int64 LastPx = 14;
      case 14: {
        if (tag == 112) {
         parse_LastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(120)) goto parse_OpenPx;
        break;
      }

      // optional int64 OpenPx = 15;
      case 15: {
        if (tag == 120) {
         parse_OpenPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &openpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(128)) goto parse_ClosePx;
        break;
      }

      // optional int64 ClosePx = 16;
      case 16: {
        if (tag == 128) {
         parse_ClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &closepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(136)) goto parse_HighPx;
        break;
      }

      // optional int64 HighPx = 17;
      case 17: {
        if (tag == 136) {
         parse_HighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(144)) goto parse_LowPx;
        break;
      }

      // optional int64 LowPx = 18;
      case 18: {
        if (tag == 144) {
         parse_LowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &lowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(152)) goto parse_DiffPx1;
        break;
      }

      // optional int64 DiffPx1 = 19;
      case 19: {
        if (tag == 152) {
         parse_DiffPx1:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx1_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(160)) goto parse_DiffPx2;
        break;
      }

      // optional int64 DiffPx2 = 20;
      case 20: {
        if (tag == 160) {
         parse_DiffPx2:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &diffpx2_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(168)) goto parse_TotalBuyQty;
        break;
      }

      // optional int64 TotalBuyQty = 21;
      case 21: {
        if (tag == 168) {
         parse_TotalBuyQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuyqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(176)) goto parse_TotalSellQty;
        break;
      }

      // optional int64 TotalSellQty = 22;
      case 22: {
        if (tag == 176) {
         parse_TotalSellQty:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellqty_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(184)) goto parse_WeightedAvgBuyPx;
        break;
      }

      // optional int64 WeightedAvgBuyPx = 23;
      case 23: {
        if (tag == 184) {
         parse_WeightedAvgBuyPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgbuypx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(192)) goto parse_WeightedAvgSellPx;
        break;
      }

      // optional int64 WeightedAvgSellPx = 24;
      case 24: {
        if (tag == 192) {
         parse_WeightedAvgSellPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgsellpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(200)) goto parse_WithdrawBuyNumber;
        break;
      }

      // optional int64 WithdrawBuyNumber = 25;
      case 25: {
        if (tag == 200) {
         parse_WithdrawBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(208)) goto parse_WithdrawBuyAmount;
        break;
      }

      // optional int64 WithdrawBuyAmount = 26;
      case 26: {
        if (tag == 208) {
         parse_WithdrawBuyAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuyamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(216)) goto parse_WithdrawBuyMoney;
        break;
      }

      // optional int64 WithdrawBuyMoney = 27;
      case 27: {
        if (tag == 216) {
         parse_WithdrawBuyMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawbuymoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(224)) goto parse_WithdrawSellNumber;
        break;
      }

      // optional int64 WithdrawSellNumber = 28;
      case 28: {
        if (tag == 224) {
         parse_WithdrawSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(232)) goto parse_WithdrawSellAmount;
        break;
      }

      // optional int64 WithdrawSellAmount = 29;
      case 29: {
        if (tag == 232) {
         parse_WithdrawSellAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(240)) goto parse_WithdrawSellMoney;
        break;
      }

      // optional int64 WithdrawSellMoney = 30;
      case 30: {
        if (tag == 240) {
         parse_WithdrawSellMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &withdrawsellmoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(248)) goto parse_TotalBuyNumber;
        break;
      }

      // optional int64 TotalBuyNumber = 31;
      case 31: {
        if (tag == 248) {
         parse_TotalBuyNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalbuynumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(256)) goto parse_TotalSellNumber;
        break;
      }

      // optional int64 TotalSellNumber = 32;
      case 32: {
        if (tag == 256) {
         parse_TotalSellNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &totalsellnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(264)) goto parse_BuyTradeMaxDuration;
        break;
      }

      // optional int64 BuyTradeMaxDuration = 33;
      case 33: {
        if (tag == 264) {
         parse_BuyTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &buytrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(272)) goto parse_SellTradeMaxDuration;
        break;
      }

      // optional int64 SellTradeMaxDuration = 34;
      case 34: {
        if (tag == 272) {
         parse_SellTradeMaxDuration:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &selltrademaxduration_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(280)) goto parse_NumBuyOrders;
        break;
      }

      // optional int32 NumBuyOrders = 35;
      case 35: {
        if (tag == 280) {
         parse_NumBuyOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numbuyorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(288)) goto parse_NumSellOrders;
        break;
      }

      // optional int32 NumSellOrders = 36;
      case 36: {
        if (tag == 288) {
         parse_NumSellOrders:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &numsellorders_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(296)) goto parse_IOPV;
        break;
      }

      // optional int64 IOPV = 37;
      case 37: {
        if (tag == 296) {
         parse_IOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &iopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(304)) goto parse_PreIOPV;
        break;
      }

      // optional int64 PreIOPV = 38;
      case 38: {
        if (tag == 304) {
         parse_PreIOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &preiopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(312)) goto parse_PurchaseNumber;
        break;
      }

      // optional int64 PurchaseNumber = 39;
      case 39: {
        if (tag == 312) {
         parse_PurchaseNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &purchasenumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(320)) goto parse_PurchaseAmount;
        break;
      }

      // optional int64 PurchaseAmount = 40;
      case 40: {
        if (tag == 320) {
         parse_PurchaseAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &purchaseamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(328)) goto parse_PurchaseMoney;
        break;
      }

      // optional int64 PurchaseMoney = 41;
      case 41: {
        if (tag == 328) {
         parse_PurchaseMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &purchasemoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(336)) goto parse_RedemptionNumber;
        break;
      }

      // optional int64 RedemptionNumber = 42;
      case 42: {
        if (tag == 336) {
         parse_RedemptionNumber:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &redemptionnumber_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(344)) goto parse_RedemptionAmount;
        break;
      }

      // optional int64 RedemptionAmount = 43;
      case 43: {
        if (tag == 344) {
         parse_RedemptionAmount:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &redemptionamount_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(352)) goto parse_RedemptionMoney;
        break;
      }

      // optional int64 RedemptionMoney = 44;
      case 44: {
        if (tag == 352) {
         parse_RedemptionMoney:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &redemptionmoney_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(360)) goto parse_ExchangeDate;
        break;
      }

      // optional int32 ExchangeDate = 45;
      case 45: {
        if (tag == 360) {
         parse_ExchangeDate:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangedate_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(368)) goto parse_ExchangeTime;
        break;
      }

      // optional int32 ExchangeTime = 46;
      case 46: {
        if (tag == 368) {
         parse_ExchangeTime:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &exchangetime_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(400)) goto parse_ChannelNo;
        break;
      }

      // optional int32 ChannelNo = 50;
      case 50: {
        if (tag == 400) {
         parse_ChannelNo:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &channelno_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(410)) goto parse_BuyPriceQueue;
        break;
      }

      // repeated int64 BuyPriceQueue = 51 [packed = true];
      case 51: {
        if (tag == 410) {
         parse_BuyPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buypricequeue())));
        } else if (tag == 408) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 410, input, this->mutable_buypricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(418)) goto parse_BuyOrderQtyQueue;
        break;
      }

      // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
      case 52: {
        if (tag == 418) {
         parse_BuyOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqtyqueue())));
        } else if (tag == 416) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 418, input, this->mutable_buyorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(426)) goto parse_SellPriceQueue;
        break;
      }

      // repeated int64 SellPriceQueue = 53 [packed = true];
      case 53: {
        if (tag == 426) {
         parse_SellPriceQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellpricequeue())));
        } else if (tag == 424) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 426, input, this->mutable_sellpricequeue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(434)) goto parse_SellOrderQtyQueue;
        break;
      }

      // repeated int64 SellOrderQtyQueue = 54 [packed = true];
      case 54: {
        if (tag == 434) {
         parse_SellOrderQtyQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqtyqueue())));
        } else if (tag == 432) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 434, input, this->mutable_sellorderqtyqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(442)) goto parse_BuyOrderQueue;
        break;
      }

      // repeated int64 BuyOrderQueue = 55 [packed = true];
      case 55: {
        if (tag == 442) {
         parse_BuyOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buyorderqueue())));
        } else if (tag == 440) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 442, input, this->mutable_buyorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(450)) goto parse_SellOrderQueue;
        break;
      }

      // repeated int64 SellOrderQueue = 56 [packed = true];
      case 56: {
        if (tag == 450) {
         parse_SellOrderQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellorderqueue())));
        } else if (tag == 448) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 450, input, this->mutable_sellorderqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(458)) goto parse_BuyNumOrdersQueue;
        break;
      }

      // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
      case 57: {
        if (tag == 458) {
         parse_BuyNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_buynumordersqueue())));
        } else if (tag == 456) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 458, input, this->mutable_buynumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(466)) goto parse_SellNumOrdersQueue;
        break;
      }

      // repeated int64 SellNumOrdersQueue = 58 [packed = true];
      case 58: {
        if (tag == 466) {
         parse_SellNumOrdersQueue:
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, this->mutable_sellnumordersqueue())));
        } else if (tag == 464) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 2, 466, input, this->mutable_sellnumordersqueue())));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(472)) goto parse_NorminalPx;
        break;
      }

      // optional int64 NorminalPx = 59;
      case 59: {
        if (tag == 472) {
         parse_NorminalPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &norminalpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(480)) goto parse_ShortSellSharesTraded;
        break;
      }

      // optional int64 ShortSellSharesTraded = 60;
      case 60: {
        if (tag == 480) {
         parse_ShortSellSharesTraded:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &shortsellsharestraded_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(488)) goto parse_ShortSellTurnover;
        break;
      }

      // optional int64 ShortSellTurnover = 61;
      case 61: {
        if (tag == 488) {
         parse_ShortSellTurnover:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &shortsellturnover_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(496)) goto parse_PreMarketLastPx;
        break;
      }

      // optional int64 PreMarketLastPx = 62;
      case 62: {
        if (tag == 496) {
         parse_PreMarketLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarketlastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(504)) goto parse_PreMarketTotalVolumeTrade;
        break;
      }

      // optional int64 PreMarketTotalVolumeTrade = 63;
      case 63: {
        if (tag == 504) {
         parse_PreMarketTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkettotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(512)) goto parse_PreMarketTotalValueTrade;
        break;
      }

      // optional int64 PreMarketTotalValueTrade = 64;
      case 64: {
        if (tag == 512) {
         parse_PreMarketTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkettotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(520)) goto parse_PreMarketHighPx;
        break;
      }

      // optional int64 PreMarketHighPx = 65;
      case 65: {
        if (tag == 520) {
         parse_PreMarketHighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarkethighpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(528)) goto parse_PreMarketLowPx;
        break;
      }

      // optional int64 PreMarketLowPx = 66;
      case 66: {
        if (tag == 528) {
         parse_PreMarketLowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &premarketlowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(536)) goto parse_AfterHoursLastPx;
        break;
      }

      // optional int64 AfterHoursLastPx = 67;
      case 67: {
        if (tag == 536) {
         parse_AfterHoursLastPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourslastpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(544)) goto parse_AfterHoursTotalVolumeTrade;
        break;
      }

      // optional int64 AfterHoursTotalVolumeTrade = 68;
      case 68: {
        if (tag == 544) {
         parse_AfterHoursTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourstotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(552)) goto parse_AfterHoursTotalValueTrade;
        break;
      }

      // optional int64 AfterHoursTotalValueTrade = 69;
      case 69: {
        if (tag == 552) {
         parse_AfterHoursTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourstotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(560)) goto parse_AfterHoursHighPx;
        break;
      }

      // optional int64 AfterHoursHighPx = 70;
      case 70: {
        if (tag == 560) {
         parse_AfterHoursHighPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourshighpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(568)) goto parse_AfterHoursLowPx;
        break;
      }

      // optional int64 AfterHoursLowPx = 71;
      case 71: {
        if (tag == 568) {
         parse_AfterHoursLowPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &afterhourslowpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(578)) goto parse_MarketPhaseCode;
        break;
      }

      // optional string MarketPhaseCode = 72;
      case 72: {
        if (tag == 578) {
         parse_MarketPhaseCode:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_marketphasecode()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->marketphasecode().data(), this->marketphasecode().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFund.MarketPhaseCode"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(584)) goto parse_USConsolidateVolume;
        break;
      }

      // optional int64 USConsolidateVolume = 73;
      case 73: {
        if (tag == 584) {
         parse_USConsolidateVolume:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &usconsolidatevolume_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(592)) goto parse_USCompositeClosePx;
        break;
      }

      // optional int64 USCompositeClosePx = 74;
      case 74: {
        if (tag == 592) {
         parse_USCompositeClosePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &uscompositeclosepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(602)) goto parse_TradingHaltReason;
        break;
      }

      // optional string TradingHaltReason = 75;
      case 75: {
        if (tag == 602) {
         parse_TradingHaltReason:
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_tradinghaltreason()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->tradinghaltreason().data(), this->tradinghaltreason().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "com.htsc.mdc.insight.model.MDFund.TradingHaltReason"));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(608)) goto parse_OtcTotalVolumeTrade;
        break;
      }

      // optional int64 OtcTotalVolumeTrade = 76;
      case 76: {
        if (tag == 608) {
         parse_OtcTotalVolumeTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otctotalvolumetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(616)) goto parse_OtcTotalValueTrade;
        break;
      }

      // optional int64 OtcTotalValueTrade = 77;
      case 77: {
        if (tag == 616) {
         parse_OtcTotalValueTrade:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otctotalvaluetrade_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(624)) goto parse_OtcNumTrades;
        break;
      }

      // optional int64 OtcNumTrades = 78;
      case 78: {
        if (tag == 624) {
         parse_OtcNumTrades:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &otcnumtrades_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(632)) goto parse_DataMultiplePowerOf10;
        break;
      }

      // optional int32 DataMultiplePowerOf10 = 79;
      case 79: {
        if (tag == 632) {
         parse_DataMultiplePowerOf10:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &datamultiplepowerof10_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(640)) goto parse_WeightedAvgPx;
        break;
      }

      // optional int64 WeightedAvgPx = 80;
      case 80: {
        if (tag == 640) {
         parse_WeightedAvgPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &weightedavgpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(648)) goto parse_PreCloseWeightedAvgPx;
        break;
      }

      // optional int64 PreCloseWeightedAvgPx = 81;
      case 81: {
        if (tag == 648) {
         parse_PreCloseWeightedAvgPx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &precloseweightedavgpx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(656)) goto parse_BestBuyPrice;
        break;
      }

      // optional int64 BestBuyPrice = 82;
      case 82: {
        if (tag == 656) {
         parse_BestBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(664)) goto parse_QtyAtBestBuyPrice;
        break;
      }

      // optional int64 QtyAtBestBuyPrice = 83;
      case 83: {
        if (tag == 664) {
         parse_QtyAtBestBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &qtyatbestbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(672)) goto parse_BestSellPrice;
        break;
      }

      // optional int64 BestSellPrice = 84;
      case 84: {
        if (tag == 672) {
         parse_BestSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &bestsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(680)) goto parse_QtyAtBestSellPrice;
        break;
      }

      // optional int64 QtyAtBestSellPrice = 85;
      case 85: {
        if (tag == 680) {
         parse_QtyAtBestSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &qtyatbestsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(688)) goto parse_HighAccuracyIOPV;
        break;
      }

      // optional int64 HighAccuracyIOPV = 86;
      case 86: {
        if (tag == 688) {
         parse_HighAccuracyIOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highaccuracyiopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(696)) goto parse_HighAccuracyPreIOPV;
        break;
      }

      // optional int64 HighAccuracyPreIOPV = 87;
      case 87: {
        if (tag == 696) {
         parse_HighAccuracyPreIOPV:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &highaccuracypreiopv_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(704)) goto parse_ReferencePx;
        break;
      }

      // optional int64 ReferencePx = 88;
      case 88: {
        if (tag == 704) {
         parse_ReferencePx:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &referencepx_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(712)) goto parse_MaxBuyPrice;
        break;
      }

      // optional int64 MaxBuyPrice = 89;
      case 89: {
        if (tag == 712) {
         parse_MaxBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(720)) goto parse_MinBuyPrice;
        break;
      }

      // optional int64 MinBuyPrice = 90;
      case 90: {
        if (tag == 720) {
         parse_MinBuyPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minbuyprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(728)) goto parse_MaxSellPrice;
        break;
      }

      // optional int64 MaxSellPrice = 91;
      case 91: {
        if (tag == 728) {
         parse_MaxSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &maxsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(736)) goto parse_MinSellPrice;
        break;
      }

      // optional int64 MinSellPrice = 92;
      case 92: {
        if (tag == 736) {
         parse_MinSellPrice:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &minsellprice_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectTag(808)) goto parse_DelayType;
        break;
      }

      // optional int32 DelayType = 101;
      case 101: {
        if (tag == 808) {
         parse_DelayType:

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &delaytype_)));
        } else {
          goto handle_unusual;
        }
        if (input->ExpectAtEnd()) goto success;
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:com.htsc.mdc.insight.model.MDFund)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:com.htsc.mdc.insight.model.MDFund)
  return false;
#undef DO_
}

void MDFund::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:com.htsc.mdc.insight.model.MDFund)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.HTSCSecurityID");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->htscsecurityid(), output);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->mddate(), output);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->mdtime(), output);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(4, this->datatimestamp(), output);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.TradingPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      5, this->tradingphasecode(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      6, this->securityidsource(), output);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      7, this->securitytype(), output);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(8, this->maxpx(), output);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->minpx(), output);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(10, this->preclosepx(), output);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(11, this->numtrades(), output);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(12, this->totalvolumetrade(), output);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(13, this->totalvaluetrade(), output);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(14, this->lastpx(), output);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->openpx(), output);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(16, this->closepx(), output);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(17, this->highpx(), output);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(18, this->lowpx(), output);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(19, this->diffpx1(), output);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(20, this->diffpx2(), output);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(21, this->totalbuyqty(), output);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(22, this->totalsellqty(), output);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(23, this->weightedavgbuypx(), output);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(24, this->weightedavgsellpx(), output);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(25, this->withdrawbuynumber(), output);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(26, this->withdrawbuyamount(), output);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(27, this->withdrawbuymoney(), output);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(28, this->withdrawsellnumber(), output);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(29, this->withdrawsellamount(), output);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(30, this->withdrawsellmoney(), output);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(31, this->totalbuynumber(), output);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(32, this->totalsellnumber(), output);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(33, this->buytrademaxduration(), output);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(34, this->selltrademaxduration(), output);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(35, this->numbuyorders(), output);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(36, this->numsellorders(), output);
  }

  // optional int64 IOPV = 37;
  if (this->iopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(37, this->iopv(), output);
  }

  // optional int64 PreIOPV = 38;
  if (this->preiopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(38, this->preiopv(), output);
  }

  // optional int64 PurchaseNumber = 39;
  if (this->purchasenumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(39, this->purchasenumber(), output);
  }

  // optional int64 PurchaseAmount = 40;
  if (this->purchaseamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(40, this->purchaseamount(), output);
  }

  // optional int64 PurchaseMoney = 41;
  if (this->purchasemoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(41, this->purchasemoney(), output);
  }

  // optional int64 RedemptionNumber = 42;
  if (this->redemptionnumber() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(42, this->redemptionnumber(), output);
  }

  // optional int64 RedemptionAmount = 43;
  if (this->redemptionamount() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(43, this->redemptionamount(), output);
  }

  // optional int64 RedemptionMoney = 44;
  if (this->redemptionmoney() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(44, this->redemptionmoney(), output);
  }

  // optional int32 ExchangeDate = 45;
  if (this->exchangedate() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(45, this->exchangedate(), output);
  }

  // optional int32 ExchangeTime = 46;
  if (this->exchangetime() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(46, this->exchangetime(), output);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(50, this->channelno(), output);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(51, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buypricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buypricequeue(i), output);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(52, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqtyqueue(i), output);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(53, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellpricequeue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellpricequeue(i), output);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(54, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqtyqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqtyqueue(i), output);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(55, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buyorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buyorderqueue(i), output);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(56, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellorderqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellorderqueue(i), output);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(57, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_buynumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->buynumordersqueue(i), output);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(58, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(_sellnumordersqueue_cached_byte_size_);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64NoTag(
      this->sellnumordersqueue(i), output);
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(59, this->norminalpx(), output);
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(60, this->shortsellsharestraded(), output);
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(61, this->shortsellturnover(), output);
  }

  // optional int64 PreMarketLastPx = 62;
  if (this->premarketlastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(62, this->premarketlastpx(), output);
  }

  // optional int64 PreMarketTotalVolumeTrade = 63;
  if (this->premarkettotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(63, this->premarkettotalvolumetrade(), output);
  }

  // optional int64 PreMarketTotalValueTrade = 64;
  if (this->premarkettotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(64, this->premarkettotalvaluetrade(), output);
  }

  // optional int64 PreMarketHighPx = 65;
  if (this->premarkethighpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(65, this->premarkethighpx(), output);
  }

  // optional int64 PreMarketLowPx = 66;
  if (this->premarketlowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(66, this->premarketlowpx(), output);
  }

  // optional int64 AfterHoursLastPx = 67;
  if (this->afterhourslastpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(67, this->afterhourslastpx(), output);
  }

  // optional int64 AfterHoursTotalVolumeTrade = 68;
  if (this->afterhourstotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(68, this->afterhourstotalvolumetrade(), output);
  }

  // optional int64 AfterHoursTotalValueTrade = 69;
  if (this->afterhourstotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(69, this->afterhourstotalvaluetrade(), output);
  }

  // optional int64 AfterHoursHighPx = 70;
  if (this->afterhourshighpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(70, this->afterhourshighpx(), output);
  }

  // optional int64 AfterHoursLowPx = 71;
  if (this->afterhourslowpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(71, this->afterhourslowpx(), output);
  }

  // optional string MarketPhaseCode = 72;
  if (this->marketphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketphasecode().data(), this->marketphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.MarketPhaseCode");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      72, this->marketphasecode(), output);
  }

  // optional int64 USConsolidateVolume = 73;
  if (this->usconsolidatevolume() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(73, this->usconsolidatevolume(), output);
  }

  // optional int64 USCompositeClosePx = 74;
  if (this->uscompositeclosepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(74, this->uscompositeclosepx(), output);
  }

  // optional string TradingHaltReason = 75;
  if (this->tradinghaltreason().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradinghaltreason().data(), this->tradinghaltreason().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.TradingHaltReason");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      75, this->tradinghaltreason(), output);
  }

  // optional int64 OtcTotalVolumeTrade = 76;
  if (this->otctotalvolumetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(76, this->otctotalvolumetrade(), output);
  }

  // optional int64 OtcTotalValueTrade = 77;
  if (this->otctotalvaluetrade() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(77, this->otctotalvaluetrade(), output);
  }

  // optional int64 OtcNumTrades = 78;
  if (this->otcnumtrades() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(78, this->otcnumtrades(), output);
  }

  // optional int32 DataMultiplePowerOf10 = 79;
  if (this->datamultiplepowerof10() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(79, this->datamultiplepowerof10(), output);
  }

  // optional int64 WeightedAvgPx = 80;
  if (this->weightedavgpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(80, this->weightedavgpx(), output);
  }

  // optional int64 PreCloseWeightedAvgPx = 81;
  if (this->precloseweightedavgpx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(81, this->precloseweightedavgpx(), output);
  }

  // optional int64 BestBuyPrice = 82;
  if (this->bestbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(82, this->bestbuyprice(), output);
  }

  // optional int64 QtyAtBestBuyPrice = 83;
  if (this->qtyatbestbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(83, this->qtyatbestbuyprice(), output);
  }

  // optional int64 BestSellPrice = 84;
  if (this->bestsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(84, this->bestsellprice(), output);
  }

  // optional int64 QtyAtBestSellPrice = 85;
  if (this->qtyatbestsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(85, this->qtyatbestsellprice(), output);
  }

  // optional int64 HighAccuracyIOPV = 86;
  if (this->highaccuracyiopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(86, this->highaccuracyiopv(), output);
  }

  // optional int64 HighAccuracyPreIOPV = 87;
  if (this->highaccuracypreiopv() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(87, this->highaccuracypreiopv(), output);
  }

  // optional int64 ReferencePx = 88;
  if (this->referencepx() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(88, this->referencepx(), output);
  }

  // optional int64 MaxBuyPrice = 89;
  if (this->maxbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(89, this->maxbuyprice(), output);
  }

  // optional int64 MinBuyPrice = 90;
  if (this->minbuyprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(90, this->minbuyprice(), output);
  }

  // optional int64 MaxSellPrice = 91;
  if (this->maxsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(91, this->maxsellprice(), output);
  }

  // optional int64 MinSellPrice = 92;
  if (this->minsellprice() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(92, this->minsellprice(), output);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(101, this->delaytype(), output);
  }

  // @@protoc_insertion_point(serialize_end:com.htsc.mdc.insight.model.MDFund)
}

::google::protobuf::uint8* MDFund::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:com.htsc.mdc.insight.model.MDFund)
  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->htscsecurityid().data(), this->htscsecurityid().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.HTSCSecurityID");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->htscsecurityid(), target);
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->mddate(), target);
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->mdtime(), target);
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(4, this->datatimestamp(), target);
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradingphasecode().data(), this->tradingphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.TradingPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        5, this->tradingphasecode(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      6, this->securityidsource(), target);
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      7, this->securitytype(), target);
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(8, this->maxpx(), target);
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->minpx(), target);
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(10, this->preclosepx(), target);
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(11, this->numtrades(), target);
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(12, this->totalvolumetrade(), target);
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(13, this->totalvaluetrade(), target);
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(14, this->lastpx(), target);
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->openpx(), target);
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(16, this->closepx(), target);
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(17, this->highpx(), target);
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(18, this->lowpx(), target);
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(19, this->diffpx1(), target);
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(20, this->diffpx2(), target);
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(21, this->totalbuyqty(), target);
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(22, this->totalsellqty(), target);
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(23, this->weightedavgbuypx(), target);
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(24, this->weightedavgsellpx(), target);
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(25, this->withdrawbuynumber(), target);
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(26, this->withdrawbuyamount(), target);
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(27, this->withdrawbuymoney(), target);
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(28, this->withdrawsellnumber(), target);
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(29, this->withdrawsellamount(), target);
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(30, this->withdrawsellmoney(), target);
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(31, this->totalbuynumber(), target);
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(32, this->totalsellnumber(), target);
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(33, this->buytrademaxduration(), target);
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(34, this->selltrademaxduration(), target);
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(35, this->numbuyorders(), target);
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(36, this->numsellorders(), target);
  }

  // optional int64 IOPV = 37;
  if (this->iopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(37, this->iopv(), target);
  }

  // optional int64 PreIOPV = 38;
  if (this->preiopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(38, this->preiopv(), target);
  }

  // optional int64 PurchaseNumber = 39;
  if (this->purchasenumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(39, this->purchasenumber(), target);
  }

  // optional int64 PurchaseAmount = 40;
  if (this->purchaseamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(40, this->purchaseamount(), target);
  }

  // optional int64 PurchaseMoney = 41;
  if (this->purchasemoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(41, this->purchasemoney(), target);
  }

  // optional int64 RedemptionNumber = 42;
  if (this->redemptionnumber() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(42, this->redemptionnumber(), target);
  }

  // optional int64 RedemptionAmount = 43;
  if (this->redemptionamount() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(43, this->redemptionamount(), target);
  }

  // optional int64 RedemptionMoney = 44;
  if (this->redemptionmoney() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(44, this->redemptionmoney(), target);
  }

  // optional int32 ExchangeDate = 45;
  if (this->exchangedate() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(45, this->exchangedate(), target);
  }

  // optional int32 ExchangeTime = 46;
  if (this->exchangetime() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(46, this->exchangetime(), target);
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(50, this->channelno(), target);
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  if (this->buypricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      51,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buypricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buypricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buypricequeue(i), target);
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  if (this->buyorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      52,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqtyqueue(i), target);
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  if (this->sellpricequeue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      53,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellpricequeue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellpricequeue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellpricequeue(i), target);
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  if (this->sellorderqtyqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      54,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqtyqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqtyqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqtyqueue(i), target);
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  if (this->buyorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      55,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buyorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buyorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buyorderqueue(i), target);
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  if (this->sellorderqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      56,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellorderqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellorderqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellorderqueue(i), target);
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  if (this->buynumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      57,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _buynumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->buynumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->buynumordersqueue(i), target);
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  if (this->sellnumordersqueue_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      58,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
      _sellnumordersqueue_cached_byte_size_, target);
  }
  for (int i = 0; i < this->sellnumordersqueue_size(); i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt64NoTagToArray(this->sellnumordersqueue(i), target);
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(59, this->norminalpx(), target);
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(60, this->shortsellsharestraded(), target);
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(61, this->shortsellturnover(), target);
  }

  // optional int64 PreMarketLastPx = 62;
  if (this->premarketlastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(62, this->premarketlastpx(), target);
  }

  // optional int64 PreMarketTotalVolumeTrade = 63;
  if (this->premarkettotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(63, this->premarkettotalvolumetrade(), target);
  }

  // optional int64 PreMarketTotalValueTrade = 64;
  if (this->premarkettotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(64, this->premarkettotalvaluetrade(), target);
  }

  // optional int64 PreMarketHighPx = 65;
  if (this->premarkethighpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(65, this->premarkethighpx(), target);
  }

  // optional int64 PreMarketLowPx = 66;
  if (this->premarketlowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(66, this->premarketlowpx(), target);
  }

  // optional int64 AfterHoursLastPx = 67;
  if (this->afterhourslastpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(67, this->afterhourslastpx(), target);
  }

  // optional int64 AfterHoursTotalVolumeTrade = 68;
  if (this->afterhourstotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(68, this->afterhourstotalvolumetrade(), target);
  }

  // optional int64 AfterHoursTotalValueTrade = 69;
  if (this->afterhourstotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(69, this->afterhourstotalvaluetrade(), target);
  }

  // optional int64 AfterHoursHighPx = 70;
  if (this->afterhourshighpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(70, this->afterhourshighpx(), target);
  }

  // optional int64 AfterHoursLowPx = 71;
  if (this->afterhourslowpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(71, this->afterhourslowpx(), target);
  }

  // optional string MarketPhaseCode = 72;
  if (this->marketphasecode().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->marketphasecode().data(), this->marketphasecode().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.MarketPhaseCode");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        72, this->marketphasecode(), target);
  }

  // optional int64 USConsolidateVolume = 73;
  if (this->usconsolidatevolume() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(73, this->usconsolidatevolume(), target);
  }

  // optional int64 USCompositeClosePx = 74;
  if (this->uscompositeclosepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(74, this->uscompositeclosepx(), target);
  }

  // optional string TradingHaltReason = 75;
  if (this->tradinghaltreason().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->tradinghaltreason().data(), this->tradinghaltreason().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "com.htsc.mdc.insight.model.MDFund.TradingHaltReason");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        75, this->tradinghaltreason(), target);
  }

  // optional int64 OtcTotalVolumeTrade = 76;
  if (this->otctotalvolumetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(76, this->otctotalvolumetrade(), target);
  }

  // optional int64 OtcTotalValueTrade = 77;
  if (this->otctotalvaluetrade() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(77, this->otctotalvaluetrade(), target);
  }

  // optional int64 OtcNumTrades = 78;
  if (this->otcnumtrades() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(78, this->otcnumtrades(), target);
  }

  // optional int32 DataMultiplePowerOf10 = 79;
  if (this->datamultiplepowerof10() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(79, this->datamultiplepowerof10(), target);
  }

  // optional int64 WeightedAvgPx = 80;
  if (this->weightedavgpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(80, this->weightedavgpx(), target);
  }

  // optional int64 PreCloseWeightedAvgPx = 81;
  if (this->precloseweightedavgpx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(81, this->precloseweightedavgpx(), target);
  }

  // optional int64 BestBuyPrice = 82;
  if (this->bestbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(82, this->bestbuyprice(), target);
  }

  // optional int64 QtyAtBestBuyPrice = 83;
  if (this->qtyatbestbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(83, this->qtyatbestbuyprice(), target);
  }

  // optional int64 BestSellPrice = 84;
  if (this->bestsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(84, this->bestsellprice(), target);
  }

  // optional int64 QtyAtBestSellPrice = 85;
  if (this->qtyatbestsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(85, this->qtyatbestsellprice(), target);
  }

  // optional int64 HighAccuracyIOPV = 86;
  if (this->highaccuracyiopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(86, this->highaccuracyiopv(), target);
  }

  // optional int64 HighAccuracyPreIOPV = 87;
  if (this->highaccuracypreiopv() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(87, this->highaccuracypreiopv(), target);
  }

  // optional int64 ReferencePx = 88;
  if (this->referencepx() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(88, this->referencepx(), target);
  }

  // optional int64 MaxBuyPrice = 89;
  if (this->maxbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(89, this->maxbuyprice(), target);
  }

  // optional int64 MinBuyPrice = 90;
  if (this->minbuyprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(90, this->minbuyprice(), target);
  }

  // optional int64 MaxSellPrice = 91;
  if (this->maxsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(91, this->maxsellprice(), target);
  }

  // optional int64 MinSellPrice = 92;
  if (this->minsellprice() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(92, this->minsellprice(), target);
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(101, this->delaytype(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:com.htsc.mdc.insight.model.MDFund)
  return target;
}

size_t MDFund::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:com.htsc.mdc.insight.model.MDFund)
  size_t total_size = 0;

  // optional string HTSCSecurityID = 1;
  if (this->htscsecurityid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->htscsecurityid());
  }

  // optional int32 MDDate = 2;
  if (this->mddate() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mddate());
  }

  // optional int32 MDTime = 3;
  if (this->mdtime() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->mdtime());
  }

  // optional int64 DataTimestamp = 4;
  if (this->datatimestamp() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->datatimestamp());
  }

  // optional string TradingPhaseCode = 5;
  if (this->tradingphasecode().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradingphasecode());
  }

  // optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
  if (this->securityidsource() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securityidsource());
  }

  // optional .com.htsc.mdc.model.ESecurityType securityType = 7;
  if (this->securitytype() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->securitytype());
  }

  // optional int64 MaxPx = 8;
  if (this->maxpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxpx());
  }

  // optional int64 MinPx = 9;
  if (this->minpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minpx());
  }

  // optional int64 PreClosePx = 10;
  if (this->preclosepx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preclosepx());
  }

  // optional int64 NumTrades = 11;
  if (this->numtrades() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->numtrades());
  }

  // optional int64 TotalVolumeTrade = 12;
  if (this->totalvolumetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvolumetrade());
  }

  // optional int64 TotalValueTrade = 13;
  if (this->totalvaluetrade() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalvaluetrade());
  }

  // optional int64 LastPx = 14;
  if (this->lastpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lastpx());
  }

  // optional int64 OpenPx = 15;
  if (this->openpx() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->openpx());
  }

  // optional int64 ClosePx = 16;
  if (this->closepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->closepx());
  }

  // optional int64 HighPx = 17;
  if (this->highpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highpx());
  }

  // optional int64 LowPx = 18;
  if (this->lowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->lowpx());
  }

  // optional int64 DiffPx1 = 19;
  if (this->diffpx1() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx1());
  }

  // optional int64 DiffPx2 = 20;
  if (this->diffpx2() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->diffpx2());
  }

  // optional int64 TotalBuyQty = 21;
  if (this->totalbuyqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuyqty());
  }

  // optional int64 TotalSellQty = 22;
  if (this->totalsellqty() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellqty());
  }

  // optional int64 WeightedAvgBuyPx = 23;
  if (this->weightedavgbuypx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgbuypx());
  }

  // optional int64 WeightedAvgSellPx = 24;
  if (this->weightedavgsellpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgsellpx());
  }

  // optional int64 WithdrawBuyNumber = 25;
  if (this->withdrawbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuynumber());
  }

  // optional int64 WithdrawBuyAmount = 26;
  if (this->withdrawbuyamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuyamount());
  }

  // optional int64 WithdrawBuyMoney = 27;
  if (this->withdrawbuymoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawbuymoney());
  }

  // optional int64 WithdrawSellNumber = 28;
  if (this->withdrawsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellnumber());
  }

  // optional int64 WithdrawSellAmount = 29;
  if (this->withdrawsellamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellamount());
  }

  // optional int64 WithdrawSellMoney = 30;
  if (this->withdrawsellmoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->withdrawsellmoney());
  }

  // optional int64 TotalBuyNumber = 31;
  if (this->totalbuynumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalbuynumber());
  }

  // optional int64 TotalSellNumber = 32;
  if (this->totalsellnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->totalsellnumber());
  }

  // optional int64 BuyTradeMaxDuration = 33;
  if (this->buytrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->buytrademaxduration());
  }

  // optional int64 SellTradeMaxDuration = 34;
  if (this->selltrademaxduration() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->selltrademaxduration());
  }

  // optional int32 NumBuyOrders = 35;
  if (this->numbuyorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numbuyorders());
  }

  // optional int32 NumSellOrders = 36;
  if (this->numsellorders() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->numsellorders());
  }

  // optional int64 IOPV = 37;
  if (this->iopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->iopv());
  }

  // optional int64 PreIOPV = 38;
  if (this->preiopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->preiopv());
  }

  // optional int64 PurchaseNumber = 39;
  if (this->purchasenumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->purchasenumber());
  }

  // optional int64 PurchaseAmount = 40;
  if (this->purchaseamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->purchaseamount());
  }

  // optional int64 PurchaseMoney = 41;
  if (this->purchasemoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->purchasemoney());
  }

  // optional int64 RedemptionNumber = 42;
  if (this->redemptionnumber() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->redemptionnumber());
  }

  // optional int64 RedemptionAmount = 43;
  if (this->redemptionamount() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->redemptionamount());
  }

  // optional int64 RedemptionMoney = 44;
  if (this->redemptionmoney() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->redemptionmoney());
  }

  // optional int32 ExchangeDate = 45;
  if (this->exchangedate() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangedate());
  }

  // optional int32 ExchangeTime = 46;
  if (this->exchangetime() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->exchangetime());
  }

  // optional int32 ChannelNo = 50;
  if (this->channelno() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->channelno());
  }

  // optional int64 NorminalPx = 59;
  if (this->norminalpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->norminalpx());
  }

  // optional int64 ShortSellSharesTraded = 60;
  if (this->shortsellsharestraded() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->shortsellsharestraded());
  }

  // optional int64 ShortSellTurnover = 61;
  if (this->shortsellturnover() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->shortsellturnover());
  }

  // optional int64 PreMarketLastPx = 62;
  if (this->premarketlastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarketlastpx());
  }

  // optional int64 PreMarketTotalVolumeTrade = 63;
  if (this->premarkettotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkettotalvolumetrade());
  }

  // optional int64 PreMarketTotalValueTrade = 64;
  if (this->premarkettotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkettotalvaluetrade());
  }

  // optional int64 PreMarketHighPx = 65;
  if (this->premarkethighpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarkethighpx());
  }

  // optional int64 PreMarketLowPx = 66;
  if (this->premarketlowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->premarketlowpx());
  }

  // optional int64 AfterHoursLastPx = 67;
  if (this->afterhourslastpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourslastpx());
  }

  // optional int64 AfterHoursTotalVolumeTrade = 68;
  if (this->afterhourstotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourstotalvolumetrade());
  }

  // optional int64 AfterHoursTotalValueTrade = 69;
  if (this->afterhourstotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourstotalvaluetrade());
  }

  // optional int64 AfterHoursHighPx = 70;
  if (this->afterhourshighpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourshighpx());
  }

  // optional int64 AfterHoursLowPx = 71;
  if (this->afterhourslowpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->afterhourslowpx());
  }

  // optional string MarketPhaseCode = 72;
  if (this->marketphasecode().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->marketphasecode());
  }

  // optional int64 USConsolidateVolume = 73;
  if (this->usconsolidatevolume() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->usconsolidatevolume());
  }

  // optional int64 USCompositeClosePx = 74;
  if (this->uscompositeclosepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->uscompositeclosepx());
  }

  // optional string TradingHaltReason = 75;
  if (this->tradinghaltreason().size() > 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->tradinghaltreason());
  }

  // optional int64 OtcTotalVolumeTrade = 76;
  if (this->otctotalvolumetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otctotalvolumetrade());
  }

  // optional int64 OtcTotalValueTrade = 77;
  if (this->otctotalvaluetrade() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otctotalvaluetrade());
  }

  // optional int64 OtcNumTrades = 78;
  if (this->otcnumtrades() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->otcnumtrades());
  }

  // optional int32 DataMultiplePowerOf10 = 79;
  if (this->datamultiplepowerof10() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->datamultiplepowerof10());
  }

  // optional int64 WeightedAvgPx = 80;
  if (this->weightedavgpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->weightedavgpx());
  }

  // optional int64 PreCloseWeightedAvgPx = 81;
  if (this->precloseweightedavgpx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->precloseweightedavgpx());
  }

  // optional int64 BestBuyPrice = 82;
  if (this->bestbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestbuyprice());
  }

  // optional int64 QtyAtBestBuyPrice = 83;
  if (this->qtyatbestbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->qtyatbestbuyprice());
  }

  // optional int64 BestSellPrice = 84;
  if (this->bestsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->bestsellprice());
  }

  // optional int64 QtyAtBestSellPrice = 85;
  if (this->qtyatbestsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->qtyatbestsellprice());
  }

  // optional int64 HighAccuracyIOPV = 86;
  if (this->highaccuracyiopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highaccuracyiopv());
  }

  // optional int64 HighAccuracyPreIOPV = 87;
  if (this->highaccuracypreiopv() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->highaccuracypreiopv());
  }

  // optional int64 ReferencePx = 88;
  if (this->referencepx() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->referencepx());
  }

  // optional int64 MaxBuyPrice = 89;
  if (this->maxbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxbuyprice());
  }

  // optional int64 MinBuyPrice = 90;
  if (this->minbuyprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minbuyprice());
  }

  // optional int64 MaxSellPrice = 91;
  if (this->maxsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->maxsellprice());
  }

  // optional int64 MinSellPrice = 92;
  if (this->minsellprice() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->minsellprice());
  }

  // optional int32 DelayType = 101;
  if (this->delaytype() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->delaytype());
  }

  // repeated int64 BuyPriceQueue = 51 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buypricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buypricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buypricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQtyQueue = 52 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellPriceQueue = 53 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellpricequeue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellpricequeue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellpricequeue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQtyQueue = 54 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqtyqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqtyqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqtyqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyOrderQueue = 55 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buyorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buyorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buyorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellOrderQueue = 56 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellorderqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellorderqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellorderqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 BuyNumOrdersQueue = 57 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->buynumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->buynumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _buynumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated int64 SellNumOrdersQueue = 58 [packed = true];
  {
    size_t data_size = 0;
    unsigned int count = this->sellnumordersqueue_size();
    for (unsigned int i = 0; i < count; i++) {
      data_size += ::google::protobuf::internal::WireFormatLite::
        Int64Size(this->sellnumordersqueue(i));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(data_size);
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _sellnumordersqueue_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void MDFund::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:com.htsc.mdc.insight.model.MDFund)
  if (GOOGLE_PREDICT_FALSE(&from == this)) MergeFromFail(__LINE__);
  const MDFund* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const MDFund>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:com.htsc.mdc.insight.model.MDFund)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:com.htsc.mdc.insight.model.MDFund)
    UnsafeMergeFrom(*source);
  }
}

void MDFund::MergeFrom(const MDFund& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:com.htsc.mdc.insight.model.MDFund)
  if (GOOGLE_PREDICT_TRUE(&from != this)) {
    UnsafeMergeFrom(from);
  } else {
    MergeFromFail(__LINE__);
  }
}

void MDFund::UnsafeMergeFrom(const MDFund& from) {
  GOOGLE_DCHECK(&from != this);
  buypricequeue_.UnsafeMergeFrom(from.buypricequeue_);
  buyorderqtyqueue_.UnsafeMergeFrom(from.buyorderqtyqueue_);
  sellpricequeue_.UnsafeMergeFrom(from.sellpricequeue_);
  sellorderqtyqueue_.UnsafeMergeFrom(from.sellorderqtyqueue_);
  buyorderqueue_.UnsafeMergeFrom(from.buyorderqueue_);
  sellorderqueue_.UnsafeMergeFrom(from.sellorderqueue_);
  buynumordersqueue_.UnsafeMergeFrom(from.buynumordersqueue_);
  sellnumordersqueue_.UnsafeMergeFrom(from.sellnumordersqueue_);
  if (from.htscsecurityid().size() > 0) {

    htscsecurityid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.htscsecurityid_);
  }
  if (from.mddate() != 0) {
    set_mddate(from.mddate());
  }
  if (from.mdtime() != 0) {
    set_mdtime(from.mdtime());
  }
  if (from.datatimestamp() != 0) {
    set_datatimestamp(from.datatimestamp());
  }
  if (from.tradingphasecode().size() > 0) {

    tradingphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradingphasecode_);
  }
  if (from.securityidsource() != 0) {
    set_securityidsource(from.securityidsource());
  }
  if (from.securitytype() != 0) {
    set_securitytype(from.securitytype());
  }
  if (from.maxpx() != 0) {
    set_maxpx(from.maxpx());
  }
  if (from.minpx() != 0) {
    set_minpx(from.minpx());
  }
  if (from.preclosepx() != 0) {
    set_preclosepx(from.preclosepx());
  }
  if (from.numtrades() != 0) {
    set_numtrades(from.numtrades());
  }
  if (from.totalvolumetrade() != 0) {
    set_totalvolumetrade(from.totalvolumetrade());
  }
  if (from.totalvaluetrade() != 0) {
    set_totalvaluetrade(from.totalvaluetrade());
  }
  if (from.lastpx() != 0) {
    set_lastpx(from.lastpx());
  }
  if (from.openpx() != 0) {
    set_openpx(from.openpx());
  }
  if (from.closepx() != 0) {
    set_closepx(from.closepx());
  }
  if (from.highpx() != 0) {
    set_highpx(from.highpx());
  }
  if (from.lowpx() != 0) {
    set_lowpx(from.lowpx());
  }
  if (from.diffpx1() != 0) {
    set_diffpx1(from.diffpx1());
  }
  if (from.diffpx2() != 0) {
    set_diffpx2(from.diffpx2());
  }
  if (from.totalbuyqty() != 0) {
    set_totalbuyqty(from.totalbuyqty());
  }
  if (from.totalsellqty() != 0) {
    set_totalsellqty(from.totalsellqty());
  }
  if (from.weightedavgbuypx() != 0) {
    set_weightedavgbuypx(from.weightedavgbuypx());
  }
  if (from.weightedavgsellpx() != 0) {
    set_weightedavgsellpx(from.weightedavgsellpx());
  }
  if (from.withdrawbuynumber() != 0) {
    set_withdrawbuynumber(from.withdrawbuynumber());
  }
  if (from.withdrawbuyamount() != 0) {
    set_withdrawbuyamount(from.withdrawbuyamount());
  }
  if (from.withdrawbuymoney() != 0) {
    set_withdrawbuymoney(from.withdrawbuymoney());
  }
  if (from.withdrawsellnumber() != 0) {
    set_withdrawsellnumber(from.withdrawsellnumber());
  }
  if (from.withdrawsellamount() != 0) {
    set_withdrawsellamount(from.withdrawsellamount());
  }
  if (from.withdrawsellmoney() != 0) {
    set_withdrawsellmoney(from.withdrawsellmoney());
  }
  if (from.totalbuynumber() != 0) {
    set_totalbuynumber(from.totalbuynumber());
  }
  if (from.totalsellnumber() != 0) {
    set_totalsellnumber(from.totalsellnumber());
  }
  if (from.buytrademaxduration() != 0) {
    set_buytrademaxduration(from.buytrademaxduration());
  }
  if (from.selltrademaxduration() != 0) {
    set_selltrademaxduration(from.selltrademaxduration());
  }
  if (from.numbuyorders() != 0) {
    set_numbuyorders(from.numbuyorders());
  }
  if (from.numsellorders() != 0) {
    set_numsellorders(from.numsellorders());
  }
  if (from.iopv() != 0) {
    set_iopv(from.iopv());
  }
  if (from.preiopv() != 0) {
    set_preiopv(from.preiopv());
  }
  if (from.purchasenumber() != 0) {
    set_purchasenumber(from.purchasenumber());
  }
  if (from.purchaseamount() != 0) {
    set_purchaseamount(from.purchaseamount());
  }
  if (from.purchasemoney() != 0) {
    set_purchasemoney(from.purchasemoney());
  }
  if (from.redemptionnumber() != 0) {
    set_redemptionnumber(from.redemptionnumber());
  }
  if (from.redemptionamount() != 0) {
    set_redemptionamount(from.redemptionamount());
  }
  if (from.redemptionmoney() != 0) {
    set_redemptionmoney(from.redemptionmoney());
  }
  if (from.exchangedate() != 0) {
    set_exchangedate(from.exchangedate());
  }
  if (from.exchangetime() != 0) {
    set_exchangetime(from.exchangetime());
  }
  if (from.channelno() != 0) {
    set_channelno(from.channelno());
  }
  if (from.norminalpx() != 0) {
    set_norminalpx(from.norminalpx());
  }
  if (from.shortsellsharestraded() != 0) {
    set_shortsellsharestraded(from.shortsellsharestraded());
  }
  if (from.shortsellturnover() != 0) {
    set_shortsellturnover(from.shortsellturnover());
  }
  if (from.premarketlastpx() != 0) {
    set_premarketlastpx(from.premarketlastpx());
  }
  if (from.premarkettotalvolumetrade() != 0) {
    set_premarkettotalvolumetrade(from.premarkettotalvolumetrade());
  }
  if (from.premarkettotalvaluetrade() != 0) {
    set_premarkettotalvaluetrade(from.premarkettotalvaluetrade());
  }
  if (from.premarkethighpx() != 0) {
    set_premarkethighpx(from.premarkethighpx());
  }
  if (from.premarketlowpx() != 0) {
    set_premarketlowpx(from.premarketlowpx());
  }
  if (from.afterhourslastpx() != 0) {
    set_afterhourslastpx(from.afterhourslastpx());
  }
  if (from.afterhourstotalvolumetrade() != 0) {
    set_afterhourstotalvolumetrade(from.afterhourstotalvolumetrade());
  }
  if (from.afterhourstotalvaluetrade() != 0) {
    set_afterhourstotalvaluetrade(from.afterhourstotalvaluetrade());
  }
  if (from.afterhourshighpx() != 0) {
    set_afterhourshighpx(from.afterhourshighpx());
  }
  if (from.afterhourslowpx() != 0) {
    set_afterhourslowpx(from.afterhourslowpx());
  }
  if (from.marketphasecode().size() > 0) {

    marketphasecode_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.marketphasecode_);
  }
  if (from.usconsolidatevolume() != 0) {
    set_usconsolidatevolume(from.usconsolidatevolume());
  }
  if (from.uscompositeclosepx() != 0) {
    set_uscompositeclosepx(from.uscompositeclosepx());
  }
  if (from.tradinghaltreason().size() > 0) {

    tradinghaltreason_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.tradinghaltreason_);
  }
  if (from.otctotalvolumetrade() != 0) {
    set_otctotalvolumetrade(from.otctotalvolumetrade());
  }
  if (from.otctotalvaluetrade() != 0) {
    set_otctotalvaluetrade(from.otctotalvaluetrade());
  }
  if (from.otcnumtrades() != 0) {
    set_otcnumtrades(from.otcnumtrades());
  }
  if (from.datamultiplepowerof10() != 0) {
    set_datamultiplepowerof10(from.datamultiplepowerof10());
  }
  if (from.weightedavgpx() != 0) {
    set_weightedavgpx(from.weightedavgpx());
  }
  if (from.precloseweightedavgpx() != 0) {
    set_precloseweightedavgpx(from.precloseweightedavgpx());
  }
  if (from.bestbuyprice() != 0) {
    set_bestbuyprice(from.bestbuyprice());
  }
  if (from.qtyatbestbuyprice() != 0) {
    set_qtyatbestbuyprice(from.qtyatbestbuyprice());
  }
  if (from.bestsellprice() != 0) {
    set_bestsellprice(from.bestsellprice());
  }
  if (from.qtyatbestsellprice() != 0) {
    set_qtyatbestsellprice(from.qtyatbestsellprice());
  }
  if (from.highaccuracyiopv() != 0) {
    set_highaccuracyiopv(from.highaccuracyiopv());
  }
  if (from.highaccuracypreiopv() != 0) {
    set_highaccuracypreiopv(from.highaccuracypreiopv());
  }
  if (from.referencepx() != 0) {
    set_referencepx(from.referencepx());
  }
  if (from.maxbuyprice() != 0) {
    set_maxbuyprice(from.maxbuyprice());
  }
  if (from.minbuyprice() != 0) {
    set_minbuyprice(from.minbuyprice());
  }
  if (from.maxsellprice() != 0) {
    set_maxsellprice(from.maxsellprice());
  }
  if (from.minsellprice() != 0) {
    set_minsellprice(from.minsellprice());
  }
  if (from.delaytype() != 0) {
    set_delaytype(from.delaytype());
  }
}

void MDFund::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:com.htsc.mdc.insight.model.MDFund)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MDFund::CopyFrom(const MDFund& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:com.htsc.mdc.insight.model.MDFund)
  if (&from == this) return;
  Clear();
  UnsafeMergeFrom(from);
}

bool MDFund::IsInitialized() const {

  return true;
}

void MDFund::Swap(MDFund* other) {
  if (other == this) return;
  InternalSwap(other);
}
void MDFund::InternalSwap(MDFund* other) {
  htscsecurityid_.Swap(&other->htscsecurityid_);
  std::swap(mddate_, other->mddate_);
  std::swap(mdtime_, other->mdtime_);
  std::swap(datatimestamp_, other->datatimestamp_);
  tradingphasecode_.Swap(&other->tradingphasecode_);
  std::swap(securityidsource_, other->securityidsource_);
  std::swap(securitytype_, other->securitytype_);
  std::swap(maxpx_, other->maxpx_);
  std::swap(minpx_, other->minpx_);
  std::swap(preclosepx_, other->preclosepx_);
  std::swap(numtrades_, other->numtrades_);
  std::swap(totalvolumetrade_, other->totalvolumetrade_);
  std::swap(totalvaluetrade_, other->totalvaluetrade_);
  std::swap(lastpx_, other->lastpx_);
  std::swap(openpx_, other->openpx_);
  std::swap(closepx_, other->closepx_);
  std::swap(highpx_, other->highpx_);
  std::swap(lowpx_, other->lowpx_);
  std::swap(diffpx1_, other->diffpx1_);
  std::swap(diffpx2_, other->diffpx2_);
  std::swap(totalbuyqty_, other->totalbuyqty_);
  std::swap(totalsellqty_, other->totalsellqty_);
  std::swap(weightedavgbuypx_, other->weightedavgbuypx_);
  std::swap(weightedavgsellpx_, other->weightedavgsellpx_);
  std::swap(withdrawbuynumber_, other->withdrawbuynumber_);
  std::swap(withdrawbuyamount_, other->withdrawbuyamount_);
  std::swap(withdrawbuymoney_, other->withdrawbuymoney_);
  std::swap(withdrawsellnumber_, other->withdrawsellnumber_);
  std::swap(withdrawsellamount_, other->withdrawsellamount_);
  std::swap(withdrawsellmoney_, other->withdrawsellmoney_);
  std::swap(totalbuynumber_, other->totalbuynumber_);
  std::swap(totalsellnumber_, other->totalsellnumber_);
  std::swap(buytrademaxduration_, other->buytrademaxduration_);
  std::swap(selltrademaxduration_, other->selltrademaxduration_);
  std::swap(numbuyorders_, other->numbuyorders_);
  std::swap(numsellorders_, other->numsellorders_);
  std::swap(iopv_, other->iopv_);
  std::swap(preiopv_, other->preiopv_);
  std::swap(purchasenumber_, other->purchasenumber_);
  std::swap(purchaseamount_, other->purchaseamount_);
  std::swap(purchasemoney_, other->purchasemoney_);
  std::swap(redemptionnumber_, other->redemptionnumber_);
  std::swap(redemptionamount_, other->redemptionamount_);
  std::swap(redemptionmoney_, other->redemptionmoney_);
  std::swap(exchangedate_, other->exchangedate_);
  std::swap(exchangetime_, other->exchangetime_);
  std::swap(channelno_, other->channelno_);
  buypricequeue_.UnsafeArenaSwap(&other->buypricequeue_);
  buyorderqtyqueue_.UnsafeArenaSwap(&other->buyorderqtyqueue_);
  sellpricequeue_.UnsafeArenaSwap(&other->sellpricequeue_);
  sellorderqtyqueue_.UnsafeArenaSwap(&other->sellorderqtyqueue_);
  buyorderqueue_.UnsafeArenaSwap(&other->buyorderqueue_);
  sellorderqueue_.UnsafeArenaSwap(&other->sellorderqueue_);
  buynumordersqueue_.UnsafeArenaSwap(&other->buynumordersqueue_);
  sellnumordersqueue_.UnsafeArenaSwap(&other->sellnumordersqueue_);
  std::swap(norminalpx_, other->norminalpx_);
  std::swap(shortsellsharestraded_, other->shortsellsharestraded_);
  std::swap(shortsellturnover_, other->shortsellturnover_);
  std::swap(premarketlastpx_, other->premarketlastpx_);
  std::swap(premarkettotalvolumetrade_, other->premarkettotalvolumetrade_);
  std::swap(premarkettotalvaluetrade_, other->premarkettotalvaluetrade_);
  std::swap(premarkethighpx_, other->premarkethighpx_);
  std::swap(premarketlowpx_, other->premarketlowpx_);
  std::swap(afterhourslastpx_, other->afterhourslastpx_);
  std::swap(afterhourstotalvolumetrade_, other->afterhourstotalvolumetrade_);
  std::swap(afterhourstotalvaluetrade_, other->afterhourstotalvaluetrade_);
  std::swap(afterhourshighpx_, other->afterhourshighpx_);
  std::swap(afterhourslowpx_, other->afterhourslowpx_);
  marketphasecode_.Swap(&other->marketphasecode_);
  std::swap(usconsolidatevolume_, other->usconsolidatevolume_);
  std::swap(uscompositeclosepx_, other->uscompositeclosepx_);
  tradinghaltreason_.Swap(&other->tradinghaltreason_);
  std::swap(otctotalvolumetrade_, other->otctotalvolumetrade_);
  std::swap(otctotalvaluetrade_, other->otctotalvaluetrade_);
  std::swap(otcnumtrades_, other->otcnumtrades_);
  std::swap(datamultiplepowerof10_, other->datamultiplepowerof10_);
  std::swap(weightedavgpx_, other->weightedavgpx_);
  std::swap(precloseweightedavgpx_, other->precloseweightedavgpx_);
  std::swap(bestbuyprice_, other->bestbuyprice_);
  std::swap(qtyatbestbuyprice_, other->qtyatbestbuyprice_);
  std::swap(bestsellprice_, other->bestsellprice_);
  std::swap(qtyatbestsellprice_, other->qtyatbestsellprice_);
  std::swap(highaccuracyiopv_, other->highaccuracyiopv_);
  std::swap(highaccuracypreiopv_, other->highaccuracypreiopv_);
  std::swap(referencepx_, other->referencepx_);
  std::swap(maxbuyprice_, other->maxbuyprice_);
  std::swap(minbuyprice_, other->minbuyprice_);
  std::swap(maxsellprice_, other->maxsellprice_);
  std::swap(minsellprice_, other->minsellprice_);
  std::swap(delaytype_, other->delaytype_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata MDFund::GetMetadata() const {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::Metadata metadata;
  metadata.descriptor = MDFund_descriptor_;
  metadata.reflection = MDFund_reflection_;
  return metadata;
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// MDFund

// optional string HTSCSecurityID = 1;
void MDFund::clear_htscsecurityid() {
  htscsecurityid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFund::htscsecurityid() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
  return htscsecurityid_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_htscsecurityid(const ::std::string& value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
}
void MDFund::set_htscsecurityid(const char* value) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
}
void MDFund::set_htscsecurityid(const char* value, size_t size) {
  
  htscsecurityid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
}
::std::string* MDFund::mutable_htscsecurityid() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
  return htscsecurityid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFund::release_htscsecurityid() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
  
  return htscsecurityid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_allocated_htscsecurityid(::std::string* htscsecurityid) {
  if (htscsecurityid != NULL) {
    
  } else {
    
  }
  htscsecurityid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), htscsecurityid);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFund.HTSCSecurityID)
}

// optional int32 MDDate = 2;
void MDFund::clear_mddate() {
  mddate_ = 0;
}
::google::protobuf::int32 MDFund::mddate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MDDate)
  return mddate_;
}
void MDFund::set_mddate(::google::protobuf::int32 value) {
  
  mddate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MDDate)
}

// optional int32 MDTime = 3;
void MDFund::clear_mdtime() {
  mdtime_ = 0;
}
::google::protobuf::int32 MDFund::mdtime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MDTime)
  return mdtime_;
}
void MDFund::set_mdtime(::google::protobuf::int32 value) {
  
  mdtime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MDTime)
}

// optional int64 DataTimestamp = 4;
void MDFund::clear_datatimestamp() {
  datatimestamp_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::datatimestamp() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.DataTimestamp)
  return datatimestamp_;
}
void MDFund::set_datatimestamp(::google::protobuf::int64 value) {
  
  datatimestamp_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.DataTimestamp)
}

// optional string TradingPhaseCode = 5;
void MDFund::clear_tradingphasecode() {
  tradingphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFund::tradingphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
  return tradingphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_tradingphasecode(const ::std::string& value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
}
void MDFund::set_tradingphasecode(const char* value) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
}
void MDFund::set_tradingphasecode(const char* value, size_t size) {
  
  tradingphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
}
::std::string* MDFund::mutable_tradingphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
  return tradingphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFund::release_tradingphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
  
  return tradingphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_allocated_tradingphasecode(::std::string* tradingphasecode) {
  if (tradingphasecode != NULL) {
    
  } else {
    
  }
  tradingphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradingphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFund.TradingPhaseCode)
}

// optional .com.htsc.mdc.model.ESecurityIDSource securityIDSource = 6;
void MDFund::clear_securityidsource() {
  securityidsource_ = 0;
}
::com::htsc::mdc::model::ESecurityIDSource MDFund::securityidsource() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.securityIDSource)
  return static_cast< ::com::htsc::mdc::model::ESecurityIDSource >(securityidsource_);
}
void MDFund::set_securityidsource(::com::htsc::mdc::model::ESecurityIDSource value) {
  
  securityidsource_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.securityIDSource)
}

// optional .com.htsc.mdc.model.ESecurityType securityType = 7;
void MDFund::clear_securitytype() {
  securitytype_ = 0;
}
::com::htsc::mdc::model::ESecurityType MDFund::securitytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.securityType)
  return static_cast< ::com::htsc::mdc::model::ESecurityType >(securitytype_);
}
void MDFund::set_securitytype(::com::htsc::mdc::model::ESecurityType value) {
  
  securitytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.securityType)
}

// optional int64 MaxPx = 8;
void MDFund::clear_maxpx() {
  maxpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::maxpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MaxPx)
  return maxpx_;
}
void MDFund::set_maxpx(::google::protobuf::int64 value) {
  
  maxpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MaxPx)
}

// optional int64 MinPx = 9;
void MDFund::clear_minpx() {
  minpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::minpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MinPx)
  return minpx_;
}
void MDFund::set_minpx(::google::protobuf::int64 value) {
  
  minpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MinPx)
}

// optional int64 PreClosePx = 10;
void MDFund::clear_preclosepx() {
  preclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::preclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreClosePx)
  return preclosepx_;
}
void MDFund::set_preclosepx(::google::protobuf::int64 value) {
  
  preclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreClosePx)
}

// optional int64 NumTrades = 11;
void MDFund::clear_numtrades() {
  numtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::numtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.NumTrades)
  return numtrades_;
}
void MDFund::set_numtrades(::google::protobuf::int64 value) {
  
  numtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.NumTrades)
}

// optional int64 TotalVolumeTrade = 12;
void MDFund::clear_totalvolumetrade() {
  totalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalVolumeTrade)
  return totalvolumetrade_;
}
void MDFund::set_totalvolumetrade(::google::protobuf::int64 value) {
  
  totalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalVolumeTrade)
}

// optional int64 TotalValueTrade = 13;
void MDFund::clear_totalvaluetrade() {
  totalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalValueTrade)
  return totalvaluetrade_;
}
void MDFund::set_totalvaluetrade(::google::protobuf::int64 value) {
  
  totalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalValueTrade)
}

// optional int64 LastPx = 14;
void MDFund::clear_lastpx() {
  lastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::lastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.LastPx)
  return lastpx_;
}
void MDFund::set_lastpx(::google::protobuf::int64 value) {
  
  lastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.LastPx)
}

// optional int64 OpenPx = 15;
void MDFund::clear_openpx() {
  openpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::openpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.OpenPx)
  return openpx_;
}
void MDFund::set_openpx(::google::protobuf::int64 value) {
  
  openpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.OpenPx)
}

// optional int64 ClosePx = 16;
void MDFund::clear_closepx() {
  closepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::closepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ClosePx)
  return closepx_;
}
void MDFund::set_closepx(::google::protobuf::int64 value) {
  
  closepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ClosePx)
}

// optional int64 HighPx = 17;
void MDFund::clear_highpx() {
  highpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::highpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.HighPx)
  return highpx_;
}
void MDFund::set_highpx(::google::protobuf::int64 value) {
  
  highpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.HighPx)
}

// optional int64 LowPx = 18;
void MDFund::clear_lowpx() {
  lowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::lowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.LowPx)
  return lowpx_;
}
void MDFund::set_lowpx(::google::protobuf::int64 value) {
  
  lowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.LowPx)
}

// optional int64 DiffPx1 = 19;
void MDFund::clear_diffpx1() {
  diffpx1_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::diffpx1() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.DiffPx1)
  return diffpx1_;
}
void MDFund::set_diffpx1(::google::protobuf::int64 value) {
  
  diffpx1_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.DiffPx1)
}

// optional int64 DiffPx2 = 20;
void MDFund::clear_diffpx2() {
  diffpx2_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::diffpx2() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.DiffPx2)
  return diffpx2_;
}
void MDFund::set_diffpx2(::google::protobuf::int64 value) {
  
  diffpx2_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.DiffPx2)
}

// optional int64 TotalBuyQty = 21;
void MDFund::clear_totalbuyqty() {
  totalbuyqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalbuyqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalBuyQty)
  return totalbuyqty_;
}
void MDFund::set_totalbuyqty(::google::protobuf::int64 value) {
  
  totalbuyqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalBuyQty)
}

// optional int64 TotalSellQty = 22;
void MDFund::clear_totalsellqty() {
  totalsellqty_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalsellqty() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalSellQty)
  return totalsellqty_;
}
void MDFund::set_totalsellqty(::google::protobuf::int64 value) {
  
  totalsellqty_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalSellQty)
}

// optional int64 WeightedAvgBuyPx = 23;
void MDFund::clear_weightedavgbuypx() {
  weightedavgbuypx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::weightedavgbuypx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WeightedAvgBuyPx)
  return weightedavgbuypx_;
}
void MDFund::set_weightedavgbuypx(::google::protobuf::int64 value) {
  
  weightedavgbuypx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WeightedAvgBuyPx)
}

// optional int64 WeightedAvgSellPx = 24;
void MDFund::clear_weightedavgsellpx() {
  weightedavgsellpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::weightedavgsellpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WeightedAvgSellPx)
  return weightedavgsellpx_;
}
void MDFund::set_weightedavgsellpx(::google::protobuf::int64 value) {
  
  weightedavgsellpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WeightedAvgSellPx)
}

// optional int64 WithdrawBuyNumber = 25;
void MDFund::clear_withdrawbuynumber() {
  withdrawbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawBuyNumber)
  return withdrawbuynumber_;
}
void MDFund::set_withdrawbuynumber(::google::protobuf::int64 value) {
  
  withdrawbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawBuyNumber)
}

// optional int64 WithdrawBuyAmount = 26;
void MDFund::clear_withdrawbuyamount() {
  withdrawbuyamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawbuyamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawBuyAmount)
  return withdrawbuyamount_;
}
void MDFund::set_withdrawbuyamount(::google::protobuf::int64 value) {
  
  withdrawbuyamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawBuyAmount)
}

// optional int64 WithdrawBuyMoney = 27;
void MDFund::clear_withdrawbuymoney() {
  withdrawbuymoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawbuymoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawBuyMoney)
  return withdrawbuymoney_;
}
void MDFund::set_withdrawbuymoney(::google::protobuf::int64 value) {
  
  withdrawbuymoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawBuyMoney)
}

// optional int64 WithdrawSellNumber = 28;
void MDFund::clear_withdrawsellnumber() {
  withdrawsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawSellNumber)
  return withdrawsellnumber_;
}
void MDFund::set_withdrawsellnumber(::google::protobuf::int64 value) {
  
  withdrawsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawSellNumber)
}

// optional int64 WithdrawSellAmount = 29;
void MDFund::clear_withdrawsellamount() {
  withdrawsellamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawsellamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawSellAmount)
  return withdrawsellamount_;
}
void MDFund::set_withdrawsellamount(::google::protobuf::int64 value) {
  
  withdrawsellamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawSellAmount)
}

// optional int64 WithdrawSellMoney = 30;
void MDFund::clear_withdrawsellmoney() {
  withdrawsellmoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::withdrawsellmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WithdrawSellMoney)
  return withdrawsellmoney_;
}
void MDFund::set_withdrawsellmoney(::google::protobuf::int64 value) {
  
  withdrawsellmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WithdrawSellMoney)
}

// optional int64 TotalBuyNumber = 31;
void MDFund::clear_totalbuynumber() {
  totalbuynumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalbuynumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalBuyNumber)
  return totalbuynumber_;
}
void MDFund::set_totalbuynumber(::google::protobuf::int64 value) {
  
  totalbuynumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalBuyNumber)
}

// optional int64 TotalSellNumber = 32;
void MDFund::clear_totalsellnumber() {
  totalsellnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::totalsellnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TotalSellNumber)
  return totalsellnumber_;
}
void MDFund::set_totalsellnumber(::google::protobuf::int64 value) {
  
  totalsellnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TotalSellNumber)
}

// optional int64 BuyTradeMaxDuration = 33;
void MDFund::clear_buytrademaxduration() {
  buytrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::buytrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BuyTradeMaxDuration)
  return buytrademaxduration_;
}
void MDFund::set_buytrademaxduration(::google::protobuf::int64 value) {
  
  buytrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BuyTradeMaxDuration)
}

// optional int64 SellTradeMaxDuration = 34;
void MDFund::clear_selltrademaxduration() {
  selltrademaxduration_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::selltrademaxduration() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.SellTradeMaxDuration)
  return selltrademaxduration_;
}
void MDFund::set_selltrademaxduration(::google::protobuf::int64 value) {
  
  selltrademaxduration_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.SellTradeMaxDuration)
}

// optional int32 NumBuyOrders = 35;
void MDFund::clear_numbuyorders() {
  numbuyorders_ = 0;
}
::google::protobuf::int32 MDFund::numbuyorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.NumBuyOrders)
  return numbuyorders_;
}
void MDFund::set_numbuyorders(::google::protobuf::int32 value) {
  
  numbuyorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.NumBuyOrders)
}

// optional int32 NumSellOrders = 36;
void MDFund::clear_numsellorders() {
  numsellorders_ = 0;
}
::google::protobuf::int32 MDFund::numsellorders() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.NumSellOrders)
  return numsellorders_;
}
void MDFund::set_numsellorders(::google::protobuf::int32 value) {
  
  numsellorders_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.NumSellOrders)
}

// optional int64 IOPV = 37;
void MDFund::clear_iopv() {
  iopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::iopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.IOPV)
  return iopv_;
}
void MDFund::set_iopv(::google::protobuf::int64 value) {
  
  iopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.IOPV)
}

// optional int64 PreIOPV = 38;
void MDFund::clear_preiopv() {
  preiopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::preiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreIOPV)
  return preiopv_;
}
void MDFund::set_preiopv(::google::protobuf::int64 value) {
  
  preiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreIOPV)
}

// optional int64 PurchaseNumber = 39;
void MDFund::clear_purchasenumber() {
  purchasenumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::purchasenumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PurchaseNumber)
  return purchasenumber_;
}
void MDFund::set_purchasenumber(::google::protobuf::int64 value) {
  
  purchasenumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PurchaseNumber)
}

// optional int64 PurchaseAmount = 40;
void MDFund::clear_purchaseamount() {
  purchaseamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::purchaseamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PurchaseAmount)
  return purchaseamount_;
}
void MDFund::set_purchaseamount(::google::protobuf::int64 value) {
  
  purchaseamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PurchaseAmount)
}

// optional int64 PurchaseMoney = 41;
void MDFund::clear_purchasemoney() {
  purchasemoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::purchasemoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PurchaseMoney)
  return purchasemoney_;
}
void MDFund::set_purchasemoney(::google::protobuf::int64 value) {
  
  purchasemoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PurchaseMoney)
}

// optional int64 RedemptionNumber = 42;
void MDFund::clear_redemptionnumber() {
  redemptionnumber_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::redemptionnumber() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.RedemptionNumber)
  return redemptionnumber_;
}
void MDFund::set_redemptionnumber(::google::protobuf::int64 value) {
  
  redemptionnumber_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.RedemptionNumber)
}

// optional int64 RedemptionAmount = 43;
void MDFund::clear_redemptionamount() {
  redemptionamount_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::redemptionamount() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.RedemptionAmount)
  return redemptionamount_;
}
void MDFund::set_redemptionamount(::google::protobuf::int64 value) {
  
  redemptionamount_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.RedemptionAmount)
}

// optional int64 RedemptionMoney = 44;
void MDFund::clear_redemptionmoney() {
  redemptionmoney_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::redemptionmoney() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.RedemptionMoney)
  return redemptionmoney_;
}
void MDFund::set_redemptionmoney(::google::protobuf::int64 value) {
  
  redemptionmoney_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.RedemptionMoney)
}

// optional int32 ExchangeDate = 45;
void MDFund::clear_exchangedate() {
  exchangedate_ = 0;
}
::google::protobuf::int32 MDFund::exchangedate() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ExchangeDate)
  return exchangedate_;
}
void MDFund::set_exchangedate(::google::protobuf::int32 value) {
  
  exchangedate_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ExchangeDate)
}

// optional int32 ExchangeTime = 46;
void MDFund::clear_exchangetime() {
  exchangetime_ = 0;
}
::google::protobuf::int32 MDFund::exchangetime() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ExchangeTime)
  return exchangetime_;
}
void MDFund::set_exchangetime(::google::protobuf::int32 value) {
  
  exchangetime_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ExchangeTime)
}

// optional int32 ChannelNo = 50;
void MDFund::clear_channelno() {
  channelno_ = 0;
}
::google::protobuf::int32 MDFund::channelno() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ChannelNo)
  return channelno_;
}
void MDFund::set_channelno(::google::protobuf::int32 value) {
  
  channelno_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ChannelNo)
}

// repeated int64 BuyPriceQueue = 51 [packed = true];
int MDFund::buypricequeue_size() const {
  return buypricequeue_.size();
}
void MDFund::clear_buypricequeue() {
  buypricequeue_.Clear();
}
::google::protobuf::int64 MDFund::buypricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BuyPriceQueue)
  return buypricequeue_.Get(index);
}
void MDFund::set_buypricequeue(int index, ::google::protobuf::int64 value) {
  buypricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BuyPriceQueue)
}
void MDFund::add_buypricequeue(::google::protobuf::int64 value) {
  buypricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.BuyPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::buypricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.BuyPriceQueue)
  return buypricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_buypricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.BuyPriceQueue)
  return &buypricequeue_;
}

// repeated int64 BuyOrderQtyQueue = 52 [packed = true];
int MDFund::buyorderqtyqueue_size() const {
  return buyorderqtyqueue_.size();
}
void MDFund::clear_buyorderqtyqueue() {
  buyorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDFund::buyorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BuyOrderQtyQueue)
  return buyorderqtyqueue_.Get(index);
}
void MDFund::set_buyorderqtyqueue(int index, ::google::protobuf::int64 value) {
  buyorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BuyOrderQtyQueue)
}
void MDFund::add_buyorderqtyqueue(::google::protobuf::int64 value) {
  buyorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.BuyOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::buyorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.BuyOrderQtyQueue)
  return buyorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_buyorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.BuyOrderQtyQueue)
  return &buyorderqtyqueue_;
}

// repeated int64 SellPriceQueue = 53 [packed = true];
int MDFund::sellpricequeue_size() const {
  return sellpricequeue_.size();
}
void MDFund::clear_sellpricequeue() {
  sellpricequeue_.Clear();
}
::google::protobuf::int64 MDFund::sellpricequeue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.SellPriceQueue)
  return sellpricequeue_.Get(index);
}
void MDFund::set_sellpricequeue(int index, ::google::protobuf::int64 value) {
  sellpricequeue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.SellPriceQueue)
}
void MDFund::add_sellpricequeue(::google::protobuf::int64 value) {
  sellpricequeue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.SellPriceQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::sellpricequeue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.SellPriceQueue)
  return sellpricequeue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_sellpricequeue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.SellPriceQueue)
  return &sellpricequeue_;
}

// repeated int64 SellOrderQtyQueue = 54 [packed = true];
int MDFund::sellorderqtyqueue_size() const {
  return sellorderqtyqueue_.size();
}
void MDFund::clear_sellorderqtyqueue() {
  sellorderqtyqueue_.Clear();
}
::google::protobuf::int64 MDFund::sellorderqtyqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.SellOrderQtyQueue)
  return sellorderqtyqueue_.Get(index);
}
void MDFund::set_sellorderqtyqueue(int index, ::google::protobuf::int64 value) {
  sellorderqtyqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.SellOrderQtyQueue)
}
void MDFund::add_sellorderqtyqueue(::google::protobuf::int64 value) {
  sellorderqtyqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.SellOrderQtyQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::sellorderqtyqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.SellOrderQtyQueue)
  return sellorderqtyqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_sellorderqtyqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.SellOrderQtyQueue)
  return &sellorderqtyqueue_;
}

// repeated int64 BuyOrderQueue = 55 [packed = true];
int MDFund::buyorderqueue_size() const {
  return buyorderqueue_.size();
}
void MDFund::clear_buyorderqueue() {
  buyorderqueue_.Clear();
}
::google::protobuf::int64 MDFund::buyorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BuyOrderQueue)
  return buyorderqueue_.Get(index);
}
void MDFund::set_buyorderqueue(int index, ::google::protobuf::int64 value) {
  buyorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BuyOrderQueue)
}
void MDFund::add_buyorderqueue(::google::protobuf::int64 value) {
  buyorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.BuyOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::buyorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.BuyOrderQueue)
  return buyorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_buyorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.BuyOrderQueue)
  return &buyorderqueue_;
}

// repeated int64 SellOrderQueue = 56 [packed = true];
int MDFund::sellorderqueue_size() const {
  return sellorderqueue_.size();
}
void MDFund::clear_sellorderqueue() {
  sellorderqueue_.Clear();
}
::google::protobuf::int64 MDFund::sellorderqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.SellOrderQueue)
  return sellorderqueue_.Get(index);
}
void MDFund::set_sellorderqueue(int index, ::google::protobuf::int64 value) {
  sellorderqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.SellOrderQueue)
}
void MDFund::add_sellorderqueue(::google::protobuf::int64 value) {
  sellorderqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.SellOrderQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::sellorderqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.SellOrderQueue)
  return sellorderqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_sellorderqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.SellOrderQueue)
  return &sellorderqueue_;
}

// repeated int64 BuyNumOrdersQueue = 57 [packed = true];
int MDFund::buynumordersqueue_size() const {
  return buynumordersqueue_.size();
}
void MDFund::clear_buynumordersqueue() {
  buynumordersqueue_.Clear();
}
::google::protobuf::int64 MDFund::buynumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BuyNumOrdersQueue)
  return buynumordersqueue_.Get(index);
}
void MDFund::set_buynumordersqueue(int index, ::google::protobuf::int64 value) {
  buynumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BuyNumOrdersQueue)
}
void MDFund::add_buynumordersqueue(::google::protobuf::int64 value) {
  buynumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.BuyNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::buynumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.BuyNumOrdersQueue)
  return buynumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_buynumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.BuyNumOrdersQueue)
  return &buynumordersqueue_;
}

// repeated int64 SellNumOrdersQueue = 58 [packed = true];
int MDFund::sellnumordersqueue_size() const {
  return sellnumordersqueue_.size();
}
void MDFund::clear_sellnumordersqueue() {
  sellnumordersqueue_.Clear();
}
::google::protobuf::int64 MDFund::sellnumordersqueue(int index) const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.SellNumOrdersQueue)
  return sellnumordersqueue_.Get(index);
}
void MDFund::set_sellnumordersqueue(int index, ::google::protobuf::int64 value) {
  sellnumordersqueue_.Set(index, value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.SellNumOrdersQueue)
}
void MDFund::add_sellnumordersqueue(::google::protobuf::int64 value) {
  sellnumordersqueue_.Add(value);
  // @@protoc_insertion_point(field_add:com.htsc.mdc.insight.model.MDFund.SellNumOrdersQueue)
}
const ::google::protobuf::RepeatedField< ::google::protobuf::int64 >&
MDFund::sellnumordersqueue() const {
  // @@protoc_insertion_point(field_list:com.htsc.mdc.insight.model.MDFund.SellNumOrdersQueue)
  return sellnumordersqueue_;
}
::google::protobuf::RepeatedField< ::google::protobuf::int64 >*
MDFund::mutable_sellnumordersqueue() {
  // @@protoc_insertion_point(field_mutable_list:com.htsc.mdc.insight.model.MDFund.SellNumOrdersQueue)
  return &sellnumordersqueue_;
}

// optional int64 NorminalPx = 59;
void MDFund::clear_norminalpx() {
  norminalpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::norminalpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.NorminalPx)
  return norminalpx_;
}
void MDFund::set_norminalpx(::google::protobuf::int64 value) {
  
  norminalpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.NorminalPx)
}

// optional int64 ShortSellSharesTraded = 60;
void MDFund::clear_shortsellsharestraded() {
  shortsellsharestraded_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::shortsellsharestraded() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ShortSellSharesTraded)
  return shortsellsharestraded_;
}
void MDFund::set_shortsellsharestraded(::google::protobuf::int64 value) {
  
  shortsellsharestraded_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ShortSellSharesTraded)
}

// optional int64 ShortSellTurnover = 61;
void MDFund::clear_shortsellturnover() {
  shortsellturnover_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::shortsellturnover() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ShortSellTurnover)
  return shortsellturnover_;
}
void MDFund::set_shortsellturnover(::google::protobuf::int64 value) {
  
  shortsellturnover_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ShortSellTurnover)
}

// optional int64 PreMarketLastPx = 62;
void MDFund::clear_premarketlastpx() {
  premarketlastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::premarketlastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreMarketLastPx)
  return premarketlastpx_;
}
void MDFund::set_premarketlastpx(::google::protobuf::int64 value) {
  
  premarketlastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreMarketLastPx)
}

// optional int64 PreMarketTotalVolumeTrade = 63;
void MDFund::clear_premarkettotalvolumetrade() {
  premarkettotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::premarkettotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreMarketTotalVolumeTrade)
  return premarkettotalvolumetrade_;
}
void MDFund::set_premarkettotalvolumetrade(::google::protobuf::int64 value) {
  
  premarkettotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreMarketTotalVolumeTrade)
}

// optional int64 PreMarketTotalValueTrade = 64;
void MDFund::clear_premarkettotalvaluetrade() {
  premarkettotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::premarkettotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreMarketTotalValueTrade)
  return premarkettotalvaluetrade_;
}
void MDFund::set_premarkettotalvaluetrade(::google::protobuf::int64 value) {
  
  premarkettotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreMarketTotalValueTrade)
}

// optional int64 PreMarketHighPx = 65;
void MDFund::clear_premarkethighpx() {
  premarkethighpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::premarkethighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreMarketHighPx)
  return premarkethighpx_;
}
void MDFund::set_premarkethighpx(::google::protobuf::int64 value) {
  
  premarkethighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreMarketHighPx)
}

// optional int64 PreMarketLowPx = 66;
void MDFund::clear_premarketlowpx() {
  premarketlowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::premarketlowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreMarketLowPx)
  return premarketlowpx_;
}
void MDFund::set_premarketlowpx(::google::protobuf::int64 value) {
  
  premarketlowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreMarketLowPx)
}

// optional int64 AfterHoursLastPx = 67;
void MDFund::clear_afterhourslastpx() {
  afterhourslastpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::afterhourslastpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.AfterHoursLastPx)
  return afterhourslastpx_;
}
void MDFund::set_afterhourslastpx(::google::protobuf::int64 value) {
  
  afterhourslastpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.AfterHoursLastPx)
}

// optional int64 AfterHoursTotalVolumeTrade = 68;
void MDFund::clear_afterhourstotalvolumetrade() {
  afterhourstotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::afterhourstotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.AfterHoursTotalVolumeTrade)
  return afterhourstotalvolumetrade_;
}
void MDFund::set_afterhourstotalvolumetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.AfterHoursTotalVolumeTrade)
}

// optional int64 AfterHoursTotalValueTrade = 69;
void MDFund::clear_afterhourstotalvaluetrade() {
  afterhourstotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::afterhourstotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.AfterHoursTotalValueTrade)
  return afterhourstotalvaluetrade_;
}
void MDFund::set_afterhourstotalvaluetrade(::google::protobuf::int64 value) {
  
  afterhourstotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.AfterHoursTotalValueTrade)
}

// optional int64 AfterHoursHighPx = 70;
void MDFund::clear_afterhourshighpx() {
  afterhourshighpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::afterhourshighpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.AfterHoursHighPx)
  return afterhourshighpx_;
}
void MDFund::set_afterhourshighpx(::google::protobuf::int64 value) {
  
  afterhourshighpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.AfterHoursHighPx)
}

// optional int64 AfterHoursLowPx = 71;
void MDFund::clear_afterhourslowpx() {
  afterhourslowpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::afterhourslowpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.AfterHoursLowPx)
  return afterhourslowpx_;
}
void MDFund::set_afterhourslowpx(::google::protobuf::int64 value) {
  
  afterhourslowpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.AfterHoursLowPx)
}

// optional string MarketPhaseCode = 72;
void MDFund::clear_marketphasecode() {
  marketphasecode_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFund::marketphasecode() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
  return marketphasecode_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_marketphasecode(const ::std::string& value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
}
void MDFund::set_marketphasecode(const char* value) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
}
void MDFund::set_marketphasecode(const char* value, size_t size) {
  
  marketphasecode_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
}
::std::string* MDFund::mutable_marketphasecode() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
  return marketphasecode_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFund::release_marketphasecode() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
  
  return marketphasecode_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_allocated_marketphasecode(::std::string* marketphasecode) {
  if (marketphasecode != NULL) {
    
  } else {
    
  }
  marketphasecode_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), marketphasecode);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFund.MarketPhaseCode)
}

// optional int64 USConsolidateVolume = 73;
void MDFund::clear_usconsolidatevolume() {
  usconsolidatevolume_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::usconsolidatevolume() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.USConsolidateVolume)
  return usconsolidatevolume_;
}
void MDFund::set_usconsolidatevolume(::google::protobuf::int64 value) {
  
  usconsolidatevolume_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.USConsolidateVolume)
}

// optional int64 USCompositeClosePx = 74;
void MDFund::clear_uscompositeclosepx() {
  uscompositeclosepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::uscompositeclosepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.USCompositeClosePx)
  return uscompositeclosepx_;
}
void MDFund::set_uscompositeclosepx(::google::protobuf::int64 value) {
  
  uscompositeclosepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.USCompositeClosePx)
}

// optional string TradingHaltReason = 75;
void MDFund::clear_tradinghaltreason() {
  tradinghaltreason_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
const ::std::string& MDFund::tradinghaltreason() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
  return tradinghaltreason_.GetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_tradinghaltreason(const ::std::string& value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
}
void MDFund::set_tradinghaltreason(const char* value) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
}
void MDFund::set_tradinghaltreason(const char* value, size_t size) {
  
  tradinghaltreason_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
}
::std::string* MDFund::mutable_tradinghaltreason() {
  
  // @@protoc_insertion_point(field_mutable:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
  return tradinghaltreason_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
::std::string* MDFund::release_tradinghaltreason() {
  // @@protoc_insertion_point(field_release:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
  
  return tradinghaltreason_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void MDFund::set_allocated_tradinghaltreason(::std::string* tradinghaltreason) {
  if (tradinghaltreason != NULL) {
    
  } else {
    
  }
  tradinghaltreason_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), tradinghaltreason);
  // @@protoc_insertion_point(field_set_allocated:com.htsc.mdc.insight.model.MDFund.TradingHaltReason)
}

// optional int64 OtcTotalVolumeTrade = 76;
void MDFund::clear_otctotalvolumetrade() {
  otctotalvolumetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::otctotalvolumetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.OtcTotalVolumeTrade)
  return otctotalvolumetrade_;
}
void MDFund::set_otctotalvolumetrade(::google::protobuf::int64 value) {
  
  otctotalvolumetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.OtcTotalVolumeTrade)
}

// optional int64 OtcTotalValueTrade = 77;
void MDFund::clear_otctotalvaluetrade() {
  otctotalvaluetrade_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::otctotalvaluetrade() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.OtcTotalValueTrade)
  return otctotalvaluetrade_;
}
void MDFund::set_otctotalvaluetrade(::google::protobuf::int64 value) {
  
  otctotalvaluetrade_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.OtcTotalValueTrade)
}

// optional int64 OtcNumTrades = 78;
void MDFund::clear_otcnumtrades() {
  otcnumtrades_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::otcnumtrades() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.OtcNumTrades)
  return otcnumtrades_;
}
void MDFund::set_otcnumtrades(::google::protobuf::int64 value) {
  
  otcnumtrades_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.OtcNumTrades)
}

// optional int32 DataMultiplePowerOf10 = 79;
void MDFund::clear_datamultiplepowerof10() {
  datamultiplepowerof10_ = 0;
}
::google::protobuf::int32 MDFund::datamultiplepowerof10() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.DataMultiplePowerOf10)
  return datamultiplepowerof10_;
}
void MDFund::set_datamultiplepowerof10(::google::protobuf::int32 value) {
  
  datamultiplepowerof10_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.DataMultiplePowerOf10)
}

// optional int64 WeightedAvgPx = 80;
void MDFund::clear_weightedavgpx() {
  weightedavgpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::weightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.WeightedAvgPx)
  return weightedavgpx_;
}
void MDFund::set_weightedavgpx(::google::protobuf::int64 value) {
  
  weightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.WeightedAvgPx)
}

// optional int64 PreCloseWeightedAvgPx = 81;
void MDFund::clear_precloseweightedavgpx() {
  precloseweightedavgpx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::precloseweightedavgpx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.PreCloseWeightedAvgPx)
  return precloseweightedavgpx_;
}
void MDFund::set_precloseweightedavgpx(::google::protobuf::int64 value) {
  
  precloseweightedavgpx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.PreCloseWeightedAvgPx)
}

// optional int64 BestBuyPrice = 82;
void MDFund::clear_bestbuyprice() {
  bestbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::bestbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BestBuyPrice)
  return bestbuyprice_;
}
void MDFund::set_bestbuyprice(::google::protobuf::int64 value) {
  
  bestbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BestBuyPrice)
}

// optional int64 QtyAtBestBuyPrice = 83;
void MDFund::clear_qtyatbestbuyprice() {
  qtyatbestbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::qtyatbestbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.QtyAtBestBuyPrice)
  return qtyatbestbuyprice_;
}
void MDFund::set_qtyatbestbuyprice(::google::protobuf::int64 value) {
  
  qtyatbestbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.QtyAtBestBuyPrice)
}

// optional int64 BestSellPrice = 84;
void MDFund::clear_bestsellprice() {
  bestsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::bestsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.BestSellPrice)
  return bestsellprice_;
}
void MDFund::set_bestsellprice(::google::protobuf::int64 value) {
  
  bestsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.BestSellPrice)
}

// optional int64 QtyAtBestSellPrice = 85;
void MDFund::clear_qtyatbestsellprice() {
  qtyatbestsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::qtyatbestsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.QtyAtBestSellPrice)
  return qtyatbestsellprice_;
}
void MDFund::set_qtyatbestsellprice(::google::protobuf::int64 value) {
  
  qtyatbestsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.QtyAtBestSellPrice)
}

// optional int64 HighAccuracyIOPV = 86;
void MDFund::clear_highaccuracyiopv() {
  highaccuracyiopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::highaccuracyiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.HighAccuracyIOPV)
  return highaccuracyiopv_;
}
void MDFund::set_highaccuracyiopv(::google::protobuf::int64 value) {
  
  highaccuracyiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.HighAccuracyIOPV)
}

// optional int64 HighAccuracyPreIOPV = 87;
void MDFund::clear_highaccuracypreiopv() {
  highaccuracypreiopv_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::highaccuracypreiopv() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.HighAccuracyPreIOPV)
  return highaccuracypreiopv_;
}
void MDFund::set_highaccuracypreiopv(::google::protobuf::int64 value) {
  
  highaccuracypreiopv_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.HighAccuracyPreIOPV)
}

// optional int64 ReferencePx = 88;
void MDFund::clear_referencepx() {
  referencepx_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::referencepx() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.ReferencePx)
  return referencepx_;
}
void MDFund::set_referencepx(::google::protobuf::int64 value) {
  
  referencepx_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.ReferencePx)
}

// optional int64 MaxBuyPrice = 89;
void MDFund::clear_maxbuyprice() {
  maxbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::maxbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MaxBuyPrice)
  return maxbuyprice_;
}
void MDFund::set_maxbuyprice(::google::protobuf::int64 value) {
  
  maxbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MaxBuyPrice)
}

// optional int64 MinBuyPrice = 90;
void MDFund::clear_minbuyprice() {
  minbuyprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::minbuyprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MinBuyPrice)
  return minbuyprice_;
}
void MDFund::set_minbuyprice(::google::protobuf::int64 value) {
  
  minbuyprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MinBuyPrice)
}

// optional int64 MaxSellPrice = 91;
void MDFund::clear_maxsellprice() {
  maxsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::maxsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MaxSellPrice)
  return maxsellprice_;
}
void MDFund::set_maxsellprice(::google::protobuf::int64 value) {
  
  maxsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MaxSellPrice)
}

// optional int64 MinSellPrice = 92;
void MDFund::clear_minsellprice() {
  minsellprice_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 MDFund::minsellprice() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.MinSellPrice)
  return minsellprice_;
}
void MDFund::set_minsellprice(::google::protobuf::int64 value) {
  
  minsellprice_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.MinSellPrice)
}

// optional int32 DelayType = 101;
void MDFund::clear_delaytype() {
  delaytype_ = 0;
}
::google::protobuf::int32 MDFund::delaytype() const {
  // @@protoc_insertion_point(field_get:com.htsc.mdc.insight.model.MDFund.DelayType)
  return delaytype_;
}
void MDFund::set_delaytype(::google::protobuf::int32 value) {
  
  delaytype_ = value;
  // @@protoc_insertion_point(field_set:com.htsc.mdc.insight.model.MDFund.DelayType)
}

inline const MDFund* MDFund::internal_default_instance() {
  return &MDFund_default_instance_.get();
}
#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace model
}  // namespace insight
}  // namespace mdc
}  // namespace htsc
}  // namespace com

// @@protoc_insertion_point(global_scope)
