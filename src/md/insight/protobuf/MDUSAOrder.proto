syntax = "proto3";

package com.htsc.mdc.insight.model;

import "ESecurityIDSource.proto";
import "ESecurityType.proto";

message MDUSAOrder {
  string HTSCSecurityID = 1;
  int32 MDDate = 2;
  int32 MDTime = 3;
  int64 DataTimestamp = 4;
  com.htsc.mdc.model.ESecurityIDSource securityIDSource = 5;
  com.htsc.mdc.model.ESecurityType securityType = 6;
  int32 ExchangeDate = 7;
  int32 ExchangeTime = 8;
  int32 Nanosecond = 9;
  int32 ChannelNo = 10;
  int64 OrderIndex = 11;
  int64 OriginalOrderIndex = 12;
  int32 OrderType = 13;
  int64 OrderPrice = 14;
  double OrderQty = 15;
  int32 OrderBSFlag = 16;
  string Attribution = 17;
  int32 TrackingNum = 18;
  int32 DataMultiplePowerOf10 = 19;
  int32 TimeIndex = 20;
  int64 DataIndex = 21;
}
